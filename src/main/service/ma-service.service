[Unit]
Description=ma-service
Documentation=http://ma-service.onepay.vn/en/docs/
After=network.target remote-fs.target nss-lookup.target

[Service]
ExecStart=/usr/java/jdk1.8.0_92/bin/java \
 -XX:+UseG1GC -Xms20M -Xmx100M \
 -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager \
 -Dlog=/var/log/ma-service/ma-service.log \
 -Dvertx.cacheDirBase=/opt/ma-service/ \
 -Dvertx.cwd=/opt/ma-service/ \
 -cp /opt/ma-service/classes:/opt/ma-service/lib/* \
 com.onepay.ma.service.Application

WorkingDirectory=/opt/ma-service

User=ma
Group=ma
LimitNOFILE=30000

[Install]
WantedBy=multi-user.target
