/*
   For step-by-step instructions on connecting your Android application to this backend module,
   see "App Engine Backend with Google Cloud Messaging" template documentation at
   https://github.com/GoogleCloudPlatform/gradle-appengine-templates/tree/master/GcmEndpoints
*/

package com.onepay;


import com.onepay.ma.service.util.IErrors;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

public class Test {
    public static final String API_KEY = "AIzaSyDTyj8OhRs5gW4tcrn1dUrNle488KNHESs"; // mOnePAY

    //SenderID:42739992319
    public static void main(String[] arg) {

        Calendar vcbMigsCutOff = Calendar.getInstance();
        vcbMigsCutOff.setTime(new Date());
        System.out.println("vcbMigsCutOff before : " + vcbMigsCutOff.getTime());
        vcbMigsCutOff.set(Calendar.MILLISECOND, 0);
        vcbMigsCutOff.set(Calendar.SECOND, 0);
        vcbMigsCutOff.set(Calendar.MINUTE, 0);
        vcbMigsCutOff.set(Calendar.HOUR_OF_DAY, 17);

        System.out.println("vcbMigsCutOff : " + vcbMigsCutOff.getTime());
        Calendar transactionTime = Calendar.getInstance();
        transactionTime.setTime(new Date());
        Date now = new Date();
        System.out.println( "now : " +now);
        System.out.println( now.compareTo(vcbMigsCutOff.getTime()) >= 0
                && transactionTime.getTime().compareTo(vcbMigsCutOff.getTime()) <0);

        vcbMigsCutOff.add(Calendar.HOUR, -24);
        System.out.println("vcbMigsCutOff after : " + vcbMigsCutOff.getTime());
        System.out.println( now.compareTo(vcbMigsCutOff.getTime()) < 0
                && transactionTime.getTime().compareTo(vcbMigsCutOff.getTime()) <0);

//
//        Calendar cal = Calendar.getInstance();
//        Timestamp date = Timestamp.valueOf("2019-07-19 11:23:19");
//        cal.setTime(date);
//        cal.set(Calendar.MILLISECOND, 0);
//        cal.set(Calendar.SECOND, 0);
//        cal.set(Calendar.MINUTE, 0);
//        Date d_to = null;
//        if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
//            cal.set(Calendar.HOUR_OF_DAY, 17);
//        } else {
//            cal.set(Calendar.HOUR_OF_DAY, 17);
//            cal.add(Calendar.DATE, 1);
//        }
//        d_to = cal.getTime();
//        if ((new Date()).getTime() > d_to.getTime()) {
////            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE");
//            System.out.println("EEEEEEE");
//            throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
//        } else {
////            Map returnMap = new HashMap();
////            returnMap.put("transaction", internationalTransaction);
////            return merchantService.getData(connOnline, internationalTransaction.getMerchant_id()).map(merchantData -> {
////                returnMap.put("merchant", merchantData);
////                return returnMap;
////            });
//            System.out.println("HHHHHH");
//        }
//        if (args.length < 1 || args.length > 2 || args[0] == null) {
//            System.err.println("usage: ./gradlew run -Pmsg=\"MESSAGE\" [-Pto=\"DEVICE_TOKEN\"]");
//            System.err.println("");
//            System.err.println("Specify a test message to broadcast via GCM. If a device's GCM registration token is\n" +
//                    "specified, the message will only be sent to that device. Otherwise, the message \n" +
//                    "will be sent to all devices subscribed to the \"global\" topic.");
//            System.err.println("");
//            System.err.println("Example (Broadcast):\n" +
//                    "On Windows:   .\\gradlew.bat run -Pmsg=\"<Your_Message>\"\n" +
//                    "On Linux/Mac: ./gradlew run -Pmsg=\"<Your_Message>\"");
//            System.err.println("");
//            System.err.println("Example (Unicast):\n" +
//                    "On Windows:   .\\gradlew.bat run -Pmsg=\"<Your_Message>\" -Pto=\"<Your_Token>\"\n" +
//                    "On Linux/Mac: ./gradlew run -Pmsg=\"<Your_Message>\" -Pto=\"<Your_Token>\"");
//            System.exit(1);
//        }
//        try {
//            // Prepare JSON containing the GCM message content. What to send and where to send.
//            JsonObject jGcmData = new JsonObject();
//            JsonObject jData = new JsonObject();
//            jData.put("message", args[0].trim());
//            // Where to send GCM message.
//            if (args.length > 1 && args[1] != null) {
//                jGcmData.put("to", args[1].trim());
//            } else {
//                jGcmData.put("to", "/topics/global");
//            }
//            // What to send in GCM message.
//            jGcmData.put("data", jData);
//
//            // Create connection to send GCM Message request.
//            URL url = new URL("https://android.googleapis.com/gcm/send");
//            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//            conn.setRequestProperty("Authorization", "key=" + API_KEY);
//            conn.setRequestProperty("Content-Type", "application/json");
//            conn.setRequestMethod("POST");
//            conn.setDoOutput(true);
//
//            // Send GCM message content.
//            OutputStream outputStream = conn.getOutputStream();
//            outputStream.write(jGcmData.toString().getBytes());
//
//            // Read GCM response.
//            InputStream inputStream = conn.getInputStream();
//            String resp = IOUtils.toString(inputStream);
//            System.out.println(resp);
//            System.out.println("Check your device/emulator for notification or logcat for " +
//                    "confirmation of the receipt of the GCM message.");
//        } catch (IOException e) {
//            System.out.println("Unable to send GCM message.");
//            System.out.println("Please ensure that API_KEY has been replaced by the server " +
//                    "API key, and that the device's registration token is correct (if specified).");
//            e.printStackTrace();
//        }
    }


}