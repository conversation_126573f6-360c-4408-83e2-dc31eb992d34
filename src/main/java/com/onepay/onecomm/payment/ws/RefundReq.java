package com.onepay.onecomm.payment.ws;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class RefundReq implements Serializable {
    static final long serialVersionUID = -8612883169929403497L;
    private long transactionId;
    private String merchantId;
    private String currencyCode;
    private double amount;
    private String operatorId;
    private String clientIp;
    private long refund_transactionId;


    public RefundReq(long transactionId, String merchantId, String currencyCode, double amount, String operatorId, String clientIp, long refund_transactionId) {
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.currencyCode = currencyCode;
        this.amount = amount;
        this.operatorId = operatorId;
        this.clientIp = clientIp;
        this.refund_transactionId = refund_transactionId;
    }

    public String getOperatorId() {
        return this.operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public long getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantId() {
        return this.merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCurrencyCode() {
        return this.currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public double getAmount() {
        return this.amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getClientIp() {
        return this.clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public long getRefund_transactionId() {
        return this.refund_transactionId;
    }

    public void setRefund_transactionId(long refund_transactionId) {
        this.refund_transactionId = refund_transactionId;
    }
}