package com.onepay.onecomm.payment.ws;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/27/16.
 */
public class QueryRes extends ServiceRes {
    private long transactionId;
    private String merchantId;
    private String currencyCode;
    private double amount;
    private String operatorId;
    private String clientIp;

    public QueryRes() {
    }

    public QueryRes(int status, String description, long transactionId, String merchantId, String currencyCode, double amount, String operatorId, String clientIp) {
        super(status, description);
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.currencyCode = currencyCode;
        this.amount = amount;
        this.operatorId = operatorId;
        this.clientIp = clientIp;
    }

    public long getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantId() {
        return this.merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCurrencyCode() {
        return this.currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public double getAmount() {
        return this.amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getOperatorId() {
        return this.operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getClientIp() {
        return this.clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
}
