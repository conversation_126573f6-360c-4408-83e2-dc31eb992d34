package com.onepay.ma.service.util;

import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public enum CardType {

    UNKNOWN,
    VISA("^4[0-9]{5,}$"),
    MASTERCARD("^5[1-5][0-9]{4,}$"),
    AMERICAN_EXPRESS("^3[47][0-9]{4,}$");

    private Pattern pattern;

    CardType() {
        this.pattern = null;
    }

    CardType(String pattern) {
        this.pattern = Pattern.compile(pattern);
    }

    public static CardType detect(String cardNumber) {

        for (CardType cardType : CardType.values()) {
            if (null == cardType.pattern) continue;
            if (cardType.pattern.matcher(cardNumber).matches()) return cardType;
        }

        return UNKNOWN;
    }

}
