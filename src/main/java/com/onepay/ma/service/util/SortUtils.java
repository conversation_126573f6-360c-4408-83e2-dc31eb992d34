package com.onepay.ma.service.util;

import java.util.Arrays;
import java.util.List;

public class SortUtils {

    private static final List<String> GENERAL_REPORT_INPUT_ORDERED_ENTRIES = Arrays.asList(
            "QT", "QR", "ND");


    public static int compareInputType(String o1, String o2){
        if (GENERAL_REPORT_INPUT_ORDERED_ENTRIES.contains(o1) && GENERAL_REPORT_INPUT_ORDERED_ENTRIES.contains(o2)) {
            // Both objects are in our ordered list. Compare them by
            // their position in the list
            return GENERAL_REPORT_INPUT_ORDERED_ENTRIES.indexOf(o1) - GENERAL_REPORT_INPUT_ORDERED_ENTRIES.indexOf(o2);
        }

        if (GENERAL_REPORT_INPUT_ORDERED_ENTRIES.contains(o1)) {
            // o1 is in the ordered list, but o2 isn't. o1 is smaller (i.e. first)
            return -1;
        }

        if (GENERAL_REPORT_INPUT_ORDERED_ENTRIES.contains(o2)) {
            // o2 is in the ordered list, but o1 isn't. o2 is smaller (i.e. first)
            return 1;
        }

        return o1.toString().compareTo(o2.toString());
    }
    
}
