package com.onepay.ma.service.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * Created by administrator on 12/9/15.
 */
public class DomesticRefundUtil {

    private static final Logger LOGGER = Logger.getLogger(DomesticRefundUtil.class.getName());
    static final String VCP_URL = PropsUtil.get("refund.one_comm.url_2", "");
    // static final String SECURE_SECRET = PropsUtil.get("domestic.vpc_secret", "6D0870CDE5F24F34F3915FB0045120DB");
    static final char[] HEX_TABLE = new char[]{
            '0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    private static byte[] decodeHexArray = new byte[103];

    static {
        int i = 0;
        for (byte b : new byte[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'}) {
            decodeHexArray[b] = (byte) i++;
        }
        decodeHexArray['a'] = decodeHexArray['A'];
        decodeHexArray['b'] = decodeHexArray['B'];
        decodeHexArray['c'] = decodeHexArray['C'];
        decodeHexArray['d'] = decodeHexArray['D'];
        decodeHexArray['e'] = decodeHexArray['E'];
        decodeHexArray['f'] = decodeHexArray['F'];
    }

    public static  Map<String, String> createRefundUrl(String merchantId, String accessCode, String hashCode, String refundRef
    , String transactionRef, String operator, Double amount, String note, String ip) {
        Map<String, String> m = new HashMap();
        m.put("vpc_Version", "2");
        m.put("vpc_Command", "refund");
        m.put("vpc_Merchant", merchantId);
        m.put("vpc_AccessCode", accessCode);
        m.put("vpc_MerchTxnRef", refundRef);
        m.put("vpc_OrgMerchTxnRef", transactionRef);
        m.put("vpc_Note", note == null ? "" : note);
        m.put("vpc_Operator", operator);
        m.put("vpc_Amount", Convert.toString(amount * 100, "0"));
        m.put("client_ip", ip);
        m.put("request_type", "REFUND");


        String secureHash = hashAllFields(m, hashCode);
        m.put("vpc_SecureHash", secureHash);

        // StringBuffer buf = new StringBuffer();
        // buf.append(VCP_URL).append('?');
        // appendQueryFields(buf, m);
        // LOGGER.info("Create domestic refund url: "+ buf.toString());
        return m;

    }

    static String  hashAllFields(Map fields, String hashCode) {
        List fieldNames = new ArrayList(fields.keySet());
        Collections.sort(fieldNames);
        StringBuffer buf = new StringBuffer();
        Iterator itr = fieldNames.iterator();
        while (itr.hasNext()) {
            String fieldName = (String) itr.next();
            String fieldValue = (String) fields.get(fieldName);
            if ((fieldValue != null) && (fieldValue.length() > 0) && fieldName.indexOf("vpc_") == 0) {
                buf.append(fieldName + "=" + fieldValue);
                if (itr.hasNext()) {
                    buf.append('&');
                }
            }
        }
        byte[] mac = null;
        try {
            byte[] b = decodeHexa(hashCode.getBytes());
            SecretKey key = new SecretKeySpec(b, "HMACSHA256");
            Mac m = Mac.getInstance("HMACSHA256");
            m.init(key);
            m.update(buf.toString().getBytes("UTF-8"));
            mac = m.doFinal();
        } catch (Exception e) {
            e.printStackTrace();
        }
        String hashValue = hex(mac);
        return hashValue;
    }

    public static byte[] decodeHexa(byte[] data) throws Exception {
        if (data == null) {
            return null;
        }
        if (data.length % 2 != 0) {
            throw new Exception("Invalid data length:" + data.length);
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte b1, b2;
        int i = 0;
        while (i < data.length) {
            b1 = decodeHexArray[data[i++]];
            b2 = decodeHexArray[data[i++]];
            out.write((b1 << 4) | b2);
        }
        out.flush();
        out.close();
        return out.toByteArray();
    }

    static String hex(byte[] input) {
        StringBuffer sb = new StringBuffer(input.length * 2);
        for (int i = 0; i < input.length; i++) {
            sb.append(HEX_TABLE[(input[i] >> 4) & 0xf]);
            sb.append(HEX_TABLE[input[i] & 0xf]);
        }
        return sb.toString();
    }

    static void appendQueryFields(StringBuffer buf, Map fields) {
        List fieldNames = new ArrayList(fields.keySet());
        Iterator itr = fieldNames.iterator();
        while (itr.hasNext()) {
            String fieldName = (String) itr.next();
            String fieldValue = (String) fields.get(fieldName);

            if ((fieldValue != null) ) {
                buf.append(URLEncoder.encode(fieldName));
                buf.append('=');
                buf.append(URLEncoder.encode(fieldValue));
            }
            if (itr.hasNext()) {
                buf.append('&');
            }

        }

    }
}
