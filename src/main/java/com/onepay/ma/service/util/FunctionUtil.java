package com.onepay.ma.service.util;


import com.onepay.ma.service.models.ServerConfig;
import com.onepay.onesm.client.OneSMHttpClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by huynguyen on 3/28/16.
 */
public final class FunctionUtil {
    public static void sendNextNullContext(RoutingContext rc, int code){
        rc.put(ParamsPool.HANDLER_DATA_RESULT, null);
        rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
        rc.next();
    }
    public static int monthsBetween(final Date s1, final Date s2) {
        final Calendar d1 = Calendar.getInstance();
        d1.setTime(s1);
        final Calendar d2 = Calendar.getInstance();
        d2.setTime(s2);
        int diff = (d2.get(Calendar.YEAR) - d1.get(Calendar.YEAR)) * 12 + d2.get(Calendar.MONTH) - d1.get(Calendar.MONTH);
        return diff;
    }

    public static long dayBetween(Date d1, Date d2) {
        long diff = d2.getTime() - d1.getTime();
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }

    // func comapre 2 date by month
    public static int compareDateByMonth(final Date fromDate, final Date toDate, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(fromDate);
        calendar.add(Calendar.MONTH, month);
        calendar.add(Calendar.DATE, 1);
        Date fromDatePlusMonth = calendar.getTime();
        // Comparison
        if (fromDatePlusMonth.before(toDate)) {
            return -1;
        } else if (fromDatePlusMonth.after(toDate)) {
            return 1;
        } else {
            return 0;
        }
    }
    
    public static byte[] encodeHessian(Object obj) {
        try {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            ObjectOutputStream objOut = new ObjectOutputStream(bout);
            objOut.writeObject(obj);
            objOut.flush();
            byte[] brequest = bout.toByteArray();
            int len = brequest.length;
            byte[] header = new byte[]{0x63, 0x02, 0x00, 0x6d, 0x00, 0x07, 'e', 'x', 'e', 'c', 'u', 't', 'e', 'B'};
            byte[] out = new byte[brequest.length + 17];
            System.arraycopy(header, 0, out, 0, header.length);
            out[14] = (byte) (len >> 8 & 0xFF);
            out[15] = (byte) (len & 0xFF);
            System.arraycopy(brequest, 0, out, 16, brequest.length);
            out[out.length - 1] = 'z';
            return out;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "encodeHessian exception", e);
        }
        return null;
    }

    public static Object decodeHessian(byte[] input) {
        try {
            byte[] buf = new byte[input.length - 7];
            System.arraycopy(input, 6, buf, 0, buf.length);
            ByteArrayInputStream bin = new ByteArrayInputStream(buf);
            ObjectInputStream objIn = new ObjectInputStream(bin);
            return objIn.readObject();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "decodeHessian exception", e);
        }
        return null;
    }

    public static String oneSMHmac(String data, ServerConfig serverConfig) {
        try {
           return OneSMHttpClient.encryptToBase64String(serverConfig.getOneSMConfig().getUrl(), serverConfig.getOneSMConfig().getClientId(),
                    Hex.decodeHex(serverConfig.getOneSMConfig().getClientKey().toCharArray()), serverConfig.getOneSMConfig().getHmacKeyLabel(), data.getBytes(), serverConfig.getOneSMConfig().getTimeOut());
        } catch (DecoderException e) {
            LOGGER.log(Level.SEVERE, "ONE SM  exception", e);

        }
        return null;
    }

    public static boolean isCardData(String data) {
        String regex = "^(?:(?<visa>4[0-9]{12}(?:[0-9]{3})?)|" +
                "(?<mastercard>5[1-5][0-9]{14})|" +
                "(?<amex>3[47][0-9]{13})|" +
                "(?<jcb>(?:2131|1800|35[0-9]{3})[0-9]{11}))$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);
        if(matcher.matches()) {
            return validMod10Algorithm(data);
        }
        return false;
    }

    public static boolean isDomesticCardData(String data) {
        String regex = "^([0-9]{16}|[0-9]{19})$";
        Pattern pattern = Pattern.compile((regex));
        Matcher matcher = pattern.matcher(data);
        if (matcher.matches()) {
            return validMod10Algorithm(data);
        }
        return false;
    }

    public static boolean isBeginIsACard(String data) {
        String regex = "^(?:(?<visa>4[0-9]{12}(?:[0-9]{3})?)|" +
                "(?<mastercard>5[1-5][0-9]{14})|" +
                "(?<amex>3[47][0-9]{13})|" +
                "(?<jcb>(?:2131|1800|35[0-9]{3})[0-9]{11}))$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);
        return matcher.matches();
    }


    private static boolean validMod10Algorithm(String cardNumber) {
        int sum = 0;
        int mul = 1;
        int len = cardNumber.length();
        for (int i = 0; i < len; i++) {
            String digit = cardNumber.substring(len - i - 1, len - i);
            int tproduct = Integer.parseInt(digit, 10) * mul;
            if (tproduct >= 10) {
                sum += tproduct % 10 + 1;
            } else {
                sum += tproduct;
            }
            if (mul == 1) {
                mul++;
            } else {
                mul--;
            }
        }
        if ((sum % 10) != 0) {
            return false;
        }
        return true;
    }

    private static final Logger LOGGER = Logger.getLogger(FunctionUtil.class.getName());
}
