package com.onepay.ma.service.util;

/**
 * Created this LogMakingConverter Plug in for masking logs statement
 * Here masking for Credit card number, CVV and SSN .
 * You can also implement for password and rest depend on your application needs.
 */
import java.util.regex.Matcher;
import java.util.regex.Pattern;
 
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;

@Plugin(name="LogMaskingConverter", category = "Converter")
@ConverterKeys({"spi","trscId"})
public class LogMaskingConverter extends LogEventPatternConverter{
 
    protected LogMaskingConverter(String name, String style) {
        super(name, style);
    }
 
    public static LogMaskingConverter newInstance(String[] options) {
        return new LogMaskingConverter("spi",Thread.currentThread().getName());
    }
 
    @Override
    public void format(LogEvent event, StringBuilder outputMessage) {
        String message = event.getMessage().getFormattedMessage();
        String maskedMessage = message;
        try {
            maskedMessage = mask(message);
        } catch (Exception e) {
            System.out.println("Failed While Masking");
            maskedMessage = message;
        }
        outputMessage.append(maskedMessage);    
 
    }
 
    private static String mask(String source) {
        if (source == null) return "null";
        String accountNumberRegexp = "((?:account_number|accountNumber|accountNo|account_no)\"?[=:]\\s*\"?)\\d+(\\d{2}\"?)";

        String cardNumberRegexp = "([\"|']?(?:number|vpc_CardNum|card_no|CardNum|cardNo|accountNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";

        String cardNumberRegexp2 = "(<(?:accountNumber)?[>]?\\d{6})\\d+(\\d{4})<?";

        String cardDateRegexp2 = "(<(expirationMonth|expirationYear)>)(\\d+)";

        String cardDateRegexp = "([\"|']?(?:vpc_CardExp|month|year|CardExp|CardExpMonth|CardExpYear|cardExp|CardMonth|CardYear|vpc_CardMonth|vpc_CardYear|CardYear|CardMonth|expire_month|expire_year)[\"|']?[=:][\"|']?)\\d+";

        String cvvRegexp = "([\"|']?(?:cvv|vpc_CardSecurityCode|CardSecurityCode|card_security_code)[\"|']?[=:][\"|']?)\\d+";

        String icvvRegexp = "(icvv|initCVV[\"|']:[\"|'][^[\"|']]{6})[^[\"|']]+";

        String phoneRegex = "([\"|']?(?:phone|customerPhone|userPhone|mobile|mobile_number)[\"|']?[=:][\"|']?)\\d+(\\d{2})[\"|']?";

        String emailRegex = "([\"|']?(?:email|customerEmail|vpc_Customer_Email)[\"|']?[:|=])([\"|']?[a-z|A-Z|0-9]{1})([a-z|A-Z|0-9]+)?([a-z|A-Z|0-9]{1}@)([a-z|A-Z|0-9]+\\.)([a-z|A-Z|0-9]+[\"|']?)";
        
        String tokenNumberRegexp = "([\"|']?(?:vpc_TokenNum|tokenNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";
        return source
                .replaceAll(accountNumberRegexp, "********$2")
                .replaceAll(cardNumberRegexp, "$1*******$2")
                .replaceAll(cardNumberRegexp2, "$1*******$2")
                .replaceAll(cardDateRegexp, "$1****")
                .replaceAll(cardDateRegexp2, "$1****")
                .replaceAll(cvvRegexp, "$1***")
                .replaceAll(icvvRegexp, "$1***")
                .replaceAll(phoneRegex, "$1******$2")
                .replaceAll(emailRegex, "$1$2******$4$5$6")
                .replaceAll(tokenNumberRegexp, "$1*******$2");
    }
 
    private StringBuffer maskMatcher(Matcher matcher, StringBuffer buffer, String maskStr)
    {
        while (matcher.find()) {
            matcher.appendReplacement(buffer,maskStr);
        }
        matcher.appendTail(buffer);
        return buffer;
    }
 
}
