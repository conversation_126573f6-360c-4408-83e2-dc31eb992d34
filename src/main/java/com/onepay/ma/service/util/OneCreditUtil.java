package com.onepay.ma.service.util;

import com.google.gson.Gson;
import com.onepay.ma.service.util.Convert;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/6/16.
 */
public final class OneCreditUtil {

    public static Map oneCreditData(String commandId, String vpcCommand, String merchantId, String accessCode,
                                        String refundRef, String transactionRef, double amount, String operatorId,
                                        String hashCode, String vpcVersion, String note) {
        Map req = new HashMap();
        req.put("command_id", commandId);
        Map vpc = new HashMap();
        vpc.put("vpc_Command", vpcCommand);
        vpc.put("vpc_Merchant", merchantId + "");
        vpc.put("vpc_AccessCode", accessCode + "");
        vpc.put("vpc_MerchTxnRef", refundRef + "");
        vpc.put("vpc_OrgMerchTxnRef", transactionRef + "");
        vpc.put("vpc_Amount", Convert.toString(amount * 100, "0"));
        vpc.put("vpc_Operator", operatorId);
        vpc.put("vpc_Version", vpcVersion);
        if(note != null && !note.isEmpty()) {
            vpc.put("vpc_Note", note);
        }
        vpc.put("vpc_SecureHash", createMerchantHash(vpc, hashCode, vpcVersion));
        
        req.put("vpc", vpc);
        return req;
    }
    public static Map mspData(String vpcCommand, String merchantId, String accessCode,
                                        String refundRef, String transactionRef, double amount, String operatorId,
                                        String hashCode, String vpcVersion, String note) {
        Map vpc = new HashMap();
        vpc.put("vpc_Command", vpcCommand);
        vpc.put("vpc_Merchant", merchantId + "");
        vpc.put("vpc_AccessCode", accessCode + "");
        vpc.put("vpc_MerchTxnRef", refundRef + "");
        vpc.put("vpc_OrgMerchTxnRef", transactionRef + "");
        vpc.put("vpc_Amount", Convert.toString(amount * 100, "0"));
        vpc.put("vpc_Operator", operatorId);
        vpc.put("vpc_Version", vpcVersion);
        if(note != null && !note.isEmpty()) {
            vpc.put("vpc_Note", note);
        }
        vpc.put("vpc_SecureHash", createMerchantHash(vpc, hashCode, vpcVersion));
        
        return vpc;
    }

    public static Map reverseDueMspData(String vpcCommand, String merchantId, String accessCode,
            String refundRef, String transactionRef, double amount, String operatorId,
            String hashCode, String vpcVersion, String note) {
        Map vpc = new HashMap();
        vpc.put("vpc_Command", vpcCommand);
        vpc.put("vpc_Merchant", merchantId + "");
        vpc.put("vpc_AccessCode", accessCode + "");
        vpc.put("vpc_MerchTxnRef", refundRef + "");
        vpc.put("vpc_OrgMerchTxnRef", transactionRef + "");
        vpc.put("vpc_Amount", Convert.toString(amount * 100, "0"));
        vpc.put("vpc_Operator", operatorId);
        vpc.put("vpc_Version", vpcVersion);
        vpc.put("vpc_Dispute", "y");
        vpc.put("vpc_DisputeReason", "DPR-00");
        if (note != null && !note.isEmpty()) {
            vpc.put("vpc_Note", note);
        }
        // vpc_SecureHash have to put in last
        vpc.put("vpc_SecureHash", createMerchantHash(vpc, hashCode, vpcVersion));
        return vpc;
    }

    public static String genHMACSHA256(byte[] data, String secretKey) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(com.onepay.commons.util.Convert.decodeHexa(secretKey.getBytes()), "HMACSHA256");
            Mac mac = Mac.getInstance("HMACSHA256");
            mac.init(signingKey);
            return  com.onepay.commons.util.Convert.toHexString(mac.doFinal(data));
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Error", e);
            return "";
        }
    }

    private static String createHash(Map<String, String> hashFields, String secretKey, String secureHashType)  {
        String secureHash = null;
        List<String> fieldNames = new ArrayList<String>(hashFields.keySet());
        Collections.sort(fieldNames);
        StringBuffer data = new StringBuffer();
        if ("SHA256".equals(secureHashType)) {
            for (String key : fieldNames) {
                if (key.matches("^(vpc_|user_).*$") && !key.matches("^(vpc_SecureHashType|vpc_SecureHash)$")) {
                    String value = hashFields.get(key);//EsbUtil.evalPropertyValue(hashFields.get(key), m);
                    if (value != null && value.length() > 0) {
                        if (data.length() > 0) data.append("&");
                        data.append(key + "=" + value);
                    }
                }
            }
            try {
                SecretKeySpec signingKey = new SecretKeySpec(com.onepay.commons.util.Convert.decodeHexa(secretKey.getBytes()), "HMACSHA256");
                Mac mac = Mac.getInstance("HMACSHA256");
                mac.init(signingKey);
                secureHash =  com.onepay.commons.util.Convert.toHexString(mac.doFinal(data.toString().getBytes("UTF-8")));
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "", e);
            }
        } else {//MD5
            MessageDigest md5;
            data.append(secretKey);
            for (String key : fieldNames) {
                String value = hashFields.get(key);
                if (value != null) {
                    data.append(value);
                }
            }
            try {
                md5 = MessageDigest.getInstance("MD5");
                secureHash = com.onepay.commons.util.Convert.toHexString(md5.digest(data.toString().getBytes("UTF-8")));
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "", e);
            }
        }
        return secureHash;
    }

    public static String createMerchantHash(Map<String, String> hashFields, String merchantSecretKey, String version)
    {
        return createHash(hashFields, merchantSecretKey, "1".equals(version)?"MD5":"SHA256");
    }

    private static final Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(OneCreditUtil.class.getName());

}
