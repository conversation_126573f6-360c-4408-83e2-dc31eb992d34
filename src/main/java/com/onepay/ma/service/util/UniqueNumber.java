package com.onepay.ma.service.util;

/**
 * Created by anhkh on 15-Sep-17.
 */
public class UniqueNumber {
    private static UniqueNumber instance = null;
    private long currentCounter;

    private UniqueNumber() {
        currentCounter = (System.currentTimeMillis() + 1) << 20;
    }

    private static synchronized UniqueNumber getInstance() {
        if (instance == null) {
            instance = new UniqueNumber();
        }
        return instance;
    }

    private synchronized long nextNumber() {
        currentCounter++;
        while (currentCounter > (System.currentTimeMillis() << 20)) {
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
            }
        }
        return currentCounter;
    }

    static long getUniqueNumber() {
        return getInstance().nextNumber();
    }
}
