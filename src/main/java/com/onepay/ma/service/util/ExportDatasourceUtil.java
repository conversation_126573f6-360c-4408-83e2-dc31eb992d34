package com.onepay.ma.service.util;

import com.onepay.ma.service.models.OracleType;
import io.vertx.core.json.JsonArray;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 29/10/2015
 */
public class ExportDatasourceUtil {

    private static void fillStatement(CallableStatement statement, JsonArray in, JsonArray out) throws SQLException {
        if(in == null) {
            in = EMPTY;
        }

        if(out == null) {
            out = EMPTY;
        }

        int max = Math.max(in.size(), out.size());

        for(int i = 0; i < max; ++i) {
            Object value = null;
            if(i < in.size()) {
                value = in.getValue(i);
            }

            if(value != null) {
                statement.setObject(i + 1, value);
            } else {
                if(i < out.size()) {
                    value = out.getValue(i);
                }

                if(value != null) {
                    statement.registerOutParameter(i + 1, OracleType.valueOf(Integer.parseInt(value.toString())).getVendorTypeNumber().intValue());
                } else {
                    statement.setNull(i + 1, 0);
                }
            }
        }

    }

    public static CallableStatement execute(Connection conn, String sql,  JsonArray in, JsonArray out) throws SQLException {
        LOGGER.info("SQL: " + sql);
        LOGGER.info("params: " + in);
        CallableStatement statement = conn.prepareCall(sql);
        Throwable throwable = null;

        try {
            fillStatement(statement, in, out);
            boolean retResult = statement.execute();
            if(retResult) {
                return  statement;
            }

        } catch (Throwable e) {
            LOGGER.log(Level.SEVERE, "Error Sql", e);
            throw e;
        }

        return statement;
    }
    private static final JsonArray EMPTY = new JsonArray(Collections.unmodifiableList(new ArrayList()));
    private static final Logger LOGGER = Logger.getLogger(ExportDatasourceUtil.class.getName());
}
