package com.onepay.ma.service.util;

/**
 * Created by locdt on 6/28/16
 */
public class ErrorException extends RuntimeException {
    private int statusCode;
    private String name;
    private String message;
    private String informationLink;
    private String details;

    public ErrorException(int statusCode, String name, String message, String informationLink, String details) {
        super(message);
        this.statusCode = statusCode;
        this.name = name;
        this.message = message;
        this.informationLink = informationLink;
        this.details = details;
        IErrors.ErrorExceptions.put(name, this);
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getName() {
        return name;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public String getInformationLink() {
        return informationLink;
    }

    public String getDetails() {
        return details;
    }

    public String toJson() {
        return "{\"name\":" + escapeJson(name)
                + ",\"message\":" + escapeJson(message)
                + ",\"information_link\":" + escapeJson(informationLink)
                + ",\"details\":" + escapeJson(details)
                + "}";
    }

    /*
    public void sendResponse(HttpServerResponse response) {
        response.setStatusCode(statusCode)
                .putHeader(CONTENT_TYPE, APPLICATION_JSON)
                .end(toJson());
    }*/
    private static String escapeJson(String string) {
        if (string == null) return "null";

        char c;
        int i;
        int len = string.length();
        StringBuilder sb = new StringBuilder(len + 4);
        String t;

        sb.append('"');
        for (i = 0; i < len; i++) {
            c = string.charAt(i);
            switch (c) {
                case '"':
                case '\\':
                case '/':
                    sb.append('\\').append(c);
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                default:
                    if (c < ' ') {
                        t = "000" + Integer.toHexString(c);
                        sb.append("\\u").append(t.substring(t.length() - 4));
                    } else {
                        sb.append(c);
                    }
            }
        }
        sb.append('"');
        return sb.toString();
    }
}
