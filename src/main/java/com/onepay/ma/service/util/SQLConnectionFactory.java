package com.onepay.ma.service.util;

import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public class SQLConnectionFactory {

    private SQLConnection connection = null;
    public Observable<SQLConnection> create(JDBCClient jdbc){
        return jdbc.getConnectionObservable().flatMap(conn -> {
            connection = conn;
            return Observable.just(connection);
        });
    }

    public Observable<SQLConnection> get(SQLConnection sqlConnection){
        connection = sqlConnection;
        return Observable.just(connection);
    }

    public void dispose(){
        if (connection != null){
            connection.close();
            connection = null;
        }
        //do nothing
    }
}
