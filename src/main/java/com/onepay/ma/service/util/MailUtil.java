package com.onepay.ma.service.util;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.util.Date;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/28/2020
 * Time: 2:43 PM
 * To change this ma-web.
 */

public class MailUtil {
    private static Logger logger = Logger.getLogger(MailUtil.class.getName());

    public static void main(String[] args) {
        logger.info("start sent email");
        sendmailAttach("<EMAIL>", "<EMAIL>", "", "", "test MA sent email", "test content", "smtp", "mail.onepay.com.vn", 25, "<EMAIL>", "aCMvf5jCng8Y6pAdCUHQNx", "true", 0, "");
    }

    
    public static String EMAIL_SITE_HOST = PropsUtil.get("email.support.site.host", "");
    public static String EMAIL_SITE_PORT = PropsUtil.get("email.support.site.port", "");
    public static String EMAIL_SITE_USER = PropsUtil.get("email.support.site.user", "");
    public static String EMAIL_SITE_FROM = PropsUtil.get("email.support.site.from", "");
    public static String EMAIL_SITE_PASSWORD = PropsUtil.get("email.supoport.site.password", "");
    public static String EMAIL_SSL = PropsUtil.get("email.support.ssl", "true");
    public static String EMAIL_STARTTLS = PropsUtil.get("email.support.starttls", "true");

    public static String sendmailAttach(String from, String to, String cc,
                                        String bcc, String subject, String content, String protocol,
                                        String host, int port, String userName, String password, String auth, int retries, String file) {

        Properties props = new Properties();
        props.setProperty("mail.transport.protocol", protocol);
        props.setProperty("mail.host", host);
        if ("smtps".equals(protocol)) {
            props.setProperty("mail.smtps.auth", String.valueOf(auth));
        } else {
            props.setProperty("mail.smtp.auth", String.valueOf(auth));
        }
        logger.info("get Properties");
        int count = 0;
        while (true) {
            try {

                Session mailSession = Session.getDefaultInstance(props, null);
                logger.info("get sss");
                Transport transport = mailSession.getTransport(protocol);
                MimeMessage message = new MimeMessage(mailSession);

                message.setSubject(subject, "UTF-8");
                message.setContent(content, "text/html;charset=utf-8");
                InternetAddress addressFrom = new InternetAddress(from);
                message.setFrom(addressFrom);


                if (file != null && file.length() > 0) {
                    File file_in = new File(file);
                    if (file_in.isFile()) {
                        //
                        // Set the email message text.
                        //
                        MimeBodyPart messagePart = new MimeBodyPart();
                        messagePart.setText(content, "UTF-8", "html");//
                        // Set the email attachment file
                        MimeBodyPart attachmentPart = new MimeBodyPart();
                        FileDataSource fileDataSource = new FileDataSource(file) {
                            @Override
                            public String getContentType() {
                                return "application/octet-stream";
                            }
                        };
                        attachmentPart.setDataHandler(new DataHandler(fileDataSource));
                        attachmentPart.setFileName(file.replaceFirst(".*(?:/|\\\\)([^/\\\\]*)$", "$1"));

                        Multipart multipart = new MimeMultipart();
                        multipart.addBodyPart(messagePart);
                        multipart.addBodyPart(attachmentPart);

                        message.setContent(multipart);
                    }
                }
                String[] tos = to.split(",|;");
                for (int i = 0; i < tos.length; i++) {
                    message.addRecipient(Message.RecipientType.TO, new InternetAddress(tos[i]));
                }
                // logger.fine(cc);
                if (cc != null && !"".equals(cc)) {
                    String[] ccs = cc.split(",|;");
                    for (int i = 0; i < ccs.length; i++) {
                        message.addRecipient(Message.RecipientType.CC, new InternetAddress(ccs[i]));
                        // logger.fine("cc=" + ccs[i]);
                    }
                }
                if (bcc != null && !"".equals(bcc)) {
                    String[] bccs = bcc.split(",|;");
                    for (int i = 0; i < bccs.length; i++) {
                        message.addRecipient(Message.RecipientType.BCC, new InternetAddress(bccs[i]));
                    }
                }

                message.setSentDate(new Date());
                message.saveChanges();
                // logger.fine("Mail transport: host=" + host + ", port=" + port + ", userName=" + userName + ",
                // password=" + password + ", props=" + props + ", message=" + message);
                transport.connect(host, port, userName, password);
                transport.sendMessage(message, message.getAllRecipients());
                transport.close();
                // logger.info("Send OK:" + map);
                return "OK";
            } catch (Exception e) {
                count++;
                // logger.log(Level.SEVERE, "", e);
                try {
                    Thread.sleep(60000);
                } catch (Exception e1) {
                    logger.log(Level.WARNING, "", e1);
                }
                if (count > retries) {
                    return e.getMessage();
                }
                // logger.info("retry:" + count);
            }
        }
    }


    public static String sendMailDirectOnePAY(String subject, String content, String to_emails, String cc_emails, String bcc_emails, String userName, String password, String protocol, String host, int port, String auth) {
        String result = "SENT";
        try {
            String from = userName;
            String to = to_emails;
            String cc = cc_emails;
            String bcc = bcc_emails;

            String str_subject = subject;
            String str_content = content;
            Properties props = new Properties();
            props.setProperty("mail.transport.protocol", protocol);
            props.setProperty("mail.host", host);
            if ("smtps".equals(protocol)) {
                props.setProperty("mail.smtps.auth", String.valueOf(auth));
            } else {
                props.setProperty("mail.smtp.auth", String.valueOf(auth));
            }

            Session mailSession = Session.getInstance(props, null);
            Transport transport = mailSession.getTransport(protocol);
            MimeMessage message = new MimeMessage(mailSession);

            message.setSubject(str_subject, "UTF-8");
            message.setContent(str_content, "text/html;charset=utf-8");
            InternetAddress addressFrom = new InternetAddress(from);
            message.setFrom(addressFrom);

            String[] tos = to.split(",|;");
            for (int i = 0; i < tos.length; i++) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(tos[i]));
            }
            if (cc != null && !"".equals(cc)) {
                String[] ccs = cc.split(",|;");
                for (int i = 0; i < ccs.length; i++) {
                    message.addRecipient(Message.RecipientType.CC, new InternetAddress(ccs[i]));
                }
            }
            if (bcc != null && !"".equals(bcc)) {
                String[] bccs = bcc.split(",|;");
                for (int i = 0; i < bccs.length; i++) {
                    message.addRecipient(Message.RecipientType.BCC, new InternetAddress(bccs[i]));
                }
            }

            message.setSentDate(new Date());
            message.saveChanges();
            // logger.info("Mail transport: host=" + host + ", port=" + port + ", userName=" + userName + ",
            // password=" + password + ", props=" + props + ", message=" + message);
            transport.connect(host, port, userName, password);
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
        } catch (Exception e) {
            result = e.getMessage();
        }
        return result;
    }

    
    public static void sendMail(String toEmail, String subject, String body) throws Exception {

        Properties props = new Properties();
        props.put("mail.smtp.host", EMAIL_SITE_HOST);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", EMAIL_SITE_PORT);
        props.put("mail.smtp.starttls.enable", EMAIL_STARTTLS);
        props.put("mail.smtp.ssl.enable", EMAIL_SSL);

        Session session = Session.getInstance(props, new javax.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(EMAIL_SITE_USER, EMAIL_SITE_PASSWORD);
            }
        });

        MimeMessage message = new MimeMessage(session);
        message.setFrom(new InternetAddress(EMAIL_SITE_FROM));
        message.setRecipients(Message.RecipientType.TO,
                InternetAddress.parse(toEmail));
        message.setSubject(subject);
        // message.setText(body);
        message.setContent(body, "text/html; charset=UTF-8");

        Transport.send(message);
    }
}
