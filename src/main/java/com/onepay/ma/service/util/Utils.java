package com.onepay.ma.service.util;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.google.auth.oauth2.GoogleCredentials;

import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.logging.Logger;
import java.util.logging.Level;

public class Utils {

    private static final String MOBILE_PARTTERN = PropsUtil.get("mobile.validation.pattern", "");
    private static final String EMAIL_PATTERN = PropsUtil.get("email.validation.pattern", "");
    private static final String TEXT_PATTERN = "^[a-zA-Z0-9\\?.,!():\\-\\/\\s\\r\\nÀ-ỹ]+$";
    public static final String API_URL_SOURCE = PropsUtil.get("onepay_google_notifucation_api_url_scopes", "https://www.googleapis.com/auth/firebase.messaging"); // mOnePAY
    public static final String API_URL_JSON = "production-mpayvn.json";

    private static Logger logger = Logger.getLogger(Utils.class.getName());

    public static String convertMobileBumber(String mobile1) {
        String mobile = mobile1;

        // Create a Pattern object
        Pattern r = Pattern.compile(MOBILE_PARTTERN);

        Matcher m = r.matcher(mobile);
        boolean isMobile = m.find();
        if (!isMobile) {
            throw IErrors.INVALID_MOBILE_NUMBER;
        }

        String number = "0123456789";
        String outPut = "";
        for (int j = 0; j < mobile1.length(); j++) {
            char c = mobile1.charAt(j);
            if (number.contains(String.valueOf(c))) {
                outPut += c;
            }
        }
        String text = outPut.replaceAll("\\+", "");
        if (text.charAt(0) == '0') {
            text = "84" + text.substring(1, text.length());
        } else if (text.charAt(0) == '9' || text.charAt(0) == '1') {
            text = "84" + text;
        }
        return text;
    }

    public static boolean validateEmail(String email) {
        Pattern pattern = Pattern.compile(EMAIL_PATTERN);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    public static boolean validatePhone(String mobile) {
        Pattern pattern = Pattern.compile(MOBILE_PARTTERN);
        Matcher matcher = pattern.matcher(mobile);
        return matcher.matches();
    }

    public static boolean validateText(String desc) {
        Pattern pattern = Pattern.compile(TEXT_PATTERN, Pattern.UNICODE_CHARACTER_CLASS);
        Matcher matcher = pattern.matcher(desc);
        return matcher.matches();
    }

    public static boolean isBigMerchant(String merchantId) {
        String[] bigMerchant = PropsUtil.get("bigMerchantId", "").split(",");
        if (null != bigMerchant && bigMerchant.length != 0 && Arrays.stream(bigMerchant).anyMatch(merchantId::equals)) {
            return true;
        } else
            return false;
    }

    public static Integer getPageSize() {
       return Integer.parseInt(PropsUtil.get("page_size", "200"));
    }

    public static boolean isInteger(String s) {
        try {
            Integer.parseInt(s);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static JsonObject decodeVpcResponse(String response) throws Exception {
        String[] params = response.split("&");
        Map<String, Object> fields = new HashMap<>();
        for (String kv : params) {
            String[] kvArray = kv.split("=");
            if (kvArray.length == 2) {
                fields.put(URLDecoder.decode(kvArray[0], "UTF-8"), URLDecoder.decode(kvArray[1], "UTF-8"));
            }
        }

        return new JsonObject(fields);

    }

    public static String toHttpParam(Object input) {
        StringBuilder result = new StringBuilder();
        for (Field prop : input.getClass().getDeclaredFields()) {
            try {
                prop.setAccessible(true);
                result.append(prop.getName() + "=" + URLEncoder.encode(prop.get(input).toString(), "UTF-8") + "&");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result.toString();
    }

    public static void main(String[] args) {
        // convertMobileBumber("***********");
    }

    public static void execBlocking(RoutingContext rc, ExecBlockingHandler handler) {
        rc.vertx().executeBlocking(promise -> {
            try {
                handler.handle();
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                rc.fail(e);
            }
        }, false, ar -> {
            if (ar.failed()) {
                logger.log(Level.SEVERE, "", ar.cause());
                rc.fail(ar.cause());
            }
        });
    }

    public static double formatCurrencyAmount(String currency, Double amount) {
        if (null != amount) {
            switch (currency) {
                case "VND":
                    return Math.round(amount);
                case "USD":
                    return Math.round(amount * 100) / 100;
                case "THB":
                    return Math.round(amount);
                case "SGD":
                    return Math.round(amount * 100) / 100;
                case "MYR":
                    return Math.round(amount * 100) / 100;
                case "IDR":
                    return Math.round(amount);
                case "JPY":
                    return Math.round(amount);
                case "KRW":
                    return Math.round(amount);
                case "TWD":
                    return Math.round(amount * 100) / 100;
                case "CNY":
                    return Math.round(amount * 100) / 100;
                default:
                    return amount;
            }
        } else {
            return 0d;
        }
    }
    public interface ExecBlockingHandler {
        void handle() throws Exception;
    }

    public static boolean hasColumn(ResultSet rs, String columnName) throws SQLException {
        ResultSetMetaData rsmd = rs.getMetaData();
        int columns = rsmd.getColumnCount();
        for (int x = 1; x <= columns; x++) {
            if (columnName.equalsIgnoreCase(rsmd.getColumnName(x))) {
                return true;
            }
        }
        return false;
    }

    public static String getColumnString(ResultSet rs, String columnName) throws SQLException {
        return hasColumn(rs, columnName) ? rs.getString(columnName.toUpperCase()) : null;
    }

    public static Integer getColumnInteger(ResultSet rs, String columnName) throws SQLException {
        return hasColumn(rs, columnName) ? Integer.valueOf(rs.getInt(columnName.toUpperCase())) : null;
    }

    public static Long getColumnLong(ResultSet rs, String columnName) throws SQLException {
        return hasColumn(rs, columnName) ? Long.valueOf(rs.getInt(columnName.toUpperCase())) : null;
    }

    public static Double getColumnDouble(ResultSet rs, String columnName) throws SQLException {
        return hasColumn(rs, columnName) ? Double.valueOf(rs.getDouble(columnName.toUpperCase())) : null;
    }

    public static java.sql.Timestamp getColumnTimeStamp(ResultSet rs, String columnName) throws SQLException {
        return hasColumn(rs, columnName) ? rs.getTimestamp(columnName.toUpperCase()) : null;
    }
    public static <T> T nvl(T value, T defValue) {
        return value != null ? value : defValue;
    }

    public static <T> T nvl(T... values) {
        for (T value : values) {
            if (value != null)
                return value;
        }
        return null;
    }

    public static String mask(Object source) {
        if (source == null)
            return "null";
        try {
            String accountNumberRegexp = "((?:account_number|accountNumber|accountNo|account_no)\"?[=:]\\s*\"?)\\d+(\\d{2}\"?)";

            String cardNumberRegexp = "([\"|']?(?:number|vpc_CardNum|card_no|CardNum|cardNo|accountNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";

            String cardNumberRegexp2 = "(<(?:accountNumber)?[>]?\\d{6})\\d+(\\d{4})<?";

            String cardDateRegexp2 = "(<(expirationMonth|expirationYear)>)(\\d+)";

            String cardDateRegexp = "([\"|']?(?:vpc_CardExp|month|year|CardExp|CardExpMonth|CardExpYear|cardExp|CardMonth|CardYear|vpc_CardMonth|vpc_CardYear|CardYear|CardMonth|expire_month|expire_year)[\"|']?[=:][\"|']?)\\d+";

            String cvvRegexp = "([\"|']?(?:cvv|vpc_CardSecurityCode|CardSecurityCode|card_security_code)[\"|']?[=:][\"|']?)\\d+";

            String icvvRegexp = "(icvv|initCVV[\"|']:[\"|'][^[\"|']]{6})[^[\"|']]+";

            String phoneRegex = "([\"|']?(?:phone|customerPhone|userPhone|mobile|mobile_number)[\"|']?[=:][\"|']?)\\d+(\\d{2})[\"|']?";

            String emailRegex = "([\"|']?(?:email|customerEmail|vpc_Customer_Email)[\"|']?[:|=])([\"|']?[a-z|A-Z|0-9]{1})([a-z|A-Z|0-9]+)?([a-z|A-Z|0-9]{1}@)([a-z|A-Z|0-9]+\\.)([a-z|A-Z|0-9]+[\"|']?)";

            String tokenNumberRegexp = "([\"|']?(?:vpc_TokenNum|tokenNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";
            return ("" + source)
                    .replaceAll(accountNumberRegexp, "********$2")
                    .replaceAll(cardNumberRegexp, "$1*******$2")
                    .replaceAll(cardNumberRegexp2, "$1*******$2")
                    .replaceAll(cardDateRegexp, "$1****")
                    .replaceAll(cardDateRegexp2, "$1****")
                    .replaceAll(cvvRegexp, "$1***")
                    .replaceAll(icvvRegexp, "$1***")
                    .replaceAll(phoneRegex, "$1******$2")
                    .replaceAll(emailRegex, "$1$2******$4$5$6")
                    .replaceAll(tokenNumberRegexp, "$1*******$2");

        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
            return "" + source;
        }
    }
    
    public static String excludeSensitiveInfo (String source) {
        if (source == null) return "null";
        String accountNumberRegexp = "((?:account_number|accountNumber|accountNo|account_no)\"?[=:]\\s*\"?)\\d+(\\d{2}\"?)";

        String cardNumberRegexp = "([\"|']?(?:number|vpc_CardNum|card_no|CardNum|cardNo|accountNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";

        String cardNumberRegexp2 = "(<(?:accountNumber)?[>]?\\d{6})\\d+(\\d{4})<?";

        String cardDateRegexp2 = "(<(expirationMonth|expirationYear)>)(\\d+)";

        String cardDateRegexp = "([\"|']?(?:vpc_CardExp|month|year|CardExp|CardExpMonth|CardExpYear|cardExp|CardMonth|CardYear|vpc_CardMonth|vpc_CardYear|CardYear|CardMonth|expire_month|expire_year)[\"|']?[=:][\"|']?)\\d+";

        String cvvRegexp = "([\"|']?(?:cvv|vpc_CardSecurityCode|CardSecurityCode|card_security_code)[\"|']?[=:][\"|']?)\\d+";

        String icvvRegexp = "(icvv|initCVV[\"|']:[\"|'][^[\"|']]{6})[^[\"|']]+";

        String phoneRegex = "([\"|']?(?:phone|customerPhone|userPhone|mobile|mobile_number)[\"|']?[=:][\"|']?)\\d+(\\d{2})[\"|']?";

        String emailRegex = "([\"|']?(?:email|customerEmail|vpc_Customer_Email)[\"|']?[:|=])([\"|']?[a-z|A-Z|0-9]{1})([a-z|A-Z|0-9]+)?([a-z|A-Z|0-9]{1}@)([a-z|A-Z|0-9]+\\.)([a-z|A-Z|0-9]+[\"|']?)";
        
        String tokenNumberRegexp = "([\"|']?(?:vpc_TokenNum|tokenNumber)[\"|']?[=:][\"|']?\\d{6})\\d+(\\d{4}[\"|']?)";
        return source
                .replaceAll(accountNumberRegexp, "")
                .replaceAll(cardNumberRegexp, "")
                .replaceAll(cardNumberRegexp2, "")
                .replaceAll(cardDateRegexp, "")
                .replaceAll(cardDateRegexp2, "")
                .replaceAll(cvvRegexp, "")
                .replaceAll(icvvRegexp, "")
                .replaceAll(phoneRegex, "")
                .replaceAll(emailRegex, "")
                .replaceAll(tokenNumberRegexp, "");
    }

    public static String getAccessToken() throws FileNotFoundException, IOException {
        GoogleCredentials googleCredentials = GoogleCredentials
                .fromStream(Thread.currentThread().getContextClassLoader()
                .getResourceAsStream(API_URL_JSON))
                .createScoped(Arrays.asList(API_URL_SOURCE));
        googleCredentials.refresh();
        return googleCredentials.getAccessToken().getTokenValue();
    }
    //EncodeHmacSha256Base64Url
    public static String encodeHmacSha256Base64Url(String data, String key) {
        try {
            // Tạo đối tượng SecretKeySpec từ key
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");

            // Tạo đối tượng Mac
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKeySpec);

            // Tính HMAC
            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // Mã hóa Base64Url
            return Base64.getUrlEncoder().withoutPadding().encodeToString(hmacBytes);
        } catch (Exception e) {
            throw new RuntimeException("Error encoding HMAC SHA256 Base64Url", e);
        }
    }

    public static String detectPlatform(String ua) {
        if (ua == null) return "Unknown";
        ua = ua.toLowerCase();

        if (ua.contains("windows")) return "Windows";
        if (ua.contains("android")) return "Android";
        if (ua.contains("iphone") || ua.contains("ios")) return "iOS";
        if (ua.contains("mac os")) return "MacOS";
        if (ua.contains("linux")) return "Linux";
        if (ua.contains("postman")) return "Postman";
        if (ua.contains("java")) return "Java Client";
        return "Unknown";
    }
}
