package com.onepay.ma.service.util;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/6/16.
 */
public class RoutePool {


    public static final String APPROVAL_ROUTE = "/approval";


    public static final String REFUND_APPROVAL_ROUTE = "/refund-approval";


    public static final String REFUND_APPROVAL_ROUTE_ID = "/refund-approval/:id";

    public static final String APPROVAL_ROUTE_ID = "^(?!/approval/merchant|/approval/user-group|/approval/channel)(/approval)/([^/]+)$";
    public static final String ROUTE_NOT_EVENT_BUS = "^((?!/eventbus).)*$";

    public static final String USER_ROUTE_SID = "/user/:userId";

    public static final String USER_PROFILE_ROUTE = "/user-profile";

    public static final String CLIENT_BILLING_ROUTE = "/client/billing";

    public static final String TRANSACTION_BILLING_ROUTE = "/transaction/billing";

    public static final String TRANSACTION_BILLING_ROUTE_ID = "/transaction/billing/:id";

    public static final String TRANSACTION_BILLING_HISTORY_ROUTE = "/transaction/billing/:id/history";

    public static final String DOWNLOAD_TRANSACTION_BILLING_ROUTE = "/transaction/billing/file";

    public static final String TRANSACTION_DOMESTIC_ROUTE = "/transaction/domestic";

    public static final String REPORT_DOMESTIC_ROUTE = "/report/domestic";

    public static final String TRANSACTION_DOMESTIC_HISTORY_ROUTE = "/transaction/domestic/:id/history";

    public static final String TRANSACTION_DOMESTIC_ROUTE_ID = "/transaction/domestic/:id";

    public static final String SAMSUNG_TRANSACTION_DOMESTIC_ROUTE = "/samsung/transaction/domestic";

    public static final String SAMSUNG_TRANSACTION_DOMESTIC_ROUTE_ID = "/samsung/transaction/domestic/:id";

    public static final String TRANSACTION_INTERNATIONAL_ROUTE = "/transaction/international";

    public static final String PAYMENT_INTERNATIONAL_ROUTE = "/payment-authentication";
    public static final String PAYMENT_INTERNATIONAL_ROUTE_ID = "/payment-authentication/:id";

    public static final String ORDER_MPAY_ROUTE = "/order/mpay";
    public static final String QUERY_ORDER_MPAY_ROUTE = "/order/query";

    public static final String ORDER_MPAY_ROUTE_ID = "/order/mpay/:id";

    public static final String LIST_TRANSACTION_MPAY_ROUTE = "/list/transaction/mpay";

    public static final String TRANSACTION_MPAY_ROUTE = "/transaction/mpay";
    public static final String SAMSUNG_TRANSACTION_MPAY_ROUTE = "/samsung/transaction/mpay";
    public static final String MOCA_TRANSACTION_MPAY_ROUTE = "/moca/transaction/mpay";

    public static final String REPORT_MPAY_ROUTE = "/report/mpay";
    public static final String REPORT_PAYMENT_MPAY_ROUTE = "/report/mpay/payment";
    public static final String REPORT_MPAY_ROUTE_MOCA = "/moca/report/mpay";

    public static final String REPORT_MPAY_PR_ROUTE = "/report/mpay-pr";
    public static final String TRANSACTION_MPAY_PR_ROUTE = "/transaction/mpay-pr";

    public static final String TRANSACTION_MPAY_PR_ROUTE_ID = "/transaction/mpay-pr/:id";

    public static final String TRANSACTION_MPAY_HISTORY_ROUTE = "/transaction/mpay/:id/history";

    public static final String TRANSACTION_MPAY_ROUTE_ID = "/transaction/mpay/:id";
    public static final String SAMSUNG_TRANSACTION_MPAY_ROUTE_ID = "/samsung/transaction/mpay/:id";
    public static final String MOCA_TRANSACTION_MPAY_ROUTE_ID = "/moca/transaction/mpay/:id";

    public static final String TRANSACTION_UPDATE_MPAY_ROUTE_ID = "/transaction/mpay/update/:id";

    public static final String TRANSACTION_INTERNATIONAL_ROUTE_REGEX = "^/transaction/international$|/transaction/international/[0-9]*$|/transaction/international/[0-9]*/history$";

    public static final String TRANSACTION_DOMESTIC_ROUTE_REGEX = "^/transaction/domestic|/transaction/domestic/[0-9]*$";

    public static final String TRANSACTION_INTERNATIONAL_HISTORY_ROUTE = "/transaction/international/:id/history";

    public static final String TRANSACTION_AUTHORIZE_HISTORY_ROUTE = "/transaction/international/authorize/:id/history";

    public static final String TRANSACTION_CAPTURE_HISTORY_ROUTE = "/transaction/international/capture/:id/history";

    public static final String TRANSACTION_REFUND_CAPTURE_HISTORY_ROUTE = "/transaction/international/refund-capture/:id/history";

    public static final String TRANSACTION_PROMOTION_ROUTE = "/transaction/promotion";

    public static final String REPORT_GENERAL_ROUTE = "/report/general";

    public static final String DOWNLOAD_REPORT_GENERAL_ROUTE = "/report/general/file";

    public static final String TRANSACTION_PROMOTION_ROUTE_ID = "/transaction/promotion/:id";

    public static final String REFUND_INTERNATIONAL_ROUTE = "/refund/international";

    public static final String REFUND_INTERNATIONAL_ROUTE_REGEX = "^/refund/international$|/refund/international/[0-9]*$";

    public static final String REFUND_DOMESTIC_ROUTE = "/refund/domestic";

    public static final String REFUND_DOMESTIC_ROUTE_REGEX = "^/refund/domestic|/refund/domestic/[0-9]*$";

    public static final String ACQUIRER_DOMESTIC_ROUTE = "/acquirer/domestic";

    public static final String DOWNLOAD_CDR_ROUTE = "/cdr/file";

    public static final String MERCHANT_ROUTE = "/merchant";

    public static final String CURRENCIES_MERCHANT_ROUTE_ID = "/currencies-merchant/:id";

    public static final String CUSTOMER_MERCHANT_ROUTE_ID = "/customer-merchant/:userId";

    public static final String USER_MERCHANT_ROUTE = "/merchant-user/";

    public static final String PARTNER_MERCHANT_ROUTE = "/merchant-partner";

    public static final String TERMINAL_ROUTE = "/terminal";

    public static final String STORE_ROUTE = "/store";

    public static final String TERMINAL_ROUTE_MER_ID = "/terminal/:id";

    public static final String DOWNLOAD_INTERNATIONAL_TRANSACTION_ROUTE = "/transaction/international/file";

    public static final String DOWNLOAD_INTERNATIONAL_PAYMENT_ROUTE = "/payment-authentication/file";

    public static final String DOWNLOAD_MPAY_PR_REPORT_ROUTE = "/report/mpay-pr/file";

    public static final String DOWNLOAD_MPAY_PR_TRANSACTION_ROUTE = "/transaction/mpay-pr/file";

    public static final String DOWNLOAD_MPAY_REPORT_ROUTE = "/report/mpay/file";
    public static final String DOWNLOAD_MPAY_REPORT_PAYMENT_ROUTE = "/report/mpay/payment/file";
    public static final String DOWNLOAD_MPAY_REPORT_ROUTE_MOCA = "/moca/report/mpay/file";


    public static final String DOWNLOAD_MPAY_TRANSACTION_ROUTE = "/transaction/mpay/file";
    public static final String SAMSUNG_DOWNLOAD_MPAY_TRANSACTION_ROUTE = "/samsung/transaction/mpay/file";
    public static final String MOCA_DOWNLOAD_MPAY_TRANSACTION_ROUTE = "/moca/transaction/mpay/file";

    public static final String DOWNLOAD_DOMESTIC_REFUND_ROUTE = "/refund/domestic/file";

    public static final String DOWNLOAD_INTERNATIONAL_REFUND_ROUTE = "/refund/international/file";

    public static final String DOWNLOAD_INTERNATIONAL_EMAIL_TEMPLATE_REFUND_ROUTE = "/refund/international/email-template/file";

    public static final String DOWNLOAD_INTERNATIONAL_TRANSACTION_PROMOTION_ROUTE = "/transaction/promotion/file";

    public static final String DOWNLOAD_DOMESTIC_REPORT_ROUTE = "/report/domestic/file";

    public static final String DOWNLOAD_INTERNATIONAL_REPORT_ROUTE = "/report/international/file";

    public static final String DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE = "/transaction/domestic/file";

    public static final String SAMSUNG_DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE = "/samsung/transaction/domestic/file";

    public static final String CDR_ROUTE_COMPARE = "/cdr/comparison";

    public static final String CDR_ROUTE_FILE_LINE = "/cdr/file-line";

    public static final String CDR_ROUTE_SERVICE = "/cdr/service";

    public static final String REFUND_DOMESTIC_ROUTE_ID = "/refund/domestic/:transactionId";

    public static final String REFUND_DOMESTIC_UPDATE_ROUTE_ID = "/refund/domestic/:transactionId/update";

    public static final String REFUND_INTERNATIONAL_ROUTE_ID = "/refund/international/:transactionID";

    public static final String REPORT_INTERNATIONAL_ROUTE = "/report/international";

    public static final String RISK_ROUTE_IP = "/risk/ip/:ip";

    public static final String RISK_ROUTE_BIN = "/risk/bin/:bin";

    public static final String TRANSACTION_INTERNATIONAL_ROUTE_ID = "/transaction/international/:id";

    public static final String BRAND_CONFIG_ROUTER = "/brand-config/:brandId";

    public static final String SAMSUNG_TRANSACTION_INTERNATIONAL_ROUTE_ID = "/samsung/transaction/international/:id";

    public static final String SAMSUNG_AUTHORIZE_TRANSACTION_INTERNATIONAL_ROUTE_ID = "/samsung/authorize/transaction/international/:id";

    public static final String SAMSUNG_CAPTURE_TRANSACTION_INTERNATIONAL_ROUTE_ID = "/samsung/capture/transaction/international/:id";

    public static final String SAMSUNG_REFUND_CAPTURE_TRANSACTION_INTERNATIONAL_ROUTE_ID = "/samsung/refund-capture/transaction/international//:id";

    public static final String PROMOTION_ROUTER = "/promotion";
    public static final String PROMOTION_CODE_ROUTER = "/code-promotion";
    public static final String PROMOTION_CODE_ROUTER_ID = "/code-promotion/:id";

    public static final String PROMOTION_ROUTER_ID = "/promotion/:promotionId";

    public static final String USER_SESSION_ROUTE_CODE = "/user/session/:code";

    public static final String USER_SESSION_ROUTE_ID = "/user/session/:userId";
    public static final String USER_SESSION_ROUTE = "/user/session";


    public static final String USER_PERMISSION_ROUTE = "/user/:userId/permission";

    public static final String USER_ROUTE = "/user";

    public static final String USER_FILE_ROUTE = "/user/:userId/file";

    public static final String ROLE_ROUTE = "/role";

    public static final String ROLE_ROUTE_ID = "/role/:id";

    public static final String PERMISSION_ROUTE = "/permission";

    public static final String FILE_DOWNLOAD_ROUTE = "/file/:fileNameHash/download";

    public static final String PERMISSION_ROUTE_ID = "/permission/:permissionId";

    public static final String APPROVAL_PROMOTION_ROUTE = "/approval/promotion";

    public static final String APPROVAL_PROMOTION_ROUTE_ID = "/approval/promotion/:id";

    public static final String PARTNER_CUSTOMER_ROUTE = "/partner";


    public static final String USERTOKEN_INTERNATIONAL_ROUTE = "/token-user/";
    public static final String USERTOKEN_INTERNATIONAL_ROUTE_ID = "/token-user/:id";
    public static final String TOKENTRAN_INTERNATIONAL_ROUTE = "/token-transaction/";
    public static final String APP_TOKEN_ROUTE = "/app-token";
    public static final String APP_TOKEN_ROUTE_ID = "/app-token/:id";
    public static final String MPAY_NOTIFY_ROUTE = "/mpay-notify";
    public static final String MPAY_NOTIFY_ROUTE_ID = "/mpay-notify/:id";
    public static final String MPAY_NOTIFICATION_ROUTE = "/mpay-notification";
    public static final String MPAY_NOTIFICATION_ROUTE_ID = "/mpay-notification/:id";
    public static final String PUSH_MPAY_NOTIFY_ROUTE = "/mpay-notification";
    public static final String PUSH_APP_NOTIFY_ROUTE = "/app-notification";
    public static final String PUSH_CUSTOMER_MPAY_NOTIFY_ROUTE = "/mpay-customer-notification";
    public static final String OP_NOTIFY_ROUTE = "/mobile-notification";
    public static final String OP_NOTIFY_ROUTE_ID = "/mobile-notification/:id";

    public static final String PUSH_CUSTOMER_MPAY_NOTIFY_ROUTE_ID = "/mpay-customer-notification/:id";

    public static final String CUSTOMER_MPAY_NOTIFY_MSG_ROUTE = "/customer-notify-msg";

    public static final String CUSTOMER_MPAY_NOTIFY_MSG_ROUTE_ID = "/customer-notify-msg/:id";
    public static final String TRANSACTION_FINANCICAL_ROUTE = "/transaction/financial";
    public static final String TRANSACTION_FINANCICAL_ROUTE_ID = "/transaction/financial/:id";
    public static final String DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE = "/transaction/financial/file";
    public static final String SAMSUNG_TRANSACTION_FINANCICAL_ROUTE = "/samsung/transaction/financial";
    public static final String SAMSUNG_TRANSACTION_FINANCICAL_ROUTE_ID = "/samsung/transaction/financial/:id";
    public static final String SAMSUNG_DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE = "/samsung/transaction/financial/file";


    // -------------------- start order approval --------------------

    public static final String INTERNATIONAL_ORDER_APPORVAL_ROUTE = "/order/approval/international";
    public static final String DOMESTIC_ORDER_APPORVAL_ROUTE = "/order/approval/domestic";

    // -------------------- end order approval --------------------

    // -------------------- start admin v2 --------------------
    public static final String USER_CHANGE_PASS_ROUTE = "/user_v2/changepass";
    public static final String USER_CHANGE_PASSWORD_V2 = "/user_v2/change-password";
    public static final String USER_V2_ROUTE = "/user_v2";
    public static final String USER_V2_UPDATE_ROUTE = "/user_v2/:id";
    public static final String USER_RESET_PASS_ROUTE = "/user_v2/resetpass";
    // -------------------- end admin v2 --------------------

    // -------------------- start statisics --------------------
    public static final String STATISTICS_REPORT_ROUTE = "/statisics";
    public static final String STATISTICS_REPORT_DETAIL_ROUTE = "/statisics-detail";
    public static final String STATISTICS_REPORT_DETAIL_DT_ROUTE = "/statisics-detail/detail";
    public static final String DOWNLOAD_STATISTICS_REPORT_ROUTE = "/statisics/file";
    public static final String DOWNLOAD_STATISTICS_DETAIL_REPORT_ROUTE = "/statisics-detail/file";
    public static final String DOWNLOAD_STATISTICS_DETAIL_DT_REPORT_ROUTE = "/statisics-detail/detail/file";
    // -------------------- end statisics --------------------

    // -------------------- start mpay refund --------------------
    public static final String REFUND_MPAY_ROUTE = "/refund/mpay";
    public static final String REFUND_MPAY_ROUTE_ID = "/refund/mpay/:id";
    public static final String DOWNLOAD_MPAY_REFUND_ROUTE = "/refund/mpay/file";
    public static final String UPDATE_MPAY_REFUND_ROUTE = "/refund/mpay/update/:id";
    public static final String MPAY_REFUND_APPROVAL_ROUTE_ID = "/refund-approval/mpay/:id";
    public static final String MPAY_REFUND_APPROVAL_ROUTE = "/refund-approval/mpay";
    // -------------------- end mpay refund --------------------

    // -------------------- start Reconcile --------------------
    public static final String REPORT_RECONCILE_ROUTE = "/reconcile/international";
    public static final String DOWNLOAD_RECONCILE_ROUTE = "/reconcile/international/file";
    public static final String DOWNLOAD_RECONCILE_PAYMENT_ROUTE = "/reconcile/payment/file";
    public static final String DOWNLOAD_RECONCILE_PAYMENT_DETAIL_ROUTE = "/reconcile/detail/file";


    public static final String REPORT_RECONCILE_PAYMENT_ROUTE = "/reconcile/payment";
    public static final String REPORT_RECONCILE_PAYMENT_DETAIL_ROUTE = "/reconcile/detail";
    // -------------------- end Reconcile --------------------

    // -------------------- start paypal --------------------
    public static final String LISTEN_DISPUTE_PAYPAL_CREATE_ROUTE = "/listener/paypal/dispute_create";
    public static final String LISTEN_DISPUTE_PAYPAL_UPDATE_ROUTE = "/listener/paypal/dispute_update";
    public static final String LISTEN_DISPUTE_PAYPAL_RESOLVE_ROUTE = "/listener/paypal/dispute_resolve";
    public static final String LISTEN_MERCHANT_ONBOARD_PAYPAL_ROUTE = "/listener/paypal/merchant-onboard";
    public static final String MERCHANT_ONBOARD_PAYPAL_ROUTE = "/paypal/merchant-onboard";
    public static final String MERCHANT_POST_PAYPAL_ROUTE = "/paypal/merchant-post";
    public static final String PAYPAL_SENTMAIL_REGISTER_ROUTE = "/paypal/sentmail-register";
    public static final String PAYPAL_SENT_MESSAGE_ROUTE = "/paypal/disputes/:id/send-message";
    // -------------------- end paypal --------------------


    // ---------------------INSTALLMENT------------------------------
    public static final String INSTALLMENT_BANK_ROUTE = "/installment/bank";

    // ---------------------START PAY OUT------------------------------

    public static final String FUNDS_TRANS_HISTORY_ROUTE = "/payout/fund-transfer-trans-his";
    public static final String FUNDS_TRANS_HISTORY_PENDING = "/payout/fund-transfer-trans-his-pending";
    public static final String FUNDS_TRANS_CHANGE_STATUS = "/payout/fund-transfer-change-status";
    public static final String FUNDS_TRANS_HISTORY_BY_ID_ROUTE = "/payout/fund-transfer-trans-his/:accountId/:fundsTransferId";
    public static final String RECEIVED_BANK_ROUTE = "/payout/received-bank-list";
    public static final String SUMMARY_FUNDS_TRANS_ROUTE = "/payout/summary-fund-transfer";
    public static final String DOWNLOAD_SUMMARY_FUNDS_TRANS_ROUTE = "/payout/summary-fund-transfer/file";
    public static final String DOWNLOAD_FUNDS_TRANS_HIS_ROUTE = "/payout/fund-transfer-trans-his/file";
    public static final String GET_MERCHANT_BY_USER = "/merchant-by-user";
    public static final String MERCHANT_ID_ROUTE = "/payout/merchant-id-list";
    public static final String RECEIVED_INFO_ROUTE = "/payout/received-info";
    public static final String MERCHANT_ACCOUNT_ROUTE = "/payout/merchant-account-list";
    public static final String TRANSFER_ROUTE = "/payout/transfer";
    public static final String TRANSFER_CHECK_AUTHOR = "/payout/transfer/partner-info";
    public static final String FUNDS_TRANS_BATCH_UPLOAD = "/file/payout/batch";
    public static final String FUNDS_TRANS_BATCH = "/payout/batch";
    public static final String FUNDS_TRANS_BATCH_DETAIL = "/payout/batch-detail";
    public static final String DOWNLOAD_FUNDS_TRANS_BATCH_DETAIL = "/payout/batch-detail/file";
    public static final String FUNDS_TRANS_BATCH_CHANGE_STATUS = "/payout/batch/changeStatus";
    public static final String FUNDS_TRANS_BATCH_CHECK_AUTHEN = "/payout/batch-detail-authen/:batchId";

    public static final String OPERATOR = "/payout/operator";
    public static final String OPERATOR_RESET_PASS = "/payout/operator/reset-password";
    public static final String OPERATOR_CHANGE_PASS = "/payout/operator/change-password/:operatorId";
    public static final String OPERATOR_CHANGE_STATUS = "/payout/operator/changeStatus";
    public static final String OPERATOR_GET_BY_USER_AND_PASS = "/payout/operator/get-by-user-and-pass";

    // ---------------------END QUICK TRANSFER------------------------------

    // -------------------- start statistic chart --------------------
    public static final String STATISTIC_CHART_INTERNATIONAL_ROUTE = "/statistic-chart/international";
    public static final String STATISTIC_CHART_DOMESTIC_ROUTE = "/statistic-chart/domestic";
    public static final String STATISTIC_CHART_TOTAL_ROUTE = "/statistic-chart/total";
    public static final String STATISTIC_CHART_INTERNATIONAL_DETAIL = "/statistic-chart/international-detail";
    public static final String STATISTIC_CHART_DOMESTIC_ROUTE_DETAIL = "/statistic-chart/domestic-detail";
    // public static final String STATISTIC_CHART_DOWNLOAD_DOMESTIC_ROUTE =
    // "/statistic-chart/domestic-download";
    public static final String DOWNLOAD_STATISTIC_CHART_DOMESTIC_ROUTE = "/statistic-chart/domestic/file";
    public static final String DOWNLOAD_STATISTIC_CHART_INTERNATIONAL_ROUTE = "/statistic-chart/international/file";
    public static final String DOWNLOAD_STATISTIC_CHART_TOTAL_DOMESTIC_ROUTE = "/statistic-chart/total/domestic/file";
    public static final String DOWNLOAD_STATISTIC_CHART_TOTAL_INTERNATIONAL_ROUTE = "/statistic-chart/total/international/file";
    public static final String DOWNLOAD_STATISTIC_CHART_TOTAL_ROUTE = "/statistic-chart/total/file";
    // -------------------- end statistic chart --------------------

    // ---------------------PAY COLLECT------------------------------
    public static final String PAYCOLLECT_SEARCH_USER_ROUTER = "/pay-collect/user";
    public static final String DOWNLOAD_PAYCOLLECT_USER_ROUTER = "/pay-collect/user/file";
    public static final String PAYCOLLECT_GET_ALL_USER_ROUTER = "/pay-collect/user/all";
    public static final String PAYCOLLECT_UPSERT_USER_ROUTER = "/pay-collect/user";
    public static final String PAYCOLLECT_GET_USER_ROUTER = "/pay-collect/partner/:partner_id/user/:user_id";
    public static final String PAYCOLLECT_GET_ALL_MERCHANT_ROUTER = "/pay-collect/merchant/all";

    public static final String PAY_COLLECT_TRANSACTION_ROUTE = "/pay-collect/transaction";
    public static final String PAY_COLLECT_BANK_ROUTE = "/pay-collect/bank";
    public static final String PAY_COLLECT_TRANSACTION_DETAIL_ROUTE = "/pay-collect/transaction/:id";
    public static final String PAY_COLLECT_DOWNLOAD_TRANSACTION_ROUTE = "/pay-collect/transaction/file";

    public static final String PAY_COLLECT_REPORT_ROUTE = "/pay-collect/report";
    public static final String PAY_COLLECT_DOWNLOAD_REPORT_ROUTE = "/pay-collect/report/file";
    // -------------------- END PAY COLLECT --------------------

    // -------------------- QUICK LINK --------------------
    //Static Link
    public static final String STATIC_LINK_LIST = "/static-link";
    public static final String STATIC_LINK_LIST_DETAIL = "/static-link/:id";
    public static final String STATIC_LINK_CHANGE_STATE = "/static-link-state/:id";

    //Payment Link
    public static final String PAYMENT_LINK_LIST = "/payment-link";
    public static final String PAYMENT_LINK_LIST_DOWNLOAD = "/payment-link/file";
    public static final String PAYMENT_LINK_LIST_FILE = "/payment-link-file";
    public static final String PAYMENT_LINK_LIST_DETAIL = "/payment-link/:id";
    public static final String PAYMENT_LINK_CHANGE_STATE = "/payment-link-state/:id";

    public static final String PAYMENT_LINK_MERCHANT_PROFILE = "/merchant-profile";
    public static final String PAYMENT_LINK_MERC_PROFILE_ID = "/list-merchant-profile";
    public static final String PAYMENT_LINK_MERC_ID_BY_PROFILE = "/merchant-id-by-profile";

    //exchange rate 
    public static final String EXCHANGE_RATE = "/exchange-rate/:id";
    public static final String EXCHANGE_RATE_CHANGE_STATE = "/exchange-rate/change-state/:id";
    public static final String EXCHANGE_RATE_CHANGE_TYPE = "/exchange-rate/change-type/:id";
    public static final String EXCHANGE_RATE_GET_CURRENCY = "/exchange-rate/getCurrencyByTemp/:id";
    public static final String EXCHANGE_RATE_GET_CURRENCY_WITHOUT_VCB = "/exchange-rate/getCurrencyWithoutVcb/:id";
    public static final String EXCHANGE_RATE_VCB = "/exchange-rate-vcb/:id";
    public static final String UPLOAD_FILE = "/upload-file";


    // -------------------- END QUICK LINK --------------------

    // VINPEARL REFUND
    public static final String TRANSACTION_VINPEARL_REFUND = "/ma-service/transaction/vinpearl/:merchant_id/refund/:id";
    // END VINPEARL REFUND
    
    // ---------------------FINANCIAL REPORT------------------------------
    public static final String MERCHANT_PAYMENT_BY_USER_ID = "/merchant-payment";

    // ---------------------END FINANCIAL REPORT------------------------------

}
