package com.onepay.ma.service.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 1/5/2015.
 * Read server config param from resources/server.properties file
 */
public class PropsUtil {

    /**
     * Get Properties value from resources/server.properties file by property key
     *
     * @param key
     * @param defaultValue
     * @return return property value mapping by key or return default value if property key not found
     */
    public static String get(String key, String defaultValue){

        try {

            InputStream inputStream = PropsUtil.class.getClassLoader().getResourceAsStream(_SERVER_PROPERTIES_FILE_NAME);

            Properties serverProperties = new Properties();
            serverProperties.load(new InputStreamReader(inputStream, "UTF-8"));

            inputStream.close();

            if(serverProperties.getProperty(key) == null){
                //LOGGER.log( Level.CONFIG, "Property key " + key + " not found in " + _SERVER_PROPERTIES_FILE_NAME + ". Return default value " + defaultValue);
                return "";
            }

            return serverProperties.getProperty(key, defaultValue);

        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "", e);
            return defaultValue;
        }
    }

    private static final String _SERVER_PROPERTIES_FILE_NAME = "server.properties";

    private static final Logger LOGGER = Logger.getLogger(PropsUtil.class.getName());
}
