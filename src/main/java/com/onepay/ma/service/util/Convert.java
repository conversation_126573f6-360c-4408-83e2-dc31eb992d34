package com.onepay.ma.service.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;

import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;


/**
 * Created by thieu<PERSON><PERSON><PERSON> on 2/16/16.
 */
public  final class Convert {
    private static byte[] hexArray = new byte[]{(byte)48, (byte)49, (byte)50, (byte)51, (byte)52, (byte)53, (byte)54, (byte)55, (byte)56, (byte)57, (byte)65, (byte)66, (byte)67, (byte)68, (byte)69, (byte)70};
    private static byte[] decodeHexArray = new byte[103];


    public static String toString(Object obj) {
        String s = "";
        int index = 0;
        if(obj == null) {
            s = "null";
        } else if(obj instanceof Map) {
            s = s + "{";

            for(Iterator i = ((Map)obj).keySet().iterator(); i.hasNext(); ++index) {
                Object key = i.next();
                Object value = ((Map)obj).get(key);
                if(index > 0) {
                    s = s + ", ";
                }

                s = s + key + "=" + toString(value);
            }

            s = s + "}";
        } else if(obj instanceof Object[]) {
            s = s + "[";

            for(int var6 = 0; var6 < ((Object[])((Object[])obj)).length; ++var6) {
                if(index > 0) {
                    s = s + ", ";
                }

                s = s + toString(((Object[])((Object[])obj))[var6]);
                ++index;
            }

            s = s + "]";
        } else {
            s = obj.toString();
        }

        return s;
    }

    public static String toString(byte number, String pattern) {
        return toString((byte)number, pattern, (String)null);
    }

    public static String toString(int number, String pattern) {
        return numberToString(Integer.valueOf(number), pattern, (String)null);
    }

    public static String toString(long number, String pattern) {
        return numberToString(Long.valueOf(number), pattern, (String)null);
    }

    public static String toString(short number, String pattern) {
        return numberToString(Short.valueOf(number), pattern, (String)null);
    }

    public static String toString(double number, String pattern) {
        return numberToString(Double.valueOf(number), pattern, (String)null);
    }

    public static String toString(byte number, String pattern, String defaultValue) {
        return numberToString(Byte.valueOf(number), pattern, defaultValue);
    }

    public static String toString(int number, String pattern, String defaultValue) {
        return numberToString(Integer.valueOf(number), pattern, defaultValue);
    }

    public static String toString(long number, String pattern, String defaultValue) {
        return numberToString(Long.valueOf(number), pattern, defaultValue);
    }

    public static String toString(short number, String pattern, String defaultValue) {
        return numberToString(Short.valueOf(number), pattern, defaultValue);
    }

    public static String toString(double number, String pattern, String defaultValue) {
        return numberToString(Double.valueOf(number), pattern, defaultValue);
    }

    private static String numberToString(Object number, String pattern, String defaultValue) {
        try {
            DecimalFormat e = new DecimalFormat(pattern);
            return e.format(number);
        } catch (Exception var4) {
            var4.printStackTrace();
            return defaultValue;
        }
    }

    public static String toString(Date date, String pattern) {
        return toString(date, pattern, (String)null);
    }

    public static String toString(Date date, String pattern, String defaultValue) {
        return toString(date, pattern, (Locale)null, defaultValue);
    }

    public static String toString(Date date, String pattern, Locale locale) {
        return toString(date, pattern, locale, (String)null);
    }

    public static String toString(Date date, String pattern, Locale locale, String defaultValue) {
        return toString(date, (Format)(locale != null?new SimpleDateFormat(pattern, locale):new SimpleDateFormat(pattern)), (String)defaultValue);
    }

    public static String toString(Date date, Format formater, String defaultValue) {
        try {
            return formater.format(date);
        } catch (Exception var4) {
            return defaultValue;
        }
    }


    public static byte[] toBase64(byte[] binaryData) {
        return Base64.encodeBase64(binaryData);
    }

    public static String toBase64String(byte[] binaryData) {
        return new String(toBase64(binaryData));
    }

    public static byte[] toHex(byte[] input) {
        ByteArrayOutputStream bOut = new ByteArrayOutputStream();

        for(int i = 0; i < input.length; ++i) {
            bOut.write(hexArray[input[i] >>> 4 & 15]);
            bOut.write(hexArray[input[i] & 15]);
        }

        return bOut.toByteArray();
    }

    public static byte[] decodeHex(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static String toHexString(byte[] input) {
        return new String(toHex(input));
    }




    public static String hash(String value) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        byte[] data = value.getBytes();
        byte[] result;
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(value.getBytes("UTF-8")); // Change this to "UTF-16" if needed
        result = md.digest();
        return new String(Convert.encodeHex(result)).toUpperCase();
    }

    public static byte[] encodeHex(byte[] value) {
        return Hex.encodeHexString(value).getBytes();
    }

    public static byte[] decodeHex(byte[] value) {
        return Hex.encodeHexString(value).getBytes();
    }

}
