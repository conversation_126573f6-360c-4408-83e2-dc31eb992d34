package com.onepay.ma.service.util;


import com.onepay.ma.service.util.IErrors;
import org.apache.commons.lang3.StringEscapeUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.sql.ResultSet;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by administrator on 10/15/15.
 */
public class CsvFileWriter {
    //Delimiter used in CSV file
    private static final String COMMA_DELIMITER = ",";
    private static final String NEW_LINE_SEPARATOR = "\n";
    private static final String[] STATE_SUCCESS = {"Y", "M"};
    private static final String[] STATE_FAILED = {"N","A","P","S", "I"};
    private static final String[] STATE_UNDETERMINE = {"T","U"};
    private static final String[] STATE_NOT_ENROLLED = {"E"};
    private static String header = "No";
    //CSV file header
    public static void writeCsvFileMoreHeader(String headerTop, String fileName, String fileHeader,List<Map<String, String>> columns, ResultSet rs) throws IOException {


        FileWriter fileWriter = null;
        // header += "," + fileHeader;
        try {
            fileWriter = new FileWriter(fileName, true);
            fileWriter.append(NEW_LINE_SEPARATOR);
            fileWriter.append(NEW_LINE_SEPARATOR);
            fileWriter.append(headerTop);
            fileWriter.append(NEW_LINE_SEPARATOR);
            //Write the CSV file header
            fileWriter.append("No, " + fileHeader);

            //Add a new line separator after the header
            fileWriter.append(NEW_LINE_SEPARATOR);
            int index = 0;
            if(rs != null) {
                while (rs.next()) {
                    index++;
                    fileWriter.append(String.valueOf(index));
                    fileWriter.append(COMMA_DELIMITER);
                    for (Map map : columns) {
                        if (map.get("type") != null && map.get("type").toString().equals("2")) {
                            if (rs.getObject(map.get("name").toString()) != null) {
                                fileWriter.append(StringEscapeUtils.escapeCsv(rs.getString(map.get("name").toString())));

                            }
                        }else if (map.get("type") != null && map.get("type").toString().equals("3")) {
                            String key = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                            String text = PropsUtil.get(key, "Blank");
                            fileWriter.append("\"" + key + "-"+ String.valueOf(text)+ "\"");
                        }else if (map.get("type") != null && map.get("type").toString().equals("4")) {
                            String key = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                            if(key.equals("0") ||  key.equals("400") ){
                                fileWriter.append("\"" + "Successful" + "\"");
                            }else{
                                fileWriter.append("\"" + "Failed" + "\"");
                            }

                        }else if (map.get("type") != null && map.get("type").toString().equals("5")) {
                            String text = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                            if( Arrays.asList(STATE_FAILED).contains(text)){
                                fileWriter.append("\"" + "Successful" + "\"");
                            }else if( Arrays.asList(STATE_FAILED).contains(text)){
                                fileWriter.append("\"" + "Failed" + "\"");
                            }else if( Arrays.asList(STATE_UNDETERMINE).contains(text)){
                                fileWriter.append("\"" + "Undetermined" + "\"");
                            }else if( Arrays.asList(STATE_NOT_ENROLLED).contains(text)){
                                fileWriter.append("\"" + "Not Enrolled" + "\"");
                            }else{
                                fileWriter.append("\"\"");
                            }


                        }else if (map.get("type") != null && map.get("type").toString().equals("6")) {
                            String key = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                            fileWriter.append("\"" + key +  "\"");
                        }else {
                            if (rs.getObject(map.get("name").toString()) != null) {
                                fileWriter.append("=\"" + StringEscapeUtils.escapeCsv(rs.getString(map.get("name").toString())) + "\"");
                            } else {
                                fileWriter.append(StringEscapeUtils.escapeCsv(""));
                            }
                        }
                        fileWriter.append(COMMA_DELIMITER);
                    }
                    fileWriter.append(NEW_LINE_SEPARATOR);
                }
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate File " + fileName + " Error", e);
            File file = new File(fileName);
            if(file.exists()) {
                Files.delete(file.toPath());
            }
            LOGGER.log(Level.SEVERE,"GENERATE FILE", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            fileWriter.flush();
            fileWriter.close();

        }
    }

    //CSV file header
    public static void writeCsvFileTotal(String fileName, int startIndex, Double[] rs) throws IOException {


        FileWriter fileWriter = null;
        //header += "," + fileHeader;
        try {
            fileWriter = new FileWriter(fileName, true);

            fileWriter.append("Total");
            fileWriter.append(COMMA_DELIMITER);
            for (int index2 = 0; index2 < startIndex; index2++){
                fileWriter.append(COMMA_DELIMITER);
                fileWriter.append("=\"\"");
            }
            for(Double data : rs){
                fileWriter.append(COMMA_DELIMITER);
                fileWriter.append("\"" + String.valueOf(data) + "\"");

                //fileWriter.append(NEW_LINE_SEPARATOR);

            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            fileWriter.flush();
            fileWriter.close();

        }
    }

    public static void writeCsvFileWithTotal(String fileName, String fileHeader,List<Map<String, String>> columns, ResultSet rs, int startIndex, String[] listTotalColumns) throws IOException {
        Map<String, Object> totalRowsMap = new HashMap<>();
        FileWriter fileWriter = null;
        // header += "," + fileHeader;
        Double totalList;
        try {
            fileWriter = new FileWriter(fileName, true);
            fileWriter.append(NEW_LINE_SEPARATOR);
            fileWriter.append(NEW_LINE_SEPARATOR);
            //Write the CSV file header
            fileWriter.append("No, " + fileHeader);

            //Add a new line separator after the header
            fileWriter.append(NEW_LINE_SEPARATOR);
            int index = 0;
            while (rs.next()) {
                index++;
                fileWriter.append(String.valueOf(index));
                fileWriter.append(COMMA_DELIMITER);
                for(Map map : columns){
                    if(map.get("type") != null && map.get("type").toString().equals("2")) {
                        if(rs.getObject(map.get("name").toString()) != null) {
                            fileWriter.append(StringEscapeUtils.escapeCsv(String.valueOf(rs.getString(map.get("name").toString()))));
                            double amount = (totalRowsMap.get(map.get("name").toString()) != null ?
                                    Double.parseDouble(totalRowsMap.get(map.get("name").toString()).toString()) : 0) + rs.getDouble(map.get("name").toString());
                            totalRowsMap.put(map.get("name").toString(),  amount);
                        }else{
                            fileWriter.append(StringEscapeUtils.escapeCsv(""));
                        }
                    }else if (map.get("type") != null && map.get("type").toString().equals("3")) {
                        String text = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                        text = PropsUtil.get(text, "Blank");
                        fileWriter.append(StringEscapeUtils.escapeCsv(String.valueOf(text)));
                    }else{
                        if(rs.getObject(map.get("name").toString()) != null) {
                            String value = String.valueOf(rs.getString(map.get("name").toString()));
                            boolean isNumber = Character.isDigit(value.charAt(0));
                            if(isNumber){
                                if(value.indexOf(",") != -1){
                                    fileWriter.append("\"\"" + StringEscapeUtils.escapeCsv(value) + "\"\"");
                                }else{
                                    fileWriter.append("=\"" + StringEscapeUtils.escapeCsv(value) + "\"");
                                }

                            }else{
                                if(value.indexOf(",") != -1){
                                    fileWriter.append("\"" + StringEscapeUtils.escapeCsv(value) + "\"");
                                }else{
                                    fileWriter.append(StringEscapeUtils.escapeCsv(value));
                                }
                            }

                        }else{
                            fileWriter.append(StringEscapeUtils.escapeCsv(""));
                        }
                    }
                    fileWriter.append(COMMA_DELIMITER);
                }
                fileWriter.append(NEW_LINE_SEPARATOR);
            }
            fileWriter.append("Total");
            fileWriter.append(COMMA_DELIMITER);
            for (int index2 = 0; index2 < startIndex; index2++){
                fileWriter.append(COMMA_DELIMITER);
                fileWriter.append("=\"\"");
            }
            for(String total : listTotalColumns){
                fileWriter.append(COMMA_DELIMITER);
                fileWriter.append("\"" + totalRowsMap.get(total) + "\"");
            }

            fileWriter.append(NEW_LINE_SEPARATOR);


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            fileWriter.flush();
            fileWriter.close();

        }
    }

    public static int writeCsvFileMerge(String fileName, String fileHeader,List<Map<String, String>> columns, ResultSet rs, boolean showHeader, int beginIndex ) throws IOException {

        int index = beginIndex;
        FileWriter fileWriter = null;
        // header += "," + fileHeader;
        try {
            fileWriter = new FileWriter(fileName, true);
            if(showHeader) {
                fileWriter.append(NEW_LINE_SEPARATOR);
                fileWriter.append(NEW_LINE_SEPARATOR);
                //Write the CSV file header
                fileWriter.append("No, " + fileHeader);
                //Add a new line separator after the header
                fileWriter.append(NEW_LINE_SEPARATOR);
            }


            while (rs.next()) {
                index++;
                fileWriter.append(String.valueOf(index));
                fileWriter.append(COMMA_DELIMITER);
                for(Map map : columns){
                    if(map.get("type") != null && map.get("type").toString().equals("2")) {
                        if(rs.getObject(map.get("name").toString()) != null) {
                            fileWriter.append(StringEscapeUtils.escapeCsv(String.valueOf(rs.getString(map.get("name").toString()))));
                        }else{
                            fileWriter.append(StringEscapeUtils.escapeCsv(""));
                        }
                    }else if (map.get("type") != null && map.get("type").toString().equals("3")) {
                        String key = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                        String text = PropsUtil.get(key, "Blank");
                        fileWriter.append("\"" + key + "-"+ String.valueOf(text)+ "\"");
                    }else if (map.get("type") != null && map.get("type").toString().equals("4")) {
                        String key = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                        if(key.equals("0") ||  key.equals("400") ){
                            fileWriter.append("\"" + "Successful" + "\"");
                        }else{
                            fileWriter.append("\"" + "Failed" + "\"");
                        }

                    }else if (map.get("type") != null && map.get("type").toString().equals("5")) {
                        String text = (rs.getObject(map.get("name").toString()) == null) ? "" : rs.getString(map.get("name").toString());
                        if( Arrays.asList(STATE_FAILED).contains(text)){
                            fileWriter.append("\"" + "Successful" + "\"");
                        }else if( Arrays.asList(STATE_FAILED).contains(text)){
                            fileWriter.append("\"" + "Failed" + "\"");
                        }else if( Arrays.asList(STATE_UNDETERMINE).contains(text)){
                            fileWriter.append("\"" + "Undetermined" + "\"");
                        }else if( Arrays.asList(STATE_NOT_ENROLLED).contains(text)){
                            fileWriter.append("\"" + "Not Enrolled" + "\"");
                        }else{
                            fileWriter.append("\"\"");
                        }
                    }else{
                        if(rs.getObject(map.get("name").toString()) != null) {
                            String value = String.valueOf(rs.getString(map.get("name").toString()));
                            boolean isNumber = Character.isDigit(value.charAt(0));
                            if(isNumber){
                                if(value.indexOf(",") != -1){
                                    fileWriter.append("\"\"" + StringEscapeUtils.escapeCsv(value) + "\"\"");
                                }else{
                                    fileWriter.append("=\"" + StringEscapeUtils.escapeCsv(value) + "\"");
                                }

                            }else{
                                if(value.indexOf(",") != -1){
                                    fileWriter.append("\"" + StringEscapeUtils.escapeCsv(value) + "\"");
                                }else{
                                    fileWriter.append(StringEscapeUtils.escapeCsv(value));
                                }
                            }

                        }else{
                            fileWriter.append(StringEscapeUtils.escapeCsv(""));
                        }
                    }
                    fileWriter.append(COMMA_DELIMITER);
                }
                fileWriter.append(NEW_LINE_SEPARATOR);
            }

        } catch (Exception e) {
            File file = new File(fileName);
            if(file.exists()){
                Files.delete(file.toPath());
            }
           LOGGER.log(Level.SEVERE,"GENERATE FILE", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            fileWriter.flush();
            fileWriter.close();

        }

        return index;
    }

    private final static Logger LOGGER = Logger.getLogger(CsvFileWriter.class.getName());

}
