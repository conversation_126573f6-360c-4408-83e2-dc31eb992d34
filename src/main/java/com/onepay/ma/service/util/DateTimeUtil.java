package com.onepay.ma.service.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created by anhkh on 03-Oct-16.
 */
public class DateTimeUtil {

    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    public enum DateTemplate {
        DD_MM_YYYY("dd/MM/yyyy"),
        DD_MM_YYYY_KK_MM_a("dd/MM/yyyy KK:mm a"),
        DD_MM_YYYY_HH_mm_ss("dd/MM/yyyy HH:mm:ss"),
        YYYYMMDDTHHmmssZ("yyyyMMdd'T'HHmmss'Z'"),
        DD_MM_HH_mm_ss("dd/MM HH:mm:ss"),
        YYYY_MM_DDTHHmmssZ("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        YYYY_MM_DD_HHmmss("yyyy-MM-dd HH:mm:ss");
        String content;

        DateTemplate(String content) {
            this.content = content;
        }

        @Override
        public String toString() {
            return this.content;
        }
    }

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static Date addMinutesToDate(int minutes, Date beforeTime){
        final long ONE_MINUTE_IN_MILLIS = 60000;//millisecs

        long curTimeInMs = beforeTime.getTime();
        Date afterAddingMins = new Date(curTimeInMs + (minutes * ONE_MINUTE_IN_MILLIS));
        return afterAddingMins;
    }

    public static Date subtractMinutesToDate(int minutes, Date beforeTime){
        final long ONE_MINUTE_IN_MILLIS = 60000;//millisecs

        long curTimeInMs = beforeTime.getTime();
        Date afterAddingMins = new Date(curTimeInMs - (minutes * ONE_MINUTE_IN_MILLIS));
        return afterAddingMins;
    }

    public static Date convertStringtoDate(String string, DateTemplate template) {
        SimpleDateFormat formatter = new SimpleDateFormat(template.toString());
        Date date = null;
        try {

            date = formatter.parse(string);

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return date;
    }

    public static Timestamp convertStringtoTimestamp(String string, DateTemplate template) {
        SimpleDateFormat formatter = new SimpleDateFormat(template.toString());
        Timestamp timestamp = null;
        try {

            Date date = formatter.parse(string);
            timestamp = new Timestamp(date.getTime());

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return timestamp;
    }

    public static String convertDatetoString(Date date, DateTemplate template) {
        DateFormat df = new SimpleDateFormat(template.toString());
        String result = df.format(date);

        return result;
    }
    public static String convertTimestamptoString(Timestamp timestamp, DateTemplate template) {
        DateFormat df = new SimpleDateFormat(template.toString());
        String result = df.format(timestamp);

        return result;
    }

    public static String convertToStringCsv(Date date, DateTemplate template) {
        String result = "";

        if (date==null) {
            return result;
        }

        Format formatter = new SimpleDateFormat(template.content);
        result = formatter.format(date);

        return "=\"" +result+ "\"";
    }

    public static long compareTwoTimeStamps(Timestamp currentTime, Timestamp oldTime)
    {
        long milliseconds1 = oldTime.getTime();
        long milliseconds2 = currentTime.getTime();

        long diff = milliseconds2 - milliseconds1;
        long diffSeconds = diff / 1000;
        long diffMinutes = diff / (60 * 1000);
        long diffHours = diff / (60 * 60 * 1000);
        long diffDays = diff / (24 * 60 * 60 * 1000);

        return diffMinutes;
    }

    public static Timestamp convertToGTM0(Timestamp source) {

        long duration = (7 * 60 * 60)* 1000;
        source.setTime(source.getTime() - duration);

        return source;
    }

    public static Timestamp convertToGTM7(Timestamp source) {

        long duration = (7 * 60 * 60)* 1000;
        source.setTime(source.getTime() + duration);

        return source;
    }

    public static Date convertToGTM7(Date source) {

        long duration = (7 * 60 * 60)* 1000;
        source.setTime(source.getTime() + duration);

        return source;
    }

    public synchronized static String iso8601(Date d) {
        if (d == null) return null;
        return yyyyMMddTHHmmssZ.format(d);
    }
}
