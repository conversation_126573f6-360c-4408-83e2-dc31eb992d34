package com.onepay.ma.service.util;

public class Converter {
    public static String convertBankNDMA(Integer bankId) {
        if (bankId ==21)  return "CUP";
        else if (bankId ==1)  return "Vietcombank";
        else if (bankId ==47)  return "Vietcombank";
        else if (bankId ==32)  return "Vietcombank";
        else if (bankId ==4)  return "VietinBank";
        else if (bankId ==50)  return "VietinBank";
        else if (bankId ==19)  return "BIDV";
        else if (bankId ==59)  return "BIDV";
        else if (bankId ==6)  return "Vikki Bank";
        else if (bankId ==57)  return "Vikki Bank";
        else if (bankId ==2)  return  "Techcombank";
        else if (bankId ==67)  return "Techcombank";
        else if (bankId ==7)  return "HDBank";
        else if (bankId ==51)  return "HDBank";
        else if (bankId ==3)  return "TienPhongBank";
        else if (bankId ==61)  return "TienPhongBank";
        else if (bankId ==8)  return "MBBank";
        else if (bankId ==49)  return "MBBank";
        else if (bankId ==5)  return "VIB";
        else if (bankId ==48)  return "VIB";
        else if (bankId ==9)  return "VietABank";
        else if (bankId ==54)  return "VietABank";
        else if (bankId ==10)  return "MSB";
        else if (bankId ==53)  return "MSB";
        else if (bankId ==11)  return "EximBank";
        else if (bankId ==68)  return "EximBank";
        else if (bankId ==12)  return "SHB";
        else if (bankId ==60)  return "SHB";
        else if (bankId ==14)  return "VPB";
        else if (bankId ==70)  return "VPB";
        else if (bankId ==15)  return "ABBank";
        else if (bankId ==58)  return "ABBank";
        else if (bankId ==16)  return "SacomBank";
        else if (bankId ==69)  return "SacomBank";
        else if (bankId ==17)  return "NamABank";
        else if (bankId ==65)  return "NamABank";
        else if (bankId ==18)  return "MBV";
        else if (bankId ==55)  return "MBV";
        else if (bankId ==20)  return "SeaBank";
        else if (bankId ==64)  return "SeaBank";
        else if (bankId ==22)  return "BAC A BANK";
        else if (bankId ==26)  return "BAC A BANK";
        else if (bankId ==56)  return "BAC A BANK";
        else if (bankId ==23)  return "NCB";
        else if (bankId ==52)  return "NCB";
        else if (bankId ==24)  return "Agribank";
        else if (bankId ==62)  return "Agribank";
        else if (bankId ==25)  return "SCB";
        else if (bankId ==63)  return "SCB";
        else if (bankId ==27)  return "PVCOMBANK";
        else if (bankId ==66)  return "PVCOMBANK";
        else if (bankId ==29)  return "QRPayment";
        else if (bankId ==30)  return "BVBank";
        else if (bankId ==71)  return "BVBank";
        else if (bankId ==31)  return "VIETTELPAY";
        else if (bankId ==33)  return "ACB";
        else if (bankId ==34)  return "GPBank";
        else if (bankId ==35)  return "OCB";
        else if (bankId ==36)  return "LPBank";
        else if (bankId ==37)  return "BAOVIET Bank";
        else if (bankId ==38)  return "Kienlongbank";
        else if (bankId ==39)  return "VRB";
        else if (bankId ==40)  return "Public Bank";
        else if (bankId ==41)  return "SAIGONBANK";
        else if (bankId ==42)  return "PGBank";
        else if (bankId ==43)  return "Indovina Bank (IVB)";
        else if (bankId ==44)  return "Wooribank";
        else if (bankId ==45)  return "UOB";
        else if (bankId ==46)  return "Shinhan Bank";
        else if (bankId ==72)  return "Vietbank";
        else if (bankId ==73)  return "VietCredit";
        else if (bankId ==74)  return "MAFC";
        else if (bankId ==75)  return "Keb Hana";
        else if (bankId ==80)  return "VNPTMONEY";
        // else if (bankId ==0)  return "No-Select";
        return null;
    }
}
