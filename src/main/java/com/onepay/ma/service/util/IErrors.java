package com.onepay.ma.service.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by locdt on 6/28/16
 */
public interface IErrors {
    Map<String, ErrorException> ErrorExceptions = new HashMap<>();
    ErrorException INTERNAL_SERVER_ERROR = new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
    ErrorException VALIDATION_ERROR = new ErrorException(400, "VALIDATION_ERROR", "Invalid request", "", "There was a validation issue with your request");
    ErrorException VALIDATION_FULLNAME_ERROR = new ErrorException(400, "VALIDATION_FULLNAME_ERROR", "Invalid fullname request", "", "Invalid fullname request");
    ErrorException VALIDATION_EMAIL_ERROR = new ErrorException(400, "VALIDATION_EMAIL_ERROR", "Invalid email request", "", "Invalid email request");
    ErrorException VALIDATION_MOBILE_ERROR = new ErrorException(400, "VALIDATION_MOBILE_ERROR", "Invalid mobile request", "", "Invalid mobile request");
    ErrorException VALIDATION_JOB_TITLE_ERROR = new ErrorException(400, "VALIDATION_JOB_TITLE_ERROR", "Invalid job title request", "", "Invalid job title request");
    ErrorException VALIDATION_PASSWORD_ERROR = new ErrorException(400, "VALIDATION_PASSWORD_ERROR", "Invalid password request", "", "Invalid password request");

    ErrorException MERCHANT_DATA_ERROR = new ErrorException(400, "MERCHANT_DATA_ERROR", "Invalid Merchant Data", "", "There was a validation issue with your merchant data");

    ErrorException AMOUNT_REFUND_ERROR = new ErrorException(400, "AMOUNT_REFUND_ERROR", "Amount Refund Error", "", "Amount Refund Error");

    ErrorException REACH_MAX_EXCEL_ROW = new ErrorException(500, "REACH_MAX_EXCEL_ROW", "The maximum rows allowed in a worksheet may have been exceeded ", "", "The maximum rows allowed in a worksheet may have been exceeded.");

    ErrorException QUERY_ERROR = new ErrorException(500, "QUERY_ERROR", "Query Failed", "", "Query Transaction Failed");
    ErrorException UPDATE_ERROR = new ErrorException(500, "UPDATE_ERROR", "UPDATE Failed", "", "UPDATE Refund Failed");

    ErrorException REFUND_FAILED = new ErrorException(500, "REFUND_FAILED", "Refund Failed", "", "Refund Transaction Failed");

    ErrorException REFUND_IN_PROCESS = new ErrorException(500, "REFUND_IN_PROCESS", "Refund in progress! Please try again after 5 minutes", "", "Refund in progress! Please try again after 5 minutes");

    ErrorException REFUND_REJECTED = new ErrorException(500, "REFUND_REJECTED", "Refund Rejected", "", "Refund Transaction Rejected");

    ErrorException RECHARGE_FAILED = new ErrorException(500, "RECHARGE_FAILED", "Recharge Failed", "", "Recharge Transaction Failed");

    ErrorException DUPLICATE_REFUND_ERROR = new ErrorException(500, "DUPLICATE_REFUND_ERROR", "This approval has already approved|rejected", "", "This approval has already approved|rejected");

    ErrorException DUPLICATE_NOTIFY = new ErrorException(500, "DUPLICATE_NOTIFY", "This paypal notify has been added", "", "This paypal notify has been added");

    ErrorException DUPLICATE_OPERATOR = new ErrorException(500, "DUPLICATE_OPERATOR", "This user has been existed", "", "This user has been existed");

    ErrorException VOID_REFUND_FAILED = new ErrorException(500, "VOID_REFUND_FAILED", "Void Refund Failed", "", "Void Refund Transaction Failed");

    ErrorException VOID_PURCHASE_FAILED = new ErrorException(500, "VOID_PURCHASE_FAILED", "Void Purchase Failed", "", "Void Purchase Transaction Failed");

    ErrorException VOID_AUTHORIZE_FAILED = new ErrorException(500, "VOID_AUTHORIZE_FAILED", "Void Authorize Failed", "", "Void Authorize Transaction Failed");

    ErrorException VOID_CAPTURE_FAILED = new ErrorException(500, "VOID_CAPTURE_FAILED", "Void Capture Failed", "", "Void Capture Transaction Failed");

    ErrorException PASS_EXCEL_LIMIT = new ErrorException(404, "PASS_EXCEL_LIMIT", "The total record has pass excel limit", "", "The total record has pass excel limit");

    ErrorException RESOURCE_NOT_FOUND = new ErrorException(404, "RESOURCE_NOT_FOUND", "Resource Not Found", "", "Resource Not Found");
    ErrorException NO_DATA_FOUND = new ErrorException(400, "NO_DATA_FOUND", "There is no data match your request", "", "No data found");
    ErrorException MOBILE_NUMBER_NOT_FOUND = new ErrorException(404, "MOBILE_NUMBER_NOT_FOUND", "Mobile Number Not Found", "", "Mobile Number Not Found");
    ErrorException ORDER_HAS_ACCEPTED = new ErrorException(500, "ORDER_HAS_ACCEPTED", "Order has been rejected or accepted", "", "Order has been rejected or accepted");

    ErrorException BIN_NOT_FOUND = new ErrorException(404, "BIN_NOT_FOUND", "Bin Number Not Found", "", "Bin Number Not Found");

    ErrorException OUT_OF_DATE_TRANSACTION_ERROR = new ErrorException(500, "OUT_OF_DATE_TRANSACTION", "Transaction is out of date", "", "Transaction is out of date");

    ErrorException SEARCH_TOO_LARGE_ERROR = new ErrorException(500, "SEARCH_TOO_LARGE_ERROR", "This search you have requested is too large.  Please select information or narrow date range  and retry.", "", "This search you have requested is too large.  Please select information or narrow date range and retry.");
    ErrorException SEARCH_TOO_OLD_ERROR = new ErrorException(500, "SEARCH_TOO_OLD_ERROR", "This search you have requested is too old.  Please select a date within the last 2 years.", "", "This search you have requested is too old.  Please select a date within the last 2 years.");

    ErrorException UNAUTHORIZED = new ErrorException(401, "UNAUTHORIZED", "Unauthorized", "", "Unauthorized");
    ErrorException FORBIDDEN = new ErrorException(403, "FORBIDDEN", "Forbidden", "", "Forbidden");
    ErrorException EXPIRED_AUTHORIZATION = new ErrorException(401, "EXPIRED_AUTHORIZATION", "Authorization is expired", "", "Authorization is expired");
    ErrorException INVALID_AUTHORIZATION_ALGORITHM = new ErrorException(401, "INVALID_AUTHORIZATION_ALGORITHM", "Invalid authorization algorithm", "", "Invalid authorization algorithm");
    ErrorException INVALID_AUTHORIZATION_REGION = new ErrorException(401, "INVALID_AUTHORIZATION_REGION", "Invalid authorization region", "", "Invalid authorization region");
    ErrorException INVALID_AUTHORIZATION_SERVICE = new ErrorException(401, "INVALID_AUTHORIZATION_SERVICE", "Invalid authorization service", "", "Invalid authorization service");
    ErrorException INVALID_AUTHORIZATION_OWS_REQUEST = new ErrorException(401, "INVALID_AUTHORIZATION_OWS_REQUEST", "Invalid authorization ows request", "", "Invalid authorization ows request");
    ErrorException INVALID_AUTHORIZATION_ACCESS_KEY_ID = new ErrorException(401, "INVALID_AUTHORIZATION_ACCESS_KEY_ID", "Invalid authorization access key id", "", "Invalid authorization access key id");
    ErrorException INVALID_AUTHORIZATION_SIGNATURE = new ErrorException(401, "INVALID_AUTHORIZATION_SIGNATURE", "Invalid authorization signature", "", "Invalid authorization signature");

    ErrorException INVALID_JSON_FORMAT = new ErrorException(400, "INVALID_JSON_FORMAT", "Invalid json format", "", "Invalid json format");
    ErrorException INVALID_MERCHANT = new ErrorException(400, "INVALID_MERCHANT", "Invalid merchant", "", "Invalid merchant");
    ErrorException INVALID_IP = new ErrorException(400, "INVALID_IP", "Invalid IP", "", "Invalid IP");
    ErrorException INVALID_AMOUNT = new ErrorException(400, "INVALID_AMOUNT", "Invalid amount", "", "Invalid amount");
    ErrorException INVALID_CURRENCY = new ErrorException(400, "INVALID_CURRENCY", "Invalid currency", "", "Invalid currency");
    ErrorException INVALID_ORDER_INFORMATION = new ErrorException(400, "INVALID_ORDER_INFORMATION", "Invalid order information", "", "Invalid order information");
    ErrorException INVALID_DELIVERY_LATITUDE = new ErrorException(400, "INVALID_DELIVERY_LATITUDE", "Invalid delivery latitude", "", "Invalid delivery latitude");
    ErrorException INVALID_DELIVERY_LONGITUDE = new ErrorException(400, "INVALID_DELIVERY_LONGITUDE", "Invalid delivery longitude", "", "Invalid delivery longitude");
    ErrorException INVALID_DELIVERY_INFORMATION = new ErrorException(400, "INVALID_DELIVERY_INFORMATION", "Invalid delivery information", "", "Invalid delivery information");
    ErrorException FINANCIAL_SERVER_ERROR = new ErrorException(500, "FINANCIAL_SERVER_ERROR", "An financial service error has occurred", "", "Resend the request at another time.");
    ErrorException INTERNATIONAL_AUTHPAYMENT_SERVER_ERROR = new ErrorException(500, "INTERNATIONAL_AUTHPAYMENT_SERVER_ERROR", "An International AuthPayment service error has occurred", "", "Resend the request at another time.");
    ErrorException DOMESTIC_SERVER_ERROR = new ErrorException(500, "DOMESTIC_SERVER_ERROR", "An domestic service error has occurred", "", "Resend the request at another time.");
    ErrorException INVALID_MOBILE_NUMBER = new ErrorException(400, "INVALID_MOBILE_NUMBER", "Invalid mobile number", "", "Invalid mobile number");
    ErrorException INVALID_EMAIL_ADDRESS = new ErrorException(400, "INVALID_EMAIL_ADDRESS", "Invalid email address", "", "Invalid email address");
    ErrorException INVALID_USER_PARTNER = new ErrorException(500, "INVALID_USER_PARTNER", "Invalid User", "", "Cannot find partner with this user");
    ErrorException INVALID_USER_PASSSAME = new ErrorException(400, "VALIDATION_ERROR", "Your new password must be different.", "", "Your new password must be different");
    ErrorException PASS_NOT_FOUND = new ErrorException(404, "PASS_NOT_FOUND", "The old password you entered was incorrect.", "", "The old password you entered was incorrect.");
    ErrorException INVALID_USER_EMAIL_EXISTED = new ErrorException(409, "INVALID_USER_EMAIL_EXISTED", "This email address already exists", "", "This email address already exists");
    ErrorException INVALID_USER_PHONE_EXISTED = new ErrorException(409, "INVALID_USER_PHONE_EXISTED", "This mobile number already exists", "", "This mobile number already exists");
    ErrorException INVALID_SENT_EMAIL = new ErrorException(500, "INVALID_SENT_EMAIL", "Error when sent mail", "", "Error when sent mail");
    ErrorException STATISTICS_SERVER_ERROR = new ErrorException(500, "STATISTICS_SERVER_ERROR", "An statistics service error has occurred", "", "Resend the request at another time.");
    ErrorException INVALID_FILE = new ErrorException(500, "INVALID_FILE_UPLOAD", "File upload invalid", "", "Funds transfer upload invalid");
    ErrorException INVALID_USER_NAME_EXISTED = new ErrorException(409, "INVALID_USER_NAME_EXISTED", "This user name already exists", "", "This user name already exists");
    ErrorException USER_NAME_OR_PASS_NOT_EXISTED = new ErrorException(409, "USER_NAME_OR_PASS_NOT_EXISTED", "User name or pass not exists in system", "", "User name or pass not exists in system");
    ErrorException OPERATOR_NOT_ACTIVE = new ErrorException(409, "OPERATOR_NOT_ACTIVE", "User name not active in system", "", "User name not active in system");
    ErrorException CAPTURE_MSP_NO_RESPONSE = new ErrorException(400, "MSP_NOT_RESPONSE_ERROR", "MSP not response", "", "MSP not response");
    ErrorException CAPTURE_MSP_ERROR = new ErrorException(500, "CAPTURE_MSP_ERROR", "An error has occurred when call MSP", "", "An error has occurred when call MSP");
    ErrorException VOID_MSP_ERROR = new ErrorException(500, "VOID_MSP_ERROR", "An error has occurred when call MSP", "", "An error has occurred when call MSP");
    ErrorException CONFIRM_PASSWORD_NOT_MATCH = new ErrorException(400, "CONFIRM_PASSWORD_NOT_MATCH", "Invalid password. Please ensure it meets the required strength criteria.", "", "Invalid password. Please ensure it meets the required strength criteria.");
    ErrorException PASSWORD_REUSED = new ErrorException(400, "PASSWORD_REUSED", "The new password has been used recently. Please select a different password.", "", "The new password has been used recently. Please select a different password.");
    ErrorException PASSWORD_REGEX_INVALID = new ErrorException(400, "PASSWORD_REGEX_INVALID", "Passwords do not match.", "", "Passwords do not match.");
    ErrorException REQUIRED_FIELDS_MISSING = new ErrorException(400, "REQUIRED_FIELDS_MISSING", "Please fill in all required fields.", "", "Please fill in all required fields.");
}
