package com.onepay.ma.service.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import java.util.concurrent.TimeUnit;

/**
 * Created by administrator on 12/9/15.
 */
public class CacheGuava {

    public CacheGuava() {
        cache = CacheBuilder.newBuilder()
                .maximumSize(MAX_SIZE)
                .expireAfterWrite(Integer.parseInt(PropsUtil.get("server.cacheTimeout", "15")), TimeUnit.MINUTES)
                .build();
    }

    public void put(String key, Object data){
        cache.put(key, data);
    }

    public Cache<String, Object> get(){
        return cache;
    }


    private static final long MAX_SIZE = 200;
    private Cache<String, Object> cache;

}
