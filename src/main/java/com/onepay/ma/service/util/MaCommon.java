package com.onepay.ma.service.util;

import io.vertx.core.buffer.Buffer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * Created by administrator on 3/23/15.
 */
public class MaCommon {
    public MaCommon() {
    }

    public static byte[] serialize(Object obj) {
        byte[] result = null;
        ByteArrayOutputStream bout = null;
        ObjectOutputStream objOut = null;

        try {
            bout = new ByteArrayOutputStream();
            objOut = new ObjectOutputStream(bout);
            objOut.writeObject(obj);
            objOut.flush();
            objOut.close();
            bout.close();
            result = bout.toByteArray();
        } catch (Exception var17) {
            System.out.println("serialize error:");
            var17.printStackTrace();
        } finally {
            try {
                if(objOut != null) {
                    objOut.close();
                }
            } catch (Exception var16) {
                var16.printStackTrace();
            }

            try {
                if(bout != null) {
                    bout.close();
                }
            } catch (Exception var15) {
                var15.printStackTrace();
            }

        }

        return result;
    }

    public static Buffer serializeBuffer(Object obj) {
        Buffer result = null;
        ByteArrayOutputStream bout = null;
        ObjectOutputStream objOut = null;

        try {
            bout = new ByteArrayOutputStream();
            objOut = new ObjectOutputStream(bout);
            objOut.writeObject(obj);
            objOut.flush();
            objOut.close();
            bout.close();
            result = Buffer.buffer(bout.toByteArray());
        } catch (Exception var17) {
            System.out.println("serialize error:");
            var17.printStackTrace();
        } finally {
            try {
                if(objOut != null) {
                    objOut.close();
                }
            } catch (Exception var16) {
                var16.printStackTrace();
            }

            try {
                if(bout != null) {
                    bout.close();
                }
            } catch (Exception var15) {
                var15.printStackTrace();
            }

        }

        return result;
    }

    public static Object deserialize(byte[] buf) {
        Object result = null;
        ByteArrayInputStream bin = null;
        ObjectInputStream objIn = null;

        try {
            bin = new ByteArrayInputStream(buf);
            objIn = new ObjectInputStream(bin);
            result = objIn.readObject();
        } catch (Exception var17) {
            var17.printStackTrace();
        } finally {
            try {
                if(objIn != null) {
                    objIn.close();
                }
            } catch (Exception var16) {
                var16.printStackTrace();
            }

            try {
                if(bin != null) {
                    bin.close();
                }
            } catch (Exception var15) {
                var15.printStackTrace();
            }

        }

        return result;
    }

    public static String formatNumber(long number) {
        String result = "";
        String s;
        if(number < 0L) {
            result = "-";
            s = String.valueOf(-number);
        } else {
            s = String.valueOf(number);
        }

        int pos = 0;

        for(int i = s.length() % 3; i <= s.length(); i += 3) {
            result = result + s.substring(pos, i);
            if(i > pos && i < s.length()) {
                result = result + ",";
            }

            pos = i;
        }

        return result;
    }

    public static Date stringToDate(String sDate, String pattern) {
        try {
            SimpleDateFormat e = new SimpleDateFormat(pattern);
            return e.parse(sDate);
        } catch (Exception var3) {
            var3.printStackTrace();
            return null;
        }
    }

    public static java.sql.Date getSqlDate(Date date) {
        return date == null?null:new java.sql.Date(date.getTime());
    }

    public static Timestamp getSqlTimestamp(Date date) {
        return date == null?null:new Timestamp(date.getTime());
    }

    public static int parseInt(String value, int defaultValue) {
        try {
            return Integer.parseInt(value);
        } catch (Exception var3) {
            return defaultValue;
        }
    }

    public static long parseLong(String value, long defaultValue) {
        try {
            return Long.parseLong(value);
        } catch (Exception var4) {
            return defaultValue;
        }
    }

    private static Pattern pOldBrowser = Pattern.compile("MSIE [1-8]|Opera Mobi|Opera Mini|Windows;.+Safari");
    public static boolean isOldBrowser(String userAgent) {
        return userAgent != null && pOldBrowser.matcher(userAgent).find();

        // Get an UserAgentStringParser and analyze the requesting client
        /*UserAgentStringParser parser = UADetectorServiceFactory.getResourceModuleParser();
        ReadableUserAgent agent = parser.parse(userAgent);
        return ("IE".equalsIgnoreCase(agent.getName()) && Convert.parseInt(agent.getVersionNumber().getGroups().get(0), 9) <= 8)
                || ("Safari".equalsIgnoreCase(agent.getName()) && "Windows".equalsIgnoreCase(agent.getOperatingSystem().getName()))
                || "Opera Mobile".equalsIgnoreCase(agent.getName())
                || "Opera Mini".equalsIgnoreCase(agent.getName());*/
    }

    private static Pattern pOldMobileBrowser = Pattern.compile("Opera Mobi|Opera Mini");
    public static boolean isOldMobileBrowser(String userAgent) {
        return userAgent != null && pOldMobileBrowser.matcher(userAgent).find();
        /*UserAgentStringParser parser = UADetectorServiceFactory.getResourceModuleParser();
        ReadableUserAgent agent = parser.parse(userAgent);

        return "Opera Mobile".equalsIgnoreCase(agent.getName())
                || "Opera Mini".equalsIgnoreCase(agent.getName());*/
    }

}
