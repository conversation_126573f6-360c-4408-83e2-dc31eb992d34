package com.onepay.ma.service.util;

import com.onepay.ma.service.models.*;
import io.vertx.core.json.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */

public  class DataSourceConfigUtil {

    public  JsonObject readOnlyDataSourceConfig(){
        return new JsonObject()
                .put("provider_class", readOnlyDataSource.getProvider())
                .put("dataSourceClassName", readOnlyDataSource.getDataSourceClassName())
                .put("username", readOnlyDataSource.getUsername())
                .put("password", readOnlyDataSource.getPassword())
                .put("poolName", readOnlyDataSource.getPoolName())
                .put("maximumPoolSize", readOnlyDataSource.getMaxPoolSize())
                .put("idleTimeout", readOnlyDataSource.getIdleTimeout())
                .put("connectionTimeout", readOnlyDataSource.getConnectionTimeout())
                .put("minimumIdle", readOnlyDataSource.getMinimumIdle())
                .put("validationTimeout", 1000)
                .put("datasource", new JsonObject().put("URL", readOnlyDataSource.getUrl()))
                .put("initializationFailFast",  false);
    }


    public  JsonObject backUpDataSourceConfig(){
        JsonObject jsonObject =  new JsonObject()
                .put("provider_class", backUpDataSource.getProvider())
                .put("dataSourceClassName", backUpDataSource.getDataSourceClassName())
                .put("username", backUpDataSource.getUsername())
                .put("password", backUpDataSource.getPassword())
                .put("poolName", backUpDataSource.getPoolName())
                .put("maximumPoolSize", backUpDataSource.getMaxPoolSize())
                .put("idleTimeout", backUpDataSource.getIdleTimeout())
                .put("connectionTimeout", backUpDataSource.getConnectionTimeout())
                .put("minimumIdle", backUpDataSource.getMinimumIdle())
                .put("validationTimeout", 1000)
                .put("datasource", new JsonObject().put("URL", backUpDataSource.getUrl()))
                .put("initializationFailFast",  false);
        return jsonObject;
    }

    public  JsonObject onlineDataSourceConfig(){
        JsonObject jsonObject =  new JsonObject()
                .put("provider_class", onlineDataSource.getProvider())
                .put("dataSourceClassName", onlineDataSource.getDataSourceClassName())
                .put("username", onlineDataSource.getUsername())
                .put("password", onlineDataSource.getPassword())
                .put("poolName", onlineDataSource.getPoolName())
                .put("maximumPoolSize", onlineDataSource.getMaxPoolSize())
                .put("idleTimeout", onlineDataSource.getIdleTimeout())
                .put("connectionTimeout", onlineDataSource.getConnectionTimeout())
                .put("minimumIdle", onlineDataSource.getMinimumIdle())
                .put("validationTimeout", 1000)
                .put("datasource", new JsonObject().put("URL", onlineDataSource.getUrl()))
                .put("initializationFailFast",  false);
        return jsonObject;
    }

    public  JsonObject promotionDataSourceConfig(){
        JsonObject jsonObject =  new JsonObject()
                .put("provider_class", promotionDataSource.getProvider())
                .put("dataSourceClassName", promotionDataSource.getDataSourceClassName())
                .put("username", promotionDataSource.getUsername())
                .put("password", promotionDataSource.getPassword())
                .put("poolName", promotionDataSource.getPoolName())
                .put("maximumPoolSize", promotionDataSource.getMaxPoolSize())
                .put("idleTimeout", promotionDataSource.getIdleTimeout())
                .put("connectionTimeout", promotionDataSource.getConnectionTimeout())
                .put("minimumIdle", promotionDataSource.getMinimumIdle())
                .put("validationTimeout", 1000)
                .put("datasource", new JsonObject().put("URL", promotionDataSource.getUrl()))
                .put("initializationFailFast",  false);
        return jsonObject;
    }

    public  JsonObject paycollectDataSourceConfig(){
        JsonObject jsonObject =  new JsonObject()
                .put("provider_class", promotionDataSource.getProvider())
                .put("dataSourceClassName", promotionDataSource.getDataSourceClassName())
                .put("username", promotionDataSource.getUsername())
                .put("password", promotionDataSource.getPassword())
                .put("poolName", promotionDataSource.getPoolName())
                .put("maximumPoolSize", promotionDataSource.getMaxPoolSize())
                .put("idleTimeout", promotionDataSource.getIdleTimeout())
                .put("connectionTimeout", promotionDataSource.getConnectionTimeout())
                .put("minimumIdle", promotionDataSource.getMinimumIdle())
                .put("validationTimeout", 1000)
                .put("datasource", new JsonObject().put("URL", promotionDataSource.getUrl()))
                .put("initializationFailFast",  false);
        return jsonObject;
    }

//    public  JsonObject cdrDataSourceConfig(){
//        JsonObject jsonObject =  new JsonObject()
//                .put("provider_class", cdrDataSource.getProvider())
//                .put("dataSourceClassName", cdrDataSource.getDataSourceClassName())
//                .put("username", cdrDataSource.getUsername())
//                .put("password", cdrDataSource.getPassword())
//                .put("poolName", cdrDataSource.getPoolName())
//                .put("maximumPoolSize", cdrDataSource.getMaxPoolSize())
//                .put("idleTimeout", cdrDataSource.getIdleTimeout())
//                .put("connectionTimeout", cdrDataSource.getConnectionTimeout())
//                .put("minimumIdle", cdrDataSource.getMinimumIdle())
//                .put("validationTimeout", 1000)
//                .put("datasource", new JsonObject().put("URL", cdrDataSource.getUrl()))
//                .put("initializationFailFast",  false);
//        return jsonObject;
//    }

    @Autowired
    @Qualifier(value = "readOnlyDataSourceConfig")
    private ReadOnlyDataSource readOnlyDataSource;

    @Autowired
    @Qualifier(value = "backUpDataSourceConfig")
    private BackUpDataSource backUpDataSource;

    @Autowired
    @Qualifier(value = "onlineDataSourceConfig")
    private OnlineDataSource onlineDataSource;

    @Autowired
    @Qualifier(value = "promotionDataSourceConfig")
    private PromotionDataSource promotionDataSource;

//    @Autowired
//    @Qualifier(value = "cdrDataSourceConfig")
//    private CdrDataSource cdrDataSource;
}
