package com.onepay.ma.service.util;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public final class ParamsPool {

    public static final String HANDLER_DATA_RESULT = "result_data";
    public static final String HANDLER_LOG_RESULT = "result_log";

    public static final String  HANDLER_DATA_CODE = "result_code";

    public static final String HANDLER_DATA_TYPE = "result_type";

    public static final String STATUS = "status";

    public static final String PAGE = "page";

    public static final String PAGE_SIZE = "page_size";
    public static final String PAGESIZE = "pageSize";

    public static final String EMAIL = "email";

    public static final String PHONE = "phone";

    public static final String CUSTOMER = "customer";

    public static final String CUSTOMER_EMAIL = "customer_email";

    public static final String CUSTOMER_MOBILE = "customer_mobile";

    public static final String CUSTOMEREMAIL = "customerEmail";

    public static final String CUSTOMERMOBILE = "customerMobile";

    public static final String MERCHANTWEBSITE = "merchantWebsite";

    public static final String MERCHANT_WEBSITE = "merchant_website";

    public static final String COUPON_CODE = "coupon_code";

    public static final String KEY_WORDS = "keywords";

    public static final String TYPE = "type";

    public static final String ID = "id";

    public static final String IS_SHOW_3D = "is_show_3d";

    public static final String REFERENCE = "reference";

    public static final String PROVIDER_ID = "provider_id";

    public static final String MERCHANT_ID = "merchant_id";
    public static final String MERCHANT_QT = "merchant_qt";
    public static final String MERCHANT_ND = "merchant_nd";
    public static final String MERCHANTID = "merchantId";

    public static final String TERMINAL_ID = "terminal_id";
    public static final String SHOP_ID = "shop_id";
    public static final String STORE_ID = "store_id";

    public static final String CREATE_TIME = "create_time";
    public static final String SUMMARY = "summary";
    public static final String CHANNEL_ID = "channel_id";

    public static final String MERCHANT_NAME = "merchant_name";
    public static final String MERCHANTNAME = "merchantName";

    public static final String DESCRIPTION = "description";

    public static final String USER_GROUP_ID = "user_group_id";

    public static final String USER_GROUP_NAME = "user_group_name";
    public static final String INSTRUMENT_TYPE = "instrument_type";
    public static final String INSTRUMENT_BRAND_ID = "instrument_brand_id";
    public static final String INPUT_TYPE = "input_type";

    public static final String ORDER_INFO = "order_info";
    public static final String ORDERINFO = "orderInfo";

    public static final String MOBILE = "mobile";

    public static final String SMS_STATE = "sms_state";

    public static final String COUPON_STATE = "coupon_state";

    public static final String IP_ADDRESS = "ip_address";

    public static final String TRANSACTION_ID = "transaction_id";
    public static final String TRANSACTIONID = "transactionId";

    public static final String CLIENT_REF = "client_ref";

    public static final String ACQ_CODE = "acq_code";
    public static final String ACQCODE = "acqCode";

    public static final String CUSTOMER_TRANSACTION_ID = "customer_transaction_id";

    public static final String CUSTOMERTRANSACTIONID = "customerTransactionId";

    public static final String MASKING = "masking";
    public static final String QRID = "qrId";
    public static final String INVOICE_ID = "invoiceId";

    public static final String ORDER_ID = "order_id";

    public static final String TRANSACTION_TYPE = "transaction_type";

    public static final String TRANSACTION_STATE = "transaction_state";

    public static final String SOURCE_CDR = "source";

    public static final String SERVICE_CDR = "service";

    public static final String CARD_TYPE = "card_type";

    public static final String INSTRUMENT = "instrument";

    public static final String INSTRUMENT_NAME = "instrument_name";

    public static final String INSTRUMENT_NUMBER = "instrument_number";
    public static final String INSTRUMENTNUMBER = "instrumentNumber";

    public static final String AUTHENTICATION_STATE = "authentication_state";

    public static final String AUTHENTICATION_CODE = "authentication_code";

    public static final String AUTHENTICATION_TYPE = "authentication_type";

    public static final String AUTHORISATION_CODE = "authorisation_code";

    public static final String TRANSACTION_STATUS = "transaction_status";

    public static final String ACQUIRER_ID = "acquirer_id";

    public static final String BANKID = "bankId";
    public static final String FROMDATE = "fromDate";
    public static final String TODATE = "toDate";
    public static final String APPNAME = "appName";
    public static final String CARDTYPE = "cardType";
    public static final String CLIENT_ID = "clientId";

    public static final String MERCHANT_TRANSACTION_REF = "merchant_transaction_ref";
    public static final String MERCHANTTRANSACTIONREF = "merchantTransactionRef";
    public static final String BANK_TRANSACTION_ID = "bank_transaction_id";
    public static final String BANKTRANSACTIONID = "bankTransactionId";
    public static final String BANK_ID = "bank_id";

    public static final String MERCHANT_ORDER_REF = "merchant_order_ref";

    public static final String PROMOTION_SID = "promotion_id";

    public static final String PROMOTION_CODE = "promotion_code";

    public static final String USER_ID = "user_id";
    public static final String ACCOUNT = "account";
    public static final String BANK = "bank";
    public static final String LANGUAGE = "language";
    public static final String LANG = "lang";
    public static final String MA_MAIN = "maMain";
    public static final String USER_NAME = "user_name";

    public static final String DATE_NAME = "date_name";

    public static final String FRAUD_ID = "fraud_id";

    public static final String NUMBER = "number";

    public static final String CARD_NUMBER = "card_number";

    public static final String ORDER_STATUS = "order_status";

    public static final String INTERVAL = "interval";

    public static final String REPORT_TYPE = "report_type";
    public static final String DOWNLOADTYPE = "downloadType";
    public static final String CSV = "csv";
    public static final String EXCEL = "excel";
    public static final String AMOUNT = "amount";

    public static final String CURRENCY = "currency";

    public static final String TOTAL_TRANSACTION_COUNT = "total_transaction_count";
    public static final String TOTALTRANSACTIONCOUNT = "totalTransactionCount";

    public static final String TOTAL_REFUND_COUNT = "total_refund_count";
    public static final String TOTALREFUNDCOUNT = "totalRefundCount";

    public static final String TOTAL_TRANSACTION_TOTAL = "total_transaction_total";
    public static final String TOTALTRANSACTIONTOTAL = "totalTransactionTotal";
    public static final String TRANS_PAYMENT_AMOUNT_TOTAL = "totalTransPaymentAmountTotal";

    public static final String TOTAL_REFUND_TOTAL = "total_refund_total";
    public static final String TOTALREFUNDTOTAL = "totalRefundTotal";
    public static final String REFUND_PAYMENT_AMOUNT_TOTAL = "totalRefundPaymentAmountTotal";
    
    public static final String CURRENCY_CODE = "currency";

    public static final String INFO = "info";

    public static final String STATE = "state";

    public static final String SERVICE = "service";

    public static final String SOURCE_LEFT = "source_left";

    public static final String SOURCE_RIGHT = "source_right";

    public static final String BASE_DATE = "base_date";

    public static final String FROM_DATE = "from_date";

    public static final String TO_DATE = "to_date";

    public static final String CODE = "code";

    public static final String X_USER_ID = "X-USER-ID";
    public static final String X_PARTNER_ID = "X-PARTNER-ID";

    public static final String X_REAL_IP = "X-Real-IP";

    public static final String GRANT_TYPE = "grant_type";

    public static final String AUTHORIZATION_CODE = "authorization_code";

    public static final String JSON_HEADER = "application/json";

    public static final String READ_ONLY_DATASOURCE_NAME = "READ_ONLY_DATASOURCE";

    public static final String BACK_UP_DATASOURCE_NAME = "BACK_UP_DATASOURCE";

    public static final String PROMOTION_DATASOURCE_NAME = "PROMOTION_DATASOURCE";

    public static final String CDR_DATASOURCE_NAME = "CDR_DATASOURCE";

    public static final String PAYCOLLECT_DATASOURCE_NAME = "PAYCOLLECT_DATASOURCE";

    public static final String MERCHANT_PORTAL_DATASOURCE_NAME = "MERCHANT_PORTAL_DATASOURCE";

    public static final String CDR_CLIENT = "CDR_CLIENT";

    public static final String ONLINE_DATASOURCE_NAME = "ONLINE_DATASOURCE";

    public static final String READ_ONLY_METADATA = "READ_ONLY_METADATA";

    public static final String BACK_UP_METADATA = "BACK_UP_METADATA";

    public static final String PROMOTION_METADATA = "PROMOTION_METADATA";

    public static final String ONLINE_METADATA = "ONLINE_METADATA";

    public static final String PAYCOLLECT_METADATA = "PAYCOLLECT_METADATA";

    public static final String ONEBILL_METADATA = "ONEBILL_METADATA";

    public static final String CONNECTION_BACKUP = "CONN_BACKUP";

    public static final String CONNECTION_READONLY = "CONN_READ_ONLY";

    public static final String CONNECTION_ONLINE = "CONN_ONLINE";

    public static final String CONNECTION_PROMOTION = "CONN_PROMOTION";

    public static final String CONNECTION_CDR = "CONN_CDR";

    public static final String ONERECON_DATASOURCE_NAME = "ONERECON_DATASOURCE";

    public static final String ONERECON_METADATA = "ONERECON_METADATA";

    public static final String PROMOTION_PARAM_ORDER = "order";

    public static final String PROMOTION_PARAM_NAME = "name";

    public static final String PROMOTION_PARAM_VALUE = "value";

    public static final String PROMOTION_PARAM_DISCOUNT_PARAM_ID = "discount_param_id";

    public static final String PROMOTION_PARAM_DISCOUNT_PARAM_NAME = "discount_param_id";

    public static final int DISCOUNT_TYPE_PERCENT_ID = 1;

    public static final int DISCOUNT_TYPE_FIX_ID = 2;

    public static final int DISCOUNT_TYPE_MAX_PER_TXN_ID = 3;

    public static final int DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID = 6;

    public static final String USER_PERMISSION = "user_permission";

    public static final String USER_SESSION = "user_session";

    public static final String INTERNAL_ERROR_MESSAGE = "There are some problems has been occured.";

    public static final String X_REQUEST_ID = "X-Request-Id";


    public static final String FILE_HASH_NAME = "file_hash_name";

    public static final String FILE_SIZE= "file_size";

    public static final String FILE_NAME = "file_name";

    public static final String FILE_EXT = "file_ext";

    public static final String PATH_FILE = "file_path";

    public static final String[] STATE_SUCCESS = {"Y", "M"};

    public static final String[] STATE_FAILED = {"N","A","P","S", "I"};

    public static final String[] STATE_UNDETERMINE = {"T","U"};

    public static final String[] STATE_NOT_ENROLLED = {"E"};

    public static final String COOKIE_NAME = "auth";

    public static final String FILE_HASH_NAME_DOWNLOAD = "fileNameHash";

    public static final String EVENT_TYPE = "event_type";

    public static final String RESOURCE_TYPE = "resource_type";

    public static final String PARENT_RESOURCE = "parent_resource";

    public static final String RESOURCE = "resource";
    public static final String INVOICE = "invoice";
    public static final String ORDER_REF = "order_ref";
    public static final String PASS = "pass";
    public static final String NEW_PASS = "new_pass";
    public static final String CONFIRM_NEW_PASS = "confirm_new_password";
    public static final String VERSION = "version";
    public static final String TARGET = "target";
    public static final String S_ID = "s_id";

    public static final String TO = "to";

    public static final String CATEGORY = "category";

    public static final String CONTENT = "content";
    public static final String TITLE = "title";

    public static final String PAYGATE = "paygate";

    public static final String ACCOUNT_ID = "account_id";
    public static final String BANK_SENDER_SWIFTCODE = "bank_sender";
    public static final String BANK_RECEIPT_SWIFTCODE = "bank_receipt";
    public static final String TIME_INTERVAL = "time_interval";

    public static final String REFUND_TYPE = "refund_type"; // DuongPXT authorize captrue: add refund type

    
    public static final String INSTALLMENT_MERC_ID = "installmentMerchantId";
    public static final String PAYNOW_MERC_ID = "paynowMerchantId";
    public static final String BNPL_MERC_ID = "bnplMerchantId";
    public static final String APPLE_MERC_ID = "applepayMerchantId";
    public static final String PAYMETHOD = "payMethod";
    public static final String BANK_MERCH_ID = "bankMerchantId";
    public static final String BANK_TERMI_ID = "bankTerminalId";

    public static final String SOURCE = "source";
    public static final String APPLEPAY_USER = "applepay_user";
    public static final String DIGITAL_WALLET_USER = "digital_wallet_user";

}
