package com.onepay.ma.service.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.onepay.ma.service.handler.common.impl.ResponseHandlerImpl;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.financial.FinancialTransaction;
import com.onepay.ma.service.models.financial.SamsungFinancialTransaction;
import com.onepay.ma.service.models.installment.InstallmentBank;
import com.onepay.ma.service.models.mpay.*;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReport;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.models.notification.MpayNotification;
import com.onepay.ma.service.models.notification.MpayNotificationGroup;
import com.onepay.ma.service.models.notification.MpayvnNotificationMsg;
import com.onepay.ma.service.models.onebill.OnebillClient;
import com.onepay.ma.service.models.onebill.OnebillTransaction;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationReportConvets;
import com.onepay.ma.service.models.report.GeneralReport;
import com.onepay.ma.service.models.shop.Store;
import com.onepay.ma.service.models.user.UserProfile;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by huynguyen on 3/14/16.
 */
public class GenericAdapter extends TypeAdapter<Object>
{
    @Override
    public void write(JsonWriter jsonWriter, Object o) throws IOException
    {
        jsonWriter.beginObject();

        if(!(o instanceof Map )) {
            for (Field field : o.getClass().getDeclaredFields()) {
                Object fieldValue = runGetter(field, o);
                if (!field.getName().equals("n_id")) {
                    jsonWriter.name(field.getName());
                    if (fieldValue == null) {
                        jsonWriter.value("");
                    } else {
                        if (fieldValue instanceof String) {
                            jsonWriter.value(fieldValue.toString());
                        } else if (fieldValue instanceof Date) {
                            String date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fieldValue);
                            jsonWriter.value(date);
                        } else if (fieldValue instanceof Timestamp) {
                            String time = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fieldValue);
                            jsonWriter.value(time);
                        } else if (fieldValue instanceof Integer) {
                            jsonWriter.value(String.valueOf(fieldValue));
                        } else if (fieldValue instanceof Boolean) {
                            jsonWriter.value(((Boolean) fieldValue).booleanValue());
                        } else if (fieldValue instanceof Long) {
                            jsonWriter.value((String.valueOf(((Long) fieldValue).longValue())));
                        } else if (fieldValue instanceof Double) {
                            jsonWriter.value(String.format("%.2f", fieldValue));
                        } else {
                            jsonWriter.jsonValue(ResponseHandlerImpl.gson.toJson(fieldValue));
                        }
                    }
                }else{
                }
            }
        }else{
            Map<String, Object> map = (Map) o;
            for(Map.Entry<String ,Object> entry: map.entrySet()){
                jsonWriter.name(entry.getKey());
                Object fieldValue = entry.getValue();
                if (!entry.getKey().equals("n_id")) {
                    if (fieldValue instanceof String) {
                        jsonWriter.value(fieldValue.toString());
                    } else if (fieldValue instanceof Date) {
                        String date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fieldValue);
                        jsonWriter.value(date);
                    } else if (fieldValue instanceof Timestamp) {
                        String time = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fieldValue);
                        jsonWriter.value(time);
                    } else if (fieldValue instanceof Integer) {
                        jsonWriter.value(String.valueOf(fieldValue));
                    } else if (fieldValue instanceof Boolean) {
                        jsonWriter.value(((Boolean) fieldValue).booleanValue());
                    } else if (fieldValue instanceof Long) {
                        jsonWriter.value(((Long) fieldValue).longValue());
                    } else if (fieldValue instanceof Double) {
                        if (!entry.getKey().equals("order")) {
                            jsonWriter.value(String.format("%.2f", fieldValue));
                        } else {
                            jsonWriter.value(String.valueOf(((Double) fieldValue).intValue()));
                        }
                    } else {
                        jsonWriter.jsonValue(ResponseHandlerImpl.gson.toJson(fieldValue));
                    }
                }

            }
        }

        jsonWriter.endObject();
    }

    @Override
    public Object read(JsonReader jsonReader) throws IOException
    {

        return null;
    }

    /**
     * A generic field accessor runner.
     * Run the right getter on the field to get its value.
     * @param field
     * @param o {@code Object}
     * @return
     */
    public static Object runGetter(Field field, Object o)
    {
        // MZ: Find the correct method
        for (Method method : o.getClass().getMethods())
        {
            if ((method.getName().startsWith("get")) || (method.getName().startsWith("is")))
            {
                if ((method.getName().startsWith("get")) || (method.getName().startsWith("is")))
                {
                    String name = method.getName().toLowerCase();
                    if(name.startsWith("get") && !name.equals("n_id")){
                        name = name.substring(name.indexOf("get") + 3);
                        if(field.getName().toLowerCase().equals(name)) {
                            try {
                                return method.invoke(o);
                            } catch (IllegalAccessException e) {
                            } catch (InvocationTargetException e) {
                            }
                        }
                    }else {
                        name = name.substring(name.indexOf("is") + 2);
                        if(name.startsWith("_") ) {
                            if ( method.getName().toLowerCase().equals(field.getName().toLowerCase())) {
                                try {
                                    return method.invoke(o);
                                } catch (IllegalAccessException e) {
                                } catch (InvocationTargetException e) {
                                }
                            }
                        }else{
                            if (name.equals(field.getName().toLowerCase())) {
                                try {
                                    return method.invoke(o);
                                } catch (IllegalAccessException e) {
                                } catch (InvocationTargetException e) {
                                }
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

}