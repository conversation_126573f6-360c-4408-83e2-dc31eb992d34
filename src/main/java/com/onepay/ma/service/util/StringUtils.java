package com.onepay.ma.service.util;

import java.text.Normalizer;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

public class StringUtils {
    public static String removeAccent(String s) {
        if(s == null) return "";
        String temp = Normalizer.normalize(s, Normalizer.Form.NFD);
        Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
        return pattern.matcher(temp).replaceAll("");
    }

    public static String mapToString(Map map) {
        StringBuilder s = new StringBuilder();
        // for (Map.Entry entry : map.entrySet())
        // {
        //     System.out.println("key: " + entry.getKey() + "; value: " + entry.getValue());
        // }
        map.forEach((key, value) -> s.append(key + "=" + value + "&"));
        return s.toString();
    }

    private static String[] sensitiveRegexps = new String[]{
            "(card_no='?|cardNumber='?|card_no_hash='?|card_no_encrypt='?|<CARD_NUMBER>|card_name='?|cardHolderName='?|<CARD_HOLDER_NAME>|mobile_number='?|<MOBILE_NUMBER>)[^',<}\"]+([^',<}\"]{4})",
            "(cardDate='?|card_date='?|<CARD_DATE>)[^',<}\"]+([',<}\"]|$)"
    };

    public static String maskSensitiveInfo(String input) {
        if (input == null || input.isEmpty()) return input;
        try {
            for (String reg : sensitiveRegexps) {
                input = input.replaceAll(reg, "$1***$2");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
        }
        return input;
    }


    private static final Logger LOGGER = Logger.getLogger(StringUtils.class.getName());
}
