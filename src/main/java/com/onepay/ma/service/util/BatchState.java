package com.onepay.ma.service.util;

public enum BatchState {
    //Batch detail state
    CREATE("created"),
    PROCESSING("processing"),
    VALIDATED("validated"),
    FAILED("failed"),
    WAIT_FOR_TRANSFER("wait for transfer"),
    COMPLETED("completed"),
    REJECTED("rejected"),

    // batch  state
    SUCCESS("success");
    public final String value;

    public String getResponse() {
        return value;
    }
    BatchState(String label) {
        this.value = label;
    }
}
