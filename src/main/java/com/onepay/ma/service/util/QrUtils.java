package com.onepay.ma.service.util;

import com.onepay.ma.service.models.mpay.MpayOrderPostModel;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by anhkh on 09-Aug-17.
 */
public class QrUtils {


    private  static final String VERSION = "01";
    private static NumberFormat amountFormatter = new DecimalFormat("0.##");

    public static String createMpayQrData(MpayOrderPostModel model) {
        Map<Integer, Object> fields = new LinkedHashMap<>();
        fields.put(0, VERSION);
        //fields.put(1, "1" + (isDynamic ? "2" : "1"));
        //fields.put(2, merchantId);
        Map<Integer, Object> f26 = new LinkedHashMap<>();
        f26.put(0, "vn.onepay");
        f26.put(1, model.getMerchantId());
        fields.put(26, f26);

        Map<Integer, Object> f50 = new LinkedHashMap<>();
        f50.put(1, model.getMerchantId());
        fields.put(50, f50);

        fields.put(52, model.getMerchantCategoryCode());
        fields.put(53, standalizeCurrencyCode(model.getCurrencyCode()));
        fields.put(54, amountFormatter.format(model.getAmount()));
        fields.put(58, standalizeCountryCode(model.getCountryCode()));
        fields.put(59, standalizeAN(model.getMerchantName()));
        fields.put(60, standalizeAN(model.getMerchantCity()));

        Map<Integer, Object> f62 = new LinkedHashMap<>();
        f62.put(1, standalizeAN(model.getBillNumber()));
        f62.put(5, model.getReferenceId());
        //f62.put(21, standalizeAN(orderInfo));

        fields.put(62, f62);

        return create(fields);
    }

    public static String create(Map<Integer, Object> fields) {
        String result = tlv(fields);
        result += tlv(63, getCRC16CCITT(result + "6304", 0x1021, 0xFFFF, false));
        return result;
    }


    private static String tlv(Map<Integer, Object> fields) {
        StringBuilder result = new StringBuilder();
        for (Map.Entry<Integer, Object> entry : fields.entrySet()) {
            int tag = entry.getKey();
            Object value = entry.getValue();
            result.append(tlv(tag, value));
        }
        return result.toString();
    }

    @SuppressWarnings("unchecked")
    private static String tlv(int tag, Object value) {
        if (value == null) return "";
        String v = "";
        if (value instanceof String) v = (String) value;
        else if (value instanceof Map) v = tlv((Map<Integer, Object>) value);

        if (v.isEmpty()) return "";
        if (v.length() > 99) throw new RuntimeException("length too large: " + v);
        return String.format("%02d", tag) + String.format("%02d", v.length()) + v;
    }

    private static String crc(String input) {

        int[] table = {
                0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
                0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
                0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
                0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
                0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
                0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
                0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
                0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
                0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
                0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
                0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
                0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
                0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
                0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
                0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
                0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
                0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
                0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
                0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
                0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
                0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
                0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
                0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
                0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
                0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
                0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
                0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
                0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
                0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
                0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
                0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
                0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040,
        };


        byte[] bytes = input.getBytes();
        int crc = 0x0000;
        for (byte b : bytes) crc = (crc >>> 8) ^ table[(crc ^ b) & 0xff];

        return Integer.toHexString(crc).toUpperCase();
    }

    /**
     * converts the given String to CRC16
     *
     * @param inputStr   - the input string to get the CRC
     * @param polynomial - the polynomial (divisor)
     * @param crc        - the CRC mask
     * @param isHex      - if true, treat input string as hex, otherwise, treat as
     *                   ASCII
     * @return
     */
    private static String getCRC16CCITT(String inputStr, int polynomial,
                                        int crc, boolean isHex) {

        int strLen = inputStr.length();
        int[] intArray;

        if (isHex) {
            if (strLen % 2 != 0) {
                inputStr = inputStr.substring(0, strLen - 1) + "0"
                        + inputStr.substring(strLen - 1, strLen);
                strLen++;
            }

            intArray = new int[strLen / 2];
            int ctr = 0;
            for (int n = 0; n < strLen; n += 2) {
                intArray[ctr] = Integer.valueOf(inputStr.substring(n, n + 2), 16);
                ctr++;
            }
        } else {
            intArray = new int[inputStr.getBytes().length];
            int ctr = 0;
            for (byte b : inputStr.getBytes()) {
                intArray[ctr] = b;
                ctr++;
            }
        }

        // main code for computing the 16-bit CRC-CCITT
        for (int b : intArray) {
            for (int i = 0; i < 8; i++) {
                boolean bit = ((b >> (7 - i) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit) crc ^= polynomial;
            }
        }

        crc &= 0xFFFF;
        StringBuilder crcStr = new StringBuilder(Integer.toHexString(crc).toUpperCase());
        int n = crcStr.length();
        for (int i = 0; i < (4 - n); i++) {
            crcStr.insert(0, "0");
        }
        return crcStr.toString();
    }

    private static String standalizeAN(String input) {
        if (input == null) return null;
        return input.toUpperCase().replaceAll("[^-+*/% A-Z0-9]", " ");
    }

    private static String standalizeCurrencyCode(String currency) {
        if (currency == null) return null;
        else if ("VND" .equalsIgnoreCase(currency)) return "704";
        else if ("USD" .equalsIgnoreCase(currency)) return "840";
        else if ("704" .equals(currency) || "840" .equals(currency)) return currency;
        else throw new RuntimeException("Unsupported currency:" + currency);
    }

    private static String standalizeCountryCode(String countryCode) {
        if (countryCode == null) return null;
        else if ("VNM" .equalsIgnoreCase(countryCode)) return "VN";
        else return countryCode;
    }

//    public  void main(String... args) {
//        Map<Integer, Object> fields = new LinkedHashMap<>();
//        fields.put(0, "01");
//        fields.put(1, "12");
//        fields.put(2, "9704360100000001");
//        fields.put(52, "1234");
//        fields.put(53, "704");
//        fields.put(54, "100000000.00");
//        fields.put(58, "VN");
//        fields.put(59, "TEST MERCHANT");
//        fields.put(60, "HA NOI");
//
//        Map<Integer, Object> f62 = new LinkedHashMap<>();
//        f62.put(1, "Thanh toan hoa don 123456");
//        f62.put(5, "Reference ID 1234567890");
//
//        fields.put(62, f62);
//
//        String merchantData = create(fields);
//        System.out.println("Merchant Data=" + merchantData);
//    }

}
