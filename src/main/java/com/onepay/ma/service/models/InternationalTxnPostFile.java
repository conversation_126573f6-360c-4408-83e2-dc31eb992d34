package com.onepay.ma.service.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public class InternationalTxnPostFile {
    private String keywords;
    private String merchant_id;
    private String from_date;
    private String to_date;
    private int transaction_id;
    private String merchant_transaction_ref;
    private String authentication_type;
    private String authentication_state;
    private String authorization_code;
    private int acquirer_id;
    private String currency;
    private String card_type;
    private String order_info;
    private String card_number;
    private String status;
    private String transaction_type;
    private String is_show_3d;
    private String version;

    public String getIs_show_3d() {
        return is_show_3d;
    }

    public void setIs_show_3d(String is_show_3d) {
        this.is_show_3d = is_show_3d;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getFrom_date() {
        return from_date;
    }

    public void setFrom_date(String from_date) {
        this.from_date = from_date;
    }

    public String getTo_date() {
        return to_date;
    }

    public void setTo_date(String to_date) {
        this.to_date = to_date;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getAuthentication_type() {
        return authentication_type;
    }

    public void setAuthentication_type(String authentication_type) {
        this.authentication_type = authentication_type;
    }

    public String getAuthentication_state() {
        return authentication_state;
    }

    public void setAuthentication_state(String authentication_state) {
        this.authentication_state = authentication_state;
    }

    public String getAuthorization_code() {
        return authorization_code;
    }

    public void setAuthorization_code(String authorization_code) {
        this.authorization_code = authorization_code;
    }

    public int getAcquirer_id() {
        return acquirer_id;
    }

    public void setAcquirer_id(int acquirer_id) {
        this.acquirer_id = acquirer_id;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
