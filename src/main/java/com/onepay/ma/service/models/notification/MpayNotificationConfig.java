package com.onepay.ma.service.models.notification;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Terminal;
import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.shop.Store;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by anhkh on 25-Sep-17.
 */
public class MpayNotificationConfig {

    private Integer id;
    private String merchantId;
    private String terminalId;
    private String userId;
    private String storeId;
    private String type;
    private Timestamp created_date;

    private List<Terminal> terminals;
    private List<User> users;
    private List<String> types;
    private List<Store> stores;

    private String displayTerminalId;
    private String displayUserId;
    private String displayStoreId;
    private String displayType;

    private List<String> terminalIds;
    private List<String> userIds;
    private List<String> storeIds;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDisplayTerminalId() {
        return displayTerminalId;
    }

    public void setDisplayTerminalId(String displayTerminalId) {
        this.displayTerminalId = displayTerminalId;
    }

    public String getDisplayUserId() {
        return displayUserId;
    }

    public void setDisplayUserId(String displayUserId) {
        this.displayUserId = displayUserId;
    }

    public String getDisplayStoreId() {
        return displayStoreId;
    }

    public void setDisplayStoreId(String displayStoreId) {
        this.displayStoreId = displayStoreId;
    }

    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType;
    }

    public List<String> getTerminalIds() {
        return terminalIds;
    }

    public void setTerminalIds(List<String> terminalIds) {
        this.terminalIds = terminalIds;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }

    public List<String> getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(List<String> storeIds) {
        this.storeIds = storeIds;
    }

    public List<Terminal> getTerminals() {
        return terminals;
    }

    public void setTerminals(List<Terminal> terminals) {
        this.terminals = terminals;
    }

    public List<User> getUsers() {
        return users;
    }

    public void setUsers(List<User> users) {
        this.users = users;
    }

    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    public List<Store> getStores() {
        return stores;
    }

    public void setStores(List<Store> stores) {
        this.stores = stores;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Timestamp getCreated_date() {
        return created_date;
    }

    public void setCreated_date(Timestamp created_date) {
        this.created_date = created_date;
    }
}
