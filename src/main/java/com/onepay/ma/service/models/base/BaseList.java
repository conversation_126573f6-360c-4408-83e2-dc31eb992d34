package com.onepay.ma.service.models.base;

import java.util.List;

/**
 * Created by anhkh on 8/8/2016.
 */
public class BaseList<T> {
    private List<T> list;

    private int total_items;

    /**
     * Getter for property 'list'.
     *
     * @return Value for property 'list'.
     */
    public List<T> getList() {
        return list;
    }

    /**
     * Setter for property 'list'.
     *
     * @param list Value to set for property 'list'.
     */
    public void setList(List<T> list) {
        this.list = list;
    }

    /**
     * Getter for property 'total_items'.
     *
     * @return Value for property 'total_items'.
     */
    public int getTotal_items() {
        return total_items;
    }

    /**
     * Setter for property 'total_items'.
     *
     * @param total_items Value to set for property 'total_items'.
     */
    public void setTotal_items(int total_items) {
        this.total_items = total_items;
    }
}
