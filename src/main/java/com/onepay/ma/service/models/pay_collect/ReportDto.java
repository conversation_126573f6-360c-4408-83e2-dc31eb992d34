package com.onepay.ma.service.models.pay_collect;

import java.sql.Timestamp;

public class ReportDto {

    private Timestamp dataType;
    private String merchantId;
    private String virtualId;
    private String senderName;
    private String senderBank;
    private String receivedName;
    private String receivedBank;
    private int noTrans;
    private Double totalTrans;

    public Timestamp getDataType() {
        return dataType;
    }

    public void setDataType(Timestamp dataType) {
        this.dataType = dataType;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getVirtualId() {
        return virtualId;
    }

    public void setVirtualId(String virtualId) {
        this.virtualId = virtualId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderBank() {
        return senderBank;
    }

    public void setSenderBank(String senderBank) {
        this.senderBank = senderBank;
    }

    public String getReceivedName() {
        return receivedName;
    }

    public void setReceivedName(String receivedName) {
        this.receivedName = receivedName;
    }

    public String getReceivedBank() {
        return receivedBank;
    }

    public void setReceivedBank(String receivedBank) {
        this.receivedBank = receivedBank;
    }

    public int getNoTrans() {
        return noTrans;
    }

    public void setNoTrans(int noTrans) {
        this.noTrans = noTrans;
    }

    public Double getTotalTrans() {
        return totalTrans;
    }

    public void setTotalTrans(Double totalTrans) {
        this.totalTrans = totalTrans;
    }


}
