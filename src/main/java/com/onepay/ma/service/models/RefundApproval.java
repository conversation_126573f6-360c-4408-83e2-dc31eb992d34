package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by anhkh on 8/8/2016.
 * RefundApproval Class 
 */
public class RefundApproval {

    public static enum Status {

        REJECTED(404, "Refund rejected"),

        APPROVED(403, "Refund approved"),

        REQUEST(401, "Request Refund"),

        REQUEST_ONEPAY(405, "Refund Waiting for OnePAY's Approval"),

        SUCCESSFUL(400, "Successful"),

        FAILED(0, "Failed"),
        WTF_ONEPAY_APPROVAL(210, "Waiting for OnePay's Approval"),
        REQUEST_EXTERNAL(300, "Wait for External Approval");

        public String content;
        public Integer code;

        private Status(Integer code, String content) {
            this.content = content;
            this.code = code;
        }

        public static String contentOf(int code) {
            for (Status s : Status.values()) {
                if (s.code == code) {
                    return s.content;
                }
            }
            return "";
        }
    }

    private Integer transaction_id;
    private String merchant_id;
    private String original_transaction_id;
    private String merchant_transaction_ref;
    private String operator_id;
    private Timestamp transaction_time;
    private Amount amount;
    private Integer status;
    private String transaction_type;
    private String data;
    private Integer parent_id;
    private Integer n_type; // DuongPXT authorize capture: add n_type
    private String brandId;
    private String dataPromotion; // promotion moca
    private String sTransRefId;
    private Double paymentAmount;
    private String note;
    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    /**
     * Getter for property 'parent_id'.
     *
     * @return Value for property 'parent_id'.
     */
    public Integer getParent_id() {
        return parent_id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    /**
     * Setter for property 'parent_id'.
     *
     * @param parent_id Value to set for property 'parent_id'.
     */
    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    /**
     * Getter for property 'transaction_id'.
     *
     * @return Value for property 'transaction_id'.
     */
    public Integer getTransaction_id() {
        return transaction_id;
    }

    /**
     * Setter for property 'transaction_id'.
     *
     * @param transaction_id Value to set for property 'transaction_id'.
     */
    public void setTransaction_id(Integer transaction_id) {
        this.transaction_id = transaction_id;
    }

    /**
     * Getter for property 'merchant_id'.
     *
     * @return Value for property 'merchant_id'.
     */
    public String getMerchant_id() {
        return merchant_id;
    }

    /**
     * Setter for property 'merchant_id'.
     *
     * @param merchant_id Value to set for property 'merchant_id'.
     */
    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getOriginal_transaction_id() {
        return original_transaction_id;
    }

    public void setOriginal_transaction_id(String original_transaction_id) {
        this.original_transaction_id = original_transaction_id;
    }

    /**
     * Getter for property 'merchant_transaction_ref'.
     *
     * @return Value for property 'merchant_transaction_ref'.
     */
    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    /**
     * Setter for property 'merchant_transaction_ref'.
     *
     * @param merchant_transaction_ref Value to set for property 'merchant_transaction_ref'.
     */
    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    /**
     * Getter for property 'operator_id'.
     *
     * @return Value for property 'operator_id'.
     */
    public String getOperator_id() {
        return operator_id;
    }

    /**
     * Setter for property 'operator_id'.
     *
     * @param operator_id Value to set for property 'operator_id'.
     */
    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    /**
     * Getter for property 'transaction_time'.
     *
     * @return Value for property 'transaction_time'.
     */
    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    /**
     * Setter for property 'transaction_time'.
     *
     * @param transaction_time Value to set for property 'transaction_time'.
     */
    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    /**
     * Getter for property 'amount'.
     *
     * @return Value for property 'amount'.
     */
    public Amount getAmount() {
        return amount;
    }

    /**
     * Setter for property 'amount'.
     *
     * @param amount Value to set for property 'amount'.
     */
    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    /**
     * Getter for property 'status'.
     *
     * @return Value for property 'status'.
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * Setter for property 'status'.
     *
     * @param status Value to set for property 'status'.
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * Getter for property 'transaction_type'.
     *
     * @return Value for property 'transaction_type'.
     */
    public String getTransaction_type() {
        return transaction_type;
    }

    /**
     * Setter for property 'transaction_type'.
     *
     * @param transaction_type Value to set for property 'transaction_type'.
     */
    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    /**
     * @return String return the n_type
     */
    public Integer getN_type() {
        return n_type;
    }

    /**
     * @param n_type the n_type to set
     */
    public void setN_type(Integer n_type) {
        this.n_type = n_type;
    }

    /**
     * @return String return the brandId
     */
    public String getBrandId() {
        return brandId;
    }

    /**
     * @param brandId the brandId to set
     */
    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getDataPromotion() {
        return dataPromotion;
    }

    public void setDataPromotion(String dataPromotion) {
        this.dataPromotion = dataPromotion;
    }

    /**
     * @return String return the sTransRefId
     */
    public String getSTransRefId() {
        return sTransRefId;
    }

    /**
     * @param sTransRefId the sTransRefId to set
     */
    public void setSTransRefId(String sTransRefId) {
        this.sTransRefId = sTransRefId;
    }

    /**
     * @return String return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }
    
    @Override
    public String toString() {
        return "RefundApproval [transaction_id=" + transaction_id + ", merchant_id=" + merchant_id + ", original_transaction_id=" + original_transaction_id + ", merchant_transaction_ref=" + merchant_transaction_ref + ", operator_id=" + operator_id + ", transaction_time=" + transaction_time + ", amount=" + amount + ", status=" + status + ", transaction_type=" + transaction_type + ", data=" + data + ", parent_id=" + parent_id + ", n_type=" + n_type + ", brandId=" + brandId + ", dataPromotion=" + dataPromotion + ", sTransRefId=" + sTransRefId + "]";
    }

}
