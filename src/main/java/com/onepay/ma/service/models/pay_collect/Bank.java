package com.onepay.ma.service.models.pay_collect;

public class Bank {
    private String id;
    private String bankName;
    private String swiftCode;
    private String state;
    private Long internerCost;
    private Long externerCost;
    private String createdDate;
    private String updatedDate;
    private String desc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getInternerCost() {
        return internerCost;
    }

    public void setInternerCost(Long internerCost) {
        this.internerCost = internerCost;
    }

    public Long getExternerCost() {
        return externerCost;
    }

    public void setExternerCost(Long externerCost) {
        this.externerCost = externerCost;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
