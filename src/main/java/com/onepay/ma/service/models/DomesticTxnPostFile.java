package com.onepay.ma.service.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public class DomesticTxnPostFile {
    private String keywords;
    private String merchant_id;
    private String bank_id;
    private int transaction_id;
    private String from_date;
    private String to_date;
    private String merchant_transaction_ref;
    private String order_info;
    private String card_number;
    private String status;
    private String transaction_type;

    private String target;
    private String version;
    private String lang;
    // SAMSUNG
    private String customer_name;
    private String customer_mobile;
    private String customer_email;
    private String fraud_check;
    private String merchant_website;
    private String order_status;

    public String getOrder_status() { return order_status; }

    public void setOrder_status(String order_status) { this.order_status = order_status; }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getMerchant_website() {
        return merchant_website;
    }

    public void setMerchant_website(String merchant_website) {
        this.merchant_website = merchant_website;
    }
    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getBank_id() {
        return bank_id;
    }

    public void setBank_id(String bank_id) {
        this.bank_id = bank_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getFrom_date() {
        return from_date;
    }

    public void setFrom_date(String from_date) {
        this.from_date = from_date;
    }

    public String getTo_date() {
        return to_date;
    }

    public void setTo_date(String to_date) {
        this.to_date = to_date;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }
    
    /**
     * @return String return the lang
     */
    public String getLang() {
        return lang;
    }

    /**
     * @param lang the lang to set
     */
    public void setLang(String lang) {
        this.lang = lang;
    }

}
