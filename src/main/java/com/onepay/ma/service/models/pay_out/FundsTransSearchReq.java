package com.onepay.ma.service.models.pay_out;

public class FundsTransSearchReq {
    private String userId ;
    private String xUserId;
    private String xRequestId;
    private String fromDate;
    private String toDate;
    private String merchantId;
    private String fundsTransferId;
    private String transactionId;
    private String merchantAccount;
    private Integer pageSize;
    private Integer page;


    public FundsTransSearchReq(String userId, String xUserId, String xRequestId, String fromDate, String toDate, String merchantId, String fundsTransferId, String transactionId, String merchantAccount, Integer pageSize, Integer page) {
        this.userId = userId;
        this.xUserId = xUserId;
        this.xRequestId = xRequestId;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.merchantId = merchantId;
        this.fundsTransferId = fundsTransferId;
        this.transactionId = transactionId;
        this.merchantAccount = merchantAccount;
        this.pageSize = pageSize;
        this.page = page;
    }

    public String getUserId() {
        return userId;
    }

    public String getxUserId() {
        return xUserId;
    }

    public String getxRequestId() {
        return xRequestId;
    }

    public String getFromDate() {
        return fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public String getFundsTransferId() {
        return fundsTransferId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public Integer getPage() {
        return page;
    }
}
