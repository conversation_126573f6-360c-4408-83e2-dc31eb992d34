package com.onepay.ma.service.models.pay_out;

import java.sql.Timestamp;

public class FundsTransHistoryDto {
    private String s_id;

    private String merchantId;
    private String merchantAccount;
    private String merchantName;
    private String merchantTransId;
    private Double balanceBefore;
    private Double balanceAfter;
    private Double balanceBankBefore;
    private Double balanceBankAfter;

    private String receivedAccountNumber;
    private String receivedAccountName;
    private String receivedCardNumber;
    private String transactionId;
    private Timestamp transactionDate;
    private Double amount;
    private String remark;
    private String state;

    private String bankTransactionId;
    private String beneficiaryBankName;
    private String beneficiarySwiftCode;
    private String bankCode;
    private String bankMsg;
    private Timestamp createdDate;
    private Timestamp updatedDate;
    private Timestamp fundDate;
    private Integer checkApproval;
    private Integer checkOperator;
    private String metadata;
    private String creator_id;
    private String creator_name;
    private String verifier_id;
    private String verifier_name;
    private String operator;

    public FundsTransHistoryDto() {
    }

    public FundsTransHistoryDto(String s_id, String merchantId, String merchantAccount, String merchantName, String merchantTransId, Double balanceBefore, Double balanceAfter,
                                String receivedAccountNumber, String receivedAccountName, String receivedCardNumber, String transactionId, Timestamp transactionDate, Double amount, String remark, String state,
                                String bankTransactionId, String bankName, String bankCode, String bankMsg, Timestamp createdDate, Double balanceBankBefore, Double balanceBankAfter) {
        this.s_id = s_id;
        this.merchantId = merchantId;
        this.merchantAccount = merchantAccount;
        this.merchantName = merchantName;
        this.merchantTransId = merchantTransId;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceAfter;
        this.balanceBankBefore = balanceBankBefore;
        this.balanceBankAfter = balanceBankAfter;
        this.receivedAccountNumber = receivedAccountNumber;
        this.receivedAccountName = receivedAccountName;
        this.receivedCardNumber = receivedCardNumber;
        this.transactionId = transactionId;
        this.transactionDate = transactionDate;
        this.amount = amount;
        this.remark = remark;
        this.state = state;
        this.bankTransactionId = bankTransactionId;
        this.beneficiaryBankName = bankName;
        this.bankCode = bankCode;
        this.bankMsg = bankMsg;
        this.createdDate = createdDate;
    }

    public String getS_id() {
        return s_id;
    }

    public void setS_id(String s_id) {
        this.s_id = s_id;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantTransId() {
        return merchantTransId;
    }

    public void setMerchantTransId(String merchantTransId) {
        this.merchantTransId = merchantTransId;
    }

    public Double getBalanceBefore() {
        return balanceBefore;
    }

    public void setBalanceBefore(Double balanceBefore) {
        this.balanceBefore = balanceBefore;
    }

    public Double getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(Double balanceAfter) {
        this.balanceAfter = balanceAfter;
    }

    public String getReceivedAccountNumber() {
        return receivedAccountNumber;
    }

    public void setReceivedAccountNumber(String receivedAccountNumber) {
        this.receivedAccountNumber = receivedAccountNumber;
    }

    public String getReceivedAccountName() {
        return receivedAccountName;
    }

    public void setReceivedAccountName(String receivedAccountName) {
        this.receivedAccountName = receivedAccountName;
    }

    public String getReceivedCardNumber() {
        return receivedCardNumber;
    }

    public void setReceivedCardNumber(String receivedCardNumber) {
        this.receivedCardNumber = receivedCardNumber;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Timestamp getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Timestamp transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getBankTransactionId() {
        return bankTransactionId;
    }

    public void setBankTransactionId(String bankTransactionId) {
        this.bankTransactionId = bankTransactionId;
    }

    public String getBeneficiaryBankName() {
        return beneficiaryBankName;
    }

    public void setBeneficiaryBankName(String beneficiaryBankName) {
        this.beneficiaryBankName = beneficiaryBankName;
    }

    public String getBeneficiarySwiftCode() {
        return beneficiarySwiftCode;
    }

    public void setBeneficiarySwiftCode(String beneficiarySwiftCode) {
        this.beneficiarySwiftCode = beneficiarySwiftCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankMsg() {
        return bankMsg;
    }

    public void setBankMsg(String bankMsg) {
        this.bankMsg = bankMsg;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public Double getBalanceBankBefore() {
        return balanceBankBefore;
    }

    public void setBalanceBankBefore(Double balanceBankBefore) {
        this.balanceBankBefore = balanceBankBefore;
    }

    public Double getBalanceBankAfter() {
        return balanceBankAfter;
    }

    public void setBalanceBankAfter(Double balanceBankAfter) {
        this.balanceBankAfter = balanceBankAfter;
    }

    public Integer getCheckApproval() {
        return checkApproval;
    }

    public void setCheckApproval(Integer checkApproval) {
        this.checkApproval = checkApproval;
    }

    public Integer getCheckOperator() {
        return checkOperator;
    }

    public void setCheckOperator(Integer checkOperator) {
        this.checkOperator = checkOperator;
    }

    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Timestamp getFundDate() {
        return fundDate;
    }

    public void setFundDate(Timestamp fundDate) {
        this.fundDate = fundDate;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getCreator_id() {
        return creator_id;
    }

    public void setCreator_id(String creator_id) {
        this.creator_id = creator_id;
    }

    public String getCreator_name() {
        return creator_name;
    }

    public void setCreator_name(String creator_name) {
        this.creator_name = creator_name;
    }

    public String getVerifier_id() {
        return verifier_id;
    }

    public void setVerifier_id(String verifier_id) {
        this.verifier_id = verifier_id;
    }

    public String getVerifier_name() {
        return verifier_name;
    }

    public void setVerifier_name(String verifier_name) {
        this.verifier_name = verifier_name;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
