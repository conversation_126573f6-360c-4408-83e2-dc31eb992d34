package com.onepay.ma.service.models.reconciliation;


import java.sql.Timestamp;

public class Reconciliation {
    private Timestamp settlement_date;
    private String acquirer;
    private String card_type;
    private String currency;
    private String merchant_id;
    private String mid_no;
    private Integer count_transaction;
    private Integer count_purchase;
    private Integer count_refund;
    private Integer count_void;
    private Integer count_authorise;
    private Integer count_capture;
    private Double total_purchase;
    private Double total_refund;
    private Double total_void;
    private Double total_capture;
    private Double total_authorise;

    public String getMid_no() {
        return mid_no;
    }

    public void setMid_no(String mid_no) {
        this.mid_no = mid_no;
    }

    public Integer getCount_purchase() {
        return count_purchase;
    }

    public void setCount_purchase(Integer count_purchase) {
        this.count_purchase = count_purchase;
    }

    public Double getTotal_capture() {
        return total_capture;
    }

    public void setTotal_capture(Double total_capture) {
        this.total_capture = total_capture;
    }

    public Double getTotal_authorise() {
        return total_authorise;
    }

    public void setTotal_authorise(Double total_authorise) {
        this.total_authorise = total_authorise;
    }

    public Integer getCount_refund() {
        return count_refund;
    }

    public void setCount_refund(Integer count_refund) {
        this.count_refund = count_refund;
    }

    public Integer getCount_void() {
        return count_void;
    }

    public void setCount_void(Integer count_void) {
        this.count_void = count_void;
    }

    public Integer getCount_authorise() {
        return count_authorise;
    }

    public void setCount_authorise(Integer count_authorise) {
        this.count_authorise = count_authorise;
    }

    public Integer getCount_capture() {
        return count_capture;
    }

    public void setCount_capture(Integer count_capture) {
        this.count_capture = count_capture;
    }

    public Timestamp getSettlement_date() {
        return settlement_date;
    }

    public void setSettlement_date(Timestamp settlement_date) {
        this.settlement_date = settlement_date;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public Integer getCount_transaction() {
        return count_transaction;
    }

    public void setCount_transaction(Integer count_transaction) {
        this.count_transaction = count_transaction;
    }

    public Double getTotal_purchase() {
        return total_purchase;
    }

    public void setTotal_purchase(Double total_purchase) {
        this.total_purchase = total_purchase;
    }

    public Double getTotal_refund() {
        return total_refund;
    }

    public void setTotal_refund(Double total_refund) {
        this.total_refund = total_refund;
    }

    public Double getTotal_void() {
        return total_void;
    }

    public void setTotal_void(Double total_void) {
        this.total_void = total_void;
    }
}
