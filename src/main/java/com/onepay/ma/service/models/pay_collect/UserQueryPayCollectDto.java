package com.onepay.ma.service.models.pay_collect;

import java.io.Serializable;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/16/2020
 * Time: 9:59 AM
 * To change this ma-web.
 */

public class UserQueryPayCollectDto implements Serializable {
    private String user_id;
    private String account;
    private String bank;
    private String merchant_id;
    private String user_name;
    private String reference;
    private String state;
    private String lang;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    /**
     * @return String return the lang
     */
    public String getLang() {
        return lang;
    }

    /**
     * @param lang the lang to set
     */
    public void setLang(String lang) {
        this.lang = lang;
    }

}
