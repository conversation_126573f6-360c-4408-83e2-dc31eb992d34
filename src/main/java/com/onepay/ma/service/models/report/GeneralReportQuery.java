package com.onepay.ma.service.models.report;

import com.onepay.ma.service.models.base.BaseSearchCondition;

public class GeneralReportQuery extends BaseSearchCondition {
    private String merchantId;
    private String merchantIdQt;
    private String merchantIdNd;
    private String merchantIdQr;
    private String fromDate;
    private String toDate;
    private String cardType;
    private String orderInfo;
    private String merchantTransactionRef;
    private String cardNumber;
    private String transactionId;
    private String transactionState;
    private String transactionType;
    private String target;
    private String version;
    private String appName;
    private String lang;

    public String getMerchantIdQt() {
        return merchantIdQt;
    }

    public void setMerchantIdQt(String merchantIdQt) {
        this.merchantIdQt = merchantIdQt;
    }

    public String getMerchantIdNd() {
        return merchantIdNd;
    }

    public void setMerchantIdNd(String merchantIdNd) {
        this.merchantIdNd = merchantIdNd;
    }

    public String getMerchantIdQr() {
        return merchantIdQr;
    }

    public void setMerchantIdQr(String merchantIdQr) {
        this.merchantIdQr = merchantIdQr;
    }

    public String getTransactionState() {
        return transactionState;
    }

    public void setTransactionState(String transactionState) {
        this.transactionState = transactionState;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getMerchantTransactionRef() {
        return merchantTransactionRef;
    }

    public void setMerchantTransactionRef(String merchantTransactionRef) {
        this.merchantTransactionRef = merchantTransactionRef;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * @return the cardType
     */
    public String getCardType() {
        return cardType;
    }

    /**
     * @param cardType the cardType to set
     */
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * @return the fromDate
     */
    public String getFromDate() {
        return fromDate;
    }

    /**
     * @param fromDate the fromDate to set
     */
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * @return the toDate
     */
    public String getToDate() {
        return toDate;
    }

    /**
     * @param toDate the toDate to set
     */
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    /**
     * @return String return the target
     */
    public String getTarget() {
        return target;
    }

    /**
     * @param target the target to set
     */
    public void setTarget(String target) {
        this.target = target;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppName() {
        return appName;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
