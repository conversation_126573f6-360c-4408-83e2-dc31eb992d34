package com.onepay.ma.service.models;

import com.onepay.ma.service.models.base.BaseSearchCondition;

/**
 * Created by anhkh on 04-Jan-17.
 */
public class PromotionCodeSearchQuery extends BaseSearchCondition {

    private String customer;
    private String couponState;
    private String smsState;
    private String transRef;
    private String orderRef;
    private String merchantId;
    private String couponCode;
    private Integer transactionId;

    /**
     * Getter for property 'couponCode'.
     *
     * @return Value for property 'couponCode'.
     */
    public String getCouponCode() {
        return couponCode;
    }

    /**
     * Setter for property 'couponCode'.
     *
     * @param couponCode Value to set for property 'couponCode'.
     */
    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    /**
     * Getter for property 'customer'.
     *
     * @return Value for property 'customer'.
     */
    public String getCustomer() {
        return customer;
    }

    /**
     * Setter for property 'customer'.
     *
     * @param customer Value to set for property 'customer'.
     */
    public void setCustomer(String customer) {
        this.customer = customer;
    }

    /**
     * Getter for property 'couponState'.
     *
     * @return Value for property 'couponState'.
     */
    public String getCouponState() {
        return couponState;
    }

    /**
     * Setter for property 'couponState'.
     *
     * @param couponState Value to set for property 'couponState'.
     */
    public void setCouponState(String couponState) {
        this.couponState = couponState;
    }

    /**
     * Getter for property 'smsState'.
     *
     * @return Value for property 'smsState'.
     */
    public String getSmsState() {
        return smsState;
    }

    /**
     * Setter for property 'smsState'.
     *
     * @param smsState Value to set for property 'smsState'.
     */
    public void setSmsState(String smsState) {
        this.smsState = smsState;
    }

    /**
     * Getter for property 'transRef'.
     *
     * @return Value for property 'transRef'.
     */
    public String getTransRef() {
        return transRef;
    }

    /**
     * Setter for property 'transRef'.
     *
     * @param transRef Value to set for property 'transRef'.
     */
    public void setTransRef(String transRef) {
        this.transRef = transRef;
    }

    /**
     * Getter for property 'orderRef'.
     *
     * @return Value for property 'orderRef'.
     */
    public String getOrderRef() {
        return orderRef;
    }

    /**
     * Setter for property 'orderRef'.
     *
     * @param orderRef Value to set for property 'orderRef'.
     */
    public void setOrderRef(String orderRef) {
        this.orderRef = orderRef;
    }

    /**
     * Getter for property 'merchantId'.
     *
     * @return Value for property 'merchantId'.
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Setter for property 'merchantId'.
     *
     * @param merchantId Value to set for property 'merchantId'.
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }


    /**
     * Getter for property 'transactionId'.
     *
     * @return Value for property 'transactionId'.
     */
    public Integer getTransactionId() {
        return transactionId;
    }

    /**
     * Setter for property 'transactionId'.
     *
     * @param transactionId Value to set for property 'transactionId'.
     */
    public void setTransactionId(Integer transactionId) {
        this.transactionId = transactionId;
    }
}
