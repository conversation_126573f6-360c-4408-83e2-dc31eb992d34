package com.onepay.ma.service.models;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public class InternationalRefundParameter implements Serializable {
    private String keywords;
    private String merchantId;
    private String fromDate;
    private String toDate;
    private String orderInfo;
    private String transactionReference;
    private String transactionType;
    private String status;
    private String cardNumber;
    private String cardType;
    private Integer acquirerId;
    private int page;
    private int pageSize;
    private int transactionId;
    private String currency;
    private int offset;
    private Integer refundType; // DuongPXT authorize captrue: add refund type

    public Integer getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(Integer acquirerId) {
        this.acquirerId = acquirerId;
    }

    /**
     * Getter for property 'cardType'.
     *
     * @return Value for property 'cardType'.
     */
    public String getCardType() {
        return cardType;
    }

    /**
     * Setter for property 'cardType'.
     *
     * @param cardType Value to set for property 'cardType'.
     */
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * Getter for property 'cardNumber'.
     *
     * @return Value for property 'cardNumber'.
     */
    public String getCardNumber() {
        return cardNumber;
    }

    /**
     * Setter for property 'cardNumber'.
     *
     * @param cardNumber Value to set for property 'cardNumber'.
     */
    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getTransactionReference() {
        return transactionReference;
    }

    public void setTransactionReference(String transactionReference) {
        this.transactionReference = transactionReference;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(int transactionId) {
        this.transactionId = transactionId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }
}
