package com.onepay.ma.service.models.notification;

import com.onepay.ma.service.models.base.BaseSearchCondition;

/**
 * Created by anhkh on 25-Sep-17.
 */
public class NotificationConfigSearchQuery extends BaseSearchCondition {

    private String merchantId;
    private String terminalId;
    private String shopId;
    private String type;
    private String userId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
