package com.onepay.ma.service.models.appToken;

/**
 * Created by anhkh on 05-Oct-17.
 */
public class AppToken {

    private String token;
    private String userId;
    private String language;
    private String os;
    private String os_version;

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOs_version() {
        return os_version;
    }

    public void setOs_version(String os_version) {
        this.os_version = os_version;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AppToken appToken = (AppToken) o;

        return token.equals(appToken.token);
    }

    @Override
    public int hashCode() {
        return token.hashCode();
    }


    @Override
    public String toString() {
        return this.token;
    }
}
