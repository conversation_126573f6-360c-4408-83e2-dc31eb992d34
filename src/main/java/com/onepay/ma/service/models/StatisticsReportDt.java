package com.onepay.ma.service.models;

/**
 * Created by tuydv on 12/6/18.
 */
public class StatisticsReportDt {
    Integer rowNumber;
    String merchantId;
    String merchantName;
    String acqCode;
    String cardType;
    String transDate;
    String transactionSource;
    String transType;
    String bankTransId;
    String cusTransId;
    String merchantRef;
    String onepayTransId;
    String cardNumber;
    String currency;

    Double amountIss;
    Double amountAcq;
    Double issFee;
    Double acqFee;
    Double percentFeeIss ;
    Double percentFeeAcq;

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getAcqCode() {
        return acqCode;
    }

    public void setAcqCode(String acqCode) {
        this.acqCode = acqCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransactionSource() {
        return transactionSource;
    }

    public void setTransactionSource(String transactionSource) {
        this.transactionSource = transactionSource;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getBankTransId() {
        return bankTransId;
    }

    public void setBankTransId(String bankTransId) {
        this.bankTransId = bankTransId;
    }

    public String getCusTransId() {
        return cusTransId;
    }

    public void setCusTransId(String cusTransId) {
        this.cusTransId = cusTransId;
    }

    public String getMerchantRef() {
        return merchantRef;
    }

    public void setMerchantRef(String merchantRef) {
        this.merchantRef = merchantRef;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getAmountIss() {
        return amountIss;
    }

    public void setAmountIss(Double amountIss) {
        this.amountIss = amountIss;
    }

    public Double getAmountAcq() {
        return amountAcq;
    }

    public void setAmountAcq(Double amountAcq) {
        this.amountAcq = amountAcq;
    }

    public Double getIssFee() {
        return issFee;
    }

    public void setIssFee(Double issFee) {
        this.issFee = issFee;
    }

    public Double getAcqFee() {
        return acqFee;
    }

    public void setAcqFee(Double acqFee) {
        this.acqFee = acqFee;
    }

    public Double getPercentFeeIss() {
        return percentFeeIss;
    }

    public void setPercentFeeIss(Double percentFeeIss) {
        this.percentFeeIss = percentFeeIss;
    }

    public Double getPercentFeeAcq() {
        return percentFeeAcq;
    }

    public void setPercentFeeAcq(Double percentFeeAcq) {
        this.percentFeeAcq = percentFeeAcq;
    }

    public String getOnepayTransId() {
        return onepayTransId;
    }

    public void setOnepayTransId(String onepayTransId) {
        this.onepayTransId = onepayTransId;
    }
}
