package com.onepay.ma.service.models.notification;

import java.util.List;

/**
 * Created by anhkh on 12-Oct-17.
 */
public class MpayNotifications {
    List<MpayNotification> list;
    Integer total_unseen;
    Integer total_items;

    public Integer getTotal_items() {
        return total_items;
    }

    public void setTotal_items(Integer total_items) {
        this.total_items = total_items;
    }

    public List<MpayNotification> getList() {
        return list;
    }

    public void setList(List<MpayNotification> list) {
        this.list = list;
    }

    public Integer getTotal_unseen() {
        return total_unseen;
    }

    public void setTotal_unseen(Integer total_unseen) {
        this.total_unseen = total_unseen;
    }
}
