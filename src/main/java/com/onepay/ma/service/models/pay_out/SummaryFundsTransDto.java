package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 5:54 PM
 * To change this ma-web.
 */

public class SummaryFundsTransDto implements Serializable {
    private String accountId ;
    private String merchantId ;
    private String merchantName ;
    private String senderSwiftCode ;
    private String receiptSwiftCode ;
    private BigInteger count ;
    private BigInteger amount ;
    private String dateType ;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getSenderSwiftCode() {
        return senderSwiftCode;
    }

    public void setSenderSwiftCode(String senderSwiftCode) {
        this.senderSwiftCode = senderSwiftCode;
    }

    public String getReceiptSwiftCode() {
        return receiptSwiftCode;
    }

    public void setReceiptSwiftCode(String receiptSwiftCode) {
        this.receiptSwiftCode = receiptSwiftCode;
    }

    public BigInteger getCount() {
        return count;
    }

    public void setCount(BigInteger count) {
        this.count = count;
    }

    public BigInteger getAmount() {
        return amount;
    }

    public void setAmount(BigInteger amount) {
        this.amount = amount;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }
}
