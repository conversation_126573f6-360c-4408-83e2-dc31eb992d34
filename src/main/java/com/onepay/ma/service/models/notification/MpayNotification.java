package com.onepay.ma.service.models.notification;

import java.sql.Timestamp;

/**
 * Created by anhkh on 09-Oct-17.
 */
public class MpayNotification {
    private Integer id;
    private String userId;
    private String target;
    private String category;
    private String state;
    private String content;
    private String header;

    private Integer msg_id;
    private String token;
    private Timestamp create_date;
    private String s_create_date;

    public String getS_create_date() {
        return s_create_date;
    }

    public void setS_create_date(String s_create_date) {
        this.s_create_date = s_create_date;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Integer getMsg_id() {
        return msg_id;
    }

    public void setMsg_id(Integer msg_id) {
        this.msg_id = msg_id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }


    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
