package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class InternationalReportConvert {
    private List<InternationalReport> items;
    private String currency;
    private int total_transaction_count;
    private int total_refund_count;
    private int total_authorize_count;
    private int total_capture_count;
    private double total_transaction_total;
    private double total_refund_total;
    private double total_void_transaction_total;
    private double total_authorize_total;
    private double total_capture_total;

    public double getTotal_void_transaction_total() { return total_void_transaction_total; }

    public void setTotal_void_transaction_total(double total_void_transaction_total) { this.total_void_transaction_total = total_void_transaction_total; }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<InternationalReport> getItems() {
        return items;
    }

    public void setItems(List<InternationalReport> items) {
        this.items = items;
    }

    public int getTotal_transaction_count() {
        return total_transaction_count;
    }

    public void setTotal_transaction_count(int total_transaction_count) {
        this.total_transaction_count = total_transaction_count;
    }

    public int getTotal_refund_count() {
        return total_refund_count;
    }

    public void setTotal_refund_count(int total_refund_count) {
        this.total_refund_count = total_refund_count;
    }

    public double getTotal_transaction_total() {
        return total_transaction_total;
    }

    public void setTotal_transaction_total(double total_transaction_total) {
        this.total_transaction_total = total_transaction_total;
    }

    public double getTotal_refund_total() {
        return total_refund_total;
    }

    public void setTotal_refund_total(double total_refund_total) {
        this.total_refund_total = total_refund_total;
    }

    public int getTotal_authorize_count() {
        return total_authorize_count;
    }

    public void setTotal_authorize_count(int total_authorize_count) {
        this.total_authorize_count = total_authorize_count;
    }

    public int getTotal_capture_count() {
        return total_capture_count;
    }

    public void setTotal_capture_count(int total_capture_count) {
        this.total_capture_count = total_capture_count;
    }

    public double getTotal_authorize_total() {
        return total_authorize_total;
    }

    public void setTotal_authorize_total(double total_authorize_total) {
        this.total_authorize_total = total_authorize_total;
    }

    public double getTotal_capture_total() {
        return total_capture_total;
    }

    public void setTotal_capture_total(double total_capture_total) {
        this.total_capture_total = total_capture_total;
    }
}
