package com.onepay.ma.service.models;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/6/16.
 */
public class ServerConfig {
    private String ip;
    private int port;
    private boolean keepAlive;
    private int threads;
    private int workers;
    private int timeout;
    private int threadInterval;
    private int rowLevel;
    private int rowLimit;
    private String key;
    private String keyStorePath;
    private String exportLocation;
    private String groupName;
    private String groupPassword;
    private String groupIp;
    private int groupPort;
    private String interfaceHazelcast;
    private boolean multicast;
    private boolean clustering;
    private OneSMConfig oneSMConfig;

    public int getRowLimit() {
        return rowLimit;
    }

    public void setRowLimit(int rowLimit) {
        this.rowLimit = rowLimit;
    }

    public OneSMConfig getOneSMConfig() {
        return oneSMConfig;
    }

    public void setOneSMConfig(OneSMConfig oneSMConfig) {
        this.oneSMConfig = oneSMConfig;
    }

    public boolean isClustering() {
        return clustering;
    }

    public void setClustering(boolean clustering) {
        this.clustering = clustering;
    }

    public boolean isMulticast() {
        return multicast;
    }

    public void setMulticast(boolean multicast) {
        this.multicast = multicast;
    }

    public String getInterfaceHazelcast() {
        return interfaceHazelcast;
    }

    public void setInterfaceHazelcast(String interfaceHazelcast) {
        this.interfaceHazelcast = interfaceHazelcast;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKeyStorePath() {
        return keyStorePath;
    }

    public void setKeyStorePath(String keyStorePath) {
        this.keyStorePath = keyStorePath;
    }

    public String getIp() {
        return ip;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupPassword() {
        return groupPassword;
    }

    public void setGroupPassword(String groupPassword) {
        this.groupPassword = groupPassword;
    }

    public String getGroupIp() {
        return groupIp;
    }

    public void setGroupIp(String groupIp) {
        this.groupIp = groupIp;
    }

    public int getGroupPort() {
        return groupPort;
    }

    public void setGroupPort(int groupPort) {
        this.groupPort = groupPort;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return Integer.parseInt(System.getProperty("server.port", port+""));
    }

    public void setPort(int port) {
        this.port = port;
    }

    public boolean isKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }

    public int getThreads() {
        return threads;
    }

    public void setThreads(int threads) {
        this.threads = threads;
    }

    public int getWorkers() {
        return workers;
    }

    public void setWorkers(int workers) {
        this.workers = workers;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getThreadInterval() {
        return threadInterval;
    }

    public void setThreadInterval(int threadInterval) {
        this.threadInterval = threadInterval;
    }

    public int getRowLevel() {
        return rowLevel;
    }

    public void setRowLevel(int rowLevel) {
        this.rowLevel = rowLevel;
    }

    public String getExportLocation() {
        return exportLocation;
    }

    public void setExportLocation(String exportLocation) {
        this.exportLocation = exportLocation;
    }
}
