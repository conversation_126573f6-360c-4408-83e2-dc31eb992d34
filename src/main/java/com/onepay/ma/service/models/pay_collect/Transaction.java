package com.onepay.ma.service.models.pay_collect;

import java.sql.Timestamp;


public class Transaction {

    private String id;
    private Timestamp createdDate;
    private String receiptAccName;
    private String receiptSwiftCode;
    private String receivedBank;
    private String bankTxnRef;
    private String virtualAccId;
    private Double amount;
    private String currency;
    private String state;
    private String bankMessage;
    private String branchName;
    private String reference;
    private String partnerId;
    // DETAIL
    private String clientId;
    private String senderAccName;
    private String senderSwiftCode;
    private String senderBank;
    private Double balanceBefore;
    private Double balanceAfter;
    private String bankResCode;
    private String senderAccount;
    private String senderAddress;
    private String senderCity;
    private String senderCountry;
    private String receivedAccount;
    private String receivedAddress;
    private String receivedCity;
    private String receivedCountry;
    private Double feeAmount;
    private Double vatAmount;
    private String userId;
    private String address;
    private String email;
    private String mobile;
    private String accountId;
    private Timestamp bankDate;

    public Timestamp getBankDate() {
        return bankDate;
    }

    public void setBankDate(Timestamp bankDate) {
        this.bankDate = bankDate;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getSenderAccName() {
        return senderAccName;
    }

    public void setSenderAccName(String senderAccName) {
        this.senderAccName = senderAccName;
    }

    public String getSenderSwiftCode() {
        return senderSwiftCode;
    }

    public void setSenderSwiftCode(String senderSwiftCode) {
        this.senderSwiftCode = senderSwiftCode;
    }

    public String getSenderBank() {
        return senderBank;
    }

    public void setSenderBank(String senderBank) {
        this.senderBank = senderBank;
    }

    public String getReceiptAccName() {
        return receiptAccName;
    }

    public void setReceiptAccName(String receiptAccName) {
        this.receiptAccName = receiptAccName;
    }

    public String getReceiptSwiftCode() {
        return receiptSwiftCode;
    }

    public void setReceiptSwiftCode(String receiptSwiftCode) {
        this.receiptSwiftCode = receiptSwiftCode;
    }

    public String getReceivedBank() {
        return receivedBank;
    }

    public void setReceivedBank(String receivedBank) {
        this.receivedBank = receivedBank;
    }

    public String getBankTxnRef() {
        return bankTxnRef;
    }

    public void setBankTxnRef(String bankTxnRef) {
        this.bankTxnRef = bankTxnRef;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getBalanceBefore() {
        return balanceBefore;
    }

    public void setBalanceBefore(Double balanceBefore) {
        this.balanceBefore = balanceBefore;
    }

    public Double getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(Double balanceAfter) {
        this.balanceAfter = balanceAfter;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getBankResCode() {
        return bankResCode;
    }

    public void setBankResCode(String bankResCode) {
        this.bankResCode = bankResCode;
    }

    public String getBankMessage() {
        return bankMessage;
    }

    public void setBankMessage(String bankMessage) {
        this.bankMessage = bankMessage;
    }


    public String getSenderAccount() {
        return senderAccount;
    }

    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderCountry() {
        return senderCountry;
    }

    public void setSenderCountry(String senderCountry) {
        this.senderCountry = senderCountry;
    }

    public String getReceivedAccount() {
        return receivedAccount;
    }

    public void setReceivedAccount(String receivedAccount) {
        this.receivedAccount = receivedAccount;
    }

    public String getReceivedAddress() {
        return receivedAddress;
    }

    public void setReceivedAddress(String receivedAddress) {
        this.receivedAddress = receivedAddress;
    }

    public String getReceivedCity() {
        return receivedCity;
    }

    public void setReceivedCity(String receivedCity) {
        this.receivedCity = receivedCity;
    }

    public String getReceivedCountry() {
        return receivedCountry;
    }

    public void setReceivedCountry(String receivedCountry) {
        this.receivedCountry = receivedCountry;
    }

    public Double getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(Double feeAmount) {
        this.feeAmount = feeAmount;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getVirtualAccId() {
        return virtualAccId;
    }

    public void setVirtualAccId(String virtualAccId) {
        this.virtualAccId = virtualAccId;
    }

}
