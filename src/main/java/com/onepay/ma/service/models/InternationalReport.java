package com.onepay.ma.service.models;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class InternationalReport {
    private Date report_date;
    private String merchant_id;
    private int transaction_count;
    private double transaction_total;
    private String currency;
    private String card_type;
    private int refund_count;
    private double refund_total;
    private int void_transaction_count;
    private double void_transaction_total;
    private String transaction_type;
    private int authorize_count;
    private double authorize_total;
    private int capture_count;
    private double capture_total;
    private int refund_capture_count;
    private double refund_capture_total;

    public int getVoid_transaction_count() { return void_transaction_count; }

    public void setVoid_transaction_count(int void_transaction_count) { this.void_transaction_count = void_transaction_count; }

    public double getVoid_transaction_total() { return void_transaction_total; }

    public void setVoid_transaction_total(double void_transaction_total) { this.void_transaction_total = void_transaction_total; }

    public String getTransaction_type() { return transaction_type; }

    public void setTransaction_type(String transaction_type) { this.transaction_type = transaction_type; }

    public Date getReport_date() {
        return report_date;
    }

    public void setReport_date(Date report_date) {
        this.report_date = report_date;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public int getTransaction_count() {
        return transaction_count;
    }

    public void setTransaction_count(int transaction_count) {
        this.transaction_count = transaction_count;
    }

    public double getTransaction_total() {
        return transaction_total;
    }

    public void setTransaction_total(double transaction_total) {
        this.transaction_total = transaction_total;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public int getRefund_count() {
        return refund_count;
    }

    public void setRefund_count(int refund_count) {
        this.refund_count = refund_count;
    }

    public double getRefund_total() {
        return refund_total;
    }

    public void setRefund_total(double refund_total) {
        this.refund_total = refund_total;
    }

    public int getAuthorize_count() {
        return authorize_count;
    }

    public void setAuthorize_count(int authorize_count) {
        this.authorize_count = authorize_count;
    }

    public double getAuthorize_total() {
        return authorize_total;
    }

    public void setAuthorize_total(double authorize_total) {
        this.authorize_total = authorize_total;
    }

    public int getCapture_count() {
        return capture_count;
    }

    public void setCapture_count(int capture_count) {
        this.capture_count = capture_count;
    }

    public double getCapture_total() {
        return capture_total;
    }

    public void setCapture_total(double capture_total) {
        this.capture_total = capture_total;
    }

    public int getRefund_capture_count() {
        return refund_capture_count;
    }

    public void setRefund_capture_count(int refund_capture_count) {
        this.refund_capture_count = refund_capture_count;
    }

    public double getRefund_capture_total() {
        return refund_capture_total;
    }

    public void setRefund_capture_total(double refund_capture_total) {
        this.refund_capture_total = refund_capture_total;
    }
}
