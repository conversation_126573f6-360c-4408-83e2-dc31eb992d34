package com.onepay.ma.service.models.pay_out;


import com.opencsv.bean.CsvBindByName;

import java.io.Serializable;

public class FundsTransBatchReq implements Serializable {
    @CsvBindByName
    private String type;

    @CsvBindByName
    private String swiftCode;

    @CsvBindByName
    private String accountNumber;

    @CsvBindByName
    private String cardNumber;

    @CsvBindByName
    private String merchantId;

    @CsvBindByName
    private String merchantAccount;

    @CsvBindByName
    private String accountName;

    @CsvBindByName
    private Long amount;

    @CsvBindByName
    private String currency;

    @CsvBindByName
    private String fundsTransferInfo;

    @CsvBindByName
    private String fundsTransferId;

    @CsvBindByName
    private String accountId;

    @CsvBindByName
    private String remark;

    private String state;

    private String reason;

    private String batchId;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getFundsTransferInfo() {
        return fundsTransferInfo;
    }

    public void setFundsTransferInfo(String fundsTransferInfo) {
        this.fundsTransferInfo = fundsTransferInfo;
    }

    public String getFundsTransferId() {
        return fundsTransferId;
    }

    public void setFundsTransferId(String fundsTransferId) {
        this.fundsTransferId = fundsTransferId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}
