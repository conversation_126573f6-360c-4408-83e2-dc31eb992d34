package com.onepay.ma.service.models.mpay;

import java.io.Serializable;

public class SamsungMpayTransactionQuery  implements Serializable {

    String merchantId;
    String orderInfo;
    String instrumentNumber;
    String transactionId;
    String merchantTransactionRef;
    String currency;
    String status;
    String bankTransactionId;
    String customerTransactionId;
    private String keywords;
    private Integer page;
    private String fromDate;
    private String toDate;
    private Integer pageSize;
    private String terminalId;
    private String bankId;
    private String merchantName;
    private String acqCode;
    private Integer offset;
    private String version;

    private String masking;
    private String appName;
    private String qrId;
    private String invoiceId;
    private String clientId;

    //SAMSUNG
    private String customerEmail;
    private String customerPhone;
    private String merchantWebsite;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getQrId() {
        return qrId;
    }

    public void setQrId(String qrId) {
        this.qrId = qrId;
    }

    public String getMasking() {
        return masking;
    }

    public void setMasking(String masking) {
        this.masking = masking;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getAcqCode() {
        return acqCode;
    }

    public void setAcqCode(String acqCode) {
        this.acqCode = acqCode;
    }

    /**
     * Getter for property 'customer_transaction_id'.
     *
     * @return Value for property 'customer_transaction_id'.
     */
    public String getCustomerTransactionId() {
        return customerTransactionId;
    }

    /**
     * Setter for property 'customer_transaction_id'.
     *
     * @param customerTransactionId Value to set for property 'customer_transaction_id'.
     */
    public void setCustomerTransactionId(String customerTransactionId) {
        this.customerTransactionId = customerTransactionId;
    }

    /**
     * Getter for property 'bank_transaction_id'.
     *
     * @return Value for property 'bank_transaction_id'.
     */
    public String getBankTransactionId() {
        return bankTransactionId;
    }

    /**
     * Setter for property 'bank_transaction_id'.
     *
     * @param bankTransactionId Value to set for property 'bank_transaction_id'.
     */
    public void setBankTransactionId(String bankTransactionId) {
        this.bankTransactionId = bankTransactionId;
    }

    /**
     * Getter for property 'merchant_transaction_ref'.
     *
     * @return Value for property 'merchant_transaction_ref'.
     */
    public String getMerchantTransactionRef() {
        return merchantTransactionRef;
    }

    /**
     * Setter for property 'merchant_transaction_ref'.
     *
     * @param merchantTransactionRef Value to set for property 'merchant_transaction_ref'.
     */
    public void setMerchantTransactionRef(String merchantTransactionRef) {
        this.merchantTransactionRef = merchantTransactionRef;
    }

    /**
     * Getter for property 'acquirer_id'.
     *
     * @return Value for property 'acquirer_id'.
     */
    public String getBankId() {
        return bankId;
    }

    /**
     * Setter for property 'acquirer_id'.
     *
     * @param bankId Value to set for property 'acquirer_id'.
     */
    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    /**
     * Getter for property 'terminal_id'.
     *
     * @return Value for property 'terminal_id'.
     */
    public String getTerminalId() {
        return terminalId;
    }

    /**
     * Setter for property 'terminal_id'.
     *
     * @param terminalId Value to set for property 'terminal_id'.
     */
    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }



    /**
     * Getter for property 'merchant_id'.
     *
     * @return Value for property 'merchant_id'.
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Setter for property 'merchant_id'.
     *
     * @param merchantId Value to set for property 'merchant_id'.
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * Getter for property 'status'.
     *
     * @return Value for property 'status'.
     */
    public String getStatus() {
        return status;
    }

    /**
     * Setter for property 'status'.
     *
     * @param status Value to set for property 'status'.
     */
    public void setStatus(String status) {
        this.status = status;
    }


    /**
     * Getter for property 'keywords'.
     *
     * @return Value for property 'keywords'.
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * Setter for property 'keywords'.
     *
     * @param keywords Value to set for property 'keywords'.
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    /**
     * Getter for property 'page'.
     *
     * @return Value for property 'page'.
     */
    public Integer getPage() {
        return page;
    }

    /**
     * Setter for property 'page'.
     *
     * @param page Value to set for property 'page'.
     */
    public void setPage(Integer page) {
        this.page = page;
    }

    /**
     * Getter for property 'from_date'.
     *
     * @return Value for property 'from_date'.
     */
    public String getFromDate() {
        return fromDate;
    }

    /**
     * Setter for property 'from_date'.
     *
     * @param fromDate Value to set for property 'from_date'.
     */
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * Getter for property 'to_date'.
     *
     * @return Value for property 'to_date'.
     */
    public String getToDate() {
        return toDate;
    }

    /**
     * Setter for property 'to_date'.
     *
     * @param toDate Value to set for property 'to_date'.
     */
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    /**
     * Getter for property 'pageSize'.
     *
     * @return Value for property 'pageSize'.
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * Setter for property 'pageSize'.
     *
     * @param pageSize Value to set for property 'pageSize'.
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * Getter for property 'order_info'.
     *
     * @return Value for property 'order_info'.
     */
    public String getOrderInfo() {
        return orderInfo;
    }

    /**
     * Setter for property 'order_info'.
     *
     * @param orderInfo Value to set for property 'order_info'.
     */
    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    /**
     * Getter for property 'instrument_number'.
     *
     * @return Value for property 'instrument_number'.
     */
    public String getInstrumentNumber() {
        return instrumentNumber;
    }

    /**
     * Setter for property 'instrument_number'.
     *
     * @param instrumentNumber Value to set for property 'instrument_number'.
     */
    public void setInstrumentNumber(String instrumentNumber) {
        this.instrumentNumber = instrumentNumber;
    }

    /**
     * Getter for property 'transaction_id'.
     *
     * @return Value for property 'transaction_id'.
     */
    public String getTransactionId() {
        return transactionId;
    }

    /**
     * Setter for property 'transaction_id'.
     *
     * @param transactionId Value to set for property 'transaction_id'.
     */
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * Getter for property 'currency'.
     *
     * @return Value for property 'currency'.
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * Setter for property 'currency'.
     *
     * @param currency Value to set for property 'currency'.
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getMerchantWebsite() {
        return merchantWebsite;
    }

    public void setMerchantWebsite(String merchantWebsite) {
        this.merchantWebsite = merchantWebsite;
    }
}
