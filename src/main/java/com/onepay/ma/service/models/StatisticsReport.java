package com.onepay.ma.service.models;

import java.util.Date;

/**
 * Created by tuydv on 12/6/18.
 */
public class StatisticsReport {
    String transDate;
    String cardType;
    String acqCode;
    Double amountIss;
    Double amountAcq;
    Double issFee;
    Double acqFee;
    Double amountRecon;
    Double feeRecon;
    String transType;
    String settlementDate;
    Integer countIss;
    Integer countAcq;

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getAcqCode() {
        return acqCode;
    }

    public void setAcqCode(String acqCode) {
        this.acqCode = acqCode;
    }

    public Double getAmountIss() {
        return amountIss;
    }

    public void setAmountIss(Double amountIss) {
        this.amountIss = amountIss;
    }

    public Double getAmountAcq() {
        return amountAcq;
    }

    public void setAmountAcq(Double amountAcq) {
        this.amountAcq = amountAcq;
    }

    public Double getIssFee() {
        return issFee;
    }

    public void setIssFee(Double issFee) {
        this.issFee = issFee;
    }

    public Double getAcqFee() {
        return acqFee;
    }

    public void setAcqFee(Double acqFee) {
        this.acqFee = acqFee;
    }

    public Double getAmountRecon() {
        return amountRecon;
    }

    public void setAmountRecon(Double amountRecon) {
        this.amountRecon = amountRecon;
    }

    public Double getFeeRecon() {
        return feeRecon;
    }

    public void setFeeRecon(Double feeRecon) {
        this.feeRecon = feeRecon;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    public Integer getCountIss() {
        return countIss;
    }

    public void setCountIss(Integer countIss) {
        this.countIss = countIss;
    }

    public Integer getCountAcq() {
        return countAcq;
    }

    public void setCountAcq(Integer countAcq) {
        this.countAcq = countAcq;
    }
}
