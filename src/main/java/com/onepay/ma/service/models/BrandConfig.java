package com.onepay.ma.service.models;

import java.sql.Timestamp;

public class BrandConfig {
    private String branchId;
    private String state;
    private Timestamp create;
    private Timestamp update;
    private String data;
    private String desc;

    /**
     * @return String return the branchId
     */
    public String getBranchId() {
        return branchId;
    }

    /**
     * @param branchId the branchId to set
     */
    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    /**
     * @return String return the state
     */
    public String getState() {
        return state;
    }

    /**
     * @param state the state to set
     */
    public void setState(String state) {
        this.state = state;
    }

    /**
     * @return String return the create
     */
    public Timestamp getCreate() {
        return create;
    }

    /**
     * @param create the create to set
     */
    public void setCreate(Timestamp create) {
        this.create = create;
    }

    /**
     * @return String return the update
     */
    public Timestamp getUpdate() {
        return update;
    }

    /**
     * @param update the update to set
     */
    public void setUpdate(Timestamp update) {
        this.update = update;
    }

    /**
     * @return String return the data
     */
    public String getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(String data) {
        this.data = data;
    }

    /**
     * @return String return the desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * @param desc the desc to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

}
