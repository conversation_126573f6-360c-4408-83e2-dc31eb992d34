package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;


public class BatchDetailQueryDto implements Serializable {
    private String batchId;
    private String bankSender;
    private String holderName;
    private String state;
    private Integer page;
    private Integer pageSize;

    public BatchDetailQueryDto(String batchId, String bankSender, String holderName, String state) {
        this.batchId = batchId;
        this.bankSender = bankSender;
        this.holderName = holderName;
        this.state = state;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBankSender() {
        return bankSender;
    }

    public void setBankSender(String bankSender) {
        this.bankSender = bankSender;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
