package com.onepay.ma.service.models;

import com.onepay.ma.service.models.partner.Partner;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class UserData {
    private int n_id;
    private String s_id;
    private String name;
    private String email;
    private String phone;
    private String address;
    private String state;
    private String city;
    private String status;
    private Integer authLevel = 1;

    private String description;
    private Timestamp create_date;
    private Timestamp update_date;
    private Timestamp last_login_date;
    private List<RoleData> roles;
    private List<Merchant> merchants;
    private List<Partner> partners;
    private List<Permission> permissions;
    private String createId;
    private String createName;
    private String createEmail;
    private String jobTitle;
    private String pass;

    private String am_first_name;
    private String am_last_name;
    private String country_code;

    public Timestamp getLast_login_date() { return last_login_date; }

    public Integer getAuthLevel() {
      return authLevel;
    }

    public void setAuthLevel(Integer authLevel) {
      this.authLevel = authLevel;
    }

    public void setLast_login_date(Timestamp last_login_date) { this.last_login_date = last_login_date; }

    public List<Permission> getPermissions() { return permissions; }

    public void setPermissions(List<Permission> permissions) { this.permissions = permissions; }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getAm_first_name() {
        return am_first_name;
    }

    public void setAm_first_name(String am_first_name) {
        this.am_first_name = am_first_name;
    }

    public String getAm_last_name() {
        return am_last_name;
    }

    public void setAm_last_name(String am_last_name) {
        this.am_last_name = am_last_name;
    }

    public List<Merchant> getMerchants() {
        return merchants;
    }

    public void setMerchants(List<Merchant> merchants) {
        this.merchants = merchants;
    }

    public List<Partner> getPartners() { return partners; }

    public void setPartners(List<Partner> partners) { this.partners = partners; }

    public int getN_id() {
        return n_id;
    }

    public void setN_id(int n_id) {
        this.n_id = n_id;
    }

    public String getS_id() {
        return s_id;
    }

    public void setS_id(String s_id) {
        this.s_id = s_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Timestamp getUpdate_date() {
        return update_date;
    }

    public void setUpdate_date(Timestamp update_date) {
        this.update_date = update_date;
    }

    public List<RoleData> getRoles() {
        return roles;
    }

    public void setRoles(List<RoleData> roles) {
        this.roles = roles;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() { return createName; }

    public void setCreateName(String createName) { this.createName = createName; }

    public String getCreateEmail() { return createEmail; }

    public void setCreateEmail(String createEmail) { this.createEmail = createEmail; }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }
}
