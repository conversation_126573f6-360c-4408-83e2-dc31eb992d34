package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by anhkh on 04-Jan-17.
 */
public class PromotionCode {

    private Integer id;
    private String coupon_code;
    private Integer transaction_id;
    private String trans_ref;
    private String order_ref;
    private String customer_phone;
    private String customer_name;
    private String customer_email;
    private String sms_state;
    private String coupon_state;
    private Timestamp send_sms_date;
    private Timestamp used_date;
    private String merchant_id;


    /**
     * Getter for property 'id'.
     *
     * @return Value for property 'id'.
     */
    public Integer getId() {
        return id;
    }

    /**
     * Setter for property 'id'.
     *
     * @param id Value to set for property 'id'.
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * Getter for property 'coupon_code'.
     *
     * @return Value for property 'coupon_code'.
     */
    public String getCoupon_code() {
        return coupon_code;
    }

    /**
     * Setter for property 'coupon_code'.
     *
     * @param coupon_code Value to set for property 'coupon_code'.
     */
    public void setCoupon_code(String coupon_code) {
        this.coupon_code = coupon_code;
    }

    /**
     * Getter for property 'transaction_id'.
     *
     * @return Value for property 'transaction_id'.
     */
    public Integer getTransaction_id() {
        return transaction_id;
    }

    /**
     * Setter for property 'transaction_id'.
     *
     * @param transaction_id Value to set for property 'transaction_id'.
     */
    public void setTransaction_id(Integer transaction_id) {
        this.transaction_id = transaction_id;
    }

    /**
     * Getter for property 'trans_ref'.
     *
     * @return Value for property 'trans_ref'.
     */
    public String getTrans_ref() {
        return trans_ref;
    }

    /**
     * Setter for property 'trans_ref'.
     *
     * @param trans_ref Value to set for property 'trans_ref'.
     */
    public void setTrans_ref(String trans_ref) {
        this.trans_ref = trans_ref;
    }

    /**
     * Getter for property 'order_ref'.
     *
     * @return Value for property 'order_ref'.
     */
    public String getOrder_ref() {
        return order_ref;
    }

    /**
     * Setter for property 'order_ref'.
     *
     * @param order_ref Value to set for property 'order_ref'.
     */
    public void setOrder_ref(String order_ref) {
        this.order_ref = order_ref;
    }

    /**
     * Getter for property 'customer_phone'.
     *
     * @return Value for property 'customer_phone'.
     */
    public String getCustomer_phone() {
        return customer_phone;
    }

    /**
     * Setter for property 'customer_phone'.
     *
     * @param customer_phone Value to set for property 'customer_phone'.
     */
    public void setCustomer_phone(String customer_phone) {
        this.customer_phone = customer_phone;
    }

    /**
     * Getter for property 'customer_name'.
     *
     * @return Value for property 'customer_name'.
     */
    public String getCustomer_name() {
        return customer_name;
    }

    /**
     * Setter for property 'customer_name'.
     *
     * @param customer_name Value to set for property 'customer_name'.
     */
    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    /**
     * Getter for property 'customer_email'.
     *
     * @return Value for property 'customer_email'.
     */
    public String getCustomer_email() {
        return customer_email;
    }

    /**
     * Setter for property 'customer_email'.
     *
     * @param customer_email Value to set for property 'customer_email'.
     */
    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    /**
     * Getter for property 'sms_state'.
     *
     * @return Value for property 'sms_state'.
     */
    public String getSms_state() {
        return sms_state;
    }

    /**
     * Setter for property 'sms_state'.
     *
     * @param sms_state Value to set for property 'sms_state'.
     */
    public void setSms_state(String sms_state) {
        this.sms_state = sms_state;
    }

    /**
     * Getter for property 'coupon_state'.
     *
     * @return Value for property 'coupon_state'.
     */
    public String getCoupon_state() {
        return coupon_state;
    }

    /**
     * Setter for property 'coupon_state'.
     *
     * @param coupon_state Value to set for property 'coupon_state'.
     */
    public void setCoupon_state(String coupon_state) {
        this.coupon_state = coupon_state;
    }


    /**
     * Getter for property 'send_sms_date'.
     *
     * @return Value for property 'send_sms_date'.
     */
    public Timestamp getSend_sms_date() {
        return send_sms_date;
    }

    /**
     * Setter for property 'send_sms_date'.
     *
     * @param send_sms_date Value to set for property 'send_sms_date'.
     */
    public void setSend_sms_date(Timestamp send_sms_date) {
        this.send_sms_date = send_sms_date;
    }

    /**
     * Getter for property 'used_date'.
     *
     * @return Value for property 'used_date'.
     */
    public Timestamp getUsed_date() {
        return used_date;
    }

    /**
     * Setter for property 'used_date'.
     *
     * @param used_date Value to set for property 'used_date'.
     */
    public void setUsed_date(Timestamp used_date) {
        this.used_date = used_date;
    }

    /**
     * Getter for property 'merchant_id'.
     *
     * @return Value for property 'merchant_id'.
     */
    public String getMerchant_id() {
        return merchant_id;
    }

    /**
     * Setter for property 'merchant_id'.
     *
     * @param merchant_id Value to set for property 'merchant_id'.
     */
    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }
}
