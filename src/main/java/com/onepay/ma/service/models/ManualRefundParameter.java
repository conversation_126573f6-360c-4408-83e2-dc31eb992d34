package com.onepay.ma.service.models;

public class ManualRefundParameter {
    private String id;
    private String paymentId;
    private String merchantId;
    private Double amount;
    private String currency;
    private String clientId;
    private String clientRef;
    private String clientMerchantId;
    private String serverId;
    private String serverRef;
    private String serverMerchantId;
    private String description;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientRef() {
        return clientRef;
    }

    public void setClientRef(String clientRef) {
        this.clientRef = clientRef;
    }

    public String getClientMerchantId() {
        return clientMerchantId;
    }

    public void setClientMerchantId(String clientMerchantId) {
        this.clientMerchantId = clientMerchantId;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getServerRef() {
        return serverRef;
    }

    public void setServerRef(String serverRef) {
        this.serverRef = serverRef;
    }

    public String getServerMerchantId() {
        return serverMerchantId;
    }

    public void setServerMerchantId(String serverMerchantId) {
        this.serverMerchantId = serverMerchantId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
