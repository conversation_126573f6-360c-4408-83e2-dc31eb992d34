package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public class DomesticTransaction {
    private int row_num;
    private int transaction_id;
    private String transaction_type;
    private Acquirer acquirer;
    private String merchant_id;
    private DomesticCard card;
    private String merchant_transaction_ref;
    private String msp_merchant_transaction_ref;
    private Timestamp transaction_time;
    private Timestamp auth_time;
    private int auth_code;
    private String order_info;
    private DomesticAmount amount;
    private String status;
    private String refund_status;
    private String transaction_info;
    private String ip_address;
    private String operator_id;
    private String advance_status;
    private Double wait_for_approval_amount;
    private Integer original_id;
    // SAMSUNG
    private String customer_name;
    private String customer_mobile;
    private String customer_email;
    private String fraud_check;
    private String epp;

    // QUICKLINK
    private String customer_address;

    public String getCustomer_address() {
        return customer_address;
    }

    public void setCustomer_address(String customer_address) {
        this.customer_address = customer_address;
    }

    public Double getWait_for_approval_amount() {
        return wait_for_approval_amount;
    }

    public void setWait_for_approval_amount(Double wait_for_approval_amount) {
        this.wait_for_approval_amount = wait_for_approval_amount;
    }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public Timestamp getAuth_time() {
        return auth_time;
    }

    public void setAuth_time(Timestamp auth_time) {
        this.auth_time = auth_time;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getEpp() {
        return epp;
    }

    public void setEpp(String epp) {
        this.epp = epp;
    }

    /**
     * Getter for property 'advance_status'.
     *
     * @return Value for property 'advance_status'.
     */
    public String getAdvance_status() {
        return advance_status;
    }

    /**
     * Setter for property 'advance_status'.
     *
     * @param advance_status Value to set for property 'advance_status'.
     */
    public void setAdvance_status(String advance_status) {
        this.advance_status = advance_status;
    }

    public int getRow_num() {
        return row_num;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public DomesticCard getCard() {
        return card;
    }

    public void setCard(DomesticCard card) {
        this.card = card;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public DomesticAmount getAmount() {
        return amount;
    }

    public void setAmount(DomesticAmount amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(String refund_status) {
        this.refund_status = refund_status;
    }

    public String getTransaction_info() {
        return transaction_info;
    }

    public void setTransaction_info(String transaction_info) {
        this.transaction_info = transaction_info;
    }

    public String getIp_address() {
        return ip_address;
    }

    public void setIp_address(String ip_address) {
        this.ip_address = ip_address;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }


    /**
     * @return Integer return the original_id
     */
    public Integer getOriginal_id() {
        return original_id;
    }

    /**
     * @param original_id the original_id to set
     */
    public void setOriginal_id(Integer original_id) {
        this.original_id = original_id;
    }


    /**
     * @return String return the auth_code
     */
    public int getAuth_code() {
        return auth_code;
    }

    /**
     * @param auth_code the auth_code to set
     */
    public void setAuth_code(int auth_code) {
        this.auth_code = auth_code;
    }
    public String getMsp_merchant_transaction_ref() {
        return msp_merchant_transaction_ref;
    }

    public void setMsp_merchant_transaction_ref(String msp_merchant_transaction_ref) {
        this.msp_merchant_transaction_ref = msp_merchant_transaction_ref;
    }

}
