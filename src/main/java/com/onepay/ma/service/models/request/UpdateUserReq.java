package com.onepay.ma.service.models.request;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.RoleData;
import java.util.List;

public class UpdateUserReq {
    private String sId;
    private String name;
    private String jobTitle;
    private String status;
    private List<RoleData> roles;
    private List<Merchant> merchants;
    
    public String getsId() {
        return sId;
    }
    public void setsId(String sId) {
        this.sId = sId;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getJobTitle() {
        return jobTitle;
    }
    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public List<RoleData> getRoles() {
        return roles;
    }
    public void setRoles(List<RoleData> roles) {
        this.roles = roles;
    }
    public List<Merchant> getMerchants() {
        return merchants;
    }
    public void setMerchants(List<Merchant> merchants) {
        this.merchants = merchants;
    }
    

}
