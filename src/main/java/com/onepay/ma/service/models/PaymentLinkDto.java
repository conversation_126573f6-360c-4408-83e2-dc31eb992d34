
package com.onepay.ma.service.models;

import java.io.Serializable;

public class PaymentLinkDto implements Serializable {
    private String fromDate;
    private String toDate;
    private int page;
    private String keyword;
    private String state;
    private String paynowMerchantId;
    private String installmentMerchantId;
    private String bnplMerchantId;
    private String email;
    private String pageSize;
    private String merchantProfileId;
    private String merchantIds;
    private String merchantBNPLIds;
    private String merchantAppleIds;
    private String lang;

    public String getFromDate() {
        return fromDate;
    }

    public String getMerchantProfileId() {
        return merchantProfileId;
    }

    public void setMerchantProfileId(String merchantProfileId) {
        this.merchantProfileId = merchantProfileId;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getInstallmentMerchantId() {
        return installmentMerchantId;
    }

    public void setInstallmentMerchantId(String installmentMerchantId) {
        this.installmentMerchantId = installmentMerchantId;
    }

    public String getPaynowMerchantId() {
        return paynowMerchantId;
    }

    public void setPaynowMerchantId(String paynowMerchantId) {
        this.paynowMerchantId = paynowMerchantId;
    }

    public String getBnplMerchantId() {
        return bnplMerchantId;
    }

    public void setBnplMerchantId(String bnplMerchantId) {
        this.bnplMerchantId = bnplMerchantId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(String merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getMerchantBNPLIds() {
        return merchantBNPLIds;
    }

    public void setMerchantBNPLIds(String merchantBNPLIds) {
        this.merchantBNPLIds = merchantBNPLIds;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    /**
     * @return String return the merchantAppleIds
     */
    public String getMerchantAppleIds() {
        return merchantAppleIds;
    }

    /**
     * @param merchantAppleIds the merchantAppleIds to set
     */
    public void setMerchantAppleIds(String merchantAppleIds) {
        this.merchantAppleIds = merchantAppleIds;
    }

}
