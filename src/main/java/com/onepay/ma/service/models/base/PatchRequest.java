package com.onepay.ma.service.models.base;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/3/16.
 */
public class PatchRequest<T> {
    private String path;
    private String op;
    private T value;
    private Boolean skipCallSynchronize;
    private String service;
    private String id;
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }

    public Boolean isSkipCallSynchronize() {
        return this.skipCallSynchronize;
    }

    public void setSkipCallSynchronize(Boolean skipCallSynchronize) {
        this.skipCallSynchronize = skipCallSynchronize;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public T getValue() {
        return value;
    }

    public void setValue(T value) {
        this.value = value;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }
}
