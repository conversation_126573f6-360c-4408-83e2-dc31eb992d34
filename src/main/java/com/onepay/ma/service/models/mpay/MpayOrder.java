package com.onepay.ma.service.models.mpay;

import com.onepay.ma.service.models.Amount;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by anhkh on 07-Aug-17.
 */
public class MpayOrder {
    private Integer row_num;
    private String order_id;
    private String merchant_id;
    private String merchant_name;
    private String onecom_merchant_id;
    private Timestamp create_time;
    private Timestamp update_time;
    private Timestamp expire_time;
    private String status;
    private String terminal_id;
    private String order_info;
    private String merchant_order_ref;
    private Amount amount;

    private String qrData;
    private List<MpayTransaction> mpayTransactions;

    /**
     * Getter for property 'merchant_name'.
     *
     * @return Value for property 'merchant_name'.
     */
    public String getMerchant_name() {
        return merchant_name;
    }

    /**
     * Setter for property 'merchant_name'.
     *
     * @param merchant_name Value to set for property 'merchant_name'.
     */
    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    /**
     * Getter for property 'mpayTransactions'.
     *
     * @return Value for property 'mpayTransactions'.
     */
    public List<MpayTransaction> getMpayTransactions() {
        return mpayTransactions;
    }

    /**
     * Setter for property 'mpayTransactions'.
     *
     * @param mpayTransactions Value to set for property 'mpayTransactions'.
     */
    public void setMpayTransactions(List<MpayTransaction> mpayTransactions) {
        this.mpayTransactions = mpayTransactions;
    }

    public Integer getRow_num() {
        return row_num;
    }

    public void setRow_num(Integer row_num) {
        this.row_num = row_num;
    }

    public String getQrData() {
        return qrData;
    }

    public void setQrData(String qrData) {
        this.qrData = qrData;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getOnecom_merchant_id() {
        return onecom_merchant_id;
    }

    public void setOnecom_merchant_id(String onecom_merchant_id) {
        this.onecom_merchant_id = onecom_merchant_id;
    }

    public Timestamp getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Timestamp create_time) {
        this.create_time = create_time;
    }

    public Timestamp getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Timestamp update_time) {
        this.update_time = update_time;
    }

    public Timestamp getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(Timestamp expire_time) {
        this.expire_time = expire_time;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTerminal_id() {
        return terminal_id;
    }

    public void setTerminal_id(String terminal_id) {
        this.terminal_id = terminal_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getMerchant_order_ref() {
        return merchant_order_ref;
    }

    public void setMerchant_order_ref(String merchant_order_ref) {
        this.merchant_order_ref = merchant_order_ref;
    }
}
