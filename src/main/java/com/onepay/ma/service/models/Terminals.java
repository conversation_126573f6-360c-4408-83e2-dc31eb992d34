package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by anhkh on 7/12/2016.
 */
public class Terminals {

    private Integer total_items;

    private List<Terminal> terminals;

    /**
     * Getter for property 'total_items'.
     *
     * @return Value for property 'total_items'.
     */
    public Integer getTotal_items() {
        return total_items;
    }

    /**
     * Setter for property 'total_items'.
     *
     * @param total_items Value to set for property 'total_items'.
     */
    public void setTotal_items(Integer total_items) {
        this.total_items = total_items;
    }

    /**
     * Getter for property 'terminals'.
     *
     * @return Value for property 'terminals'.
     */
    public List<Terminal> getTerminals() {
        return terminals;
    }

    /**
     * Setter for property 'terminals'.
     *
     * @param terminals Value to set for property 'terminals'.
     */
    public void setTerminals(List<Terminal> terminals) {
        this.terminals = terminals;
    }
}
