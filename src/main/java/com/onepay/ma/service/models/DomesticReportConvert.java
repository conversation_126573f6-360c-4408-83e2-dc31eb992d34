package com.onepay.ma.service.models;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class DomesticReportConvert {
    private Date transaction_date;
    private String acquirer_name;
    private String merchant_id;
    private String acquirer_id;
    private int transaction_count;
    private int refund_count;
    private double transaction_total;
    private double refund_total;

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public Date getTransaction_date() {
        return transaction_date;
    }

    public void setTransaction_date(Date transaction_date) {
        this.transaction_date = transaction_date;
    }

    public String getAcquirer_name() {
        return acquirer_name;
    }

    public void setAcquirer_name(String acquirer_name) {
        this.acquirer_name = acquirer_name;
    }

    public String getAcquirer_id() {
        return acquirer_id;
    }

    public void setAcquirer_id(String acquirer_id) {
        this.acquirer_id = acquirer_id;
    }

    public int getTransaction_count() {
        return transaction_count;
    }

    public void setTransaction_count(int transaction_count) {
        this.transaction_count = transaction_count;
    }

    public int getRefund_count() {
        return refund_count;
    }

    public void setRefund_count(int refund_count) {
        this.refund_count = refund_count;
    }

    public double getTransaction_total() {
        return transaction_total;
    }

    public void setTransaction_total(double transaction_total) {
        this.transaction_total = transaction_total;
    }

    public double getRefund_total() {
        return refund_total;
    }

    public void setRefund_total(double refund_total) {
        this.refund_total = refund_total;
    }
}
