package com.onepay.ma.service.models.reconciliation;

import com.onepay.ma.service.models.InternationalReport;

import java.util.List;

public class ReconciliationReportConvets {
    private List<Reconciliation> items;
    private String currency;
    private int total_transaction_count;
    private int total_purchase_count;
    private int total_refund_count;
    private int total_void_count;
    private int total_capture_count;
    private int total_authorise_count;
    private double total_purchase_total;
    private double total_refund_total;
    private double total_void_total;
    private double total_authorise_total;
    private double total_capture_total;

    public double getTotal_void_total() {
        return total_void_total;
    }

    public void setTotal_void_total(double total_void_total) {
        this.total_void_total = total_void_total;
    }

    public List<Reconciliation> getItems() {
        return items;
    }

    public void setItems(List<Reconciliation> items) {
        this.items = items;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public int getTotal_transaction_count() {
        return total_transaction_count;
    }

    public void setTotal_transaction_count(int total_transaction_count) {
        this.total_transaction_count = total_transaction_count;
    }

    public int getTotal_refund_count() {
        return total_refund_count;
    }

    public void setTotal_refund_count(int total_refund_count) {
        this.total_refund_count = total_refund_count;
    }

    public int getTotal_void_count() {
        return total_void_count;
    }

    public void setTotal_void_count(int total_void_count) {
        this.total_void_count = total_void_count;
    }

    public int getTotal_capture_count() {
        return total_capture_count;
    }

    public void setTotal_capture_count(int total_capture_count) {
        this.total_capture_count = total_capture_count;
    }

    public int getTotal_authorise_count() {
        return total_authorise_count;
    }

    public void setTotal_authorise_count(int total_authorise_count) {
        this.total_authorise_count = total_authorise_count;
    }

    public int getTotal_purchase_count() {
        return total_purchase_count;
    }

    public void setTotal_purchase_count(int total_purchase_count) {
        this.total_purchase_count = total_purchase_count;
    }

    public double getTotal_purchase_total() {
        return total_purchase_total;
    }

    public void setTotal_purchase_total(double total_purchase_total) {
        this.total_purchase_total = total_purchase_total;
    }

    public double getTotal_authorise_total() {
        return total_authorise_total;
    }

    public void setTotal_authorise_total(double total_authorise_total) {
        this.total_authorise_total = total_authorise_total;
    }

    public double getTotal_capture_total() {
        return total_capture_total;
    }

    public void setTotal_capture_total(double total_capture_total) {
        this.total_capture_total = total_capture_total;
    }

    public double getTotal_refund_total() {
        return total_refund_total;
    }

    public void setTotal_refund_total(double total_refund_total) {
        this.total_refund_total = total_refund_total;
    }
}
