package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/23/16.
 */
public class Terminal {
    private String terminal_id;
    private String terminal_name;
    private String description;
    private Address address;
    private String status;
    private Timestamp create_date;
    private Timestamp update_date;
    private String merchant_id;

    /**
     * Getter for property 'merchant_id'.
     *
     * @return Value for property 'merchant_id'.
     */
    public String getMerchant_id() {
        return merchant_id;
    }

    /**
     * Setter for property 'merchant_id'.
     *
     * @param merchant_id Value to set for property 'merchant_id'.
     */
    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getTerminal_id() {
        return terminal_id;
    }

    public void setTerminal_id(String terminal_id) {
        this.terminal_id = terminal_id;
    }

    public String getTerminal_name() {
        return terminal_name;
    }

    public void setTerminal_name(String terminal_name) {
        this.terminal_name = terminal_name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Timestamp getUpdate_date() {
        return update_date;
    }

    public void setUpdate_date(Timestamp update_date) {
        this.update_date = update_date;
    }
}
