package com.onepay.ma.service.models.mpayPromotion;

import com.onepay.ma.service.models.PromotionAddress;
import com.onepay.ma.service.models.PromotionAmount;
import com.onepay.ma.service.models.PromotionCard;

import java.sql.Timestamp;

/**
 * Created by anhkh on 25-Jan-18.
 */
public class MpayPromotionTransaction {
    private Timestamp transaction_time;
    private PromotionAmount amount;
    private String transaction_id;
    private String ref_id;
    private PromotionCard card;
    private String merchant_id;
    private String order_info;
    private String promotion_name;
    private String status;
    private String transaction_reference;
    private String promotion_id;
    private String invoice_id;
    private String mobile;
    private String card_type;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getInvoice_id() {
        return invoice_id;
    }

    public void setInvoice_id(String invoice_id) {
        this.invoice_id = invoice_id;
    }

    public String getRef_id() {
        return ref_id;
    }

    public void setRef_id(String ref_id) {
        this.ref_id = ref_id;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public PromotionAmount getAmount() {
        return amount;
    }

    public void setAmount(PromotionAmount amount) {
        this.amount = amount;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public PromotionCard getCard() {
        return card;
    }

    public void setCard(PromotionCard card) {
        this.card = card;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }


    public String getPromotion_name() {
        return promotion_name;
    }

    public void setPromotion_name(String promotion_name) {
        this.promotion_name = promotion_name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTransaction_reference() {
        return transaction_reference;
    }

    public void setTransaction_reference(String transaction_reference) {
        this.transaction_reference = transaction_reference;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }
}
