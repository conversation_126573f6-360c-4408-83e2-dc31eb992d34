package com.onepay.ma.service.models.pay_collect;

import java.io.Serializable;

public class TransactionSearchReq implements Serializable {
    private String fromDate;
    private String toDate;
    private String transactionId;
    private String virtualAccId;
    private String bankTxnRef;
    private String senderSwiftCode;
    private String receiptSwiftCode;
    private String state;
    private int page;
    private int pageSize;
    private String accountName;
    private String reference;
    private String merchantId;
    private String lang;

    public TransactionSearchReq() {
    }

    public TransactionSearchReq(String fromDate, String toDate, String transactionId, String virtualAccId,
            String bankTxnRef, String senderSwiftCode, String receiptSwiftCode, String state, int page, int pageSize,
            String accountName, String reference, String merchantId, String lang) {
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.transactionId = transactionId;
        this.virtualAccId = virtualAccId;
        this.bankTxnRef = bankTxnRef;
        this.senderSwiftCode = senderSwiftCode;
        this.receiptSwiftCode = receiptSwiftCode;
        this.state = state;
        this.page = page;
        this.pageSize = pageSize;
        this.accountName = accountName;
        this.reference = reference;
        this.merchantId = merchantId;
        this.lang = lang;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getVirtualAccId() {
        return virtualAccId;
    }

    public void setVirtualAccId(String virtualAccId) {
        this.virtualAccId = virtualAccId;
    }

    public String getBankTxnRef() {
        return bankTxnRef;
    }

    public void setBankTxnRef(String bankTxnRef) {
        this.bankTxnRef = bankTxnRef;
    }

    public String getSenderSwiftCode() {
        return senderSwiftCode;
    }

    public void setSenderSwiftCode(String senderSwiftCode) {
        this.senderSwiftCode = senderSwiftCode;
    }

    public String getReceiptSwiftCode() {
        return receiptSwiftCode;
    }

    public void setReceiptSwiftCode(String receiptSwiftCode) {
        this.receiptSwiftCode = receiptSwiftCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
