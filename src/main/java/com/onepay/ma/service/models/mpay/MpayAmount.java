package com.onepay.ma.service.models.mpay;

/**
 * Created by anhkh on 12-Jun-17.
 */
public class MpayAmount {

    private double total;
    private String currency;
    private Double refundTotal;

    public Double getRefundTotal() {
        return refundTotal;
    }

    public void setRefundTotal(Double refundTotal) {
        this.refundTotal = refundTotal;
    }

    /**
     * Getter for property 'total'.
     *
     * @return Value for property 'total'.
     */
    public double getTotal() {
        return total;
    }

    /**
     * Setter for property 'total'.
     *
     * @param total Value to set for property 'total'.
     */
    public void setTotal(double total) {
        this.total = total;
    }

    /**
     * Getter for property 'currency'.
     *
     * @return Value for property 'currency'.
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * Setter for property 'currency'.
     *
     * @param currency Value to set for property 'currency'.
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "MpayAmount [total=" + total + ", currency=" + currency + ", refundTotal=" + refundTotal + "]";
    }

}
