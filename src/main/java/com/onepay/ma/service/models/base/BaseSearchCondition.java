package com.onepay.ma.service.models.base;

import java.io.Serializable;

/**
 * Created by anhkh on 8/8/2016.
 */
public class BaseSearchCondition   implements Serializable {

    private String keywords;

    private Integer page;

    private String from_date;

    private String to_date;

    private Integer pageSize;

    private Integer offset;

    /**
     * Getter for property 'keywords'.
     *
     * @return Value for property 'keywords'.
     */
    public String getKeywords() {
        return keywords;
    }

    /**
     * Setter for property 'keywords'.
     *
     * @param keywords Value to set for property 'keywords'.
     */
    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    /**
     * Getter for property 'page'.
     *
     * @return Value for property 'page'.
     */
    public Integer getPage() {
        return page;
    }

    /**
     * Setter for property 'page'.
     *
     * @param page Value to set for property 'page'.
     */
    public void setPage(Integer page) {
        this.page = page;
    }

    /**
     * Getter for property 'from_date'.
     *
     * @return Value for property 'from_date'.
     */
    public String getFrom_date() {
        return from_date;
    }

    /**
     * Setter for property 'from_date'.
     *
     * @param from_date Value to set for property 'from_date'.
     */
    public void setFrom_date(String from_date) {
        this.from_date = from_date;
    }

    /**
     * Getter for property 'to_date'.
     *
     * @return Value for property 'to_date'.
     */
    public String getTo_date() {
        return to_date;
    }

    /**
     * Setter for property 'to_date'.
     *
     * @param to_date Value to set for property 'to_date'.
     */
    public void setTo_date(String to_date) {
        this.to_date = to_date;
    }

    /**
     * Getter for property 'pageSize'.
     *
     * @return Value for property 'pageSize'.
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * Setter for property 'pageSize'.
     *
     * @param pageSize Value to set for property 'pageSize'.
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }
}
