package com.onepay.ma.service.models;

import oracle.jdbc.OracleTypes;

import java.sql.SQLType;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public enum OracleType implements SQLType {

    BIT(OracleTypes.BIT),

    TINYINT(OracleTypes.TINYINT),

    INTEGER(OracleTypes.INTEGER),

    BIGINT(OracleTypes.BIGINT),

    FLOAT(OracleTypes.FLOAT),


    REAL(OracleTypes.REAL),

    DOUBLE(OracleTypes.DOUBLE),


    NUMERIC(OracleTypes.NUMERIC),

    DECIMAL(OracleTypes.DECIMAL),

    <PERSON>AR(OracleTypes.CHAR),

    VARCHAR(OracleTypes.VARCHAR),

    <PERSON>ON<PERSON>VARCHAR(OracleTypes.LONGVARCHAR),

    DATE(OracleTypes.DATE),

    TIME(OracleTypes.TIME),

    TIMESTAMP(OracleTypes.TIMESTAMP),


    TIMESTAMPTZ(OracleTypes.TIMESTAMPTZ),

    TIMESTAMPLTZ(OracleTypes.TIMESTAMPLTZ),

    INTERVALYM(OracleTypes.INTERVALYM),

    INTERVALDS(OracleTypes.INTERVALDS),

    BINARY(OracleTypes.BINARY),

    VARBINARY(OracleTypes.VARBINARY),

    LONGVARBINARY(OracleTypes.LONGVARBINARY),

    ROWID(OracleTypes.ROWID),

    CURSOR(OracleTypes.CURSOR),

    BLOB(OracleTypes.BLOB),

    CLOB(OracleTypes.CLOB),

    BFILE(OracleTypes.BFILE),

    STRUCT(OracleTypes.STRUCT),

    ARRAY(OracleTypes.ARRAY),

    REF(OracleTypes.REF),

    NCHAR(OracleTypes.NCHAR),

    NCLOB(OracleTypes.NCLOB),

    NVARCHAR(OracleTypes.NVARCHAR),

    LONGNVARCHAR(OracleTypes.LONGNVARCHAR),

    SQLXML(OracleTypes.SQLXML),

    OPAQUE(OracleTypes.OPAQUE),

    JAVA_STRUCT(OracleTypes.JAVA_STRUCT),

    JAVA_OBJECT(OracleTypes.JAVA_OBJECT),

    PLSQL_INDEX_TABLE(OracleTypes.PLSQL_INDEX_TABLE),

    BINARY_FLOAT(OracleTypes.BINARY_FLOAT),

    BINARY_DOUBLE(OracleTypes.BINARY_DOUBLE),

    NULL(OracleTypes.NULL),

    NUMBER(OracleTypes.NUMBER),

    RAW(OracleTypes.RAW),

    OTHER(OracleTypes.OTHER),

    FIXED_CHAR(OracleTypes.FIXED_CHAR),

    DATALINK(OracleTypes.DATALINK),

    BOOLEAN(OracleTypes.BOOLEAN);




    OracleType(final Integer type) {
        this.type = type;
    }

    public static OracleType valueOf(int type) {
        for( OracleType sqlType : OracleType.class.getEnumConstants()) {
            if(type == sqlType.type)
                return sqlType;
        }
        throw new IllegalArgumentException("Type:" + type + " is not a valid "
                + "Types.java value.");
    }

    /**
     * The Integer value for the OracleType.  It maps to a value in
     * {@code Types.java}
     */
    private Integer type;

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String getVendor() {
        return "oracle.jdbc";
    }

    @Override
    public Integer getVendorTypeNumber() {
        return type;
    }
}
