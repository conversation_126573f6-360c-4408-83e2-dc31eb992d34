package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class TreeRole {
    private int role_id;
    private String role_name;
    private String role_type;
    private String description;
    private int order;
    private int parent_id;
    private List<PermissionRole> permissions;
    private List<TreeRole> sub_roles;


    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getParent_id() {
        return parent_id;
    }

    public void setParent_id(int parent_id) {
        this.parent_id = parent_id;
    }

    public List<PermissionRole> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<PermissionRole> permissions) {
        this.permissions = permissions;
    }

    public int getRole_id() {
        return role_id;
    }

    public void setRole_id(int role_id) {
        this.role_id = role_id;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(String role_name) {
        this.role_name = role_name;
    }

    public String getRole_type() {
        return role_type;
    }

    public void setRole_type(String role_type) {
        this.role_type = role_type;
    }

    public List<TreeRole> getSub_roles() {
        return sub_roles;
    }

    public void setSub_roles(List<TreeRole> sub_roles) {
        this.sub_roles = sub_roles;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
