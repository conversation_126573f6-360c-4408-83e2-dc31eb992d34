package com.onepay.ma.service.models;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class StatisticsReportParameter implements Serializable{
    private String acquirerId;
    private String fromDate;
    private String toDate;
    private String merchantId;
    private String merchantName;
    private int interval;
    private String transType;
    private String exportType;
    private String transDate;
    private String acqType;

    public String getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(String acquirerId) {
        this.acquirerId = acquirerId;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getAcqType() {
        return acqType;
    }

    public void setAcqType(String acqType) {
        this.acqType = acqType;
    }
}
