package com.onepay.ma.service.models;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class DomesticTransactionHistory {

    private int transaction_id;
    private int original_id;
    private String merchant_transaction_ref;
    private String transaction_type;
    private Amount amount;
    private String status;
    private String advanced_status;
    private String operator_id;
    private String note;
    private Timestamp transaction_time;

    private Integer parent_id;
    // private Integer id;
    private List<DomesticTransactionHistory> subHistories;


    /**
     * Getter for property 'advanced_status'.
     *
     * @return Value for property 'advanced_status'.
     */
    public String getAdvanced_status() {
        return advanced_status;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    /**
     * Setter for property 'advanced_status'.
     *
     * @param advanced_status Value to set for property 'advanced_status'.
     */
    public void setAdvanced_status(String advanced_status) {
        this.advanced_status = advanced_status;
    }

    /**
     * Getter for property 'subHistories'.
     *
     * @return Value for property 'subHistories'.
     */
    public List<DomesticTransactionHistory> getSubHistories() {
        return subHistories;
    }

    /**
     * Setter for property 'subHistories'.
     *
     * @param subHistories Value to set for property 'subHistories'.
     */
    public void setSubHistories(List<DomesticTransactionHistory> subHistories) {
        this.subHistories = subHistories;
    }

    /**
     * Getter for property 'parent_id'.
     *
     * @return Value for property 'parent_id'.
     */
    public Integer getParent_id() {
        return parent_id;
    }

    /**
     * Setter for property 'parent_id'.
     *
     * @param parent_id Value to set for property 'parent_id'.
     */
    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public int getOriginal_id() {
        return original_id;
    }

    public void setOriginal_id(int original_id) {
        this.original_id = original_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public DomesticTransactionHistory() {
    }

    public DomesticTransactionHistory(DomesticTransactionHistory source) {
        this.transaction_id = source.transaction_id;
        this.original_id = source.original_id;
        this.merchant_transaction_ref = source.merchant_transaction_ref;
        this.transaction_type = source.transaction_type;
        this.amount = source.amount;
        this.status = source.status;
        this.advanced_status = source.advanced_status;
        this.operator_id = source.operator_id;
        this.transaction_time = source.transaction_time;
        this.parent_id = source.parent_id;
        this.subHistories = source.subHistories;
        this.note = source.note;
    }
}
