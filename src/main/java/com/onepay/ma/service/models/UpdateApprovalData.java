package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/8/16.
 */
public class UpdateApprovalData {
    private String field;
    private Object old_value;
    private Object new_value;
    private String description;
    private List<OtherConditions> other_conditions;
    private String type;

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public Object getOld_value() {
        return old_value;
    }

    public void setOld_value(Object old_value) {
        this.old_value = old_value;
    }

    public Object getNew_value() {
        return new_value;
    }

    public void setNew_value(Object new_value) {
        this.new_value = new_value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<OtherConditions> getOther_conditions() {
        return other_conditions;
    }

    public void setOther_conditions(List<OtherConditions> other_conditions) {
        this.other_conditions = other_conditions;
    }
}
