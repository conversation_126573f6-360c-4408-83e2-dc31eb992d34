package com.onepay.ma.service.models;

import com.onepay.ma.service.models.base.BaseSearchCondition;

/**
 * Created by anhkh on 8/8/2016.
 */
public class RefundApprovalQuery extends BaseSearchCondition {

    private String merchantId;

    /**
     * Getter for property 'merchantId'.
     *
     * @return Value for property 'merchantId'.
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Setter for property 'merchantId'.
     *
     * @param merchantId Value to set for property 'merchantId'.
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}
