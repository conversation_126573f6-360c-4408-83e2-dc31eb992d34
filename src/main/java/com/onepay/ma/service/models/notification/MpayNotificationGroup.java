package com.onepay.ma.service.models.notification;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by anhkh on 12-Oct-17.
 */
public class MpayNotificationGroup {
    String header;
    String last_content;
    Timestamp last_date;
    Integer count_item;

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getLast_content() {
        return last_content;
    }

    public void setLast_content(String last_content) {
        this.last_content = last_content;
    }

    public Timestamp getLast_date() {
        return last_date;
    }

    public void setLast_date(Timestamp last_date) {
        this.last_date = last_date;
    }

    public Integer getCount_item() {
        return count_item;
    }

    public void setCount_item(Integer count_item) {
        this.count_item = count_item;
    }
}
