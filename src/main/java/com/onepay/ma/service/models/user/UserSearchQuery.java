package com.onepay.ma.service.models.user;

import com.onepay.ma.service.models.base.BaseSearchCondition;

import java.io.Serializable;

public class UserSearchQuery extends BaseSearchCondition implements Serializable {
    String merchant_id;
    String user_id;
    String email;
    String phone;

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
