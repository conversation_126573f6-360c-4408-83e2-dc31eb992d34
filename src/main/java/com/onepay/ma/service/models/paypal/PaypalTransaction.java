package com.onepay.ma.service.models.paypal;

import com.onepay.ma.service.models.Amount;

import java.sql.Timestamp;

public class PaypalTransaction {
    private String disputeId;
    private String buyerTransactionId;
    private String sellerTransactionId;
    private String transactionStatus;
    private Amount grossAmount;
    private String invoiceNumber;
    private String custom;
    private String buyerEmail;
    private String buyerPayerId;
    private String buyerName;
    private String sellerEmail;
    private String sellerMerchantId;
    private String sellerName;
    private Timestamp creatDate;
    private String sellerProtection;

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public String getBuyerTransactionId() {
        return buyerTransactionId;
    }

    public void setBuyerTransactionId(String buyerTransactionId) {
        this.buyerTransactionId = buyerTransactionId;
    }

    public String getSellerTransactionId() {
        return sellerTransactionId;
    }

    public void setSellerTransactionId(String sellerTransactionId) {
        this.sellerTransactionId = sellerTransactionId;
    }

    public String getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(String transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public Amount getGrossAmount() {
        return grossAmount;
    }

    public void setGrossAmount(Amount grossAmount) {
        this.grossAmount = grossAmount;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getCustom() {
        return custom;
    }

    public void setCustom(String custom) {
        this.custom = custom;
    }

    public String getBuyerEmail() {
        return buyerEmail;
    }

    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }

    public String getBuyerPayerId() {
        return buyerPayerId;
    }

    public void setBuyerPayerId(String buyerPayerId) {
        this.buyerPayerId = buyerPayerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getSellerEmail() {
        return sellerEmail;
    }

    public void setSellerEmail(String sellerEmail) {
        this.sellerEmail = sellerEmail;
    }

    public String getSellerMerchantId() {
        return sellerMerchantId;
    }

    public void setSellerMerchantId(String sellerMerchantId) {
        this.sellerMerchantId = sellerMerchantId;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public Timestamp getCreatDate() {
        return creatDate;
    }

    public void setCreatDate(Timestamp creatDate) {
        this.creatDate = creatDate;
    }

    public String getSellerProtection() {
        return sellerProtection;
    }

    public void setSellerProtection(String sellerProtection) {
        this.sellerProtection = sellerProtection;
    }
}
