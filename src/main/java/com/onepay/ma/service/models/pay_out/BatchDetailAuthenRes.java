package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;

public class BatchDetailAuthenRes implements Serializable {

    private String batchDetailId;
    private Integer checkApproval;
    private Integer checkOperator;

    public String getBatchDetailId() {
        return batchDetailId;
    }

    public void setBatchDetailId(String batchDetailId) {
        this.batchDetailId = batchDetailId;
    }

    public Integer getCheckApproval() {
        return checkApproval;
    }

    public void setCheckApproval(Integer checkApproval) {
        this.checkApproval = checkApproval;
    }

    public Integer getCheckOperator() {
        return checkOperator;
    }

    public void setCheckOperator(Integer checkOperator) {
        this.checkOperator = checkOperator;
    }
}
