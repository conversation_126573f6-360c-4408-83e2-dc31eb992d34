package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 5:54 PM
 * To change this ma-web.
 */

public class SummaryQueryDto implements Serializable {
    private String fromDate;
    private String toDate;
    private String merchantId;
    private String merchantName;
    private String merchantAccount;
    private String bankSender;
    private String bankReceipt;
    private String timeInterval;
    private String timeIntervalConvert;
    private String lang;

    public String getTimeIntervalConvert() {
        return timeIntervalConvert;
    }

    public void setTimeIntervalConvert(String timeIntervalConvert) {
        this.timeIntervalConvert = timeIntervalConvert;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public String getBankSender() {
        return bankSender;
    }

    public void setBankSender(String bankSender) {
        this.bankSender = bankSender;
    }

    public String getBankReceipt() {
        return bankReceipt;
    }

    public void setBankReceipt(String bankReceipt) {
        this.bankReceipt = bankReceipt;
    }

    public String getTimeInterval() {
        return timeInterval;
    }

    public void setTimeInterval(String timeInterval) {
        this.timeInterval = timeInterval;
    }

    /**
     * @return String return the lang
     */
    public String getLang() {
        return lang;
    }

    /**
     * @param lang the lang to set
     */
    public void setLang(String lang) {
        this.lang = lang;
    }

}
