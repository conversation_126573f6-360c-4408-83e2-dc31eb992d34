package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public class DomesticRefund {
    private int row_num;
    private Acquirer acquirer;
    private String merchant_id;
    private int transaction_id;
    private Integer original_id;
    private DomesticCard card;
    private String merchant_transaction_ref;
    private RefundAmount amount;
    private int status;
    private String order_info;
    private Timestamp transaction_time;
    private Timestamp transaction_purchase_time;
    private String ip_address;
    private String transaction_type;
    private String operator_id;
    private String note;

    /** Added on comment 3 */
    private String transaction_info;
    private String authentication_url;


    public Timestamp getTransaction_purchase_time() {
        return transaction_purchase_time;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public void setTransaction_purchase_time(Timestamp transaction_purchase_time) {
        this.transaction_purchase_time = transaction_purchase_time;
    }

    public int getRow_num() {
        return row_num;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public DomesticCard getCard() {
        return card;
    }

    public void setCard(DomesticCard card) {
        this.card = card;
    }

    public String getIp_address() {
        return ip_address;
    }

    public void setIp_address(String ip_address) {
        this.ip_address = ip_address;
    }

    public Integer getOriginal_id() {
        return original_id;
    }

    public void setOriginal_id(Integer original_id) {
        this.original_id = original_id;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public RefundAmount getAmount() {
        return amount;
    }

    public void setAmount(RefundAmount amount) {
        this.amount = amount;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }


    public String getTransaction_info() {
        return transaction_info;
    }

    public void setTransaction_info(String transaction_info) {
        this.transaction_info = transaction_info;
    }

    public String getAuthentication_url() {
        return authentication_url;
    }

    public void setAuthentication_url(String authentication_url) {
        this.authentication_url = authentication_url;
    }
}
