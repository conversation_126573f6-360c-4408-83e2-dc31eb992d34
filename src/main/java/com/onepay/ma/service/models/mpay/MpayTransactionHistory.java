package com.onepay.ma.service.models.mpay;

import com.onepay.ma.service.models.Amount;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by anhkh on 13-Jun-17.
 */
public class MpayTransactionHistory {


    private String transaction_id;
    private String original_id;
    private String order_info;
    private String merchant_transaction_ref;
    private String transaction_type;
    private Amount amount;
    private String status;
    private String operator_id;
    private Timestamp transaction_time;
    private String advanced_status;
    private String parent_id;
    private String note;
    private String description;
    //    private Integer id;
    private List<MpayTransactionHistory> subHistories;
    private Double paymentAmount;
    private Double partnerDiscountRate;
    private Double merchantDiscountRate;
    private Double offerDiscountAmount;
   

    public Double getPartnerDiscountRate() {
        return partnerDiscountRate;
    }

    public void setPartnerDiscountRate(Double partnerDiscountRate) {
        this.partnerDiscountRate = partnerDiscountRate;
    }

    public Double getMerchantDiscountRate() {
        return merchantDiscountRate;
    }

    public void setMerchantDiscountRate(Double merchantDiscountRate) {
        this.merchantDiscountRate = merchantDiscountRate;
    }

    public Double getOfferDiscountAmount() {
        return offerDiscountAmount;
    }

    public void setOfferDiscountAmount(Double offerDiscountAmount) {
        this.offerDiscountAmount = offerDiscountAmount;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getOrder_info() {
        return order_info;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public String getAdvanced_status() {
        return advanced_status;
    }

    public void setAdvanced_status(String advanced_status) {
        this.advanced_status = advanced_status;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    /**
     * Getter for property 'amount'.
     *
     * @return Value for property 'amount'.
     */
    public Amount getAmount() {
        return amount;
    }

    /**
     * Setter for property 'amount'.
     *
     * @param amount Value to set for property 'amount'.
     */
    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    /**
     * Getter for property 'transaction_id'.
     *
     * @return Value for property 'transaction_id'.
     */
    public String getTransaction_id() {
        return transaction_id;
    }

    /**
     * Setter for property 'transaction_id'.
     *
     * @param transaction_id Value to set for property 'transaction_id'.
     */
    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    /**
     * Getter for property 'original_id'.
     *
     * @return Value for property 'original_id'.
     */
    public String getOriginal_id() {
        return original_id;
    }

    /**
     * Setter for property 'original_id'.
     *
     * @param original_id Value to set for property 'original_id'.
     */
    public void setOriginal_id(String original_id) {
        this.original_id = original_id;
    }

    /**
     * Getter for property 'merchant_transaction_ref'.
     *
     * @return Value for property 'merchant_transaction_ref'.
     */
    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    /**
     * Setter for property 'merchant_transaction_ref'.
     *
     * @param merchant_transaction_ref Value to set for property 'merchant_transaction_ref'.
     */
    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    /**
     * Getter for property 'transaction_type'.
     *
     * @return Value for property 'transaction_type'.
     */
    public String getTransaction_type() {
        return transaction_type;
    }

    /**
     * Setter for property 'transaction_type'.
     *
     * @param transaction_type Value to set for property 'transaction_type'.
     */
    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }


    /**
     * Getter for property 'status'.
     *
     * @return Value for property 'status'.
     */
    public String getStatus() {
        return status;
    }

    /**
     * Setter for property 'status'.
     *
     * @param status Value to set for property 'status'.
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Getter for property 'operator_id'.
     *
     * @return Value for property 'operator_id'.
     */
    public String getOperator_id() {
        return operator_id;
    }

    /**
     * Setter for property 'operator_id'.
     *
     * @param operator_id Value to set for property 'operator_id'.
     */
    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    /**
     * Getter for property 'transaction_time'.
     *
     * @return Value for property 'transaction_time'.
     */
    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    /**
     * Setter for property 'transaction_time'.
     *
     * @param transaction_time Value to set for property 'transaction_time'.
     */
    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    
    public MpayTransactionHistory(MpayTransactionHistory source) {
        this.transaction_id = source.transaction_id;
        this.original_id = source.original_id;
        this.merchant_transaction_ref = source.merchant_transaction_ref;
        this.transaction_type = source.transaction_type;
        this.amount = source.amount;
        this.status = source.status;
        this.note = source.note;
        this.advanced_status = source.advanced_status;
        this.operator_id = source.operator_id;
        this.transaction_time = source.transaction_time;
        this.parent_id = source.parent_id;
        this.subHistories = source.subHistories;
        this.paymentAmount = source.paymentAmount;
    }

    public MpayTransactionHistory() {
    }

    public List<MpayTransactionHistory> getSubHistories() {
        return subHistories;
    }

    public void setSubHistories(List<MpayTransactionHistory> subHistories) {
        this.subHistories = subHistories;
    }

    /**
     * @return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
