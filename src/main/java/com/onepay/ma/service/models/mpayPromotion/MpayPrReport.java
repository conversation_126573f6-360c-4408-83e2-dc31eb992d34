package com.onepay.ma.service.models.mpayPromotion;

import java.sql.Timestamp;

/**
 * Created by anhkh on 01-Feb-18.
 */
public class MpayPrReport {
    private String merchant_id;
    private Timestamp date;
    private Double sum_payment;
    private Double sum_discount;
    private Double sum_original;
    private Integer trans_count;

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public Double getSum_payment() {
        return sum_payment;
    }

    public void setSum_payment(Double sum_payment) {
        this.sum_payment = sum_payment;
    }

    public Double getSum_discount() {
        return sum_discount;
    }

    public void setSum_discount(Double sum_discount) {
        this.sum_discount = sum_discount;
    }

    public Double getSum_original() {
        return sum_original;
    }

    public void setSum_original(Double sum_original) {
        this.sum_original = sum_original;
    }

    public Integer getTrans_count() {
        return trans_count;
    }

    public void setTrans_count(Integer trans_count) {
        this.trans_count = trans_count;
    }
}
