package com.onepay.ma.service.models.chartStatistics;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class ChartStatisticQuery implements Serializable {
    private String fromDate;
    private String toDate;
    private String merchantId;
    private String cardType;
    private String bank;
    private String card;
    private String paygate;
    private String target;
    private String merchantQt;
    private String merchantNd;
    private String fileName;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMerchantQt() {
        return merchantQt;
    }

    public void setMerchantQt(String merchantQt) {
        this.merchantQt = merchantQt;
    }

    public String getMerchantNd() {
        return merchantNd;
    }

    public void setMerchantNd(String merchantNd) {
        this.merchantNd = merchantNd;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getCard() {
        return card;
    }

    public void setCard(String card) {
        this.card = card;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}
