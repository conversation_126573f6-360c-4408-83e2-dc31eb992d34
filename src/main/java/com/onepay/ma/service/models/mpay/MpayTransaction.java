package com.onepay.ma.service.models.mpay;

import com.onepay.ma.service.models.Instrument;

import java.sql.Timestamp;

/**
 * Created by anhkh on 7/5/2016.
 */
public class MpayTransaction {

    private Integer rowNum;
    private String id;
    private String transactionId;
    private String merchantId;
    private String merchantName;
    private String onecomMerchantId;
    private String customerTransId;
    private MpayAmount amount;
    private Timestamp createTime;
    private Timestamp updateTime;
    private Timestamp expireTime;
    private String status;
    private String inputType;
    private String terminalId;
    private String orderInfo;
    private Instrument instrument;
    private String client;
    private String clientRef;
    private String bankTerminalId;
    private String bankMerchantId;

    private String acqCode;

    private MpayAcquirer acquirer;

    private String merchantOrderRef;
    private String merchantTxnRef;
    private String bankTransId;

    private String appName;
    private String invoiceId;
    private String masking;
    private String qrId;
    private String clientId;
    private String note;
    private String transactionType;

    // SAMSUNG
    private String customer_name;
    private String customer_mobile;
    private String customer_email;
    
    // QUICKLINK
    private String customer_address;

    //MOCA
    private Double paymentAmount;
    private Double offerDiscountAmount;


    public Double getOfferDiscountAmount() {
        return offerDiscountAmount;
    }

    public void setOfferDiscountAmount(Double offerDiscountAmount) {
        this.offerDiscountAmount = offerDiscountAmount;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getCustomer_address() {
        return customer_address;
    }

    public void setCustomer_address(String customer_address) {
        this.customer_address = customer_address;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getQrId() {
        return qrId;
    }

    public void setQrId(String qrId) {
        this.qrId = qrId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getMasking() {
        return masking;
    }

    public void setMasking(String masking) {
        this.masking = masking;
    }

    public String getAcqCode() {
        return acqCode;
    }

    public void setAcqCode(String acqCode) {
        this.acqCode = acqCode;
    }

    public String getClientRef() {
        return clientRef;
    }

    public void setClientRef(String clientRef) {
        this.clientRef = clientRef;
    }

    /**
     * Getter for property 'customerTransId'.
     *
     * @return Value for property 'customerTransId'.
     */
    public String getCustomerTransId() {
        return customerTransId;
    }

    /**
     * Setter for property 'customerTransId'.
     *
     * @param customerTransId Value to set for property 'customerTransId'.
     */
    public void setCustomerTransId(String customerTransId) {
        this.customerTransId = customerTransId;
    }

    /**
     * Getter for property 'merchantName'.
     *
     * @return Value for property 'merchantName'.
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * Setter for property 'merchantName'.
     *
     * @param merchantName Value to set for property 'merchantName'.
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * Getter for property 'bankTransId'.
     *
     * @return Value for property 'bankTransId'.
     */
    public String getBankTransId() {
        return bankTransId;
    }

    /**
     * Setter for property 'bankTransId'.
     *
     * @param bankTransId Value to set for property 'bankTransId'.
     */
    public void setBankTransId(String bankTransId) {
        this.bankTransId = bankTransId;
    }

    /**
     * Getter for property 'client'.
     *
     * @return Value for property 'client'.
     */
    public String getClient() {
        return client;
    }

    /**
     * Setter for property 'client'.
     *
     * @param client Value to set for property 'client'.
     */
    public void setClient(String client) {
        this.client = client;
    }

    public MpayAcquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(MpayAcquirer acquirer) {
        this.acquirer = acquirer;
    }

    /**
     * Getter for property 'onecomMerchantId'.
     *
     * @return Value for property 'onecomMerchantId'.
     */
    public String getOnecomMerchantId() {
        return onecomMerchantId;
    }

    /**
     * Setter for property 'onecomMerchantId'.
     *
     * @param onecomMerchantId Value to set for property 'onecomMerchantId'.
     */
    public void setOnecomMerchantId(String onecomMerchantId) {
        this.onecomMerchantId = onecomMerchantId;
    }

    /**
     * Getter for property 'merchantOrderRef'.
     *
     * @return Value for property 'merchantOrderRef'.
     */
    public String getMerchantOrderRef() {
        return merchantOrderRef;
    }

    /**
     * Setter for property 'merchantOrderRef'.
     *
     * @param merchantOrderRef Value to set for property 'merchantOrderRef'.
     */
    public void setMerchantOrderRef(String merchantOrderRef) {
        this.merchantOrderRef = merchantOrderRef;
    }

    /**
     * Getter for property 'merchantTxnRef'.
     *
     * @return Value for property 'merchantTxnRef'.
     */
    public String getMerchantTxnRef() {
        return merchantTxnRef;
    }

    /**
     * Setter for property 'merchantTxnRef'.
     *
     * @param merchantTxnRef Value to set for property 'merchantTxnRef'.
     */
    public void setMerchantTxnRef(String merchantTxnRef) {
        this.merchantTxnRef = merchantTxnRef;
    }

    /**
     * Getter for property 'rowNum'.
     *
     * @return Value for property 'rowNum'.
     */
    public Integer getRowNum() {
        return rowNum;
    }

    /**
     * Setter for property 'rowNum'.
     *
     * @param rowNum Value to set for property 'rowNum'.
     */
    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    /**
     * Getter for property 'transactionId'.
     *
     * @return Value for property 'transactionId'.
     */
    public String getTransactionId() {
        return transactionId;
    }

    /**
     * Setter for property 'transactionId'.
     *
     * @param transactionId Value to set for property 'transactionId'.
     */
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    /**
     * Getter for property 'merchantId'.
     *
     * @return Value for property 'merchantId'.
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Setter for property 'merchantId'.
     *
     * @param merchantId Value to set for property 'merchantId'.
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * Getter for property 'amount'.
     *
     * @return Value for property 'amount'.
     */
    public MpayAmount getAmount() {
        return amount;
    }

    /**
     * Setter for property 'amount'.
     *
     * @param amount Value to set for property 'amount'.
     */
    public void setAmount(MpayAmount amount) {
        this.amount = amount;
    }

    /**
     * Getter for property 'createTime'.
     *
     * @return Value for property 'createTime'.
     */
    public Timestamp getCreateTime() {
        return createTime;
    }

    /**
     * Setter for property 'createTime'.
     *
     * @param createTime Value to set for property 'createTime'.
     */
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    /**
     * Getter for property 'updateTime'.
     *
     * @return Value for property 'updateTime'.
     */
    public Timestamp getUpdateTime() {
        return updateTime;
    }

    /**
     * Setter for property 'updateTime'.
     *
     * @param updateTime Value to set for property 'updateTime'.
     */
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * Getter for property 'expireTime'.
     *
     * @return Value for property 'expireTime'.
     */
    public Timestamp getExpireTime() {
        return expireTime;
    }

    /**
     * Setter for property 'expireTime'.
     *
     * @param expireTime Value to set for property 'expireTime'.
     */
    public void setExpireTime(Timestamp expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * Getter for property 'status'.
     *
     * @return Value for property 'status'.
     */
    public String getStatus() {
        return status;
    }

    /**
     * Setter for property 'status'.
     *
     * @param status Value to set for property 'status'.
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Getter for property 'inputType'.
     *
     * @return Value for property 'inputType'.
     */
    public String getInputType() {
        return inputType;
    }

    /**
     * Setter for property 'inputType'.
     *
     * @param inputType Value to set for property 'inputType'.
     */
    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    /**
     * Getter for property 'terminalId'.
     *
     * @return Value for property 'terminalId'.
     */
    public String getTerminalId() {
        return terminalId;
    }

    /**
     * Setter for property 'terminalId'.
     *
     * @param terminalId Value to set for property 'terminalId'.
     */
    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }


    /**
     * Getter for property 'orderInfo'.
     *
     * @return Value for property 'orderInfo'.
     */
    public String getOrderInfo() {
        return orderInfo;
    }

    /**
     * Setter for property 'orderInfo'.
     *
     * @param orderInfo Value to set for property 'orderInfo'.
     */
    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    /**
     * Getter for property 'instrument'.
     *
     * @return Value for property 'instrument'.
     */
    public Instrument getInstrument() {
        return instrument;
    }

    /**
     * Setter for property 'instrument'.
     *
     * @param instrument Value to set for property 'instrument'.
     */
    public void setInstrument(Instrument instrument) {
        this.instrument = instrument;
    }

    /**
     * @return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     * Getter for property 'customer_name'.
     *
     * @return Value for property 'customer_name'.
     */
    public String getCustomer_name() {
        return customer_name;
    }

    /**
     * @param customer_name the note to set
     */
    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    /**
     * Getter for property 'customer_mobile'.
     *
     * @return Value for property 'customer_mobile'.
     */
    public String getCustomer_mobile() {
        return customer_mobile;
    }

    /**
     * @param customer_mobile the note to set
     */
    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    /**
     * Getter for property 'customer_email'.
     *
     * @return Value for property 'customer_email'.
     */
    public String getCustomer_email() {
        return customer_email;
    }

    /**
     * @param customer_email the note to set
     */
    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getBankTerminalId() {
        return this.bankTerminalId;
    }

    public void setBankTerminalId(String bankTerminalId) {
        this.bankTerminalId = bankTerminalId;
    }

    public String getBankMerchantId() {
        return this.bankMerchantId;
    }

    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }
    /**
     * @return String return the transactionType
     */
    public String getTransactionType() {
        return transactionType;
    }

    /**
     * @param transactionType the transactionType to set
     */
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }


    /**
     * @return String return the id
     */
    public String getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(String id) {
        this.id = id;
    }

}
