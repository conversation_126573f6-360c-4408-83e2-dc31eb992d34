/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */
/**
 * Created by duongtv on 24/8/2017.
 */
package com.onepay.ma.service.models;

import java.util.List;

public class TokenizationTrans {
    private List<TokenizationTran> trans;
    private int totalTran;

    public List<TokenizationTran> getTrans() {
        return trans;
    }

    public void setTrans(List<TokenizationTran> trans) {
        this.trans = trans;
    }

    public int getTotalTran() {
        return totalTran;
    }

    public void setTotalTran(int totalTran) {
        this.totalTran = totalTran;
    }
}
