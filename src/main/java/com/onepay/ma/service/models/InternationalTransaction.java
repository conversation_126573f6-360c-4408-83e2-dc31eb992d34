package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public class InternationalTransaction {
    private int row_num;
    private int transaction_id;
    private int original_transaction_id;
    private String transaction_type;
    private String order_info;
    private Acquirer acquirer;
    private String transaction_reference;
    private String ocMerTxnRef;
    private InternationalAmount amount;
    private InternationalCard card;
    private String merchant_id;
    private AuthenticationData authentication;
    private Timestamp transaction_time;
    private String transaction_status;
    private String transaction_ref_number;
    private String ip_address;
    private String ip_proxy;
    private String bin_country;
    private String csc_result_code;
    private String enrolled_3ds;
    private AvsData avs;
    private String verification_security_level;
    private String eci;
    private String operator;
    private String response_code;
    private boolean can_void;
    private boolean is_required_avs;
    private String mid_no;
    private String risk_assesment;
    private String bank_id;
    private Double wait_for_approval_amount;

    private String advance_status;
    private String ticket_number;

    //PP
    private String status3ds;
    private String riskOverAllResult;
    private String xid;
    private String cardLevelIndicator;

    // SAMSUNG
    private String customer_mobile;
    private String customer_email;
    private String epp;
    private String fraud_check;
    private String customer_address;

    // ADAYROI
    private String customer_name;


    // INSTALLMENT
    private String installment_bank;
    private String installment_status;
    private String installment_time;
    private String installment_cus_phone;
    private String installment_cus_email;
    private Integer installment_cancel_days;
    private Double installment_monthly_amount;
    private Double installment_fee;
    private String card_holder_name;
    private String type;
    public String getBank_id() {
        return bank_id;
    }

    public void setBank_id(String bank_id) {
        this.bank_id = bank_id;
    }

    public String getRisk_assesment() {
        return risk_assesment;
    }

    public void setRisk_assesment(String risk_assesment) {
        this.risk_assesment = risk_assesment;
    }

    public Double getInstallment_fee() {
        return installment_fee;
    }

    public void setInstallment_fee(Double installment_fee) {
        this.installment_fee = installment_fee;
    }

    public String getCard_holder_name() {
        return card_holder_name;
    }

    public void setCard_holder_name(String card_holder_name) {
        this.card_holder_name = card_holder_name;
    }

    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getInstallment_status() {
        return installment_status;
    }

    public void setInstallment_status(String installment_status) {
        this.installment_status = installment_status;
    }

    public String getInstallment_time() {
        return installment_time;
    }

    public void setInstallment_time(String installment_time) {
        this.installment_time = installment_time;
    }

    public String getInstallment_cus_phone() {
        return installment_cus_phone;
    }

    public void setInstallment_cus_phone(String installment_cus_phone) {
        this.installment_cus_phone = installment_cus_phone;
    }

    public String getInstallment_cus_email() {
        return installment_cus_email;
    }

    public void setInstallment_cus_email(String installment_cus_email) {
        this.installment_cus_email = installment_cus_email;
    }

    public Integer getInstallment_cancel_days() {
        return installment_cancel_days;
    }

    public void setInstallment_cancel_days(Integer installment_cancel_days) {
        this.installment_cancel_days = installment_cancel_days;
    }

    public Double getInstallment_monthly_amount() {
        return installment_monthly_amount;
    }

    public void setInstallment_monthly_amount(Double installment_monthly_amount) {
        this.installment_monthly_amount = installment_monthly_amount;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getEpp() {
        return epp;
    }

    public void setEpp(String epp) {
        this.epp = epp;
    }
    
    public Double getWait_for_approval_amount() {
        return wait_for_approval_amount;
    }

    public void setWait_for_approval_amount(Double wait_for_approval_amount) {
        this.wait_for_approval_amount = wait_for_approval_amount;
    }

    public String getStatus3ds() {
        return status3ds;
    }

    public void setStatus3ds(String status3ds) {
        this.status3ds = status3ds;
    }

    public String getRiskOverAllResult() {
        return riskOverAllResult;
    }

    public void setRiskOverAllResult(String riskOverAllResult) {
        this.riskOverAllResult = riskOverAllResult;
    }

    public String getXid() {
        return xid;
    }

    public void setXid(String xid) {
        this.xid = xid;
    }

    public String getCardLevelIndicator() {
        return cardLevelIndicator;
    }

    public void setCardLevelIndicator(String cardLevelIndicator) {
        this.cardLevelIndicator = cardLevelIndicator;
    }

    public String getAdvance_status() {
        return advance_status;
    }

    public void setAdvance_status(String advance_status) {
        this.advance_status = advance_status;
    }

    public String getMid_no() {
        return mid_no;
    }

    public void setMid_no(String mid_no) {
        this.mid_no = mid_no;
    }

    public String getEci() {
        return eci;
    }

    public void setEci(String eci) {
        this.eci = eci;
    }

    public boolean isIs_required_avs() {
        return is_required_avs;
    }

    public void setIs_required_avs(boolean is_required_avs) {
        this.is_required_avs = is_required_avs;
    }

    public int getRow_num() {
        return row_num;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public boolean isCan_void() {
        return can_void;
    }

    public void setCan_void(boolean can_void) {
        this.can_void = can_void;
    }

    public String getIp_proxy() {
        return ip_proxy;
    }

    public void setIp_proxy(String ip_proxy) {
        this.ip_proxy = ip_proxy;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public int getOriginal_transaction_id() {
        return original_transaction_id;
    }

    public void setOriginal_transaction_id(int original_transaction_id) {
        this.original_transaction_id = original_transaction_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public String getTransaction_reference() {
        return transaction_reference;
    }

    public void setTransaction_reference(String transaction_reference) {
        this.transaction_reference = transaction_reference;
    }

    public InternationalAmount getAmount() {
        return amount;
    }

    public void setAmount(InternationalAmount amount) {
        this.amount = amount;
    }

    public InternationalCard getCard() {
        return card;
    }

    public void setCard(InternationalCard card) {
        this.card = card;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public AuthenticationData getAuthentication() {
        return authentication;
    }

    public void setAuthentication(AuthenticationData authentication) {
        this.authentication = authentication;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getTransaction_status() {
        return transaction_status;
    }

    public void setTransaction_status(String transaction_status) {
        this.transaction_status = transaction_status;
    }

    public String getTransaction_ref_number() {
        return transaction_ref_number;
    }

    public void setTransaction_ref_number(String transaction_ref_number) {
        this.transaction_ref_number = transaction_ref_number;
    }

    public String getIp_address() {
        return ip_address;
    }

    public void setIp_address(String ip_address) {
        this.ip_address = ip_address;
    }

    public String getBin_country() {
        return bin_country;
    }

    public void setBin_country(String bin_country) {
        this.bin_country = bin_country;
    }

    public String getCsc_result_code() {
        return csc_result_code;
    }

    public void setCsc_result_code(String csc_result_code) {
        this.csc_result_code = csc_result_code;
    }

    public String getEnrolled_3ds() {
        return enrolled_3ds;
    }

    public void setEnrolled_3ds(String enrolled_3ds) {
        this.enrolled_3ds = enrolled_3ds;
    }

    public AvsData getAvs() {
        return avs;
    }

    public void setAvs(AvsData avs) {
        this.avs = avs;
    }

    public String getVerification_security_level() {
        return verification_security_level;
    }

    public void setVerification_security_level(String verification_security_level) {
        this.verification_security_level = verification_security_level;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }

    public String getTicket_number() {
        return ticket_number;
    }

    public void setTicket_number(String ticket_number) {
        this.ticket_number = ticket_number;
    }

    public String getCustomer_address() {
        return customer_address;
    }

    public void setCustomer_address(String customer_address) {
        this.customer_address = customer_address;
    }

    /**
     * @return String return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return String return the ocMerTxnRef
     */
    public String getOcMerTxnRef() {
        return ocMerTxnRef;
    }

    /**
     * @param ocMerTxnRef the ocMerTxnRef to set
     */
    public void setOcMerTxnRef(String ocMerTxnRef) {
        this.ocMerTxnRef = ocMerTxnRef;
    }

}
