package com.onepay.ma.service.models;

/**
 * Created by anhkh on 7/5/2016.
 */
public class Instrument {

    private String id;
    private String type;
    private String name;
    private String number;
    private String brandId;

    /**
     * Getter for property 'brandId'.
     *
     * @return Value for property 'brandId'.
     */
    public String getBrandId() {
        return brandId;
    }

    /**
     * Setter for property 'brandId'.
     *
     * @param brandId Value to set for property 'brandId'.
     */
    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    /**
     * Getter for property 'id'.
     *
     * @return Value for property 'id'.
     */
    public String getId() {
        return id;
    }

    /**
     * Setter for property 'id'.
     *
     * @param id Value to set for property 'id'.
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * Getter for property 'type'.
     *
     * @return Value for property 'type'.
     */
    public String getType() {
        return type;
    }

    /**
     * Setter for property 'type'.
     *
     * @param type Value to set for property 'type'.
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * Getter for property 'name'.
     *
     * @return Value for property 'name'.
     */
    public String getName() {
        return name;
    }

    /**
     * Setter for property 'name'.
     *
     * @param name Value to set for property 'name'.
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter for property 'number'.
     *
     * @return Value for property 'number'.
     */
    public String getNumber() {
        return number;
    }

    /**
     * Setter for property 'number'.
     *
     * @param number Value to set for property 'number'.
     */
    public void setNumber(String number) {
        this.number = number;
    }
}
