package com.onepay.ma.service.models.notification;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by anhkh on 26-Mar-18.
 */
public class MpayvnNotificationMsg implements Serializable {
    private Integer id;
    private String header_vi;
    private String header_en;
    private String content_vi;
    private String content_en;
    private String target;
    private String destination;
    private String category;
    private String state;
    private Timestamp create_date;
    private Integer send_total;
    private Integer seen_total;
    private String sender;
    private String html_content_vi;
    private String html_content_en;

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getHeader_vi() {
        return header_vi;
    }

    public void setHeader_vi(String header_vi) {
        this.header_vi = header_vi;
    }

    public String getHeader_en() {
        return header_en;
    }

    public void setHeader_en(String header_en) {
        this.header_en = header_en;
    }

    public String getContent_vi() {
        return content_vi;
    }

    public void setContent_vi(String content_vi) {
        this.content_vi = content_vi;
    }

    public String getContent_en() {
        return content_en;
    }

    public void setContent_en(String content_en) {
        this.content_en = content_en;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Integer getSend_total() {
        return send_total;
    }

    public void setSend_total(Integer send_total) {
        this.send_total = send_total;
    }

    public Integer getSeen_total() {
        return seen_total;
    }

    public void setSeen_total(Integer seen_total) {
        this.seen_total = seen_total;
    }

    public String getHtml_content_vi() {
        return html_content_vi;
    }

    public void setHtml_content_vi(String html_content_vi) {
        this.html_content_vi = html_content_vi;
    }

    public String getHtml_content_en() {
        return html_content_en;
    }

    public void setHtml_content_en(String html_content_en) {
        this.html_content_en = html_content_en;
    }
}
