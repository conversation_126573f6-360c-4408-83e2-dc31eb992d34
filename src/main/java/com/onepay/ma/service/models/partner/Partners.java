package com.onepay.ma.service.models.partner;

import java.util.List;

public class Partners {
    private List<Partner> partners;

    private int total_items;

    /**
     * Getter for property 'partners'.
     *
     * @return Value for property 'partners'.
     */
    public List<Partner> getPartners() {
        return this.partners;
    }

    /**
     * Setter for property 'partners'.
     *
     * @param partners Value to set for property 'partners'.
     */
    public void setPartners(List<Partner> partners) {
        this.partners = partners;
    }

    /**
     * Getter for property 'total_items'.
     *
     * @return Value for property 'total_items'.
     */
    public int getTotal_items() {
        return this.total_items;
    }

    /**
     * Setter for property 'total_items'.
     *
     * @param total_items Value to set for property 'total_items'.
     */
    public void setTotal_items(int total_items) {
        this.total_items = total_items;
    }
}
