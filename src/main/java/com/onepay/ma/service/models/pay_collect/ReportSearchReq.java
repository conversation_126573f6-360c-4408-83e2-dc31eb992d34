package com.onepay.ma.service.models.pay_collect;

import java.io.Serializable;

public class ReportSearchReq implements Serializable {
    private String fromDate;
    private String toDate;
    private String virtualId;
    private String bankTxnRef;
    private String receivedBankList;
    private String senderBankList;
    private String interval;
    private int page;
    private int pageSize;
    private String merchantId;
    private String groupBy;
    private String lang;

    public ReportSearchReq() {
    }

    public ReportSearchReq(String fromDate, String toDate, String virtualId, String interval, int page, int pageSize,
            String receivedBankList, String merchantId, String groupBy) {
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.virtualId = virtualId;
        // this.bankTxnRef = bankTxnRef;
        this.receivedBankList = receivedBankList;
        // this.senderBankList = senderBankList;
        this.interval = interval;
        this.page = page;
        this.pageSize = pageSize;
        this.merchantId = merchantId;
        this.groupBy = groupBy;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getVirtualId() {
        return virtualId;
    }

    public void setVirtualId(String virtualId) {
        this.virtualId = virtualId;
    }

    public String getBankTxnRef() {
        return bankTxnRef;
    }

    public void setBankTxnRef(String bankTxnRef) {
        this.bankTxnRef = bankTxnRef;
    }

    public String getReceivedBankList() {
        return receivedBankList;
    }

    public void setReceivedBankList(String receivedBankList) {
        this.receivedBankList = receivedBankList;
    }

    public String getSenderBankList() {
        return senderBankList;
    }

    public void setSenderBankList(String senderBankList) {
        this.senderBankList = senderBankList;
    }

    public String getInterval() {
        return interval;
    }

    public void setInterval(String interval) {
        this.interval = interval;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
