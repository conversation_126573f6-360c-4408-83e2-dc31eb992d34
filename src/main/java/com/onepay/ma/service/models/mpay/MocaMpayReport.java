package com.onepay.ma.service.models.mpay;

import java.sql.Timestamp;

/**
 * Created by anhkh on 04-Jun-18.
 */
public class MocaMpayReport {

    private Timestamp reportDate;
    private String merchantId;
    private String merchantName;
    private String cardType;
    private String appName;
    private Integer transactionCount;
    private Double transactionTotal;
    private Integer refundCount;
    private Double refundTotal;
    private String client;
    private Double transPaymentAmountTotal;
    private Double refundPaymentAmountTotal;

    public Double getTransPaymentAmountTotal() {
		return transPaymentAmountTotal;
	}

	public void setTransPaymentAmountTotal(Double transPaymentAmountTotal) {
		this.transPaymentAmountTotal = transPaymentAmountTotal;
	}

	public Double getRefundPaymentAmountTotal() {
		return refundPaymentAmountTotal;
	}

	public void setRefundPaymentAmountTotal(Double refundPaymentAmountTotal) {
		this.refundPaymentAmountTotal = refundPaymentAmountTotal;
	}

	public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Timestamp getReportDate() {
        return reportDate;
    }

    public void setReportDate(Timestamp reportDate) {
        this.reportDate = reportDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getTransactionCount() {
        return transactionCount;
    }

    public void setTransactionCount(Integer transactionCount) {
        this.transactionCount = transactionCount;
    }

    public Double getTransactionTotal() {
        return transactionTotal;
    }

    public void setTransactionTotal(Double transactionTotal) {
        this.transactionTotal = transactionTotal;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public Double getRefundTotal() {
        return refundTotal;
    }

    public void setRefundTotal(Double refundTotal) {
        this.refundTotal = refundTotal;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }
}
