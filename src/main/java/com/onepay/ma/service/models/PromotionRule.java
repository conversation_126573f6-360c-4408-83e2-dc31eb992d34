package com.onepay.ma.service.models;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public class PromotionRule {
    private int rule_id;
    private String rule_name;
    private String description;
    private PromotionMessage message;
    private Timestamp create_time;
    private PromotionRuleType rule_type;
    private List<PromotionRuleParam> params;
    private int order;

    public List<PromotionRuleParam> getParams() {
        return params;
    }

    public void setParams(List<PromotionRuleParam> params) {
        this.params = params;
    }

    public int getRule_id() {
        return rule_id;
    }

    public void setRule_id(int rule_id) {
        this.rule_id = rule_id;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PromotionMessage getMessage() {
        return message;
    }

    public void setMessage(PromotionMessage message) {
        this.message = message;
    }

    public Timestamp getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Timestamp create_time) {
        this.create_time = create_time;
    }

    public PromotionRuleType getRule_type() {
        return rule_type;
    }

    public void setRule_type(PromotionRuleType rule_type) {
        this.rule_type = rule_type;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
}
