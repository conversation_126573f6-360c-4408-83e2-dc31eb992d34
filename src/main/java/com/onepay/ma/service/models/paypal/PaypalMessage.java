package com.onepay.ma.service.models.paypal;

import java.sql.Timestamp;

public class PaypalMessage {
    private int id;
    private String disputeId;
    private String posted_by;
    private Timestamp timePosted;
    private String content;
    private int nDisputeId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public String getPosted_by() {
        return posted_by;
    }

    public void setPosted_by(String posted_by) {
        this.posted_by = posted_by;
    }

    public Timestamp getTimePosted() {
        return timePosted;
    }

    public void setTimePosted(Timestamp timePosted) {
        this.timePosted = timePosted;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getnDisputeId() {
        return nDisputeId;
    }

    public void setnDisputeId(int nDisputeId) {
        this.nDisputeId = nDisputeId;
    }
}
