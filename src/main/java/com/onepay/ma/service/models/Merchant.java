package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/31/16.
 */
public class Merchant {

    private String merchant_id;
    private String merchant_name;
    private String currency_code;
    private String type;
    private String state;

    private List<String> terminalIdList;
    private boolean isAllTerminal;

    public boolean isAllTerminal() {
        return isAllTerminal;
    }

    public void setAllTerminal(boolean allTerminal) {
        isAllTerminal = allTerminal;
    }

    /**
     * Getter for property 'terminalIdList'.
     *
     * @return Value for property 'terminalIdList'.
     */
    public List<String> getTerminalIdList() {
        return terminalIdList;
    }

    /**
     * Setter for property 'terminalIdList'.
     *
     * @param terminalIdList Value to set for property 'terminalIdList'.
     */
    public void setTerminalIdList(List<String> terminalIdList) {
        this.terminalIdList = terminalIdList;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getMerchant_name() {
        return merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getCurrency_code() {
        return currency_code;
    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

}
