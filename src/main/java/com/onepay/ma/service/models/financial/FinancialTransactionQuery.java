package com.onepay.ma.service.models.financial;

import com.onepay.ma.service.models.base.BaseSearchCondition;

import java.io.Serializable;

/**
 * Created by tuydv on 09-5-18.
 */
public class FinancialTransactionQuery extends BaseSearchCondition  implements Serializable{
    private String merchant_id;
    private String order_info;
    private String status;
    private String card_number;
    private String card_type;
    private String merchant_transaction_ref;
    private String currency;
    private String transaction_type;
    private String acquirer_id;
    private String response_code;
    private String transaction_id;
    private String authorisation_code;

    private String target;

    private String installment_bank;
    private String installment_status;
    private String risk_assessment;
    private String show_risk_assessment;
    private String hidden_column;
    // SAMSUNG
    private String customer_name;
    private String customer_mobile;
    private String customer_email;
    private String merchant_website;
    private String fraud_check;
    private String order_status;

    //date to query connection backup 114
    private String fromDate;
    private String toDate;

    private String lang;

    public String getHidden_column() {
        return hidden_column;
    }

    public void setHidden_column(String hidden_column) {
        this.hidden_column = hidden_column;
    }

    public String getShow_risk_assessment() {
        return show_risk_assessment;
    }

    public void setShow_risk_assessment(String show_risk_assessment) {
        this.show_risk_assessment = show_risk_assessment;
    }

    public String getRisk_assessment() {
        return risk_assessment;
    }

    public void setRisk_assessment(String risk_assessment) {
        this.risk_assessment = risk_assessment;
    }

    public String getInstallment_status() {
        return installment_status;
    }

    public void setInstallment_status(String installment_status) {
        this.installment_status = installment_status;
    }

    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getOrder_status() { return order_status; }

    public void setOrder_status(String order_status) { this.order_status = order_status; }

    public String getAuthorisation_code() { return authorisation_code; }

    public void setAuthorisation_code(String authorisation_code) { this.authorisation_code = authorisation_code; }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getMerchant_website() {
        return merchant_website;
    }

    public void setMerchant_website(String merchant_website) {
        this.merchant_website = merchant_website;
    }


    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getAcquirer_id() {
        return acquirer_id;
    }

    public void setAcquirer_id(String acquirer_id) {
        this.acquirer_id = acquirer_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    /**
     * @return String return the fromDate
     */
    public String getFromDate() {
        return fromDate;
    }

    /**
     * @param fromDate the fromDate to set
     */
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * @return String return the toDate
     */
    public String getToDate() {
        return toDate;
    }

    /**
     * @param toDate the toDate to set
     */
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
