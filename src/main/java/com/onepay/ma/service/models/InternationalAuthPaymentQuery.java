package com.onepay.ma.service.models;

import com.onepay.ma.service.models.base.BaseSearchCondition;

import java.io.Serializable;

/**
 * Created by anhkh on 18-May-17.
 */
public class InternationalAuthPaymentQuery extends BaseSearchCondition implements Serializable{

    private String merchantId;
    private String transactionId;
    private String merchantTransactionRef;
    private String authenticationState;
    private String currency;
    private String cardType;
    private String orderInfo;
    private String cardNumber;
    private String authenticationCode;
    private String lang;
    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantTransactionRef() { return merchantTransactionRef; }

    public void setMerchantTransactionRef(String merchantTransactionRef) { this.merchantTransactionRef = merchantTransactionRef; }

    public String getAuthenticationState() {
        return authenticationState;
    }

    public void setAuthenticationState(String authenticationState) {
        this.authenticationState = authenticationState;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getAuthenticationCode() {
        return authenticationCode;
    }

    public void setAuthenticationCode(String authenticationCode) {
        this.authenticationCode = authenticationCode;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
