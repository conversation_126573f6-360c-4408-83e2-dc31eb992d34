package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 5:54 PM
 * To change this ma-web.
 */

public class ListSummaryFundsTransDto implements Serializable {
    private BigInteger totalCount;
    private BigInteger totalAmount;
    private List<SummaryFundsTransDto> list;

    public BigInteger getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigInteger totalCount) {
        this.totalCount = totalCount;
    }

    public BigInteger getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigInteger totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<SummaryFundsTransDto> getList() {
        return list;
    }

    public void setList(List<SummaryFundsTransDto> list) {
        this.list = list;
    }
}
