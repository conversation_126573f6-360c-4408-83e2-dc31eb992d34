package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/4/16.
 */
public class PromotionTransaction {
    private Timestamp transaction_time;
    private PromotionAmount amount;
    private int promotion_status;
    private String transaction_id;
    private PromotionAddress avs;
    private PromotionCard card;
    private String csc_result_code;
    private String merchant_id;
    private String order_info;
    private String promotion_code;
    private String promotion_name;
    private String response_code;
    private String status;
    private String ip_address;
    private String transaction_no;
    private String transaction_reference;
    private String paygate;

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getPromotion_name() {
        return promotion_name;
    }

    public void setPromotion_name(String promotion_name) {
        this.promotion_name = promotion_name;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public PromotionAmount getAmount() {
        return amount;
    }

    public void setAmount(PromotionAmount amount) {
        this.amount = amount;
    }

    public int getPromotion_status() {
        return promotion_status;
    }

    public void setPromotion_status(int promotion_status) {
        this.promotion_status = promotion_status;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public PromotionAddress getAvs() {
        return avs;
    }

    public void setAvs(PromotionAddress avs) {
        this.avs = avs;
    }

    public PromotionCard getCard() {
        return card;
    }

    public void setCard(PromotionCard card) {
        this.card = card;
    }

    public String getCsc_result_code() {
        return csc_result_code;
    }

    public void setCsc_result_code(String csc_result_code) {
        this.csc_result_code = csc_result_code;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getPromotion_code() {
        return promotion_code;
    }

    public void setPromotion_code(String promotion_code) {
        this.promotion_code = promotion_code;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIp_address() {
        return ip_address;
    }

    public void setIp_address(String ip_address) {
        this.ip_address = ip_address;
    }

    public String getTransaction_no() {
        return transaction_no;
    }

    public void setTransaction_no(String transaction_no) {
        this.transaction_no = transaction_no;
    }

    public String getTransaction_reference() {
        return transaction_reference;
    }

    public void setTransaction_reference(String transaction_reference) {
        this.transaction_reference = transaction_reference;
    }
}
