package com.onepay.ma.service.models.paypal;

import java.sql.Timestamp;
import java.util.List;

public class PaypalEvidence {
    private String type;
    private String source;
    private String disputeId;
    private List<PaypalTrackingInfo> trackingInfo;
    private Timestamp date;
    private Integer nDisputeId;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public List<PaypalTrackingInfo> getTrackingInfo() {
        return trackingInfo;
    }

    public void setTrackingInfo(List<PaypalTrackingInfo> trackingInfo) {
        this.trackingInfo = trackingInfo;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public Integer getnDisputeId() {
        return nDisputeId;
    }

    public void setnDisputeId(Integer nDisputeId) {
        this.nDisputeId = nDisputeId;
    }
}
