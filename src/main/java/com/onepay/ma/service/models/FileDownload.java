package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/16/16.
 */
public class FileDownload {
    private String file_hash_name;
    private String file_name;
    private String conditions;
    private String user;
    private String status;
    private Timestamp create_date;
    private Timestamp update_date;
    private Timestamp expired_date;
    private String ext;
    private String file_type;
    private long file_size;

    public long getFile_size() {
        return file_size;
    }

    public void setFile_size(long file_size) {
        this.file_size = file_size;
    }

    public String getFile_hash_name() {
        return file_hash_name;
    }

    public void setFile_hash_name(String file_hash_name) {
        this.file_hash_name = file_hash_name;
    }

    public String getFile_name() {
        return file_name;
    }

    public void setFile_name(String file_name) {
        this.file_name = file_name;
    }

    public String getConditions() {
        return conditions;
    }

    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Timestamp getUpdate_date() {
        return update_date;
    }

    public void setUpdate_date(Timestamp update_date) {
        this.update_date = update_date;
    }

    public Timestamp getExpired_date() {
        return expired_date;
    }

    public void setExpired_date(Timestamp expired_date) {
        this.expired_date = expired_date;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getFile_type() {
        return file_type;
    }

    public void setFile_type(String file_type) {
        this.file_type = file_type;
    }
}
