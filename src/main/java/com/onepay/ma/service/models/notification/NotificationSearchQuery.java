package com.onepay.ma.service.models.notification;

import com.onepay.ma.service.models.base.BaseSearchCondition;

/**
 * Created by anhkh on 09-Oct-17.
 */
public class NotificationSearchQuery extends BaseSearchCondition {
    private String userId;
    private String language;
    private String group;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
