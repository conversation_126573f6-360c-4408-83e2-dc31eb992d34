package com.onepay.ma.service.models.config;

/**
 * Created by anhkh on 10-Aug-17.
 */
public class HttpServiceConfig {

        private String groupId;
        private String clientId;
        private String clientKey;
        private String serviceName;
        private String serviceRegion;
        private String serviceOwsRequest;
        private String serviceOwsAlgorithm;
        private String serviceBaseURL;

        private int serviceTimeOut;

        public HttpServiceConfig() {
        }

        public HttpServiceConfig(String groupId, String clientId, String clientKey, String serviceName, String serviceRegion,
                                 String serviceOwsRequest, String serviceOwsAlgorithm, String serviceBaseURL, int serviceTimeOut) {
            this.groupId = groupId;
            this.clientId = clientId;
            this.clientKey = clientKey;
            this.serviceName = serviceName;
            this.serviceRegion = serviceRegion;
            this.serviceOwsRequest = serviceOwsRequest;
            this.serviceOwsAlgorithm = serviceOwsAlgorithm;
            this.serviceBaseURL = serviceBaseURL;
            this.serviceTimeOut = serviceTimeOut;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getClientKey() {
            return clientKey;
        }

        public void setClientKey(String clientKey) {
            this.clientKey = clientKey;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getServiceRegion() {
            return serviceRegion;
        }

        public void setServiceRegion(String serviceRegion) {
            this.serviceRegion = serviceRegion;
        }

        public String getServiceOwsRequest() {
            return serviceOwsRequest;
        }

        public void setServiceOwsRequest(String serviceOwsRequest) {
            this.serviceOwsRequest = serviceOwsRequest;
        }

        public String getServiceOwsAlgorithm() {
            return serviceOwsAlgorithm;
        }

        public void setServiceOwsAlgorithm(String serviceOwsAlgorithm) {
            this.serviceOwsAlgorithm = serviceOwsAlgorithm;
        }

        public String getServiceBaseURL() {
            return serviceBaseURL;
        }

        public void setServiceBaseURL(String serviceBaseURL) {
            this.serviceBaseURL = serviceBaseURL;
        }


        public int getServiceTimeOut() {
            return serviceTimeOut;
        }

        public void setServiceTimeOut(int serviceTimeOut) {
            this.serviceTimeOut = serviceTimeOut;
        }
}
