package com.onepay.ma.service.models;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public class Promotion {
    private int n_id;
    private String promotion_id;
    private String promotion_name;
    private String description;
    private Timestamp start_time;
    private Timestamp end_time;
    private int status;
    private Timestamp approve_time;
    private Timestamp create_time;
    private String approve_user;
    private String create_user;
    private boolean expired;
    private List<PromotionDiscount> discounts;
    private List<PromotionMerchant> merchants;
    private List<PromotionRule> rules;
    private String currency_code;

    public String getCurrency_code() {
        return currency_code;
    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public String getPromotion_name() {
        return promotion_name;
    }

    public void setPromotion_name(String promotion_name) {
        this.promotion_name = promotion_name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getStart_time() {
        return start_time;
    }

    public void setStart_time(Timestamp start_time) {
        this.start_time = start_time;
    }

    public Timestamp getEnd_time() {
        return end_time;
    }

    public void setEnd_time(Timestamp end_time) {
        this.end_time = end_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Timestamp getApprove_time() {
        return approve_time;
    }

    public void setApprove_time(Timestamp approve_time) {
        this.approve_time = approve_time;
    }

    public String getApprove_user() {
        return approve_user;
    }

    public void setApprove_user(String approve_user) {
        this.approve_user = approve_user;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Timestamp getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Timestamp create_time) {
        this.create_time = create_time;
    }

    public int getN_id() {
        return n_id;
    }

    public void setN_id(int n_id) {
        this.n_id = n_id;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean expired) {
        this.expired = expired;
    }

    public List<PromotionDiscount> getDiscounts() {
        return discounts;
    }

    public void setDiscounts(List<PromotionDiscount> discounts) {
        this.discounts = discounts;
    }

    public List<PromotionMerchant> getMerchants() {
        return merchants;
    }

    public void setMerchants(List<PromotionMerchant> merchants) {
        this.merchants = merchants;
    }

    public List<PromotionRule> getRules() {
        return rules;
    }

    public void setRules(List<PromotionRule> rules) {
        this.rules = rules;
    }
}
