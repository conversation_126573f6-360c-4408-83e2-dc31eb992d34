package com.onepay.ma.service.models.user;

import com.onepay.ma.service.models.Merchant;

import java.util.List;

/**
 * Created by anhkh on 10-Apr-18.
 */
public class UserProfile {
    private String s_id;
    private String name;
    private String email;
    private String phone;
    private Boolean show_risk_assetment;
    private List<Merchant> merchants;
    private List<String> functions;

    public Boolean getShow_risk_assetment() {
        return show_risk_assetment;
    }

    public void setShow_risk_assetment(Boolean show_risk_assetment) {
        this.show_risk_assetment = show_risk_assetment;
    }

    public String getS_id() {
        return s_id;
    }

    public void setS_id(String s_id) {
        this.s_id = s_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<Merchant> getMerchants() {
        return merchants;
    }

    public void setMerchants(List<Merchant> merchants) {
        this.merchants = merchants;
    }

    public List<String> getFunctions() {
        return functions;
    }

    public void setFunctions(List<String> functions) {
        this.functions = functions;
    }
}
