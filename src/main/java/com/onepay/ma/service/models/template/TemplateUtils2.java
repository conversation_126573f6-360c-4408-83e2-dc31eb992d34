package com.onepay.ma.service.models.template;

/**
 * Created by anhkh on 05-Jun-17.
 */
public  class TemplateUtils2 {

    public static final String CDR_TEMPLATE_SRC = "template/temp_cdr.xls";

    public static final String INTERNATIONAL_TRANSACTION_TEMPLATE_SRC = "template/temp_international_transaction.xls";

    public static final String INTERNATIONAL_TRANSACTION_TEMPLATE_NEW_SRC = "template/temp_international_transaction_new.xls";

    public static final String INTERNATIONAL_REFUND_TEMPLATE_SRC = "template/temp_international_refund.xls";

    public static final String INTERNATIONAL_REPORT_TEMPLATE_SRC = "template/temp_international_report.xls";

    public static final String INTERNATIONAL_REPORT_MERCHANT_TEMPLATE_SRC = "template/update_v2/temp_international_report_2.xlsx";

    public static final String INTERNATIONAL_REFUND_EMAIL_TEMPLATE_SRC = "template/temp_international_refund_template.xls";

    public static final String INTERNATIONAL_REPORT_DETAIL_TEMPLATE_SRC = "template/update_v2/temp_international_report_detail.xlsx";


    public static final String DOMESTIC_TRANSACTION_TEMPLATE_SRC = "template/update_v2/temp_domestic_transaction.xlsx";
    public static final String DOMESTIC_TRANSACTION_TEMPLATE_SRC_VI = "template/vi/vi_temp_domestic_transaction.xlsx";
    public static final String DOMESTIC_TRANSACTION_SAMSUNG_TEMPLATE_SRC = "template/update_v2/temp_domestic_transaction_samsung.xlsx";

    public static final String DOMESTIC_REFUND_TEMPLATE_SRC = "template/update_v2/temp_domestic_refund.xlsx";
    public static final String DOMESTIC_REFUND_TEMPLATE_SRC_VI = "template/vi/vi_temp_domestic_refund.xlsx";

    public static final String DOMESTIC_REPORT_TEMPLATE_SRC = "template/update_v2/temp_domestic_report.xlsx";

    public static final String DOMESTIC_REPORT_MERCHANT_TEMPLATE_SRC = "template/update_v2/temp_domestic_report_2.xlsx";

    public static final String MPA_PR_REPORT_TEMPLATE_SRC = "template/temp_mpay_pr_report.xls";

    public static final String MPA_PR_TRANSACTION_TEMPLATE_SRC = "template/temp_mpay_pr_transaction.xls";

    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_PM = "template/update_v2/temp_mpay_transaction.xlsx";
    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_PM_VI = "template/vi/vi_temp_mpay_transaction.xlsx";

    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_MA = "template/update_v2/temp_mpay_transaction_ma.xlsx";
    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_MA_VI = "template/vi/vi_temp_mpay_transaction_ma.xlsx";

    public static final String SAMSUNG_MPAY_TRANSACTION_TEMPLATE_SRC_MA = "template/update_v2/temp_samsung_mpay_transaction_ma.xlsx";

    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_SS = "template/update_v2/temp_mpay_transaction_ss.xlsx";
    public static final String MPAY_TRANSACTION_TEMPLATE_SRC_SS_VI = "template/vi/vi_temp_mpay_transaction_ss.xlsx";

    public static final String MOCA_MPAY_TRANSACTION_TEMPLATE_SRC_MA = "template/update_v2/temp_mpay_transaction_ma_moca.xlsx";
    public static final String MOCA_MPAY_TRANSACTION_TEMPLATE_SRC_MA_VI = "template/vi/vi_temp_mpay_transaction_ma_moca.xlsx";

    public static final String MPAY_REFUND_TEMPLATE_SRC_PM = "template/update_v2/temp_mpay_refund.xlsx";
    public static final String MPAY_REFUND_TEMPLATE_SRC_PM_VI = "template/vi/vi_temp_mpay_refund.xlsx";

    public static final String MPAY_REFUND_TEMPLATE_SRC_MA = "template/update_v2/temp_mpay_refund_ma.xlsx";
    public static final String MPAY_REFUND_TEMPLATE_SRC_MA_VI = "template/vi/vi_temp_mpay_refund_ma.xlsx";

    public static final String MPAY_REFUND_TEMPLATE_SRC_SS = "template/update_v2/temp_mpay_refund_ss.xlsx";
    public static final String MPAY_REFUND_TEMPLATE_SRC_SS_VI = "template/vi/vi_temp_mpay_refund_ss.xlsx";

    public static final String MPAY_REPORT_TEMPLATE_SRC_SS = "template/temp_mpay_report_ss.xls";
    public static final String MPAY_REPORT_TEMPLATE_SRC_MA = "template/temp_mpay_report_ma.xls";
    public static final String MPAY_REPORT_PAYMENT_TEMPLATE_SRC = "template/update_v2/temp_mpay_report_payment.xlsx";

    public static final String PAYMENT_RECONCILE_TEMPLATE_SRC = "template/temp_payment_reconcile.xls";

    public static final String GENERAL_TRANSACTION_TEMPLATE_SRC = "template/update_v2/temp_general_transaction.xlsx";
    public static final String GENERAL_TRANSACTION_TEMPLATE_SRC_VI = "template/vi/vi_temp_general_transaction.xlsx";

    public static final String BILLING_TRANSACTION_TEMPLATE_SRC = "template/temp_billing_transaction.xls";
    public static final String CHART_DOMESTIC_TEMPLATE_SRC = "template/statistic_chart/temp_chart_domestic.xls";
    public static final String CHART_INTERNATIONAL_TEMPLATE_SRC = "template/statistic_chart/temp_chart_international.xls";
    public static final String CHART_TOTAL_TEMPLATE_SRC = "template/statistic_chart/temp_chart_overview.xls";
    public static final String CHART_TOTAL_INTERNATIONAL_TEMPLATE_SRC = "template/statistic_chart/temp_chart_international_overview.xls";
    public static final String CHART_TOTAL_DOMESTIC_TEMPLATE_SRC = "template/statistic_chart/temp_chart_domestic_overview.xls";
    public static final String PAYMENT_RECONCILE_DETAIL_TEMPLATE_SRC = "template/temp_payment_reconcile_detail.xls";

    public static final String PR_TRANSACTION_TEMPLATE_SRC = "template/update_v2/temp_promotion_transaction.xlsx";
    public static final String PR_TRANSACTION_TEMPLATE_SRC_VI = "template/vi/vi_temp_promotion_transaction.xlsx";
    public static final String FINANCIAL_TRANSACTION_TEMPLATE_SRC = "template/update_v2/temp_finanical_transaction.xlsx";
    public static final String FINANCIAL_TRANSACTION_TEMPLATE_SRC_VI = "template/vi/vi_temp_finanical_transaction.xlsx";
    public static final String FINANCIAL_TRANSACTION_SAMSUNG_TEMPLATE_SRC = "template/update_v2/temp_finanical_transaction_samsung.xlsx";
    public static final String FINANCIAL_TRANSACTION_SAMSUNG_TEMPLATE_SRC_VI = "template/vi/vi_temp_finanical_transaction_samsung.xlsx";
    public static final String FINANCIAL_TRANSACTION_ADAYROI_TEMPLATE_SRC = "template/update_v2/temp_finanical_transaction_adayroi.xlsx";
    public static final String FINANCIAL_TRANSACTION_ADAYROI_TEMPLATE_SRC_VI = "template/vi/vi_temp_finanical_transaction_adayroi.xls";
    public static final String INTERNATIONAL_AUTHPAYMENT_TEMPLATE_SRC = "template/update_v2/temp_international_authpayment.xlsx";
    public static final String INTERNATIONAL_AUTHPAYMENT_TEMPLATE_SRC_VI = "template/vi/vi_temp_international_authpayment.xlsx";
    public static final String PAYCOLLECT_USER_CONFIG_TEMPLATE_SRC = "template/payCollect/temp_paycollect_user_config.xls";
    public static final String PAY_COLLECT_REPORT_TRANSACTION_TEMPLATE_SRC = "template/payCollect/temp_pay_collect_transaction_report.xls";
    public static final String TEMPLATE_PAY_COLLECT_REPORT_FILE_SRC = "template/payCollect/temp_pay_collect_report.xls";
    public static final String STATISTICS_PDF_TEMPLATE_SRC = "template/onerecon/temp_statistics_report.jasper";
    public static final String STATISTICS_XLSX_TEMPLATE_SRC = "template/onerecon/temp_statistics_report_XLSX.jasper";
    public static final String STATISTICS_DETAIL_PDF_TEMPLATE_SRC = "template/onerecon/statistics_detail_pdf.jasper";
    public static final String STATISTICS_DETAIL_XLSX_TEMPLATE_SRC = "template/onerecon/statistics_detail_xlsx.jasper";
    public static final String STATISTICS_DETAIL_DT_PDF_TEMPLATE_SRC = "template/onerecon/statistics_detail_dt_pdf.jasper";
    public static final String STATISTICS_DETAIL_DT_XLSX_TEMPLATE_SRC = "template/onerecon/statistics_detail_dt_xlsx.jasper";
    public static final String SUMMARY_FUNDS_TRANFER_TEMPLATE_SRC = "template/pay_out/temp_summary_funds_tranfer.xls";
    public static final String FUNDS_TRANFER_HISTORY_TEMPLATE_SRC = "template/pay_out/temp_payout_funds_trans_history.xls";
    public static final String ACCOUNT_REPORT_TEMPLATE_SRC = "template/temp_account_report.xls";
    public static final String ACCOUNT_STATEMENT_TEMPLATE_SRC = "template/temp_account_statement.xls";
    public static final String FIRM_BANKING_TRANSACTION_TEMPLATE_SRC = "template/temp_firm_banking_transaction.xls";
    public static final String PAYMENT_LINK_TEMPLATE_SRC = "template/en/temp_paymentlink.xlsx";
    public static final String PAYMENT_LINK_TEMPLATE_SRC_VI = "template/vi/temp_paymentlink.xlsx";

    public static final String ROW_NUMBER_COLUMN = "NO";

    public static final String MID_NO_COLUMN = "S_MID_NO";
    public static final String MERCHANT_ID_COLUMN = "S_MERCHANT_ID";
    public static final String ONECOM_MERCHANT_ID_COLUMN = "S_ONECOM_MERCHANT_ID";
    public static final String MERCHANT_NAME_COLUMN = "S_MERCHANT_NAME";

    public static final String MERCHANT_TRANSACTION_REF_COLUMN = "S_MERCHANT_TRANSACTION_REF";

    public static final String N_TRANSACTION_ID_COLUMN = "N_TRANSACTION_ID";

    public static final String PR_ID_COLUMN = "S_PROMOTION_ID";

    public static final String TRANSACTION_ID_COLUMN = "S_TRANSACTION_ID";

    public static final String CUSTOMER_TRANSACTION_ID_COLUMN = "S_CUSTOMER_TRANS_ID";

    public static final String TRANSACTION_COUNT_COLUMN = "N_TRANSACTION";

    public static final String VOID_PURCHASE_COUNT_COLUMN = "N_VOID_PURCHASE";

    public static final String PURCHASE_COUNT_COLUMN = "N_PURCHASE";

    public static final String REFUND_COUNT_COLUMN = "N_REFUND";

    public static final String PURCHASE_TOTAL_COUNT_COLUMN = "N_TOTAL_PURCHASE";

    public static final String REFUND_TOTAL_COUNT_COLUMN = "N_TOTAL_REFUND";

    public static final String VOID_PURCHASE_TOTAL_COUNT_COLUMN = "N_TOTAL_VOID_PURCHASE";

    public static final String CAPTURE_TOTAL_COUNT_COLUMN = "N_TOTAL_CAPTURE";

    public static final String AUTHORISE_TOTAL_COUNT_COLUMN = "N_TOTAL_AUTHORISE";

    public static final String ORDER_INFO_COLUMN = "S_ORDER_INFO";

    public static final String CHANNEL_COLUMN = "S_CHANNEL";

    public static final String TRANS_REF_COLUMN = "S_TRANSACTION_REFERENCE";

    public static final String PR_NAME_COLUMN = "S_PR_NAME";

    public static final String CLIENT_COLUMN = "S_CLIENT";

    public static final String DATE_COLUMN = "DATE";

    public static final String DATE_SETTLEMENT_COLUMN = "DATE_SETTLEMENT";

    public static final String PURCHASE_DATE_COLUMN = "PURCHASE_DATE";

    public static final String REFUND_DATE_COLUMN = "REFUND_DATE";

    public static final String MOBILE_COLUMN = "S_MOBILE";

    public static final String CARD_NO_COLUMN = "S_CARD_NO";

    public static final String CARD_TYPE_COLUMN = "S_CARD_TYPE";

    public static final String ACQUIRER_COLUMN = "S_ACQUIRER";

    public static final String AUTH_CODE_COLUMN = "S_AUTH_CODE";

    public static final String AUTH_STATE_COLUMN = "S_AUTH_STATE";

    public static final String TRANS_TYPE_COLUMN = "S_TRANS_TYPE";

    public static final String CURRENCY_COLUMN = "S_CURRENCY";

    public static final String TOTAL_AMOUNT_COLUMN = "TOTAL_AMOUNT";

    public static final String DISCOUNT_AMOUNT_COLUMN = "DISCOUNT_AMOUNT";

    public static final String AMOUNT_COLUMN = "AMOUNT";

    public static final String BATCH_NUMBER_COLUMN = "S_BATCH_NUMBER";

    public static final String REFUND_AMOUNT_COLUMN = "REFUND_AMOUNT";

    public static final String PURCHASE_AMOUNT_COLUMN = "PURCHASE_AMOUNT";

    public static final String ORIGINAL_AMOUNT_COLUMN = "ORG_AMOUNT";

    public static final String RESPONSE_CODE_COLUMN = "S_RESPONSE_CODE";

    public static final String STATUS_COLUMN = "S_STATUS";

    public static final String ACQ_CODE_COLUMN = "S_ACQ_CODE";


    public static final String BANK_COLUMN = "S_BANK_ID";

    public static final String BANK_REF_COLUMN = "S_BANK_REF";

    public static final String ID_COLUMN = "S_ID";

    public static final String PROMOTION_ID_COLUMN = "N_PR_ID";

    public static final String TRANSACTION_DATE = "TRANSACTION_DATE";
    public static final String UPDATE_DATE = "UPDATE_DATE";
    public static final String FUND_DATE = "FUND_DATE";
    public static final String TOTAL_TRANS = "TOTAL_TRANS";
    public static final String TRANS_ID = "TRANS_ID";
    public static final String BENEFICIARY_ACCOUNT_NUMBER = "BENEFICIARY_ACCOUNT_NUMBER";
    public static final String BENEFICIARY_NAME = "BENEFICIARY_NAME";
    public static final String BENEFICIARY_BANK_NAME = "BENEFICIARY_BANK_NAME";
    public static final String BRANCH_NAME = "BRANCH_NAME";
    public static final String CONTENT = "CONTENT";
    public static final String DEBIT_AMOUNT = "DEBIT_AMOUNT";
    public static final String CREDIT_AMOUNT = "CREDIT_AMOUNT";
    public static final String BALANCE = "BALANCE";
    public static final String ONEPAY_ACCOUNT = "ONEPAY_ACCOUNT";
    public static final String ACCOUNT_NAME = "ACCOUNT_NAME";
    public static final String TRANSACTION_STATE = "TRANSACTION_STATE";
    public static final String TOTAL_BENEFICIARY_ACCOUNT_NUMBER = "TOTAL_BENEFICIARY_ACCOUNT_NUMBER";
    public static final String TOTAL_DEBIT_AMOUNT = "TOTAL_DEBIT_AMOUNT";
    public static final String TOTAL_CREDIT_AMOUNT = "TOTAL_CREDIT_AMOUNT";
    public static final String DIFFERENCE = "DIFFERENCE";
    public static final String CLOSING_BALANCE = "CLOSING_BALANCE";
    public static final String CURRENCY = "CURRENCY";

    //    ---------------payout Funds trans his Columns----------
    public static final String FUNDS_MERCHANT_ID = "MERCHANT_ID";
    public static final String MERCHANT_ACCOUNT = "MERCHANT_ACCOUNT";
    public static final String MERCHANT_NAME = "MERCHANT_NAME";
    public static final String MERCHANT_TRANS_ID = "MERCHANT_TRANS_ID";
    public static final String TRANSACTION_ID = "TRANSACTION_ID";
    public static final String BALANCE_BEFORE = "BALANCE_BEFORE";
    public static final String BALANCE_AFTER = "BALANCE_AFTER";
    public static final String RECEIVED_ACCOUNT = "RECEIVED_ACCOUNT";
    public static final String RECEIVED_ACCOUNT_NAME = "RECEIVED_ACCOUNT_NAME";
    public static final String RECEIVED_BANK = "RECEIVED_BANK";
    public static final String REMARK = "REMARK";
    public static final String BANK_TRANS_ID = "BANK_TRANS_ID";
    public static final String RESPONSE_CODE = "RESPONSE_CODE";
    public static final String MESSAGE = "MESSAGE";
    public static final String BALANCE_BANK_BEFORE = "BALANCE_BANK_BEFORE";
    public static final String BALANCE_BANK_AFTER = "BALANCE_BANK_AFTER";
    public static final String S_CHANNEL = "S_CHANNEL";
    public static final String PAYMENT_CHANNEL = "PAYMENT_CHANNEL";

}
