package com.onepay.ma.service.models;

import java.sql.Timestamp;

/**
 * Created by anhkh on 18-May-17.
 */
public class InternationalAuthPayment {
    private String transaction_id;
    private String order_info;
    private String merchant_txn_ref;
    private Amount amount;
    private InternationalCard card;
    private String merchant_id;
    private AuthenticationData authentication;
    private Timestamp transaction_time;
    private String verification_token;
    private String xid;
    private String enrolled_3ds;
    private String verification_security_level;
    private String eci;
    private String time_taken;
    private String financial_number;
    private String pares_status;
    private int row_num;
    private String onecredit_transaction_id;
    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMerchant_txn_ref() {
        return merchant_txn_ref;
    }

    public void setMerchant_txn_ref(String merchant_txn_ref) {
        this.merchant_txn_ref = merchant_txn_ref;
    }

    public String getPares_status() {
        return pares_status;
    }

    public void setPares_status(String pares_status) {
        this.pares_status = pares_status;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public InternationalCard getCard() {
        return card;
    }

    public void setCard(InternationalCard card) {
        this.card = card;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public AuthenticationData getAuthentication() {
        return authentication;
    }

    public void setAuthentication(AuthenticationData authentication) {
        this.authentication = authentication;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getVerification_token() {
        return verification_token;
    }

    public void setVerification_token(String verification_token) {
        this.verification_token = verification_token;
    }

    public String getXid() {
        return xid;
    }

    public void setXid(String xid) {
        this.xid = xid;
    }

    public String getEnrolled_3ds() {
        return enrolled_3ds;
    }

    public void setEnrolled_3ds(String enrolled_3ds) {
        this.enrolled_3ds = enrolled_3ds;
    }

    public String getVerification_security_level() {
        return verification_security_level;
    }

    public void setVerification_security_level(String verification_security_level) {
        this.verification_security_level = verification_security_level;
    }

    public String getEci() {
        return eci;
    }

    public void setEci(String eci) {
        this.eci = eci;
    }

    public String getTime_taken() {
        return time_taken;
    }

    public void setTime_taken(String time_taken) {
        this.time_taken = time_taken;
    }

    public String getFinancial_number() {
        return financial_number;
    }

    public void setFinancial_number(String financial_number) {
        this.financial_number = financial_number;
    }

    public int getRow_num() {
        return row_num;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public String getOnecredit_transaction_id() {
        return onecredit_transaction_id;
    }

    public void setOnecredit_transaction_id(String onecredit_transaction_id) {
        this.onecredit_transaction_id = onecredit_transaction_id;
    }
}
