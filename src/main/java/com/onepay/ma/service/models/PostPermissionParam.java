package com.onepay.ma.service.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/4/16.
 */
public class PostPermissionParam {
    private int parent_id;
    private String permission_name;
    private String icon;
    private boolean is_root_permission;
    private boolean is_abstract;
    private String module_name;
    private String module_url;
    private String item_name;
    private String directive_url;
    private String controller_url;
    private String description;
    private String state;
    private String path_regex;

    public int getParent_id() {
        return parent_id;
    }

    public void setParent_id(int parent_id) {
        this.parent_id = parent_id;
    }

    public String getPermission_name() {
        return permission_name;
    }

    public void setPermission_name(String permission_name) {
        this.permission_name = permission_name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean is_root_permission() {
        return is_root_permission;
    }

    public void setIs_root_permission(boolean is_root_permission) {
        this.is_root_permission = is_root_permission;
    }

    public boolean is_abstract() {
        return is_abstract;
    }

    public void setIs_abstract(boolean is_abstract) {
        this.is_abstract = is_abstract;
    }

    public String getModule_name() {
        return module_name;
    }

    public void setModule_name(String module_name) {
        this.module_name = module_name;
    }

    public String getModule_url() {
        return module_url;
    }

    public void setModule_url(String module_url) {
        this.module_url = module_url;
    }

    public String getItem_name() {
        return item_name;
    }

    public void setItem_name(String item_name) {
        this.item_name = item_name;
    }

    public String getDirective_url() {
        return directive_url;
    }

    public void setDirective_url(String directive_url) {
        this.directive_url = directive_url;
    }

    public String getController_url() {
        return controller_url;
    }

    public void setController_url(String controller_url) {
        this.controller_url = controller_url;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPath_regex() {
        return path_regex;
    }

    public void setPath_regex(String path_regex) {
        this.path_regex = path_regex;
    }
}
