package com.onepay.ma.service.models.chartStatistics;

public class ChartStatistics {
    private Integer CountS;
    private Double totalS;
    private Double totalF;
    private Integer CountF;
    private String date_name;
    private String merchantId;
    private String paygate;
    private String cardType;

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public Integer getCountS() {
        return CountS;
    }

    public void setCountS(Integer countS) {
        CountS = countS;
    }

    public Integer getCountF() {
        return CountF;
    }

    public void setCountF(Integer countF) {
        CountF = countF;
    }

    public String getDate_name() {
        return date_name;
    }

    public void setDate_name(String date_name) {
        this.date_name = date_name;
    }

    public Double getTotalS() {
        return totalS;
    }

    public void setTotalS(Double totalS) {
        this.totalS = totalS;
    }

    public Double getTotalF() {
        return totalF;
    }

    public void setTotalF(Double totalF) {
        this.totalF = totalF;
    }

}
