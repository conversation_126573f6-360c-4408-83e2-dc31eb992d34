package com.onepay.ma.service.models.paypal;

import com.onepay.ma.service.models.Amount;

import java.sql.Timestamp;
import java.util.List;

public class PaypalDispute {
    private String id;
    private Timestamp createDate;
    private Timestamp updateDate;
    private String reason;
    private String status;
    private Amount amount;
    private String outcomeCode;
    private Amount refundAmount;
    private Timestamp sellerResponseDueDate;
    private List<PaypalEvidence> evidences;
    private String disputeLifeCycleStage;
    private String disputeChannel;
    private String extensions;
    private String content;
    private Integer disputeId;

    public String getOutcomeCode() {
        return outcomeCode;
    }

    public void setOutcomeCode(String outcomeCode) {
        this.outcomeCode = outcomeCode;
    }

    public Amount getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Amount refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Timestamp getSellerResponseDueDate() {
        return sellerResponseDueDate;
    }

    public void setSellerResponseDueDate(Timestamp sellerResponseDueDate) {
        this.sellerResponseDueDate = sellerResponseDueDate;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public List<PaypalEvidence> getEvidences() {
        return evidences;
    }

    public void setEvidences(List<PaypalEvidence> evidences) {
        this.evidences = evidences;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getDisputeLifeCycleStage() {
        return disputeLifeCycleStage;
    }

    public void setDisputeLifeCycleStage(String disputeLifeCycleStage) {
        this.disputeLifeCycleStage = disputeLifeCycleStage;
    }

    public String getDisputeChannel() {
        return disputeChannel;
    }

    public void setDisputeChannel(String disputeChannel) {
        this.disputeChannel = disputeChannel;
    }

    public String getExtensions() {
        return extensions;
    }

    public void setExtensions(String extensions) {
        this.extensions = extensions;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(Integer disputeId) {
        this.disputeId = disputeId;
    }
}
