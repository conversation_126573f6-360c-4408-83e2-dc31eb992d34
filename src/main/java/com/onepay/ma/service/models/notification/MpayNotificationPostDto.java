package com.onepay.ma.service.models.notification;

import com.onepay.ma.service.models.appToken.AppToken;

/**
 * Created by anhkh on 12-Oct-17.
 */
public class MpayNotificationPostDto {
    private Integer id;
    private String content_en;
    private String content_vi;
    private String header_en;
    private String header_vi;
    private String userId;
    private String target;
    private String category;

    private String token;
    private Integer msgId;
    private String os;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getToken() {

        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getMsgId() {
        return msgId;
    }

    public void setMsgId(Integer msgId) {
        this.msgId = msgId;
    }

    public String getContent_en() {
        return content_en;
    }

    public void setContent_en(String content_en) {
        this.content_en = content_en;
    }

    public String getContent_vi() {
        return content_vi;
    }

    public void setContent_vi(String content_vi) {
        this.content_vi = content_vi;
    }

    public String getHeader_en() {
        return header_en;
    }

    public void setHeader_en(String header_en) {
        this.header_en = header_en;
    }

    public String getHeader_vi() {
        return header_vi;
    }

    public void setHeader_vi(String header_vi) {
        this.header_vi = header_vi;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
