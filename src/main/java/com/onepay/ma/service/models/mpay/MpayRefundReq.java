package com.onepay.ma.service.models.mpay;

import io.vertx.core.json.JsonObject;
/**
 * Created by anhkh on 21-Jun-18.
 */
public class MpayRefundReq {
    private String payment_id;
    private String merchant_id;
    private String client_id;
    private Double amount;
    private String currency;
    private String reference;
    private String operator;
    private String notes;
    private JsonObject promotion;
    private String direct;
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getPayment_id() {
        return payment_id;
    }

    public void setPayment_id(String payment_id) {
        this.payment_id = payment_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    /**
     * @return the notes
     */
    public String getNotes() {
        return notes;
    }

    /**
     * @param notes the notes to set
     */
    public void setNotes(String notes) {
        this.notes = notes;
    }    

    /**
     * @return String return the promotion
     */
    public JsonObject getPromotion() {
        return promotion;
    }

    /**
     * @param promotion the promotion to set
     */
    public void setPromotion(JsonObject promotion) {
        this.promotion = promotion;
    }

    /**
     * @return String return the direct
     */
    public String getDirect() {
        return direct;
    }

    /**
     * @param direct the direct to set
     */
    public void setDirect(String direct) {
        this.direct = direct;
    }

}
