package com.onepay.ma.service.models.mpay;

import java.util.List;

/**
 * Created by anhkh on 04-Jun-18.
 */
public class MpayReportConvert {

    private List<MpayReport> items;
    private int total_transaction_count;
    private int total_refund_count;
    private double total_transaction_total;
    private double total_refund_total;

    public List<MpayReport> getItems() {
        return items;
    }

    public void setItems(List<MpayReport> items) {
        this.items = items;
    }

    public int getTotal_transaction_count() {
        return total_transaction_count;
    }

    public void setTotal_transaction_count(int total_transaction_count) {
        this.total_transaction_count = total_transaction_count;
    }

    public int getTotal_refund_count() {
        return total_refund_count;
    }

    public void setTotal_refund_count(int total_refund_count) {
        this.total_refund_count = total_refund_count;
    }

    public double getTotal_transaction_total() {
        return total_transaction_total;
    }

    public void setTotal_transaction_total(double total_transaction_total) {
        this.total_transaction_total = total_transaction_total;
    }

    public double getTotal_refund_total() {
        return total_refund_total;
    }

    public void setTotal_refund_total(double total_refund_total) {
        this.total_refund_total = total_refund_total;
    }
}
