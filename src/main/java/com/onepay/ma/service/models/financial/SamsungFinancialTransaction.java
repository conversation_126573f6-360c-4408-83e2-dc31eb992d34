package com.onepay.ma.service.models.financial;

import com.onepay.ma.service.models.Amount;

import java.sql.Timestamp;

/**
 * Created by tuydv on 09-5-18.
 */
public class SamsungFinancialTransaction {

    private Integer row_num;
    private Integer id;
    private String trans_no;
    private Timestamp date;
    private String merchant_id;
    private String acquirer;
    private String trans_ref;
    private String trans_type;
    private Amount amount;
    private String  currency;
    private String response;
    private String order_ref;
    private String card_number;
    private String auth_code;
    private String payment_auth_id;
    private String batch_number;
    private String tranaction_id;
    private String card_type;
    private String advance_status;
    private String base_status;

    // SAMSUNG
    private String customer_name;
    private String customer_mobile;
    private String customer_email;
    private String epp;
    private String fraud_check;
    private String order_status;
    private int n_fraud;
    private String order_desc;
    private String risk_assesment;

    public String getRisk_assesment() {
        return risk_assesment;
    }

    public void setRisk_assesment(String risk_assesment) {
        this.risk_assesment = risk_assesment;
    }

    public String getOrder_desc() { return order_desc; }

    public void setOrder_desc(String order_desc) { this.order_desc = order_desc; }

    public String getOrder_status() { return order_status; }

    public void setOrder_status(String order_status) { this.order_status = order_status; }

    public int getN_fraud() { return n_fraud; }

    public void setN_fraud(int n_fraud) { this.n_fraud = n_fraud; }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getEpp() {
        return epp;
    }

    public void setEpp(String epp) {
        this.epp = epp;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getBase_status() {
        return base_status;
    }

    public void setBase_status(String base_status) {
        this.base_status = base_status;
    }

    public Integer getRow_num() {
        return row_num;
    }

    public void setRow_num(Integer row_num) {
        this.row_num = row_num;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTrans_no() {
        return trans_no;
    }

    public void setTrans_no(String trans_no) {
        this.trans_no = trans_no;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getTrans_ref() {
        return trans_ref;
    }

    public void setTrans_ref(String trans_ref) {
        this.trans_ref = trans_ref;
    }

    public String getTrans_type() {
        return trans_type;
    }

    public void setTrans_type(String trans_type) {
        this.trans_type = trans_type;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getOrder_ref() {
        return order_ref;
    }

    public void setOrder_ref(String order_ref) {
        this.order_ref = order_ref;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getAuth_code() {
        return auth_code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public String getPayment_auth_id() {
        return payment_auth_id;
    }

    public void setPayment_auth_id(String payment_auth_id) {
        this.payment_auth_id = payment_auth_id;
    }

    public String getBatch_number() {
        return batch_number;
    }

    public void setBatch_number(String batch_number) {
        this.batch_number = batch_number;
    }

    public String getTranaction_id() {
        return tranaction_id;
    }

    public void setTranaction_id(String tranaction_id) {
        this.tranaction_id = tranaction_id;
    }

    public String getAdvance_status() {
        return advance_status;
    }

    public void setAdvance_status(String advance_status) {
        this.advance_status = advance_status;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }
}
