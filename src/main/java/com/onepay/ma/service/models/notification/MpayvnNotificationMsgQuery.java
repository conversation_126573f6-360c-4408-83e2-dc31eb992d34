package com.onepay.ma.service.models.notification;

import com.onepay.ma.service.models.base.BaseSearchCondition;

/**
 * Created by anhkh on 26-Mar-18.
 */
public class MpayvnNotificationMsgQuery extends BaseSearchCondition {

    private String state;

    /**
     * Getter for property 'state'.
     *
     * @return Value for property 'state'.
     */
    public String getState() {
        return state;
    }

    /**
     * Setter for property 'state'.
     *
     * @param state Value to set for property 'state'.
     */
    public void setState(String state) {
        this.state = state;
    }


}
