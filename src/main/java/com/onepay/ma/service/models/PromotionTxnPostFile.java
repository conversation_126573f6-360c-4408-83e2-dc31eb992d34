package com.onepay.ma.service.models;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public class PromotionTxnPostFile {
    private String keywords;
    private String from_date;
    private String to_date;
    private String transaction_id;
    private String promotion_id;
    private String promotion_code;
    private String merchant_id;
    private String card_number;
    private String card_type;
    private String order_info;
    private String currency;
    private String merchant_transaction_ref;
    private String lang;
    private String paygate;

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    /**
     * Getter for property 'promotion_code'.
     *
     * @return Value for property 'promotion_code'.
     */
    public String getPromotion_code() {
        return promotion_code;
    }

    /**
     * Setter for property 'promotion_code'.
     *
     * @param promotion_code Value to set for property 'promotion_code'.
     */
    public void setPromotion_code(String promotion_code) {
        this.promotion_code = promotion_code;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getFrom_date() {
        return from_date;
    }

    public void setFrom_date(String from_date) {
        this.from_date = from_date;
    }

    public String getTo_date() {
        return to_date;
    }

    public void setTo_date(String to_date) {
        this.to_date = to_date;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

}
