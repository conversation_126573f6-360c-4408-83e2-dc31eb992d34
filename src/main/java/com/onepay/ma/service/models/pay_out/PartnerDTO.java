package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;
import java.sql.Timestamp;

public class PartnerDTO implements Serializable {

    private String id;
    private String name;
    private String state;
    private String notifyUrl;
    private Timestamp createdDate;
    private Timestamp updatedDate;
    private String desc;
    private Integer checkApproval;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCheckApproval() {
        return checkApproval;
    }

    public void setCheckApproval(Integer checkApproval) {
        this.checkApproval = checkApproval;
    }

    public Integer getCheckOperator() {
        return checkOperator;
    }

    public void setCheckOperator(Integer checkOperator) {
        this.checkOperator = checkOperator;
    }

    private Integer checkOperator;

}
