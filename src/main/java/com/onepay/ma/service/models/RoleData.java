package com.onepay.ma.service.models;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class RoleData {
    private int role_id;
    private int parent_id;
    private String role_name;
    private String description;
    private List<PermissionRole> permissions;
    private int order;

    public RoleData() {
    }

    public int getRole_id() {
        return role_id;
    }

    public void setRole_id(int role_id) {
        this.role_id = role_id;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(String role_name) {
        this.role_name = role_name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getParent_id() {
        return parent_id;
    }

    public void setParent_id(int parent_id) {
        this.parent_id = parent_id;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public List<PermissionRole> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<PermissionRole> permissions) {
        this.permissions = permissions;
    }
}
