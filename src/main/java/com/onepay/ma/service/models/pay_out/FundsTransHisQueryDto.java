package com.onepay.ma.service.models.pay_out;

import java.io.Serializable;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 5:54 PM
 * To change this ma-web.
 */

public class FundsTransHisQueryDto implements Serializable {
    private String userId;
    private String xUserId;
    private String xRequestId;

    private String fromDate;
    private String toDate;
    private String dateOf;
    private String beneficiaryBanks;
    private String state;
    private String merchantId;
    private String funds_transfer_id;
    private String transaction_id;
    private String merchantAccount;
    private String beneficiaryAccount;
    private String downloadType;
    private Integer pageSize;
    private Integer page;
    private String lang;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getxUserId() {
        return xUserId;
    }

    public void setxUserId(String xUserId) {
        this.xUserId = xUserId;
    }

    public String getxRequestId() {
        return xRequestId;
    }

    public void setxRequestId(String xRequestId) {
        this.xRequestId = xRequestId;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getDateOf() {
        return dateOf;
    }

    public void setDateOf(String dateOf) {
        this.dateOf = dateOf;
    }

    public String getBeneficiaryBanks() {
        return beneficiaryBanks;
    }

    public void setBeneficiaryBanks(String beneficiaryBanks) {
        this.beneficiaryBanks = beneficiaryBanks;
    }

    public String getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(String downloadType) {
        this.downloadType = downloadType;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getFunds_transfer_id() {
        return funds_transfer_id;
    }

    public void setFunds_transfer_id(String funds_transfer_id) {
        this.funds_transfer_id = funds_transfer_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public String getBeneficiaryAccount() {
        return beneficiaryAccount;
    }

    public void setBeneficiaryAccount(String beneficiaryAccount) {
        this.beneficiaryAccount = beneficiaryAccount;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    /**
     * @return String return the xUserId
     */
    public String getXUserId() {
        return xUserId;
    }

    /**
     * @param xUserId the xUserId to set
     */
    public void setXUserId(String xUserId) {
        this.xUserId = xUserId;
    }

    /**
     * @return String return the xRequestId
     */
    public String getXRequestId() {
        return xRequestId;
    }

    /**
     * @param xRequestId the xRequestId to set
     */
    public void setXRequestId(String xRequestId) {
        this.xRequestId = xRequestId;
    }

    /**
     * @return String return the lang
     */
    public String getLang() {
        return lang;
    }

    /**
     * @param lang the lang to set
     */
    public void setLang(String lang) {
        this.lang = lang;
    }

}
