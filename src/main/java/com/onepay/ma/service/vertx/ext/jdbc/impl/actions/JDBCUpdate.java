package com.onepay.ma.service.vertx.ext.jdbc.impl.actions;

import io.vertx.core.Vertx;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.ext.sql.UpdateResult;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/8/16.
 */
public class JDBCUpdate extends AbstractJDBCAction<UpdateResult> {
    private static final Pattern regex = Pattern.compile("(^|\\s)insert(\\s|$)", Pattern.CASE_INSENSITIVE + Pattern.MULTILINE);

    private final String sql;
    private final JsonArray in;
    private final int timeout;

    public JDBCUpdate(Vertx vertx, JDBCStatementHelper helper, Connection connection, WorkerExecutor exec, int timeout, String sql, JsonArray in) {
        super(vertx, helper, connection, exec);
        this.sql = sql;
        this.in = in;
        this.timeout = timeout;
    }

    protected UpdateResult execute() throws SQLException {
        final boolean returKeys = regex.matcher(sql).groupCount() == 2;
        try (PreparedStatement statement = conn.prepareStatement(sql, returKeys ? Statement.RETURN_GENERATED_KEYS : Statement.NO_GENERATED_KEYS)) {
            if (timeout >= 0) {
                statement.setQueryTimeout(timeout);
            }

            helper.fillStatement(statement, this.in);
            int updated = statement.executeUpdate();


            return new UpdateResult(updated, null);
        }
    }

    @Override
    protected String name() {
        return "update";
    }
}
