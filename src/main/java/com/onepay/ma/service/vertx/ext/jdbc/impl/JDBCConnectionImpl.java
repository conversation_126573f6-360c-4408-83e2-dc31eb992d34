package com.onepay.ma.service.vertx.ext.jdbc.impl;


import com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCCallable2;
import com.onepay.ma.service.vertx.ext.sql.SQLConnection;
import io.vertx.core.*;
import io.vertx.core.impl.ContextInternal;
import io.vertx.core.json.JsonArray;
import io.vertx.core.logging.Logger;
import io.vertx.core.logging.LoggerFactory;
import io.vertx.core.spi.metrics.PoolMetrics;
import io.vertx.ext.jdbc.impl.actions.*;
import io.vertx.ext.sql.ResultSet;
import io.vertx.ext.sql.TransactionIsolation;
import io.vertx.ext.sql.UpdateResult;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by huy<PERSON>uy<PERSON> on 3/7/16.
 */
class JDBCConnectionImpl implements SQLConnection {

    private static final Logger log = LoggerFactory.getLogger(JDBCConnectionImpl.class);

    private final Vertx vertx;
    private final Connection conn;
    private final WorkerExecutor executor;
    private final PoolMetrics metrics;
    private final Object metric;

    private final JDBCStatementHelper helper;
    private final com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper helper2;

    private int timeout = -1;

    public JDBCConnectionImpl(Vertx vertx, JDBCStatementHelper helper, com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper helper2, Connection conn, PoolMetrics metrics, Object metric) {
        this.vertx = vertx;
        this.helper = helper;
        this.conn = conn;
        this.metrics = metrics;
        this.metric = metric;
        this.helper2 = helper2;
        this.executor = ((ContextInternal) vertx.getOrCreateContext()).createWorkerExecutor();
    }


    public SQLConnection callWithParams2(String sql, JsonArray params, JsonArray outputs, Handler<AsyncResult<ResultSet>> resultHandler) {
        new JDBCCallable2(vertx, helper2, conn, executor, timeout, sql, params, outputs).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection setAutoCommit(boolean autoCommit, Handler<AsyncResult<Void>> resultHandler) {
        new JDBCAutoCommit(vertx, conn, executor, autoCommit).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection execute(String sql, Handler<AsyncResult<Void>> resultHandler) {
        new JDBCExecute(vertx, conn, executor, timeout, sql).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection query(String sql, Handler<AsyncResult<ResultSet>> resultHandler) {
        new JDBCQuery(vertx, helper, conn, executor, timeout, sql, null).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection queryWithParams(String sql, JsonArray params, Handler<AsyncResult<ResultSet>> resultHandler) {
        new JDBCQuery(vertx, helper, conn, executor, timeout, sql, params).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection update(String sql, Handler<AsyncResult<UpdateResult>> resultHandler) {
        new com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCUpdate(vertx, helper2, conn, executor, timeout, sql, null).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection updateWithParams(String sql, JsonArray params, Handler<AsyncResult<UpdateResult>> resultHandler) {
        new com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCUpdate(vertx, helper2, conn, executor, timeout, sql, params).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection call(String sql, Handler<AsyncResult<ResultSet>> resultHandler) {
        new JDBCCallable(vertx, helper, conn, executor, timeout, sql, null, null).execute(resultHandler);
        return this;
    }

    @Override
    public SQLConnection callWithParams(String sql, JsonArray params, JsonArray outputs, Handler<AsyncResult<ResultSet>> resultHandler) {
        new JDBCCallable(vertx, helper, conn, executor, timeout, sql, params, outputs).execute(resultHandler);
        return this;
    }

    @Override
    public void close(Handler<AsyncResult<Void>> handler) {
        if (metrics != null) {
            metrics.end(metric, true);
        }
        new JDBCClose(vertx, conn, executor).execute(handler);
    }

    @Override
    public void close() {
        close(ar -> {
            if (ar.failed()) {
                log.error("Failure in closing connection", ar.cause());
            }
        });
    }

    @Override
    public SQLConnection commit(Handler<AsyncResult<Void>> handler) {
        new JDBCCommit(vertx, conn, executor).execute(handler);
        return this;
    }

    @Override
    public SQLConnection rollback(Handler<AsyncResult<Void>> handler) {
        new JDBCRollback(vertx, conn, executor).execute(handler);
        return this;
    }

    @Override
    public SQLConnection setQueryTimeout(int timeoutInSeconds) {
        this.timeout = timeoutInSeconds;
        return this;
    }

    @Override
    public SQLConnection setTransactionIsolation(TransactionIsolation isolation, Handler<AsyncResult<Void>> handler) {
        executor.executeBlocking((Future<Void> f) -> {
            try {
                switch (isolation) {
                    case READ_COMMITTED:
                        conn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
                        break;
                    case READ_UNCOMMITTED:
                        conn.setTransactionIsolation(Connection.TRANSACTION_READ_UNCOMMITTED);
                        break;
                    case REPEATABLE_READ:
                        conn.setTransactionIsolation(Connection.TRANSACTION_REPEATABLE_READ);
                        break;
                    case SERIALIZABLE:
                        conn.setTransactionIsolation(Connection.TRANSACTION_SERIALIZABLE);
                        break;
                    case NONE:
                        conn.setTransactionIsolation(Connection.TRANSACTION_NONE);
                        break;
                    default:
                        log.warn("Unknown isolation level " + isolation.name());
                }
                f.complete();
            } catch (SQLException e) {
                f.fail(e);
            }
        }, handler);

        return this;
    }

    @Override
    public SQLConnection getTransactionIsolation(Handler<AsyncResult<TransactionIsolation>> handler) {
        executor.executeBlocking((Future<TransactionIsolation> f) -> {
            try {
                int level = conn.getTransactionIsolation();

                switch (level) {
                    case Connection.TRANSACTION_READ_COMMITTED:
                        f.complete(TransactionIsolation.READ_COMMITTED);
                        break;
                    case Connection.TRANSACTION_READ_UNCOMMITTED:
                        f.complete(TransactionIsolation.READ_UNCOMMITTED);
                        break;
                    case Connection.TRANSACTION_REPEATABLE_READ:
                        f.complete(TransactionIsolation.REPEATABLE_READ);
                        break;
                    case Connection.TRANSACTION_SERIALIZABLE:
                        f.complete(TransactionIsolation.SERIALIZABLE);
                        break;
                    case Connection.TRANSACTION_NONE:
                        f.complete(TransactionIsolation.NONE);
                        break;
                    default:
                        f.fail("Unknown isolation level " + level);
                        break;
                }
            } catch (SQLException e) {
                f.fail(e);
            }
        }, handler);

        return this;
    }

    @Override
    public SQLConnection batch(List<String> sqlStatements, Handler<AsyncResult<List<Integer>>> handler) {
        new JDBCBatch(vertx, helper, conn, executor, sqlStatements).execute(handler);
        return this;
    }

    @Override
    public SQLConnection batchWithParams(String statement, List<JsonArray> args, Handler<AsyncResult<List<Integer>>> handler) {
        new JDBCBatch(vertx, helper, conn, executor, statement, args).execute(handler);
        return this;
    }

    @Override
    public SQLConnection batchCallableWithParams(String statement, List<JsonArray> inArgs, List<JsonArray> outArgs, Handler<AsyncResult<List<Integer>>> handler) {
        new JDBCBatch(vertx, helper, conn, executor, statement, inArgs, outArgs).execute(handler);
        return this;
    }

}