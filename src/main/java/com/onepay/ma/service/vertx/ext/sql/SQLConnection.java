package com.onepay.ma.service.vertx.ext.sql;

import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.VertxGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.ext.sql.ResultSet;
import io.vertx.ext.sql.TransactionIsolation;
import io.vertx.ext.sql.UpdateResult;

import java.io.Serializable;
import java.util.List;

/**
 * Created by huynguyen on 3/7/16.
 */
@VertxGen
public interface SQLConnection extends AutoCloseable,Serializable {

    @Fluent
    SQLConnection setAutoCommit(boolean var1, Handler<AsyncResult<Void>> var2);

    @Fluent
    SQLConnection execute(String var1, Handler<AsyncResult<Void>> var2);

    @Fluent
    SQLConnection query(String var1, Handler<AsyncResult<ResultSet>> var2);

    @Fluent
    SQLConnection queryWithParams(String var1, JsonArray var2, Handler<AsyncResult<ResultSet>> var3);

    @Fluent
    SQLConnection update(String var1, Handler<AsyncResult<UpdateResult>> var2);

    @Fluent
    SQLConnection updateWithParams(String var1, JsonArray var2, Handler<AsyncResult<UpdateResult>> var3);

    @Fluent
    SQLConnection call(String var1, Handler<AsyncResult<ResultSet>> var2);

    @Fluent
    SQLConnection callWithParams(String var1, JsonArray var2, JsonArray var3, Handler<AsyncResult<ResultSet>> var4);

    @Fluent
    SQLConnection callWithParams2(String var1, JsonArray var2, JsonArray var3, Handler<AsyncResult<ResultSet>> var4);

    void close(Handler<AsyncResult<Void>> var1);

    void close();

    @Fluent
    SQLConnection commit(Handler<AsyncResult<Void>> var1);

    @Fluent
    SQLConnection rollback(Handler<AsyncResult<Void>> var1);

    @Fluent
    SQLConnection setQueryTimeout(int var1);

    @Fluent
    SQLConnection batch(List<String> var1, Handler<AsyncResult<List<Integer>>> var2);

    @Fluent
    SQLConnection batchWithParams(String var1, List<JsonArray> var2, Handler<AsyncResult<List<Integer>>> var3);

    @Fluent
    SQLConnection batchCallableWithParams(String var1, List<JsonArray> var2, List<JsonArray> var3, Handler<AsyncResult<List<Integer>>> var4);

    @Fluent
    SQLConnection setTransactionIsolation(TransactionIsolation var1, Handler<AsyncResult<Void>> var2);

    @Fluent
    SQLConnection getTransactionIsolation(Handler<AsyncResult<TransactionIsolation>> var1);
}
