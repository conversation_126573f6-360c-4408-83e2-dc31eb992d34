package com.onepay.ma.service.vertx.rxjava.ext.jdbc;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rx.java.ObservableFuture;
import io.vertx.rx.java.RxHelper;
import io.vertx.rxjava.core.Vertx;
import rx.Observable;

import java.io.Serializable;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public class JDBCClient implements Serializable {
    final com.onepay.ma.service.vertx.ext.jdbc.JDBCClient delegate;

    public JDBCClient(com.onepay.ma.service.vertx.ext.jdbc.JDBCClient delegate) {
        this.delegate = delegate;
    }

    public Object getDelegate() {
        return this.delegate;
    }

    public static JDBCClient createNonShared(Vertx vertx, JsonObject config) {
        JDBCClient ret = newInstance(com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.createNonShared((io.vertx.core.Vertx)vertx.getDelegate(), config));
        return ret;
    }

    public static JDBCClient createShared(Vertx vertx, JsonObject config, String dataSourceName) {
        JDBCClient ret = newInstance(com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.createShared((io.vertx.core.Vertx)vertx.getDelegate(), config, dataSourceName));
        return ret;
    }

    public static JDBCClient createShared(Vertx vertx, JsonObject config) {
        JDBCClient ret = newInstance(com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.createShared((io.vertx.core.Vertx)vertx.getDelegate(), config));
        return ret;
    }

    public JDBCClient getConnection(final Handler<AsyncResult<SQLConnection>> handler) {
        this.delegate.getConnection(new Handler<AsyncResult<com.onepay.ma.service.vertx.ext.sql.SQLConnection>>() {
            @Override
            public void handle(AsyncResult<com.onepay.ma.service.vertx.ext.sql.SQLConnection> event) {
                AsyncResult f;
                if(event.succeeded()) {
                    handler.handle(io.vertx.core.Future.succeededFuture(SQLConnection.newInstance(event.result())));
                } else {
                    handler.handle(io.vertx.core.Future.failedFuture(event.cause()));
                }

            }
        });
        return this;
    }

    public Observable<SQLConnection> getConnectionObservable() {
        ObservableFuture handler = RxHelper.observableFuture();
        this.getConnection(handler.toHandler());
        return handler;
    }

    public void close() {
        this.delegate.close();
    }

    public static JDBCClient newInstance(com.onepay.ma.service.vertx.ext.jdbc.JDBCClient arg) {
        return arg != null?new JDBCClient(arg):null;
    }
}
