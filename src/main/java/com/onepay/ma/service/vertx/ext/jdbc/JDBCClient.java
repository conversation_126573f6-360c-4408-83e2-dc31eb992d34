package com.onepay.ma.service.vertx.ext.jdbc;

import com.onepay.ma.service.vertx.ext.jdbc.impl.JDBCClientImpl;
import com.onepay.ma.service.vertx.ext.sql.SQLConnection;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.VertxGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;

import javax.sql.DataSource;
import java.io.Serializable;
import java.util.UUID;

/**
 * Created by huynguyen on 3/7/16.
 */
@VertxGen
public interface JDBCClient extends Serializable {
    String DEFAULT_PROVIDER_CLASS = "io.vertx.ext.jdbc.spi.impl.C3P0DataSourceProvider";
    String DEFAULT_DS_NAME = "DEFAULT_DS";

    static JDBCClient createNonShared(Vertx vertx, JsonObject config) {
        return new JDBCClientImpl(vertx, config, UUID.randomUUID().toString());
    }

    static JDBCClient createShared(Vertx vertx, JsonObject config, String dataSourceName) {
        return new JDBCClientImpl(vertx, config, dataSourceName);
    }

    static JDBCClient createShared(Vertx vertx, JsonObject config) {
        return new JDBCClientImpl(vertx, config, "DEFAULT_DS");
    }

    @GenIgnore
    static JDBCClient create(Vertx vertx, DataSource dataSource) {
        return new JDBCClientImpl(vertx, dataSource);
    }

    @Fluent
    JDBCClient getConnection(Handler<AsyncResult<SQLConnection>> var1);

    void close();
}
