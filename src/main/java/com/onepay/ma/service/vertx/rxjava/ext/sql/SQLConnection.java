package com.onepay.ma.service.vertx.rxjava.ext.sql;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.ext.sql.ResultSet;
import io.vertx.ext.sql.UpdateResult;
import io.vertx.rx.java.ObservableFuture;
import io.vertx.rx.java.RxHelper;
import rx.Observable;

import java.io.Serializable;
import java.sql.CallableStatement;

public class SQLConnection implements Serializable {
    final com.onepay.ma.service.vertx.ext.sql.SQLConnection delegate;

    public SQLConnection(com.onepay.ma.service.vertx.ext.sql.SQLConnection delegate) {
        this.delegate = delegate;
    }

    public Object getDelegate() {
        return this.delegate;
    }

    public SQLConnection setAutoCommit(boolean autoCommit, Handler<AsyncResult<Void>> resultHandler) {
        this.delegate.setAutoCommit(autoCommit, resultHandler);
        return this;
    }

    public Observable<Void> setAutoCommitObservable(boolean autoCommit) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.setAutoCommit(autoCommit, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection execute(String sql, Handler<AsyncResult<Void>> resultHandler) {
        this.delegate.execute(sql, resultHandler);
        return this;
    }

    public Observable<Void> executeObservable(String sql) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.execute(sql, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection query(String sql, Handler<AsyncResult<ResultSet>> resultHandler) {
        this.delegate.query(sql, resultHandler);
        return this;
    }

    public Observable<ResultSet> queryObservable(String sql) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.query(sql, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection queryWithParams(String sql, JsonArray params, Handler<AsyncResult<ResultSet>> resultHandler) {
        this.delegate.queryWithParams(sql, params, resultHandler);
        return this;
    }

    public Observable<ResultSet> queryWithParamsObservable(String sql, JsonArray params) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.queryWithParams(sql, params, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection update(String sql, Handler<AsyncResult<UpdateResult>> resultHandler) {
        this.delegate.update(sql, resultHandler);
        return this;
    }

    public Observable<UpdateResult> updateObservable(String sql) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.update(sql, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection updateWithParams(String sql, JsonArray params, Handler<AsyncResult<UpdateResult>> resultHandler) {
        this.delegate.updateWithParams(sql, params, resultHandler);
        return this;
    }

    public Observable<UpdateResult> updateWithParamsObservable(String sql, JsonArray params) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.updateWithParams(sql, params, resultHandler.toHandler());
        return resultHandler;
    }


    public SQLConnection call(String sql, Handler<AsyncResult<ResultSet>> resultHandler) {
        delegate.call(sql, resultHandler);
        return this;
    }

    public Observable<ResultSet> callObservable(String sql) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.call(sql, resultHandler.toHandler());
        return resultHandler;
    }

    public SQLConnection callWithParams(String sql, JsonArray params, JsonArray outputs, Handler<AsyncResult<ResultSet>> resultHandler) {
        delegate.callWithParams(sql, params, outputs, resultHandler);
        return this;
    }

    public SQLConnection callWithParams2(String sql, JsonArray params, JsonArray outputs, Handler<AsyncResult<ResultSet>> resultHandler) {
        delegate.callWithParams2(sql, params, outputs, resultHandler);
        return this;
    }

    public Observable<CallableStatement> callWithParamsObservable(String sql, JsonArray params, JsonArray outputs) {
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.callWithParams(sql, params, outputs, resultHandler.toHandler());
        return resultHandler;
    }

    public Observable<ResultSet> callWithParamsObservable2(String sql, JsonArray params, JsonArray outputs) {
        LOGGER.info("SQL: " + sql);
        // LOGGER.info("params: " + params);
        ObservableFuture resultHandler = RxHelper.observableFuture();
        this.callWithParams2(sql, params, outputs, resultHandler.toHandler());
        return resultHandler;
    }

    public void close(Handler<AsyncResult<Void>> handler) {
        this.delegate.close(handler);
    }

    public Observable<Void> closeObservable() {
        ObservableFuture handler = RxHelper.observableFuture();
        this.close(handler.toHandler());
        return handler;
    }

    public void close() {
        this.delegate.close();
    }

    public SQLConnection commit(Handler<AsyncResult<Void>> handler) {
        this.delegate.commit(handler);
        return this;
    }

    public Observable<Void> commitObservable() {
        ObservableFuture handler = RxHelper.observableFuture();
        this.commit(handler.toHandler());
        return handler;
    }

    public SQLConnection rollback(Handler<AsyncResult<Void>> handler) {
        this.delegate.rollback(handler);
        return this;
    }

    public Observable<Void> rollbackObservable() {
        ObservableFuture handler = RxHelper.observableFuture();
        this.rollback(handler.toHandler());
        return handler;
    }

    public static SQLConnection newInstance(com.onepay.ma.service.vertx.ext.sql.SQLConnection arg) {
        return arg != null?new SQLConnection(arg):null;
    }

    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(SQLConnection.class.getName());
}
