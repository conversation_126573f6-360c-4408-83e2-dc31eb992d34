package com.onepay.ma.service.vertx.ext.jdbc.impl.actions;

import io.vertx.core.Vertx;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.ext.sql.ResultSet;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper.asList;
import static com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper.convertSqlValue;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/7/16.
 */

public class JDBCCallable2 extends AbstractJDBCAction<ResultSet> {
    private final String sql;
    private final JsonArray in;
    private final JsonArray out;
    private final int timeout;

    public JDBCCallable2(Vertx vertx, JDBCStatementHelper helper, Connection connection, WorkerExecutor exec, int timeout, String sql, JsonArray in, JsonArray out) {
        super(vertx, helper, connection, exec);
        this.sql = sql;
        this.in = in;
        this.out = out;
        this.timeout = timeout;
    }


    @Override
    protected ResultSet execute() throws SQLException {
        CallableStatement statement = this.conn.prepareCall(this.sql);
        Throwable var2 = null;

        try {
            if(this.timeout >= 0) {
                statement.setQueryTimeout(this.timeout);
            }

            this.helper.fillStatement(statement, this.in, this.out);
            boolean retResult = statement.execute();
            boolean outResult = this.out != null && this.out.size() > 0;
            if(retResult) {
                java.sql.ResultSet rs1 = statement.getResultSet();
                Throwable var6 = null;

                try {
                    Object var7;
                    try {
                        if(outResult) {
                            var7 = this.helper.asList(rs1).setOutput(this.convertOutputs(statement));
                            return (ResultSet)var7;
                        } else {
                            var7 = this.helper.asList(rs1);
                            return (ResultSet)var7;
                        }
                    } catch (Throwable var35) {
                        var7 = var35;
                        var6 = var35;
                        throw var35;
                    }
                } finally {
                    if(rs1 != null) {
                        if(var6 != null) {
                            try {
                                rs1.close();
                            } catch (Throwable var34) {
                                var6.addSuppressed(var34);
                            }
                        } else {
                            rs1.close();
                        }
                    }

                }
            } else {
                ResultSet rs;
                if(outResult) {
                    rs = (new ResultSet(Collections.emptyList(), Collections.emptyList())).setOutput(this.convertOutputs(statement));
                    return rs;
                } else {
                    rs = null;
                    return rs;
                }
            }
        } catch (Throwable var37) {
            var2 = var37;
            throw var37;
        } finally {
            if(statement != null) {
                if(var2 != null) {
                    try {
                        statement.close();
                    } catch (Throwable var33) {
                        var2.addSuppressed(var33);
                    }
                } else {
                    statement.close();
                }
            }

        }
    }

    private JsonArray convertOutputs(CallableStatement statement) throws SQLException {
        JsonArray result = new JsonArray();
        for(int i = 0; i < this.out.size(); ++i) {
            Object var = this.out.getValue(i);
            if(var != null) {
                Object value = statement.getObject(i + 1);
                if(value == null) {
                    result.addNull();
                } else if(value instanceof java.sql.ResultSet) {
                    Map resultData = new HashMap();
                    resultData.put("result_set", asList((java.sql.ResultSet)value));
                    result.add(resultData);
                } else {
                    result.add(convertSqlValue(value));
                }
            } else {
                result.addNull();
            }
        }

        return result;
    }

    protected String name() {
        return "callable";
    }
}
