package com.onepay.ma.service.vertx.ext.jdbc.impl;

import com.onepay.ma.service.vertx.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.ext.sql.SQLConnection;
import io.vertx.core.*;
import io.vertx.core.impl.VertxInternal;
import io.vertx.core.json.JsonObject;
import io.vertx.core.shareddata.LocalMap;
import io.vertx.core.shareddata.Shareable;
import io.vertx.core.spi.metrics.PoolMetrics;
import io.vertx.ext.jdbc.spi.DataSourceProvider;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public class JDBC<PERSON>lientImpl implements JDBCClient {
    private static final String DS_LOCAL_MAP_NAME = "__vertx.JDBCClient.datasources";

    private final Vertx vertx;
    private final DataSourceHolder holder;

    // We use this executor to execute getConnection requests
    private final ExecutorService exec;
    private final DataSource ds;
    private final PoolMetrics metrics;
    // Helper that can do param and result transforms, its behavior is defined by the
    // initial config and immutable after that moment. It is safe to reuse since there
    // is no state involved
    private final io.vertx.ext.jdbc.impl.actions.JDBCStatementHelper helper;
    private final com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper helper2;

    /*
    Create client with specific datasource
     */
    public JDBCClientImpl(Vertx vertx, DataSource dataSource) {
        Objects.requireNonNull(vertx);
        Objects.requireNonNull(dataSource);
        this.vertx = vertx;
        this.holder = new DataSourceHolder((VertxInternal) vertx, dataSource);
        this.exec = holder.exec();
        this.ds = dataSource;
        this.metrics = holder.metrics;
        this.helper = new io.vertx.ext.jdbc.impl.actions.JDBCStatementHelper();
        this.helper2 = new com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper();
        setupCloseHook();
    }

    /*
    Create client with shared datasource
     */
    public JDBCClientImpl(Vertx vertx, JsonObject config, String datasourceName) {
        Objects.requireNonNull(vertx);
        Objects.requireNonNull(config);
        Objects.requireNonNull(datasourceName);
        this.vertx = vertx;
        this.holder = lookupHolder(datasourceName, config);
        this.exec = holder.exec();
        this.ds = holder.ds();
        this.metrics = holder.metrics;
        this.helper = new io.vertx.ext.jdbc.impl.actions.JDBCStatementHelper(config);
        this.helper2 = new com.onepay.ma.service.vertx.ext.jdbc.impl.actions.JDBCStatementHelper(config);
        setupCloseHook();
    }

    private void setupCloseHook() {
        Context ctx = Vertx.currentContext();
        if (ctx != null && ctx.owner() == vertx) {
            ctx.addCloseHook(holder::close);
        }
    }

    @Override
    public void close() {
        holder.close(null);
    }

    @Override
    public JDBCClient getConnection(Handler<AsyncResult<SQLConnection>> handler) {
        Context ctx = vertx.getOrCreateContext();
        boolean enabled = metrics != null && metrics.isEnabled();
        Object queueMetric = enabled ? metrics.submitted() : null;
        PoolMetrics metrics = enabled ? this.metrics : null;
        exec.execute(() -> {
            Future<SQLConnection> res = Future.future();
            try {
        /*
        This can block until a connection is free.
        We don't want to do that while running on a worker as we can enter a deadlock situation as the worker
        might have obtained a connection, and won't release it until it is run again
        There is a general principle here:
        *User code* should be executed on a worker and can potentially block, it's up to the *user* to deal with
        deadlocks that might occur there.
        If the *service code* internally blocks waiting for a resource that might be obtained by *user code*, then
        this can cause deadlock, so the service should ensure it never does this, by executing such code
        (e.g. getConnection) on a different thread to the worker pool.
        We don't want to use the vert.x internal pool for this as the threads might end up all blocked preventing
        other important operations from occurring (e.g. async file access)
        */
                Connection conn = ds.getConnection();
                Object execMetric = null;
                if (metrics != null) {
                    execMetric = metrics.begin(queueMetric);
                }
                SQLConnection sconn = new JDBCConnectionImpl(vertx, helper, helper2, conn, metrics, execMetric);
                res.complete(sconn);
            } catch (SQLException e) {
                if (metrics != null) {
                    metrics.rejected(queueMetric);
                }
                res.fail(e);
            }
            ctx.runOnContext(v -> res.setHandler(handler));
        });
        return this;
    }

    private DataSourceHolder lookupHolder(String datasourceName, JsonObject config) {
        synchronized (vertx) {
            LocalMap<String, DataSourceHolder> map = vertx.sharedData().getLocalMap(DS_LOCAL_MAP_NAME);
            DataSourceHolder theHolder = map.get(datasourceName);
            if (theHolder == null) {
                theHolder = new DataSourceHolder((VertxInternal) vertx, config, () -> removeFromMap(map, datasourceName), datasourceName);
                map.put(datasourceName, theHolder);
            } else {
                theHolder.incRefCount();
            }
            return theHolder;
        }
    }

    private void removeFromMap(LocalMap<String, DataSourceHolder> map, String dataSourceName) {
        synchronized (vertx) {
            map.remove(dataSourceName);
            if (map.isEmpty()) {
                map.close();
            }
        }
    }

    private class DataSourceHolder implements Shareable {

        private final VertxInternal vertx;
        DataSourceProvider provider;
        JsonObject config;
        Runnable closeRunner;
        DataSource ds;
        PoolMetrics metrics;
        ExecutorService exec;
        int refCount = 1;
        String name;

        DataSourceHolder(VertxInternal vertx, DataSource ds) {
            this.ds = ds;
            this.metrics = vertx.metricsSPI().createMetrics(ds, "datasource", UUID.randomUUID().toString(), -1);
            this.vertx = vertx;
        }

        DataSourceHolder(VertxInternal vertx, JsonObject config, Runnable closeRunner, String name) {
            this.config = config;
            this.closeRunner = closeRunner;
            this.vertx = vertx;
            this.name = name;
        }

        synchronized DataSource ds() {
            if (ds == null) {
                String providerClass = config.getString("provider_class");
                if (providerClass == null) {
                    providerClass = DEFAULT_PROVIDER_CLASS;
                }

                if (Thread.currentThread().getContextClassLoader() != null) {
                    try {
                        // Try with the TCCL
                        Class clazz = Thread.currentThread().getContextClassLoader().loadClass(providerClass);
                        provider = (DataSourceProvider) clazz.newInstance();
                        ds = provider.getDataSource(config);
                        int poolSize = provider.maximumPoolSize(ds, config);
                        metrics = vertx.metricsSPI().createMetrics(ds, "datasource", name, poolSize);
                        return ds;
                    } catch (ClassNotFoundException e) {
                        // Next try.
                    } catch (InstantiationException | SQLException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }

                try {
                    // Try with the classloader of the current class.
                    Class clazz = this.getClass().getClassLoader().loadClass(providerClass);
                    provider = (DataSourceProvider) clazz.newInstance();
                    ds = provider.getDataSource(config);
                    int poolSize = provider.maximumPoolSize(ds, config);
                    metrics = vertx.metricsSPI().createMetrics(ds, "datasource", name, poolSize);
                    return ds;
                } catch (ClassNotFoundException | InstantiationException | SQLException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            return ds;
        }

        synchronized ExecutorService exec() {
            if (exec == null) {
                exec = new ThreadPoolExecutor(1, 1,
                        1000L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>(),
                        (r -> new Thread(r, "vertx-jdbc-service-get-connection-thread")));
            }
            return exec;
        }

        synchronized void incRefCount() {
            refCount++;
        }

        synchronized void close(Handler<AsyncResult<Void>> completionHandler) {
            if (--refCount == 0) {
                if (metrics != null) {
                    metrics.close();
                }
                Future<Void> f1 = Future.future();
                Future<Void> f2 = Future.future();
                if (completionHandler != null) {
                    CompositeFuture.all(f1, f2).<Void>map(f -> null).setHandler(completionHandler);
                }
                if (provider != null) {
                    vertx.executeBlocking(future -> {
                        try {
                            provider.close(ds);
                            future.complete();
                        } catch (SQLException e) {
                            future.fail(e);
                        }
                    }, f2.completer());
                } else {
                    f2.complete();
                }
                try {
                    if (exec != null) {
                        exec.shutdown();
                    }
                    if (closeRunner != null) {
                        closeRunner.run();
                    }
                    f1.complete();
                } catch (Throwable t) {
                    f1.fail(t);
                }
            } else {
                if (completionHandler != null) {
                    completionHandler.handle(Future.succeededFuture());
                }
            }
        }
    }
}
