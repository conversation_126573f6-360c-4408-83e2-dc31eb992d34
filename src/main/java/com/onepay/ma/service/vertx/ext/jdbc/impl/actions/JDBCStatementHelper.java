package com.onepay.ma.service.vertx.ext.jdbc.impl.actions;

import com.onepay.ma.service.models.OracleType;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.logging.Logger;
import io.vertx.core.logging.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.TimeZone;
import java.util.regex.Pattern;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public final class JDBCStatementHelper {
    private static final Logger log = LoggerFactory.getLogger(io.vertx.ext.jdbc.impl.actions.JDBCStatementHelper.class);
    private static final JsonArray EMPTY = new JsonArray(Collections.unmodifiableList(new ArrayList()));
    private static final Pattern DATETIME = Pattern.compile("^\\d{4}-(?:0[0-9]|1[0-2])-[0-9]{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z$");
    private static final Pattern DATE = Pattern.compile("^\\d{4}-(?:0[0-9]|1[0-2])-[0-9]{2}$");
    private static final Pattern TIME = Pattern.compile("^\\d{2}:\\d{2}:\\d{2}$");
    private static final Pattern UUID = Pattern.compile("^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$");

    private final boolean castUUID;

    public JDBCStatementHelper() {
        this(new JsonObject());
    }

    public JDBCStatementHelper(JsonObject config) {
        this.castUUID = config.getBoolean("castUUID", Boolean.valueOf(false)).booleanValue();
    }


    public void fillStatement(PreparedStatement statement, JsonArray in) throws SQLException {
        if(in == null) {
            in = EMPTY;
        }

        for(int i = 0; i < in.size(); ++i) {
            Object value = in.getValue(i);
            if(value != null) {
                if(value instanceof String) {
                    statement.setObject(i + 1, this.optimisticCast((String)value));
                } else {
                    statement.setObject(i + 1, value);
                }
            } else {
                statement.setNull(i + 1, 0);
            }
        }

    }

    public void fillStatement(CallableStatement statement, JsonArray in, JsonArray out) throws SQLException {
        if(in == null) {
            in = EMPTY;
        }

        if(out == null) {
            out = EMPTY;
        }

        int max = Math.max(in.size(), out.size());

        for(int i = 0; i < max; ++i) {
            Object value = null;
            if(i < in.size()) {
                value = in.getValue(i);
            }

            if(value != null) {
                statement.setObject(i + 1, value);
            } else {
                if(i < out.size()) {
                    value = out.getValue(i);
                }

                if(value != null) {
                    statement.registerOutParameter(i + 1, OracleType.valueOf(Integer.parseInt(value.toString())).getVendorTypeNumber().intValue());
                } else {
                    statement.setNull(i + 1, 0);
                }
            }
        }

    }

    public static io.vertx.ext.sql.ResultSet asList(ResultSet rs) throws SQLException {
        ArrayList columnNames = new ArrayList();
        ResultSetMetaData metaData = rs.getMetaData();
        int cols = metaData.getColumnCount();

        for(int results = 1; results <= cols; ++results) {
            columnNames.add(metaData.getColumnLabel(results));
        }

        ArrayList var8 = new ArrayList();

        while(rs.next()) {
            JsonArray result = new JsonArray();

            for(int i = 1; i <= cols; ++i) {
                Object res = convertSqlValue(rs.getObject(i));
                if(res != null) {
                    result.add(res);
                } else {
                    result.addNull();
                }
            }

            var8.add(result);
        }

        return new io.vertx.ext.sql.ResultSet(columnNames, var8);
    }

    public static Object convertSqlValue(Object value) {
        if(value == null) {
            return null;
        } else if(!(value instanceof Boolean) && !(value instanceof String) && !(value instanceof byte[])) {
            if(value instanceof Number) {
                if(value instanceof BigDecimal) {
                    BigDecimal var13 = (BigDecimal)value;
                    return var13.scale() == 0?((BigDecimal)value).toBigInteger(): Double.valueOf(((BigDecimal)value).doubleValue());
                } else {
                    return value;
                }
            } else if(!(value instanceof Date) && !(value instanceof Time) && !(value instanceof Timestamp)) {
                if(value instanceof Clob) {
                    Clob var12 = (Clob)value;

                    try {
                        String var15 = var12.getSubString(1L, (int)var12.length());
                        var12.free();
                        return var15;
                    } catch (SQLException var8) {
                        throw new RuntimeException(var8);
                    }
                } else if(value instanceof Blob) {
                    Blob var11 = (Blob)value;

                    try {
                        byte[] var14 = var11.getBytes(1L, (int)var11.length());
                        var11.free();
                        return var14;
                    } catch (SQLException var9) {
                        throw new RuntimeException(var9);
                    }
                } else {
                    if(value instanceof Array) {
                        Array a = (Array)value;

                        try {
                            Object[] e = (Object[])((Object[])a.getArray());
                            if(e != null) {
                                JsonArray jsonArray = new JsonArray();
                                Object[] var4 = e;
                                int var5 = e.length;

                                for(int var6 = 0; var6 < var5; ++var6) {
                                    Object o = var4[var6];
                                    jsonArray.add(convertSqlValue(o));
                                }

                                a.free();
                                return jsonArray;
                            }
                        } catch (SQLException var10) {
                            throw new RuntimeException(var10);
                        }
                    }

                    return value.toString();
                }
            } else {
                return value.toString();
            }
        } else {
            return value;
        }
    }

    public Object optimisticCast(String value) {
        if(value == null) {
            return null;
        } else {
            try {
                Instant e;
                int offset;
                if(TIME.matcher(value).matches()) {
                    e = LocalTime.parse(value).atDate(LocalDate.of(1970, 1, 1)).toInstant(ZoneOffset.UTC);
                    offset = TimeZone.getDefault().getOffset(e.toEpochMilli());
                    return new Time(e.toEpochMilli() - (long)offset);
                }

                if(DATE.matcher(value).matches()) {
                    e = LocalDate.parse(value).atTime(LocalTime.of(0, 0, 0, 0)).toInstant(ZoneOffset.UTC);
                    offset = TimeZone.getDefault().getOffset(e.toEpochMilli());
                    return new Date(e.toEpochMilli() - (long)offset);
                }

                if(DATETIME.matcher(value).matches()) {
                    e = Instant.from(DateTimeFormatter.ISO_INSTANT.parse(value));
                    return new Timestamp(e.toEpochMilli());
                }

                if(this.castUUID && UUID.matcher(value).matches()) {
                    return java.util.UUID.fromString(value);
                }
            } catch (RuntimeException var4) {
                log.debug(var4);
            }

            return value;
        }
    }
}