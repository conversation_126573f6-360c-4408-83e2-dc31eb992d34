package com.onepay.ma.service.vertx.ext.jdbc.impl.actions;


import io.vertx.core.*;
import io.vertx.core.logging.Logger;
import io.vertx.core.logging.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public abstract class AbstractJDBCAction<T> {

    private static final Logger log = LoggerFactory.getLogger(AbstractJDBCAction.class);

    protected final Vertx vertx;
    protected final Connection conn;
    protected final WorkerExecutor exec;
    protected final JDBCStatementHelper helper;

    protected AbstractJDBCAction(Vertx vertx, Connection conn, WorkerExecutor exec) {
        this(vertx, null, conn, exec);
    }

    protected AbstractJDBCAction(Vertx vertx, JDBCStatementHelper helper, Connection conn, WorkerExecutor exec) {
        this.vertx = vertx;
        this.conn = conn;
        this.exec = exec;
        this.helper = helper;
    }

    private void handle(Future<T> future) {
        try {
            T result = execute();
            future.complete(result);
        } catch (SQLException e) {
            future.fail(e);
        }
    }

    public void execute(Handler<AsyncResult<T>> resultHandler) {
        exec.executeBlocking(this::handle, resultHandler);
    }

    protected abstract T execute() throws SQLException;

    protected abstract String name();
}