package com.onepay.ma.service.queue.listener.excelBuilder.ext.financial;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.financial.FinancialTransactionService;
import com.onepay.ma.service.service.financial.SSFinancialTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import groovy.json.JsonException;
import io.vertx.core.json.JsonObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("samsungFinancialTransaction")
public class SamsungFinancialTransactionBuildImpl implements ReportDataBuild<FinancialTransactionQuery> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<FinancialTransactionQuery> messageData) {

        List<Map> listMap = new ArrayList<>();
        FinancialTransactionQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = SSFinancialTransactionService.downloadOnline(connOnline, param);
            rsReadonly = SSFinancialTransactionService.downloadReadonly(readOnly, param);
            this.generateRs(rsOnline, listMap);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT FINANCIAL TRANSACTION] ", ex);
            throw IErrors.FINANCIAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANTID"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getTimestamp("D_DATE"));

            item.put("D_ORIGINAL_DATE", rs.getTimestamp("D_ORIGINAL_DATE"));

            item.put("ORIGINAL_AMOUNT", rs.getDouble("N_ORIGINAL_AMOUNT"));

            // trans Id
           // item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("S_TRANSACTIONID"));

            Integer transactionId = rs.getInt("ONECREDIT_TRANS_ID");
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, transactionId==0?"":transactionId.toString());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDERREFERENCE"));

            // trans ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCHANTTRANSACTIONREFEREN"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARDNO"));

//            // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARDTYPE"));

            // BIN_COUNTRY
            item.put("BIN_COUNTRY", rs.getString("BIN_COUNTRY"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // trans type
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, rs.getString("S_TRANSACTIONTYPE"));

            // email
            item.put("S_CUSTOMER_EMAIL", rs.getString("S_CUSTOMER_EMAIL"));

            item.put("S_CUSTOMER_PHONE", rs.getString("S_CUSTOMER_PHONE"));

            item.put("S_CUSTOMER_NAME", rs.getString("S_CUSTOMER_NAME"));

           item.put("S_MERCHANT_WEBSITE", "");

            item.put("S_FRAUD", rs.getString("S_FRAUD"));
            item.put("S_RISK_ASSESSMENT", rs.getString("S_FRAUD_INFO_FOR_MER_ALERT")!=null && rs.getString("S_TRANSACTIONTYPE").equals("Purchase") ? "Review Required":"");
            // response code
            String responseCode = "";
            if (rs.getObject("S_RESPONSECODE") != null) {
                responseCode = rs.getString("S_RESPONSECODE") + "-" + PropsUtil.get(rs.getString("S_RESPONSECODE"), "");
            }
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("S_ADVANCE_STATUS"));

            // Authorization Code
            item.put(TemplateUtils.AUTH_CODE_COLUMN, rs.getString("S_AUTHORISATIONCODE"));

            // Batch Number
            item.put(TemplateUtils.BATCH_NUMBER_COLUMN, rs.getString("S_BATCHNUMBER"));

            // order status
            item.put("S_REVIEW_STATE", StringUtils.capitalize(rs.getString("S_REVIEW_STATE")));

            // NOTE
            item.put("S_NOTE", getNoteData(rs.getString("S_DATA")));

            // put into list
            listData.add(item);
        }

    }
    /**
     * Convert FromDate,ToDate Str(YYYY-MM-dd HH:mm:ss )--> Str(dd/MM/yyyy hh:mm)
     */
    private  String  convertStrDate (String strDate){
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = df.parse(strDate);

            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm");
            strDate = sdf.format(date);
        }catch (Exception e){

        }
        return strDate;
    }

     //get Note Data from a String JsonObject
     private static String getNoteData(String jsonObjectString) {
        String note = null;
        JsonObject jsonObject = null;
        if (jsonObjectString != null && !jsonObjectString.equals("")) {
            try {
                jsonObject = new JsonObject(jsonObjectString);
            }catch (JsonException err){
                Logger.getLogger("Error parse: ", err.toString());
            }
    
            try {
                note = jsonObject.getString("note");
            }catch (JsonException err){
                Logger.getLogger("Error get note data : ", err.toString());
            }
        }
        
        return note;
    }


    private static final Logger LOGGER = Logger.getLogger(SamsungFinancialTransactionBuildImpl.class.getName());
}
