package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.service.mpay.impl.MpayRefundService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import groovy.json.JsonException;
import io.vertx.core.json.JsonObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 07-Jun-18.
 */
@Service("mpayRefund")
public class MpayRefundBuildImpl implements ReportDataBuild<MpayRefundParameter> {



    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayRefundParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayRefundParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            rsOnline = MpayRefundService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param.getLang());
            rsReadonly = MpayRefundService.downloadReadonly(readOnly, param, listMap.size());
            this.generateRs(rsReadonly, listMap, param.getLang());
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT MPAY REFUND] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsBackup != null) {
                try {
                    rsBackup.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.ID_COLUMN, rs.getString("S_ID"));
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // merchant name
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, rs.getString("S_MERCHANT_NAME"));

            // onecomMerchantId
            String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT") == null ? "" : rs.getString("S_ONECOM_MERCHANT");
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, onecomMerchantId);

            // bank
            String instrumentType = Objects.equals(rs.getString("S_INSTRUMENT_TYPE"), "ewallet") ? rs.getString("S_INS_BRAND_ID")
                    : Objects.equals(rs.getString("S_INSTRUMENT_TYPE"), "")
                            || (rs.getString("S_INSTRUMENT_TYPE") == null || rs.getString("S_INSTRUMENT_TYPE").isEmpty()) ? rs.getString("S_INS_BRAND_ID") : PropsUtil.get(rs.getString("S_INSTRUMENT_TYPE"), "");
            item.put(TemplateUtils.BANK_COLUMN, instrumentType);

            item.put("S_APP_NAME", rs.getString("S_APP_NAME"));
            item.put("S_MASKING", rs.getString("S_MASKING"));
            item.put("S_INVOICE_ID", rs.getString("S_INVOICE_ID"));
            item.put("S_QR_ID", rs.getString("S_QR_ID"));

            // channel
            item.put("S_CHANNEL", rs.getString("S_CLIENT_ID") == null ? "" : handleChannel(rs.getString("S_CLIENT_ID")));

            JsonObject jData = null;
            String tid = "";
            String mid = "";
            try {
                jData = new JsonObject(rs.getString("S_DATA") != null ? rs.getString("S_DATA") : "{}");
                tid = jData.containsKey("msb_qr") && jData.getJsonObject("msb_qr").containsKey("bank_terminal_id") ? jData.getJsonObject("msb_qr").getString("bank_terminal_id") : tid;
                mid = jData.containsKey("msb_qr") && jData.getJsonObject("msb_qr").containsKey("bank_merchant_id") ? jData.getJsonObject("msb_qr").getString("bank_merchant_id") : mid;
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "parse tid mid error", e);
            }
            // tid
            item.put(TemplateUtils.TID_COLUMN, tid);

            // mid
            item.put(TemplateUtils.MID_COLUMN, mid);

            // merchantTransId
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, rs.getString("S_PAYMENT_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // Order Info
            item.put("S_CLIENT_ID", rs.getString("S_CLIENT_ID") != null ? handleChannel(rs.getString("S_CLIENT_ID")) : "");

            // S_QR_ID
            item.put("S_QR_ID", rs.getString("S_QR_ID"));

            // purchase date
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, rs.getDate("D_PURCHASE_DATE"));

            // purchase amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, rs.getString("N_PURCHASE_AMOUNT"));

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, rs.getDate("D_REFUND_DATE"));

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, rs.getString("N_REFUND_AMOUNT"));

            String status = rs.getString("S_STATE");
            if(status != null) {
                if (status.equals("approved")) status = "Successful";
                if (status.equals("pending")) status = "Waiting for OnePay's Approval";
                if (status.equals("failed")) status = "Failed";
            }

            if (status != null && lang != null && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if (rs.getString("S_STATE").equals("approved"))
                    status = "Thành công";
                else if (rs.getString("S_STATE").equals("pending"))
                    status = "Đang chờ xử lý";
                else if (rs.getString("S_STATE").equals("failed"))
                    status = "GD chờ OnePay duyệt";
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);



            // bankTransId
            item.put(TemplateUtils.BANK_REF_COLUMN, rs.getString("S_BANK_TRANS_ID"));

            // customerTransId
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, rs.getString("S_CUSTOMER_TRANS_ID"));

            // acqCode
            item.put(TemplateUtils.ACQ_CODE_COLUMN, rs.getString("ACQ_CODE"));

            // cardNumber
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_INSTRUMENT_NUMBER"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // merchant transaction ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCH_TXN_REF") == null ? StringPool.BLANK : rs.getString("S_MERCH_TXN_REF"));

            // S_INSTRUMENT_NAME
            item.put("S_INSTRUMENT_NAME", rs.getString("S_INSTRUMENT_NAME"));

            // S_INSTRUMENT_BRAND_ID
            item.put("S_INSTRUMENT_BRAND_ID", rs.getString("S_INS_BRAND_ID") == null || rs.getString("S_INS_BRAND_ID").equals("") ? "" : rs.getString("S_INS_BRAND_ID").toUpperCase());

            // NOTE
            item.put("S_NOTE", getNoteData(rs.getString("S_DESCRIPTION")));

            // put into list
            listData.add(item);
        }

    }

    // get Note Data from a String JsonObject
    private static String getNoteData(String jsonObjectString) {
        String note = null;
        JsonObject jsonObject = null;
        if (jsonObjectString != null && !jsonObjectString.equals("") && jsonObjectString.startsWith("{")) {
            try {
                jsonObject = new JsonObject(jsonObjectString);
            } catch (JsonException err) {
                Logger.getLogger("Error parse: ", err.toString());
            }

            try {
                note = jsonObject.getString("note");
            } catch (JsonException err) {
                Logger.getLogger("Error get note data : ", err.toString());
            }
        }

        return note;
    }

    private static String handleChannel(String inputData) {
        String outputData = null;
        if (inputData.equals("DSP")) {
            outputData = "mPayVN";
        } else if (inputData.equals("VIETINQR")) {
            outputData = "Vietin";
        } else if (inputData.equals("DSP_VRBANK")) {
            outputData = "VRB";
        } else if (inputData.equals("MSBQR")) {
            outputData = "MSB";
        } else {
            outputData = inputData;
        }
        return outputData;
    }


    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());
}
