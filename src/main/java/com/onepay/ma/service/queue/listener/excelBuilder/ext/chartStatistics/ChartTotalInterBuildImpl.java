package com.onepay.ma.service.queue.listener.excelBuilder.ext.chartStatistics;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay.MpayTransactionBuildImpl;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

;

@Service("chartTotalInter")
public class ChartTotalInterBuildImpl implements ReportDataBuild<ChartStatisticQuery> {
    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());
    @Autowired
    private StatisticChartService statisticChartService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ChartStatisticQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        ChartStatisticQuery query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.statisticChartService.dowloadFileTotal(readOnly, query);
            String paygate = query.getPaygate();
            this.generateRs(rsOnline, listMap,paygate);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData ,String paygate) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            if(rs.getString("PAYGATE").equals("QT")) {
                Map item = new HashMap();
                Integer iSuccess =0;
                Integer iFailed = 0;
                if(paygate.contains("QT")) {
                    String nSuccess = rs.getString("SUCCESS_TRANSACTION");
                    iSuccess = Integer.parseInt(nSuccess);
                    String nFailed = rs.getString("FAILED_TRANSACTION");
                    iFailed = Integer.parseInt(nFailed);
                }
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));
                item.put("N_TOTAL", iSuccess + iFailed);
                item.put("N_SUCCESS", iSuccess);
                item.put("N_FAILED", iFailed);
                if(paygate.contains("QT")) {
                    listData.add(item);
                }
            }
        }
    }
}
