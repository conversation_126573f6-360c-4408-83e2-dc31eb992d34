package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.InternationalTxnParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("internationalTransaction")
public class InternationalTransactionBuildImpl implements ReportDataBuild<InternationalTxnParameter> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalTxnParameter> messageData) {

        LOGGER.log(Level.INFO, "[GENERATE INTERNATIONAL TRANSACTION DATA START]: ");
        Long start = System.currentTimeMillis();
        List<Map> listMap = new ArrayList<>();
        InternationalTxnParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {

            this.calendar = Calendar.getInstance();
            rsOnline = internationalTransactionService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap);
            Long endOnl =System.currentTimeMillis();
            LOGGER.log(Level.INFO, "[GENERATE INTERNATIONAL TRANSACTION DATA ONLINE END]: " + ( endOnl- start));

            rsReadonly = internationalTransactionService.downloadReadonly(readOnly, param, listMap.size());
            this.generateRs(rsReadonly, listMap);
            LOGGER.log(Level.INFO, "[GENERATE INTERNATIONAL TRANSACTION DATA READONLY END]: " + (System.currentTimeMillis() - endOnl));
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            LOGGER.log(Level.WARNING, "[GENERATE INTERNATIONAL TRANSACTION DATA END]: " + (System.currentTimeMillis() - start));
            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("EXT_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_TRANSACTION_REFERENCE"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NO"));

            // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));

            // trans type
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, rs.getString("EXT_TYPE"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("EXT_AMOUNT"));

            // date
            Date transactionTime = rs.getDate("EXT_DATE");
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            // response code
            String responseCode = (rs.getObject("EXT_STATUS") == null) ? "" : rs.getString("EXT_STATUS");
            responseCode = responseCode + "-" + PropsUtil.get(responseCode, "Blank") ;
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            // status
            String statusData = rs.getString("S_ADVANCE_STATUS");
            item.put(TemplateUtils.STATUS_COLUMN, statusData);



            // batch number
            String batchNumber = StringPool.BLANK;
            calendar.setTime(transactionTime);
            calendar.set(Calendar.HOUR_OF_DAY, 16);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.MILLISECOND, 59);
            Date cutOffTime = calendar.getTime();
            if(transactionTime.after(cutOffTime)) {
                calendar.add(Calendar.DAY_OF_YEAR, 1);
            }
            batchNumber +=  calendar.get(Calendar.YEAR);
            batchNumber +=  String.format("%02d", calendar.get(Calendar.MONTH) + 1); // The Months are numbered from 0 (January) to 11 (December).
            batchNumber +=  String.format("%02d", calendar.get(Calendar.DATE));
            item.put(TemplateUtils.BATCH_NUMBER_COLUMN, batchNumber);

            // authorization code
            item.put(TemplateUtils.AUTH_CODE_COLUMN, rs.getString("S_AUTHORISATION_CODE") ==null ? "" : rs.getString("S_AUTHORISATION_CODE"));

            // authorization state
            String state = (rs.getObject("S_AUTHENTICATION_STATE") == null) ? "" : rs.getString("S_AUTHENTICATION_STATE");
            String stateCode = StringEscapeUtils.escapeCsv(state);
            String authState = "";
            if( Arrays.asList(ParamsPool.STATE_SUCCESS).contains(state)){
                authState = stateCode + "-" + "Successful";
            }else if( Arrays.asList(ParamsPool.STATE_FAILED).contains(state)){
                authState = stateCode + "-" + "Failed";
            }else if( Arrays.asList(ParamsPool.STATE_UNDETERMINE).contains(state)){
                authState = stateCode + "-" + "Undetermined";
            }else if( Arrays.asList(ParamsPool.STATE_NOT_ENROLLED).contains(state)){
                authState = stateCode + "-" +"Not Enrolled";
            }
            item.put(TemplateUtils.AUTH_STATE_COLUMN, authState);

            // put into list
            listData.add(item);
        }

    }

    @Autowired
    private InternationalTransactionService internationalTransactionService;

    private Calendar calendar;

    private static final Logger LOGGER = Logger.getLogger(InternationalTransactionBuildImpl.class.getName());
}
