package com.onepay.ma.service.queue.listener.excelBuilder.ext.statistics;

import com.onepay.commons.util.Convert;
import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.StatisticsReportService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("staticticsDetail")
public class StatisticsDetailBuildImpl implements ReportDataBuild<StatisticsReportParameter> {
    @Override
    public List<Map> generate(Connection connOnrecon, Connection readOnly, Connection connBackup, Message<StatisticsReportParameter> messageData) {

        List<Map> listMap = new ArrayList<>();
        StatisticsReportParameter param = messageData.getRequestBody();
        ResultSet rsOnerecon = null;
        try {
            rsOnerecon = statisticsReportService.downloadDetail(connOnrecon, param);
            this.generateRs(rsOnerecon, listMap);

        }catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT STATISTICS ] ", ex);
            throw IErrors.STATISTICS_SERVER_ERROR;
        } finally {

            if(rsOnerecon != null){
                try {
                    rsOnerecon.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }
        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        while (rs.next()) {
            Map item = new HashMap();
            String transDate = rs.getString("D_TRANS_DATE");
            String cardType= rs.getString("S_CARD_TYPE");
            Integer countIss = Convert.parseInt(rs.getString("COUNT_ISS"),0);
            Integer countAcq= Convert.parseInt(rs.getString("COUNT_ACQ"),0);
            Double amountIss=Convert.parseDouble(rs.getString("N_AMOUNT_ISS"),0D);
            Double amountAcq=Convert.parseDouble(rs.getString("N_AMOUNT_ACQ"),0D);
            Double issFee = Convert.parseDouble(rs.getString("N_ISS_FEE"),0D);
            Double acqFee=Convert.parseDouble(rs.getString("N_ACQ_FEE"),0D);
            Double amountRecon=amountIss-amountAcq;
            Double feeRecon =issFee -acqFee;
            try {
                String transType = rs.getString("S_TRANS_TYPE");
                //Trans Type
                item.put("transType", transType);
            }catch (Exception e){

            }
            // date Trans
            item.put("transDate", transDate);

            // Card type
            item.put("cardType", PropsUtil.get(cardType, StringPool.BLANK));

            // amount Iss
            item.put("amountIss", amountIss);

            // amountAcq
            item.put("amountAcq", amountAcq);

            // issFee
            item.put("issFee", issFee);

            // acqFee
            item.put("acqFee", acqFee);

            // amountRecon
            item.put("amountRecon", amountRecon);

            // feeRecon
            item.put("feeRecon", feeRecon);

            // countIss
            item.put("countIss", countIss);

            // countAcq
            item.put("countAcq", countAcq);

            // put into list
            listData.add(item);
        }
    }



    @Autowired
    private StatisticsReportService statisticsReportService;

    private static final Logger LOGGER = Logger.getLogger(StatisticsDetailBuildImpl.class.getName());
}
