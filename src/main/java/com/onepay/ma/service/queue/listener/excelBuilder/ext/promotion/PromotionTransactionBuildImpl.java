package com.onepay.ma.service.queue.listener.excelBuilder.ext.promotion;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.DomesticRefundParameter;
import com.onepay.ma.service.models.PromotionTransactionParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.domestic.DomesticTransactionBuildImpl;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.PromotionService;
import com.onepay.ma.service.service.PromotionTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 07-Jun-17.
 */
@Service("promotionTransaction")
public class PromotionTransactionBuildImpl implements ReportDataBuild<PromotionTransactionParameter> {

    @Override
    public List<Map> generate(Connection promotionConnection1, Connection promotionConnection2, Connection connBackup, Message<PromotionTransactionParameter> messageData) {

        List<Map> listMap = new ArrayList<>();
        PromotionTransactionParameter param = messageData.getRequestBody();
        ResultSet rs = null;
        try {
            rs = promotionTransactionService.download(promotionConnection1, param);
            this.generateRs(rs, listMap, param.getLang());
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT PROMOTION TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_TRANSACTION_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // trans ref
            item.put(TemplateUtils.PR_NAME_COLUMN, rs.getString("S_PROMOTION_NAME"));

            // card number
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NO"));

            // card typ;e
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));

            // original amount
            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, rs.getString("N_PR_ORG_AMOUNT"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_DATE"));

            // amount amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // promotion id
            item.put(TemplateUtils.PROMOTION_ID_COLUMN, Utils.getColumnString(rs, "S_PROMOTION_LIST"));

            // paygate
            String paygate = Utils.getColumnString(rs, "S_PAYGATE");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("QT".equalsIgnoreCase(paygate)) {
                    paygate = "Quốc Tế";
                } else if ("ND".equalsIgnoreCase(paygate)) {
                    paygate = "Nội Địa";
                } else if ("QR".equalsIgnoreCase(paygate)) {
                    paygate = "Ứng dụng di động";
                } else if ("BNPL".equalsIgnoreCase(paygate)) {
                    paygate = "BNPL";
                }
            } else {
                if ("QT".equalsIgnoreCase(paygate)) {
                    paygate = "International";
                } else if ("ND".equalsIgnoreCase(paygate)) {
                    paygate = "Domestic";
                } else if ("QR".equalsIgnoreCase(paygate)) {
                    paygate = "Mobile App";
                } else if ("BNPL".equalsIgnoreCase(paygate)) {
                    paygate = "BNPL";
                }
            }
            item.put(TemplateUtils.S_PAYGATE, paygate);

            // response code BNPL
            Map<String, String> responseCodeMap = new HashMap<>();
            // responseCodeMap.put("AMIGO_APPROVED", "0 - Approved");
            // responseCodeMap.put("AMIGO_CANCEL", "2 - Declined");
            // responseCodeMap.put("AMIGO_ERROR_500", "7 - System Error");
            // responseCodeMap.put("AMIGO_INVALID_API_TOKEN", "7 - System Error");
            // responseCodeMap.put("AMIGO_INVALID_INFO_ONEPAY", "7 - System Error");
            // responseCodeMap.put("AMIGO_INVALID_ORDER", "7 - System Error");
            // responseCodeMap.put("AMIGO_ORDER_IS_REFUSED", "2 - Declined");
            // responseCodeMap.put("AMIGO_CUTSOMER_NON_PERFORMING_LOAN", "2 - Declined");
            // responseCodeMap.put("AMIGO_EXCEEDING_THE_LIMIT", "5 - Credit Limit Exceeded");
            // responseCodeMap.put("AMIGO_HAVE_AN_UNDISBURSED_LOAN", "2 - Declined");

            // responseCodeMap.put("KBANK_SUCCESS", "0 - Approved");
            // responseCodeMap.put("KBANK_NEED_CUSTOMER_TO_VERIFY_OTP", "W - Waiting for authentication");
            // responseCodeMap.put("KBANK_CUSTOMER_PHONE_INVALID", "N - New Customer");
            // responseCodeMap.put("KBANK_CUSTOMER_INFO_INVALID", "3 - Invalid Customer Info");
            // responseCodeMap.put("KBANK_OTP_RESEND_LIMIT", "1 - Authentication failed");
            // responseCodeMap.put("KBANK_OTP_IS_INCORRECT", "4 - Invalid OTP");
            // responseCodeMap.put("KBANK_OTP_HAS_EXPIRED", "4 - Invalid OTP");
            // responseCodeMap.put("KBANK_OTP_INPUT_LIMIT", "1 - Authentication failed");
            // responseCodeMap.put("KBANK_SESSION_OTP_LIMIT", "1 - Authentication failed");
            // responseCodeMap.put("KBANK_TOKEN_IS_INVALID", "7 - System Error");
            // responseCodeMap.put("KBANK_TOKEN_HAS_EXPIRED", "7 - System Error");
            // responseCodeMap.put("KBANK_PUID_IS_INVALID", "7 - System Error");
            // responseCodeMap.put("KBANK_UNAUTHORIZED_INTERNAL_ERROR", "2 - Declined");
            // responseCodeMap.put("KBANK_OUTSTANDING_LOAN_EXISTED", "2 - Declined");
            // responseCodeMap.put("KBANK_PAYMENT_IS_NOT_ALLOWED", "2 - Declined");
            // responseCodeMap.put("KBANK_AMOUNT_OVER_LIMIT", "5 - Credit Limit Exceeded");
            // responseCodeMap.put("KBANK_PAYMENT_IS_INVALID", "7 - System Error");
            // responseCodeMap.put("KBANK_PARTNER_NOT_PERMISSION", "7 - System Error");
            // responseCodeMap.put("KBANK_REFUND_IS_ACCEPTED", "0 - Approved");
            // responseCodeMap.put("KBANK_CUSTOMER_CANNOT_CANCELLED", "2 - Declined");
            // responseCodeMap.put("KBANK_CANNOT_CANCELLED", "11 - Date Limit Exceeded");
            // responseCodeMap.put("KBANK_PARTNER_INVALID", "7 - System Error");
            // responseCodeMap.put("KBANK_ERROR_CODE_IS_NOT_MAP_", "-1 - Not map");
            // responseCodeMap.put("KBANK_STATUS_NOT_MAP_", "7 - System Error");
            // responseCodeMap.put("KBANK_NO_ACCESS_PERMISSION", "7 - System Error");
            // responseCodeMap.put("KBANK_PARAMETER_ERROR", "7 - System Error");
            // responseCodeMap.put("KBANK_SYSTEM_ERROR", "7 - System Error");
            // responseCodeMap.put("KBANK_CHECK_ERROR", "7 - System Error");

            responseCodeMap.put("HOMECREDIT_SUCCESS","0 - Approved" );
            responseCodeMap.put("HOMECREDIT_SYSTEM_ERROR","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_SYSTEM_COMMUNICATION_ERROR","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_MISSING_REQUIRED_FIELDS","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_INVALID_CUSTOMERTOKEN","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_CLIENT_NOT_FOUND","3 - Invalid Customer Info" );
            responseCodeMap.put("HOMECREDIT_NO_AVAILABLE_OFFER_FOUND","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_LOW_LIMIT","5 - Credit Limit Exceeded" );
            responseCodeMap.put("HOMECREDIT_FINAL_AMOUNT_IS_INVALID","5 - Credit Limit Exceeded" );
            responseCodeMap.put("HOMECREDIT_DATA_MISMATCH_FROM_GET_OFFERS","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_FLOWID_NOT_FOUND","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_FLOWID_ALREADY_USED","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_INVALID_FLOWID_OR_OFFERCODE","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_OFFERCODE_NOT_FOUND","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_GET_URL_FAILED","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_PARTNER_ORDER_ID_NOT_MATCH","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_ORIGINAL_TRANSACTION_NOT_FOUND","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_COULD_NOT_REFUND_TRANSACTION","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_UNREFUNDABLE_TRANSACTION_STATUS","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_UNREFUNDABLE_SUPPLEMENT_STATUS","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_REFUND_DATE_EXCEED_THRESHOLD","11 - Date Limit Exceeded" );
            responseCodeMap.put("HOMECREDIT_REFUND_AMOUNT_NOT_POSITIVE","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_INVALID_REFUND_AMOUNT_EXCEED_TXN_AMOUNT","12 - Amount Limit Exceeded" );
            responseCodeMap.put("HOMECREDIT_PARTNER_REFUND_ID_EXISTED","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_TRANSACTION_LOCKED","2 - Declined" );
            responseCodeMap.put("HOMECREDIT_TRANSACTION_IS_DENIED","2 - Declined" );
            responseCodeMap.put("HOMECREDIT_TRANSACTION_NOT_IN_PROGRESS","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_TRANSACTION_NOT_FOUND","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_ERROR_CODE_IS_NOT_MAP","7 - System Error" );
            responseCodeMap.put("HOMECREDIT_TRANSACTION_DECLINED","2 - Declined" );
            responseCodeMap.put("HOMECREDIT_INVALID_FINAL_AMOUNT","5 - Credit Limit Exceeded" );
            responseCodeMap.put("KREDIVO_SUCCESS","0 - Approved" );
            responseCodeMap.put("KREDIVO_INVALID_REQUEST","7 - System Error" );
            responseCodeMap.put("KREDIVO_INVALID_SERVER_KEY","7 - System Error" );
            responseCodeMap.put("KREDIVO_SOMETHING_WENT_WRONG","7 - System Error" );
            responseCodeMap.put("KREDIVO_FIELD_AMOUNT_NEGATIVE","7 - System Error" );
            responseCodeMap.put("KREDIVO_INVALID_MERCHANT","7 - System Error" );
            responseCodeMap.put("KREDIVO_CANCEL_AMOUNT_DIFFERENT","7 - System Error" );
            responseCodeMap.put("KREDIVO_CANCEL_NOT_SETTLED_TRX","2 - Declined" );
            responseCodeMap.put("KREDIVO_LOAN_ALREADY_CANCELLED","2 - Declined" );
            responseCodeMap.put("KREDIVO_CANCEL_PAST_29_DAYS","11 - Date Limit Exceeded" );
            responseCodeMap.put("KREDIVO_INVALID_TRX_TOKEN","7 - System Error" );
            responseCodeMap.put("KREDIVO_ERROR_CODE_IS_NOT_MAP","7 - System Error" );
            responseCodeMap.put("KREDIVO_DENY","2 - Declined" );
            responseCodeMap.put("KREDIVO_FAILED","253 - Timeout" );
            responseCodeMap.put("KREDIVO_CANCEL","99 - Cancelled" );
            responseCodeMap.put("KREDIVO_PENDING","W - Waiting for authentication" );
            responseCodeMap.put("KREDIVO_STATUS_IS_NOT_MAP","7 - System Error" );
            //--------------------------------------------------------------
            
            // response code
            String responseCode = rs.getString("S_RESPONSE_CODE");
            if ("bnpl".equalsIgnoreCase(paygate) && !"0".equalsIgnoreCase(responseCode)) {
                responseCode = responseCodeMap.getOrDefault(responseCode, "");
            } else {
                responseCode = responseCode + "-" + PropsUtil.get(responseCode, "Blank");
                if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    if ("0-Approved".equalsIgnoreCase(responseCode)) {
                        responseCode = "0 - Giao dịch thành công";
                    } else if ("1-Unspecified Failure".equalsIgnoreCase(responseCode)) {
                        responseCode = "1 - Lỗi không xác định";
                    } else if ("5-Invalid amount".equalsIgnoreCase(responseCode)) {
                        responseCode = "5 - Số tiền không hợp lệ";
                    } else if ("7-System Error".equalsIgnoreCase(responseCode)) {
                        responseCode = "7 - Lỗi hệ thống";
                    } else if ("8-Invalid Card No".equalsIgnoreCase(responseCode)) {
                        responseCode = "8 - Số thẻ không đúng";
                    } else if ("9-Invalid Card name".equalsIgnoreCase(responseCode)) {
                        responseCode = "9 - Tên chủ thẻ không đúng";
                    } else if ("10-Expired Card".equalsIgnoreCase(responseCode)) {
                        responseCode = "10 - Thẻ hết hạn/Thẻ bị khóa";
                    } else if ("11-Not Registered".equalsIgnoreCase(responseCode)) {
                        responseCode = "11 - Thẻ chưa đăng ký sử dụng dịch vụ";
                    } else if ("12-Invalid Card Date".equalsIgnoreCase(responseCode)) {
                        responseCode = "12 - Ngày phát hành/Hết hạn không đúng";
                    } else if ("13-Exceeded credit limit".equalsIgnoreCase(responseCode)) {
                        responseCode = "13 - Vượt quá hạn mức thanh toán";
                    } else if ("21-".equalsIgnoreCase(responseCode)) {
                        responseCode = "21 - Số dư không đủ";
                    } else if ("22-Invalid Account".equalsIgnoreCase(responseCode)) {
                        responseCode = "22 - Thông tin tài khoản không đúng";
                    } else if ("23-Account Lock".equalsIgnoreCase(responseCode)) {
                        responseCode = "23 - Tài khoản bị khóa";
                    } else if ("24-Invalid Card Info".equalsIgnoreCase(responseCode)) {
                        responseCode = "24 - Thông tin thẻ không đúng";
                    } else if ("25-".equalsIgnoreCase(responseCode)) {
                        responseCode = "25 - ";
                    } else if ("235-Transaction Timeout".equalsIgnoreCase(responseCode)) {
                        responseCode = "235 - Quá thời gian thanh toán";
                    } else if ("99-".equalsIgnoreCase(responseCode) || "99-User Cancel".equalsIgnoreCase(responseCode)) {
                        responseCode = "99 - Người dùng hủy giao dịch";
                    } else if ("100-Not Processed".equalsIgnoreCase(responseCode)) {
                        responseCode = "100 - Không được xử lý";
                    } else if ("200-Failed".equalsIgnoreCase(responseCode)) {
                        responseCode = "200 - Thất bại";
                    } else if ("Blank".equalsIgnoreCase(responseCode)) {
                        responseCode = "";
                    } else if ("Authentication failed".equalsIgnoreCase(responseCode)) {
                        responseCode = "Xác thực không thành công";
                    } else if ("Authentication cancelled".equalsIgnoreCase(responseCode)) {
                        responseCode = "Hủy xác thực";
                    } else if ("Not process".equalsIgnoreCase(responseCode)) {
                        responseCode = "Chưa được xử lý";
                    } else if ("No Response from IB payment".equalsIgnoreCase(responseCode)) {
                        responseCode = "Không có phản hồi từ IB Ngân hàng";
                    } else if ("No Select Bank".equalsIgnoreCase(responseCode)) {
                        responseCode = "Không chọn ngân hàng";
                    } else if ("Not Response".equalsIgnoreCase(responseCode)) {
                        responseCode = "Không phản hồi";
                    }
                }
            }

            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            // Transaction State
            String transactionState = "en".equalsIgnoreCase(lang) ? "Failed" : "Thất bại";
            if ((responseCode == null || responseCode.isEmpty()) && null != lang && !lang.isEmpty() && "en".equalsIgnoreCase(lang)) {
                transactionState = "Pending";
            } else if (responseCode != null && !responseCode.isEmpty() && null != lang && !lang.isEmpty() && "en".equalsIgnoreCase(lang) && responseCode.trim().substring(0, 1).equals("0")) {
                transactionState = "Successful";
            }
            if ((responseCode == null || responseCode.isEmpty())  && null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                transactionState = "Đang chờ xử lý";
            } else if (responseCode != null && !responseCode.isEmpty() && null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang) && responseCode.trim().substring(0, 1).equals("0")) {
                transactionState = "Thành công";
            }

            item.put(TemplateUtils.STATUS_COLUMN, transactionState);

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // put into list
            listData.add(item);
        }

    }

    @Autowired
    private PromotionTransactionService promotionTransactionService;


    private static final Logger LOGGER = Logger.getLogger(PromotionTransactionBuildImpl.class.getName());
}
