package com.onepay.ma.service.queue.producer;

import com.onepay.ma.service.queue.message.JMSMessageCreator;
import com.onepay.ma.service.queue.message.Message;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;

/**
 * Created by HungDX on 22-Jan-16.
 */
public class QueueProducer {

    private static JmsTemplate jmsTemplate;

    public void setJmsTemplate(JmsTemplate jmsTemplate) {
        this.jmsTemplate = jmsTemplate;
    }

    public static void sendMessage(Message message) {

        MessageCreator jmsMessageCreator = new JMSMessageCreator(message);

        jmsTemplate.send(message.getDestinationQueue(), jmsMessageCreator);


    }
}
