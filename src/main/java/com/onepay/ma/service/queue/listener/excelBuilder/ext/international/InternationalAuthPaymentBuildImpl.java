package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.InternationalAuthPaymentQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.InternationalAuthPaymentService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("internationalAuthPayment")
public class InternationalAuthPaymentBuildImpl implements ReportDataBuild<InternationalAuthPaymentQuery> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalAuthPaymentQuery> messageData) {

        List<Map> listMap = new ArrayList<>();
        InternationalAuthPaymentQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = internationalAuthPaymentService.downloadOnline(connOnline, param);
            rsReadonly = internationalAuthPaymentService.downloadReadonly(readOnly, param);
            this.generateRs(rsOnline, listMap, param.getLang());
            this.generateRs(rsReadonly, listMap, param.getLang());
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL AUTHPAYMENT TRANSACTION] ", ex);
            throw IErrors.INTERNATIONAL_AUTHPAYMENT_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANTID"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_DATE"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDERREFERENCE"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NUMBER_MASK"));

            // amount
            String amountStr =  rs.getString("S_AMOUNT");
            item.put(TemplateUtils.AMOUNT_COLUMN,amountStr==null?0: Double.valueOf(amountStr));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // Authorization Code
            String authorization = rs.getString("S_AUTHENTICATION_STATE") ==  null ? "No Response" :rs.getString("S_AUTHENTICATION_STATE") + " - " + PropsUtil.get("AUTH_" +rs.getString("S_AUTHENTICATION_STATE"), "")  ;
            authorization = authorization + " - " + PropsUtil.get("AUTH_" +authorization, "") ;
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("No Response - ".equalsIgnoreCase(authorization)) {
                    authorization = "Không phản hồi";
                } else if ("Y - Cardholder Verified - ".equalsIgnoreCase(authorization)) {
                    authorization = "Y - Đã xác minh chủ thẻ";
                } else if ("M - Verification Attempt - ".equalsIgnoreCase(authorization)) {
                    authorization = "M - Thẻ/NHPH không tham gia 3D Secure";
                } else if ("E - Cardholder Not Enrolled - ".equalsIgnoreCase(authorization)) {
                    authorization = "E - Referred";
                } else if ("N - Cardholder Not Verified - ".equalsIgnoreCase(authorization)) {
                    authorization = "N - Không xác thực được chủ";
                } else if ("T - ACS Timeout - ".equalsIgnoreCase(authorization)) {
                    authorization = "T - Quá hạn ACS";
                } else if ("A - Authentication Failed - ".equalsIgnoreCase(authorization)) {
                    authorization = "A - Xác thực không thành công";
                }
            }
            item.put(TemplateUtils.AUTH_CODE_COLUMN, authorization);

            // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));


            // Apple Pay
            item.put(TemplateUtils.SOURCE, rs.getString("S_SOURCE"));
            item.put(TemplateUtils.NETWORK_TRANS_ID, rs.getString("S_NETWORK_TRANS_ID"));

            // put into list
            listData.add(item);
        }

    }
    /**
     * Convert FromDate,ToDate Str(YYYY-MM-dd HH:mm:ss )--> Str(dd/MM/yyyy hh:mm)
     */
    private  String  convertStrDate (String strDate){
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = df.parse(strDate);

            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm");
            strDate = sdf.format(date);
        }catch (Exception e){

        }
        return strDate;
    }

    @Autowired
    private InternationalAuthPaymentService internationalAuthPaymentService;

    private static final Logger LOGGER = Logger.getLogger(InternationalAuthPaymentBuildImpl.class.getName());
}
