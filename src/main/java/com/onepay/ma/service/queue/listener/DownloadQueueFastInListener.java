package com.onepay.ma.service.queue.listener;

import com.onepay.ma.service.util.ParamsPool;

import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.File;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 22-Jan-16.
 */
public class DownloadQueueFastInListener  extends BaseDownloadQueueInListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
        onBaseMassage(message);
    }

    @Override
    void writeFile(com.onepay.ma.service.queue.message.Message<?> messageData, String ext) throws Exception {

            LOGGER.log(Level.INFO, "WRITE FILE NAME :" + fileNameHash);
            File file = new File(fileNameHash);
            long fileSize = file.length();
            messageData.getRequestData().put(ParamsPool.FILE_SIZE, fileSize);
            messageData.setResultCode(200);
            messageData.setResultString("File Has been generated");
    }

    @Override
    String fileName(String ext) {
        return this.fileNameHash;
    }


    private static final Logger LOGGER = Logger.getLogger(DownloadQueueFastInListener.class.getName());
}
