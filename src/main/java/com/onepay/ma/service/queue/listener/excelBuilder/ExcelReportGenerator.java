package com.onepay.ma.service.queue.listener.excelBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.util.StringPool;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.apache.poi.ss.formula.EvaluationCell;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
public class ExcelReportGenerator {



    private String templateFilePath;

    private Map bean;

	private String exportFileName;

    private List<String> hiddenColumn;

    private Map<String, Object> parameters;

    private List<Object> listData;


	private String exportLocation;

    private String fileName;

    /**
     * Getter for property 'templateFilePath'.
     *
     * @return Value for property 'templateFilePath'.
     */
    public String getTemplateFilePath() {
        return templateFilePath;
    }

    /**
     * Setter for property 'templateFilePath'.
     *
     * @param templateFilePath Value to set for property 'templateFilePath'.
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }

    /**
     * Getter for property 'bean'.
     *
     * @return Value for property 'bean'.
     */
    public Map getBean() {
        return bean;
    }

    /**
     * Setter for property 'bean'.
     *
     * @param bean Value to set for property 'bean'.
     */
    public void setBean(Map bean) {
        this.bean = bean;
    }

    /**
     * Getter for property 'exportFileName'.
     *
     * @return Value for property 'exportFileName'.
     */
    public String getExportFileName() {
        return exportFileName;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public List<String> getHiddenColumn() {
        return hiddenColumn;
    }

    public void setHiddenColumn(List<String> hiddenColumn) {
        this.hiddenColumn = hiddenColumn;
    }

    public void setHiddenColumnItem(String column) {
        this.hiddenColumn.add(column);
    }


    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public List<Object> getListData() {
        return listData;
    }

    public void setListData(List<Object> listData) {
        this.listData = listData;
    }

    public static Logger getLOGGER() {
        return LOGGER;
    }

    public String getExportLocation() {
        return exportLocation;
    }

    public void setExportLocation(String exportLocation) {
        this.exportLocation = exportLocation;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * Setter for property 'exportFileName'.
     *
     * @param exportFileName Value to set for property 'exportFileName'.
     */
    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }

    public void exportExcel() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                transformer.setColumnPropertyNamesToHide(this.hiddenColumn.toArray(new String[this.hiddenColumn.size()]));
            }
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, this.bean);
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }


        }


    }

    public void exportExcel2() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XSSFWorkbook templateWorkbook= new XSSFWorkbook(templateFileStream);
            XSSFSheet templateSheet = (XSSFSheet) templateWorkbook.getSheetAt(0);
            SXSSFWorkbook wb = new SXSSFWorkbook(templateWorkbook, 100);
            wb.setCompressTempFiles(true);
            SXSSFSheet sh = (SXSSFSheet) wb.getSheetAt(0); 
            sh.setDefaultColumnWidth(25);
            sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk

            //cell delete
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                CreateHugeXLSFile.deleteExcelRow(templateSheet,this.hiddenColumn,7);
            }
            
            CreateHugeXLSFile.createExcelDetail(sh, templateSheet, (Map) this.bean.get("detail"));
            CreateHugeXLSFile.createExcelData(sh,templateSheet,  (List<Map>) this.bean.get("listMap"), 7);

            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            FileOutputStream out = new FileOutputStream(exportFileName);
            wb.write(out);

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }


        }


    }
    

/**
    public void exportExcelReport() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XSSFWorkbook templateWorkbook= new XSSFWorkbook(templateFileStream);
            XSSFSheet templateSheet = (XSSFSheet) templateWorkbook.getSheetAt(0);
            SXSSFWorkbook wb = new SXSSFWorkbook(templateWorkbook, 100);
            wb.setCompressTempFiles(true);
            SXSSFSheet sh = (SXSSFSheet) wb.getSheetAt(0); 
            sh.setDefaultColumnWidth(25);
            sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk
          

            CreateHugeXLSFile.createExcelDetail(sh, templateSheet, (Map) this.bean.get("detail"));
            CreateHugeXLSFile.createExcelData(sh,templateSheet,  (List<Map>) this.bean.get("listMap"), 7);
            String [] sumCells = new String [] {"D", "E", "F", "G"};
            List<Map> temp = (List<Map>) this.bean.get("listMap");

            wb.setForceFormulaRecalculation(true);
            Row row = sh.createRow(temp.size() + 7);
            CellStyle cellStyle = sh.getRow(temp.size() + 6).getCell(3).getCellStyle();
            row.createCell(0).setCellValue("Total");
            sh.addMergedRegion(new CellRangeAddress(temp.size() + 7,temp.size() + 7,0,2));

            for(int i = 0; i < sumCells.length; i++) {
                String currentCol = sumCells[i];
                Cell cell = row.createCell(i + 3);
                String formula = "SUM(" + currentCol + "8:" + currentCol + (temp.size() + 7) + ")";
                cellStyle = sh.getRow(temp.size() + 6).getCell(i + 3).getCellStyle();
                cell.setCellFormula(formula);
                cell.setCellStyle(cellStyle);
                // evaluator.evaluate(cell);
            }
            cellStyle.setAlignment(CellStyle.ALIGN_CENTER);
            row.getCell(0).setCellStyle(cellStyle);
            row.createCell(1).setCellStyle(cellStyle);
            row.createCell(2).setCellStyle(cellStyle);
            row.createCell(7).setCellValue("VND");
            row.getCell(7).setCellStyle(cellStyle);

            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            FileOutputStream out = new FileOutputStream(exportFileName);
            wb.write(out);

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }


        }


    }
 */
    public void exportMultiExcel() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, this.bean);
            outputStream = new FileOutputStream(this.exportLocation + StringPool.FORWARD_SLASH + this.fileName + ".xls");
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }


        }


    }

    public void exportByJasperReport(String typeFile) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE PDF BY JASPER-----------------------");
        InputStream templateFileStream = null;
        templateFileStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(this.templateFilePath);
        try {
            JRBeanCollectionDataSource beanColDataSource = new JRBeanCollectionDataSource(listData);
            JasperReport jasperReport = (JasperReport) JRLoader.loadObject(templateFileStream);
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, beanColDataSource);
            // export PDF
            if (typeFile.equalsIgnoreCase("PDF")) {
                JasperExportManager.exportReportToPdfFile(jasperPrint, exportFileName);
            } else if (typeFile.equalsIgnoreCase("XLSX")) {
                JRXlsxExporter exporterXLS = new JRXlsxExporter();
                exporterXLS.setExporterInput(new SimpleExporterInput(jasperPrint));
                exporterXLS.setExporterOutput(new SimpleOutputStreamExporterOutput(exportFileName));
                SimpleXlsxReportConfiguration configuration = new SimpleXlsxReportConfiguration();
                configuration.setOnePagePerSheet(true);
                configuration.setDetectCellType(true);
                configuration.setCollapseRowSpan(false);
                exporterXLS.setConfiguration(configuration);
                exporterXLS.exportReport();
            }

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export file by Jasper", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE BY JASPER-----------------------");

        }


    }

    public void exportCsv(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader, ResultSet resultSet, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(StringPool.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(StringPool.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(StringPool.NEW_LINE);
            // Write the CSV file header
            fileWriter.append(fileHeader);

            // Add a new line separator after the header
            fileWriter.append(StringPool.NEW_LINE);
            int rowNumber = 0;
            while (resultSet.next()) {
                rowNumber++;
                fileWriter.append(String.valueOf(rowNumber));
                fileWriter.append(StringPool.COMMA);
                for (String str : listHeader) {
                    String dataField = StringPool.BLANK;
                    if (str.startsWith("D_")) {
                        Timestamp timestamp = resultSet.getTimestamp(str);
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = ("=\"" + dateFormat.format(timestamp) + "\"");
                    } else if (str.startsWith("N_")) {
                        dataField = String.valueOf(resultSet.getDouble(str));
                    } else {
                        if (resultSet.getString(str) == null || "null".equals(resultSet.getString(str)) ||
                                "".equals(resultSet.getString(str))) {
                            dataField = StringPool.BLANK;
                        } else {
                            String dataObj = resultSet.getString(str).replace("\"", "");
                            dataField = ("=\"" + dataObj + "\"");
                        }
                    }
                    fileWriter.append(StringEscapeUtils.escapeCsv(dataField));
                    fileWriter.append(StringPool.COMMA);
                }
                fileWriter.append(StringPool.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }


        }

    }


    public void exportCsv(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader,
            List<Map> listMap, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(StringPool.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(StringPool.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(StringPool.NEW_LINE);
            fileWriter.append(fileHeader);

            fileWriter.append(StringPool.NEW_LINE);
            int rowNumber = 0;
            for (Map m : listMap) {
                rowNumber++;
                fileWriter.append(String.valueOf(rowNumber));
                fileWriter.append(StringPool.COMMA);
                for (String str : listHeader) {
                    String dataField = StringPool.BLANK;

                    Object data = m.get(str);
                    if (Timestamp.class.isInstance(data)) {
                        Timestamp timestamp = (Timestamp) data;
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                        dataField = dataField == null ? StringPool.BLANK
                                : ("null".equals(dataField) ? StringPool.BLANK : ("=\"" + dataField + "\""));
                    } else if (Double.class.isInstance(data)) {
                        dataField = String.valueOf((Double) data);
                    } else if (Integer.class.isInstance(data)) {
                        dataField = String.valueOf((Integer) data);
                    } else if (Long.class.isInstance(data)) {
                        dataField = String.valueOf((Long) data);
                    } else {

                        if (data == null || "null".equals(data.toString()) ||
                                data.toString().isEmpty()) {
                            dataField = StringPool.BLANK;
                        } else {
                            String dataObj = data.toString().replace("\"", "");
                            dataField = ("=\"" + dataObj + "\"");
                        }
                    }

                    fileWriter.append(dataField == null ? StringPool.BLANK
                            : ("null".equals(dataField) ? StringPool.BLANK : StringEscapeUtils.escapeCsv(dataField)));
                    fileWriter.append(StringPool.COMMA);
                }
                fileWriter.append(StringPool.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }

        }

    }


    private static final Logger LOGGER = Logger.getLogger(ExcelReportGenerator.class.getName());
}
