package com.onepay.ma.service.queue.listener.excelBuilder.ext.statistics;

import com.onepay.commons.util.Convert;
import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.StatisticsReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("staticticsDetailDt")
public class StatisticsDetailDtBuildImpl implements ReportDataBuild<StatisticsReportParameter> {
    @Override
    public List<Map> generate(Connection connOnrecon, Connection readOnly, Connection connBackup, Message<StatisticsReportParameter> messageData) {

        List<Map> listMap = new ArrayList<>();
        StatisticsReportParameter param = messageData.getRequestBody();
        ResultSet rsOnerecon = null;
        try {
            rsOnerecon = statisticsReportService.downloadDetailDt(connOnrecon, param);
            this.generateRs(rsOnerecon, listMap);

        }catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT STATISTICS ] ", ex);
            throw IErrors.STATISTICS_SERVER_ERROR;
        } finally {

            if(rsOnerecon != null){
                try {
                    rsOnerecon.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }
        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put("rowNumber", rowNumber);

            // merchant Id
            String merchantId =rs.getString("S_MERCHANT_ID");
            item.put("merchantId", merchantId==null?"":merchantId);

            // merchant Name
            String merchantName =rs.getString("S_MERCHANT_NAME");
            item.put("merchantName", merchantName==null?"":merchantName);

            // Acq
            String acqCode =rs.getString("S_ACQ_CODE");
            item.put("acqCode", acqCode==null?"":acqCode);

            // S_CARD_TYPE
            String cardType =rs.getString("S_CARD_TYPE");
            item.put("cardType", cardType==null?"":cardType);

            // D_TRANS_DATE
            String transDate =rs.getString("D_TRANS_DATE");
            item.put("transDate", transDate==null?"":transDate);

            // Transaction Source
            String transactionSource =rs.getString("S_CLIENT_CODE");
            item.put("transactionSource", transactionSource==null?"":transactionSource);

            // S_TRANS_TYPE
            String transType =rs.getString("S_TRANS_TYPE");
            item.put("transType", transType==null?"":transType);

            // Bank Transaction ID
            String bankTransId =rs.getString("S_BANK_TRANS_ID");
            item.put("bankTransId", bankTransId==null?"":bankTransId);

            // Transaction ID (Khach hang - DSP)
            String cusTransId =rs.getString("S_CUSTOMER_TRANS_ID");
            item.put("cusTransId", cusTransId==null?"":cusTransId);

            // Merchant transaction ID (merchant - MSP)
            String merchantRef =rs.getString("S_MERCHANT_REF");
            item.put("merchantRef", merchantRef==null?"":merchantRef);


            // Merchant transaction ID (merchant - MSP)
            String onepayTransId= rs.getString("S_ONEPAY_TRANS_ID");
            item.put("onepayTransId", onepayTransId==null?"":onepayTransId);

            // S_CARD_NUMBER
            String cardNumber =rs.getString("S_CARD_NUMBER");
            item.put("cardNumber", cardNumber==null?"":cardNumber);

            // S_CURRENCY_CODE
            String currency =rs.getString("S_CURRENCY_CODE");
            item.put("currency", currency==null?"":currency);

            // N_AMOUNT_ISS
            Double amountIss=Convert.parseDouble(rs.getString("N_AMOUNT_ISS"),0D);
            Double amountAcq=Convert.parseDouble(rs.getString("N_AMOUNT_ACQ"),0D);
            Double issFee = Convert.parseDouble(rs.getString("N_ISS_FEE"),0D);
            Double acqFee=Convert.parseDouble(rs.getString("N_ACQ_FEE"),0D);
            Double percentFeeIss = Convert.parseDouble(rs.getString("N_MSO_PERCENT_ISS"),0D);
            Double percentFeeAcq=Convert.parseDouble(rs.getString("N_MSO_PERCENT_ACQ"),0D);

            // amount Iss
            item.put("amountIss", amountIss);

            // amountAcq
            item.put("amountAcq", amountAcq);

            // issFee
            item.put("issFee", issFee);

            // acqFee
            item.put("acqFee", acqFee);

            // percentFeeIss
            item.put("percentFeeIss", percentFeeIss);

            // percentFeeAcq
            item.put("percentFeeAcq", percentFeeAcq);

            // put into list
            listData.add(item);
        }
    }



    @Autowired
    private StatisticsReportService statisticsReportService;

    private static final Logger LOGGER = Logger.getLogger(StatisticsDetailDtBuildImpl.class.getName());
}
