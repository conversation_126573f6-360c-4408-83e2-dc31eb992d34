package com.onepay.ma.service.queue.listener.excelBuilder.ext.pay_out;

import com.onepay.ma.service.models.pay_out.SummaryQueryDto;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.pay_out.SummaryFundsTransService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("summary_funds_tranfer_build")
public class SummaryFundsTranferBuildImpl implements ReportDataBuild<SummaryQueryDto> {
    private static final Logger LOGGER = Logger.getLogger(SummaryFundsTranferBuildImpl.class.getName());
    @Autowired
    private SummaryFundsTransService summaryFundsTransService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<SummaryQueryDto> messageData) {
        List<Map> listMap = new ArrayList<>();
        SummaryQueryDto query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.summaryFundsTransService.dowloadfile(readOnly, query);
            this.generateRs(rsOnline, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("DATE_TYPE", rs.getString("S_DATE_TYPE"));
            item.put("MERCHANT_ID", rs.getString("S_PARTNER_ID"));
            item.put("MERCHANT_ACC", rs.getString("S_ACCOUNT_ID"));
            item.put("MERCHANT_NAME", rs.getString("S_NAME"));
            item.put("BANK_SENDER", rs.getString("S_SENDER_SWIFT_CODE"));
            item.put("BANK_RECEIPT", rs.getString("S_RECEIPT_SWIFT_CODE"));
            item.put("NO_TRANFER", rs.getLong("N_COUNT"));
            item.put("TOTAL_TRANFER", rs.getLong("N_AMOUNT"));
            listData.add(item);
        }

    }
}
