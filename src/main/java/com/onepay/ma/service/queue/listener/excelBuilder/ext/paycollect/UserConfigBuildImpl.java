package com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect;

import com.onepay.ma.service.models.pay_collect.UserQueryPayCollectDto;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.pay_collect.UserConfigService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("paycollect_user_config")
public class UserConfigBuildImpl implements ReportDataBuild<UserQueryPayCollectDto> {
    @Override
    public List<Map> generate(Connection connOnrecon, Connection readOnly, Connection connBackup,
            Message<UserQueryPayCollectDto> messageData) {

        List<Map> listMap = new ArrayList<>();
        UserQueryPayCollectDto param = messageData.getRequestBody();
        ResultSet rsOnerecon = null;
        try {
            rsOnerecon = configService.download(connOnrecon, param);
            this.generateRs(rsOnerecon, listMap, param.getLang());

        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT STATISTICS ] ", ex);
            throw IErrors.STATISTICS_SERVER_ERROR;
        } finally {

            if (rsOnerecon != null) {
                try {
                    rsOnerecon.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.WARNING, "Error Close Connection File", e);
                }
            }
        }
        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put("NO", rowNumber);
            item.put("S_USER_ID", rs.getString("S_ID"));
            item.put("S_MERCHANT_ID", rs.getString("S_PARTNER_ID"));
            item.put("S_REFERENCE", rs.getString("S_REFERENCE_ID"));
            item.put("S_NAME", rs.getString("S_FULL_NAME"));
            // item.put("S_GENDER", rs.getString("S_GENDER"));
            // item.put("S_DATE_OF_BIRTH", rs.getString("D_DATE_OF_BIRTH"));
            // item.put("S_MOBILE", rs.getString("S_MOBILE"));
            item.put("DATE", formatDate(rs.getDate("D_CREATE")));
            item.put("S_DESCRIPTION", rs.getString("S_DESC") == null ? "" : rs.getString("S_DESC"));
            item.put("S_STATUS", formatState(rs.getString("S_STATE"), lang));
            item.put("S_BANK_ACCOUNT", rs.getString("S_BANK_ACCOUNT") == null ? "" : rs.getString("S_BANK_ACCOUNT"));
            item.put("S_BANK_NAME", rs.getString("S_BANK_NAME"));
            item.put("S_BRANCH_NAME", rs.getString("S_BRANCH_NAME"));
            listData.add(item);
        }
    }

    @Autowired
    private UserConfigService configService;

    private static final Logger LOGGER = Logger.getLogger(UserConfigBuildImpl.class.getName());

    private static String formatDate(Date date) {
        SimpleDateFormat formatDate = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
        return formatDate.format(date);
    }

    private static String formatState(String state, String lang) {
        String result;
        if (state.equals("enabled"))
            result = "Enabled";
        else
            result = "Disabled";
        if (null != lang && "vi".equals(lang)) {
            if (state.equals("enabled"))
                result = "Hoạt động";
            else
                result = "Không hoạt động";
        }
        return result;
    }

}
