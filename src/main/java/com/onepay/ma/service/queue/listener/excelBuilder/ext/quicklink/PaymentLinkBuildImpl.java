package com.onepay.ma.service.queue.listener.excelBuilder.ext.quicklink;

import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.handler.externalClient.QuickLinkClient;
import com.onepay.ma.service.models.PaymentLinkDto;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.IErrors;

import org.springframework.stereotype.Service;

@Service("paymentLink")
public class PaymentLinkBuildImpl implements ReportDataBuild<PaymentLinkDto> {

    private static final Logger LOGGER = Logger.getLogger(PaymentLinkBuildImpl.class.getName());

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<PaymentLinkDto> messageData) {
        LOGGER.log(Level.INFO, "[START REPORT PAYMENT LINK LOG]");
        List<Map> listMap = new ArrayList<>();
        PaymentLinkDto param = messageData.getRequestBody();
        try {
            LOGGER.log(Level.INFO, "[START CALL ENDPOINT DOWNLOAD]");
            listMap = QuickLinkClient.download(param).getJsonArray("list").getList();
            LOGGER.log(Level.INFO, "[END CALL ENDPOINT DOWNLOAD]");
            listMap = generateList(listMap);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[REPORT PAYMENT LINK] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private List<Map> generateList(List<Map> listData) throws Exception {
        List<Map> returnList = new ArrayList<>();
        int count = 1;
        SimpleDateFormat sdf = new SimpleDateFormat("MMM d, yyyy, hh:mm:ss a");
        SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        for (Map map : listData) {
            Map<String, Object> temp = new HashMap<>();
            temp.put("count", count);
            temp.put("merchantProfileName", map.get("merchantProfileName"));
            temp.put("name", map.get("name"));
            temp.put("amount", formatNumber(Double.parseDouble(map.get("amount").toString()),map.get("currency").toString()));
            temp.put("currency", map.get("currency").toString());
            String dCreate = sdf2.format(sdf.parse(map.get("createDate") + ""));
            temp.put("createDate", dCreate);
            List<Map> listMethod = (List<Map>) map.get("paymentMethod");
            temp.put("paynowMerc", "");
            temp.put("installmentMerc", "");
            temp.put("bnplMerc", "");
            temp.put("applepayMerc", "");
            for (Map map2 : listMethod) {
                if (map2.get("method").equals("paynow")) {
                    temp.put("paynowMerc", map2.get("merchantId"));
                }
                if (map2.get("method").equals("installment")) {
                    temp.put("installmentMerc", map2.get("merchantId"));
                }
                if (map2.get("method").equals("bnpl")) {
                    temp.put("bnplMerc", map2.get("merchantId"));
                }
                if (map2.get("method").equals("applepay")) {
                    temp.put("applepayMerc", map2.get("merchantId"));
                }
            }
            String sExpired = map.get("expiredDate") + "";
            long dExpired = sdf.parse(sExpired).getTime();
            long now = new Date().getTime();
            if (dExpired < now) {
                temp.put("state", "Expired");
                temp.put("on_off", "");
            } else if (map.get("linkState").equals("approved")) {
                temp.put("state", "Enabled");
                temp.put("on_off", "On");
            } else {
                temp.put("state", "Disabled");
                temp.put("on_off", "Off");
            }
            temp.put("number_of_trans", map.get("numberOfTrans"));
            temp.put("total_amount", map.get("totalTransAmount"));

            returnList.add(temp);
            count += 1;
        }
        return returnList;
    }

    private String formatNumber(Double amount, String currency){
        DecimalFormat formatter = new DecimalFormat("###,###,###0.00");
        if(currency.equals("VND")){
            formatter = new DecimalFormat("###,###,###");
        }
        return formatter.format(amount);

    }
}
