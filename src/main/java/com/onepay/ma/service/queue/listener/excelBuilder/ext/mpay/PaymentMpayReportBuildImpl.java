package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.models.mpay.PaymentMpayReport;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.mpay.impl.PaymentMpayReportService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringPool;
import org.springframework.stereotype.Service;
import io.vertx.core.json.JsonObject;
import java.sql.Connection;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-18.
 */
@Service("mpayReportPayment")
public class PaymentMpayReportBuildImpl implements ReportDataBuild<MpayReportQuery> {

    private static final Logger LOGGER = Logger.getLogger(PaymentMpayReportBuildImpl.class.getName());

    
    private static Map<String, String> channelMap = new HashMap<String, String>() {{
        put("DSP", "mPayVN");
        put("MOMO", "MOMO");
        put("VIETINQR", "mPayVN");
        put("VIETINQR", "Vietin");
        put("DSP_VRBANK", "VRB");
        put("BIDV", "BIDV");
        put("VIB", "VIB");
        put("MSBQR", "MSB");
    }};

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayReportQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayReportQuery param = messageData.getRequestBody();
        List<PaymentMpayReport> rsReadonly = null;
        try {
            rsReadonly = PaymentMpayReportService.download(readOnly, param);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<PaymentMpayReport> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (PaymentMpayReport itemReport : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchantId());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, itemReport.getMerchantName());


            // date
            item.put(TemplateUtils.DATE_COLUMN, handleDate(itemReport.getReportDate()));

            // Card Type
            // item.put(TemplateUtils.CARD_TYPE_COLUMN, PropsUtil.get(itemReport.getCardType(), StringPool.BLANK));

            item.put(TemplateUtils.CARD_TYPE_COLUMN, itemReport.getCardType());

            // App
            item.put("S_APP_NAME", itemReport.getAppName());
            item.put("S_CLIENT_ID", channelMap.get(itemReport.getChannel()) == null ? itemReport.getChannel() : channelMap.get(itemReport.getChannel()));
            
            // tid
            item.put(TemplateUtils.TID_COLUMN, itemReport.getBankTerminalId());

            // mid
            item.put(TemplateUtils.MID_COLUMN, itemReport.getBankMerchantId());

            item.put("S_ACQ_CODE", itemReport.getAcqCode());
            item.put("S_MASKING", itemReport.getMasking());
            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");



            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getTransactionCount());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, itemReport.getTransactionTotal());

            // Refund Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN,itemReport.getRefundCount());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, itemReport.getRefundTotal());


            // put into list
            listData.add(item);
        }

    }

    private static String handleDate(Timestamp inputDate) {
        String outputDate = null;
        if (inputDate != null) {
            Date date = new Date();
            date.setTime(inputDate.getTime());
            outputDate = new SimpleDateFormat("dd/MM/yyyy").format(date);
        } else {
            outputDate = "";
        }
        return outputDate;
    }
}
