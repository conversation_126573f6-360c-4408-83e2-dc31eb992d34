package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.InternationalReportParameterFile;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportResultSetBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("reconcileReport")
public class ReconcileReportBuildImpl implements ReportResultSetBuild<InternationalReportParameterFile> {

    @Override
    public ResultSet getResultSet (Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalReportParameterFile> messageData) {
        InternationalReportParameterFile param = messageData.getRequestBody();

        InternationalReportParameter parameter = new InternationalReportParameter();
        parameter.setAcquirerId(param.getAcquirerId());
        parameter.setFromDate(param.getFromDate());
        parameter.setToDate(param.getToDate());
        parameter.setInterval(param.getInterval());
        parameter.setCardType(param.getCardType());
        parameter.setCurrency(param.getCurrency());
        parameter.setLang(param.getLang());
        List<String> merchantIdList = param.getMerchantList();
        parameter.setMerchantId(String.join(",", merchantIdList));
        ResultSet rs =null;
        try {
            rs = this.reconcileService.downloadDetail(readOnly, parameter);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "GET ResultSet RECONCILE REPORT: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    @Autowired
    private ReconcileService reconcileService;


    private static final Logger LOGGER = Logger.getLogger(ReconcileReportBuildImpl.class.getName());
}
