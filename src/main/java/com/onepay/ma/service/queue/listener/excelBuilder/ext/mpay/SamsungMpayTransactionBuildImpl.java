package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.service.mpay.impl.SSMpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.google.gson.Gson;
import com.onepay.ma.service.models.mpay.SamsungMpayTransactionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.PropsUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.json.JsonParser;
import org.springframework.boot.json.JsonSimpleJsonParser;
import org.springframework.stereotype.Service;

import io.vertx.core.json.JsonObject;

import java.io.FileReader;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.annotation.Resource;

@Service("samsungMpayTransaction")
public class SamsungMpayTransactionBuildImpl implements ReportDataBuild<SamsungMpayTransactionQuery> {

    private static final Logger LOGGER = Logger.getLogger(SamsungMpayTransactionBuildImpl.class.getName());
    
    private static Map<String, String> channelMap = new HashMap<String, String>() {{
        put("DSP", "mPayVN");
        put("MOMO", "MOMO");
        put("VIETINQR", "mPayVN");
        put("VIETINQR", "Vietin");
        put("DSP_VRBANK", "VRB");
        put("BIDV", "BIDV");
        put("VIB", "VIB");
        put("MSBQR", "MSB");
    }};

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<SamsungMpayTransactionQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        SamsungMpayTransactionQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = SSMpayTransactionService.downloadOnline(connOnline, param);
            rsReadonly = SSMpayTransactionService.downloadReadonly(readOnly, param);
            this.generateRs(rsOnline, listMap);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private static String SAMSUNG_EPP = PropsUtil.get("samsung.epp", "");

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, rs.getString("S_MERCHANT_NAME"));

            // merchant Name
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, rs.getString("S_ONECOM_MERCHANT") == null ? StringPool.BLANK : rs.getString("S_ONECOM_MERCHANT"));

            // merchant transaction ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCH_TXN_REF") == null ? StringPool.BLANK : rs.getString("S_MERCH_TXN_REF"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("S_ID"));

            // customer trans Id
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, rs.getString("S_CUSTOMER_TRANS_ID"));

            // S_QR_ID
            item.put("S_QR_ID", rs.getString("S_QR_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_INSTRUMENT_NUMBER"));

            //SAMSUNG
            item.put(TemplateUtils.CUSTOMER_EMAIL, rs.getString("S_CUSTOMER_EMAIL"));
            item.put(TemplateUtils.CUSTOMER_PHONE, rs.getString("S_CUSTOMER_PHONE"));
            String sRequestBody = rs.getString("S_REQUEST_BODY");
            
            List<String> eppL  = Arrays.asList(SAMSUNG_EPP.split(","));
            if (sRequestBody != null) {
                JsonObject sRqBodyJson = new JsonObject(sRequestBody);
                String sReturnUrl = sRqBodyJson.getJsonObject("request_samsung").getString("vpc_ReturnURL");
                String sEpp = sReturnUrl.split("/")[6];
                if (eppL.contains(sEpp)) {
                    item.put(TemplateUtils.EPP, sEpp);
                } else {
                    item.put(TemplateUtils.EPP, "");
                }
            } else {
                item.put(TemplateUtils.EPP, "");
            }

            item.put("S_APP_NAME", rs.getString("S_APP_NAME"));
            item.put("S_MASKING", rs.getString("S_MASKING"));
            item.put("S_INVOICE_ID", rs.getString("S_INVOICE_ID"));
            item.put("S_QR_ID", rs.getString("S_QR_ID"));
            item.put("S_CHANNEL", channelMap.get(rs.getString("S_CLIENT_ID")) == null ? rs.getString("S_CLIENT_ID") : channelMap.get(rs.getString("S_CLIENT_ID")));
//            item.put("S_CARD_NUMBER", rs.getString("S_CARD_NUMBER"));

            // bank
            String instrumentType = Objects.equals( rs.getString("S_INSTRUMENT_TYPE"), "ewallet")? rs.getString("S_INSTRUMENT_BRAND_ID") :   Objects.equals(rs.getString("S_INSTRUMENT_TYPE"),  "")
             ||  (rs.getString("S_INSTRUMENT_TYPE") == null || rs.getString("S_INSTRUMENT_TYPE").isEmpty()) ? rs.getString("S_INSTRUMENT_BRAND_ID") : PropsUtil.get(rs.getString("S_INSTRUMENT_TYPE"), "") ;
            item.put(TemplateUtils.BANK_COLUMN, instrumentType);

            // Bank ID
            item.put(TemplateUtils.BANK_REF_COLUMN, rs.getString("S_BANK_TRANS_ID"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_CREATE"));

            // client
            item.put(TemplateUtils.CLIENT_COLUMN, rs.getString("S_CLIENT_ID"));

            // S_INSTRUMENT_NAME
            item.put("S_INSTRUMENT_NAME", rs.getString("S_INSTRUMENT_NAME"));

            // S_INSTRUMENT_BRAND_ID
            item.put("S_INSTRUMENT_BRAND_ID", rs.getString("S_INSTRUMENT_BRAND_ID") == null || rs.getString("S_INSTRUMENT_BRAND_ID").equals("") ? "" : rs.getString("S_INSTRUMENT_BRAND_ID").toUpperCase());

            // status
            String status = rs.getString("S_STATE");
            if(status != null) {
                if (status.equals("approved")) status = "Successful";
                if (status.equals("pending")) status = "Pending";
                if (status.equals("failed")) status = "Failed";
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);

            // status
            item.put(TemplateUtils.ACQ_CODE_COLUMN, rs.getString("S_MSP_ID"));


            // put into list
            listData.add(item);
        }

    }
}
