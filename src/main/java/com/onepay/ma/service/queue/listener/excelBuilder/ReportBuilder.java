package com.onepay.ma.service.queue.listener.excelBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 05-Jun-17.
 */
public class ReportBuilder {

    private static ExcelReportGenerator reportGenerator;

    public  ReportBuilder exportFileName(String fileName) {
        this.reportGenerator.setExportFileName(fileName);
        return this;
    }

    public  ReportBuilder exportLocation(String exportLocation) {
        this.reportGenerator.setExportLocation(exportLocation);
        return this;
    }

    public  ReportBuilder fileName(String fileName) {
        this.reportGenerator.setFileName(fileName);
        return this;
    }

    public ReportBuilder template(String templatePath) {
        this.reportGenerator.setTemplateFilePath(templatePath);
        return this;
    }

    public ReportBuilder setBeanValue(String key, Object value) {
        
        if(this.reportGenerator.getBean() == null) {
            this.reportGenerator.setBean(new HashMap());
        }
        this.reportGenerator.getBean().put(key, value);

        return this;
    }
    public  ReportBuilder setHiddenColumn(String column) {
        if(column == null ) {
            return this;
        }
        if(this.reportGenerator.getHiddenColumn() == null) {
            this.reportGenerator.setHiddenColumn(new ArrayList<>());
        }
        this.reportGenerator.setHiddenColumnItem(column);
        return this;
    }
    public  ReportBuilder setParameters(Map<String,Object> parameters) {
        this.reportGenerator.setParameters(parameters);
        return this;
    }
    public  ReportBuilder setListData(List<Object> listData) {
        this.reportGenerator.setListData(listData);
        return this;
    }

    public static ReportBuilder newInstance() {
        reportGenerator =  new ExcelReportGenerator();

        return new ReportBuilder();
    }

    public ExcelReportGenerator build() {
        return this.reportGenerator;
    }

    public  ReportBuilder setListHiddenColumn(List<String> lstColumn) {
        if(lstColumn == null ) {
            return this;
        }
        this.reportGenerator.setHiddenColumn(lstColumn);
        return this;
    }
}
