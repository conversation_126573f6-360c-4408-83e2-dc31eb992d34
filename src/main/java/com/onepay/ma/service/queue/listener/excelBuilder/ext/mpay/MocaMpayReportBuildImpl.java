package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.service.mpay.impl.MocaMpayReportService;
import com.onepay.ma.service.service.mpay.impl.MpayReportService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.mpay.MocaMpayReport;
import com.onepay.ma.service.models.mpay.MpayReport;
import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-18.
 */
@Service("mocaMpayReport")
public class MocaMpayReportBuildImpl implements ReportDataBuild<MpayReportQuery> {

    private static final Logger LOGGER = Logger.getLogger(MpayReportBuildImpl.class.getName());

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayReportQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayReportQuery param = messageData.getRequestBody();
        List<MocaMpayReport> rsReadonly = null;
        try {
            rsReadonly = MocaMpayReportService.download(readOnly, param);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<MocaMpayReport> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (MocaMpayReport itemReport : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchantId());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, itemReport.getMerchantName());


            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getReportDate());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, PropsUtil.get(itemReport.getCardType(), StringPool.BLANK).equals("") ? itemReport.getCardType() : PropsUtil.get(itemReport.getCardType(), StringPool.BLANK));

            item.put("S_APP_NAME", itemReport.getAppName());
            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");


            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getTransactionCount());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, itemReport.getTransactionTotal());

            // Refund Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN,itemReport.getRefundCount());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, itemReport.getRefundTotal());

            //channel
            item.put(TemplateUtils.S_CHANNEL, itemReport.getClient() != null ? handleChannel(itemReport.getClient()) : "");

            // Total payment amount refund
            item.put(TemplateUtils.REFUND_PAYMENT_AMOUNT_TOTAL_COLUMN, itemReport.getRefundPaymentAmountTotal());
            // Total payment amount purchase
            item.put(TemplateUtils.PURCHASE_PAYMENT_AMOUNT_TOTAL_COLUMN, itemReport.getTransPaymentAmountTotal());

            // put into list
            listData.add(item);
        }

    }

    private static String handleChannel(String inputData) {
        String outputData = null;
        if (inputData.equals("DSP")) {
            outputData = "mPayVN";
        } else if (inputData.equals("VIETINQR")) {
            outputData = "Vietin";
        } else if (inputData.equals("DSP_VRBANK")) {
            outputData = "VRB";
        } else if (inputData.equals("MSBQR")) {
            outputData = "MSB";
        } else {
            outputData = inputData;
        }
        return outputData;
    }
}
