package com.onepay.ma.service.queue.listener.csvBuilder;

import com.opencsv.CSVWriter;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CsvReportGenerator {

    private String templateFilePath;

    private Map bean;

    private String exportFileName;

    private List<Object> listData;

    private String exportLocation;

    private String fileName;

    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }

    public Map getBean() {
        return bean;
    }

    public void setBean(Map bean) {
        this.bean = bean;
    }

    public String getExportFileName() {
        return exportFileName;
    }

    public List<Object> getListData() {
        return listData;
    }

    public void setListData(List<Object> listData) {
        this.listData = listData;
    }

    public static Logger getLOGGER() {
        return LOGGER;
    }

    public String getExportLocation() {
        return exportLocation;
    }

    public void setExportLocation(String exportLocation) {
        this.exportLocation = exportLocation;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }


    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }


    public void exportCsv() {
        LOGGER.log(Level.INFO, "-------------START EXPORT CSV FILE-----------------------");

        try {
            Writer writer = Files.newBufferedWriter(Paths.get(this.exportFileName));
            CSVWriter csvWriter = new CSVWriter(writer,
                    CSVWriter.DEFAULT_SEPARATOR,
                    CSVWriter.NO_QUOTE_CHARACTER,
                    CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                    CSVWriter.DEFAULT_LINE_END);

            String[] headerRecord;
            if(bean.containsKey("headerRecord")){
                Object listMap = bean.get("headerRecord");
                headerRecord = (String[]) listMap;
                csvWriter.writeNext(headerRecord);
            }

            List<String[]> datas = null;
            Map bean = this.bean;
            if(bean.containsKey("listContent")){
                Object listMap = bean.get("listContent");
                datas = (List<String[]>) listMap;
                if(!datas.isEmpty()){
                    for(String[] map:datas){
                        csvWriter.writeNext(map);
                    }

                }

            }
            csvWriter.close();
            writer.close();
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export csv file", ex);
        }

    }

    private static final Logger LOGGER = Logger.getLogger(CsvReportGenerator.class.getName());
}
