package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReportQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.mpayPromotion.MpayPrReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 03-Feb-18.
 */
@Service("mpayPrReport")
public class MpayPrReportBuildImpl  implements ReportDataBuild<MpayPrReportQuery> {

    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());
    @Autowired
    private MpayPrReportService mpayPrReportService;

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayPrReportQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayPrReportQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = mpayPrReportService.download(readOnly, param);
            this.generateRs(rsOnline, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT MPAY PR REPORT] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));


            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, rs.getInt("N_TRANS_COUNT"));


            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));
//            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");

            // amount
            item.put(TemplateUtils.TOTAL_AMOUNT_COLUMN, rs.getDouble("N_SUM_PAYMENT"));

            // amount
            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, rs.getDouble("N_SUM_ORIGINAL"));

            // amount
            item.put(TemplateUtils.DISCOUNT_AMOUNT_COLUMN, rs.getDouble("N_SUM_DISCOUNT"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_DATE"));



            // put into list
            listData.add(item);
        }

    }
}
