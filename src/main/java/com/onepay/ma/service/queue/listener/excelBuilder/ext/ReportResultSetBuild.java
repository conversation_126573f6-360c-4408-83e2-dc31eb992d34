package com.onepay.ma.service.queue.listener.excelBuilder.ext;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 05-Jun-17.
 */
public interface ReportResultSetBuild<T> {

    ResultSet getResultSet(Connection connOnline, Connection readOnly, Connection connBackup, com.onepay.ma.service.queue.message.Message<T> messageData);

    default ResultSet getResultSet(Connection connOnline, Connection readOnly, com.onepay.ma.service.queue.message.Message<T> messageData) {
        return this.getResultSet(connOnline, readOnly, connOnline, messageData);
    };

    default ResultSet getResultSet(Connection conn, com.onepay.ma.service.queue.message.Message<T> messageData) {
        return this.getResultSet(conn, conn, messageData);
    }
}
