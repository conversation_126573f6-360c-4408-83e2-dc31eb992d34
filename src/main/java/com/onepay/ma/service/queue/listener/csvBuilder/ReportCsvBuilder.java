package com.onepay.ma.service.queue.listener.csvBuilder;

import java.util.HashMap;
import java.util.List;

public class ReportCsvBuilder {

    private static CsvReportGenerator reportGenerator;

    public ReportCsvBuilder exportFileName(String fileName) {
        this.reportGenerator.setExportFileName(fileName);
        return this;
    }

    public ReportCsvBuilder exportLocation(String exportLocation) {
        this.reportGenerator.setExportLocation(exportLocation);
        return this;
    }

    public ReportCsvBuilder fileName(String fileName) {
        this.reportGenerator.setFileName(fileName);
        return this;
    }

    public ReportCsvBuilder template(String templatePath) {
        this.reportGenerator.setTemplateFilePath(templatePath);
        return this;
    }

    public ReportCsvBuilder setBeanValue(String key, Object value) {
        if(this.reportGenerator.getBean() == null) {
            this.reportGenerator.setBean(new HashMap());
        }
        this.reportGenerator.getBean().put(key, value);

        return this;
    }

    public ReportCsvBuilder setListData(List<Object> listData) {
        this.reportGenerator.setListData(listData);
        return this;
    }

    public static ReportCsvBuilder newInstance() {
        reportGenerator =  new CsvReportGenerator();

        return new ReportCsvBuilder();
    }

    public CsvReportGenerator build() {
        return this.reportGenerator;
    }
}
