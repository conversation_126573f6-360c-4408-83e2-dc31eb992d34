package com.onepay.ma.service.queue.listener.csvBuilder.ext.pay_out;

import com.onepay.ma.service.models.pay_out.BatchDetailQueryDto;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.pay_out.BatchDetailService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("batch_detail_build")
public class BatchDetailBuildImpl implements ReportDataBuild<BatchDetailQueryDto> {
    private static final Logger LOGGER = Logger.getLogger(BatchDetailBuildImpl.class.getName());
    @Autowired
    private BatchDetailService batchDetailService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<BatchDetailQueryDto> messageData) {
        List<Map> listMap = new ArrayList<>();
        BatchDetailQueryDto query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.batchDetailService.downloadFile(readOnly, query);
            this.generateRs(rsOnline, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            item.put("TYPE", rs.getString("S_TYPE"));
            item.put("SWIFT_CODE", rs.getString("S_SWIFT_CODE"));
            item.put("ACCOUNT_NUMBER", rs.getString("S_ACCOUNT_NUMBER"));
            item.put("CARD_NUMBER", rs.getString("S_CARD_NUMBER"));
            item.put("ACCOUNT_NAME", rs.getString("S_RECEIPT_ACCOUNT_NAME"));
            item.put("MERCHANT_ID", rs.getString("S_MERCHANT_ID"));
            item.put("MERCHANT_ACCOUNT", rs.getString("S_MERCHANT_ACCOUNT"));
            item.put("AMOUNT", rs.getInt("N_AMOUNT"));
            item.put("CURRENCY", rs.getString("S_CURRENCY"));
            item.put("FUNDS_TRANSFER_INFO", rs.getString("S_FUNDS_TRANSFER_INFO"));
            item.put("REMARK", rs.getString("S_REMARK"));
            item.put("REASON", rs.getString("S_REASON"));
            listData.add(item);
        }

    }
}
