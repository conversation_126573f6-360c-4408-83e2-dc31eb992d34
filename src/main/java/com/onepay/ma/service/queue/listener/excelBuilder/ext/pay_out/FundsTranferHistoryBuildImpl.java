package com.onepay.ma.service.queue.listener.excelBuilder.ext.pay_out;

import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import com.onepay.ma.service.models.pay_out.FundsTransHisQueryDto;
import com.onepay.ma.service.models.pay_out.FundsTransHistoryDto;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.IErrors;
import groovy.json.JsonException;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.sql.Date;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("funds_tranfer_history_build")
public class FundsTranferHistoryBuildImpl implements ReportDataBuild<FundsTransHisQueryDto> {
    private static final Logger LOGGER = Logger.getLogger(FundsTranferHistoryBuildImpl.class.getName());

    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<FundsTransHisQueryDto> messageData) {
        List<Map> listMap = new ArrayList<>();
        FundsTransHisQueryDto query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            JsonObject jo = OnePayoutClient.getFundsTransHistory(query.getUserId(),
                    query.getxUserId(), query.getxRequestId(), query.getFromDate(), query.getToDate(), query.getDateOf(), query.getBeneficiaryBanks(),
                    query.getMerchantId(), query.getFunds_transfer_id(), query.getTransaction_id(), query.getMerchantAccount(), query.getBeneficiaryAccount(), query.getState(), query.getPageSize(), query.getPage());

            List<FundsTransHistoryDto> fundsTransHistoryDtos = new ArrayList<>();
            JsonArray jsonArray = jo.getJsonArray("list");
            if (jsonArray != null) {
                jsonArray.forEach(obj -> {
                    fundsTransHistoryDtos.add(bindFundsTransHistoryDtos((JsonObject) obj));
                });

            }
            this.generateRs(fundsTransHistoryDtos, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(List<FundsTransHistoryDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {

            for (FundsTransHistoryDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.FUNDS_MERCHANT_ID, itemReport.getMerchantId());
                item.put(TemplateUtils.MERCHANT_ACCOUNT, itemReport.getMerchantAccount());
//                item.put(TemplateUtils.MERCHANT_NAME, itemReport.getMerchantName());
                item.put(TemplateUtils.MERCHANT_TRANS_ID, itemReport.getMerchantTransId());
                item.put(TemplateUtils.TRANSACTION_ID, itemReport.getTransactionId());
                item.put(TemplateUtils.TRANSACTION_DATE, itemReport.getTransactionDate() == null ? "" : formatDate(new Date(itemReport.getTransactionDate().getTime())));
                item.put(TemplateUtils.UPDATE_DATE, itemReport.getUpdatedDate() == null ? "" : formatDate(new Date(itemReport.getUpdatedDate().getTime())));
                item.put(TemplateUtils.FUND_DATE, itemReport.getFundDate() == null ? "" : formatDate(new Date(itemReport.getFundDate().getTime())));
                item.put(TemplateUtils.BALANCE_BEFORE, formatCurrencyDouble(itemReport.getBalanceBefore()) + " VND");
                item.put(TemplateUtils.BALANCE_AFTER, formatCurrencyDouble(itemReport.getBalanceAfter()) + " VND");
                item.put(TemplateUtils.RECEIVED_ACCOUNT, itemReport.getReceivedAccountNumber());
                item.put(TemplateUtils.RECEIVED_ACCOUNT_NAME, itemReport.getReceivedAccountName());
                item.put(TemplateUtils.RECEIVED_BANK, itemReport.getBeneficiaryBankName());
                item.put("AMOUNT", formatCurrencyDouble(itemReport.getAmount()) + " VND");
                item.put(TemplateUtils.REMARK, itemReport.getRemark());
                item.put(TemplateUtils.BANK_TRANS_ID, itemReport.getTransactionId());
//                item.put(TemplateUtils.BANK_NAME, itemReport.getBankName());
                item.put(TemplateUtils.RESPONSE_CODE, itemReport.getBankCode());
                item.put(TemplateUtils.MESSAGE, itemReport.getBankMsg());
                item.put(TemplateUtils.BALANCE_BANK_BEFORE, formatCurrencyDouble(itemReport.getBalanceBankBefore()) + " VND");
                item.put(TemplateUtils.BALANCE_BANK_AFTER, formatCurrencyDouble(itemReport.getBalanceBankAfter()) + " VND");
                item.put("CREATOR_NAME", itemReport.getCreator_name());
                item.put("APPROVAL_NAME", itemReport.getVerifier_name());
                item.put("OPERATOR", itemReport.getOperator());
                String state = itemReport.getState().substring(0, 1).toUpperCase() + itemReport.getState().substring(1);
                item.put("STATE", state.replaceAll("_", " "));
                listData.add(item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("###,###,###");
        return df.format(value);
    }

    public static String formatDate(Date dates) throws ParseException {
        String myDate = null;
        if (!dates.equals("")) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm a");
            try {
                myDate = sdf.format(dates);
            } catch (Exception ex) {
                throw ex;
            }
        } else {
            myDate = "";
        }
        return myDate;
    }

    private FundsTransHistoryDto bindFundsTransHistoryDtos(JsonObject rs) {
        FundsTransHistoryDto fundsTransHistoryDto = new FundsTransHistoryDto();
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            fundsTransHistoryDto.setS_id(rs.getString("funds_transfer_id"));

            fundsTransHistoryDto.setMerchantId(rs.getString("merchant_id"));
            fundsTransHistoryDto.setMerchantAccount(rs.getString("account_id"));
            fundsTransHistoryDto.setMerchantName(null);
            fundsTransHistoryDto.setMerchantTransId(rs.getString("funds_transfer_id"));
            fundsTransHistoryDto.setBalanceBefore(rs.getString("before_account_balance") == null ? 0 : Double.parseDouble(rs.getString("before_account_balance")));
            fundsTransHistoryDto.setBalanceAfter(rs.getString("after_account_balance") == null ? 0 : Double.parseDouble(rs.getString("after_account_balance")));
            fundsTransHistoryDto.setBalanceBankBefore(rs.getString("before_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("before_bank_balance")));
            fundsTransHistoryDto.setBalanceBankAfter(rs.getString("after_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("after_bank_balance")));
            fundsTransHistoryDto.setReceivedAccountNumber(rs.getString("account_number"));
            fundsTransHistoryDto.setReceivedAccountName(rs.getString("holder_name"));
            fundsTransHistoryDto.setReceivedCardNumber(rs.getString("card_number"));
            fundsTransHistoryDto.setTransactionId(rs.getString("transaction_id"));
            fundsTransHistoryDto.setTransactionDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setAmount(rs.getString("amount") == null ? 0 : Double.parseDouble(rs.getString("amount")));
            fundsTransHistoryDto.setRemark(rs.getString("remark"));
            fundsTransHistoryDto.setState(rs.getString("state"));

            fundsTransHistoryDto.setBankTransactionId(null);
            fundsTransHistoryDto.setBeneficiaryBankName(rs.getString("bank_name"));
            fundsTransHistoryDto.setBankCode(rs.getString("response_code"));
            fundsTransHistoryDto.setBankMsg(rs.getString("message"));

            fundsTransHistoryDto.setCreatedDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setUpdatedDate(rs.getString("update_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("update_time")).getTime()));
            fundsTransHistoryDto.setFundDate(rs.getString("fund_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("fund_time")).getTime()));
            if (rs.containsKey("meta_data") && null != rs.getString("meta_data")) {
                fundsTransHistoryDto.setMetadata(rs.getString("meta_data"));
                if (isJSONValid(rs.getString("meta_data"))) {
                    JsonObject metaData = new JsonObject(rs.getString("meta_data"));
                    if (metaData.containsKey("creator_id")) {
                        fundsTransHistoryDto.setCreator_id(metaData.getString("creator_id"));
                    }
                    if (metaData.containsKey("creator_name")) {
                        fundsTransHistoryDto.setCreator_name(metaData.getString("creator_name"));
                    }
                    if (metaData.containsKey("verifier_id")) {
                        fundsTransHistoryDto.setVerifier_id(metaData.getString("verifier_id"));
                    }
                    if (metaData.containsKey("verifier_name")) {
                        fundsTransHistoryDto.setVerifier_name(metaData.getString("verifier_name"));
                    }
                }
            }
            fundsTransHistoryDto.setOperator(rs.getString("operator_user"));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return fundsTransHistoryDto;
    }

    public boolean isJSONValid(String test) {
        try {
            new JsonObject(test);
        } catch (JsonException ex) {
            try {
                new JsonArray(test);
            } catch (JsonException ex1) {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }
}
