package com.onepay.ma.service.queue.message;

import org.springframework.jms.core.MessageCreator;

import javax.jms.JMSException;
import javax.jms.Queue;
import javax.jms.Session;

/**
 * Created by HungDX on 22-Jan-16.
 */
public class JMSMessageCreator implements MessageCreator {

    private Queue destinationQueue;
    private Queue forwardToQueue;
    private int messagePriority;
    private Message message;

    public JMSMessageCreator(Message message) {
        this.destinationQueue = message.getDestinationQueue();
        this.forwardToQueue = message.getForwardQueue();
        this.messagePriority = message.getMessagePriority() != 0 ? message.getMessagePriority() : MessagePriority.MEDIUM_PRIORITY;
        this.message = message;
    }

    @Override
    public javax.jms.Message createMessage(Session session) throws JMSException {

        javax.jms.Message jmsMessage = session.createObjectMessage(message);

        jmsMessage.setJMSPriority(messagePriority);
        jmsMessage.setJMSDestination(destinationQueue);
        jmsMessage.setJMSReplyTo(forwardToQueue);

        return jmsMessage;
    }
}
