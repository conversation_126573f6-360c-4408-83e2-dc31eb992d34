package com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect;


import com.onepay.ma.service.models.pay_collect.TransactionSearchReq;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.pay_collect.TransactionService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("paycollectTransaction")
public class TransactionBuildImpl implements ReportDataBuild<TransactionSearchReq> {

    private static final Logger LOGGER = Logger.getLogger(TransactionBuildImpl.class.getName());
    // Create DateTimeFormatter to parse the timestamp strings
    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    @Autowired
    private TransactionService transactionService;

    @Override
    public List<Map> generate(Connection connPaycollect, Connection readOnly, Connection connBackup, Message<TransactionSearchReq> messageData) {
        List<Map> listMap = new ArrayList<>();
        TransactionSearchReq query = messageData.getRequestBody();
        ResultSet rs = null;
        try {
            rs = this.transactionService.download(connPaycollect, query);
            this.generateRs(rs, listMap, query.getLang());
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[PAY COLLECT TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }
        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("merchantId", rs.getString("S_PARTNER_ID"));
            item.put("transactionId", rs.getString("S_ID"));
            Timestamp createdDate = rs.getString("D_CREATE") == null ? null : Timestamp.valueOf(rs.getString("D_CREATE"));
            item.put("createdDate", createdDate == null ? "" : formatDate(new java.sql.Date(createdDate.getTime())));
            Timestamp bankDate = rs.getString("D_BANK_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_BANK_DATE"));
            item.put("bankDate", bankDate == null ? "" : formatDate(new java.sql.Date(bankDate.getTime())));
            item.put("clientId", rs.getString("S_CLIENT_ID"));
            // item.put("senderName",rs.getString("SENDER_BANK"));
            // item.put("senderBank", rs.getString("SENDER_BANK"));
            item.put("receivedName", rs.getString("RECEIPT_BANK"));
            item.put("name", rs.getString("S_RECEIPT_ACC_NAME"));
            item.put("receivedBank", rs.getString("RECEIPT_BANK"));
            item.put("bankTxnRef", rs.getString("S_BANK_TXN_REF"));
            Double amount = rs.getDouble("N_AMOUNT");
            item.put("amount", formatCurrency(amount) + " VND");
            item.put("accountNumber", rs.getString("s_bank_account"));
            item.put("reference", rs.getString("S_REFERENCE_ID"));

            // item.put("balanceAfter", rs.getString("N_ACC_BALANCE_AFTER"));
            item.put("currency", rs.getString("S_CURRENCY"));
            item.put("state", formatState(rs.getString("S_STATE"), lang));
            item.put("bankCode", rs.getString("S_BANK_RES_CODE"));
            item.put("bankMessage", rs.getString("S_REMARK"));
            
            // Parse the timestamp strings into LocalDateTime objects
            LocalDateTime dateTime1 = LocalDateTime.parse(createdDate.toString(), formatter);
            LocalDateTime dateTime2 = LocalDateTime.parse(bankDate.toString(), formatter);
            // Calculate the duration between the two timestamps
            Duration duration = Duration.between(dateTime1, dateTime2);
            // Get the difference in minutes
            long minutesDifference = Math.abs(duration.toMinutes());
            if (minutesDifference <= 5){
                item.put("highlight", "none");
            } else {
                item.put("highlight", "highlight");
            }
            listData.add(item);
        }
    }

    public static String formatCurrency(Double price) {
        DecimalFormat formatter = new DecimalFormat("###,###,###");
        return formatter.format(price);
    }

    public static String formatDate(Date dates) throws ParseException {
        String myDate = null;
        if (!dates.equals("")) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm aa");
            try {
                myDate = sdf.format(dates);
            } catch (Exception ex) {
                throw ex;
            }
        } else {
            myDate = "";
        }
        return myDate;
    }

    private static String formatState(String state, String lang) {
        String result;
        if (state.equals("approved"))
            result = "Successful";
        else
            result = "Failed";
        if (null != lang && "vi".equals(lang)) {
            if (state.equals("approved"))
                result = "Thành công";
            else
                result = "Thất bại";
        }
        return result;
    }
}
