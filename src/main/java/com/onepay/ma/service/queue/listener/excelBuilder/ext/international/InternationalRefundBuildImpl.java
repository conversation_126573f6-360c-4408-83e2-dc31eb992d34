package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.InternationalRefundParameter;
import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("internationalRefund")
public class InternationalRefundBuildImpl implements ReportDataBuild<InternationalRefundParameter> {

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalRefundParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        InternationalRefundParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            this.calendar = Calendar.getInstance();
            rsOnline = internationalRefundService.downloadOnline(connOnline, param);
            rsReadonly = internationalRefundService.downloadReadonly(readOnly, param);
            rsBackup = internationalRefundService.downloadBackp(connBackup, param);

            this.generateRsBackup(rsBackup,  listMap);
            List<String> transIdList = listMap.stream().map(map -> map.get("N_ORIGINAL_ID")+"").collect(Collectors.toList());


            Map transMap =  internationalTransactionService.mapByIdsDownload(connOnline, String.join(StringPool.COMMA, transIdList),param);
            listMap = fillBackupData(listMap, transMap);
            this.generateRs(rsOnline, listMap);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL REFUND] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }


    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("EXT_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_TRANSACTION_REFERENCE"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NO"));

            // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));

            // trans type
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, rs.getString("EXT_TYPE"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // purchase amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, rs.getString("EXT_PURCHASE_AMOUNT"));

            // purchase date
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, rs.getDate("EXT_PURCHASE_DATE"));

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, rs.getString("EXT_AMOUNT"));

            // refund date
            Date refundTime = rs.getDate("EXT_DATE");
            item.put(TemplateUtils.REFUND_DATE_COLUMN, refundTime);

            // response code
            String responseCode = (rs.getObject("EXT_STATUS") == null) ? "" : rs.getString("EXT_STATUS");
            responseCode = responseCode + "-" + PropsUtil.get(responseCode, "Blank") ;
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            // status
            String statusData = rs.getString("EXT_STATUS");
            String status = "";
            if ( statusData != null && !statusData.isEmpty() ) {
                status = statusData.equals("0") ? "Successful" : "Failed";
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);

            // authorization code
            item.put(TemplateUtils.AUTH_CODE_COLUMN, rs.getString("S_AUTHORISATION_CODE") ==null ? "" : rs.getString("S_AUTHORISATION_CODE"));

            // batch number
            String batchNumber = StringPool.BLANK;
            calendar.setTime(refundTime);
            calendar.set(Calendar.HOUR_OF_DAY, 16);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.MILLISECOND, 59);
            Date cutOffTime = calendar.getTime();
            if(refundTime.after(cutOffTime)) {
                calendar.add(Calendar.DAY_OF_YEAR, 1);
            }
            batchNumber +=  calendar.get(Calendar.YEAR);
            batchNumber +=  String.format("%02d", calendar.get(Calendar.MONTH) + 1); // The Months are numbered from 0 (January) to 11 (December).
            batchNumber +=  String.format("%02d", calendar.get(Calendar.DATE));
            item.put(TemplateUtils.BATCH_NUMBER_COLUMN, batchNumber);


            // put into list
            listData.add(item);
        }

    }
    private void generateRsBackup(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
//            item.put(TemplateUtils.BANK_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getAcquirer().getAcquirer_name());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));


            // trans Id
            item.put("N_ORIGINAL_ID", rs.getString("N_ORIGINAL_ID"));


            // Order Info
//            item.put(TemplateUtils.ORDER_INFO_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getOrder_info());

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // purchase amount
//            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getAmount().getTotal());

            // purchase date
//            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getTransaction_time());

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, rs.getDate("D_MERCHANT_TRANSACTION_DATE"));

            // trans type
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, "Refund");

            // status
            String statusData = rs.getString("N_TRANSACTION_STATUS");
            if(!statusData.isEmpty()) {
                switch (statusData) {
                    case "400": statusData = "Successful"; break;
                    case "300": statusData = "Wait for Confirmed"; break;
                    case "401": statusData = "Wait for Approval"; break;
                    case "402": statusData = "Approval Rejected"; break;
                    case "403": statusData = "Approved"; break;
                    case "404": statusData = "Rejected"; break;
                    case "310": statusData = "Confirmed"; break;
                    default: statusData = "Failed"; break;
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, statusData);

            // put into list
            listData.add(item);
        }

    }

    private List<Map> fillBackupData(List<Map> listData, Map<Integer, InternationalTransaction> tranMap ) throws Exception {
        List<Map> result = new ArrayList<>();
        for (Map item: listData) {
            if(tranMap.get(item.get("N_ORIGINAL_ID")) != null) {
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getTransaction_time());
            item.put(TemplateUtils.BANK_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getAcquirer().getAcquirer_name());
            item.put(TemplateUtils.ORDER_INFO_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getOrder_info());
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getAmount().getTotal());
            item.put(TemplateUtils.CARD_NO_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getCard().getCard_number());
            item.put(TemplateUtils.CARD_TYPE_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getCard().getCard_type());
            item.put(TemplateUtils.CURRENCY_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getAmount().getCurrency());
            // response code
            String responseCode = (tranMap.get(item.get("N_ORIGINAL_ID")).getResponse_code()== null) ? "" : tranMap.get(item.get("N_ORIGINAL_ID")).getResponse_code();
            responseCode = responseCode + "-" + PropsUtil.get(responseCode, "Blank") ;
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);
           // item.put(TemplateUtils.RESPONSE_CODE_COLUMN,  tranMap.get(item.get("N_ORIGINAL_ID")).getResponse_code());
            // authorization code
            String authenticationCode =tranMap.get(item.get("N_ORIGINAL_ID")).getAuthentication().getAuthorization_code();
            item.put(TemplateUtils.AUTH_CODE_COLUMN, authenticationCode==null ? "" :authenticationCode );

            // batch number
            //TemplateUtils.REFUND_DATE_COLUMN
            Date refundTime = (Date) item.get(TemplateUtils.REFUND_DATE_COLUMN);
            String batchNumber = StringPool.BLANK;
            calendar.setTime(refundTime);
            calendar.set(Calendar.HOUR_OF_DAY, 16);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.MILLISECOND, 59);
            Date cutOffTime = calendar.getTime();
            if(refundTime.after(cutOffTime)) {
                calendar.add(Calendar.DAY_OF_YEAR, 1);
            }
            batchNumber +=  calendar.get(Calendar.YEAR);
            batchNumber +=  String.format("%02d", calendar.get(Calendar.MONTH) + 1); // The Months are numbered from 0 (January) to 11 (December).
            batchNumber +=  String.format("%02d", calendar.get(Calendar.DATE));
            item.put(TemplateUtils.BATCH_NUMBER_COLUMN, batchNumber);
                result.add(item);
            }
        }
        return result;

    }


    @Autowired
    private InternationalRefundService internationalRefundService;

    @Autowired
    private InternationalTransactionService internationalTransactionService;

    private Calendar calendar;

    private static final Logger LOGGER = Logger.getLogger(InternationalRefundBuildImpl.class.getName());
}
