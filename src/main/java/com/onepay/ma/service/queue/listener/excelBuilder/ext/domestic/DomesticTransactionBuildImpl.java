package com.onepay.ma.service.queue.listener.excelBuilder.ext.domestic;

import java.sql.Timestamp;
import com.onepay.ma.service.util.Converter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.DomesticTransaction;
import com.onepay.ma.service.models.DomesticTxnParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("domesticTransaction")
public class DomesticTransactionBuildImpl implements ReportDataBuild<DomesticTxnParameter> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<DomesticTxnParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        DomesticTxnParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            rsBackup = DomesticTransactionService.downloadBackup(connBackup, param);

            this.generateRsBackup(rsBackup, listMap, param.getLang());
            List<String> transIdList = listMap.stream().map(map -> map.get("N_ORIGINAL_ID") + "").collect(Collectors.toList());
            Map transMap = DomesticTransactionService.transMapByIdsDownload(connOnline, String.join(StringPool.COMMA, transIdList), param);
            listMap = fillBackupData(listMap, transMap, param.getVersion(), param.getLang());

            rsOnline = DomesticTransactionService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param);
            rsReadonly = DomesticTransactionService.downloadReadonly(readOnly, param, listMap.size());
            this.generateRs(rsReadonly, listMap, param);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT DOMESTIC TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRsBackup(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));

            // trans Id
            item.put("N_ORIGINAL_ID", rs.getInt("N_ORIGINAL_ID"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, rs.getDate("D_MERCHANT_TRANSACTION_DATE"));

            // status
            String statusData = rs.getString("N_TRANSACTION_STATUS");
            if (!statusData.isEmpty()) {
                switch (statusData) {
                    case "400":
                        statusData = "Successful";
                        break;
                    case "300":
                        statusData = "Waiting for OnePay's Approval";
                        break;
                    case "210":
                        statusData = "Waiting for OnePay's Approval";
                        break;
                    case "401":
                        statusData = "Wait for Approval";
                        break;
                    case "402":
                        statusData = "Approval Rejected";
                        break;
                    case "403":
                        statusData = "Merchant approved";
                        break;
                    case "404":
                        statusData = "Rejected";
                        break;
                    case "310":
                        statusData = "OnePay Approved";
                        break;
                    default:
                        statusData = "Failed";
                        break;
                }

                if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    switch (rs.getString("N_TRANSACTION_STATUS")) {
                        case "400":
                            statusData = "Thành công";
                            break;
                        case "300":
                            statusData = "GD chờ OnePay duyệt";
                            break;
                        case "210":
                            statusData = "GD chờ OnePay duyệt";
                            break;
                        case "401":
                            statusData = "Đang chờ duyệt";
                            break;
                        case "402":
                            statusData = "Approval Rejected";
                            break;
                        case "403":
                            statusData = "Đơn vị đã chấp thuận";
                            break;
                        case "404":
                            statusData = "Từ chối";
                            break;
                        case "310":
                            statusData = "OnePay đã xử lý";
                            break;
                        default:
                            statusData = "Không thành công";
                            break;
                    }
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, statusData);
            String transType = rs.getString("S_TRANSACTION_TYPE");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("Purchase".equalsIgnoreCase(transType)) {
                    transType = "Thanh toán";
                } else if ("Refund".equalsIgnoreCase(transType)) {
                    transType = "Hoàn tiền";
                } else if ("Request refund".equalsIgnoreCase(transType)) {
                    transType = "Yêu cầu hoàn trả";
                }
            }

            item.put("TRANSACTION_TYPE", transType);
            // put into list
            listData.add(item);
        }

    }

    private void generateRs(ResultSet rs, List<Map> listData, DomesticTxnParameter param) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, param.getVersion().equals("v2")
                    ? Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID")) != null
                            ? Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID"))
                            : rs.getString("S_ACQUIRER_NAME")
                    : rs.getString("S_ACQUIRER_NAME"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_TRANSACTION_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // email
            item.put("S_CUSTOMER_EMAIL", rs.getString("S_CUSTOMER_EMAIL"));

            item.put("S_CUSTOMER_PHONE", rs.getString("S_CUSTOMER_PHONE"));

            // item.put("S_MERCHANT_WEBSITE", rs.getString("S_MERCHANT_WEBSITE"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NUMBER"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // date
            Date transactionTime = rs.getDate("D_MERCHANT_TRANSACTION_DATE");
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            String transType = rs.getString("S_TRANSACTION_TYPE");
            if (!"Purchase".equalsIgnoreCase(rs.getString("S_TRANSACTION_TYPE"))) {
                item.put("ORIGINAL_DATE", rs.getDate("D_ORIGINAL_DATE"));
                item.put("ORIGINAL_AMOUNT", rs.getString("N_ORIGINAL_AMOUNT"));
            } else {
                item.put("ORIGINAL_DATE", "");
                item.put("ORIGINAL_AMOUNT", "");
            }

            if (null != param.getLang() && !param.getLang().isEmpty() && "vi".equalsIgnoreCase(param.getLang())) {
                if ("Purchase".equalsIgnoreCase(transType)) {
                    transType = "Thanh toán";
                } else if ("Refund".equalsIgnoreCase(transType)) {
                    transType = "Hoàn tiền";
                } else if ("Request refund".equalsIgnoreCase(transType)) {
                    transType = "Yêu cầu hoàn trả";
                }
            }

            item.put("TRANSACTION_TYPE", transType);
            // response code
            String responseCode = this.convertCardVertificationCode(rs, param);
            if (null != param.getLang() && !param.getLang().isEmpty() && "vi".equalsIgnoreCase(param.getLang())) {
                responseCode = i18NResponseCode(responseCode);
            }
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            // status
            String transState = rs.getString("S_ADVANCE_STATUS");
            if ("Waiting for OnePAY's Approval".equalsIgnoreCase(transState))
                transState = "Waiting for OnePay's Approval";

            if (null != param.getLang() && !param.getLang().isEmpty() && "vi".equalsIgnoreCase(param.getLang())) {
                if ("Successful".equalsIgnoreCase(transState)) {
                    transState = "Thành công";
                } else if ("Failed".equalsIgnoreCase(transState)) {
                    transState = "Không thành công";
                } else if ("Processing".equalsIgnoreCase(transState)) {
                    transState = "Đang xử lý";
                } else if ("Waiting for authentication".equalsIgnoreCase(transState)) {
                    transState = "Chờ xác thực";
                } else if ("Processing".equalsIgnoreCase(transState)) {
                    transState = "Đang xử lý";
                } else if ("Waiting for Approval".equalsIgnoreCase(transState) || "Waiting for approval".equalsIgnoreCase(transState)) {
                    transState = "Đang chờ duyệt";
                } else if ("Waiting for OnePay's Approval".equalsIgnoreCase(transState)) {
                    transState = "GD chờ OnePay duyệt";
                } else if ("Onepay approved".equalsIgnoreCase(transState) || "OnePAY Approved".equalsIgnoreCase(transState)) {
                    transState = "OnePay đã xử lý";
                } else if ("Merchant approved".equalsIgnoreCase(transState) || "Merchant Approved".equalsIgnoreCase(transState)) {
                    transState = "Đơn vị đã chấp thuận";
                } else if ("Waiting for authentication".equalsIgnoreCase(transState) || "Waiting for Authentication".equalsIgnoreCase(transState)) {
                    transState = "Chờ xác thực";
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, transState);

            // fraud
            item.put("S_FRAUD", rs.getString("S_FRAUD"));

            // put into list
            listData.add(item);
        }

    }

    private String convertCardVertificationCode(ResultSet rs, DomesticTxnParameter param) throws SQLException {

        String status = String.valueOf(rs.getInt("N_TRANSACTION_STATUS"));
        String cardVerificationCode = String.valueOf(rs.getInt("N_CARD_VERIFICATION_CODE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String authTime = rs.getString("D_USER_AUTHENTICATION_DATE");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");

        if (advanceStatus == null) {
            return null;
        }
        // SPECIAL CASE : 17/04/2019
        if (acquirerId == 0 && status.equals("100") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100")) {
            return "No Select Bank";
        }
        if (advanceStatus.equals("Waiting for authentication")) {
            return "";
        }
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("99")) {
            return "Authentication cancelled";
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null
                && (acquirerId == 2 || acquirerId == 6 || acquirerId == 5 || acquirerId == 31 || acquirerId == 21)) {
            return "No Response from IB payment"; // case Techcombank, DongAbank, VIB, Viettelpay, CUP && onepay admin
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null) {
            return "Authentication failed";
        }
        if (status.equals("100") && cardVerificationCode.equals("100")) {
            return "Not process";
        }
        if (status.equals("300") && advanceStatus.equals("Successful") && cardVerificationCode.equals("100")) {
            return "0 - Approved";
        }
        // if (cardVerificationCode.equals("100")) {
        // return "";
        // }

        /*
        Special case with response code
        Case status is 200 and failed, response code is 100
        the response code will be change with specific bank requirements
        */
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100") && authTime != null) {
            Integer authCode = rs.getInt("N_USER_AUTHENTICATION_CODE");
            String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                    acquirerId + StringPool.DOT + authCode, "1"); // default is 1

            cardVerificationCode = specialResponseCode.length() == 0 ? "1" : specialResponseCode;
        }

        return cardVerificationCode + "-" + PropsUtil.get("D" + cardVerificationCode, "Not Response");
    }

    private List<Map> fillBackupData(List<Map> listData, Map<Integer, DomesticTransaction> tranMap, String version, String lang) {

        List<Map> result = new ArrayList<>();
        for (Map item : listData) {
            DomesticTransaction domesticTran = tranMap.get(item.get("N_ORIGINAL_ID"));
            if (null != domesticTran) {
                item.put(TemplateUtils.BANK_COLUMN, "v2".equals(version)
                        ? Converter.convertBankNDMA(domesticTran.getAcquirer().getAcquirer_id()) != null
                                ? Converter.convertBankNDMA(domesticTran.getAcquirer().getAcquirer_id())
                                : domesticTran.getAcquirer().getAcquirer_name()
                        : domesticTran.getAcquirer().getAcquirer_name());

                item.put(TemplateUtils.CARD_NO_COLUMN, domesticTran.getCard().getCard_number());
                item.put(TemplateUtils.AMOUNT_COLUMN, domesticTran.getAmount().getTotal());
                item.put(TemplateUtils.DATE_COLUMN, domesticTran.getTransaction_time());
                item.put("ORIGINAL_DATE", domesticTran.getTransaction_time());
                item.put("ORIGINAL_AMOUNT", domesticTran.getAmount().getTotal());
                item.put("S_ORDER_INFO", domesticTran.getOrder_info());

                String status = String.valueOf(domesticTran.getStatus());
                String cardVerificationCode = domesticTran.getCard().getCard_verification_code();
                String advanceStatus = domesticTran.getAdvance_status();
                Timestamp authTime = domesticTran.getAuth_time();
                int acquirerId = domesticTran.getAcquirer().getAcquirer_id();
                String state = "";
                if (advanceStatus == null) {
                    return null;
                }
                // SPECIAL CASE : 17/04/2019
                if (acquirerId == 0 && status.equals("100") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100")) {
                    state = "No Select Bank";
                }
                if (advanceStatus.equals("Waiting for authentication")) {
                    state = "";
                }
                if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("99")) {
                    state = "Authentication cancelled";
                } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null
                        && (acquirerId == 2 || acquirerId == 6 || acquirerId == 5 || acquirerId == 31 || acquirerId == 21)) {
                    state = "No Response from IB payment";
                } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null) {
                    state = "Authentication failed";
                }
                if (status.equals("100") && cardVerificationCode.equals("100")) {
                    state = "Not process";
                }
                if (status.equals("300") && advanceStatus.equals("Successful") && cardVerificationCode.equals("100")) {
                    state = "0 - Approved";
                }

                if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100") && authTime != null) {
                    Integer authCode = domesticTran.getAuth_code();
                    String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                            acquirerId + StringPool.DOT + authCode, "1"); // default is 1

                    cardVerificationCode = specialResponseCode.length() == 0 ? "1" : specialResponseCode;
                }
                state = cardVerificationCode + "-" + PropsUtil.get("D" + cardVerificationCode, "Not Response");
                if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    state = i18NResponseCode(state);
                }
                item.put(TemplateUtils.RESPONSE_CODE_COLUMN, state);

                result.add(item);
            }
        }
        return result;

    }

    public static String i18NResponseCode(String responseCode) {
        String result = "";
        if ("0 -  Approved".equalsIgnoreCase(responseCode) || "0-Approved".equalsIgnoreCase(responseCode)) {
            result = "0 - Giao dịch thành công";
        } else if ("1 - Declined".equalsIgnoreCase(responseCode) || "1-Declined".equalsIgnoreCase(responseCode)) {
            result = "1 - Ngân hàng từ chối giao dịch";
        } else if ("7 - Unspecified Failure".equalsIgnoreCase(responseCode) || "7-Unspecified Failure".equalsIgnoreCase(responseCode)) {
            result = "7 - Lỗi không xác định";
        } else if ("8 - Invalid Card No".equalsIgnoreCase(responseCode) || "8-Invalid Card No".equalsIgnoreCase(responseCode)
                || "8 - Invalid Card Number".equalsIgnoreCase(responseCode) || "8-Invalid Card Number".equalsIgnoreCase(responseCode)) {
            result = "8 - Số thẻ không đúng";
        } else if ("9 - Invalid Card name".equalsIgnoreCase(responseCode) || "9-Invalid Card name".equalsIgnoreCase(responseCode)) {
            result = "9 - Tên chủ thẻ không đúng";
        } else if ("10 - Expired Card".equalsIgnoreCase(responseCode) || "10-Expired Card".equalsIgnoreCase(responseCode)) {
            result = "10 - Thẻ hết hạn/Thẻ bị khóa";
        } else if ("11 - Not Registered".equalsIgnoreCase(responseCode) || "11-Not Registered".equalsIgnoreCase(responseCode)) {
            result = "11 - Thẻ chưa đăng ký sử dụng dịch vụ";
        } else if ("12 - Invalid Card Date".equalsIgnoreCase(responseCode) || "12-Invalid Card Date".equalsIgnoreCase(responseCode)) {
            result = "12 - Ngày phát hành/Hết hạn không đúng";
        } else if ("13 - Exceeded credit limit".equalsIgnoreCase(responseCode) || "13-Exceeded credit limit".equalsIgnoreCase(responseCode)) {
            result = "13 - Vượt quá hạn mức thanh toán";
        } else if ("21 - Insufficient fund".equalsIgnoreCase(responseCode) || "21-Insufficient fund".equalsIgnoreCase(responseCode)) {
            result = "21 - Số tiền không đủ để thanh toán";
        } else if ("22 - Invalid Account".equalsIgnoreCase(responseCode) || "22-Invalid Account".equalsIgnoreCase(responseCode)) {
            result = "22 - Thông tin tài khoản không đúng";
        } else if ("23 - Account Lock".equalsIgnoreCase(responseCode) || "23-Account Lock".equalsIgnoreCase(responseCode)) {
            result = "23 - Tài khoản bị khóa";
        } else if ("24 - Invalid Card Info".equalsIgnoreCase(responseCode) || "24-Invalid Card Info".equalsIgnoreCase(responseCode)) {
            result = "24 - Thông tin thẻ không đúng";
        } else if ("25 - Invalid OTP".equalsIgnoreCase(responseCode) || "25-Invalid OTP".equalsIgnoreCase(responseCode)) {
            result = "25 - OTP không đúng";
        } else if ("235 - Transaction Timeout".equalsIgnoreCase(responseCode) || "235-Transaction Timeout".equalsIgnoreCase(responseCode)) {
            result = "235 - Quá thời gian thanh toán";
        } else if ("99 - User Cancel".equalsIgnoreCase(responseCode) || "99-User Cancel".equalsIgnoreCase(responseCode)) {
            result = "99 - Người sử dụng hủy giao dịch";
        } else if ("Blank".equalsIgnoreCase(responseCode)) {
            result = "";
        } else if ("Authentication failed".equalsIgnoreCase(responseCode)) {
            result = "Xác thực không thành công";
        } else if ("Authentication cancelled".equalsIgnoreCase(responseCode)) {
            result = "Hủy xác thực";
        } else if ("Not process".equalsIgnoreCase(responseCode)) {
            result = "Chưa được xử lý";
        } else if ("No Response from IB payment".equalsIgnoreCase(responseCode)) {
            result = "Không có phản hồi từ IB Ngân hàng";
        } else if ("No Select Bank".equalsIgnoreCase(responseCode)) {
            result = "Không chọn ngân hàng";
        } else if ("Not Response".equalsIgnoreCase(responseCode)) {
            result = "Không phản hồi";
        }
        return result;
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionBuildImpl.class.getName());
}
