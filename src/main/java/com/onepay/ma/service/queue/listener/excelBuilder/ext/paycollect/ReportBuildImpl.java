package com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect;


import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.pay_collect.ReportService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("paycollectReport")
public class ReportBuildImpl implements ReportDataBuild<ReportSearchReq> {

    private static final Logger LOGGER = Logger.getLogger(ReportBuildImpl.class.getName());

    @Autowired
    private ReportService reportService;

    @Override
    public List<Map> generate(Connection connPaycollect, Connection readOnly, Connection connBackup, Message<ReportSearchReq> messageData) {
        List<Map> listMap = new ArrayList<>();
        ReportSearchReq query = messageData.getRequestBody();
        ResultSet rs = null;
        try {
            rs = this.reportService.download(connPaycollect, query);
            this.generateRs(rs, listMap,query.getInterval(), query.getGroupBy());
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[PAY COLLECT REPORT] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if(rs != null){
                try {
                    rs.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }
        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String interval, String groupBy) {
        int rowNumber = 0;
        Integer totalCount = 0;
        Double totalAmount = 0D;
        try {
            while (rs.next()) {
                rowNumber++;
                Map item = new HashMap();
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("dataType", rs.getString("DATA_TYPE") == null ? "" : formatDate(new Date(java.sql.Timestamp.valueOf(rs.getString("DATA_TYPE")).getTime()), interval));
                item.put("merchantId", rs.getString("S_MERCHANT_ID"));
                if ("accountNumber".equals(groupBy)) {
                    item.put("account", rs.getString("S_BANK_ACCOUNT") == null ? "" : rs.getString("S_BANK_ACCOUNT"));
                    item.put("accountName", rs.getString("S_FULL_NAME") == null ? "" : rs.getString("S_FULL_NAME"));
                }
                item.put("bankName", rs.getString("RECEIPT_BANK"));
                totalCount += rs.getInt("NO_TRANS");
                item.put("noTrans", rs.getInt("NO_TRANS"));
                totalAmount += rs.getDouble("TOTAL_TRANS");
                item.put("totalTrans", formatCurrencyDouble(rs.getDouble("TOTAL_TRANS")));
                listData.add(item);
            }
            HashMap mapCount = new HashMap();
            mapCount.put("totalCount",totalCount);
            listData.add( mapCount);
            HashMap mapAmount = new HashMap();
            mapAmount.put("totalAmount", formatCurrencyDouble(totalAmount));
            listData.add( mapAmount);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }
    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("###,###,###");
        return df.format(value);
    }

    public static String formatDate(Date dates, String interval) throws ParseException {
        String myDate = null;
        String partern = null;
        if (!dates.equals("")) {
            if (interval.equals("DD")) {
                partern = "dd-MM-yyyy";
            } else if (interval.equals("MM") ) {
                partern = "MM-yyyy";
            } else if (interval.equals("Q")) {
                SimpleDateFormat sfq = new SimpleDateFormat("yyyy");
                String year = sfq.format(dates);
                return  "Q"+((dates.getMonth() / 3) + 1) +","+year;
            }
            else {
                partern = "yyyy";
            }
            SimpleDateFormat sdf = new SimpleDateFormat(partern);
            try {
                myDate = sdf.format(dates);
            } catch (Exception ex) {
                throw ex;
            }
        } else {
            myDate = "";
        }
        return myDate;
    }

}
