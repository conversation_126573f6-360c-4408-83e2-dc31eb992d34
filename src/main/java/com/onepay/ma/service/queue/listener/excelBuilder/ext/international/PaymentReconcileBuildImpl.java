package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("reconcilePayment")
public class PaymentReconcileBuildImpl implements ReportDataBuild<ReconciliationQuery> {

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ReconciliationQuery> messageData) {
        ReconciliationQuery param = messageData.getRequestBody();

//        ReconciliationQuery parameter = new ReconciliationQuery();
//        parameter.setAcquirer(param.getAcquirerId());
//        parameter.setFrom_date(param.getFromDate());
//        parameter.setTo_date(param.getToDate());
//        parameter.setInterval(param.getInterval());
//        parameter.setCard_type(param.getCardType());
//        parameter.setCurrency(param.getCurrency());
//        parameter.setTransaction_type(param.getTransaction_type());
        String[] currencyList = {"VND", "USD"};
//        List<String> merchantIdList = param.getMerchantList();
//        parameter.setMerchant_id(String.join(",", merchantIdList));
        if (!param.getCurrency().isEmpty()) {
            currencyList = new String[1];
            currencyList[0] = param.getCurrency();
        }
        param.setCurrency(String.join(",", currencyList));

        List<Map> resultMap = new ArrayList<>();

        try {

            for (String currency : currencyList) {
                Map data = new HashMap();
                param.setCurrency(currency);
                List<Reconciliation> rs = this.reconcileService.downloadPayment(readOnly, param);
                data.put("listMap", this.generateRs(rs));
                resultMap.add(data);
            }

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "GENERATE INTERNATIONAL REPORT: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


        return resultMap;
    }

    private List<Map> generateRs(List<Reconciliation> rs) throws Exception {
        List<Map> listData =  new ArrayList<>();
        int rowNumber = listData.size();

        for (Reconciliation r : rs) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_SETTLEMENT_COLUMN, r.getSettlement_date());

            // Card Type
            item.put(TemplateUtils.ACQUIRER_COLUMN, r.getAcquirer());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, r.getCard_type());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, r.getCurrency());

            // Transaction Count
            item.put(TemplateUtils.TRANSACTION_COUNT_COLUMN, r.getCount_transaction());

            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, r.getCount_purchase());

            // Transaction Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN, r.getCount_refund());

            // Transaction Count
            item.put(TemplateUtils.VOID_PURCHASE_COUNT_COLUMN, r.getCount_void());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, r.getTotal_purchase());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, r.getTotal_refund());

            // Refund total Count
            item.put(TemplateUtils.VOID_PURCHASE_TOTAL_COUNT_COLUMN, r.getTotal_void());

            // Refund total Count
            item.put(TemplateUtils.AUTHORISE_TOTAL_COUNT_COLUMN, r.getTotal_authorise());

            // Refund total Count
            item.put(TemplateUtils.CAPTURE_TOTAL_COUNT_COLUMN, r.getTotal_capture());

            // put into list
            listData.add(item);
        }

        return listData;

    }

    @Autowired
    private ReconcileService reconcileService;


    private static final Logger LOGGER = Logger.getLogger(PaymentReconcileBuildImpl.class.getName());
}
