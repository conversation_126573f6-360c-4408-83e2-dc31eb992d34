package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;
import com.onepay.ma.service.models.InternationalRefundEmailTemplateParameter;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.IErrors;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 10/14/2020
 * Time: 10:03 AM
 * To change this ma-web.
 */
@Service("internationalRefundEmailTemplate")
public class InternationalRefundEmailTemplateBuildImpl implements ReportDataBuild<InternationalRefundEmailTemplateParameter> {
    private static final Logger LOGGER = Logger.getLogger(InternationalRefundEmailTemplateBuildImpl.class.getName());
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalRefundEmailTemplateParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        InternationalRefundEmailTemplateParameter query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            Map map = new HashMap();
            map.put("NO",1);
            map.put("S_MERCHANT_ID",query.getMerchant_id());
            map.put("S_CARD_NO",query.getCard_no());
            map.put("S_ORDER_INFO",query.getOrder_info());
            map.put("S_AUTHORIZATION_CODE",query.getAuthorization_code());
            map.put("DATE",query.getPurchase_date());
            map.put("AMOUNT",formatNumber(Double.parseDouble(query.getPurchase_amount()),query.getCurrency()));
            map.put("CURRENCY",query.getCurrency());
            listMap.add(map);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[REFUND TEMPLATE FAILED] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private String formatNumber(Double amount, String currency){
        DecimalFormat formatter = new DecimalFormat("###,###,###.00");
        if(currency.equals("VND")){
            formatter = new DecimalFormat("###,###,###");
        }
        return formatter.format(amount);

    }
}
