package com.onepay.ma.service.queue.listener.excelBuilder.ext.chartStatistics;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay.MpayTransactionBuildImpl;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import static java.util.stream.Collectors.*;

@Service("chartTotal")
public class ChartTotalBuildImpl implements ReportDataBuild<ChartStatisticQuery> {
    private static final Logger LOGGER = Logger.getLogger(ChartTotalBuildImpl.class.getName());
    @Autowired
    private StatisticChartService statisticChartService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ChartStatisticQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        ChartStatisticQuery query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.statisticChartService.dowloadFileTotal(readOnly, query);
            String paygate = query.getPaygate();
            this.generateRs(rsOnline, listMap,paygate);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData ,String payGate) throws Exception {
        List<Map> listMapClone = new ArrayList<>();
        int rowNumber = listMapClone.size();
        Integer sumSND = 0;
        Integer sumFND = 0;
        Integer sumSQT = 0;
        Integer sumFQT = 0;
        Integer sum = 0;
        System.out.println(payGate);
        while (rs.next()) {
            String nSuccess = rs.getString("SUCCESS_TRANSACTION");
            Integer iSuccess = Integer.parseInt(nSuccess);
            String nFailed = rs.getString("FAILED_TRANSACTION");
            Integer iFailed = Integer.parseInt(nFailed);
            sum += iSuccess + iFailed;
            if(rs.getString("PAYGATE").equals("ND")){
                sumSND += iSuccess;
                sumFND += iFailed;
            }else{
                sumSQT += iSuccess;
                sumFQT += iFailed;
            }
        }
        if(!payGate.contains("QT")){
            sumSQT = 0;
            sumFQT = 0;
        }
        if(!payGate.contains("ND")){
            sumSND = 0;
            sumFND = 0;
        }
        System.out.println(sum);
        for(int i = 1; i <=2 ; i++){
                Map item = new HashMap();
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, i);
                item.put("PAYGATE", i==1? "International Card":"Domestic Card");
                item.put("N_SUCCESS",i==1?  sumSQT:sumSND);
                item.put("N_FAILED",i==1?  sumFQT:sumFND);
                item.put("N_TOTAL", i==1? (sumSQT+sumFQT):(sumSND+sumFND));
                listData.add(item);
        }

    }
}
