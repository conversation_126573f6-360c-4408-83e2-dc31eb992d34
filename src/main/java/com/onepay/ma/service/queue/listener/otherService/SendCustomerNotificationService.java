package com.onepay.ma.service.queue.listener.otherService;

import com.google.gson.Gson;
import com.onepay.ma.service.models.notification.MpayNotificationPostDto;
import com.onepay.ma.service.models.notification.MpayvnNotificationMsg;
import com.onepay.ma.service.service.notification.impl.CustomerAppNotifyServiceImpl;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;

import io.vertx.core.json.JsonObject;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 30-Mar-18.
 */
@Component
public class SendCustomerNotificationService {

    @Autowired
    private CustomerAppNotifyServiceImpl customerAppNotifyService;

    public static String API_KEY = PropsUtil.get("customer_google_notifucation_api_key", ""); // mOnePAY

    public static String API_URL = PropsUtil.get("customer_google_notifucation_api_url", ""); // mOnePAY

    private static Logger LOGGER = Logger.getLogger(SendCustomerNotificationService.class.getName());


    private final static Gson gson = new Gson();

    public void send(Connection onlineConn, Connection readonlyConn, Connection backupConn, MpayvnNotificationMsg mapBody) throws SQLException {
        // INSERT notify message
        Integer msgId = customerAppNotifyService.insertMsg(backupConn, mapBody);
        try {
            // Get List Of Object(Information) by Mobile Number
            List<Map<String, String>> listMap = customerAppNotifyService.getAppTokenByMobileDSP(onlineConn, mapBody.getDestination());

            // Convert
            List<MpayNotificationPostDto> notificationPostDtos = listMap.stream().map(map -> {
                MpayNotificationPostDto dto = new MpayNotificationPostDto();
                dto.setUserId(map.get("userId"));
                dto.setMsgId(msgId);
                dto.setHeader_en(mapBody.getHeader_en());
                dto.setHeader_vi(mapBody.getHeader_vi());
                dto.setContent_en(mapBody.getContent_en());
                dto.setContent_vi(mapBody.getContent_vi());
                dto.setToken(map.get("token"));
                dto.setTarget(msgId.toString());
                dto.setCategory("NOTIFICATION");

                dto.setOs(map.get("os"));
                return dto;
            }).collect(Collectors.toList());


            LOGGER.log(Level.INFO, "FOUNDED TOKEN: " + notificationPostDtos.stream().map(MpayNotificationPostDto::getToken).collect(Collectors.toList()));
            try {
                String userTokenFireBase = Utils.getAccessToken();
                // insert notification into database for each user
                for (MpayNotificationPostDto postDto : notificationPostDtos) {
                    Integer id = customerAppNotifyService.insertNotify(backupConn, postDto);
                    postDto.setId(id);
                    this.sendNotification(mapBody.getHeader_vi(), mapBody.getContent_vi(), postDto, userTokenFireBase);

                }
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Notify not send ", e);
                LOGGER.log(Level.SEVERE, "userTokenFireBase cant get, Error! ", e);
            }

            // convert to Senable message
//            List<AppToken> foundedToken = listMap.stream().map(tokenMap -> {
//                AppToken appToken = new AppToken();
//                appToken.setOs(tokenMap.get("os"));
//                appToken.setToken(tokenMap.get("token"));
//                appToken.setUserId(tokenMap.get("userId"));
//                return appToken;
//            }).collect(Collectors.toList());


            // Send Notification
//            for (AppToken token : foundedToken) {

                // send notification
//                this.sendNotification(mapBody.getContent_vi(), token, msgId.toString());
//            }
            customerAppNotifyService.sendMsgState(backupConn, msgId);
        } catch (Exception ex) {

            customerAppNotifyService.failMsgState(backupConn, msgId);
        }

    }


    private void sendNotification(String title, String messageContent, MpayNotificationPostDto token, String userTokenFireBase) {

        try {

            // Prepare JSON containing the GCM message content. What to send and where to send.
            JsonObject jGcmData = new JsonObject();
            JsonObject jGcmMessNew = new JsonObject();

            if (token.getOs().equalsIgnoreCase("iOS") || token.getOs().equalsIgnoreCase("iPhone OS")) {
                JsonObject jNotification = new JsonObject();
                jNotification.put("title", title);
                jNotification.put("body", messageContent);
                // jNotification.put("sound", "default");
                // jNotification.put("badge", 1);
                jGcmData.put("notification", jNotification);
                // jGcmData.put("priority", "high");
            }
            // jGcmData.put("to", token.getToken());
            jGcmData.put("token", token.getToken());

            JsonObject jData = new JsonObject();

            if (token.getOs().equalsIgnoreCase("Android")) {
                jData.put("title", title);
                jData.put("body", messageContent);
                jData.put("sound", "default");
            }
            jData.put("target_msg_id", token.getMsgId());
            jData.put("target_id", token.getId().toString());
            // What to send in GCM message.
            jGcmData.put("data", jData);
            // jGcmData.put("content_available", true);
            // jGcmData.put("mutable_content", true);
            // jGcmData.put("target_msg_id", token.getMsgId());
            // jGcmData.put("target_id", token.getId());

            // What to send in GCM message New.
            jGcmMessNew.put("message", jGcmData);
            // Create connection to send GCM Message request.
            URL url = new URL(API_URL);
//            URL url = new URL("https://fcm.googleapis.com/fcm/send");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // conn.setRequestProperty("Authorization", "key=" + API_KEY);
            conn.setRequestProperty("Authorization", "Bearer " + userTokenFireBase);

            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);


            LOGGER.log(Level.INFO, "SEND MPAVN NOTIFICATION TO TOKEN: " + token.getToken());
            LOGGER.log(Level.INFO, "SEND MPAVN NOTIFICATION WITH CONTENT: " + jGcmMessNew.toString());
            // Send GCM message content.
            OutputStream outputStream = conn.getOutputStream();
            outputStream.write(jGcmMessNew.toString().getBytes());
            // Read GCM response.
            InputStream inputStream = conn.getInputStream();
            String resp = IOUtils.toString(inputStream);

            // LOGGER.log(Level.INFO, "RESPONSE: " + resp);
            LOGGER.log(Level.INFO, "Check your device/emulator for notification or logcat for " +
                    "confirmation of the receipt of the GCM message.");
        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "Unable to send GCM message.");
            LOGGER.log(Level.WARNING, "Please ensure that API_KEY has been replaced by the server " +
                    "API key, and that the device's registration token is correct (if specified).");
            LOGGER.log(Level.WARNING, "Error ", e);
        }
    }

}
