package com.onepay.ma.service.queue.message;

import javax.jms.Queue;
import java.io.Serializable;
import java.util.Map;

/**
 * Created by HungDX on 23-Jan-16.
 */
public class Message<T> implements Serializable {

    private T requestBody;
    private Map requestData;
    private int messagePriority;
    private String path;
    private int resultCode;
    private String resultString;
    private Queue forwardQueue;
    private Queue destinationQueue;

    public Message(T requestBody, Map requestData, int messagePriority, String path, Queue forwardQueue, Queue destinationQueue) {
        this.requestBody = requestBody;
        this.requestData = requestData;
        this.messagePriority = messagePriority;
        this.path = path;
        this.forwardQueue = forwardQueue;
        this.destinationQueue = destinationQueue;
    }

    public Message(T requestBody) {
        this.requestBody = requestBody;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultString() {
        return resultString;
    }

    public void setResultString(String resultString) {
        this.resultString = resultString;
    }

    public Map getRequestData() {
        return requestData;
    }

    public void setRequestData(Map requestData) {
        this.requestData = requestData;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Queue getForwardQueue() {
        return forwardQueue;
    }

    public void setForwardQueue(Queue forwardQueue) {
        this.forwardQueue = forwardQueue;
    }

    public Queue getDestinationQueue() {
        return destinationQueue;
    }

    public void setDestinationQueue(Queue destinationQueue) {
        this.destinationQueue = destinationQueue;
    }

    public int getMessagePriority() {
        return messagePriority;
    }

    public void setMessagePriority(int messagePriority) {
        this.messagePriority = messagePriority;
    }


    public T getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(T requestBody) {
        this.requestBody = requestBody;
    }

}
