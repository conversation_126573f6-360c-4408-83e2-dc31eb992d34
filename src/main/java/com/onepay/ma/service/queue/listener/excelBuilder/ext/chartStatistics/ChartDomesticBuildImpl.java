package com.onepay.ma.service.queue.listener.excelBuilder.ext.chartStatistics;
import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay.MpayTransactionBuildImpl;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.Converter;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("chartDomestic")
public class ChartDomesticBuildImpl implements ReportDataBuild<ChartStatisticQuery> {
    private static final Logger LOGGER = Logger.getLogger(ChartDomesticBuildImpl.class.getName());
    @Autowired
    private StatisticChartService statisticChartService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ChartStatisticQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        ChartStatisticQuery query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.statisticChartService.dowloadfile(readOnly, query,"ND");
            this.generateRs(rsOnline, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            Date transactionTime = rs.getDate("D_DATE");
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);


            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));
//            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");

            // S_CARD_NO
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NO"));

            // N_AMOUNT
            item.put("N_AMOUNT", rs.getInt("N_AMOUNT"));

            // TRANS_REF
//            item.put("N_BANK_ID", rs.getString("N_BANK_ID"));
            item.put("TRANS_REF", rs.getString("TRANS_REF"));

            // bank
            item.put("N_BANK_ID", Converter.convertBankNDMA(rs.getInt("N_BANK_ID")));


            // amount rs.getString("S_RESPONSE_CODE")
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, rs.getString("S_ADV_RESPONSE_CODE"));

            // date
            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("S_ADVANCE_STATUS"));

            // put into list
            listData.add(item);
        }

    }
}
