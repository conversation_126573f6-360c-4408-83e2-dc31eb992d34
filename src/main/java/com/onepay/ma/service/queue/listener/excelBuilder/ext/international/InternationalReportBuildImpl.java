package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.InternationalReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("internationalReport")
public class InternationalReportBuildImpl implements ReportDataBuild<InternationalReportParameterFile> {

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<InternationalReportParameterFile> messageData) {
        InternationalReportParameterFile param = messageData.getRequestBody();

        InternationalReportParameter parameter = new InternationalReportParameter();
        parameter.setAcquirerId(param.getAcquirerId());
        parameter.setFromDate(param.getFromDate());
        parameter.setToDate(param.getToDate());
        parameter.setInterval(param.getInterval());
        parameter.setCardType(param.getCardType());
        parameter.setKeywords(param.getKeywords());
        parameter.setVersion(param.getVersion());
        parameter.setCurrency(param.getCurrency());
        parameter.setLang(param.getLang());
        String[] currencyList = {"VND", "USD", "THB", "SGD", "MYR", "IDR", "JPY", "KRW", "TWD", "CNY"};
        List<String> merchantIdList = param.getMerchantList();
        parameter.setMerchantId(String.join(",", merchantIdList));

        List<Map> resultMap = new ArrayList<>();

        try {
            if (!parameter.getCurrency().isEmpty() && parameter.getCurrency() != null) {

                Map data = new HashMap();
                List<InternationalReport> reports = internationalReportService.downloadByCurrency(readOnly, parameter);
                data.put("listMap", this.generateRs(reports, param.getLang()));
                resultMap.add(data);
            } else {
                Map data;
                List<Map> dataRs;
                for (String currency : currencyList) {
                    data = new HashMap<>();
                    dataRs = new ArrayList<>();
                    parameter.setCurrency(currency);
                    if ("v2".equals(parameter.getVersion())) {
                        List<InternationalReport> reports = internationalReportService.downloadByCurrency(readOnly, parameter);
                        dataRs = this.generateRs(reports, param.getLang());
                    } else {
                        ResultSet rs = this.internationalReportService.downloadDetail(readOnly, parameter);
                        dataRs = this.generateRs(rs, param.getLang());
                    }
                    if (!dataRs.isEmpty()) {
                        data.put("listMap", dataRs);
                        resultMap.add(data);
                    }

                }
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "GENERATE INTERNATIONAL REPORT: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return resultMap;
    }

    private List<Map> generateRs(ResultSet rs, String lang) throws Exception {
        List<Map> listData = new ArrayList<>();
        int rowNumber = listData.size();

        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getTimestamp("D_DATE"));

            // date
            item.put(TemplateUtils.DATE_SETTLEMENT_COLUMN, rs.getTimestamp("D_SETTLEMENT"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANTID"));

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARDTYPE"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDERREFERENCE"));

            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, rs.getString("S_TRANSACTIONID"));

            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARDNO"));

            item.put(TemplateUtils.TRANS_TYPE_COLUMN, rs.getString("S_TRANSACTIONTYPE"));

            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getDouble("N_AMOUNT"));

            item.put(TemplateUtils.AUTH_CODE_COLUMN, rs.getString("S_AUTHORISATIONCODE"));

            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCHANTTRANSACTIONREFEREN"));


            // put into list
            listData.add(item);
        }

        return listData;

    }

    private List<Map> generateRs(List<InternationalReport> list, String lang) throws Exception {
        List<Map> listData = new ArrayList<>();
        int rowNumber = listData.size();

        for (InternationalReport itemReport : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getReport_date());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchant_id());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, itemReport.getCard_type());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, itemReport.getCurrency());

            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getTransaction_count());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, itemReport.getTransaction_total());

            // Refund Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN, itemReport.getRefund_count());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, itemReport.getRefund_total());

            // Authorize Count
            item.put(TemplateUtils.N_AUTHORIZE, itemReport.getAuthorize_count());
            // Authorize total Count
            item.put(TemplateUtils.AUTHORISE_TOTAL_COUNT_COLUMN, itemReport.getAuthorize_total());
            // Capture Count
            item.put(TemplateUtils.N_CAPTURE, itemReport.getCapture_count());
            // Capture total Count
            item.put(TemplateUtils.CAPTURE_TOTAL_COUNT_COLUMN, itemReport.getCapture_total()); 

            // put into list
            listData.add(item);
        }

        return listData;
    }

    @Autowired
    private InternationalReportService internationalReportService;


    private static final Logger LOGGER = Logger.getLogger(InternationalRefundBuildImpl.class.getName());
}
