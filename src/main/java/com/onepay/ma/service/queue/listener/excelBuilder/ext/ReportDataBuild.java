package com.onepay.ma.service.queue.listener.excelBuilder.ext;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 05-Jun-17.
 */
public interface ReportDataBuild<T> {


    List<Map> generate(Connection connOnline, Connection readOnly,Connection connBackup, com.onepay.ma.service.queue.message.Message<T> messageData);

    default List<Map> generate(Connection connOnline, Connection readOnly, com.onepay.ma.service.queue.message.Message<T> messageData) {
        return this.generate(connOnline, readOnly, connOnline, messageData);
    };

    default List<Map> generate(Connection conn, com.onepay.ma.service.queue.message.Message<T> messageData) {
        return this.generate(conn, conn, messageData);
    }
}
