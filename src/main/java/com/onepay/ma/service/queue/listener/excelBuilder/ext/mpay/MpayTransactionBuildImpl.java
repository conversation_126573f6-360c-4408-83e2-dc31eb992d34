package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.models.mpay.MpayTransactionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.stereotype.Service;
import io.vertx.core.json.JsonObject;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 13-Jun-17.
 */
@Service("mpayTransaction")
public class MpayTransactionBuildImpl implements ReportDataBuild<MpayTransactionQuery> {

    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());

    private static Map<String, String> channelMap = new HashMap<String, String>() {
        {
            put("DSP", "mPayVN");
            put("MOMO", "MOMO");
            put("VIETINQR", "mPayVN");
            put("VIETINQR", "Vietin");
            put("DSP_VRBANK", "VRB");
            put("BIDV", "BIDV");
            put("VIB", "VIB");
            put("MSBQR", "MSB");
        }
    };

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayTransactionQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayTransactionQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            rsBackup = MpayTransactionService.downloadBackup(connBackup, param);
            this.generateRsBackup(rsBackup, listMap, param.getLang());
            List<String> transIdList = listMap.stream().map(map -> map.get("N_ORIGINAL_ID") + "").collect(Collectors.toList());
            Map transMap = MpayTransactionService.transMapByIdsDownload(connOnline, String.join(StringPool.COMMA, transIdList), param);
            listMap = fillBackupData(listMap, transMap, param.getLang());

            rsOnline = MpayTransactionService.downloadOnline(connOnline, param);
            rsReadonly = MpayTransactionService.downloadReadonly(readOnly, param);
            this.generateRs(rsOnline, listMap, param.getLang());
            this.generateRs(rsReadonly, listMap, param.getLang());
            listMap.sort((o1, o2) -> {
                Date d2 = (Date) o2.get(TemplateUtils.DATE_COLUMN);
                Date d1 = (Date) o1.get(TemplateUtils.DATE_COLUMN);
                return d2.compareTo(d1);
            });
            int index = 1;
            for (Map data : listMap) {
                data.put(TemplateUtils.ROW_NUMBER_COLUMN, index++);
            }
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRsBackup(ResultSet rs, List<Map> listData, String lang) throws Exception {
        Map item;
        while (rs.next()) {
            item = new HashMap();
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));
            item.put("N_ORIGINAL_ID", rs.getString("N_ORIGINAL_ID"));

            item.put("TRANSACTION_TYPE", rs.getString("S_TRANSACTION_TYPE"));
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                item.put("TRANSACTION_TYPE", "Yêu cầu hoàn trả");
            }

            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("N_TRANSACTION_STATUS"));
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_MERCHANT_TRANSACTION_DATE"));
            listData.add(item);
        }

    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        Map item;
        while (rs.next()) {
            item = new HashMap();

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, rs.getString("S_MERCHANT_NAME"));

            // merchant Name
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, rs.getString("S_ONECOM_MERCHANT") == null ? StringPool.BLANK : rs.getString("S_ONECOM_MERCHANT"));

            // merchant transaction ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCH_TXN_REF") == null ? StringPool.BLANK : rs.getString("S_MERCH_TXN_REF"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("S_ID"));

            // customer trans Id
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, rs.getString("S_CUSTOMER_TRANS_ID"));

            // S_QR_ID
            item.put("S_QR_ID", rs.getString("S_QR_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_INSTRUMENT_NUMBER"));

            item.put("S_APP_NAME", rs.getString("S_APP_NAME"));
            item.put("S_MASKING", rs.getString("S_MASKING"));
            item.put("S_INVOICE_ID", rs.getString("S_INVOICE_ID"));
            item.put("S_QR_ID", rs.getString("S_QR_ID"));
            item.put("S_CHANNEL", channelMap.get(rs.getString("S_CLIENT_ID")) == null ? rs.getString("S_CLIENT_ID") : channelMap.get(rs.getString("S_CLIENT_ID")));
            // item.put("S_CARD_NUMBER", rs.getString("S_CARD_NUMBER"));

            // bank
            String instrumentType = Objects.equals(rs.getString("S_INSTRUMENT_TYPE"), "ewallet") ? rs.getString("S_INSTRUMENT_BRAND_ID")
                    : Objects.equals(rs.getString("S_INSTRUMENT_TYPE"), "")
                            || (rs.getString("S_INSTRUMENT_TYPE") == null || rs.getString("S_INSTRUMENT_TYPE").isEmpty()) ? rs.getString("S_INSTRUMENT_BRAND_ID") : PropsUtil.get(rs.getString("S_INSTRUMENT_TYPE"), "");
            item.put(TemplateUtils.BANK_COLUMN, instrumentType);

            // Bank ID
            item.put(TemplateUtils.BANK_REF_COLUMN, rs.getString("S_BANK_TRANS_ID"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_CREATE"));

            // client
            item.put(TemplateUtils.CLIENT_COLUMN, rs.getString("S_CLIENT_ID"));
            JsonObject jData = null;
            String tid = "";
            String mid = "";
            try {
                jData = new JsonObject(rs.getString("S_DATA") != null ? rs.getString("S_DATA") : "{}");
                tid = jData.containsKey("msb_qr") && jData.getJsonObject("msb_qr").containsKey("bank_terminal_id") ? jData.getJsonObject("msb_qr").getString("bank_terminal_id") : tid;
                mid = jData.containsKey("msb_qr") && jData.getJsonObject("msb_qr").containsKey("bank_merchant_id") ? jData.getJsonObject("msb_qr").getString("bank_merchant_id") : mid;
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "parse tid mid error", e);
            }
            // tid
            item.put(TemplateUtils.TID_COLUMN, tid);

            // mid
            item.put(TemplateUtils.MID_COLUMN, mid);

            // S_INSTRUMENT_NAME
            item.put("S_INSTRUMENT_NAME", rs.getString("S_INSTRUMENT_NAME"));

            // S_INSTRUMENT_BRAND_ID
            item.put("S_INSTRUMENT_BRAND_ID", rs.getString("S_INSTRUMENT_BRAND_ID") == null || rs.getString("S_INSTRUMENT_BRAND_ID").equals("") ? "" : rs.getString("S_INSTRUMENT_BRAND_ID").toUpperCase());

            // status
            String status = rs.getString("S_STATE");
            if (status != null) {
                if ("approved".equals(status))
                    status = "Successful";
                else if ("pending".equals(status))
                    status = "Pending";
                else if ("failed".equals(status))
                    status = "Failed";
                if ("Waiting for OnePAY's Approval".equalsIgnoreCase(status) || "Waiting for onepays approval".equalsIgnoreCase(status)) {
                    status = "Waiting for OnePay's Approval";
                } else if ("Waiting for approval".equalsIgnoreCase(status) || "Wait for approval".equalsIgnoreCase(status)) {
                    status = "Waiting for Approval";
                }
            }
            if (status != null && lang != null && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("approved".equals(rs.getString("S_STATE")))
                    status = "Thành công";
                else if ("pending".equals(rs.getString("S_STATE")))
                    status = "Đang chờ xử lý";
                else if ("failed".equals(rs.getString("S_STATE")))
                    status = "Không thành công";
                else if ("Waiting for OnePay's Approval".equalsIgnoreCase(rs.getString("S_STATE"))) {
                    status = "GD chờ OnePay duyệt";
                } else if ("Waiting for Approval".equalsIgnoreCase(rs.getString("S_STATE"))) {
                    status = "Đang chờ duyệt";
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);
            String transType = rs.getString("S_TRANSACTION_TYPE");

            if (!"Purchase".equalsIgnoreCase(transType)) {
                item.put("ORIGINAL_DATE", rs.getDate("D_CREATE"));
                item.put("ORIGINAL_AMOUNT", rs.getString("N_AMOUNT"));
            } else {
                item.put("ORIGINAL_DATE", "");
                item.put("ORIGINAL_AMOUNT", "");
            }
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("Purchase".equalsIgnoreCase(transType)) {
                    transType = "Thanh toán";
                } else if ("Refund".equalsIgnoreCase(transType)) {
                    transType = "Hoàn tiền";
                } else if ("Request refund".equalsIgnoreCase(transType)) {
                    transType = "Yêu cầu hoàn trả";
                }
            }
            item.put("TRANSACTION_TYPE", transType);
            // status
            item.put(TemplateUtils.ACQ_CODE_COLUMN, rs.getString("S_MSP_ID"));

            // put into list
            listData.add(item);
        }

    }

    private List<Map> fillBackupData(List<Map> listData, Map<String, MpayTransaction> tranMap, String lang) {

        List<Map> result = new ArrayList<>();

        for (Map item : listData) {
            MpayTransaction mpayTran = tranMap.get(item.get("N_ORIGINAL_ID"));
            if (null != mpayTran) {
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, mpayTran.getMerchantId());

                // merchant Id
                item.put(TemplateUtils.MERCHANT_NAME_COLUMN, mpayTran.getMerchantName());

                // merchant Name
                item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, mpayTran.getOnecomMerchantId() == null ? StringPool.BLANK : mpayTran.getOnecomMerchantId());

                // merchant transaction ref
                item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, mpayTran.getMerchantTxnRef() == null ? StringPool.BLANK : mpayTran.getMerchantTxnRef());

                // customer trans Id
                item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, mpayTran.getCustomerTransId());

                // S_QR_ID
                item.put("S_QR_ID", mpayTran.getQrId());

                // Order Info
                item.put(TemplateUtils.ORDER_INFO_COLUMN, mpayTran.getOrderInfo());

                // Card no
                item.put(TemplateUtils.CARD_NO_COLUMN, mpayTran.getInstrument().getNumber());

                item.put("S_APP_NAME", mpayTran.getAppName());
                item.put("S_MASKING", mpayTran.getMasking());
                item.put("S_INVOICE_ID", mpayTran.getInvoiceId());
                item.put("S_CHANNEL", channelMap.get(mpayTran.getClientId()) == null ? mpayTran.getClientId() : channelMap.get(mpayTran.getClientId()));

                // bank
                String instrumentType = Objects.equals(mpayTran.getInstrument().getType(), "ewallet") ? mpayTran.getInstrument().getBrandId()
                        : Objects.equals(mpayTran.getInstrument().getType(), "")
                                || (mpayTran.getInstrument().getType() == null || mpayTran.getInstrument().getType().isEmpty()) ? mpayTran.getInstrument().getBrandId() : PropsUtil.get(mpayTran.getInstrument().getType(), "");
                item.put(TemplateUtils.BANK_COLUMN, instrumentType);

                // Bank ID
                item.put(TemplateUtils.BANK_REF_COLUMN, mpayTran.getBankTransId());

                // currency
                item.put(TemplateUtils.CURRENCY_COLUMN, mpayTran.getAmount().getCurrency());

                // client
                item.put(TemplateUtils.CLIENT_COLUMN, mpayTran.getClientId());

                // S_INSTRUMENT_NAME
                item.put("S_INSTRUMENT_NAME", mpayTran.getInstrument().getName());

                // S_INSTRUMENT_BRAND_ID
                item.put("S_INSTRUMENT_BRAND_ID", mpayTran.getInstrument().getBrandId() == null || mpayTran.getInstrument().getBrandId().equals("") ? "" : mpayTran.getInstrument().getBrandId().toUpperCase());

                // status
                Object status = item.get(TemplateUtils.STATUS_COLUMN);
                String state = "";
                if (status != null) {
                    if (status.equals("401"))
                        state = "Waiting for Approval";
                    else if (status.equals("405"))
                        state = "Waiting for OnePay's Approval";
                }
                if (status != null && lang != null && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    if ("Waiting for OnePay's Approval".equals(state)) {
                        state = "GD chờ OnePay duyệt";
                    } else if ("Waiting for Approval".equals(state)) {
                        state = "Đang chờ duyệt";
                    }
                }
                item.put(TemplateUtils.STATUS_COLUMN, state);
                item.put("ORIGINAL_DATE", mpayTran.getCreateTime());
                item.put("ORIGINAL_AMOUNT", String.format("%.0f", mpayTran.getAmount().getTotal()));
                // status
                result.add(item);
            }
        }
        return result;

    }
}
