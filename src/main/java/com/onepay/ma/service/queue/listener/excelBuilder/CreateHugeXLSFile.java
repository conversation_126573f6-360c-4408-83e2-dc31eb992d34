package com.onepay.ma.service.queue.listener.excelBuilder;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;

import net.sf.jxls.transformer.XLSTransformer;
import oracle.net.aso.b;

public class CreateHugeXLSFile {
    private static final Logger LOGGER = Logger.getLogger(CreateHugeXLSFile.class.getName());

    private static final int ROW_TITLE = 3;
    private static final int ROW_COL_HEADER = 6;
    private static final int ROW_FROM_TO = 4;
    private static final int ROW_START = 7;
    private static final String DEFAULT_FONT = "Times New Roman";

    private String fileName;
    private String fromDate;
    private String toDate;
    private String title;
    private List<String> headers;
    private List<Map> datas;

    public void getAllInvoice() {
        String sqlStr = "select s_id id, s_state state, s_merchant_id merchant, s_info info, s_input_type type, n_amount amount from tb_invoice where rownum <= 100000";

        // create DB connect
        try (Connection conn = DriverManager.getConnection("******************************************", "msp",
                "msp")) {

            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sqlStr);
            // Extract data from result set
            testXSSFWorkbook(rs);
            // jxlsWritingExcel(rs);
            // while (rs.next()) {
            // System.out.print("ID: " + rs.getInt(0));

            // }
        } catch (SQLException e) {
            System.err.format("SQL State: %s\n%s", e.getSQLState(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void testXSSFWorkbook(ResultSet rs) throws SQLException, IOException {
        FileInputStream inputStream = new FileInputStream("src/main/resources/template/test_template.xlsx");

        XSSFWorkbook wb_template = new XSSFWorkbook(inputStream);
        // XSSFSheet sheet = wb_template.createSheet("Datatypes in Java");
        inputStream.close();

        SXSSFWorkbook wb = new SXSSFWorkbook(wb_template);
        wb.setCompressTempFiles(true);

        SXSSFSheet sh = (SXSSFSheet) wb.getSheetAt(0);
        sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk
        int rownum = 7;
        while (rs.next()) {
            Row row = sh.createRow(rownum);
            row.createCell(0).setCellValue(rs.getString("id"));
            row.createCell(1).setCellValue(rs.getString("state"));
            row.createCell(2).setCellValue(rs.getString("merchant"));
            row.createCell(3).setCellValue(rs.getString("info"));
            row.createCell(4).setCellValue(rs.getString("type"));
            row.createCell(5).setCellValue(rs.getString("amount"));
            rownum++;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("ddMMyyyyHHmmss");
        Date date = new Date();
        System.out.println(formatter.format(date));
        FileOutputStream out = new FileOutputStream(
                "src/main/resources/template/test_template_" + formatter.format(date) + ".xlsx");
        wb.write(out);
        out.close();
    }

    public CreateHugeXLSFile(String fileName, String title, String fromDate, String todate, List<String> headers, List<Map> datas) {
        this.fileName = fileName;
        this.title = title;
        this.fromDate = fromDate;
        this.toDate = todate;
        this.headers = headers;
        this.datas = datas;
    }

    public CellStyle setTitleStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 18);
        font.setFontName(DEFAULT_FONT);
        font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cs.setFont(font);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        return cs;
    }

    public CellStyle setColHeaderStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 9);
        font.setFontName(DEFAULT_FONT);
        font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cs.setFont(font);
        cs.setFillForegroundColor((short) 0);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        return cs;
    }

    public CellStyle setDataStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName(DEFAULT_FONT);
        cs.setFont(font);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        return cs;
    }

    public CellStyle setFromToStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName(DEFAULT_FONT);
        font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cs.setFont(font);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        return cs;
    }

    public void write() {
        LOGGER.info("Start writing " + fileName + " ");
        try {
            FileInputStream inputStream = new FileInputStream("src/main/resources/template/test_template.xlsx");

            XSSFWorkbook wb_template = new XSSFWorkbook(inputStream);
            // XSSFSheet sheet = wb_template.createSheet("Datatypes in Java");
            inputStream.close();

            SXSSFWorkbook wb = new SXSSFWorkbook(wb_template);
            wb.setCompressTempFiles(true);
            SXSSFSheet sh = (SXSSFSheet) wb.getSheetAt(0);
            sh.setDefaultColumnWidth(25);
            sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk

            createExcelTitle(sh, setTitleStyle(wb));
            // createExcelFromTo(sh, setFromToStyle(wb));
            createExcelColHeader(sh, setColHeaderStyle(wb));
            // createExcelData(sh, setDataStyle(wb));

            FileOutputStream out = new FileOutputStream("src/main/resources/template/" + fileName);
            wb.write(out);
            out.close();
            LOGGER.info("Writing " + fileName + " successfull");
        } catch (Exception e) {
            LOGGER.warning("Writing " + fileName + " fail");
            LOGGER.log(Level.SEVERE, e.getMessage());
        }

    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    private void createExcelColHeader(SXSSFSheet sh, CellStyle cs) {
        if (Objects.isNull(headers))
            return;
        Row row = sh.createRow(ROW_COL_HEADER);
        for (int i = 0; i < headers.size(); i++) {
            String celValue = Objects.toString(headers.get(i), "");
            Cell cell = row.createCell(i);
            cell.setCellValue(StringUtils.upperCase(celValue));
            cell.setCellStyle(cs);
        }
    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    private void createExcelTitle(SXSSFSheet sh, CellStyle cs) {
        Row row = sh.createRow(ROW_TITLE);
        title = Objects.toString(title, "");
        Cell cell = row.createCell(0);
        cell.setCellValue(StringUtils.upperCase(title));
        cell.setCellStyle(cs);
    }

    /**
     * ghi F
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    public static void createExcelDetail(SXSSFSheet sh, XSSFSheet templateSheet, Map detail) {
        for (Object key : detail.keySet()) {
            Cell fc = findCellKeyDetail(templateSheet, key.toString());
            if(fc == null) continue;
            // Row r = sh.getRow(fc.getRowIndex()) == null ? sh.createRow(fc.getRowIndex()) : sh.getRow(fc.getRowIndex());
            // Cell c = r.getCell(fc.getColumnIndex()) == null ? r.createCell(fc.getColumnIndex()) : r.getCell(fc.getColumnIndex());
            writeCell(fc, detail.get(key.toString()));
            // c.setCellStyle(fc.getCellStyle());
        }
    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    public static void createExcelData(SXSSFSheet sh, XSSFSheet templateSheet, List<Map> datas, Integer startRow) {
        int rownum = startRow;

        

        // keyCollumnIndexMap: map key va vi tri cot cua no trong excel
        Map<String, Integer> keyCollumnIndexMap = new HashMap<>();
        Map<Integer, CellStyle> collumnStyleIndexMap = new HashMap<>();
        Row templateRow = templateSheet.getRow(rownum);

        for (int i = 0; i < datas.size(); i++) {
            Map bean = datas.get(i); 

            SXSSFRow row =  (SXSSFRow) (sh.getRow(rownum + i) == null ? sh.createRow(rownum + i) : sh.getRow(rownum + i));

            for (Object beanKey : bean.keySet()) {
                if (keyCollumnIndexMap.get(beanKey.toString()) == null) {
                    // TODO: tim vi tri cot cua key tuong ung trong excel
                    Integer collumnI = findCollumnIndexOfKey(templateSheet, beanKey.toString(), templateRow);
                    if (collumnI != -1) {
                        // row.getCell
                        keyCollumnIndexMap.put(beanKey.toString(), collumnI);
                        collumnStyleIndexMap.put(collumnI, templateRow.getCell(collumnI).getCellStyle());
                    } else {
                        continue;
                    }
                }
                SXSSFCell cell = (SXSSFCell)row.createCell(keyCollumnIndexMap.get(beanKey.toString())); 
                writeCell(cell, bean.get(beanKey));
                // set style
                cell.setCellStyle(collumnStyleIndexMap.get(getCollumnIndexByKey(keyCollumnIndexMap, beanKey.toString())));


            } 
        }

    }

    private static void writeCell(Cell c, Object fieldValue){
        if(fieldValue == null) {
            c.setCellValue("");
        }
        else if (fieldValue instanceof String) {
            c.setCellValue(fieldValue.toString());
        } else if (fieldValue instanceof Date) {
            c.setCellValue((Date) fieldValue);
        } else if (fieldValue instanceof Timestamp) {
            Timestamp d = (Timestamp) fieldValue;
            c.setCellValue(new Date(d.getTime()));
        } else if (fieldValue instanceof Integer) {
            c.setCellValue((Integer) fieldValue);
        } else if (fieldValue instanceof Boolean) {
            c.setCellValue((Boolean) fieldValue);
        } else if (fieldValue instanceof Long) {
            c.setCellValue((Long) fieldValue);
        } else if (fieldValue instanceof Double) {
            c.setCellValue((Double) fieldValue);
        } else {
            c.setCellValue(fieldValue.toString());
        }
    }

    // tim vi tri cot cua key tuong ung trong excel
    private static Cell findCellKeyDetail(XSSFSheet sh, String key) {
        String beanKeyS = "${detail." + key + "}";
        for (int rowNum = sh.getFirstRowNum(); rowNum <= sh.getLastRowNum(); rowNum++) {
            Row row = sh.getRow(rowNum);
            if (row == null) {
                continue;
            }
            for (int cellNum = 0; cellNum < row.getLastCellNum(); cellNum++) {
                Cell cell = row.getCell(cellNum, Row.RETURN_BLANK_AS_NULL);
                
                if (cell != null && cell.getStringCellValue().equals(beanKeyS)) {
                    return cell;
                }
            }
        }
        return null;
    }

    private static Integer findCollumnIndexOfKey(XSSFSheet sh, String key, Row templateRow) {
        String beanKeyS = "${trans." + key + "}";
        // for (int rowNum = sh.getFirstRowNum(); rowNum < sh.getLastRowNum(); rowNum++) {
            // Row row = sh.getRow(rowNum);
            for (int cellNum = 0; cellNum < templateRow.getLastCellNum(); cellNum++) {
                Cell cell = templateRow.getCell(cellNum, Row.RETURN_BLANK_AS_NULL);
                if (cell != null && cell.getStringCellValue().equals(beanKeyS)) {
                    return cell.getColumnIndex();
                }
            }
        // }
        return -1;
    }
        private static Integer findCollumnIndexOfKey2(String key, Row templateRow) {
        String beanKeyS = "${trans." + key + "}";
        for (int cellNum = 0; cellNum < templateRow.getLastCellNum(); cellNum++) {
            Cell cell = templateRow.getCell(cellNum, Row.RETURN_BLANK_AS_NULL);
            if (cell == null)
                continue;
            cell.setCellType(1); //String
            if (cell.getStringCellValue().equals(beanKeyS)) {
                return cell.getColumnIndex();
            }
        }
        return -1;
    }


    private static Integer getCollumnIndexByKey(Map<String, Integer> map, String key) {
        return map.get(key);
    }

    public static void main(String[] args) {
        long startTime = System.nanoTime();
        String sqlStr = "select s_id id, s_state state, s_merchant_id merchant, s_info info, s_input_type type, n_amount amount from tb_invoice where rownum <= 100000";

        // create DB connect
        try (Connection conn = DriverManager.getConnection("******************************************", "msp",
                "msp")) {

            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sqlStr);
            // Extract data from result set
            List<String> headers = new ArrayList<>();
            headers.add("id");
            headers.add("state");
            headers.add("merchant");
            headers.add("info");
            headers.add("type");
            headers.add("amount");

            List<Map> datas = new ArrayList<>();
            while (rs.next()) {

                Map invoice = new HashMap<>();
                invoice.put("id", rs.getString("id"));
                invoice.put("state", rs.getString("state"));
                invoice.put("merchant", rs.getString("merchant"));
                invoice.put("info", rs.getString("info"));
                invoice.put("type", rs.getString("type"));
                invoice.put("amount", rs.getString("amount"));

                datas.add(invoice);
            }
            CreateHugeXLSFile file = new CreateHugeXLSFile("invoice_report.xlsx", "invoice report", "01/08/2021", "30/08/2021", headers, datas);
            file.write();
            // testXSSFWorkbook(rs);
            // jxlsWritingExcel(rs);
            // while (rs.next()) {
            // System.out.print("ID: " + rs.getInt(0));

            // }
        } catch (SQLException e) {
            System.err.format("SQL State: %s\n%s", e.getSQLState(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        long endTime = System.nanoTime();

        long duration = (endTime - startTime) / 1000000;
        System.out.println(duration);
    }

    /**
     * @return String return the fromDate
     */
    public String getFromDate() {
        return fromDate;
    }

    /**
     * @param fromDate the fromDate to set
     */
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * @return String return the toDate
     */
    public String getToDate() {
        return toDate;
    }

    /**
     * @param toDate the toDate to set
     */
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    /**
     * @return List<String> return the headers
     */
    public List<String> getHeaders() {
        return headers;
    }

    /**
     * @param headers the headers to set
     */
    public void setHeaders(List<String> headers) {
        this.headers = headers;
    }

    /**
     * @return List<String> return the datas
     */
    public List<Map> getDatas() {
        return datas;
    }

    /**
     * @param datas the datas to set
     */
    public void setDatas(List<Map> datas) {
        this.datas = datas;
    }

    /**
     * @return String return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * @return String return the fileName
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * @param fileName the fileName to set
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }


    public static void deleteExcelRow(XSSFSheet templateSheet, List<String> key,int startRow){
        Row templateRow = templateSheet.getRow(startRow);
        for(String column:key){
            Integer cellIndex = findCollumnIndexOfKey2(column,templateRow);
            deletecell(cellIndex,templateSheet);
        }
    }

        private static void deletecell(int columnToDelete,XSSFSheet sheet){
        for (int rId = 6; rId <= sheet.getLastRowNum(); rId++) {
            Row row = sheet.getRow(rId);
            for (int cID = columnToDelete; cID < row.getLastCellNum(); cID++) {
                Cell cOld = row.getCell(cID);
                if (cOld != null) {
                    row.removeCell(cOld);
                }
                Cell cNext = row.getCell(cID + 1);
                if (cNext != null) {
                    Cell cNew = row.createCell(cID, cNext.getCellType());
                    cloneCell(cNew, cNext);
                    sheet.setColumnWidth(cID, sheet.getColumnWidth(cID + 1));
                }
            }
        }
    }

    private static void cloneCell( Cell cNew, Cell cOld ){
        cNew.setCellComment( cOld.getCellComment() );
        cNew.setCellStyle( cOld.getCellStyle() );
    
        switch ( cNew.getCellType() ){
            case Cell.CELL_TYPE_BOOLEAN:{
                cNew.setCellValue( cOld.getBooleanCellValue() );
                break;
            }
            case Cell.CELL_TYPE_NUMERIC:{
                cNew.setCellValue( cOld.getNumericCellValue() );
                break;
            }
            case Cell.CELL_TYPE_STRING:{
                cNew.setCellValue( cOld.getStringCellValue() );
                break;
            }
            case Cell.CELL_TYPE_ERROR:{
                cNew.setCellValue( cOld.getErrorCellValue() );
                break;
            }
            case Cell.CELL_TYPE_FORMULA:{
                cNew.setCellFormula( cOld.getCellFormula() );
                break;
            }
        }
    
    }

}
