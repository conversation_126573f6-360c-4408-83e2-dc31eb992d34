package com.onepay.ma.service.queue.listener.excelBuilder.ext.domestic;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.DomesticReport;
import com.onepay.ma.service.models.DomesticReportConvert;
import com.onepay.ma.service.models.DomesticReportParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.DomesticReportService;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.*;

/**
 * Created by anhkh on 14-Jun-17.
 */
@Service("domesticReport")
public class DomesticReportBuildImpl implements ReportDataBuild<DomesticReportParameter> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<DomesticReportParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        DomesticReportParameter param = messageData.getRequestBody();
        try {
            List<DomesticReport> domesticReports = domesticReportService.download(readOnly, param);
            List<DomesticReportConvert> listFinalReports = convertReport(domesticReports);
            this.generateRs(listFinalReports, listMap);

        }catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "ERROR ON: ", ex);
            throw  IErrors.INTERNAL_SERVER_ERROR;
        }


        return listMap;
    }


    private void generateRs(List<DomesticReportConvert>  list, List<Map> listData ) throws Exception {
         int rowNumber = listData.size();

        for (DomesticReportConvert itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getTransaction_date());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchant_id());

            // bank Id
            item.put(TemplateUtils.BANK_COLUMN, itemReport.getAcquirer_name());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");

            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getTransaction_count());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, itemReport.getTransaction_total());

            // Refund Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN, itemReport.getRefund_count());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, itemReport.getRefund_total());

            // put into list
            listData.add(item);
        }
    }


    /**
     * convert report domestic
     * @param listReport
     * @return
     */
    private List<DomesticReportConvert> convertReport(List<DomesticReport> listReport){
        List<DomesticReportConvert> reportOut = new ArrayList();
        DomesticReportConvert temp = null;
        boolean add = false;
        for (int i = 0; i < listReport.size(); i++) {
            DomesticReport domesticReport = listReport.get(i);
            // ADD DATA TO TRANSACTION DATA
            if ("pay".equals(domesticReport.getCommand().toLowerCase())) {
                add = false;
                for (int j = 0; j < reportOut.size(); j++) {
                    temp = reportOut.get(j);

                    Date resultTranDate = domesticReport.getReport_date();
                    Date  transDate = temp.getTransaction_date();
                    //String transDate = df2.format(txnDate);

                    if (transDate != null && transDate.equals(resultTranDate) &&
                            temp.getAcquirer_name() != null && temp.getAcquirer_name().equals(domesticReport.getAcquirer_name()) &&
                            temp.getMerchant_id() != null && temp.getMerchant_id().equals(domesticReport.getMerchant_id())) {
                        temp.setTransaction_count(domesticReport.getCount());
                        temp.setTransaction_total(domesticReport.getTotal());
                        reportOut.remove(j);
                        reportOut.add(j, temp);
                        add = true;
                    }

                }

                if (!add) {
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(domesticReport.getCount());
                    temp.setTransaction_total(domesticReport.getTotal());
                    temp.setRefund_count(0);
                    temp.setRefund_total(0);
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());

                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
                }
            } else {
                // ADD DATA TO REFUND DATA
                add = false;

                for (int j = 0; j < reportOut.size(); j++) {
                    temp = reportOut.get(j);

                    Date resultTranDate = domesticReport.getReport_date();
                    Date  transDate = temp.getTransaction_date();
                    //String transDate = df2.format(txnDate);

                    if (transDate != null && transDate.equals(resultTranDate) &&
                            temp.getAcquirer_name() != null && temp.getAcquirer_name().equals(domesticReport.getAcquirer_name()) &&
                            temp.getMerchant_id() != null && temp.getMerchant_id().equals(domesticReport.getMerchant_id())) {
                        temp.setRefund_count(domesticReport.getCount());
                        temp.setRefund_total(domesticReport.getTotal());
                        reportOut.remove(j);
                        reportOut.add(j, temp);
                        add = true;
                    }

                }

                if (!add) {
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(0);
                    temp.setTransaction_total(0);
                    temp.setRefund_count(domesticReport.getCount());
                    temp.setRefund_total(domesticReport.getTotal());
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());
                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
                }
            }
        }
        return  reportOut;
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticReportBuildImpl.class.getName());
    @Autowired
    private DomesticReportService domesticReportService;
}
