package com.onepay.ma.service.queue.listener;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.models.pay_collect.TransactionSearchReq;
import com.onepay.ma.service.models.pay_collect.UserQueryPayCollectDto;
import com.onepay.ma.service.models.pay_out.BatchDetailQueryDto;
import com.onepay.ma.service.models.pay_out.FundsTransHisQueryDto;
import com.onepay.ma.service.models.pay_out.SummaryQueryDto;
import com.onepay.ma.service.queue.listener.csvBuilder.ReportCsvBuilder;
import com.onepay.ma.service.queue.listener.csvBuilder.ext.pay_out.BatchDetailBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.chartStatistics.*;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.international.InternationalRefundEmailTemplateBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.pay_out.FundsTranferHistoryBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.pay_out.SummaryFundsTranferBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect.ReportBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect.TransactionBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.paycollect.UserConfigBuildImpl;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.models.mpay.MocaMpayTransactionQuery;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.models.mpay.MpayTransactionQuery;
import com.onepay.ma.service.models.mpay.SamsungMpayTransactionQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReportQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationDetailQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.models.template.TemplateUtils2;
import com.onepay.ma.service.models.user.UserProfile;
import com.onepay.ma.service.queue.listener.excelBuilder.ReportBuilder;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportResultSetBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.billing.BillingTransactionBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.international.PaymentReconcileBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.international.PaymentReconcileDetailBuildImpl;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.report.GeneralReportBuildImpl;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.RoutePool;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.jms.Message;
import javax.jms.ObjectMessage;
import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
public abstract class BaseDownloadQueueInListener {

    public void onBaseMassage(Message message) {

        com.onepay.ma.service.queue.message.Message messageData = null;

        try {
            LOGGER.log(Level.INFO, "========================== BEGIN GENERATE FILE ==========================");
            LOGGER.log(Level.INFO, "========================== BEGIN GENERATE FILE1 ==========================");
            messageData = (com.onepay.ma.service.queue.message.Message) ((ObjectMessage) message).getObject();

            messageData.setDestinationQueue(messageData.getForwardQueue());
            LOGGER.log(Level.INFO, "=============== messageData.getPath() ==========" + messageData.getPath());

            this.fileNameHash = serverConfig.getExportLocation() + StringPool.FORWARD_SLASH + messageData.getRequestData().get(ParamsPool.FILE_HASH_NAME);
            this.locationFileName = serverConfig.getExportLocation() + StringPool.FORWARD_SLASH + messageData.getRequestData().get(ParamsPool.FILE_NAME);
            String ext = messageData.getRequestData().get(ParamsPool.FILE_EXT) == null ? "xls" : messageData.getRequestData().get(ParamsPool.FILE_EXT).toString();
            if (messageData.getPath().equals(RoutePool.DOWNLOAD_CDR_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<CdrParameterPost> parameterPostMessage = messageData;
                // cdrGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalTxnParameter> parameterPostMessage = messageData;
                internationalTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<DomesticTxnParameter> parameterPostMessage = messageData;
                domesticTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.SAMSUNG_DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<DomesticTxnParameter> parameterPostMessage = messageData;
                samsungDomesticTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_DOMESTIC_REFUND_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<DomesticRefundParameter> parameterPostMessage = messageData;
                domesticRefundGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_REFUND_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalRefundParameter> parameterPostMessage = messageData;
                internationalRefundGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_DOMESTIC_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<DomesticReportParameter> parameterPostMessage = messageData;
                domesticReportGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalReportParameterFile> parameterPostMessage = messageData;
                internationalReportGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_TRANSACTION_PROMOTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<PromotionTransactionParameter> parameterPostMessage = messageData;
                promotionTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayTransactionQuery> queryMessage = messageData;
                mpayTransactionGenerateReport(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.SAMSUNG_DOWNLOAD_MPAY_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<SamsungMpayTransactionQuery> queryMessage = messageData;
                samsungMpayTransactionGenerateReport(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.MOCA_DOWNLOAD_MPAY_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MocaMpayTransactionQuery> queryMessage = messageData;
                mocaMpayTransactionGenerateReport(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_PR_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayPromotionQuery> queryMessage = messageData;
                mpayPrTransactionGenerateReport(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_PR_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayPrReportQuery> queryMessage = messageData;
                mpayPrReportGenerateReport(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<FinancialTransactionQuery> parameterPostMessage = messageData;
                financialTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.SAMSUNG_DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<FinancialTransactionQuery> parameterPostMessage = messageData;
                samsungFinancialTransactionGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_PAYMENT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalAuthPaymentQuery> parameterPostMessage = messageData;
                internationalAuthPaymentGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayReportQuery> parameterPostMessage = messageData;
                mpayReportGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_REPORT_ROUTE_MOCA)) {
                com.onepay.ma.service.queue.message.Message<MpayReportQuery> parameterPostMessage = messageData;
                mocaMpayReportGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_REPORT_PAYMENT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayReportQuery> parameterPostMessage = messageData;
                mpayReportPaymentGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_MPAY_REFUND_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<MpayRefundParameter> parameterPostMessage = messageData;
                mpayRefundGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTICS_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> parameterPostMessage = messageData;
                statisticsGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTICS_DETAIL_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> parameterPostMessage = messageData;
                statisticsDetailGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTICS_DETAIL_DT_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> parameterPostMessage = messageData;
                statisticsDetailDtGenerateFile(parameterPostMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_RECONCILE_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalReportParameterFile> queryMessage = messageData;
                reconcileGenerateFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_RECONCILE_PAYMENT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ReconciliationQuery> queryMessage = messageData;
                reconcilePaymentGenerateFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_RECONCILE_PAYMENT_DETAIL_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ReconciliationDetailQuery> queryMessage = messageData;
                reconcilePaymentDetailGenerateFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_REPORT_GENERAL_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<GeneralReportQuery> queryMessage = messageData;
                generalReportGenerateFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_TRANSACTION_BILLING_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<OnebillTransactionQuery> queryMessage = messageData;
                billingTransactionGenerateFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_DOMESTIC_ROUTE)
                    && messageData.getMessagePriority() == 2) {
                com.onepay.ma.service.queue.message.Message<List<ChartStatisticQuery>> queryMessage = messageData;
                chartDomesticFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_DOMESTIC_ROUTE)
                    && messageData.getMessagePriority() == 1) {
                com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> queryMessage = messageData;
                chartDomesticOnlyFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_INTERNATIONAL_ROUTE)
                    && messageData.getMessagePriority() == 2) {
                com.onepay.ma.service.queue.message.Message<List<ChartStatisticQuery>> queryMessage = messageData;
                chartInterFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_INTERNATIONAL_ROUTE)
                    && messageData.getMessagePriority() == 1) {
                com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> queryMessage = messageData;
                chartInterOnlyFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> queryMessage = messageData;
                chartTotalFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_INTERNATIONAL_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> queryMessage = messageData;
                chartTotalInterFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_DOMESTIC_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> queryMessage = messageData;
                chartTotalDmsFile(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_INTERNATIONAL_EMAIL_TEMPLATE_REFUND_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<InternationalRefundEmailTemplateParameter> queryMessage = messageData;
                internationalRefundEmailTemplate(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.PAY_COLLECT_DOWNLOAD_TRANSACTION_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<TransactionSearchReq> queryMessage = messageData;
                payCollectTransactionTemplate(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_PAYCOLLECT_USER_ROUTER)) {
                com.onepay.ma.service.queue.message.Message<UserQueryPayCollectDto> queryMessage = messageData;
                paycollectUserConfigTemplate(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.PAY_COLLECT_DOWNLOAD_REPORT_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<ReportSearchReq> queryMessage = messageData;
                payCollectReportTemplate(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_SUMMARY_FUNDS_TRANS_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<SummaryQueryDto> queryMessage = messageData;
                summaryFundsTranfer(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_FUNDS_TRANS_HIS_ROUTE)) {
                com.onepay.ma.service.queue.message.Message<FundsTransHisQueryDto> queryMessage = messageData;
                fundsTranferHistory(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.DOWNLOAD_FUNDS_TRANS_BATCH_DETAIL)) {
                com.onepay.ma.service.queue.message.Message<BatchDetailQueryDto> queryMessage = messageData;
                batchDetail(queryMessage);
            } else if (messageData.getPath().equals(RoutePool.PAYMENT_LINK_LIST_DOWNLOAD)) {
                com.onepay.ma.service.queue.message.Message<PaymentLinkDto> parameterPostMessage = messageData;
                quickLinkPaymentLinkGenerateFile(parameterPostMessage);
            }
            writeFile(messageData, ext);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "DOWNLOAD FILE ERROR : ", ex);
            ex.printStackTrace();

            if (connectionOnline != null) {
                try {
                    connectionOnline.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Connection File", e);
                }

            }
            if (connectionReadOnly != null) {
                try {
                    connectionReadOnly.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }
            if (connectionPromotion != null) {
                try {
                    connectionPromotion.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }
            if (connectionBackup != null) {
                try {
                    connectionBackup.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }
            if (connectionOnerecon != null) {
                try {
                    connectionOnerecon.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }
            if (connectionPaycollect != null) {
                try {
                    connectionPaycollect.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }

            if (connectionMerchantPortal != null) {
                try {
                    connectionMerchantPortal.rollback();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Rollback Close Connection File", e);
                }

            }

            File file = new File(this.fileNameHash);
            if (file.exists()) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e1) {
                    LOGGER.log(Level.SEVERE, "Error Delete File Error", ex);
                }
            }
            messageData.getRequestData().put(ParamsPool.FILE_SIZE, 0);
            messageData.setResultCode(500);
            messageData.setResultString(" File Generated Failed");

        } finally {
            if (connectionOnline != null) {
                try {
                    connectionOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }
            if (connectionReadOnly != null) {
                try {
                    connectionReadOnly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }
            if (connectionPromotion != null) {
                try {
                    connectionPromotion.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }
            if (connectionBackup != null) {
                try {
                    connectionBackup.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }

            if (connectionMerchantPortal != null) {
                try {
                    connectionMerchantPortal.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (connectionOnerecon != null) {
                try {
                    connectionOnerecon.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }

            if (connectionPaycollect != null) {
                try {
                    connectionPaycollect.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }

            }

            // next
            QueueProducer.sendMessage(messageData);
        }


    }


    private void internationalTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<InternationalTxnParameter> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();

        List<Map> listMap = internationalTransactionReportBuild.generate(connectionOnline, connectionReadOnly, messageData);

        LOGGER.log(Level.INFO, "[GENERATE INTERNATIONAL TRANSACTION EXCEL START]: ");
        Long start = System.currentTimeMillis();
        InternationalTxnParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = parameterPost.getFromDate();
        String toDate = parameterPost.getToDate();
        String version = parameterPost.getVersion();

        if (version != null && version.equals("v2")) {
            fromDate = convertStrDate(fromDate, 0);
            toDate = convertStrDate(toDate, 0);
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            ReportBuilder.newInstance()
                    .template(TemplateUtils.INTERNATIONAL_TRANSACTION_TEMPLATE_NEW_SRC)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .build()
                    .exportExcel();
        } else {
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            ReportBuilder.newInstance()
                    .template(TemplateUtils.INTERNATIONAL_TRANSACTION_TEMPLATE_SRC)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .build()
                    .exportExcel();
        }

        LOGGER.log(Level.INFO, "[GENERATE INTERNATIONAL TRANSACTION EXCEL END]: " + (System.currentTimeMillis() - start));

    }

    private void mpayPrReportGenerateReport(com.onepay.ma.service.queue.message.Message<MpayPrReportQuery> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = mpayPrReportDataBuild.generate(connectionOnline, connectionReadOnly, messageData);

        MpayPrReportQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFrom_date());
        detailMap.put("TO_DATE", parameterPost.getTo_date());


        ReportBuilder.newInstance()
                .template(TemplateUtils.MPA_PR_REPORT_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void mpayPrTransactionGenerateReport(com.onepay.ma.service.queue.message.Message<MpayPromotionQuery> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = mpayPrTransactionDataBuild.generate(connectionOnline, connectionReadOnly, messageData);

        MpayPromotionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFrom_date());
        detailMap.put("TO_DATE", parameterPost.getTo_date());


        ReportBuilder.newInstance()
                .template(TemplateUtils.MPA_PR_TRANSACTION_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void mpayTransactionGenerateReport(com.onepay.ma.service.queue.message.Message<MpayTransactionQuery> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = mpayReportDataBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        MpayTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        if (messageData.getRequestBody().getVersion().equals("ma")) {
            for (Map item : listMap) {
                if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
                    item.put("S_APP_NAME", "Mobile Banking / E-Wallet");
            }
        }

        String template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_PM;
        if (messageData.getRequestBody().getVersion() != null) {
            if (messageData.getRequestBody().getVersion().equals("ma")) {
                template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_MA;
            } else if (messageData.getRequestBody().getVersion().equals("ss")) {
                template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_SS;
            }
        }
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
            if (messageData.getRequestBody().getVersion() != null) {
                if (messageData.getRequestBody().getVersion().equals("ma")) {
                    template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_MA_VI;
                } else if (messageData.getRequestBody().getVersion().equals("ss")) {
                    template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_SS_VI;
                } else {
                    template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_PM_VI;
                }
            }
        }
        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();

    }

    private void samsungMpayTransactionGenerateReport(com.onepay.ma.service.queue.message.Message<SamsungMpayTransactionQuery> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = samsungMpayReportDataBuild.generate(connectionOnline, connectionReadOnly, messageData);

        SamsungMpayTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        if (messageData.getRequestBody().getVersion().equals("ma")) {
            for (Map item : listMap) {
                if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
                    item.put("S_APP_NAME", "QR");
            }
        }

        ReportBuilder.newInstance()
                // .template(messageData.getRequestBody().getVersion() != null &&
                // messageData.getRequestBody().getVersion().equals("ma") ?
                // TemplateUtils2.SAMSUNG_MPAY_TRANSACTION_TEMPLATE_SRC_MA
                // : messageData.getRequestBody().getVersion() != null &&
                // messageData.getRequestBody().getVersion().equals("ss") ?
                // TemplateUtils2.SAMSUNG_MPAY_TRANSACTION_TEMPLATE_SRC_SS
                // : TemplateUtils2.SAMSUNG_MPAY_TRANSACTION_TEMPLATE_SRC_PM)
                .template(TemplateUtils2.SAMSUNG_MPAY_TRANSACTION_TEMPLATE_SRC_MA)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();

    }

    private void mocaMpayTransactionGenerateReport(com.onepay.ma.service.queue.message.Message<MocaMpayTransactionQuery> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = mocaMpayReportDataBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        MocaMpayTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        String template = TemplateUtils2.MOCA_MPAY_TRANSACTION_TEMPLATE_SRC_MA;
        

        // if (messageData.getRequestBody().getVersion().equals("ma")) {
        //     for (Map item : listMap) {
        //         if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
        //             item.put("S_APP_NAME", "Mobile Banking / E-Wallet");
        //     }
        // }

        // String template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_PM;
        // if (messageData.getRequestBody().getVersion() != null) {
        //     if (messageData.getRequestBody().getVersion().equals("ma")) {
        //         template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_MA;
        //     } else if (messageData.getRequestBody().getVersion().equals("ss")) {
        //         template = TemplateUtils2.MPAY_TRANSACTION_TEMPLATE_SRC_SS;
        //     } else if (messageData.getRequestBody().getVersion().equals("moca")) {
        //         template = TemplateUtils2.MOCA_MPAY_TRANSACTION_TEMPLATE_SRC_MA;
        //     }
        // }
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
                    template = TemplateUtils2.MOCA_MPAY_TRANSACTION_TEMPLATE_SRC_MA_VI;
        }
        
        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();

    }

    private void internationalRefundGenerateFile(com.onepay.ma.service.queue.message.Message<InternationalRefundParameter> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = internationalRefundReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        InternationalRefundParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());


        ReportBuilder.newInstance()
                .template(TemplateUtils.INTERNATIONAL_REFUND_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void domesticTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<DomesticTxnParameter> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = domesticTransactionReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        DomesticTxnParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());
        String fileTemplate = TemplateUtils2.DOMESTIC_TRANSACTION_TEMPLATE_SRC;
        if (parameterPost.getTarget() != null && parameterPost.getTarget().contains("samsung-user")) {
            fileTemplate = TemplateUtils2.DOMESTIC_TRANSACTION_SAMSUNG_TEMPLATE_SRC;
        }

        if ((parameterPost.getTarget() == null || !parameterPost.getTarget().contains("samsung-user")) && parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils2.DOMESTIC_TRANSACTION_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void samsungDomesticTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<DomesticTxnParameter> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = samsungDomesticTransactionReportBuild.generate(connectionOnline, connectionReadOnly, messageData);

        DomesticTxnParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());
        String fileTemplate = TemplateUtils2.DOMESTIC_TRANSACTION_SAMSUNG_TEMPLATE_SRC;
        if (parameterPost.getTarget() != null && parameterPost.getTarget().contains("samsung-user")) {
            fileTemplate = TemplateUtils2.DOMESTIC_TRANSACTION_SAMSUNG_TEMPLATE_SRC;
        }


        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void promotionTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<PromotionTransactionParameter> messageData) throws Exception {

        connectionPromotion = this.promotionDataSource.getConnection();
        List<Map> listMap = promotionTransactionReportBuild.generate(connectionPromotion, messageData);

        PromotionTransactionParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        String fileTemplate = TemplateUtils2.PR_TRANSACTION_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils2.PR_TRANSACTION_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void domesticRefundGenerateFile(com.onepay.ma.service.queue.message.Message<DomesticRefundParameter> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = domesticRefundReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        DomesticRefundParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();

        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());
        String template = TemplateUtils2.DOMESTIC_REFUND_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            template = TemplateUtils2.DOMESTIC_REFUND_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void domesticReportGenerateFile(com.onepay.ma.service.queue.message.Message<DomesticReportParameter> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();

        List<Map> listMap = domesticReportReportBuild.generate(connectionReadOnly, messageData);

        DomesticReportParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());
        String fileTemplate = TemplateUtils.DOMESTIC_REPORT_TEMPLATE_SRC;
        if (parameterPost.getVersion().equalsIgnoreCase("v2")) {
            fileTemplate = TemplateUtils.DOMESTIC_REPORT_MERCHANT_TEMPLATE_SRC;
        }
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils.DOMESTIC_REPORT_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }
        if (parameterPost.getVersion().equalsIgnoreCase("v2") && parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils.DOMESTIC_REPORT_MERCHANT_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

        // CreateHugeXLSFile xlsFile = new CreateHugeXLSFile(this.fileName(), parameterPost.getFromDate(),
        // parameterPost.getToDate());
    }

    private void internationalReportGenerateFile(com.onepay.ma.service.queue.message.Message<InternationalReportParameterFile> messageData) throws Exception {
        connectionReadOnly = this.readOnlyDataSource.getConnection();

        List<Map> listMap = internationalReportReportBuild.generate(connectionReadOnly, messageData);

        InternationalReportParameterFile parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        String templateName = TemplateUtils.INTERNATIONAL_REPORT_DETAIL_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            templateName = TemplateUtils.INTERNATIONAL_REPORT_DETAIL_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }
        // String templateName = TemplateUtils.INTERNATIONAL_REPORT_MERCHANT_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "en".equalsIgnoreCase(parameterPost.getLang()) && "v2".equals(parameterPost.getVersion())) {
            templateName = TemplateUtils.INTERNATIONAL_REPORT_MERCHANT_TEMPLATE_SRC;
        }
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang()) && "v2".equals(parameterPost.getVersion())) {
            templateName = TemplateUtils.INTERNATIONAL_REPORT_MERCHANT_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }
        ReportBuilder.newInstance()
                .template(templateName)
                .exportFileName(this.fileName())
                .setBeanValue("currencyList", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void financialTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<FinancialTransactionQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = financialTransactionReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        FinancialTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        detailMap.put("HEADER", "FROM " + fromDate + " TO " + toDate);
        detailMap.put("HEADER_VI", "Từ ngày " + fromDate + " đến " + toDate);

        String fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_TEMPLATE_SRC;
        if (parameterPost.getTarget() != null && parameterPost.getTarget().contains("samsung-user")) {
            fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_SAMSUNG_TEMPLATE_SRC;
        } else if (parameterPost.getTarget() != null && parameterPost.getTarget().contains("adayroi-user")) {
            fileTemplate = TemplateUtils.FINANCIAL_TRANSACTION_ADAYROI_TEMPLATE_SRC;
        } else if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_TEMPLATE_SRC_VI;
        } else if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang()) && parameterPost.getTarget() != null && parameterPost.getTarget().contains("samsung-user")) {
            fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_SAMSUNG_TEMPLATE_SRC_VI;
        } else if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang()) && parameterPost.getTarget() != null && parameterPost.getTarget().contains("adayroi-user")) {
            fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_ADAYROI_TEMPLATE_SRC_VI;
        }

        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .setHiddenColumn(parameterPost.getShow_risk_assessment().equals("false") ? "trans.S_RISK_ASSESSMENT" : null)
                .build()
                .exportExcel2();

    }

    private void samsungFinancialTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<FinancialTransactionQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = ssFinancialTransactionReportBuild.generate(connectionOnline, connectionReadOnly, messageData);

        FinancialTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        detailMap.put("HEADER", "FROM " + fromDate + " TO " + toDate);
        String fileTemplate = TemplateUtils2.FINANCIAL_TRANSACTION_SAMSUNG_TEMPLATE_SRC;
        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .setHiddenColumn(parameterPost.getShow_risk_assessment().equals("false") ? "trans.S_RISK_ASSESSMENT" : null)
                .build()
                .exportExcel2();

    }

    private void mpayReportGenerateFile(com.onepay.ma.service.queue.message.Message<MpayReportQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = mpayReportBuild.generate(connectionOnline, connectionReadOnly, messageData);

        MpayReportQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());

        if (Objects.equals(parameterPost.getVersion(), "ma")) {
            for (Map item : listMap) {
                if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
                    item.put("S_APP_NAME", "Mobile Banking / E-Wallet");
            }
        }
        String templateName = parameterPost.getVersion() != null && parameterPost.getVersion().equals("ma")
                ? TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_MA
                : TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_SS;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            templateName = parameterPost.getVersion() != null && parameterPost.getVersion().equals("ma")
                    ? TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_MA_VI
                    : TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_SS;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(templateName)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void mocaMpayReportGenerateFile(com.onepay.ma.service.queue.message.Message<MpayReportQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = mocaMpayReportBuild.generate(connectionOnline, connectionReadOnly, messageData);

        MpayReportQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());

        if (Objects.equals(parameterPost.getVersion(), "ma")) {
            for (Map item : listMap) {
                if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
                    item.put("S_APP_NAME", "Mobile Banking / E-Wallet");
            }
        }
        String templateName =  TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_MOCA;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            templateName = TemplateUtils.MPAY_REPORT_TEMPLATE_SRC_MA_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(templateName)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void mpayReportPaymentGenerateFile(com.onepay.ma.service.queue.message.Message<MpayReportQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = mpayReportPaymentBuild.generate(connectionOnline, connectionReadOnly, messageData);

        MpayReportQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());


        ReportBuilder.newInstance()
                .template(TemplateUtils2.MPAY_REPORT_PAYMENT_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();

    }

    private void mpayRefundGenerateFile(com.onepay.ma.service.queue.message.Message<MpayRefundParameter> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        List<Map> listMap = mpayRefundReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        MpayRefundParameter parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();

        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        if (messageData.getRequestBody().getVersion().equals("ma")) {
            for (Map item : listMap) {
                if (Objects.equals(item.get("S_CHANNEL"), "MSB") && !Objects.equals(item.get("S_APP_NAME"), "MSB"))
                    item.put("S_APP_NAME", "Mobile Banking / E-Wallet");
            }
        }

        String template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_PM;
        if (messageData.getRequestBody().getVersion() != null) {
            if (messageData.getRequestBody().getVersion().equals("ma")) {
                template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_MA;
            } else if (messageData.getRequestBody().getVersion().equals("ss")) {
                template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_SS;
            }
        }
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
            if (messageData.getRequestBody().getVersion() != null) {
                if (messageData.getRequestBody().getVersion().equals("ma")) {
                    template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_MA_VI;
                } else if (messageData.getRequestBody().getVersion().equals("ss")) {
                    template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_SS_VI;
                } else {
                    template = TemplateUtils2.MPAY_REFUND_TEMPLATE_SRC_PM_VI;
                }
            }
        }
        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }
    private UserService userService;

    private void internationalAuthPaymentGenerateFile(com.onepay.ma.service.queue.message.Message<InternationalAuthPaymentQuery> messageData) throws Exception {
        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = internationalAuthPaymentBuildImpl.generate(connectionOnline, connectionReadOnly, messageData);

        InternationalAuthPaymentQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        detailMap.put("HEADER", "FROM " + fromDate + " TO " + toDate);

        String fileTemplate = TemplateUtils2.INTERNATIONAL_AUTHPAYMENT_TEMPLATE_SRC;

        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils2.INTERNATIONAL_AUTHPAYMENT_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + fromDate + " đến " + toDate);
        }

        // check if is applepay/googlepay hidden column source + trans network
        List<String> listHiddenColumn = new ArrayList<>();
        if(Objects.equals(messageData.getRequestData().get(ParamsPool.DIGITAL_WALLET_USER), false)) {
            listHiddenColumn.add("S_SOURCE");
        }

        ReportBuilder.newInstance().setListHiddenColumn(listHiddenColumn)
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();

    }

    private void reconcilePaymentGenerateFile(com.onepay.ma.service.queue.message.Message<ReconciliationQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = paymentReconcileBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        ReconciliationQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);


        ReportBuilder.newInstance()
                .template(TemplateUtils.PAYMENT_RECONCILE_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("currencyList", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void billingTransactionGenerateFile(com.onepay.ma.service.queue.message.Message<OnebillTransactionQuery> messageData) throws Exception {
        connectionMerchantPortal = this.merchantPortalDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = billingTransactionBuild.generate(connectionMerchantPortal, connectionReadOnly, messageData);

        OnebillTransactionQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);


        ReportBuilder.newInstance()
                .template(TemplateUtils.BILLING_TRANSACTION_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void generalReportGenerateFile(com.onepay.ma.service.queue.message.Message<GeneralReportQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionBackup = this.backupDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = generalReportBuild.generate(connectionOnline, connectionReadOnly, connectionBackup, messageData);

        GeneralReportQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());

        String templateName = TemplateUtils2.GENERAL_TRANSACTION_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            templateName = TemplateUtils2.GENERAL_TRANSACTION_TEMPLATE_SRC_VI;
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }
        ReportBuilder.newInstance()
                .template(templateName)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void quickLinkPaymentLinkGenerateFile(com.onepay.ma.service.queue.message.Message<PaymentLinkDto> messageData) throws Exception {

        connectionOnline = this.onlineDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        LOGGER.info("===start in generate download quickLinkPaymentLinkGenerateFile: ");
        List<PaymentLinkDto> listMap = paymentLinkBuild.generate(connectionOnline, connectionReadOnly, messageData);
        LOGGER.info("===end generate download quickLinkPaymentLinkGenerateFile: ");
        PaymentLinkDto parameterPost = messageData.getRequestBody();
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("FROM_DATE", parameterPost.getFromDate());
        detailMap.put("TO_DATE", parameterPost.getToDate());
        detailMap.put("HEADER", "FROM " + parameterPost.getFromDate() + " TO " + parameterPost.getToDate());
        
        String fileTemplate = TemplateUtils2.PAYMENT_LINK_TEMPLATE_SRC;
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTemplate = TemplateUtils2.PAYMENT_LINK_TEMPLATE_SRC_VI;
            detailMap.put("HEADER", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
        }

        ReportBuilder.newInstance()
                .template(fileTemplate)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }


    private void reconcilePaymentDetailGenerateFile(com.onepay.ma.service.queue.message.Message<ReconciliationDetailQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = paymentReconcileDetailBuild.generate(connectionReadOnly, messageData);

        ReconciliationDetailQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String baseDate = convertStrDate(parameterPost.getBase_date(), 0);
        String fromDate = convertStrDate(parameterPost.getFrom_date(), 0);
        String toDate = convertStrDate(parameterPost.getTo_date(), -1);
        detailMap.put("BASE_DATE", baseDate);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);


        ReportBuilder.newInstance()
                .template(TemplateUtils.PAYMENT_RECONCILE_DETAIL_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void statisticsGenerateFile(com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> messageData) throws Exception {
        connectionOnerecon = this.onereconDataSource.getConnection();
        List<Object> listMap = statisticsReportBuild.generate(connectionOnerecon, messageData);

        StatisticsReportParameter parameterPost = messageData.getRequestBody();
        Map parameters = new HashMap();
        parameters.put("fromDate", parameterPost.getFromDate());
        parameters.put("toDate", parameterPost.getToDate());
        parameters.put("bankId", parameterPost.getAcquirerId());
        Map item = (Map) listMap.get(listMap.size() - 1);
        parameters.put("totalReceiable", item.get("totalReceiable"));
        parameters.put("totalPayable", item.get("totalPayable"));
        parameters.put("totalDiff", item.get("totalDiff"));
        listMap.remove(item);
        String exportType = "XLSX";
        String tempalteName = TemplateUtils.STATISTICS_XLSX_TEMPLATE_SRC;
        if (parameterPost.getExportType() != null && "PDF".equals(parameterPost.getExportType())) {
            exportType = "PDF";
            tempalteName = TemplateUtils.STATISTICS_PDF_TEMPLATE_SRC;
        }

        ReportBuilder.newInstance()
                .template(tempalteName)
                .exportFileName(this.fileName())
                .setParameters(parameters)
                .setListData(listMap)
                .build()
                .exportByJasperReport(exportType);

    }

    private void statisticsDetailGenerateFile(com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> messageData) throws Exception {
        connectionOnerecon = this.onereconDataSource.getConnection();
        List<Object> listMap = statisticsDetailReportBuild.generate(connectionOnerecon, messageData);

        StatisticsReportParameter parameterPost = messageData.getRequestBody();
        Map parameters = new HashMap();
        parameters.put("p_fromDate", parameterPost.getFromDate());
        parameters.put("p_toDate", parameterPost.getToDate());
        parameters.put("p_merchantName", parameterPost.getMerchantName());
        parameters.put("p_merchantId", parameterPost.getMerchantId());
        parameters.put("p_transType", parameterPost.getTransType());
        parameters.put("p_bankId", parameterPost.getAcquirerId());
        String exportType = "XLSX";
        String tempalteName = TemplateUtils.STATISTICS_DETAIL_XLSX_TEMPLATE_SRC;
        if (parameterPost.getExportType() != null && "PDF".equals(parameterPost.getExportType())) {
            exportType = "PDF";
            tempalteName = TemplateUtils.STATISTICS_DETAIL_PDF_TEMPLATE_SRC;
        }

        ReportBuilder.newInstance()
                .template(tempalteName)
                .exportFileName(this.fileName())
                .setParameters(parameters)
                .setListData(listMap)
                .build()
                .exportByJasperReport(exportType);
    }

    private void statisticsDetailDtGenerateFile(com.onepay.ma.service.queue.message.Message<StatisticsReportParameter> messageData) throws Exception {
        connectionOnerecon = this.onereconDataSource.getConnection();
        List<Object> listMap = statisticsDetailDtReportBuild.generate(connectionOnerecon, messageData);

        StatisticsReportParameter parameterPost = messageData.getRequestBody();
        Map parameters = new HashMap();
        parameters.put("p_acqType", parameterPost.getAcqType());
        String exportType = "XLSX";
        String tempalteName = TemplateUtils.STATISTICS_DETAIL_DT_XLSX_TEMPLATE_SRC;
        if (parameterPost.getExportType() != null && "PDF".equals(parameterPost.getExportType())) {
            exportType = "PDF";
            tempalteName = TemplateUtils.STATISTICS_DETAIL_DT_PDF_TEMPLATE_SRC;
        }

        ReportBuilder.newInstance()
                .template(tempalteName)
                .exportFileName(this.fileName())
                .setParameters(parameters)
                .setListData(listMap)
                .build()
                .exportByJasperReport(exportType);
    }


    private void reconcileGenerateFile(com.onepay.ma.service.queue.message.Message<InternationalReportParameterFile> messageData) throws Exception {
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        ResultSet resultSet = reconcileReportBuild.getResultSet(connectionReadOnly, messageData);

        InternationalReportParameterFile parameterPost = messageData.getRequestBody();

        ArrayList<String> listHeader = new ArrayList<String>();
        listHeader.add("S_MERCHANTID");
        listHeader.add("S_TRANSACTIONID");
        listHeader.add("S_ORDERREFERENCE");
        listHeader.add("S_MERCHANTTRANSACTIONREFEREN");
        listHeader.add("D_DATE");
        listHeader.add("D_SETTLEMENT");
        listHeader.add("S_CARDNO");
        listHeader.add("S_CARDTYPE");
        listHeader.add("S_TRANSACTIONTYPE");
        listHeader.add("S_CURRENCY");
        listHeader.add("N_AMOUNT");
        listHeader.add("S_AUTHORISATIONCODE");
        listHeader.add("S_RESPONSECODE");
        listHeader.add("S_STATE");

        Map<String, String> mapDate = new HashMap<>();
        mapDate.put("D_DATE", "dd/MM/yyyy HH:mm:ss");
        mapDate.put("D_SETTLEMENT", "dd/MM/yyyy");
        Map parameters = new HashMap();
        String fileTitle = "Reconciliation";
        String fileDate = "FROM DATE " + parameterPost.getFromDate() + " TO DATE " + parameterPost.getToDate();
        String fileHeader = "No,Merchant ID,Transaction ID,Order Reference,Merchant Transaction Reference,Payment Date,Settlement Date,Card No,Card Type,Trans Type,Currency,Amount,Authorization Code,Response Code,State,";
        if (parameterPost.getLang() != null && !parameterPost.getLang().isEmpty() && "vi".equalsIgnoreCase(parameterPost.getLang())) {
            fileTitle = "Đối soát";
            fileDate = "TỪ NGÀY " + parameterPost.getFromDate() + " ĐẾN NGÀY " + parameterPost.getToDate();
            fileHeader = "No,Merchant ID,Transaction ID,Order Reference,Merchant Transaction Reference,Payment Date,Settlement Date,Card No,Card Type,Trans Type,Currency,Amount,Authorization Code,Response Code,State,";
        }
        ReportBuilder.newInstance()
                .exportFileName(this.fileName())
                .setParameters(parameters)
                .build()
                .exportCsv(fileTitle, fileDate, fileHeader, listHeader, resultSet, mapDate);
    }

    private void chartDomesticFile(com.onepay.ma.service.queue.message.Message<List<ChartStatisticQuery>> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        this.fileNames = new ArrayList<>();
        for (ChartStatisticQuery query : messageData.getRequestBody()) {
            com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> mes = new com.onepay.ma.service.queue.message.Message<>(query);
            List<Map> listMap = chartDomesticBuild.generate(connectionOnline, connectionReadOnly, mes);
            String fileName = "domestic_chart" + StringPool.UNDERLINE + query.getFileName();
            this.fileNames.add(fileName);
            Map detailMap = new HashMap();
            String fromDate = convertStrDate(query.getFromDate(), 0);
            String toDate = convertStrDate(query.getToDate(), -1);
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            ReportBuilder.newInstance()
                    .template(TemplateUtils.CHART_DOMESTIC_TEMPLATE_SRC)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .fileName(fileName)
                    .exportLocation(serverConfig.getExportLocation())
                    .build()
                    .exportMultiExcel();
        }
    }

    private void chartDomesticOnlyFile(com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = chartDomesticBuild.generate(connectionOnline, connectionReadOnly, messageData);
        ChartStatisticQuery query = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(query.getFromDate(), 0);
        String toDate = convertStrDate(query.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        ReportBuilder.newInstance()
                .template(TemplateUtils.CHART_DOMESTIC_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void chartInterFile(com.onepay.ma.service.queue.message.Message<List<ChartStatisticQuery>> messageData) throws Exception {
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        this.fileNames = new ArrayList<>();
        for (ChartStatisticQuery query : messageData.getRequestBody()) {
            com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> mes = new com.onepay.ma.service.queue.message.Message<>(query);
            List<Map> listMap = chartInternationalBuild.generate(connectionOnline, connectionReadOnly, mes);
            String fileName = "international_chart" + StringPool.UNDERLINE + query.getFileName();
            this.fileNames.add(fileName);
            Map detailMap = new HashMap();
            String fromDate = convertStrDate(query.getFromDate(), 0);
            String toDate = convertStrDate(query.getToDate(), -1);
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            ReportBuilder.newInstance()
                    .template(TemplateUtils.CHART_INTERNATIONAL_TEMPLATE_SRC)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .fileName(fileName)
                    .exportLocation(serverConfig.getExportLocation())
                    .build()
                    .exportMultiExcel();
        }
    }

    private void chartInterOnlyFile(com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> messageData) throws Exception {
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = chartInternationalBuild.generate(connectionOnline, connectionReadOnly, messageData);
        ChartStatisticQuery query = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(query.getFromDate(), 0);
        String toDate = convertStrDate(query.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        ReportBuilder.newInstance()
                .template(TemplateUtils.CHART_INTERNATIONAL_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void chartTotalFile(com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = chartTotalBuild.generate(connectionOnline, connectionReadOnly, messageData);
        ChartStatisticQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        ReportBuilder.newInstance()
                .template(TemplateUtils.CHART_TOTAL_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void chartTotalDmsFile(com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = chartTotalDmsBuild.generate(connectionOnline, connectionReadOnly, messageData);
        ChartStatisticQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        ReportBuilder.newInstance()
                .template(TemplateUtils.CHART_TOTAL_DOMESTIC_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void chartTotalInterFile(com.onepay.ma.service.queue.message.Message<ChartStatisticQuery> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = chartTotalInterBuild.generate(connectionOnline, connectionReadOnly, messageData);
        ChartStatisticQuery parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        LOGGER.info("chartTotalInterFile: " + this.fileName());
        ReportBuilder.newInstance()
                .template(TemplateUtils.CHART_TOTAL_INTERNATIONAL_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();

    }

    private void internationalRefundEmailTemplate(com.onepay.ma.service.queue.message.Message<InternationalRefundEmailTemplateParameter> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = internationalRefundEmailTemplateBuild.generate(connectionOnline, connectionReadOnly, messageData);
        // InternationalRefundEmailTemplateBuildImpl parameterPost = messageData.getRequestBody();
        Map detailMap = listMap.get(0);
        ReportBuilder.newInstance()
                .template(TemplateUtils.INTERNATIONAL_REFUND_EMAIL_TEMPLATE_SRC)
                .exportFileName(this.fileName())
                // .setBeanValue("listMap", listMap)
                .setBeanValue("detailMap", detailMap)
                .build()
                .exportExcel();
    }

    private void paycollectUserConfigTemplate(com.onepay.ma.service.queue.message.Message<UserQueryPayCollectDto> messageData) throws Exception {
        connectionPaycollect = this.payCollectDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        List<Map> listMap = userConfigBuild.generate(connectionPaycollect, connectionReadOnly, messageData);
        Map detailMap;
        if (null != listMap && !listMap.isEmpty()) {
            detailMap = listMap.get(0);
        } else  {
            detailMap = new HashMap();
        }
            
        UserQueryPayCollectDto parameterPost = messageData.getRequestBody();
        String template = TemplateUtils.PAYCOLLECT_USER_CONFIG_TEMPLATE_SRC;

        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang())) {
            // detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
            template = TemplateUtils.PAYCOLLECT_USER_CONFIG_TEMPLATE_SRC_VI;
        }
        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                
                .build()
                .exportExcel2();
    }

    private void payCollectTransactionTemplate(com.onepay.ma.service.queue.message.Message<TransactionSearchReq> messageData) throws Exception {
        connectionPaycollect = this.payCollectDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        TransactionSearchReq parameterPost = messageData.getRequestBody();
        List<Map> listMap = transactionBuildImpl.generate(connectionPaycollect, connectionReadOnly, messageData);

        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        detailMap.put("HEADER", "From " + parameterPost.getFromDate() + " to " + parameterPost.getToDate());
        String template = TemplateUtils.PAY_COLLECT_REPORT_TRANSACTION_TEMPLATE_SRC;

        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang())) {
            detailMap.put("HEADER_VI", "Từ ngày " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
            template = TemplateUtils.PAY_COLLECT_REPORT_TRANSACTION_TEMPLATE_SRC_VI;
        }

        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel2();
    }

    private void payCollectReportTemplate(com.onepay.ma.service.queue.message.Message<ReportSearchReq> messageData) throws Exception {
        connectionPaycollect = this.payCollectDataSource.getConnection();
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        ReportSearchReq parameterPost = messageData.getRequestBody();
        List<Map> listMap = reportBuildImpl.generate(connectionPaycollect, connectionReadOnly, messageData);

        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        if (!listMap.isEmpty()) {
            Map mapCount = listMap.get(listMap.size() - 2);
            Map mapAmount = listMap.get(listMap.size() - 1);
            detailMap.putAll(mapCount);
            detailMap.putAll(mapAmount);
            listMap.remove(listMap.size() - 2);
            listMap.remove(listMap.size() - 1);
        } else {
            detailMap.put("totalCount", 0);
            detailMap.put("totalAmount", 0);
        }
        String template = TemplateUtils.TEMPLATE_PAY_COLLECT_REPORT_FILE_SRC_1;
        if (null != parameterPost.getLang() && "en".equals(parameterPost.getLang()) && "accountNumber".equals(parameterPost.getGroupBy())) {
            template = TemplateUtils.TEMPLATE_PAY_COLLECT_REPORT_FILE_SRC_2;
        }
        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang()) && "merchantId".equals(parameterPost.getGroupBy())) {
            template = TemplateUtils.TEMPLATE_PAY_COLLECT_REPORT_FILE_SRC_VI_1;
        }
        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang()) && "accountNumber".equals(parameterPost.getGroupBy())) {
            template = TemplateUtils.TEMPLATE_PAY_COLLECT_REPORT_FILE_SRC_VI_2;
        }

        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void summaryFundsTranfer(com.onepay.ma.service.queue.message.Message<SummaryQueryDto> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = summaryFundsTranferBuild.generate(connectionOnline, connectionReadOnly, messageData);
        SummaryQueryDto parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate(), 0);
        String toDate = convertStrDate(parameterPost.getToDate(), -1);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        detailMap.put("TIME_INTERVAL", parameterPost.getTimeIntervalConvert());
        String template = TemplateUtils.SUMMARY_FUNDS_TRANFER_TEMPLATE_SRC;
        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang())) {
            template = TemplateUtils.SUMMARY_FUNDS_TRANFER_TEMPLATE_SRC_VI;
        }
        ReportBuilder.newInstance()
                .template(template)
                .exportFileName(this.fileName())
                .setBeanValue("listMap", listMap)
                .setBeanValue("detail", detailMap)
                .build()
                .exportExcel();
    }

    private void fundsTranferHistory(com.onepay.ma.service.queue.message.Message<FundsTransHisQueryDto> messageData) throws Exception {

        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = fundsTranferHistoryBuild.generate(connectionOnline, connectionReadOnly, messageData);
        FundsTransHisQueryDto parameterPost = messageData.getRequestBody();
        Map detailMap = new HashMap();
        String fromDate = convertStrDate(parameterPost.getFromDate() + " 00:00:00", 0);
        String toDate = convertStrDate(parameterPost.getToDate() + " 23:59:59", 0);
        detailMap.put("FROM_DATE", fromDate);
        detailMap.put("TO_DATE", toDate);
        String downloadType = parameterPost.getDownloadType();
        String template = TemplateUtils.FUNDS_TRANFER_HISTORY_TEMPLATE_SRC;
        if (null != parameterPost.getLang() && "vi".equals(parameterPost.getLang())) {
            template = TemplateUtils.FUNDS_TRANFER_HISTORY_TEMPLATE_SRC_VI;
        }
        if (ParamsPool.CSV.equals(downloadType)) {
            ArrayList<String> listHeader = new ArrayList<String>();
            listHeader.add(TemplateUtils.FUNDS_MERCHANT_ID);
            listHeader.add(TemplateUtils.MERCHANT_ACCOUNT);
            listHeader.add(TemplateUtils.MERCHANT_TRANS_ID);
            listHeader.add(TemplateUtils.TRANSACTION_ID);
            listHeader.add(TemplateUtils.TRANSACTION_DATE);
            listHeader.add(TemplateUtils.UPDATE_DATE);
            listHeader.add(TemplateUtils.FUND_DATE);
            listHeader.add("AMOUNT");
            listHeader.add(TemplateUtils.RECEIVED_ACCOUNT);
            listHeader.add(TemplateUtils.RECEIVED_ACCOUNT_NAME);
            listHeader.add(TemplateUtils.RECEIVED_BANK);
            listHeader.add(TemplateUtils.REMARK);
            listHeader.add("CREATOR_NAME");
            listHeader.add("APPROVAL_NAME");
            listHeader.add("OPERATOR");
            listHeader.add("STATE");

            String fileTitle = "FUNDS TRANSFER TRANSACTION HISTORY";
            String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
            StringBuilder fileHeader = new StringBuilder("No,Merchant ID,Merchant Account,Fund Transfer ID,Transaction Id, Create Date,");
            fileHeader.append("Update Date,Fund Transfer Date,Amount,Beneficiary Account Name,Beneficiary Bank,Amount,");
            fileHeader.append("Remark,Creator,Verifier, Operator,Status");
            Map<String, String> mapDate = new HashMap<>();
            mapDate.put("DATE", "dd/MM/yyyy");
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .build()
                    .exportCsv(fileTitle, fileDate, fileHeader.toString(), listHeader, listMap, mapDate);
        } else {
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(this.fileName())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .build()
                    .exportExcel();
        }

    }

    private void batchDetail(com.onepay.ma.service.queue.message.Message<BatchDetailQueryDto> messageData) throws Exception {
        connectionReadOnly = this.readOnlyDataSource.getConnection();
        connectionOnline = this.onlineDataSource.getConnection();
        List<Map> listMap = batchDetailBuildImpl.generate(connectionOnline, connectionReadOnly, messageData);
        System.out.println(this.fileName());
        String[] headerRecord = {"type", "swiftCode", "accountNumber", "cardNumber", "merchantId", "merchantAccount", "accountName", "amount", "currency", "fundsTransferInfo", "remark", "reason"};
        List<String[]> contents = new ArrayList<>();
        if (!listMap.isEmpty()) {
            String[] contentDatas;
            for (Map map : listMap) {
                contentDatas = new String[] {map.get("TYPE") != null ? map.get("TYPE").toString() : "",
                        map.get("SWIFT_CODE") != null ? map.get("SWIFT_CODE").toString() : "",
                        map.get("ACCOUNT_NUMBER") != null ? map.get("ACCOUNT_NUMBER").toString() : "",
                        map.get("CARD_NUMBER") != null ? map.get("CARD_NUMBER").toString() : "",
                        map.get("ACCOUNT_NAME") != null ? map.get("ACCOUNT_NAME").toString() : "",
                        map.get("MERCHANT_ID") != null ? map.get("MERCHANT_ID").toString() : "",
                        map.get("MERCHANT_ACCOUNT") != null ? map.get("MERCHANT_ACCOUNT").toString() : "",
                        map.get("AMOUNT") != null ? map.get("AMOUNT").toString() : "",
                        map.get("CURRENCY") != null ? map.get("CURRENCY").toString() : "",
                        map.get("FUNDS_TRANSFER_INFO") != null ? map.get("FUNDS_TRANSFER_INFO").toString() : "",
                        map.get("REMARK") != null ? map.get("REMARK").toString() : "",
                        map.get("REASON") != null ? map.get("REASON").toString() : ""};
                contents.add(contentDatas);
            }
        }
        ReportCsvBuilder.newInstance()
                .exportFileName(this.fileName())
                .setBeanValue("headerRecord", headerRecord)
                .setBeanValue("listContent", contents)
                .build()
                .exportCsv();
    }

    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("#########");
        return df.format(value);
    }

    /**
     * Convert FromDate,ToDate Str(YYYY-MM-dd HH:mm:ss )--> Str(dd/MM/yyyy hh:mm a)
     */
    private String convertStrDate(String strDate, int amount) {
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = df.parse(strDate);
            date = DateUtils.addMinutes(date, amount);

            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            strDate = sdf.format(date);
        } catch (Exception e) {

        }
        return strDate;
    }


    protected String fileNameHash;

    protected String locationFileName;

    protected List<String> fileNames;

    abstract String fileName(String ext);

    String fileName() {
        return fileName("xls");
    }

    abstract void writeFile(com.onepay.ma.service.queue.message.Message<?> messageData, String ext) throws Exception;

    @Autowired
    @Qualifier("internationalTransaction")
    ReportDataBuild internationalTransactionReportBuild;

    @Autowired
    @Qualifier("internationalRefund")
    ReportDataBuild internationalRefundReportBuild;

    @Autowired
    @Qualifier("domesticTransaction")
    ReportDataBuild domesticTransactionReportBuild;


    @Autowired
    @Qualifier("paymentLink")
    ReportDataBuild paymentLinkBuild;


    @Autowired
    @Qualifier("domesticRefund")
    ReportDataBuild domesticRefundReportBuild;

    @Autowired
    @Qualifier("domesticReport")
    ReportDataBuild domesticReportReportBuild;

    @Autowired
    @Qualifier("samsungDomesticTransaction")
    ReportDataBuild samsungDomesticTransactionReportBuild;

    @Autowired
    @Qualifier("internationalReport")
    ReportDataBuild internationalReportReportBuild;

    @Autowired
    @Qualifier("promotionTransaction")
    ReportDataBuild promotionTransactionReportBuild;

    @Autowired
    @Qualifier("mpayTransaction")
    ReportDataBuild mpayReportDataBuild;

    @Autowired
    @Qualifier("samsungMpayTransaction")
    ReportDataBuild samsungMpayReportDataBuild;

    @Autowired
    @Qualifier("mocaMpayTransaction")
    ReportDataBuild mocaMpayReportDataBuild;

    @Autowired
    @Qualifier("mpayPrTransaction")
    ReportDataBuild mpayPrTransactionDataBuild;

    @Autowired
    @Qualifier("mpayPrReport")
    ReportDataBuild mpayPrReportDataBuild;

    @Autowired
    @Qualifier("financialTransaction")
    ReportDataBuild financialTransactionReportBuild;


    @Autowired
    @Qualifier("samsungFinancialTransaction")
    ReportDataBuild ssFinancialTransactionReportBuild;


    @Autowired
    @Qualifier("internationalAuthPayment")
    ReportDataBuild internationalAuthPaymentBuildImpl;

    @Autowired
    @Qualifier("mpayReport")
    ReportDataBuild mpayReportBuild;
    @Autowired
    @Qualifier("mpayReportPayment")
    ReportDataBuild mpayReportPaymentBuild;

    @Autowired
    @Qualifier("mocaMpayReport")
    ReportDataBuild mocaMpayReportBuild;

    @Autowired
    @Qualifier("mpayRefund")
    ReportDataBuild mpayRefundReportBuild;

    @Autowired
    @Qualifier("statictics")
    ReportDataBuild statisticsReportBuild;

    @Autowired
    @Qualifier("staticticsDetail")
    ReportDataBuild statisticsDetailReportBuild;

    @Autowired
    @Qualifier("staticticsDetailDt")
    ReportDataBuild statisticsDetailDtReportBuild;

    @Autowired
    @Qualifier("reconcileReport")
    ReportResultSetBuild reconcileReportBuild;

    @Autowired
    @Qualifier("reconcilePayment")
    PaymentReconcileBuildImpl paymentReconcileBuild;


    @Autowired
    @Qualifier("reconcilePaymentDetail")
    PaymentReconcileDetailBuildImpl paymentReconcileDetailBuild;


    @Autowired
    @Qualifier("generalReport")
    GeneralReportBuildImpl generalReportBuild;


    @Autowired
    @Qualifier("billingTransaction")
    BillingTransactionBuildImpl billingTransactionBuild;

    @Autowired
    @Qualifier("chartDomestic")
    ChartDomesticBuildImpl chartDomesticBuild;

    @Autowired
    @Qualifier("chartInternational")
    ChartInternationalBuildImpl chartInternationalBuild;

    @Autowired
    @Qualifier("paycollectTransaction")
    TransactionBuildImpl transactionBuildImpl;

    @Autowired
    @Qualifier("paycollectReport")
    ReportBuildImpl reportBuildImpl;

    @Autowired
    @Qualifier("chartTotal")
    ChartTotalBuildImpl chartTotalBuild;

    @Autowired
    @Qualifier("chartTotalDms")
    ChartTotalDmsBuildImpl chartTotalDmsBuild;

    @Autowired
    @Qualifier("chartTotalInter")
    ChartTotalInterBuildImpl chartTotalInterBuild;

    @Autowired
    @Qualifier("internationalRefundEmailTemplate")
    InternationalRefundEmailTemplateBuildImpl internationalRefundEmailTemplateBuild;

    @Autowired
    @Qualifier("paycollect_user_config")
    UserConfigBuildImpl userConfigBuild;

    @Autowired
    @Qualifier("summary_funds_tranfer_build")
    SummaryFundsTranferBuildImpl summaryFundsTranferBuild;

    @Autowired
    @Qualifier("batch_detail_build")
    BatchDetailBuildImpl batchDetailBuildImpl;

    @Autowired
    @Qualifier("funds_tranfer_history_build")
    FundsTranferHistoryBuildImpl fundsTranferHistoryBuild;

    @Autowired
    ServerConfig serverConfig;


    @Autowired
    @Qualifier(value = "onlineDataSourceDownload")
    private DataSource onlineDataSource;

    @Autowired
    @Qualifier(value = "readOnlyDataSourceDownload")
    private DataSource readOnlyDataSource;

    @Autowired
    @Qualifier(value = "payCollectDataSourceDownload")
    private DataSource payCollectDataSource;


    @Autowired
    @Qualifier(value = "MPDataSourceDownload")
    private DataSource merchantPortalDataSource;

    @Autowired
    @Qualifier(value = "backUpDataSource")
    private DataSource backupDataSource;


    @Autowired
    @Qualifier(value = "prDataSourceDownload")
    private DataSource promotionDataSource;

    @Autowired
    @Qualifier(value = "onereconDataSource")
    private DataSource onereconDataSource;

    Connection connectionOnline;

    Connection connectionReadOnly;

    Connection connectionPromotion;

    Connection connectionBackup;

    Connection connectionOnerecon;

    Connection connectionPaycollect;

    Connection connectionMerchantPortal;

    private static final Logger LOGGER = Logger.getLogger(BaseDownloadQueueInListener.class.getName());
}
