package com.onepay.ma.service.queue.listener.excelBuilder.ext.domestic;

import com.onepay.ma.service.util.Converter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.DomesticRefundParameter;
import com.onepay.ma.service.models.DomesticTransaction;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import groovy.json.JsonException;
import io.vertx.core.json.JsonObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("domesticRefund")
public class DomesticRefundBuildImpl implements ReportDataBuild<DomesticRefundParameter> {



    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<DomesticRefundParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        DomesticRefundParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            rsBackup = domesticRefundService.downloadBackp(connBackup, param);

            this.generateRsBackup(rsBackup, listMap, param.getLang());
            List<String> transIdList = listMap.stream().map(map -> map.get("N_ORIGINAL_ID")+"").collect(Collectors.toList());

            Map transMap = DomesticTransactionService.mapByIdsDownload(connOnline, String.join(StringPool.COMMA, transIdList),param);
            listMap = fillBackupData(listMap, transMap);

            rsOnline = domesticRefundService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param.getVersion(), param.getLang());
            rsReadonly = domesticRefundService.downloadReadonly(readOnly, param, listMap.size());
            this.generateRs(rsReadonly, listMap , param.getVersion(), param.getLang());
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT DOMESTIC REFUND] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsBackup != null) {
                try {
                    rsBackup.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, String version, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, version.equals("v2")
                    ? Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID")) != null
                    ? Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID"))
                    : rs.getString("S_ACQUIRER_NAME")
                    : rs.getString("S_ACQUIRER_NAME"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // purchase amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, rs.getString("N_PURCHASE_AMOUNT"));

            // purchase date
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, rs.getDate("D_PURCHASE_DATE"));

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, rs.getDate("D_MERCHANT_TRANSACTION_DATE"));

            // card numebr
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NUMBER"));

            // NOTE
            item.put("S_NOTE", getNoteData(rs.getString("S_DATA")));

            // status
            String statusData = rs.getString("N_TRANSACTION_STATUS");
            if (!statusData.isEmpty()) {
                switch (statusData) {
                        case "400": statusData = "Successful"; break;
                        case "210": statusData = "Waiting for OnePay's Approval"; break;
                        case "300": statusData = "Waiting for OnePay's Approval"; break;
                        case "401": statusData = "Wait for Approval"; break;
                        case "402": statusData = "Approval Rejected"; break;
                        case "403": statusData = "Approved"; break;
                        case "404": statusData = "Rejected"; break;
                        case "310": statusData = "OnePay Approved"; break;
                        default: statusData = "Failed"; break;
                    }

                    if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                        switch (rs.getString("N_TRANSACTION_STATUS")) {
                            case "400": statusData = "Thành công"; break;
                            case "210": statusData = "GD chờ OnePay duyệt"; break;
                            case "300": statusData = "GD chờ OnePay duyệt"; break;
                            case "401": statusData = "Đang chờ duyệt"; break;
                            case "402": statusData = "Approval Rejected"; break;
                            case "403": statusData = "Đơn vị đã chấp thuận"; break;
                            case "404": statusData = "Từ chối"; break;
                            case "310": statusData = "OnePay đã xử lý"; break;
                            default: statusData = "Không thành công"; break;
                        }
                    }
                }
            item.put(TemplateUtils.STATUS_COLUMN, statusData);

            // put into list
            listData.add(item);
        }

    }

    private void generateRsBackup(ResultSet rs, List<Map> listData, String lang) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
//            item.put(TemplateUtils.BANK_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getAcquirer().getAcquirer_name());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));


            // trans Id
            item.put("N_ORIGINAL_ID", rs.getString("N_ORIGINAL_ID"));


            // Order Info
//            item.put(TemplateUtils.ORDER_INFO_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getOrder_info());

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // purchase amount
//            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getAmount().getTotal());

            // purchase date
//            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, tranMap.get(rs.getInt("N_TRANSACTION_ID")).getTransaction_time());

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, rs.getDate("D_MERCHANT_TRANSACTION_DATE"));

            // status
            String statusData = rs.getString("N_TRANSACTION_STATUS");
            if (!statusData.isEmpty()) {
                switch (statusData) {
                    case "400": statusData = "Successful"; break;
                    case "210": statusData = "Waiting for OnePay's Approval"; break;
                    case "300": statusData = "Waiting for OnePay's Approval"; break;
                    case "401": statusData = "Wait for Approval"; break;
                    case "402": statusData = "Approval Rejected"; break;
                    case "403": statusData = "Approved"; break;
                    case "404": statusData = "Rejected"; break;
                    case "310": statusData = "Confirmed"; break;
                    default: statusData = "Failed"; break;
                }
                if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    switch (rs.getString("N_TRANSACTION_STATUS")) {
                        case "400": statusData = "Thành công"; break;
                        case "210": statusData = "GD chờ OnePay duyệt"; break;
                        case "300": statusData = "GD chờ OnePay duyệt"; break;
                        case "401": statusData = "Đang chờ duyệt"; break;
                        case "402": statusData = "Approval Rejected"; break;
                        case "403": statusData = "Đơn vị đã chấp thuận"; break;
                        case "404": statusData = "Từ chối"; break;
                        case "310": statusData = "OnePay đã xử lý"; break;
                        default: statusData = "Không thành công"; break;
                    }
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, statusData);

            // put into list
            listData.add(item);
        }

    }

    private List<Map> fillBackupData(List<Map> listData, Map<Integer, DomesticTransaction> tranMap) {

        List<Map> result = new ArrayList<>();

        for (Map item : listData) {
            if (tranMap.get(item.get("N_ORIGINAL_ID")) != null) {
                item.put(TemplateUtils.PURCHASE_DATE_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getTransaction_time());
                item.put(TemplateUtils.BANK_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getAcquirer().getAcquirer_name());
                item.put(TemplateUtils.ORDER_INFO_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getOrder_info());
                item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, tranMap.get(item.get("N_ORIGINAL_ID")).getAmount().getTotal());
                result.add(item);
            }
        }

        return result;

    }

    // get Note Data from a String JsonObject
    private static String getNoteData(String jsonObjectString) {
        String note = null;
        JsonObject jsonObject = null;
        if (jsonObjectString != null && !jsonObjectString.equals("")) {
            try {
                jsonObject = new JsonObject(jsonObjectString);
            } catch (JsonException err) {
                Logger.getLogger("Error parse: ", err.toString());
            }

            try {
                note = jsonObject.getString("note");
            } catch (JsonException err) {
                Logger.getLogger("Error get note data : ", err.toString());
            }
        }

        return note;
    }

    @Autowired
    private DomesticRefundService domesticRefundService;

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionBuildImpl.class.getName());
}
