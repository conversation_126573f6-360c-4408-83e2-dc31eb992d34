package com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.mpayPromotion.MpayPromotionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 30-Jan-18.
 */
@Service("mpayPrTransaction")
public class
MpayPrTransactionBuildImpl  implements ReportDataBuild<MpayPromotionQuery> {

    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());
    @Autowired
    private MpayPromotionService mpayPromotionService;

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<MpayPromotionQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        MpayPromotionQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = mpayPromotionService.downloadOnline(connOnline, param);
            rsReadonly = mpayPromotionService.downloadReadonly(readOnly, param);
            this.generateRs(rsOnline, listMap);
            this.generateRs(rsReadonly, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // merchant transaction ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCHANT_INVOICE_REF") == null ? StringPool.BLANK : rs.getString("S_MERCH_TXN_REF"));

            // trans Id
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, rs.getString("S_ID"));

            // Pr Id
            item.put(TemplateUtils.PR_ID_COLUMN, rs.getString("S_PR_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_INFO"));

            // Mobile
            item.put(TemplateUtils.MOBILE_COLUMN, rs.getString("S_MOBILE"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_INS_NUMBER"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // amount
            item.put(TemplateUtils.TOTAL_AMOUNT_COLUMN, rs.getDouble("N_TOTAL_AMOUNT"));

            // amount
            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, rs.getDouble("N_AMOUNT"));

            // amount
            item.put(TemplateUtils.DISCOUNT_AMOUNT_COLUMN, rs.getDouble("N_AMOUNT") - rs.getDouble("N_TOTAL_AMOUNT"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_CREATE"));

            // status
            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("S_STATE"));

            // card type
            String cardType= rs.getString("S_CARD_TYPE");
            item.put(TemplateUtils.CARD_TYPE_COLUMN, PropsUtil.get(cardType, StringPool.BLANK));


            // put into list
            listData.add(item);
        }

    }
}
