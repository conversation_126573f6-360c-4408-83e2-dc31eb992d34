package com.onepay.ma.service.queue.server;

import com.onepay.ma.service.queue.listener.*;
import com.onepay.ma.service.util.Utils;

import org.apache.activemq.broker.BrokerService;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.stereotype.Component;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 19-Jan-16.
 */
@Component
public class QueueServer implements Runnable{

    private String queueURL;
    private String queueDataDirectory;

    private long queueTempLimit;
    private long queueMemoryLimit;
    private long queueStorageLimit;
    private CachingConnectionFactory connectionFactory;

    public  void setQueueURL(String queueURL) {
        this.queueURL = queueURL;
    }

    public  void setQueueDataDirectory(String queueDataDirectory) {
        this.queueDataDirectory = queueDataDirectory;
    }

    public  void setQueueTempLimit(long queueTempLimit) {
        this.queueTempLimit = queueTempLimit;
    }

    public  void setQueueMemoryLimit(long queueMemoryLimit) {
        this.queueMemoryLimit = queueMemoryLimit;
    }

    public  void setQueueStorageLimit(long queueStorageLimit) {
        this.queueStorageLimit = queueStorageLimit;
    }

    public void setConnectionFactory(CachingConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public void setDownloadQueueSlowOutListener(DownloadQueueSlowOutListener downloadQueueSlowOutListener) {
        this.downloadQueueSlowOutListener = downloadQueueSlowOutListener;
    }

    public void setDownloadQueueSlowInListener(DownloadQueueSlowInListener downloadQueueSlowInListener) {
        this.downloadQueueSlowInListener = downloadQueueSlowInListener;
    }

    public void setDownloadQueueMultiOutListener(DownloadQueueMultiOutListener downloadQueueMultiOutListener) {
        this.downloadQueueMultiOutListener = downloadQueueMultiOutListener;
    }

    public void setDownloadQueueMultiInListener(DownloadQueueMultiInListener downloadQueueMultiInListener) {
        this.downloadQueueMultiInListener = downloadQueueMultiInListener;
    }


    public void init() {
        Thread thread = new Thread(this);
        thread.start();
    }


    @Override
    public void run() {
        try {

            LOGGER.log(Level.INFO, "------------------------------- STARTING DOWNLOAD QUEUE SERVER -----------------------------------");

            BrokerService brokerService = new BrokerService();
            brokerService.addConnector(queueURL);

            brokerService.setDataDirectory( queueDataDirectory);

            brokerService.getSystemUsage().getTempUsage().setLimit(1024 * 1024 * queueTempLimit);
            brokerService.getSystemUsage().getMemoryUsage().setLimit(1024 * 1024 * queueMemoryLimit);
            brokerService.getSystemUsage().getStoreUsage().setLimit(1024 * 1024 * queueStorageLimit);
            brokerService.setUseJmx(false);
            if(brokerService.isStarted()){
                brokerService.stop();
                brokerService.waitUntilStopped();
                brokerService.start();
            }else{

                brokerService.start();
            }



            LOGGER.log(Level.INFO, "-------------------------------  DOWNLOAD QUEUE SERVER START UP SUCCESSFULLY AT " + Utils.excludeSensitiveInfo(queueURL) + " ------------------------------- ");

        } catch (Exception e){
            LOGGER.log(Level.SEVERE, "Error", e);
        }
    }

    private DownloadQueueSlowOutListener downloadQueueSlowOutListener;

    private DownloadQueueSlowInListener downloadQueueSlowInListener;

    private DownloadQueueMultiOutListener downloadQueueMultiOutListener;

    private DownloadQueueMultiInListener downloadQueueMultiInListener;

    private DownloadQueueFastOutListener downloadQueueFastOutListener;

    private DownloadQueueFastInListener downloadQueueFastInListener;

    private static final Logger LOGGER = Logger.getLogger(QueueServer.class.getName());


}
