package com.onepay.ma.service.queue.listener;

import com.google.gson.Gson;
import com.onepay.ma.service.models.NotificationConfig;
import com.onepay.ma.service.server.ServiceServer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.StringPool;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.Vertx;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 22-Jan-16.
 */
@Component
public class DownloadQueueMultiOutListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
        com.onepay.ma.service.queue.message.Message messageData = null;

        try {
            messageData = (com.onepay.ma.service.queue.message.Message) ((ObjectMessage) message).getObject();
        } catch (JMSException e) {
            LOGGER.log(Level.SEVERE, "Error Generate File", e);
            return;
        }
        int code = messageData.getResultCode();
        Map requestData = messageData.getRequestData();
        Map resultNoti = new HashMap<>();
        JsonObject json = new JsonObject();
        json.put("notification_id", "notification." + requestData.get(ParamsPool.X_USER_ID));

        Connection backUpConnection = null;
        try {
            backUpConnection = backUpDataSource.getConnection();
            String status = StringPool.BLANK;
            if(code == 200){
                resultNoti.put(ParamsPool.HANDLER_DATA_CODE, "200");
                resultNoti.put(ParamsPool.FILE_HASH_NAME, requestData.get(ParamsPool.FILE_HASH_NAME));
                resultNoti.put(ParamsPool.FILE_NAME, requestData.get(ParamsPool.FILE_NAME));
                status = "success";
            }else{
                resultNoti.put(ParamsPool.HANDLER_DATA_CODE, "500");
                resultNoti.put(ParamsPool.FILE_HASH_NAME, requestData.get(ParamsPool.FILE_HASH_NAME));
                resultNoti.put(ParamsPool.FILE_NAME, requestData.get(ParamsPool.FILE_NAME));
                status = "failed";
            }
            fileService.updateStatusDownload(backUpConnection, status, String.valueOf(requestData.get(ParamsPool.FILE_HASH_NAME)),  Long.valueOf(requestData.get(ParamsPool.FILE_SIZE).toString()));

            LOGGER.log(Level.INFO, "Generate File With Info " +   gson.toJson(requestData));
            json.put("notification_detail", resultNoti);
        } catch (SQLException e) {
            resultNoti.put(ParamsPool.HANDLER_DATA_CODE, "500");
            resultNoti.put(ParamsPool.FILE_HASH_NAME, requestData.get(ParamsPool.FILE_HASH_NAME));
            resultNoti.put(ParamsPool.FILE_NAME, requestData.get(ParamsPool.FILE_NAME));
            json.put("notification_detail", resultNoti);
            LOGGER.log(Level.SEVERE, "Error Generate File", e);
        } finally {
            if(backUpConnection != null){
                try {
                    backUpConnection.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
            sendToNotification(serviceServer.getCurrentVertx(), Json.encode(json));
        }

    }

    private void sendToNotification(Vertx vertx, String param){
        final HttpClient httpClient = vertx.createHttpClient();
        HttpClientRequest request = httpClient.postAbs(notificationConfig.getUrl() + "/push");
        request.toObservable().subscribe(httpClientResponse -> {
            int status = httpClientResponse.statusCode();
            if(status != 201){
                LOGGER.log(Level.SEVERE, "Send Notification Error : " + httpClientResponse.statusMessage());
            }
        },throwable -> {
            LOGGER.log(Level.SEVERE, "Send Notification Error : ", throwable);
        });

        Buffer buffer = Buffer.buffer(param);
        request.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
        request.write(buffer);
        request.end();
    }
    @Autowired
    @Qualifier(value = "backUpDataSource")
    private DataSource backUpDataSource;

    @Autowired
    private ServiceServer serviceServer;

    @Autowired
    private FileService fileService;

    @Autowired
    private NotificationConfig notificationConfig;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(DownloadQueueMultiOutListener.class.getName());
}
