package com.onepay.ma.service.queue.listener.excelBuilder.ext.billing;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("billingTransaction")
public class BillingTransactionBuildImpl implements ReportDataBuild<OnebillTransactionQuery> {
    @Override
    public List<Map> generate(Connection connOnebill, Connection readOnly, Connection connBackup, Message<OnebillTransactionQuery> messageData) {
        List<Map> listMap = new ArrayList<>();

        OnebillTransactionQuery param = messageData.getRequestBody();
        ResultSet rsOnebill = null;
        try {
            rsOnebill = oneBillTransactionService.download(connOnebill, param);
            this.generateRs(rsOnebill, listMap);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT DOMESTIC TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnebill != null) {
                try {
                    rsOnebill.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }


    private void generateRs(ResultSet rs, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, rs.getString("S_BANK_ID"));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_PROVIDER_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_TRANS_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // trans ref
            item.put(TemplateUtils.CHANNEL_COLUMN, rs.getString("S_CHANNEL_ID"));

            // Card no
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, rs.getString("S_TRANSACTION_TYPE"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // amount
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // date
            Date transactionTime = rs.getDate("D_DATE");
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            // status
            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("S_STATE"));

            // put into list
            listData.add(item);
        }

    }

    @Autowired
    private OneBillTransactionService oneBillTransactionService;

    private static final Logger LOGGER = Logger.getLogger(BillingTransactionBuildImpl.class.getName());
}
