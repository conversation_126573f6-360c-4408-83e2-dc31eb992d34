package com.onepay.ma.service.queue.listener.excelBuilder.ext.chartStatistics;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.mpay.MpayTransactionBuildImpl;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.Converter;
import com.onepay.ma.service.util.IErrors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

;

@Service("chartTotalDms")
public class ChartTotalDmsBuildImpl implements ReportDataBuild<ChartStatisticQuery> {
    private static final Logger LOGGER = Logger.getLogger(MpayTransactionBuildImpl.class.getName());
    @Autowired
    private StatisticChartService statisticChartService;
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ChartStatisticQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        List<Map> listMapFix = new ArrayList<>();
        ChartStatisticQuery query = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = this.statisticChartService.dowloadFileTotal(readOnly, query);
            String paygate = query.getPaygate();
            this.generateRs(rsOnline, listMap,listMapFix,paygate);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[CHART DOMESTIC BUILDING] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if(rsOnline != null){
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if(rsReadonly != null){
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }
    private void generateRs(ResultSet rs, List<Map> listData ,List<Map> listMapFix ,String paygate) throws Exception {
        int rowNumber = listMapFix.size();
        while (rs.next()) {
            if(rs.getString("PAYGATE").equals("ND")){
                Map item = new HashMap();
                Integer iSuccess =0;
                Integer iFailed = 0;
                if(paygate.contains("ND")) {
                    String nSuccess = rs.getString("SUCCESS_TRANSACTION");
                    iSuccess = Integer.parseInt(nSuccess);
                    String nFailed = rs.getString("FAILED_TRANSACTION");
                    iFailed = Integer.parseInt(nFailed);
                }
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.CARD_TYPE_COLUMN, Converter.convertBankNDMA(rs.getInt("S_CARD_TYPE")));
//                item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARD_TYPE"));
                item.put("N_TOTAL", iSuccess+ iFailed);
                item.put("N_SUCCESS", iSuccess);
                item.put("N_FAILED", iFailed);
                if(paygate.contains("ND")) {
                    listMapFix.add(item);
                }
            }
        }
        Integer total = 0;
        Integer iSuccess =0;
        Integer iFailed = 0;
        Integer count = 1;
        for(Map m : listMapFix){
            Map map = new HashMap();
            if(m.get("S_CARD_TYPE")!= null && m.get("S_CARD_TYPE").toString().equals("Vietcombank")){
                total += Integer.parseInt(m.get("N_TOTAL").toString());
                iSuccess += Integer.parseInt(m.get("N_SUCCESS").toString());
                iFailed += Integer.parseInt(m.get("N_FAILED").toString());
            }else{
                map.put(TemplateUtils.ROW_NUMBER_COLUMN, count++);
                map.put(TemplateUtils.CARD_TYPE_COLUMN, m.get("S_CARD_TYPE"));
                map.put("N_TOTAL", m.get("N_TOTAL"));
                map.put("N_SUCCESS", m.get("N_SUCCESS"));
                map.put("N_FAILED", m.get("N_FAILED"));
                listData.add(map);
            }
        }
        Map item2 = new HashMap();
        item2.put(TemplateUtils.ROW_NUMBER_COLUMN, count);
        item2.put(TemplateUtils.CARD_TYPE_COLUMN, "Vietcombank");
        item2.put("N_TOTAL", total);
        item2.put("N_SUCCESS", iSuccess);
        item2.put("N_FAILED", iFailed);
        listData.add(item2);
    }
}
