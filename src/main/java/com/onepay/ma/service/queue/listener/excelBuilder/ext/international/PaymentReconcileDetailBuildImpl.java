package com.onepay.ma.service.queue.listener.excelBuilder.ext.international;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.reconciliation.*;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service("reconcilePaymentDetail")
public class PaymentReconcileDetailBuildImpl implements ReportDataBuild<ReconciliationDetailQuery> {

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<ReconciliationDetailQuery> messageData) {
        ReconciliationDetailQuery param = messageData.getRequestBody();


        List<Map> resultMap = new ArrayList<>();

        try {

                List<Reconciliation> rs = this.reconcileService.downloadDetailPayment(readOnly, param);
                this.generateRs(rs, resultMap);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "GENERATE INTERNATIONAL REPORT: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


        return resultMap;
    }

    private List<Map> generateRs(List<Reconciliation> rs, List<Map> listData) throws Exception {
//        List<Map> listData =  new ArrayList<>();
        int rowNumber = listData.size();

        for (Reconciliation r : rs) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_SETTLEMENT_COLUMN, r.getSettlement_date());

            // Card Type
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, r.getMerchant_id());

            // MID NO
            item.put(TemplateUtils.MID_NO_COLUMN, r.getMid_no());


            // Card Type
            item.put(TemplateUtils.ACQUIRER_COLUMN, r.getAcquirer());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, r.getCard_type());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, r.getCurrency());

            // Transaction Count
            item.put(TemplateUtils.TRANSACTION_COUNT_COLUMN, r.getCount_transaction());

            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, r.getCount_purchase());

            // Transaction Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN, r.getCount_refund());

            // Transaction Count
            item.put(TemplateUtils.VOID_PURCHASE_COUNT_COLUMN, r.getCount_void());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, r.getTotal_purchase());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, r.getTotal_refund());

            // Refund total Count
            item.put(TemplateUtils.VOID_PURCHASE_TOTAL_COUNT_COLUMN, r.getTotal_void());

            // Refund total Count
            item.put(TemplateUtils.AUTHORISE_TOTAL_COUNT_COLUMN, r.getTotal_authorise());

            // Refund total Count
            item.put(TemplateUtils.CAPTURE_TOTAL_COUNT_COLUMN, r.getTotal_capture());

            // put into list
            listData.add(item);
        }

        return listData;

    }

    @Autowired
    private ReconcileService reconcileService;


    private static final Logger LOGGER = Logger.getLogger(PaymentReconcileBuildImpl.class.getName());
}
