package com.onepay.ma.service.queue.listener;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.StringPool;

import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.*;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by HungDX on 22-Jan-16.
 */
public class DownloadQueueMultiInListener extends BaseDownloadQueueInListener implements MessageListener {
    @Override
    void writeFile(com.onepay.ma.service.queue.message.Message<?> messageData, String ext) throws Exception {
            FileOutputStream fos = new FileOutputStream(this.fileNameHash);
            ZipOutputStream zos = new ZipOutputStream(fos);
            messageData.getRequestBody();
            for (String fileName: this.fileNames) {
                addToZipFile(serverConfig.getExportLocation()+ StringPool.FORWARD_SLASH+fileName+ StringPool.DOT + ext, zos);
            }
            File file = new File(this.fileNameHash);
            long fileSize = file.length();
            zos.close();
            fos.close();
            messageData.getRequestData().put(ParamsPool.FILE_SIZE, fileSize);
            messageData.setResultCode(200);
            messageData.setResultString("File Has been generated");
        }

    @Override
    String fileName(String ext) {
        return this.locationFileName+ StringPool.DOT + ext;
    }

    private  void addToZipFile(String fileName, ZipOutputStream zos) throws FileNotFoundException, IOException {

        LOGGER.info("Writing '" + fileName + "' to zip file");

        File file = new File(fileName);
        FileInputStream fis = new FileInputStream(file);
        ZipEntry zipEntry = new ZipEntry(file.getName());
        zos.putNextEntry(zipEntry);

        byte[] bytes = new byte[1024];
        int length;
        while ((length = fis.read(bytes)) >= 0) {
            zos.write(bytes, 0, length);
        }
        file.delete();
        zos.closeEntry();
        fis.close();
    }


    @Override
    public void onMessage(Message message) {
        this.onBaseMassage(message);
    }

    private static final Logger LOGGER = Logger.getLogger(DownloadQueueMultiInListener.class.getName());
}
