package com.onepay.ma.service.queue.listener.excelBuilder.ext.domestic;

import com.onepay.ma.service.util.Converter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.DomesticTxnParameter;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.domestic.SSDomesticTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("samsungDomesticTransaction")
public class SamsungDomesticTransactionBuildImpl implements ReportDataBuild<DomesticTxnParameter> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<DomesticTxnParameter> messageData) {
        List<Map> listMap = new ArrayList<>();
        DomesticTxnParameter param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        try {
            rsOnline = SSDomesticTransactionService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param);
            rsReadonly = SSDomesticTransactionService.downloadReadonly(readOnly, param, listMap.size());
            this.generateRs(rsReadonly, listMap, param);
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT DOMESTIC TRANSACTION] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }

    private void generateRs(ResultSet rs, List<Map> listData, DomesticTxnParameter param) throws Exception {
        int rowNumber = listData.size();
        while (rs.next()) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID")) == null ? rs.getString("S_ACQUIRER_NAME") : Converter.convertBankNDMA(rs.getInt("N_ACQUIRER_ID")));

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_TRANSACTION_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));

            // email
            item.put("S_CUSTOMER_EMAIL", rs.getString("S_CUSTOMER_EMAIL"));

            item.put("S_CUSTOMER_PHONE", rs.getString("S_CUSTOMER_PHONE"));

           item.put("S_MERCHANT_WEBSITE", "");

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, rs.getString("S_MERCHANT_TRANSACTION_REF"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NUMBER"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // date
            Date transactionTime = rs.getDate("D_MERCHANT_TRANSACTION_DATE");
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            // response code
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, this.convertCardVertificationCode(rs, param));

            // status
            item.put(TemplateUtils.STATUS_COLUMN, rs.getString("S_ADVANCE_STATUS"));

            // fraud
            item.put("S_FRAUD", rs.getString("S_FRAUD"));

            // order status
            item.put("S_REVIEW_STATE", StringUtils.capitalize(rs.getString("S_REVIEW_STATE")));

            // put into list
            listData.add(item);
        }

    }

    private String convertCardVertificationCode(ResultSet rs, DomesticTxnParameter param) throws SQLException {

        String status = String.valueOf(rs.getInt("N_TRANSACTION_STATUS"));
        String cardVerificationCode = String.valueOf(rs.getInt("N_CARD_VERIFICATION_CODE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String authTime = rs.getString("D_USER_AUTHENTICATION_DATE");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");

        if(advanceStatus == null) {
            return null;
        }
        // SPECIAL CASE : 17/04/2019
        if (acquirerId == 0 && status.equals("100") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100")) {
            return "No Select Bank";
        }
        if (advanceStatus.equals("Waiting for authentication")) {
            return "";
        }
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("99")) {
            return "Authentication cancelled";
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null
                && (acquirerId == 2 || acquirerId == 6 || acquirerId == 5 || acquirerId == 31 || acquirerId == 21)) {
            return "No Response from IB payment"; // case Techcombank, DongAbank, VIB, Viettelpay, CUP && onepay admin
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null) {
            return "Authentication failed";
        }
        if (status.equals("100") && cardVerificationCode.equals("100")) {
            return "Not process";
        }
        if (status.equals("300") && advanceStatus.equals("Successful") && cardVerificationCode.equals("100")) {
            return "0 - Approved";
        }
//        if (cardVerificationCode.equals("100")) {
//            return "";
//        }

         /*
         Special case with response code
         Case status is 200 and failed, response code is 100
         the response code will be change with specific bank requirements
         */
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100") && authTime != null) {
            Integer authCode = rs.getInt("N_USER_AUTHENTICATION_CODE");
            String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                    acquirerId + StringPool.DOT + authCode, "1"); // default is 1

            cardVerificationCode = specialResponseCode.length() == 0 ? "1" : specialResponseCode;
        }

        return cardVerificationCode + "-" + PropsUtil.get("D" + cardVerificationCode, "Not Response");
    }



    private static final Logger LOGGER = Logger.getLogger(SamsungDomesticTransactionBuildImpl.class.getName());
}
