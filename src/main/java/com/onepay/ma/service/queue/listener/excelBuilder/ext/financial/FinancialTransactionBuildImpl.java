package com.onepay.ma.service.queue.listener.excelBuilder.ext.financial;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.financial.FinancialTransactionService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.stereotype.Service;

import groovy.json.JsonException;
import io.vertx.core.json.JsonObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 05-Jun-17.
 */
@Service("financialTransaction")
public class FinancialTransactionBuildImpl implements ReportDataBuild<FinancialTransactionQuery> {
    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<FinancialTransactionQuery> messageData) {

        LOGGER.log(Level.INFO, "[GENERATE FINANCIAL INTERNATIONAL TRANSACTION DATA START]: ");

        Long start = System.currentTimeMillis();
        List<Map> listMap = new ArrayList<>();
        FinancialTransactionQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        try {
            rsBackup = FinancialTransactionService.downloadBackup(connBackup, param);
            this.generateRs(rsBackup, listMap, param.getLang());
            // List<String> transIdList = listMap.stream().map(map -> map.get("N_TRANSACTION_ID") + "").collect(Collectors.toList());

            // Map transMap = FinancialTransactionService.transMapByIdsDownload(connOnline, String.join(StringPool.COMMA, transIdList), param);
            // listMap = fillBackupData(listMap, transMap);

            rsOnline = FinancialTransactionService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param.getLang());
            LOGGER.log(Level.INFO, "[GENERATE FINANCIAL INTERNATIONAL TRANSACTION DATA ONLINE END]: " + (System.currentTimeMillis() - start));

            rsReadonly = FinancialTransactionService.downloadReadonly(readOnly, param);
            this.generateRs(rsReadonly, listMap, param.getLang());
            listMap.sort((o1, o2) -> {
                Date d2 = (Date) o2.get(TemplateUtils.DATE_COLUMN);
                Date d1 = (Date) o1.get(TemplateUtils.DATE_COLUMN);
                return d2.compareTo(d1);
            });
            int index = 1;
            for (Map data : listMap) {
                data.put(TemplateUtils.ROW_NUMBER_COLUMN, index++);
            }
            LOGGER.log(Level.INFO, "[GENERATE FINANCIAL INTERNATIONAL TRANSACTION DATA READ ONLY END]: " + (System.currentTimeMillis() - start));
        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT FINANCIAL TRANSACTION] ", ex);
            throw IErrors.FINANCIAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }


    private void generateRsBackup(ResultSet rs, List<Map> listData) throws Exception {
        Map item = null;
        while (rs.next()) {
            item = new HashMap();
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getInt("N_ORIGINAL_ID"));

            item.put("S_MERCHANT_TRANSACTION_REF", rs.getString("S_MERCHANT_TRANSACTION_REF"));
            // status
            String statusData = rs.getString("N_TRANSACTION_STATUS");
            if (!statusData.isEmpty()) {
                switch (statusData) {
                    case "405":
                        statusData = "Waiting for OnePay's Approval";
                        break;
                    case "401":
                        statusData = "Wait for approval";
                        break;
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, statusData);
            item.put("S_TRANS_TYPE", rs.getString("S_TRANSACTION_TYPE"));
            item.put(TemplateUtils.DATE_COLUMN, Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE")));

            // put into list
            listData.add(item);
        }

    }

    private List<Map> fillBackupData(List<Map> listData, Map<String, InternationalTransaction> tranMap) {

        List<Map> result = new ArrayList<>();
        for (Map item : listData) {
            InternationalTransaction transaction = tranMap.get(item.get("N_TRANSACTION_ID"));
            if (null != transaction) {
                // merchant Id
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, transaction.getMerchant_id());

                // date
                // item.put(TemplateUtils.DATE_COLUMN, transaction.getTransaction_time());

                item.put("D_ORIGINAL_DATE", transaction.getTransaction_time());

                item.put("ORIGINAL_AMOUNT", transaction.getAmount().getTotal());
                item.put("S_ORDER_INFO", transaction.getOrder_info());
                item.put("S_BANK_ID", transaction.getBank_id());
                item.put(TemplateUtils.CARD_NO_COLUMN, transaction.getCard().getCard_number());

                item.put(TemplateUtils.AMOUNT_COLUMN, transaction.getAmount().getTotal());
                item.put(TemplateUtils.NAME_ON_CARD, transaction.getCard_holder_name());

                String responseCode = "";
                if (transaction.getResponse_code() != null) {
                    responseCode = transaction.getResponse_code() + "-" +
                            PropsUtil.get(transaction.getResponse_code(), "");
                }
                item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

                // currency
                item.put(TemplateUtils.CURRENCY_COLUMN, transaction.getAmount().getCurrency());

                // Authorization Code
                item.put(TemplateUtils.AUTH_CODE_COLUMN, transaction.getAuthentication().getAuthorization_code());

                // INSTALLMENT
                item.put("S_INSTALLMENT_STATE", transaction.getInstallment_status());

                // Card type
                item.put(TemplateUtils.CARD_TYPE_COLUMN, transaction.getCard().getCard_type());

                item.put("S_RISK_ASSESSMENT", "");

                // // NOTE
                item.put("S_NOTE", "");

                result.add(item);
            }
        }
        return result;

    }

    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        Map<String, String> disputeReasonEn = new HashMap();
        Map<String, String> disputeReasonVi = new HashMap();
        disputeReasonEn.put("DPR-01", "Refund at merchant's request");
        disputeReasonEn.put("DPR-02", "Lost due to overdue");
        disputeReasonEn.put("DPR-03", "Lost at arbitration");
        disputeReasonEn.put("DPR-04", "Credit chargeback - Funds back to merchant");

        disputeReasonVi.put("DPR-01", "Hoàn tiền theo yêu cầu của ĐVCNTT");
        disputeReasonVi.put("DPR-02", "Hoàn tiền do quá hạn");
        disputeReasonVi.put("DPR-03", "Hoàn tiền do thua kiện ở Trọng tài");
        disputeReasonVi.put("DPR-04", "ĐVCNTT nhận lại tiền");
        while (rs.next()) {
            Map item = new HashMap();

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANTID"));

            // date
            item.put(TemplateUtils.DATE_COLUMN, rs.getTimestamp("D_DATE"));

            item.put("D_ORIGINAL_DATE", rs.getTimestamp("D_ORIGINAL_DATE"));

            item.put("ORIGINAL_AMOUNT", rs.getDouble("N_ORIGINAL_AMOUNT"));

            // trans Id
            // item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("S_TRANSACTIONID"));

            Integer transactionId = rs.getInt("ONECREDIT_TRANS_ID");
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, transactionId == 0 ? "" : transactionId.toString());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDERREFERENCE"));

            // trans ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCHANTTRANSACTIONREFEREN"));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARDNO"));


            // item.put(TemplateUtils.NAME_ON_CARD, rs.getString("S_NAME_ON_CARD"));

            // BIN_COUNTRY
            item.put("BIN_COUNTRY", rs.getString("BIN_COUNTRY"));

            // // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_CARDTYPE"));

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, rs.getString("N_AMOUNT"));

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // trans type
            String transType = rs.getString("S_TRANSACTIONTYPE");
            if (null != lang && lang.isEmpty() || "en".equalsIgnoreCase(lang)) {
                if ("PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Purchase";
                } else if ("REFUND".equalsIgnoreCase(transType)) {
                    transType = "Refund";
                } else if ("REQUEST REFUND".equalsIgnoreCase(transType)) {
                    transType = "Request refund";
                } else if ("VOID_PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Void Purchase";
                } else if ("AUTHORIZE".equalsIgnoreCase(transType)) {
                    transType = "Authorize";
                } else if ("CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Capture";
                } else if ("REQUEST CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Request Capture";
                } else if ("REFUND CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Refund Capture";
                } else if ("REFUND DISPUTE".equalsIgnoreCase(transType)) {
                    transType = "Refund Dispute";
                }
            } else if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Thanh toán";
                } else if ("REFUND".equalsIgnoreCase(transType)) {
                    transType = "Hoàn tiền";
                } else if ("REQUEST REFUND".equalsIgnoreCase(transType)) {
                    transType = "Yêu cầu hoàn trả";
                } else if ("VOID PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Hủy-Thanh toán";
                } else if ("AUTHORIZE".equalsIgnoreCase(transType)) {
                    transType = "Cấp phép";
                } else if ("CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Quyết toán";
                } else if ("REQUEST CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Hoàn trả quyết toán";
                } else if ("REFUND DISPUTE".equalsIgnoreCase(transType)) {
                    transType = "Hoàn trả khiếu nại";
                }
            }
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, transType);

            // email
            item.put("S_CUSTOMER_EMAIL", rs.getString("S_CUSTOMER_EMAIL"));

            item.put("S_CUSTOMER_PHONE", rs.getString("S_CUSTOMER_PHONE"));

            item.put("S_CUSTOMER_NAME", rs.getString("S_CUSTOMER_NAME"));

            // item.put("S_MERCHANT_WEBSITE", rs.getString("S_MERCHANT_WEBSITE"));

            item.put("S_FRAUD", rs.getString("S_FRAUD"));

            // response code
            String responseCode = "";
            if (rs.getObject("S_RESPONSECODE") != null) {
                responseCode = rs.getString("S_RESPONSECODE") + "-" + PropsUtil.get(rs.getString("S_RESPONSECODE"), "");
            }
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                //international response code
                if ("0-Approved".equalsIgnoreCase(responseCode)) {
                    responseCode = "0 - Phê duyệt";
                } else if ("1-Unspecified Failure".equalsIgnoreCase(responseCode)) {
                    responseCode = "1 - Thất bại không xác định";
                } else if ("2-Declined".equalsIgnoreCase(responseCode)) {
                    responseCode = "2 - Bị từ chối";
                } else if ("3-Time Out".equalsIgnoreCase(responseCode)) {
                    responseCode = "3 - GD quá hạn";
                } else if ("4-Expired Card".equalsIgnoreCase(responseCode)) {
                    responseCode = "4 - Thẻ hết hạn";
                } else if ("5-Insufficient Funds".equalsIgnoreCase(responseCode)) {
                    responseCode = "5 - Không đủ số dư";
                } else if ("6-Bank Errors".equalsIgnoreCase(responseCode)) {
                    responseCode = "6 - Lỗi ngân hàng";
                } else if ("7-System Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "7 - Lỗi hệ thống";
                } else if ("8-Not Supported".equalsIgnoreCase(responseCode)) {
                    responseCode = "8 - Không hỗ trợ";
                } else if ("9-Bank Declined".equalsIgnoreCase(responseCode)) {
                    responseCode = "9 - Ngân hàng từ chối";
                } else if ("A-Authentication Failed".equalsIgnoreCase(responseCode)) {
                    responseCode = "A - Xác thực không thành công";
                } else if ("B-Transaction Blocked".equalsIgnoreCase(responseCode)) {
                    responseCode = "B - Giao dịch bị chặn";
                } else if ("C-Transaction Cancelled".equalsIgnoreCase(responseCode)) {
                    responseCode = "C - Giao dịch bị hủy";
                } else if ("D-Awaiting Processing".equalsIgnoreCase(responseCode)) {
                    responseCode = "D - Đang chờ xử lý";
                } else if ("E-Cardholder Not Enrolled".equalsIgnoreCase(responseCode)) {
                    responseCode = "E - Referred";
                } else if ("F-3D Secure Failure".equalsIgnoreCase(responseCode)) {
                    responseCode = "F - Lỗi bảo mật 3D";
                } else if ("I-Internal Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "I - Lỗi nội bộ";
                } else if ("L-Transaction Locked".equalsIgnoreCase(responseCode)) {
                    responseCode = "L - Giao dịch bị khóa";
                } else if ("N-Cardholder".equalsIgnoreCase(responseCode)) {
                    responseCode = "N - Không xác thực được chủ thẻ";
                } else if ("P-Parse Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "P - Lỗi cú pháp";
                } else if ("R-Not processed".equalsIgnoreCase(responseCode)) {
                    responseCode = "R - Chưa xử lý";
                } else if ("S-Security Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "S - Lỗi bảo mật";
                } else if ("T-ACS Timeout".equalsIgnoreCase(responseCode)) {
                    responseCode = "T - Quá hạn ACS";
                } else if ("U-Undertermined".equalsIgnoreCase(responseCode)) {
                    responseCode = "U - CSC không chính xác";
                } else if ("V-AVS CSC Failed".equalsIgnoreCase(responseCode)) {
                    responseCode = "V - AVS CSC thất bại";
                } else if ("?-Unknown Status".equalsIgnoreCase(responseCode)) {
                    responseCode = "? - Trạng thái không xác định";
                } else if ("W-Undetermined".equalsIgnoreCase(responseCode)) {
                    responseCode = "W - Không xác định";
                } else if ("Y-Cardholder Verified".equalsIgnoreCase(responseCode)) {
                    responseCode = "Y - Đã xác minh chủ thẻ";
                } else if ("M-Verification Attemped".equalsIgnoreCase(responseCode)) {
                    responseCode = "M - Thẻ/NHPH không tham gia 3D Secure";
                } else if ("OP-Not Process".equalsIgnoreCase(responseCode)) {
                    responseCode = "OP -Chưa xử lý ";
                } else if ("".equalsIgnoreCase(responseCode)) {
                    responseCode = "";
                } else if ("Blank".equalsIgnoreCase(responseCode)) {
                    responseCode = "Không phản hồi";
                } else if ("Blank2".equalsIgnoreCase(responseCode)) {
                    responseCode = "";
                }
            }
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);

            //trans state
            String transState = rs.getString("S_ADVANCE_STATUS");
            if ("Waiting for OnePAY's Approval".equalsIgnoreCase(transState) || "Waiting for onepays approval".equalsIgnoreCase(transState)) {
                transState = "Waiting for OnePay's Approval";
            } else  if ("Waiting for approval".equalsIgnoreCase(transState) || "Wait for approval".equalsIgnoreCase(transState)) {
                transState = "Waiting for Approval";
            }

            //trans state
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("Successful".equalsIgnoreCase(transState)) {
                    transState = "Thành công";
                } else if ("Failed".equalsIgnoreCase(transState)) {
                    transState = "Không thành công";
                } else if ("Waiting for authentication".equalsIgnoreCase(transState)) {
                    transState = "Chờ xác thực";
                } else if ("Processing".equalsIgnoreCase(transState)) {
                    transState = "Đang xử lý";
                } else if ("Incomplete".equalsIgnoreCase(transState)) {
                    transState = "Chưa hoàn thiện";
                } else if ("Waiting for Approval".equalsIgnoreCase(transState)) {
                    transState = "Đang chờ duyệt";
                } else if ("Waiting for OnePay's Approval".equalsIgnoreCase(transState)) {
                    transState = "GD chờ OnePay duyệt";
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, transState);

            // Authorization Code
            item.put(TemplateUtils.AUTH_CODE_COLUMN, rs.getString("S_AUTHORISATIONCODE"));

            // Batch Number
            item.put(TemplateUtils.BATCH_NUMBER_COLUMN, rs.getString("S_BATCHNUMBER"));

            // INSTALLMENT
            String installmentState = rs.getString("S_INSTALLMENT_STATE");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("approved".equalsIgnoreCase(installmentState)) {
                    installmentState = "Đã duyệt";
                } else if ("failed".equalsIgnoreCase(installmentState)) {
                    installmentState = "Không thành công";
                } else if ("void".equalsIgnoreCase(installmentState)) {
                    installmentState = "Hủy";
                } else if ("created".equalsIgnoreCase(installmentState)) {
                    installmentState = "Chờ duyệt";
                } 
            }
            item.put("S_INSTALLMENT_STATE", installmentState);
            item.put("S_INSTALLMENT_BANK", rs.getString("S_INSTALLMENT_BANK"));
            item.put("S_BANK_ID", rs.getString("S_BANK_ID"));

            String riskAssessment = rs.getString("S_FRAUD_INFO_FOR_MER_ALERT");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                item.put("S_RISK_ASSESSMENT", riskAssessment != null && rs.getString("S_TRANSACTIONTYPE").equals("Purchase") ? "Yêu cầu đánh giá" : "");
            } else {
                item.put("S_RISK_ASSESSMENT", riskAssessment != null && rs.getString("S_TRANSACTIONTYPE").equals("Purchase") ? "Review Required" : "");
            }

            // NOTE // DESCRIPTION
            String desc = "";
            if(transType.equals("Refund Dispute")){
                if(rs.getString("S_DISPUTE_REASON") == null){
                    desc = "";
                } else if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                    desc = disputeReasonVi.get(rs.getString("S_DISPUTE_REASON")) == null ? "" : disputeReasonVi.get(rs.getString("S_DISPUTE_REASON"));
                } else{
                    desc = disputeReasonEn.get(rs.getString("S_DISPUTE_REASON")) == null ? "" : disputeReasonEn.get(rs.getString("S_DISPUTE_REASON"));
                }
                
            } else{
                desc = getNoteData(rs.getString("S_DATA"));
            }
            item.put("S_NOTE", desc);

            // put into list
            listData.add(item);
        }

    }

    /**
     * Convert FromDate,ToDate Str(YYYY-MM-dd HH:mm:ss )--> Str(dd/MM/yyyy hh:mm)
     */
    private String convertStrDate(String strDate) {
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = df.parse(strDate);

            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm");
            strDate = sdf.format(date);
        } catch (Exception e) {

        }
        return strDate;
    }

    // get Note Data from a String JsonObject
    private static String getNoteData(String jsonObjectString) {
        String note = null;
        JsonObject jsonObject = null;
        if (jsonObjectString != null && !jsonObjectString.equals("")) {
            try {
                jsonObject = new JsonObject(jsonObjectString);
            } catch (JsonException err) {
                Logger.getLogger("Error parse: ", err.toString());
            }

            try {
                note = jsonObject.getString("note");
            } catch (JsonException err) {
                Logger.getLogger("Error get note data : ", err.toString());
            }
        }

        return note;
    }

    private static final Logger LOGGER = Logger.getLogger(FinancialTransactionBuildImpl.class.getName());
}
