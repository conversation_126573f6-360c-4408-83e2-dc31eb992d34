package com.onepay.ma.service.queue.listener.excelBuilder.ext.report;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.models.template.TemplateUtils;
import com.onepay.ma.service.queue.listener.excelBuilder.ext.ReportDataBuild;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.SortUtils;
import com.onepay.ma.service.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by anhkh on 06-Jun-17.
 */
@Service("generalReport")
public class GeneralReportBuildImpl implements ReportDataBuild<GeneralReportQuery> {

    @Override
    public List<Map> generate(Connection connOnline, Connection readOnly, Connection connBackup, Message<GeneralReportQuery> messageData) {
        List<Map> listMap = new ArrayList<>();
        GeneralReportQuery param = messageData.getRequestBody();
        ResultSet rsOnline = null;
        ResultSet rsReadonly = null;
        ResultSet rsBackup = null;
        ResultSet rsNDRR = null;
        try {

            rsBackup = generalReportService.downloadBackp(connBackup, param);

            this.generateRs(rsBackup, listMap, param.getLang());

            Comparator<Map> comparatorState = (o1, o2) -> o1.get(TemplateUtils.STATUS_COLUMN).toString().compareTo(o2.get(TemplateUtils.STATUS_COLUMN).toString());
            Comparator<Map> comparatorDate = ((o1, o2) -> {
                Date d1 = (Date) o1.get(TemplateUtils.DATE_COLUMN);
                Date d2 = (Date) o2.get(TemplateUtils.DATE_COLUMN);
                return d1.compareTo(d2);
            });
            Collections.sort(listMap, comparatorState
                    .thenComparing((o1, o2) -> SortUtils.compareInputType(o1.get("S_INPUT_TYPE").toString(), o2.get("S_INPUT_TYPE").toString()))
                    .thenComparing(comparatorDate.reversed()));

            rsNDRR = generalReportService.downloadDomesticRequestRefundData(connOnline, param);
            this.generateRs(rsNDRR, listMap, param.getLang());

            rsOnline = generalReportService.downloadOnline(connOnline, param);
            this.generateRs(rsOnline, listMap, param.getLang());

            // rsReadonly = generalReportService.downloadReadonly(readOnly, param, listMap.size());
            // this.generateRs(rsReadonly, listMap);

            int index = 1;
            for (Map data : listMap) {
                data.put(TemplateUtils.ROW_NUMBER_COLUMN, index++);
            }

        } catch (Exception ex) {

            LOGGER.log(Level.SEVERE, "[REPORT INTERNATIONAL REFUND] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {

            if (rsOnline != null) {
                try {
                    rsOnline.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsReadonly != null) {
                try {
                    rsReadonly.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsBackup != null) {
                try {
                    rsBackup.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }

            if (rsNDRR != null) {
                try {
                    rsNDRR.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error Close Connection File", e);
                }
            }
        }

        return listMap;
    }


    private void generateRs(ResultSet rs, List<Map> listData, String lang) throws Exception {
        while (rs.next()) {
            Map item = new HashMap();
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, rs.getString("S_MERCHANT_ID"));

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, rs.getString("N_ORIGINAL_ID"));

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, rs.getString("S_ORDER_INFO"));


            // Order Info
            item.put("S_INPUT_TYPE", rs.getString("S_INPUT_TYPE"));
            // BIN_COUNTRY
            item.put("BIN_COUNTRY", rs.getString("BIN_COUNTRY"));

            // trans ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, rs.getString("S_MERCHANT_TRANS_REF"));

            // S_CARD_TYPE
            if (Objects.equals(rs.getString("PAYMENT_CHANNEL"), "QR")) {
                if (Objects.equals(rs.getString("S_APP_NAME"), "QR"))
                    item.put(TemplateUtils.CARD_TYPE_COLUMN, "Mobile Banking / E-Wallet");
                else {
                    item.put(TemplateUtils.CARD_TYPE_COLUMN, rs.getString("S_APP_NAME"));
                }
            } else
                item.put(TemplateUtils.CARD_TYPE_COLUMN, convertBank(rs.getString("S_CARD_TYPE"), rs.getString("S_INPUT_TYPE")));

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, rs.getString("S_CARD_NUMBER"));

            // S_RESPONSE_CODE
            String responseCode = rs.getString("S_RESPONSE_CODE");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                // international response code
                if ("0 -  Approved".equalsIgnoreCase(responseCode)) {
                    responseCode = "0 - Phê duyệt";
                } else if ("1 - Unspecified Failure".equalsIgnoreCase(responseCode)) {
                    responseCode = "1 - Thất bại không xác định";
                } else if ("2 - Declined".equalsIgnoreCase(responseCode)) {
                    responseCode = "2 - Bị từ chối";
                } else if ("3 - Time Out".equalsIgnoreCase(responseCode)) {
                    responseCode = "3 - GD quá hạn";
                } else if ("4 - Expired Card".equalsIgnoreCase(responseCode)) {
                    responseCode = "4 - Thẻ hết hạn";
                } else if ("5 - Insufficient Funds".equalsIgnoreCase(responseCode)) {
                    responseCode = "5 - Không đủ số dư";
                } else if ("6 - Bank Errors".equalsIgnoreCase(responseCode)) {
                    responseCode = "6 - Lỗi ngân hàng";
                } else if ("7 - System Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "7 - Lỗi hệ thống";
                } else if ("8 - Not Supported".equalsIgnoreCase(responseCode)) {
                    responseCode = "8 - Không hỗ trợ";
                } else if ("9 - Bank Declined".equalsIgnoreCase(responseCode)) {
                    responseCode = "9 - Ngân hàng từ chối";
                } else if ("A - Authentication Failed".equalsIgnoreCase(responseCode)) {
                    responseCode = "A - Xác thực không thành công";
                } else if ("B - Transaction Blocked".equalsIgnoreCase(responseCode)) {
                    responseCode = "B - Giao dịch bị chặn";
                } else if ("C - Transaction Cancelled".equalsIgnoreCase(responseCode)) {
                    responseCode = "C - Giao dịch bị hủy";
                } else if ("D - Awaiting Processing".equalsIgnoreCase(responseCode)) {
                    responseCode = "D - Đang chờ xử lý";
                } else if ("E - Cardholder Not Enrolled".equalsIgnoreCase(responseCode)) {
                    responseCode = "E - Referred";
                } else if ("F - 3D Secure Failure".equalsIgnoreCase(responseCode)) {
                    responseCode = "F - Lỗi bảo mật 3D";
                } else if ("I - Internal Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "I - Lỗi nội bộ";
                } else if ("L - Transaction Locked".equalsIgnoreCase(responseCode)) {
                    responseCode = "L - Giao dịch bị khóa";
                } else if ("N - Cardholder".equalsIgnoreCase(responseCode)) {
                    responseCode = "N - Không xác thực được chủ thẻ";
                } else if ("P - Parse Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "P - Lỗi cú pháp";
                } else if ("R - Not processed".equalsIgnoreCase(responseCode)) {
                    responseCode = "R - Chưa xử lý";
                } else if ("S - Security Error".equalsIgnoreCase(responseCode)) {
                    responseCode = "S - Lỗi bảo mật";
                } else if ("T - ACS Timeout".equalsIgnoreCase(responseCode)) {
                    responseCode = "T - Quá hạn ACS";
                } else if ("U - Undertermined".equalsIgnoreCase(responseCode)) {
                    responseCode = "U - CSC không chính xác";
                } else if ("V - AVS CSC Failed".equalsIgnoreCase(responseCode)) {
                    responseCode = "V - AVS CSC thất bại";
                } else if ("? - Unknown Status".equalsIgnoreCase(responseCode)) {
                    responseCode = "? - Trạng thái không xác định";
                } else if ("W - Undetermined".equalsIgnoreCase(responseCode)) {
                    responseCode = "W - Không xác định";
                } else if ("Y - Cardholder Verified".equalsIgnoreCase(responseCode)) {
                    responseCode = "Y - Đã xác minh chủ thẻ";
                } else if ("M - Verification Attemped".equalsIgnoreCase(responseCode)) {
                    responseCode = "M - Thẻ/NHPH không tham gia 3D Secure";
                } else if ("OP - Not Process".equalsIgnoreCase(responseCode)) {
                    responseCode = "OP -Chưa xử lý ";
                } else if ("".equalsIgnoreCase(responseCode)) {
                    responseCode = "";
                } else if ("Blank".equalsIgnoreCase(responseCode)) {
                    responseCode = "Không phản hồi";
                } else if ("Blank2".equalsIgnoreCase(responseCode)) {
                    responseCode = "";
                }
                // domestic response code
                else if ("0 -  Approved".equalsIgnoreCase(responseCode)) {
                    responseCode = "0 - Giao dịch thành công";
                } else if ("1 - Declined".equalsIgnoreCase(responseCode)) {
                    responseCode = "1 - Ngân hàng từ chối giao dịch";
                } else if ("5 - Invalid amount".equalsIgnoreCase(responseCode)) {
                    responseCode = "5 - Số tiền không hợp lệ";
                } else if ("7 - Unspecified Failure".equalsIgnoreCase(responseCode)) {
                    responseCode = "7 - Lỗi không xác định";
                } else if ("8 - Invalid Card No".equalsIgnoreCase(responseCode)) {
                    responseCode = "8 - Số thẻ không đúng";
                } else if ("9 - Invalid Card name".equalsIgnoreCase(responseCode)) {
                    responseCode = "9 - Tên chủ thẻ không đúng";
                } else if ("10 - Expired Card".equalsIgnoreCase(responseCode)) {
                    responseCode = "10 - Thẻ hết hạn/Thẻ bị khóa";
                } else if ("11 - Not Registered".equalsIgnoreCase(responseCode)) {
                    responseCode = "11 - Thẻ chưa đăng ký sử dụng dịch vụ";
                } else if ("12 - Invalid Card Date".equalsIgnoreCase(responseCode)) {
                    responseCode = "12 - Ngày phát hành/Hết hạn không đúng";
                } else if ("13 - Exceeded credit limit".equalsIgnoreCase(responseCode)) {
                    responseCode = "13 - Vượt quá hạn mức thanh toán";
                } else if ("21 - Insufficient fund".equalsIgnoreCase(responseCode)) {
                    responseCode = "21 - Số tiền không đủ để thanh toán";
                } else if ("22 - Invalid Account".equalsIgnoreCase(responseCode)) {
                    responseCode = "22 - Thông tin tài khoản không đúng";
                } else if ("23 - Account Lock".equalsIgnoreCase(responseCode)) {
                    responseCode = "23 - Tài khoản bị khóa";
                } else if ("24 - Invalid Card Info".equalsIgnoreCase(responseCode)) {
                    responseCode = "24 - Thông tin thẻ không đúng";
                } else if ("25 - Invalid OTP".equalsIgnoreCase(responseCode)) {
                    responseCode = "25 - OTP không đúng";
                } else if ("235 - Transaction Timeout".equalsIgnoreCase(responseCode)) {
                    responseCode = "235 - Quá thời gian thanh toán";
                } else if ("99 - User Cancel".equalsIgnoreCase(responseCode)) {
                    responseCode = "99 - Người sử dụng hủy giao dịch";
                } else if ("Blank".equalsIgnoreCase(responseCode)) {
                    responseCode = "";
                } else if ("Authentication failed".equalsIgnoreCase(responseCode)) {
                    responseCode = "Xác thực không thành công";
                } else if ("Authentication cancelled".equalsIgnoreCase(responseCode)) {
                    responseCode = "Hủy xác thực";
                } else if ("Not process".equalsIgnoreCase(responseCode)) {
                    responseCode = "Chưa được xử lý";
                } else if ("No Response from IB payment".equalsIgnoreCase(responseCode)) {
                    responseCode = "Không có phản hồi từ IB Ngân hàng";
                } else if ("No Select Bank".equalsIgnoreCase(responseCode)) {
                    responseCode = "Không chọn ngân hàng";
                } else if ("Not Response".equalsIgnoreCase(responseCode) || "No Response".equalsIgnoreCase(responseCode)) {
                    responseCode = "Không phản hồi";
                }
            }
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseCode);
            // trans type
            String transType = rs.getString("S_TRANSACTION_TYPE");
                if ("PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Purchase";
                } else if ("REFUND".equalsIgnoreCase(transType)) {
                    transType = "Refund";
                } else if ("REQUEST REFUND".equalsIgnoreCase(transType)) {
                    transType = "Request refund";
                } else if ("VOID_PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Void Purchase";
                } else if ("AUTHORIZE".equalsIgnoreCase(transType)) {
                    transType = "Authorize";
                } else if ("CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Capture";
                } else if ("REQUEST CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Request Capture";
                } else if ("REFUND CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Refund Capture";
                }

            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Thanh toán";
                } else if ("REFUND".equalsIgnoreCase(transType)) {
                    transType = "Hoàn tiền";
                } else if ("REQUEST REFUND".equalsIgnoreCase(transType)) {
                    transType = "Yêu cầu hoàn trả";
                } else if ("VOID PURCHASE".equalsIgnoreCase(transType)) {
                    transType = "Hủy-Thanh toán";
                } else if ("AUTHORIZE".equalsIgnoreCase(transType)) {
                    transType = "Cấp phép";
                } else if ("CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Quyết toán";
                } else if ("REQUEST CAPTURE".equalsIgnoreCase(transType)) {
                    transType = "Hoàn trả quyết toán";
                }
            }
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, transType);
            // trans state
            String transState = rs.getString("S_TRANSACTION_STATE");
            if ("Waiting for OnePAY's Approval".equalsIgnoreCase(transState) || transState.equalsIgnoreCase("Waiting for onepays approval") || transState.equalsIgnoreCase("300") || transState.equalsIgnoreCase("210")) {
                transState = "Waiting for OnePay's Approval";
            } else if (transState.equalsIgnoreCase("Waiting for approval") || "401".equalsIgnoreCase(transState)) {
                transState = "Waiting for Approval";
            }
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("Successful".equalsIgnoreCase(transState)) {
                    transState = "Thành công";
                } else if ("Failed".equalsIgnoreCase(transState)) {
                    transState = "Không thành công";
                } else if ("Waiting for authentication".equalsIgnoreCase(transState)) {
                    transState = "Chờ xác thực";
                } else if ("Processing".equalsIgnoreCase(transState)) {
                    transState = "Đang xử lý";
                } else if ("Incomplete".equalsIgnoreCase(transState)) {
                    transState = "Chưa hoàn thiện";
                } else if ("Waiting for Approval".equalsIgnoreCase(transState)) {
                    transState = "Đang chờ duyệt";
                } else if ("Waiting for OnePAY's Approval".equalsIgnoreCase(transState) || "Waiting for OnePay's Approval".equalsIgnoreCase(transState)) {
                    transState = "GD chờ OnePay duyệt";
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, transState);

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, rs.getString("S_CURRENCY"));

            // purchase amount
            item.put(TemplateUtils.AMOUNT_COLUMN, Utils.formatCurrencyAmount(rs.getString("S_CURRENCY"),rs.getDouble("N_AMOUNT")));

            // purchase date
            item.put(TemplateUtils.DATE_COLUMN, rs.getDate("D_TRANSACTION_DATE"));

            String paychannel = rs.getString("PAYMENT_CHANNEL");
            if (null != lang && !lang.isEmpty() && "vi".equalsIgnoreCase(lang)) {
                if ("INT".equalsIgnoreCase(paychannel)) {
                    paychannel = "Quốc tế";
                } else if ("DOM".equalsIgnoreCase(paychannel)) {
                    paychannel = "Nội địa";
                }
            }
            item.put("PAYMENT_CHANNEL", paychannel);
            // put into list

            listData.add(item);

        }

    }

    private static String convertBank(String inputData, String type) {
        String outputData = "";
        if (!type.equals("ND")) {
            outputData = inputData;
        } else {
            if (inputData == null) {
                return "";
            }
            if (inputData.equals("21")) {
                outputData = "CUP";
            } else if (inputData.equals("1") || inputData.equals("32") || inputData.equals("47")) {
                outputData = "Vietcombank";
            } else if (inputData.equals("19") || inputData.equals("59")) {
                outputData = "BIDV";
            } else if (inputData.equals("6") || inputData.equals("57")) {
                outputData = "Vikki Bank";
            } else if (inputData.equals("2") || inputData.equals("67")) {
                outputData = "Techcombank";
            } else if (inputData.equals("7") || inputData.equals("51")) {
                outputData = "HDBank";
            } else if (inputData.equals("3") || inputData.equals("61")) {
                outputData = "TienPhongBank";
            } else if (inputData.equals("8") || inputData.equals("49")) {
                outputData = "MBBank";
            } else if (inputData.equals("5") || inputData.equals("48")) {
                outputData = "VIB";
            } else if (inputData.equals("9") || inputData.equals("54")) {
                outputData = "VietABank";
            } else if (inputData.equals("10") || inputData.equals("53")) {
                outputData = "MSB";
            } else if (inputData.equals("11") || inputData.equals("68")) {
                outputData = "EximBank";
            } else if (inputData.equals("12") || inputData.equals("60")) {
                outputData = "SHB";
            } else if (inputData.equals("14") || inputData.equals("70")) {
                outputData = "VPB";
            } else if (inputData.equals("15") || inputData.equals("58")) {
                outputData = "ABBank";
            } else if (inputData.equals("16") || inputData.equals("69")) {
                outputData = "SacomBank";
            } else if (inputData.equals("17") || inputData.equals("65")) {
                outputData = "NamABank";
            } else if (inputData.equals("18") || inputData.equals("55")) {
                outputData = "MBV";
            } else if (inputData.equals("20") || inputData.equals("64")) {
                outputData = "SeaBank";
            } else if (inputData.equals("22") || inputData.equals("26") || inputData.equals("56")) {
                outputData = "BAC A BANK";
            } else if (inputData.equals("23") || inputData.equals("52")) {
                outputData = "NCB";
            } else if (inputData.equals("24") || inputData.equals("62")) {
                outputData = "Agribank";
            } else if (inputData.equals("25") || inputData.equals("63")) {
                outputData = "SCB";
            } else if (inputData.equals("27") || inputData.equals("66")) {
                outputData = "PVCOMBANK";
            } else if (inputData.equals("30") || inputData.equals("71")) {
                outputData = "Viet Capital Bank";
            } else if (inputData.equals("31")) {
                outputData = "VIETTELPAY";
            } else if (inputData.equals("33")) {
                outputData = "ACB";
            } else if (inputData.equals("34")) {
                outputData = "GPBank";
            } else if (inputData.equals("35")) {
                outputData = "OCB";
            } else if (inputData.equals("36")) {
                outputData = "LPBank";
            } else if (inputData.equals("37")) {
                outputData = "BAOVIET Bank";
            } else if (inputData.equals("38")) {
                outputData = "Kienlongbank";
            } else if (inputData.equals("39")) {
                outputData = "VRB";
            } else if (inputData.equals("40")) {
                outputData = "Public Bank";
            } else if (inputData.equals("41")) {
                outputData = "SAIGONBANK";
            } else if (inputData.equals("42")) {
                outputData = "PGBank";
            } else if (inputData.equals("43")) {
                outputData = "Indovina Bank (IVB)";
            } else if (inputData.equals("44")) {
                outputData = "Wooribank";
            } else if (inputData.equals("45")) {
                outputData = "UOB";
            } else if (inputData.equals("46")) {
                outputData = "Shinhan Bank";
            } else if (inputData.equals("4") || inputData.equals("50")) {
                outputData = "VietinBank";
            } else if (inputData.equals("72")) {
                outputData = "Vietbank";
            } else if (inputData.equals("73")) {
                outputData = "VietCredit";
            } else if (inputData.equals("74")) {
                outputData = "MAFC";
            } else if (inputData.equals("75")) {
                outputData = "Keb Hana";
            }
        }
        return outputData;
    }


    @Autowired
    private GeneralReportService generalReportService;

    private static final Logger LOGGER = Logger.getLogger(GeneralReportBuildImpl.class.getName());
}
