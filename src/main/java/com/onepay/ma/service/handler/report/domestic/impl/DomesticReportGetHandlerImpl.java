package com.onepay.ma.service.handler.report.domestic.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.report.domestic.DomesticReportGetHandler;
import com.onepay.ma.service.models.DomesticReport;
import com.onepay.ma.service.models.DomesticReportConvert;
import com.onepay.ma.service.models.DomesticReportParameter;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.DomesticReportService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class DomesticReportGetHandlerImpl implements DomesticReportGetHandler {

    public DomesticReportGetHandlerImpl(DomesticReportService domesticReportService, MerchantService merchantService) {
        this.domesticReportService = domesticReportService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[DOMESTIC REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {

            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        // int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);

        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ?   1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
//         if(interval == 1){
//             if (months > 1) {
//                 throw  IErrors.SEARCH_TOO_LARGE_ERROR;
// //                throw new InvalidValueException("This search you have requested is too large for daily interval.  Please select information or narrow date range to within 1 month and retry.");
//             }
//         }else if(interval == 2){
//             if (months > 3) {
// //                throw new InvalidValueException("This search you have requested is too large for weekly interval.  Please select information or narrow date range to within 3 months and retry.");
//                 throw  IErrors.SEARCH_TOO_LARGE_ERROR;
//             }
//         }else {
//             if (months > 12) {
//                 throw  IErrors.SEARCH_TOO_LARGE_ERROR;
// //                throw new InvalidValueException("This search you have requested is too large .  Please select information or narrow date range to within 1 year and retry.");
//             }
//         }
        int checkFromDateDiffToDate = FunctionUtil.compareDateByMonth(oFromDate, oToDate, 6);
        int checkFromdateTooOld = FunctionUtil.compareDateByMonth(oFromDate, new Date(), 24);

        // Validate fromdate, todate too long
        if (checkFromDateDiffToDate <= 0) {
            throw  IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        
        // Validate fromdate after 2 years ago
        if (checkFromdateTooOld <= 0) {
            throw IErrors.SEARCH_TOO_OLD_ERROR;
        }
        String bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID)).replace("|",",");

        String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

        String reportType = rc.request().getParam(ParamsPool.REPORT_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REPORT_TYPE));

        String version=rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));

        DomesticReportParameter domesticReportParameter = new DomesticReportParameter();
        domesticReportParameter.setAcquirerId(bankId);
        domesticReportParameter.setFromDate(fromDate);
        domesticReportParameter.setToDate(toDate);
        domesticReportParameter.setInterval(interval);
        domesticReportParameter.setKeywords(keywords);
        domesticReportParameter.setVersion(version);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                            //get online connection
                            return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                    .flatMap(connOnline -> {
                                        //get back up connection
                                        return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                .flatMap(connBackUp -> {

                                                    return merchantService.list(connOnline, connBackUp, merchantId, userId, "domestic").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        domesticReportParameter.setMerchantId(String.join(",", merchantIdList));
                                                        if (!reportType.equals("detail")) {
                                                                return domesticReportService.list(connReadOnly, domesticReportParameter).map(domesticReports -> {
                                                                    Map returnReport = new HashMap();

                                                                    List<DomesticReportConvert> domesticReportConverts = convertReport(domesticReports);
                                                                    //if (domesticReportConverts.size() > 0) {
                                                                    int totalTransCount = 0;
                                                                    int totalRefundCount = 0;
                                                                    double totalTransAmount = 0;
                                                                    double totalRefundAmount = 0;
                                                                    for (DomesticReportConvert items : domesticReportConverts) {
                                                                        totalTransCount += items.getTransaction_count();
                                                                        totalRefundCount += items.getRefund_count();
                                                                        totalTransAmount += items.getTransaction_total();
                                                                        totalRefundAmount += items.getRefund_total();
                                                                    }
                                                                    returnReport.put("reports", convertReport(domesticReports));
                                                                    returnReport.put(ParamsPool.TOTAL_REFUND_TOTAL, totalRefundAmount);
                                                                    returnReport.put(ParamsPool.TOTAL_TRANSACTION_TOTAL, totalTransAmount);
                                                                    returnReport.put(ParamsPool.TOTAL_REFUND_COUNT, totalRefundCount);
                                                                    returnReport.put(ParamsPool.TOTAL_TRANSACTION_COUNT, totalTransCount);
                                                                    //}
                                                                    return returnReport;
                                                                });
                                                        } else {
                                                            return domesticReportService.listDetail(connReadOnly, domesticReportParameter).map(domesticReports -> {
                                                                Map returnReport = new HashMap();

                                                                List<DomesticReportConvert> domesticReportConverts = convertReport(domesticReports);
                                                                //if (domesticReportConverts.size() > 0) {
                                                                int totalTransCount = 0;
                                                                int totalRefundCount = 0;
                                                                double totalTransAmount = 0;
                                                                double totalRefundAmount = 0;
                                                                for (DomesticReportConvert items : domesticReportConverts) {
                                                                    totalTransCount += items.getTransaction_count();
                                                                    totalRefundCount += items.getRefund_count();
                                                                    totalTransAmount += items.getTransaction_total();
                                                                    totalRefundAmount += items.getRefund_total();
                                                                }
                                                                returnReport.put("reports", convertReport(domesticReports));
                                                                returnReport.put(ParamsPool.TOTAL_REFUND_TOTAL, totalRefundAmount);
                                                                returnReport.put(ParamsPool.TOTAL_TRANSACTION_TOTAL, totalTransAmount);
                                                                returnReport.put(ParamsPool.TOTAL_REFUND_COUNT, totalRefundCount);
                                                                returnReport.put(ParamsPool.TOTAL_TRANSACTION_COUNT, totalTransCount);
                                                                //}
                                                                return returnReport;
                                                            });
                                                        }
                                                    });
                                                });
                                    });
                        }).subscribe(returnReport -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
                        rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

    }

    private List<DomesticReportConvert> convertReport(List<DomesticReport> listReport){
        List<DomesticReportConvert> reportOut = new ArrayList();
        DomesticReportConvert temp = null;
        boolean add = false;

        for (int i = 0; i < listReport.size(); i++) {
            DomesticReport domesticReport = listReport.get(i);

            // ADD DATA TO TRANSACTION DATA
            if ("pay".equals(domesticReport.getCommand().toLowerCase())) {
                add = false;
                for (int j = 0; j < reportOut.size(); j++) {
                    temp = reportOut.get(j);

                    Date resultTranDate = domesticReport.getReport_date();
                    Date  transDate = temp.getTransaction_date();
                    //String transDate = df2.format(txnDate);

                    if (transDate != null && transDate.equals(resultTranDate) &&
                            temp.getAcquirer_name() != null && temp.getAcquirer_name().equals(domesticReport.getAcquirer_name()) &&
                            ((temp.getMerchant_id() != null && temp.getMerchant_id().equals(domesticReport.getMerchant_id())) || temp.getMerchant_id() == null))  {
                       // temp.setTransaction_count(domesticReport.getCount());
                       // temp.setTransaction_total(domesticReport.getTotal());
                        temp.setTransaction_count(temp.getTransaction_count()+domesticReport.getCount());
                        temp.setTransaction_total(temp.getTransaction_total()+domesticReport.getTotal());
                        reportOut.remove(j);
                        reportOut.add(j, temp);
                        add = true;
                    }
                }

                if (!add) {
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(domesticReport.getCount());
                    temp.setTransaction_total(domesticReport.getTotal());
                    temp.setRefund_count(0);
                    temp.setRefund_total(0);
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());

                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
                }
            } else {
                // ADD DATA TO REFUND DATA
                add = false;

                for (int j = 0; j < reportOut.size(); j++) {
                    temp = reportOut.get(j);

                    Date resultTranDate = domesticReport.getReport_date();
                    Date  transDate = temp.getTransaction_date();
                    //String transDate = df2.format(txnDate);

                    if (transDate != null && transDate.equals(resultTranDate) &&
                            temp.getAcquirer_name() != null && temp.getAcquirer_name().equals(domesticReport.getAcquirer_name()) &&
                            ((temp.getMerchant_id() != null && temp.getMerchant_id().equals(domesticReport.getMerchant_id()))|| temp.getMerchant_id() == null)) {
                        // temp.setRefund_count(domesticReport.getCount());
                        // temp.setRefund_total(domesticReport.getTotal());
                        temp.setRefund_count(temp.getRefund_count()+domesticReport.getCount());
                        temp.setRefund_total(temp.getRefund_total()+domesticReport.getTotal());
                        reportOut.remove(j);
                        reportOut.add(j, temp);
                        add = true;
                    }

                }

                if (!add) {
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(0);
                    temp.setTransaction_total(0);
                    temp.setRefund_count(domesticReport.getCount());
                    temp.setRefund_total(domesticReport.getTotal());
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());
                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
                }
            }
            /* tuydv
            // ADD DATA TO TRANSACTION DATA
            if ("pay".equals(domesticReport.getCommand().toLowerCase())) {
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(domesticReport.getCount());
                    temp.setTransaction_total(domesticReport.getTotal());
                    temp.setRefund_count(0);
                    temp.setRefund_total(0);
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());

                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
            } else {
                // ADD DATA TO REFUND DATA
                    temp = new DomesticReportConvert();
                    temp.setTransaction_count(0);
                    temp.setTransaction_total(0);
                    temp.setRefund_count(domesticReport.getCount());
                    temp.setRefund_total(domesticReport.getTotal());
                    temp.setTransaction_date(domesticReport.getReport_date());
                    temp.setAcquirer_name(domesticReport.getAcquirer_name());
                    temp.setAcquirer_id(domesticReport.getAcquirer_id());
                    temp.setMerchant_id(domesticReport.getMerchant_id());
                    reportOut.add(temp);
            }*/
        }
        return  reportOut;
    }

    private DomesticReportService domesticReportService;

    private MerchantService merchantService;

    private static final Logger LOGGER = Logger.getLogger(DomesticReportGetHandler.class.getName());
}
