package com.onepay.ma.service.handler.common;

import com.onepay.ma.service.handler.common.impl.ResponseHandlerImpl;
import com.onepay.ma.service.service.FileService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public interface ResponseHandler extends Handler<RoutingContext> {
    static ResponseHandler create(FileService fileService) {
        return new ResponseHandlerImpl(fileService);
    }
}
