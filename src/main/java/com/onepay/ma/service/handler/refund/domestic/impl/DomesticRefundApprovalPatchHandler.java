package com.onepay.ma.service.handler.refund.domestic.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.DomesticRefund;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.RefundConfig;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.service.impl.MerchantPermitService;
import com.onepay.ma.service.util.DomesticRefundUtil;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 8/12/2016.
 */
@Component
public class DomesticRefundApprovalPatchHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        Utils.execBlocking(rc, () -> {
            String userId = rc.get(ParamsPool.X_USER_ID);
            if (userId == null) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND APPROVAL ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String ipAddress = rc.get(ParamsPool.X_REAL_IP);
            if (ipAddress == null) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND  APPROVAL] => IP EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }

            String approvalId = rc.request().getParam("transactionId");
            final HttpServerRequest request = rc.request();
            // final IService iService = iService();
            JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
            JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
            JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


            if (approvalId != null) {

                String body = rc.getBodyAsString();
                PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
                if (mapRequest == null) {
                    LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND  APPROVAL] => IP EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }


                Observable<RefundApproval> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            // LOGGER.log(Level.INFO, "===================================== GET REFUND APPROVAL INFO
                            // ==================================" + StringPool.NEW_LINE);
                            return this.refundApprovalService.get(connBackUp, Integer.valueOf(approvalId)).map(approval -> {
                                if (approval.getStatus().compareTo(RefundApproval.Status.REQUEST.code) != 0) {
                                    LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND  APPROVAL] => REFUND APPORVAL DONE ALREADY");
                                    throw IErrors.DUPLICATE_REFUND_ERROR;
                                }

                                LOGGER.log(Level.INFO, "REFUND APPROVAL INFO:" + approval.toString());
                                // LOGGER.log(Level.INFO, "===================================== END GET REFUND APPROVAL INFO
                                // ==================================" + StringPool.NEW_LINE);
                                return approval;
                            });
                        });

                if (mapRequest.getPath().equals("/approve")) {

                    obs.subscribe(approval -> {
                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return DomesticTransactionService.get(connOnline, approval.getOriginal_transaction_id().toString()).flatMap(purchase -> {

                                                    return merchantService.getDomesticData(connOnline, approval.getMerchant_id()).flatMap(merchantData -> {
                                                        if (Utils.isBigMerchant(approval.getMerchant_id())) {
                                                            // TODO CALL BIG MERCHANT
                                                            String note = new JsonObject(approval.getData()).getString("note");
                                                            String merPurchaseRef = Utils.isBigMerchant(purchase.getMerchant_id())? purchase.getMsp_merchant_transaction_ref() :purchase.getMerchant_transaction_ref();
                                                            LOGGER.info("isBigMerchant: " + Utils.isBigMerchant(purchase.getMerchant_id()) + " merPurchaseRef: " + merPurchaseRef);
                                                            try {
                                                                Map<String, String> requestRefund = DomesticRefundUtil.createRefundUrl(approval.getMerchant_id(), merchantData.getAccessCode(), merchantData.getHashCode(),
                                                                        approval.getMerchant_transaction_ref(), merPurchaseRef, userId, approval.getAmount().getTotal(), note, ipAddress);

                                                                JsonObject mapVpc = MSPClient.refundPurchaseND(requestRefund);
                                                                String merchantTransRef = mapVpc.getString("vpc_MerchTxnRef");
                                                                Integer status = mapVpc.getString("vpc_TxnResponseCode").equalsIgnoreCase("0") ? 400 : 200;
                                                                if (!mapVpc.containsKey("vpc_TxnResponseCode") || !mapVpc.getString("vpc_TxnResponseCode").equalsIgnoreCase("0")) {
                                                                    LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ] =>  CODE : " + mapVpc.getString("vpc_TxnResponseCode") + " DESCRIPTION : " + mapVpc.getString("vpc_Message"));
                                                                    throw IErrors.REFUND_FAILED;
                                                                }
                                                                Observable<DomesticRefund> obsRefund = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                                        .flatMap(connOnline2 -> {
                                                                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                                    .flatMap(conBackUp2 -> {
                                                                                        return this.domesticRefundService.getByRef(connOnline2, merchantTransRef).flatMap(domesticRefund -> {
                                                                                            return this.refundApprovalService.insertApprove3(conBackUp2, userId, approval.getMerchant_id(),
                                                                                                    String.valueOf(Long.valueOf(domesticRefund.getTransaction_id()).intValue()), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                                                    Integer.valueOf(approvalId), merchantTransRef, status, RefundData.Type.DOMESTIC.getValue(), note)
                                                                                                    .flatMap(approval1 -> {
                                                                                                        if (approval1.getStatus() != 400 && approval1.getStatus() != 300) {
                                                                                                            throw IErrors.REFUND_FAILED;
                                                                                                        }
                                                                                                        String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                                                                                                        try {
                                                                                                            OneSchedClient.synchronize(jobId, "0s");
                                                                                                        } catch (Exception e) {
                                                                                                            LOGGER.log(Level.WARNING, "domestic_synx error " + e.getMessage());
                                                                                                        }
                                                                                                        return userService.get(conBackUp2, domesticRefund.getOperator_id()).map(userData -> {
                                                                                                            if (userData != null) {
                                                                                                                domesticRefund.setOperator_id(userData.getEmail());
                                                                                                            }
                                                                                                            return domesticRefund;
                                                                                                        });
                                                                                                    });
                                                                                        });
                                                                                    });
                                                                        });
                                                                obsRefund.subscribe(domesticRefund -> {
                                                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                                                                    rc.next();
                                                                }, throwable -> {
                                                                    rc.fail(throwable);
                                                                });
                                                            } catch (Exception ex) {
                                                                LOGGER.log(Level.INFO, "===================================== END VOID PURCHASE ==================================");
                                                            }
                                                            return Observable.just(null);
                                                        } else {
                                                            HttpClient httpClient = rc.vertx().createHttpClient();
                                                            String note = new JsonObject(approval.getData()).getString("note");
                                                            Map<String, String> requestRefund = DomesticRefundUtil.createRefundUrl(approval.getMerchant_id(), merchantData.getAccessCode(), merchantData.getHashCode(),
                                                                    approval.getMerchant_transaction_ref(), purchase.getMerchant_transaction_ref(), userId, approval.getAmount().getTotal(), note, ipAddress);

                                                            byte[] encoded = FunctionUtil.encodeHessian(requestRefund);
                                                            Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
                                                            HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

                                                            req.putHeader("Content-Type", "x-application/hessian");

                                                            req.toObservable().subscribe(httpClientResponse -> {
                                                                httpClientResponse.bodyHandler(responseData -> {
                                                                    // String json = responseData.toString("UTF-8");

                                                                    io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                                                                    byte[] bytesData = new byte[bufferDataResp.length()];

                                                                    for (int i = 0; i < bufferDataResp.length(); i++) {
                                                                        byte data = bufferDataResp.getByte(i);
                                                                        bytesData[i] = data;
                                                                    }

                                                                    // RefundRes resp = (RefundRes) FunctionUtil.decodeHessian(bytesData);
                                                                    Map<String, String> resp = (Map<String, String>) FunctionUtil.decodeHessian(bytesData);
                                                                    String merchantTransRef = resp.get("vpc_MerchTxnRef");
                                                                    Integer status = resp.get("vpc_TxnResponseCode").equalsIgnoreCase("0") ? 400 : 200;
                                                                    if (!resp.get("vpc_TxnResponseCode").equalsIgnoreCase("0")) {
                                                                        LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ] =>  CODE : " + resp.get("vpc_TxnResponseCode") + " DESCRIPTION : " + resp.get("vpc_Message"));
                                                                        throw IErrors.REFUND_FAILED;
                                                                    }
                                                                    Observable<DomesticRefund> obsRefund = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                                            .flatMap(connOnline2 -> {

                                                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                                        .flatMap(conBackUp2 -> {
                                                                                            // MERCHANT TRANS REF
                                                                                            return this.domesticRefundService.getByRef(connOnline2, merchantTransRef).flatMap(domesticRefund -> {
                                                                                                // get back up connection
                                                                                                return this.refundApprovalService.insertApprove3(conBackUp2, userId, approval.getMerchant_id(),
                                                                                                        String.valueOf(Long.valueOf(domesticRefund.getTransaction_id()).intValue()), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                                                        Integer.valueOf(approvalId), merchantTransRef, status, RefundData.Type.DOMESTIC.getValue(), note)
                                                                                                        .flatMap(approval1 -> {
                                                                                                            if (approval1.getStatus() != 400 && approval1.getStatus() != 300) {
                                                                                                                throw IErrors.REFUND_FAILED;
                                                                                                            }
                                                                                                            String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                                                                                                            try {
                                                                                                                OneSchedClient.synchronize(jobId, "0s");
                                                                                                            } catch (Exception e) {
                                                                                                                LOGGER.log(Level.WARNING, "domestic_synx error " + e.getMessage());
                                                                                                            }
                                                                                                            return userService.get(conBackUp2, domesticRefund.getOperator_id()).map(userData -> {
                                                                                                                if (userData != null) {
                                                                                                                    domesticRefund.setOperator_id(userData.getEmail());
                                                                                                                }
                                                                                                                return domesticRefund;
                                                                                                            });
                                                                                                        });
                                                                                            });
                                                                                        });
                                                                            });
                                                                    obsRefund.subscribe(domesticRefund -> {
                                                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                                                                        rc.next();
                                                                    }, throwable -> {
                                                                        rc.fail(throwable);
                                                                    });


                                                                });
                                                                httpClientResponse.exceptionHandler(throwable -> {
                                                                    rc.fail(throwable);
                                                                });
                                                            });

                                                            req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
                                                            req.write(buffer);
                                                            req.end();
                                                            return Observable.just(null);
                                                        }
                                                    });

                                                });
                                            });
                                }).subscribe(a -> {

                                }, throwable -> {
                                    // LOGGER.log(Level.INFO, "===================================== END REFUND
                                    // ==================================" + StringPool.NEW_LINE);
                                    rc.fail(throwable);
                                });
                    }, throwable -> {
                        // LOGGER.log(Level.INFO, "===================================== END REFUND
                        // ==================================" + StringPool.NEW_LINE);
                        rc.fail(throwable);
                    });

                } else if (mapRequest.getPath().equals("/reject")) {

                    obs.subscribe(approval -> {

                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                            .flatMap(connOnline -> {
                                                String tranRef = approval.getMerchant_transaction_ref() == null ? "" : approval.getMerchant_transaction_ref();
                                                return this.refundApprovalService.insertReject(connBackUp, userId, approval.getMerchant_id(),
                                                        approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                        approval.getTransaction_id(), RefundData.Type.DOMESTIC.getValue(), tranRef)
                                                        .flatMap(approval1 -> {
                                                            String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                                                            try {
                                                                OneSchedClient.synchronize(jobId, "0s");
                                                            } catch (Exception e) {
                                                                LOGGER.log(Level.WARNING, "domestic_synx error " + e.getMessage());
                                                            }
                                                            return this.domesticRefundService.get(connOnline, connBackUp, approval.getTransaction_id().toString()).flatMap(domesticRefund -> {
                                                                return userService.get(connBackUp, domesticRefund.getOperator_id()).map(userData -> {
                                                                    if (userData != null) {
                                                                        domesticRefund.setOperator_id(userData.getEmail());
                                                                    }
                                                                    return domesticRefund;
                                                                });
                                                            });
                                                        });
                                            });
                                }).subscribe(domesticRefund -> {
                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                                    rc.next();
                                }, throwable -> {
                                    rc.fail(throwable);
                                });
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                }
            }
        });
    }

    //
    // protected IService iService() {
    // try {
    // return (IService) new HessianProxyFactory().create(IService.class, refundConfig.getOneCommUrl());
    // } catch (MalformedURLException e) {
    // throw new RuntimeException(e);
    // }
    // }

    @Autowired
    private MerchantPermitService merchantPermitService;

    @Autowired
    private RefundApprovalService refundApprovalService;

    @Autowired
    private UserService userService;

    @Autowired
    private RefundConfig refundConfig;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private DomesticRefundService domesticRefundService;


    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(DomesticRefundApprovalPatchHandler.class.getName());
}
