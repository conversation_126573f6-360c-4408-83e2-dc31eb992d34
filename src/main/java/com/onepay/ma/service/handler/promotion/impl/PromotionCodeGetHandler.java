package com.onepay.ma.service.handler.promotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionCodeSearchQuery;
import com.onepay.ma.service.service.PromotionCodeService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by anhkh on 04-Jan-17.
 */
@Component
public class PromotionCodeGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String promotionId = request.getParam("id") == null ? StringPool.BLANK : String.valueOf(request.getParam("id"));

        if(promotionId.isEmpty()) {

            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if(months > 6){
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));
            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            int pageSize = request.getParam(ParamsPool.PAGE_SIZE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE_SIZE));
            String customer = request.getParam(ParamsPool.CUSTOMER) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.CUSTOMER));
            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
            String couponCode = request.getParam(ParamsPool.COUPON_CODE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.COUPON_CODE));
            String transRef = request.getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.MERCHANT_TRANSACTION_REF));
            String orderRef = request.getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.ORDER_INFO));
            String smsState = request.getParam(ParamsPool.SMS_STATE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SMS_STATE));
            String couponState = request.getParam(ParamsPool.COUPON_STATE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.COUPON_STATE));
            Integer transactionId = request.getParam(ParamsPool.TRANSACTION_ID) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.TRANSACTION_ID));

            PromotionCodeSearchQuery query = new PromotionCodeSearchQuery();
            query.setPageSize(pageSize);
            query.setPage(page);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setKeywords(keywords);
            query.setCustomer(customer);
            query.setMerchantId(merchantId);
            query.setCouponState(couponState);
            query.setCouponCode(couponCode);
            query.setTransRef(transRef);
            query.setOrderRef(orderRef);
            query.setSmsState(smsState);
            query.setTransactionId(transactionId);

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f-> f.dispose())
                    .flatMap(connBackup -> {
                        return promotionCodeService.search(connBackup, query);
                    }).subscribe(promotions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, promotions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f-> f.dispose())
                    .flatMap(connBackup -> {
                        return promotionCodeService.get(connBackup, promotionId);
                    }).subscribe(promotions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, promotions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }

    }

    @Autowired
    private PromotionCodeService promotionCodeService;
}
