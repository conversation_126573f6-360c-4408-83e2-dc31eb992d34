package com.onepay.ma.service.handler.report.domestic;

import com.onepay.ma.service.handler.report.domestic.impl.DomesticReportGetHandlerImpl;
import com.onepay.ma.service.service.DomesticReportService;
import com.onepay.ma.service.service.MerchantService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface DomesticReportGetHandler extends Handler<RoutingContext>  {
    static DomesticReportGetHandlerImpl create(DomesticReportService domesticReportService, MerchantService merchantService){
        return new DomesticReportGetHandlerImpl(domesticReportService, merchantService);
    }
}
