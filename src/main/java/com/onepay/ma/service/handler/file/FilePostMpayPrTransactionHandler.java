package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostMpayPrTransactionHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.mpayPromotion.MpayPromotionService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;

/**
 * Created by anhkh on 30-Jan-18.
 */
public interface FilePostMpayPrTransactionHandler extends Handler<RoutingContext> {

    static FilePostMpayPrTransactionHandlerImpl create(MpayPromotionService service, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue
            , UserService userService, MerchantService merchantService) {
        return new FilePostMpayPrTransactionHandlerImpl(service, serverConfig, downloadFastIn, downloadFastOut, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue, userService, merchantService);
    }
}