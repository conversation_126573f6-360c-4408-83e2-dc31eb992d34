package com.onepay.ma.service.handler.merchant.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.merchant.MerchantGetHandler;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/2/16.
 */
public class MerchantGetHandlerImpl implements MerchantGetHandler {

    public MerchantGetHandlerImpl(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String merchantId = request.getParam("merchantId") == null ? StringPool.BLANK : String.valueOf(request.getParam("merchantId"));

        if(merchantId.isEmpty()){
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));
            String type = request.getParam(ParamsPool.TYPE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.TYPE));
            String currencyCode = request.getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.CURRENCY_CODE));
            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return merchantService.listMerchantData(connOnline, keywords, page, type, currencyCode);
                    }).subscribe(merchants -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
        }else{
           throw IErrors.VALIDATION_ERROR;
        }

    }


    private MerchantService merchantService;


}
