package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_out.SummaryFundsTransService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import org.apache.commons.lang.StringUtils;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class SummaryFundsTransHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private SummaryFundsTransService summaryFundsTransService;

    
    @Autowired
    private MerchantService merchantService;

    private static Logger LOGGER = Logger.getLogger(SummaryFundsTransHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                String xUserId = rc.get(ParamsPool.X_USER_ID);
                if (xUserId == null) {
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
                String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
                DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
                String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
                String toDate = rc.request().getParam(ParamsPool.TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);

                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => PARSE DATE ERROR", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if(months > 6){
                    LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => REQUEST TOO LONG");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
                String merchantName = rc.request().getParam(ParamsPool.MERCHANT_NAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_NAME));
                String accountId = rc.request().getParam(ParamsPool.ACCOUNT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACCOUNT_ID));
                String bankSenderSwiftcode = rc.request().getParam(ParamsPool.BANK_SENDER_SWIFTCODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_SENDER_SWIFTCODE));
                String bankReceiptSwiftcode = rc.request().getParam(ParamsPool.BANK_RECEIPT_SWIFTCODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_RECEIPT_SWIFTCODE));
                String timeInterval = rc.request().getParam(ParamsPool.TIME_INTERVAL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TIME_INTERVAL));
                String maMain = rc.request().getParam(ParamsPool.MA_MAIN) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MA_MAIN));
                LOGGER.log(Level.INFO, "userId: {}", xUserId);
                LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
                LOGGER.log(Level.INFO, "maMain: {}", maMain);
                Map<String,String> mIn = new HashMap();
                mIn.put(ParamsPool.FROM_DATE,fromDate);
                mIn.put(ParamsPool.TO_DATE,toDate);
                mIn.put(ParamsPool.ACCOUNT_ID,accountId);
                mIn.put(ParamsPool.MERCHANT_NAME,merchantName);
                mIn.put(ParamsPool.BANK_SENDER_SWIFTCODE,bankSenderSwiftcode);
                mIn.put(ParamsPool.BANK_RECEIPT_SWIFTCODE,bankReceiptSwiftcode);
                mIn.put(ParamsPool.TIME_INTERVAL,timeInterval);
                if (StringUtils.isNotBlank(maMain)) {
                    LOGGER.log(Level.INFO, "maMain v1: ");
                    String finalMerchantsList = MaPermission.getMc(xUserId, xRequestId, partnerId, merchantId);
                    mIn.put(ParamsPool.MERCHANT_ID, finalMerchantsList);          
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return this.summaryFundsTransService.getTotalSummaryFundsTrans(connOnline,mIn).flatMap(total->{
                            return this.summaryFundsTransService.getSummaryFundsTrans(connOnline,mIn).map(datas -> {
                                total.setList(datas);
                                return total;
                            });
                        });
                    }).subscribe(data -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, data);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else  {
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").flatMap(merchants -> {
                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList()); 
                                    mIn.put(ParamsPool.MERCHANT_ID,String.join(",", merchantIdList));                           
                                    return this.summaryFundsTransService.getTotalSummaryFundsTrans(connOnline,mIn).flatMap(total->{
                                        return this.summaryFundsTransService.getSummaryFundsTrans(connOnline,mIn).map(list -> {
                                            total.setList(list);
                                            return total;
                                        });
                                    });
                                });
                            });
                        }).subscribe(data -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, data);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "SummaryFundsTransHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);

    }
}
