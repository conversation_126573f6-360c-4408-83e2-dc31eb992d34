package com.onepay.ma.service.handler.user.impl;

import com.onepay.ma.service.handler.user.UserChangePassV2Handler;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UserChangePassV2HandlerImpl implements UserChangePassV2Handler {
    private static Logger LOGGER = Logger.getLogger(UserChangePassHandlerImpl.class.getName());
    private final String PASSWORD_REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%?&])[A-Za-z\\d@$!%?&]{12,}$";

    public UserChangePassV2HandlerImpl(UserServiceV2 userService) {
        this.userService = userService;
    }

    @Override
    public void handle(RoutingContext rc) {
        LOGGER.info("Change password version 2 - new password polices");
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        String platform = Utils.detectPlatform(request.getHeader("User-Agent"));

        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => USER ID IS EMPTY ");
            rc.fail(IErrors.VALIDATION_ERROR);
            return;
        }

        io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
        if (body == null) {
            LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => BODY PATCH IS EMPTY ");
            rc.fail(IErrors.VALIDATION_ERROR);
            return;
        }

        String passTmp = body.getString(ParamsPool.PASS, "");
        String newPassTmp = body.getString(ParamsPool.NEW_PASS, "");
        String confirmPassTmp = body.getString(ParamsPool.CONFIRM_NEW_PASS, "");

        validatePass(userId, passTmp, newPassTmp, confirmPassTmp, client).subscribe(valid -> {
            String pass = userService.encodePassword(passTmp, 12);
            String newPass = userService.encodePassword(newPassTmp, 12);

            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> userService.changePassV2(sqlConnection, userId, pass, newPass, platform))
                    .subscribe(result -> {
                        if (result != 200) {
                            rc.fail(IErrors.VALIDATION_ERROR);
                            return;
                        }
                        rc.put(ParamsPool.HANDLER_DATA_CODE, result);
                        rc.put("validated", true);
                        rc.next();
                    }, rc::fail);
        }, rc::fail);
    }

    private Observable<Boolean> validatePass(String userId, String pass, String newPass, String confirmNewPass, JDBCClient client) {
        return Observable.defer(() -> {
            if (isNullOrBlank(pass) || isNullOrBlank(newPass) || isNullOrBlank(confirmNewPass)) {
                return Observable.error(IErrors.REQUIRED_FIELDS_MISSING);
            }

            if (Objects.equals(pass, newPass)) {
                return Observable.error(IErrors.INVALID_USER_PASSSAME);
            }

            if (!Objects.equals(newPass, confirmNewPass)) {
                return Observable.error(IErrors.CONFIRM_PASSWORD_NOT_MATCH);
            }
            if (!newPass.matches(PASSWORD_REGEX)) {
                return Observable.error(IErrors.PASSWORD_REGEX_INVALID);
            }

            //validate không được trùng 2 password gần nhất
            return Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(connection -> userService.listRecentPasswords(connection, userId))
                    .flatMap(recentPasswords -> {
                        if (recentPasswords == null || Objects.equals(recentPasswords.size(), 0)) {
                            return Observable.error(IErrors.RESOURCE_NOT_FOUND);
                        }
                        if (recentPasswords.contains(userService.encodePassword(newPass, 12))) {
                            return Observable.error(IErrors.PASSWORD_REUSED);
                        }
                        return Observable.just(true);
                    });
        });
    }


    private boolean isNullOrBlank(String value) {
        return value == null || value.trim().isEmpty();
    }


    private UserServiceV2 userService;
}
