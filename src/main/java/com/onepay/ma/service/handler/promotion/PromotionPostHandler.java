package com.onepay.ma.service.handler.promotion;

import com.onepay.ma.service.handler.promotion.impl.PromotionPostHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionPostHandler extends Handler<RoutingContext> {
    static PromotionPostHandlerImpl create(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, ApprovalService approvalService){
        return new PromotionPostHandlerImpl(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, approvalService);

    }
}
