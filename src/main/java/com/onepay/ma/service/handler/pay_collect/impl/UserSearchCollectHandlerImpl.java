package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_collect.UserConfigService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import org.apache.commons.lang.StringUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class UserSearchCollectHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    private UserConfigService configService;

    private static Logger LOGGER = Logger.getLogger(UserSearchCollectHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                String userId = rc.get(ParamsPool.X_USER_ID);
                String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
                String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
                String user_id = rc.request().getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_ID));
                String merchant_id = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
                String user_name = rc.request().getParam(ParamsPool.USER_NAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_NAME));
                String account = rc.request().getParam(ParamsPool.ACCOUNT) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACCOUNT));
                String bank = rc.request().getParam(ParamsPool.BANK) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK));

                String reference = rc.request().getParam(ParamsPool.REFERENCE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REFERENCE));
                String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
                String page = rc.request().getParam(ParamsPool.PAGE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAGE));
                String page_size = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
                if (StringUtils.isNotBlank(page_size) && Utils.isInteger(page_size)) {
                    if (Integer.parseInt(page_size) > Utils.getPageSize()) {
                        page_size = String.valueOf(Utils.getPageSize());
                    }
                }
                Map<String, String> mIn = new HashMap<>();
                mIn.put(ParamsPool.USER_ID, user_id);
                mIn.put(ParamsPool.USER_NAME, user_name);
                mIn.put(ParamsPool.ACCOUNT, account);
                mIn.put(ParamsPool.BANK, bank);
                mIn.put(ParamsPool.REFERENCE, reference);
                mIn.put(ParamsPool.STATE, state);
                mIn.put(ParamsPool.PAGE, page);
                mIn.put(ParamsPool.PAGE_SIZE, page_size);
                mIn.put(ParamsPool.MERCHANT_ID, merchant_id);
                String maMain = rc.request().getParam(ParamsPool.MA_MAIN) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MA_MAIN));
                LOGGER.log(Level.INFO, "userId: {}", userId);
                LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
                LOGGER.log(Level.INFO, "maMain: {}", maMain);
                if (StringUtils.isNotBlank(maMain)) {
                    LOGGER.log(Level.INFO, "maMain v1: ");
                    String finalMerchantsList = MaPermission.getMc(userId, xRequestId, partnerId, merchant_id);
                    mIn.put(ParamsPool.MERCHANT_ID, finalMerchantsList);
                    Observable<BaseList<UserConfigDto>> obs = Observable
                            .using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                            .flatMap(connPaycollect -> {
                                return this.configService.total(connPaycollect, mIn).flatMap(total -> {
                                    return this.configService.search(connPaycollect, mIn).map(dataSearch -> {
                                        total.setList(dataSearch);
                                        return total;
                                    });
                                });
                            });
                    obs.subscribe(transactions -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    Observable<BaseList<UserConfigDto>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                        .flatMap(connPaycollect -> {
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return merchantService.merchantPaycollects(connPaycollect, connBackUp, merchant_id, userId, "paycollect").flatMap(merchants -> {
                                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                            if (merchantIdList != null && !merchantIdList.isEmpty()) { // user k dc phan quyen all mid
                                              mIn.put(ParamsPool.MERCHANT_ID, String.join(",", merchantIdList));
                                            }
                                            if (merchantIdList.isEmpty()) { // user k dc phan quyen mid nao
                                              mIn.put(ParamsPool.MERCHANT_ID, "partner_id");
                                            }
                                            return this.configService.total(connPaycollect, mIn).flatMap(total -> {
                                                return this.configService.search(connPaycollect, mIn).map(list -> {
                                                    total.setList(list);
                                                    return total;
                                                });
                                            });
                                        });
                                    });
                        });
                    obs.subscribe(transactions -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "UserSearchCollectHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    @Autowired
    private MerchantService merchantService;
}
