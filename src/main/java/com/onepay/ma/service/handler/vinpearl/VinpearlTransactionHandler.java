package com.onepay.ma.service.handler.vinpearl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.onepay.ma.service.handler.vinpearl.impl.VinpearlTransactionRefundHandlerImpl;
import io.vertx.core.Handler;

@Component
public class VinpearlTransactionHandler {

    @Autowired
    private VinpearlTransactionRefundHandlerImpl vinpearlTransactionRefundHandlerImpl;

    public Handler refund() {
        return vinpearlTransactionRefundHandlerImpl;
    }
}
