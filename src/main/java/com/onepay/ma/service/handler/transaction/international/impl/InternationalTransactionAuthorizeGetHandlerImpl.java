package com.onepay.ma.service.handler.transaction.international.impl;

import com.onepay.ma.service.handler.transaction.international.InternationalTransactionGetHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.MidService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/30/16.
 */
public class InternationalTransactionAuthorizeGetHandlerImpl implements InternationalTransactionGetHandler {

    public InternationalTransactionAuthorizeGetHandlerImpl(InternationalTransactionService internationalTransactionService, MerchantService merchantService, MidService midService, ServerConfig serverConfig) {
        this.internationalTransactionService = internationalTransactionService;
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
        this.midService = midService;
    }


    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

            int bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));

            String authenticationState = rc.request().getParam(ParamsPool.AUTHENTICATION_STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHENTICATION_STATE));

            String authenticationType = rc.request().getParam(ParamsPool.AUTHENTICATION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHENTICATION_TYPE));

            String authorizationCode = rc.request().getParam(ParamsPool.AUTHORIZATION_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHORIZATION_CODE));

            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

            String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            String is_show_3d = rc.request().getParam(ParamsPool.IS_SHOW_3D) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.IS_SHOW_3D));

            int transactionIdValue = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? 0 : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID)).isEmpty() ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            String finalCardNumber = FunctionUtil.isBeginIsACard(cardNumber) && cardNumber.contains("*") ? cardNumber.replaceAll("\\*", "0") : cardNumber;
            InternationalTxnParameter internationalTxnParameter = new InternationalTxnParameter();
            internationalTxnParameter.setPage(page);
            internationalTxnParameter.setPageSize(pageSize);
            internationalTxnParameter.setFromDate(fromDate);
            internationalTxnParameter.setToDate(toDate);
            internationalTxnParameter.setCardNumber(cardNumber);
            internationalTxnParameter.setTransactionReference(merchantTransactionRef);
            internationalTxnParameter.setOrderInfo(orderInfo);
            internationalTxnParameter.setTransactionId(transactionIdValue);
            internationalTxnParameter.setAcquirerId(bankId);
            internationalTxnParameter.setTransactionType(transactionType);
            internationalTxnParameter.setStatus(status);
            internationalTxnParameter.setCardType(cardType);
            internationalTxnParameter.setAuthenticationState(authenticationState);
            internationalTxnParameter.setAuthenticationType(authenticationType);
            internationalTxnParameter.setAuthorizationCode(authorizationCode);
            internationalTxnParameter.setCurrency(currency);
            internationalTxnParameter.setKeywords(keywords);
            internationalTxnParameter.setIs_show_3d(is_show_3d);


            // IN CASE COMPARE CARD NO HASH
            if (FunctionUtil.isCardData(finalCardNumber)) {
                rc.vertx().executeBlockingObservable(objectFuture -> {
                    objectFuture.complete(FunctionUtil.oneSMHmac(finalCardNumber, serverConfig));
                }).subscribe(o -> {
                    internationalTxnParameter.setCardNumber(String.valueOf(o));
                    queryTransaction(internationalTxnParameter, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                });
            } else {
                queryTransaction(internationalTxnParameter, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            }


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                .flatMap(connReadOnly -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackup -> {
                                                return internationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                                                    return internationalTransactionService.listHistory(connOnline, connBackup, transactionId).flatMap(histories -> {

                                                    return this.midService.getByMerchantId(connBackup, internationalTransaction.getMerchant_id()).map(mid_no -> {
                                                        double amount = 0;
                                                        for (InternationalTransactionHistory his: histories) {
                                                            if((his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content)
                                                                    && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST.code)==0)
                                                                || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST_ONEPAY.content)
                                                                    && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST_ONEPAY.code)==0)){
                                                                amount += his.getAmount().getTotal();
                                                            }
                                                        }
                                                        internationalTransaction.setWait_for_approval_amount(amount);

                                                        internationalTransaction.setMid_no(mid_no);
                                                        return internationalTransaction;
                                                    });
                                                    });
                                                });
                                            });
                                });
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            }); ;
        }
    }

    private void queryTransaction(InternationalTxnParameter internationalTxnParameter, String merchantId, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable<Transactions<InternationalTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                internationalTxnParameter.setMerchantId(String.join(",", merchantIdList));
                                                return internationalTransactionService.list(connReadOnly, connOnline, internationalTxnParameter);
                                            });

                                        });

                            });

                });
        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private InternationalTransactionService internationalTransactionService;

    private MerchantService merchantService;

    private MidService midService;

    private ServerConfig serverConfig;
}
