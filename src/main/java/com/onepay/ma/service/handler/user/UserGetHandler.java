package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserGetHandlerImpl;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/14/16.
 */
public interface UserGetHandler extends Handler<RoutingContext> {
    static UserGetHandlerImpl create(UserService userService, UserPermissionService userPermissionService){
        return new UserGetHandlerImpl(userService, userPermissionService);
    }
}
