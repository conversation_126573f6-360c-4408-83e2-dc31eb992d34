package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.BatchService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class BatchChangeStatusHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private BatchService batchService;

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                String xUserId = rc.get(ParamsPool.X_USER_ID);
                if (xUserId == null) {
                    throw IErrors.FORBIDDEN;
                }
                String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
                String userId = rc.get(ParamsPool.USER_ID);
                JsonObject bodyAsJson = rc.getBodyAsJson();
                String batchId = null;
                String state = null;
                if (null != bodyAsJson && bodyAsJson.containsKey("id")) {
                    batchId = bodyAsJson.getString("id");
                }
                if (null != bodyAsJson && bodyAsJson.containsKey("state")) {
                    state = bodyAsJson.getString("state");
                }
                if (null == batchId || batchId.isEmpty() || null == state || state.isEmpty()) {
                    throw IErrors.VALIDATION_ERROR;
                }
                String userName = "";
                if (bodyAsJson.containsKey("user_name")) {
                    userName = bodyAsJson.getString("user_name");
                }
                String password = null;
                if (bodyAsJson.containsKey("password")) {
                    password = bodyAsJson.getString("password");
                }
                Map<String, String> mIn = new HashMap();
                mIn.put("batch_id", batchId);
                mIn.put("user_name", userName);
                if (BatchState.REJECTED.getResponse().equalsIgnoreCase(state)) {
                    mIn.put(ParamsPool.STATE, state);
                } else {
                    mIn.put(ParamsPool.STATE, BatchState.WAIT_FOR_TRANSFER.getResponse());
                }
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                            return batchService.updateMsgStateSqlConnect(connOnline, mIn);
                        }).subscribe(partners -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
                    rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Change status batch Error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    private static Logger LOGGER = Logger.getLogger(BatchChangeStatusHandlerImpl.class.getName());

}
