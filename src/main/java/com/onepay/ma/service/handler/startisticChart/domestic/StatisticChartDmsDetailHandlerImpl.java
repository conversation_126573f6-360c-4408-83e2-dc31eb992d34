package com.onepay.ma.service.handler.startisticChart.domestic;

import com.onepay.ma.service.handler.startisticChart.international.StatisticChartIntGetHandlerImpl;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:18 PM
 * To change this ma-service.
 */
@Component
public class StatisticChartDmsDetailHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private StatisticChartService statisticChartService;
    private static Logger LOGGER = Logger.getLogger(StatisticChartIntGetHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
                JsonObject data =rc.getBodyAsJson();
                String datef = data.getString("datef");
                String datet = data.getString("datet");
                JsonArray merchantId = data.getJsonArray("merchantId");
                String sMerchantId = String.join(",",merchantId.getList());
                JsonArray cardType = data.getJsonArray("cardType");
                String sCardType= String.join(",", cardType.getList()).replace("|",",");
                String target = String.join(",",data.getJsonArray("target").getList());
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                        .flatMap(connBackup -> {
                            return this.statisticChartService.listfail(connBackup,sMerchantId,sCardType,datef,datet,"ND",target);
                        }).subscribe(chartdata -> {
                    Map returnValue = new HashMap();
                    returnValue.put("chartStatisticDetail", chartdata);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "StatisticChartInternationalGetDetailHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);

    }
}
