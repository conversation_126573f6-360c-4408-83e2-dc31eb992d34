package com.onepay.ma.service.handler.refund.mpay.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.ibm.icu.text.SimpleDateFormat;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.ManualRefundDescription;
import com.onepay.ma.service.models.ManualRefundParameter;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.models.mpay.MpayRefundReq;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.mpay.impl.MocaMpayTransactionService;
import com.onepay.ma.service.service.mpay.impl.MpayRefundService;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import groovy.json.JsonException;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import rx.subjects.BehaviorSubject;

/**
 * Created by anhkh on 14.10.80.
 */
@Component
public class MpayRefundPatchHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ QR REFUND PATCH ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        if (xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ QR REFUND PATCH ] =>  REQUEST EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        // String xRequestId ="51fsdfsd6B5CBF9A31BDDEFDSFDSFE380D89" ;
        String transactionId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        if (transactionId != null) {
            String body = rc.getBodyAsString();
            PatchRequest mapPatchRequest = gson.fromJson(body, PatchRequest.class);
            if (mapPatchRequest == null) {
                LOGGER.log(Level.SEVERE, "[ QR REFUND PATCH ] => BODY EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            Observable<RefundApproval> obsBackUp = Observable
                    .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        // LOGGER.log(Level.INFO, "===================================== GET REFUND
                        // APPROVAL INFO
                        // ==================================" + StringPool.NEW_LINE);
                        return this.refundApprovalService.getMpay(connBackUp, Integer.valueOf(transactionId))
                                .map(approval -> {
                                    if (approval.getStatus().compareTo(RefundApproval.Status.REQUEST.code) != 0
                                            && approval.getStatus()
                                                    .compareTo(RefundApproval.Status.REQUEST_ONEPAY.code) != 0) {
                                        LOGGER.log(Level.SEVERE,
                                                "[ QR REFUND  APPROVAL] => REFUND APPORVAL DONE ALREADY");
                                        this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                                Integer.parseInt(transactionId), 500,
                                                "This approval has already approved|rejected");
                                        throw new ErrorException(500, "DUPLICATE_REFUND_ERROR",
                                                500 + "|" + "This approval has already approved|rejected", "",
                                                "This approval has already approved|rejected");
                                    }
                                    LOGGER.log(Level.INFO, "REFUND APPROVAL INFO:" + approval.toString());
                                    // LOGGER.log(Level.INFO, "===================================== END GET REFUND
                                    // APPROVAL INFO
                                    // ==================================" + StringPool.NEW_LINE);
                                    return approval;
                                });
                    });
            if (mapPatchRequest.getPath().equals("/approve")) {
                approve(rc, clientBackUp,clientOnline,userId, obsBackUp);
            } else if (mapPatchRequest.getPath().equals("/onepay-approve")) {
                onepayApprove(rc, clientBackUp, clientOnline, userId, obsBackUp);
            } else if (mapPatchRequest.getPath().equals("/reject")) {
                reject(rc, clientBackUp, userId, obsBackUp);
            } else if (mapPatchRequest.getPath().equals("/onepay-reject")) {
                onepayReject(rc, clientBackUp, clientOnline, userId, obsBackUp);
            } else if (mapPatchRequest.getPath().equals("/onepay-manual")) {
                onepayManual(rc, clientBackUp, clientOnline, userId, obsBackUp);
            }

        } else {
            throw IErrors.VALIDATION_ERROR;
        }

    }

    private void approve(RoutingContext rc, JDBCClient clientBackUp,JDBCClient clientOnline, String userId,
            Observable<RefundApproval> obsBackUp) {

        obsBackUp.subscribe(approval -> {

            if (approval.getStatus().equals(RefundApproval.Status.REQUEST.code)) {
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                            String body = rc.getBodyAsString();
                            PatchRequest<RefundData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {
                            }.getType());
                            // Insert new request refund for OnePAY Approval
                            BehaviorSubject<Boolean> isOkSubject = BehaviorSubject.create();
                            this.refundApprovalService.getGetByTransRef(connBackUp, approval.getOriginal_transaction_id())
                                    .flatMap(Observable::just)
                                    .subscribe(listRefundApproval -> Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), SQLConnectionFactory::dispose)
                                            .flatMap(connOnline -> this.checkHistoryRefund(connOnline, listRefundApproval, approval.getOriginal_transaction_id(), mapPatchRequest.getValue().getStringHash()))
                                            .subscribe(isOkSubject::onNext, isOkSubject::onError, isOkSubject::onCompleted));
                            return isOkSubject.asObservable().flatMap(isOk -> {
                                if (isOk) {
                                    LOGGER.log(Level.INFO, "****** INSERT REFUND REQUEST TO ONEPAY *****");
                                    return this.refundApprovalService.insertRequestOnepay3Mpay(connBackUp, userId,
                                            approval.getMerchant_id(), approval.getOriginal_transaction_id(),
                                            approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                            approval.getTransaction_id(), approval.getMerchant_transaction_ref(),
                                            RefundApproval.Status.REQUEST_ONEPAY.code, RefundData.Type.QR.getValue(),
                                            getNoteData(approval.getData())).flatMap(Observable::just);
                                } else {
                                    LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND HISTORY NOT MATCH");
                                    return Observable.error(IErrors.INVALID_AMOUNT);
                                }
                            });
                        })).subscribe(stringObjectMap -> {
                            // call ep dong bo qr
                            try {
                                OneSchedClient.synchronizeQR();
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "qr call ep dong bo error " + e.getMessage());
                            }
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                            rc.next();
                        }, rc::fail);
            } else {
                LOGGER.severe("cannot call refund on MA");
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                approval.getTransaction_id(), 400, "Invalid request")).subscribe();
                rc.fail(IErrors.VALIDATION_ERROR);
            }

        }, rc::fail);
    }

    private void onepayApprove(RoutingContext rc, JDBCClient clientBackUp, JDBCClient clientOnline, String userId, Observable<RefundApproval> obsBackUp) {

        obsBackUp.subscribe(approval -> {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return this.mpayRefundService.getRefundByRef(connOnline, approval.getMerchant_transaction_ref(), approval.getMerchant_id()).flatMap(rf -> {
                                        if (rf != null && !rf.getStatus().equals("pending") && !rf.getStatus().equals("approved")) {
                                            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
                                            JsonObject des = new JsonObject(rf.getNote());
                                            des.put("history", new JsonObject()
                                                    .put("old_client_ref", approval.getMerchant_transaction_ref())
                                                    .put("refund_date", sdf.format(rf.getRefundDate().getTime())));

                                            String reqId = UUID.randomUUID().toString();
                                            String newRef = approval.getMerchant_id() + "_" + reqId;
                                            if (newRef.length() > 32) {
                                                int largerInt = newRef.length() - 32 + 1;
                                                reqId = reqId.substring(0, reqId.length() - largerInt);
                                                newRef = approval.getMerchant_id() + "_" + reqId;
                                            }
                                            return this.mpayRefundService.updateRefundByRef(connOnline, rf.getId(), newRef, des.toString()).flatMap(urf -> {
                                                return callApiMSP(connBackUp, connOnline, approval, userId);
                                            });
                                        } else {
                                            return callApiMSP(connBackUp, connOnline, approval, userId);
                                        }
                                    });
                                });
                    }).subscribe(qrRefund -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, qrRefund);
                        rc.next();
                    }, throwable -> {
                        LOGGER.log(Level.SEVERE, "ERROR ON APPROVAL REFUND QR : ", throwable);
                        rc.fail(throwable);
                    });

        }, throwable -> {
            rc.fail(throwable);
        });
    }

    Observable<RefundApproval> callApiMSP(SQLConnection connBackUp, SQLConnection connOnline, RefundApproval approval, String userId) {

        MpayRefundReq req = new MpayRefundReq();
        req.setAmount(approval.getAmount().getTotal());
        req.setMerchant_id(approval.getMerchant_id());
        req.setPayment_id(approval.getOriginal_transaction_id());
        req.setClient_id("MADM");
        req.setCurrency(approval.getAmount().getCurrency());
        req.setOperator(userId);
        req.setNotes(getNoteData(approval.getData()));
        req.setReference(approval.getMerchant_transaction_ref());
        req.setDirect("y");
        if (null != approval.getBrandId() && "grabpay".equalsIgnoreCase(approval.getBrandId()) && null != approval.getDataPromotion()) {
            JsonObject dataPromption = new JsonObject(approval.getDataPromotion());
            // LOGGER.log(Level.INFO, "QR J_DATA PROMOTION " + dataPromption.toString());
            if (dataPromption.containsKey("promotion")) {
                JsonObject jPromotion = dataPromption.getJsonObject("promotion");
                if (jPromotion.containsKey("result")) {
                    jPromotion.remove("result");
                    JsonObject result = new JsonObject();
                    return MocaMpayTransactionService.get(connOnline, approval.getSTransRefId()).flatMap(dataMocaDB -> {
                        double actualRefundAmount = Math.round(approval.getAmount().getTotal() / (1 + (dataMocaDB.getOfferDiscountAmount() / dataMocaDB.getPaymentAmount())));
                        result.put("refund_amount", actualRefundAmount);
                        jPromotion.put("result", result);
                        req.setPromotion(jPromotion);
                        JsonNode jsonOut = null;
                        try {
                            jsonOut = MSPClient.refund(req);
                        } catch (Exception e) {
                            LOGGER.log(Level.SEVERE, "ERROR CALL REFUND MSP QR : ", e);
                            this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                            throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                        }

                        if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                            this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                            throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                        }
                        return this.refundApprovalService.insertApprove(connBackUp, userId, approval.getMerchant_id(),
                                approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue());
                    });

                } else {
                    this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                }
            } else {
                JsonNode jsonOut = null;
                try {
                    jsonOut = MSPClient.refund(req);
                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "error on refund qr: ", e);
                    this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                }

                if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                    this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                }

                return this.refundApprovalService.insertApprove(connBackUp, userId, approval.getMerchant_id(),
                        approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                        approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue());
            }

        } else {
            JsonNode jsonOut = null;
            try {
                jsonOut = MSPClient.refund(req);
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "error on refund qr: ", e);
                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
            }

            if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
            }

            return this.refundApprovalService.insertApprove(connBackUp, userId, approval.getMerchant_id(),
                    approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                    approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue());
        }

    }

    private void reject(RoutingContext rc, JDBCClient clientBackUp, String userId, Observable<RefundApproval> obsBackUp) {

        obsBackUp.subscribe(approval -> {

            if(approval.getStatus().equals(RefundApproval.Status.REQUEST.code)) {
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            return this.refundApprovalService.insertReject(connBackUp, userId, approval.getMerchant_id(),
                                    approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                    approval.getTransaction_id(), RefundData.Type.QR.getValue(), approval.getMerchant_transaction_ref());

                        }).subscribe(qrRefund -> {
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, qrRefund);
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
            } else {
                LOGGER.severe("cannot call reject on MA");
                rc.fail(IErrors.VALIDATION_ERROR);
            }

        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private void onepayReject(RoutingContext rc, JDBCClient clientBackUp, JDBCClient clientOnline, String userId, Observable<RefundApproval> obsBackUp) {

        obsBackUp.subscribe(approval -> {

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                    ManualRefundParameter parameter = new ManualRefundParameter();
                                    ManualRefundDescription description = new ManualRefundDescription();

                                    description.setOperator(userId);
                                    description.setNote(getNoteData(approval.getData()));
                                    description.setType("rejected");

                                    parameter.setId("");
                                    parameter.setMerchantId(approval.getMerchant_id());
                                    parameter.setClientMerchantId("");
                                    parameter.setServerId("");
                                    parameter.setServerRef("");
                                    parameter.setServerMerchantId("");
                                    parameter.setPaymentId(approval.getOriginal_transaction_id());
                                    parameter.setAmount(approval.getAmount().getTotal());
                                    parameter.setCurrency(approval.getAmount().getCurrency());
                                    parameter.setClientId(PropsUtil.get("onepay_msp_service_client_id", ""));
                                    parameter.setClientRef(approval.getMerchant_transaction_ref());
                                    parameter.setDescription(gson.toJson(description));

                                        // return this.refundApprovalService.insertTblRefund(connOnline, parameter)
                                        //         .flatMap(refundId -> {
                                                    // return this.refundApprovalService.updateRefundQrStatus(connOnline, refundId).flatMap(o -> {
                                                        return this.refundApprovalService.insertReject(connBackUp, userId, approval.getMerchant_id(),
                                                                approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                approval.getTransaction_id(), RefundData.Type.QR.getValue(), approval.getMerchant_transaction_ref());
                                                    // });
                                                // });
                                });
                    }).subscribe(qrRefund -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, qrRefund);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }, throwable -> {
            rc.fail(throwable);
        });
    }


    // get Note Data from a String JsonObject
    private String getNoteData(String jsonObjectString) {
        String note = null;
        JsonObject jsonObject = null;
        if (jsonObjectString != null && !jsonObjectString.equals("")) {
            try {
                jsonObject = new JsonObject(jsonObjectString);
            } catch (JsonException err) {
                Logger.getLogger("Error parse", err.toString());
            }

            try {
                note = jsonObject.getString("note");
            } catch (JsonException err) {
                Logger.getLogger("Error", err.toString());
            }
        }

        return note;
    }

    private void onepayManual(RoutingContext rc, JDBCClient clientBackUp, JDBCClient clientOnline, String userId, Observable<RefundApproval> obsBackUp) {
        Gson gson = new Gson();
        obsBackUp.subscribe(approval -> {

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                    ManualRefundParameter parameter = new ManualRefundParameter();
                                    ManualRefundDescription description = new ManualRefundDescription();

                                    description.setOperator(userId);
                                    description.setNote(getNoteData(approval.getData()));
                                    description.setType("manual");

                                    parameter.setId("");
                                    parameter.setMerchantId("");
                                    parameter.setClientMerchantId("");
                                    parameter.setServerId("");
                                    parameter.setServerRef("");
                                    parameter.setServerMerchantId("");
                                    parameter.setPaymentId(approval.getOriginal_transaction_id());
                                    parameter.setAmount(approval.getAmount().getTotal());
                                    parameter.setCurrency("VND");
                                    parameter.setClientId(PropsUtil.get("onepay_msp_service_client_id", ""));
                                    parameter.setClientRef(approval.getMerchant_transaction_ref());
                                    parameter.setDescription(gson.toJson(description));

                                    String note = getNoteData(approval.getData());

                                    MpayRefundReq req = new MpayRefundReq();
                                    req.setAmount(approval.getAmount().getTotal());
                                    req.setMerchant_id(approval.getMerchant_id());
                                    req.setPayment_id(approval.getOriginal_transaction_id());
                                    req.setClient_id("MADM");
                                    req.setCurrency(approval.getAmount().getCurrency());
                                    req.setOperator(userId);
                                    req.setNotes(getNoteData(approval.getData()));
                                    req.setReference(approval.getMerchant_transaction_ref());
                                    req.setDirect("n");
                                    if (null != approval.getBrandId() && "grabpay".equalsIgnoreCase(approval.getBrandId()) && null != approval.getDataPromotion()) {
                                        JsonObject dataPromption = new JsonObject(approval.getDataPromotion());
                                        // LOGGER.log(Level.INFO, "QR J_DATA PROMOTION MANUAL " + dataPromption.toString());
                                        if (dataPromption.containsKey("promotion")) {
                                            JsonObject jPromotion = dataPromption.getJsonObject("promotion");
                                            if (jPromotion.containsKey("result")) {
                                                jPromotion.remove("result");
                                                JsonObject result = new JsonObject();
                                                return MocaMpayTransactionService.get(connOnline, approval.getSTransRefId()).flatMap(dataMocaDB -> {
                                                    double actualRefundAmount = Math.round(approval.getAmount().getTotal() / (1 + (dataMocaDB.getOfferDiscountAmount() / dataMocaDB.getPaymentAmount())));
                                                    result.put("refund_amount", actualRefundAmount);
                                                    jPromotion.put("result", result);
                                                    req.setPromotion(jPromotion);
                                                    JsonNode jsonOut = null;
                                                    try {
                                                        jsonOut = MSPClient.refund(req);
                                                    } catch (Exception e) {
                                                        LOGGER.log(Level.SEVERE, "ERROR CALL REFUND MSP QR : ", e);
                                                        this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                        throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                                    }

                                                    if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                                                        this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                        throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                                    }
                                                    try {
                                                        return this.refundApprovalService.insertApprove3(connBackUp, userId, approval.getMerchant_id(), approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue(), note);
                                                    } catch (Exception ex) {
                                                        LOGGER.severe("cannot insert data into table: MSP.TB_REFUND");
                                                        this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                        throw IErrors.INTERNAL_SERVER_ERROR;
                                                    }
                                                });

                                            } else {
                                                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                            }
                                        } else {
                                            JsonNode jsonOut = null;
                                            try {
                                                jsonOut = MSPClient.refund(req);
                                            } catch (Exception e) {
                                                LOGGER.log(Level.SEVERE, "error on refund qr: ", e);
                                                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                            }

                                            if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                                                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                            }

                                            try {
                                                return this.refundApprovalService.insertApprove3(connBackUp, userId, approval.getMerchant_id(), approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                        approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue(), note);
                                            } catch (Exception ex) {
                                                LOGGER.severe("cannot insert data into table: MSP.TB_REFUND");
                                                this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                                throw IErrors.INTERNAL_SERVER_ERROR;
                                            }
                                        }

                                    } else {
                                        JsonNode jsonOut = null;
                                        try {
                                            jsonOut = MSPClient.refund(req);
                                        } catch (Exception e) {
                                            LOGGER.log(Level.SEVERE, "error on refund qr: ", e);
                                            this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                            throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                        }

                                        if (jsonOut == null || !jsonOut.has("id") || !jsonOut.has("reference") || !jsonOut.has("state") || !"approved".equalsIgnoreCase(jsonOut.get("state").asText())) {
                                            this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                            throw new ErrorException(500, "INTERNAL_SERVER_ERROR", 500 + "|" + "An internal service error has occurred", "", "Resend the request at another time.");
                                        }

                                        try {
                                            return this.refundApprovalService.insertApprove3(connBackUp, userId, approval.getMerchant_id(), approval.getOriginal_transaction_id(), approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                    approval.getTransaction_id(), approval.getMerchant_transaction_ref(), 400, RefundData.Type.QR.getValue(), note);
                                        } catch (Exception ex) {
                                            LOGGER.severe("cannot insert data into table: MSP.TB_REFUND");
                                            this.refundApprovalService.updateErrorRefundStatus(connBackUp, approval.getTransaction_id(), 500, "An internal service error has occurred");
                                            throw IErrors.INTERNAL_SERVER_ERROR;
                                        }
                                    }

                                });
                    }).subscribe(qrRefund -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, qrRefund);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private Observable<Boolean> checkHistoryRefund(SQLConnection connOnline, List<JsonObject> approvalList, String paymentId, String stringHash) {
        if (stringHash == null || stringHash.isEmpty()) {
            LOGGER.info("stringHash is empty, khong check history refund");
            return Observable.just(true);
        }
        return MpayRefundService.getRefundByPaymentId(connOnline, paymentId).flatMap(refundData -> {
            String hisHash = "[]";
            List<JsonObject> hisHashList = new ArrayList<>();
            LOGGER.info("approvalList " + gson.toJson(approvalList).toString());
            LOGGER.info("refundData " + gson.toJson(refundData).toString());
            if (approvalList != null) {
                for (JsonObject refund : approvalList) {
                    if (refund.getInteger("N_STATUS") == 405) {
                        JsonObject his = new JsonObject();
                        his.put("S_ID", refund.getValue("N_ID").toString());
                        his.put("N_AMOUNT", String.valueOf(refund.getDouble("N_AMOUNT").longValue()));
                        his.put("S_STATE", "wfa");
                        his.put("S_TYPE", "REQUEST REFUND");
                        his.put("S_CURRENCY", refund.getString("S_CURRENCY"));
                        his.put("D_CREATE", refund.getValue("D_CREATE"));
                        hisHashList.add(his);
                    }
                }
            }
            if (refundData != null) {
                for (JsonObject refund : refundData) {
                    JsonObject his = new JsonObject();
                    String state = refund.getString("S_STATE");
                    String amount = String.valueOf(refund.getDouble("N_AMOUNT").longValue());
                    String type = "REFUND";
                    his.put("S_ID", refund.getString("S_ID"));
                    his.put("N_AMOUNT", amount);
                    his.put("S_STATE", state);
                    his.put("S_TYPE", type);
                    his.put("S_CURRENCY", refund.getString("S_CURRENCY"));
                    his.put("D_CREATE", refund.getString("D_CREATE"));
                    hisHashList.add(his);
                }
            }
            // sort by D_CREATE desc
            hisHashList.sort(
                    Comparator.comparing((JsonObject o) -> o.getString("D_CREATE"))
                            .reversed()  // Sắp xếp theo thứ tự giảm dần của D_CREATE
                            .thenComparing(o -> o.getString("S_ID")) // Sắp xếp theo S_ID nếu D_CREATE bằng nhau
            );

            // convert to string [S_AMOUNT, S_STATE, S_TYPE],[S_AMOUNT, S_STATE, S_TYPE,],.v.v
            if(!hisHashList.isEmpty()) {
                hisHash = hisHashList.stream().map(his -> "[" + his.getString("N_AMOUNT") + "," + his.getString("S_TYPE") + "," + his.getString("S_CURRENCY") + "," + his.getString("S_STATE")+ "]").reduce("", (a, b) -> a + b );
            }
            String stringHashDB = Utils.encodeHmacSha256Base64Url(hisHash, PropsUtil.get("key_hash_history_refund", "MDrR9%q`@ETJ_@[7B6$5jQ]CS4Z0qL}R-U,qfP8[ai&Oi|U|`BTc!V,C~T>^#M$"));
            LOGGER.info("hisHash " + hisHash);
            LOGGER.info("stringHashDB " + stringHashDB);
            LOGGER.info("stringHash " + stringHash);
            return Observable.just(stringHashDB.equals(stringHash));
        }).onErrorReturn(e -> {
            LOGGER.log(Level.SEVERE, "[ CHECK HISTORY REFUND ERROR  ] =>  " + e);
            return false;
        }).doOnNext(isOk -> LOGGER.info("[ CHECK HISTORY REFUND OK ? ] => " + isOk));
    }

    @Autowired
    private RefundApprovalService refundApprovalService;

    @Autowired
    private MpayRefundService mpayRefundService;

    private final static Gson gson = new Gson();
    private static final String X_SECURE_HASH = "X-Secure-Hash";
    private static final Logger LOGGER = Logger.getLogger(MpayRefundPatchHandler.class.getName());

}
