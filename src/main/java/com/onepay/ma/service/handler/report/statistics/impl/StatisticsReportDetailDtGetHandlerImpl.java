package com.onepay.ma.service.handler.report.statistics.impl;

import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.report.statistics.StatisticsReportDetailDtGetHandler;
import com.onepay.ma.service.models.StatisticsReportDt;
import com.onepay.ma.service.service.StatisticsReportService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 12/6/18.
 */
public class StatisticsReportDetailDtGetHandlerImpl implements StatisticsReportDetailDtGetHandler {

    public StatisticsReportDetailDtGetHandlerImpl(StatisticsReportService statisticsReportService) {
        this.statisticsReportService = statisticsReportService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[Statistics Detail dt GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnerecon = rc.get(ParamsPool.ONERECON_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String transDate = rc.request().getParam("trans_date");
        try {
            df.parse(transDate);
        } catch (Exception e) {

            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int interval = (rc.request().getParam(ParamsPool.INTERVAL) == null||rc.request().getParam(ParamsPool.INTERVAL) =="") ?   1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        String bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));
        String acqType = rc.request().getParam("acq_type") == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam("acq_type"));
        String transType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));


       // String reportType = rc.request().getParam(ParamsPool.REPORT_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REPORT_TYPE));

        StatisticsReportParameter statisticsReportParameter = new StatisticsReportParameter();
        statisticsReportParameter.setAcquirerId(bankId);
        statisticsReportParameter.setTransDate(transDate);
        statisticsReportParameter.setAcqType(acqType);
        statisticsReportParameter.setInterval(interval);
        statisticsReportParameter.setTransType(transType);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnerecon), f -> f.dispose()).flatMap(connOnerecon -> {
                                return statisticsReportService.listDetailDt(connOnerecon, statisticsReportParameter).map(statisticsReports -> {
                                    Map returnReport = new HashMap();
                                    int index = 1;
                                    for (StatisticsReportDt items : statisticsReports) {
                                        items.setRowNumber(index);
                                        index++;
                                    }
                                    returnReport.put("reports", statisticsReports);
                                    return returnReport;
                                });
            }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    private StatisticsReportService statisticsReportService;

    private static final Logger LOGGER = Logger.getLogger(StatisticsReportDetailDtGetHandler.class.getName());
}
