package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.FundsTransferReq;
import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;

import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import org.apache.commons.lang.StringUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import io.vertx.core.json.JsonObject;

@Component
public class TransferTransactionHandlerImpl implements Handler<RoutingContext> {

    private static final Logger LOGGER = Logger.getLogger(FundsTransHistoryHandlerImpl.class.getName());
    @Autowired
    private MerchantService merchantService;

    @Autowired
    private UserService userService;

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String partnerId = myEvent.get(ParamsPool.X_PARTNER_ID);
        String userId = myEvent.get(ParamsPool.USER_ID);
        String xIP = myEvent.get(ParamsPool.X_REAL_IP);
        JDBCClient clientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = myEvent.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        JsonObject body = myEvent.getBodyAsJson();

        String funds_transfer_id = body.getString("funds_transfer_id");
        String merchantId = body.getString("merchantId");
        String merchantAccount = body.getString("merchantAccount");
        String receiptAccountId = body.getString("receiptAccountId");
        String holder_name = body.getString("holder_name");
        String receiptBank = body.getString("receiptBank");
        String amount = body.getString("amount");
        String remark = body.getString("remark");
        String userName = null;
        if(body.containsKey("username")){
            userName = body.getString("username");
        }
        String password = null;
        if(body.containsKey("password")){
            password = body.getString("password");
        }
        final String userNameF = userName;
        final String passwordF = password;
        String maMain = body.getString(ParamsPool.MA_MAIN);
        LOGGER.log(Level.INFO, "xUserId: {}", xUserId);
        LOGGER.log(Level.INFO, "userId: {}", userId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain v1: ");
            String merchantIds = MaPermission.getMc(userId, xRequestId, partnerId, merchantId);
            if (null == merchantIds || merchantIds.isEmpty()) {
                throw IErrors.FORBIDDEN;
            }
            if (merchantIds.isEmpty() || merchantIds.equalsIgnoreCase("all")) {
                throw IErrors.INVALID_MERCHANT;
            }
            if (merchantIds.equals("ONEPAYVTB2")) {
                throw IErrors.INVALID_MERCHANT;
            } else if (merchantIds.equals("ONEPAY_REFUND")) {
                if (!xIP.equals("**************")) {
                    throw IErrors.INVALID_IP;
                }
            }

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return userService.getOneAm(connBackUp, xUserId).map(user -> {
                        FundsTransferReq fundReq = new FundsTransferReq(userId, xUserId, xRequestId, merchantId, merchantAccount, receiptAccountId, receiptBank, holder_name, amount, remark, funds_transfer_id, userNameF, passwordF, user.getS_id(), user.getName());
                            JsonObject result = OnePayoutClient.transferTransaction(fundReq);
                            Map<String, Object> responseHashMap = new HashMap();
                            responseHashMap.put("result", result);
                            return responseHashMap;
                    });
            }).subscribe(a -> {
                try {
                    OneSchedClient.synchronizePO();
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "po call ep dong bo error " + e.getMessage());
                }
                myEvent.put(ParamsPool.HANDLER_DATA_RESULT, a);
                myEvent.next();
            }, throwable -> {
                myEvent.fail(throwable);
            });
        } else  {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return userService.getOneAm(connBackUp, xUserId).flatMap(user -> {
                            return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").map(merchants -> {
                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                String merchantIdx = String.join(",", merchantIdList);
                                if(merchantIdx.isEmpty() || merchantIdx.equalsIgnoreCase("all")) {
                                    throw IErrors.INVALID_MERCHANT;
                                }

                                if(merchantIdx.equals("ONEPAYVTB2"))
                                {
                                    throw IErrors.INVALID_MERCHANT;
                                }
                                else if(merchantIdx.equals("ONEPAY_REFUND"))
                                {
                                    if(!xIP.equals("**************")) {
                                        throw IErrors.INVALID_IP;
                                    }                                    
                                }

                                FundsTransferReq fundReq = new FundsTransferReq(userId, xUserId, xRequestId, merchantId, merchantAccount, receiptAccountId, receiptBank, holder_name, amount, remark, funds_transfer_id, userNameF, passwordF, user.getS_id(), user.getName());
                                JsonObject result = OnePayoutClient.transferTransaction(fundReq);
                                Map<String, Object> responseHashMap = new HashMap();
                                responseHashMap.put("result", result);
                                return responseHashMap;

                            });
                        });
                    });
                }).subscribe(a -> {
                    myEvent.put(ParamsPool.HANDLER_DATA_RESULT, a);
                    myEvent.next();
                }, throwable -> {
                    myEvent.fail(throwable);
                });
            }
    }
}
