package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.InternationalAuthorizeHistoryGetHandlerImpl;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalAuthorizeHistoryGetHandler extends Handler<RoutingContext>  {
    static InternationalAuthorizeHistoryGetHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, UserService userService){
        return new InternationalAuthorizeHistoryGetHandlerImpl(internationalTransactionService, merchantService, userService);

    }
}
