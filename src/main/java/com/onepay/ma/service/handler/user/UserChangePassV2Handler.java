package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserChangePassV2HandlerImpl;
import com.onepay.ma.service.service.UserServiceV2;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

public interface UserChangePassV2Handler extends Handler<RoutingContext>  {
    static UserChangePassV2Handler changePasswordV2(UserServiceV2 userService){
        return new UserChangePassV2HandlerImpl(userService);
    }
}


