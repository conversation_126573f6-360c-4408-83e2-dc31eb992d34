package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostCdrHandler;
import com.onepay.ma.service.models.CdrParameterPost;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FilePostCdrHandlerImpl implements FilePostCdrHandler {

    public FilePostCdrHandlerImpl(CdrService cdrService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.cdrService = cdrService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.cacheGuava = cacheGuava;
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE  CDR POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientCdr = rc.get(ParamsPool.CDR_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        CdrParameterPost mapBody =  gson.fromJson(body, CdrParameterPost.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE  CDR POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientCdr), f-> f.dispose())
                .flatMap(connCdr -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                connectBackUp = connBackUp;
                                Map data = new HashMap();
                                return  cdrService.getTotal(connCdr, mapBody).flatMap(integer -> {
                                    if(integer == 0){
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    String fileName  = "cdr" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
                                    long date = new java.util.Date().getTime();
                                    fileName += date;
                                    String fileHashName = "";
                                    data.put("parameter", mapBody);
                                    data.put("file_name", fileName);
                                    data.put("row", integer);
                                    try {
                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                    } catch (NoSuchAlgorithmException e) {
                                        rc.fail(e);
                                    } catch (UnsupportedEncodingException e) {
                                        rc.fail(e);
                                    }
                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                    FileDownload fileDownload = new FileDownload();
                                    fileDownload.setUser(userId);
                                    fileDownload.setFile_type("cdr_transaction");
                                    if(integer <= serverConfig.getRowLevel()){
                                        fileDownload.setExt("csv");
                                    }else {
                                        fileDownload.setExt("zip");
                                    }
                                    fileDownload.setFile_name(fileName);
                                    fileDownload.setFile_hash_name(fileHashName);
                                    fileDownload.setConditions(gson.toJson(mapBody));
                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                            return data;
                                        });
                                    });
                                });
                            });
                }).subscribe(map -> {
                if(connectBackUp != null){
                    connectBackUp.commitObservable();
                }
                CdrParameterPost parameter = (CdrParameterPost) map.get("parameter");
                int row = Integer.valueOf(map.get("row").toString());
                if (row <= serverConfig.getRowLevel()) {
                    //fileDownload.setExt("csv");
                    Message<CdrParameterPost> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                    QueueProducer.sendMessage(message);
                } else {
                    //  fileDownload.setExt("zip");
                    Message<CdrParameterPost> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                    QueueProducer.sendMessage(message);
                }

                rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });



    }

    private CdrService cdrService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private CacheGuava cacheGuava;

    private SQLConnection connectBackUp;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(FilePostCdrHandler.class.getName());
}
