package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.security.SecureRandom;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OperatorCreateHandlerImpl implements Handler<RoutingContext> {
    private static final String ALPHA_CAPS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String ALPHA = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMERIC = "**********";
    private static SecureRandom random = new SecureRandom();
    private static Logger LOGGER = Logger.getLogger(OperatorCreateHandlerImpl.class.getName());
    private final static Gson gson = new Gson();

    @Autowired
    private OperatorService operatorService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserService userService;

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String body = rc.getBodyAsString();

        OperatorDTO operator = gson.fromJson(body, OperatorDTO.class);
        String pass = generatePassword();
        operator.setPassword(pass);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(sqlConnection -> {
                    connectionBackUp = sqlConnection;
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {
                        return userService.getOneAm(connBackUp, userId).flatMap(user -> {
                            return operatorService.getOperatorExisted(connectionBackUp, operator.getPartnerId(), operator.getUserName()).flatMap(operatorExist -> {
                                if (operatorExist != null) {
                                    throw IErrors.INVALID_USER_NAME_EXISTED;
                                }
                                operator.setUserCreate(user.getName());
                                return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                    return operatorService.insert(connectionBackUp, operator).map(data -> {
                                        
                                        data.setPassword(pass);
                                        emailService.sentMailCreateOperator(data, pass);
                                        return data;
                                    });
                                });
                            });

                        });
                    });
                }).subscribe(user -> {
            if (user == null) {
                if (connectionBackUp != null) {
                    connectionBackUp.rollbackObservable();
                }
                throw IErrors.VALIDATION_ERROR;
            }
            if (connectionBackUp != null) {
                connectionBackUp.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
            rc.next();
        }, throwable -> {
            if (connectionBackUp != null) {
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    private static String generatePassword() {
        String result = "";
        result += generateString(6, ALPHA_CAPS + ALPHA);
        result += generateString(6, NUMERIC);
        return result;
    }

    private static String generateString(int len, String dic) {
        String result = "";
        for (int i = 0; i < len; i++) {
            int index = random.nextInt(dic.length());
            result += dic.charAt(index);
        }
        return result;
    }

    private SQLConnection connectionBackUp;

}
