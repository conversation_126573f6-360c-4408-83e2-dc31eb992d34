package com.onepay.ma.service.handler.transaction.international.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class BrandConfigGetHandlerImpl implements Handler<RoutingContext> {


    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String brandId = rc.request().getParam("brandId");
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        if (brandId.isEmpty()) {
            throw IErrors.RESOURCE_NOT_FOUND;
        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(sqlOnline -> {
                                    return interTranService.getBrandConfig(sqlOnline, brandId);
                                });
                    }).subscribe(brandConfig -> {
                if (brandConfig == null) {
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                rc.put(ParamsPool.HANDLER_DATA_RESULT, brandConfig);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }

    }


    @Autowired
    private InternationalTransactionService interTranService;

}
