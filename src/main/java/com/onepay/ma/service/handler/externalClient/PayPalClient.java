package com.onepay.ma.service.handler.externalClient;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonParser;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringUtils;
import com.onepay.ma.service.util.TLSSocketFactory;
import com.onepay.ma.service.util.Utils;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PayPalClient {


    private static final Logger LOGGER = Logger.getLogger(PayPalClient.class.getName());


    private static final String PARTNER_CLIENT_ID = PropsUtil.get("paypal.partner.client", StringPool.BLANK);
    private static final String PARTNER_PASS = PropsUtil.get("paypal.partner.pass", StringPool.BLANK);

    private static final String PARTNER_ID = PropsUtil.get("paypal.partner.id", StringPool.BLANK);

    private static final String PARTNER_LOGO_URL = PropsUtil.get("paypal.partner.logo_url", StringPool.BLANK);
    private static final String PARTNER_RETURN_URL = PropsUtil.get("paypal.return_url", StringPool.BLANK);
    private static final String PARTNER_ACTION_URL = PropsUtil.get("paypal.action_url", StringPool.BLANK);

    private static final String PAYPAL_BASE_URL = PropsUtil.get("paypal.base_url", StringPool.BLANK);

    public static JsonNode getAccessToken() {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = PAYPAL_BASE_URL + "/oauth2/token";

        String requestMethod = "POST";

        try {

            String userpass = PARTNER_CLIENT_ID+":"+PARTNER_PASS;
            //String basicAuth = "Basic " + javax.xml.bind.DatatypeConverter.printBase64Binary(userpass.getBytes("UTF-8"));
            String basicAuth = "Basic " + Base64.getEncoder().encodeToString(userpass.getBytes("UTF-8"));
            String data =  "grant_type=client_credentials";

            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Accept-Language", "en_US");
            connection.setRequestProperty("Authorization", basicAuth);

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(data);
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("GET ACCESS TOKEN responseCode:" + responseCode);
            LOGGER.info(" GET ACCESS TOKEN responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();

                LOGGER.log(Level.SEVERE, "RETURN ACCESS TOKEN CONTENT " + Utils.mask(response) );
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;

    }
    public static JsonNode getAccountStatus(String merchantId) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = PAYPAL_BASE_URL + "/customer/partners/"+ PARTNER_ID +"/merchant-integrations/" + merchantId;

        String requestMethod = "GET";

        JsonNode accessTokenJson = getAccessToken();
        String accessToken = accessTokenJson.get("access_token").asText();
        try {

            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer "+ accessToken );

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("GET ACCOUNT STATUS responseCode:" + responseCode);
            LOGGER.info("GET ACCOUNT STATUS  responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        LOGGER.log(Level.SEVERE, " GET ACCOUNT STATUS RETURN CONTENT " + Utils.mask(jsonReturn));
        return jsonReturn;

    }
    public static JsonNode getDisputeDetail(String disputeId,String payer_id) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = PAYPAL_BASE_URL + "/customer/disputes/" + disputeId;
        LOGGER.log(Level.INFO, "GET DISPUTE DETAIL WOTH URL: " + Utils.excludeSensitiveInfo(requestURI));

        String requestMethod = "GET";

        JsonNode accessTokenJson = getAccessToken();
        String accessToken = accessTokenJson.get("access_token").asText();

        System.out.println("accessToken = "+ accessToken);

        try {

            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Authorization", "Bearer "+ accessToken );
            connection.setRequestProperty("cache-control", "no-cache" );
            connection.setRequestProperty("Content-Type", "application/json");
            String payPalAuthAssertion =getPayPalAuthAssertion(payer_id,PARTNER_CLIENT_ID);
            connection.setRequestProperty("paypal-auth-assertion", payPalAuthAssertion);

            // LOGGER.info("DISPUTE DETAIL REQUEST HEADER: Authorization " + "Bearer "+ accessToken );
            // LOGGER.info("DISPUTE DETAIL REQUEST HEADER: paypal-auth-assertion " + payPalAuthAssertion);

           // System.out.println("payPalAuthAssertion = "+payPalAuthAssertion);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("GET DISPUTE DETAIL responseCode:" + responseCode);
            LOGGER.info("GET DISPUTE DETAIL  responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();

                LOGGER.log(Level.SEVERE, "ERROR ON GET DISPUTE DETAIL: " + Utils.mask(response));
                throw  new ErrorException(responseCode, responseMsg, responseMsg, "", strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        LOGGER.log(Level.SEVERE, " GET DISPUTE DETAIL RETURN CONTENT " + Utils.mask(jsonReturn) );
        return jsonReturn;

    }

    public static JsonNode signupPrefillMerchant(UserData customerData, Partner partner) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = PAYPAL_BASE_URL + "/customer/partner-referrals";

        String requestMethod = "POST";

        JsonNode accessTokenJson = getAccessToken();
        String accessToken = accessTokenJson.get("access_token").asText();
        // LOGGER.log(Level.SEVERE, "ACCESS TOKEN: " + accessToken);


        /*----------------------------------------------- Business Detail JS------------------------------------------------*/
        ObjectNode business_phone_number_details = JsonNodeFactory.instance.objectNode();
        business_phone_number_details.put("country_code", "84");
        business_phone_number_details.put("national_number", customerData.getPhone().substring(2));

        ObjectNode phone_contact_item = JsonNodeFactory.instance.objectNode();
        phone_contact_item.put("phone_number_details", business_phone_number_details);
        phone_contact_item.put("phone_type", "MOBILE");

        ArrayNode business_phone_contacts = JsonNodeFactory.instance.arrayNode();
        business_phone_contacts.add(phone_contact_item);

        ObjectNode business_address = JsonNodeFactory.instance.objectNode();
//        business_address.put("line1", StringUtils.removeAccent(partner.getAddress()));
        business_address.put("country_code", "VN");

        ObjectNode name_item = JsonNodeFactory.instance.objectNode();
        name_item.put("type", "LEGAL");
        name_item.put("name", StringUtils.removeAccent(partner.getName()));

        ArrayNode business_names = JsonNodeFactory.instance.arrayNode();
        business_names.add(name_item);

        ArrayNode website_urls = JsonNodeFactory.instance.arrayNode();
        website_urls.add(partner.getWebsite());

        ObjectNode business_details = JsonNodeFactory.instance.objectNode();
        business_details.put("business_address", business_address);
        business_details.put("phone_contacts", business_phone_contacts);
        business_details.put("names", business_names);
        business_details.put("website_urls", website_urls);
        business_details.put("business_description", partner.getDescription());
        /*----------------------------------------------- CUSTOMER JS------------------------------------------------*/
        ObjectNode name = JsonNodeFactory.instance.objectNode();
        name.put("prefix", StringPool.BLANK);
        name.put("given_name", customerData.getAm_first_name() ==  null ? "" : StringUtils.removeAccent(customerData.getAm_first_name()));
        name.put("surname", StringUtils.removeAccent(customerData.getAm_last_name()));

        ObjectNode phone_number_details = JsonNodeFactory.instance.objectNode();
        phone_number_details.put("country_code", "84");
        phone_number_details.put("national_number", customerData.getPhone().substring(2, customerData.getPhone().length()));


        ObjectNode phone_contact = JsonNodeFactory.instance.objectNode();
        phone_contact.put("phone_number_details", phone_number_details);
        phone_contact.put("phone_type", "HOME");

        ArrayNode phone_contacts = JsonNodeFactory.instance.arrayNode();
        phone_contacts.add(phone_contact);

        ObjectNode home_address = JsonNodeFactory.instance.objectNode();
        home_address.put("line1", StringPool.BLANK ); //customerData.getAddress()
        home_address.put("state", StringPool.BLANK);
        home_address.put("city", StringPool.BLANK);
        home_address.put("country_code", "VN");
        home_address.put("postal_code", StringPool.BLANK);

        ObjectNode person_details = JsonNodeFactory.instance.objectNode();
        person_details.put("email_address", customerData.getEmail());
        person_details.put("nationality_country_code", "VN");
//        person_details.put("name", name);
//        person_details.put("phone_contacts", phone_contacts );
//        person_details.put("home_address", home_address );



        ObjectNode Partner_specific_identifier = JsonNodeFactory.instance.objectNode();
        Partner_specific_identifier.put("type", "TRACKING_ID");
        Partner_specific_identifier.put("value", partner.getId());

        ArrayNode partner_specific_identifiers = JsonNodeFactory.instance.arrayNode();
        partner_specific_identifiers.add(Partner_specific_identifier);



        ObjectNode customer_data = JsonNodeFactory.instance.objectNode();
        customer_data.put("customer_type", "MERCHANT");
        customer_data.put("person_details", person_details);
//        customer_data.put("business_details", business_details);
        customer_data.put("partner_specific_identifiers", partner_specific_identifiers);
        customer_data.put("preferred_language_code", "en_US");
        customer_data.put("primary_currency_code", "USD");
        /*---------------------END CUSTOMER JS ----------*/

        /*--------------------------------------------------- requested_capabilities JS-----------------------------------*/
        ArrayNode feature_list = JsonNodeFactory.instance.arrayNode();
        feature_list.add("PAYMENT");
        feature_list.add("REFUND");
        feature_list.add("READ_SELLER_DISPUTE");
        feature_list.add("UPDATE_SELLER_DISPUTE");

        ObjectNode rest_third_party_details = JsonNodeFactory.instance.objectNode();
        rest_third_party_details.put("partner_client_id", PARTNER_CLIENT_ID);
        rest_third_party_details.put("feature_list", feature_list);

        ObjectNode rest_api_integration = JsonNodeFactory.instance.objectNode();
        rest_api_integration.put("integration_method", "PAYPAL");
        rest_api_integration.put("integration_type", "THIRD_PARTY");


        ObjectNode api_integration_preference = JsonNodeFactory.instance.objectNode();
        api_integration_preference.put("partner_id", PARTNER_ID);
        api_integration_preference.put("rest_api_integration", rest_api_integration);
        api_integration_preference.put("rest_third_party_details", rest_third_party_details);

        ObjectNode requested_capability = JsonNodeFactory.instance.objectNode();
        requested_capability.put("capability", "API_INTEGRATION");
        requested_capability.put("api_integration_preference", api_integration_preference);

        ArrayNode requested_capabilities = JsonNodeFactory.instance.arrayNode();
        requested_capabilities.add(requested_capability);
        /*--------------------------------------------------- END requested_capabilities JS-----------------------------------*/

        /*--------------------------------------------------- web_experience_preference JS-----------------------------------*/

        ObjectNode web_experience_preference = JsonNodeFactory.instance.objectNode();
        web_experience_preference.put("partner_logo_url", PARTNER_LOGO_URL);
        web_experience_preference.put("return_url", PARTNER_RETURN_URL);
        web_experience_preference.put("action_renewal_url", PARTNER_ACTION_URL);

        /*--------------------------------------------------- END web_experience_preference JS-----------------------------------*/

        /*--------------------------------------------------- collected_consents JS-----------------------------------*/

        ObjectNode collected_consent = JsonNodeFactory.instance.objectNode();
        collected_consent.put("type", "SHARE_DATA_CONSENT");
        collected_consent.put("granted", true);

        ArrayNode collected_consents = JsonNodeFactory.instance.arrayNode();
        collected_consents.add(collected_consent);

        /*--------------------------------------------------- END collected_consents JS-----------------------------------*/

        /*--------------------------------------------------- products JS-----------------------------------*/

        ArrayNode products = JsonNodeFactory.instance.arrayNode();
        products.add("EXPRESS_CHECKOUT");
        /*--------------------------------------------------- END products JS-----------------------------------*/


        ObjectNode data = JsonNodeFactory.instance.objectNode();
        data.put("customer_data", customer_data);
        data.put("requested_capabilities", requested_capabilities);
        data.put("web_experience_preference", web_experience_preference);
        data.put("collected_consents", collected_consents);
        data.put("products", products);
        LOGGER.info("POST MERCHANT ONBOARDING content: " + data.toString());

//        try {
//        postToPayPal(accessToken, requestURI, 60000, 60000, data.toString(), System.currentTimeMillis()+":", "ONEPAY");
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            throw IErrors.INTERNAL_SERVER_ERROR;
//        }
        try {
            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer "+ accessToken );

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(data.toString());
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("POST MERCHANT ONBOARDING responseCode:" + responseCode);
            LOGGER.info("POST MERCHANT ONBOARDING  responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();

                LOGGER.log(Level.WARNING, "ERROR ON ONBOARDING MERCHANT: " + Utils.mask(response));
                throw  new ErrorException(responseCode, responseMsg, responseMsg, "", strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.SEVERE, " POST MERCHANT ONBOARDING RETURN CONTENT " + Utils.mask(jsonReturn));
        return jsonReturn;

    }

    public static JsonNode postSentMessage(String disputeId,String message,String payer_id) {
        JsonNode jsonReturn = null;

        JsonNode accessTokenJson = getAccessToken();
        String accessToken = accessTokenJson.get("access_token").asText();
        //System.out.println("accessToken = "+ accessToken);

        // Request uri base
        String requestURI = PAYPAL_BASE_URL + "/customer/disputes/" + disputeId + "/send-message";
        LOGGER.log(Level.INFO, "POST SENT MESSAGE URL: " + Utils.excludeSensitiveInfo(requestURI));
        String requestMethod = "POST";
        ObjectNode data = JsonNodeFactory.instance.objectNode();
        data.put("message", message);
        LOGGER.info("POST SENT MESSAGE content: " + data.toString());
        try {

            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Authorization", "Bearer "+ accessToken );
            connection.setRequestProperty("Content-Type", "application/json");
            String payPalAuthAssertion =getPayPalAuthAssertion(payer_id,PARTNER_CLIENT_ID);
            connection.setRequestProperty("paypal-auth-assertion", payPalAuthAssertion);
            //System.out.println("payPalAuthAssertion = "+payPalAuthAssertion);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(data.toString());
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("POST SENT MESSAGE responseCode:" + responseCode);
            LOGGER.info("POST SENT MESSAGE  responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();

                LOGGER.log(Level.SEVERE, "ERROR ON POST SENT MESSAGE: " + Utils.mask(response));
                throw  new ErrorException(responseCode, responseMsg, responseMsg, "", strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "[ERROR]", e);
        }
        LOGGER.log(Level.SEVERE, " POST SENT MESSAGE RETURN CONTENT " + Utils.mask(jsonReturn) );
        return jsonReturn;

    }
    private static String getPayPalAuthAssertion(String payer_id, String client_id){
        String result ="";
        String header1 ="{\"typ\":\"JWT\",\"alg\":\"none\"}";
        String token1=Convert.toBase64String(header1.getBytes()).replaceAll("\r|\n","");
        token1=token1.replace("=","");
        String header2 ="{\"payer_id\":\""+payer_id+"\",\"iss\":\""+client_id+"\"}";
        String token2=Convert.toBase64String(header2.getBytes()).replaceAll("\r|\n","");
        result =token1 + "." + token2 + ".";
        return  result;
    }

    public static void main(String[] args) {
      /* String str = " {\"id\":\"WH-3L160524M3951900J-7DJ89222CC222110L\",\"event_version\":\"1.0\",\"create_time\":\"2018-09-26T08:48:42.000Z\",\"resource_type\":\"dispute\",\"event_type\":\"CUSTOMER.DISPUTE.CREATED\",\"summary\":\"A new dispute opened with Case # PP-000-043-306-362\",\"resource\":{\"dispute_id\":\"PP-000-043-306-362\",\"create_time\":\"2018-09-26T08:47:38.000Z\",\"update_time\":\"2018-09-26T08:47:38.000Z\",\"disputed_transactions\":[{\"seller_transaction_id\":\"4S768307RH8127004\",\"invoice_number\":\"TESTONEPAYUSD_TEST_15378488507402048561148\",\"custom\":\"TESTONEPAYUSD_TEST_15378488507402048561148\",\"seller\":{\"merchant_id\":\"E299X6C7WN4NC\",\"name\":\"test facilitator's Test Store\"},\"items\":[{\"item_id\":\"1\"}],\"seller_protection_eligible\":true}],\"reason\":\"MERCHANDISE_OR_SERVICE_NOT_RECEIVED\",\"status\":\"OPEN\",\"dispute_amount\":{\"currency_code\":\"USD\",\"value\":\"1000.00\"},\"dispute_life_cycle_stage\":\"INQUIRY\",\"dispute_channel\":\"INTERNAL\",\"messages\":[{\"posted_by\":\"BUYER\",\"time_posted\":\"2018-09-26T08:47:45.000Z\",\"content\":\"Hêlo\"}],\"links\":[{\"href\":\"https://api.sandbox.paypal.com/v1/customer/disputes/PP-000-043-306-362\",\"rel\":\"self\",\"method\":\"GET\"},{\"href\":\"https://api.sandbox.paypal.com/v1/customer/disputes/PP-000-043-306-362/send-message\",\"rel\":\"send_message\",\"method\":\"POST\"}]},\"links\":[{\"href\":\"https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-3L160524M3951900J-7DJ89222CC222110L\",\"rel\":\"self\",\"method\":\"GET\"},{\"href\":\"https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-3L160524M3951900J-7DJ89222CC222110L/resend\",\"rel\":\"resend\",\"method\":\"POST\"}]}";
        JsonObject body = new JsonObject(str);
        JsonObject transaction =  body.getJsonObject("resource");
        JsonArray disputedTransactions =  transaction.getJsonArray("disputed_transactions");
        JsonObject disputedTransactions1 =disputedTransactions.getJsonObject(0);
        JsonObject seller =disputedTransactions1.getJsonObject("seller");
        String merchant_id = seller.getString("merchant_id");*/
        JsonNode js = getDisputeDetail("PP-000-043-412-887","E299X6C7WN4NC");
//        JsonNode js = postSentMessage("PP-000-043-342-977","hello","E299X6C7WN4NC");
        JsonNode disputedTransaction= js.get("disputed_transactions").get(0);
        String strt = disputedTransaction.get("seller_protection_eligible").asText();
        System.out.println("strt = " +strt);


    }


    private static String certPayPal="-----BEGIN CERTIFICATE-----\n" +
            "MIIDjjCCAnagAwIBAgIQAzrx5qcRqaC7KGSxHQn65TANBgkqhkiG9w0BAQsFADBh\n" +
            "MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\n" +
            "d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBH\n" +
            "MjAeFw0xMzA4MDExMjAwMDBaFw0zODAxMTUxMjAwMDBaMGExCzAJBgNVBAYTAlVT\n" +
            "MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j\n" +
            "b20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IEcyMIIBIjANBgkqhkiG\n" +
            "9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuzfNNNx7a8myaJCtSnX/RrohCgiN9RlUyfuI\n" +
            "2/Ou8jqJkTx65qsGGmvPrC3oXgkkRLpimn7Wo6h+4FR1IAWsULecYxpsMNzaHxmx\n" +
            "1x7e/dfgy5SDN67sH0NO3Xss0r0upS/kqbitOtSZpLYl6ZtrAGCSYP9PIUkY92eQ\n" +
            "q2EGnI/yuum06ZIya7XzV+hdG82MHauVBJVJ8zUtluNJbd134/tJS7SsVQepj5Wz\n" +
            "tCO7TG1F8PapspUwtP1MVYwnSlcUfIKdzXOS0xZKBgyMUNGPHgm+F6HmIcr9g+UQ\n" +
            "vIOlCsRnKPZzFBQ9RnbDhxSJITRNrw9FDKZJobq7nMWxM4MphQIDAQABo0IwQDAP\n" +
            "BgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBhjAdBgNVHQ4EFgQUTiJUIBiV\n" +
            "5uNu5g/6+rkS7QYXjzkwDQYJKoZIhvcNAQELBQADggEBAGBnKJRvDkhj6zHd6mcY\n" +
            "1Yl9PMWLSn/pvtsrF9+wX3N3KjITOYFnQoQj8kVnNeyIv/iPsGEMNKSuIEyExtv4\n" +
            "NeF22d+mQrvHRAiGfzZ0JFrabA0UWTW98kndth/Jsw1HKj2ZL7tcu7XUIOGZX1NG\n" +
            "Fdtom/DzMNU+MeKNhJ7jitralj41E6Vf8PlwUHBHQRFXGU7Aj64GxJUTFy8bJZ91\n" +
            "8rGOmaFvE7FBcf6IKshPECBV1/MUReXgRPTqh5Uykw7+U0b6LJ3/iyK5S9kJRaTe\n" +
            "pLiaWN0bfVKfjllDiIGknibVb63dDcY3fe0Dkhvld1927jyNxF1WW6LZZm6zNTfl\n" +
            "MrY=\n" +
            "-----END CERTIFICATE-----\n";
    private static com.google.gson.JsonObject postToPayPal(String accessToken, String url, int connectTimeout, int readTimeout, String content, String requestId, String partnerCode) throws Exception {
        HttpsURLConnection conn=null;
//        TrustManager[] trustAllCerts = new TrustManager[]{
//                new X509TrustManager() {
//                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
//                        return null;
//                    }
//
//                    public void checkClientTrusted(
//                            java.security.cert.X509Certificate[] certs, String authType) {
//                    }
//
//                    public void checkServerTrusted(
//                            java.security.cert.X509Certificate[] certs, String authType) {
//                    }
//                }};
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
//        String ssl = getSSLCertificate();
//        if (ssl == null || ssl.length() == 0) Log.d(TAG, "SSL have not value");
        InputStream caInput = new ByteArrayInputStream(certPayPal.getBytes("UTF-8"));
        Certificate ca;
        try {
            ca = cf.generateCertificate(caInput);
        } finally {
            caInput.close();
        }
        String keyStoreType = KeyStore.getDefaultType();
        KeyStore keyStore = KeyStore.getInstance(keyStoreType);
        keyStore.load(null, null);
        keyStore.setCertificateEntry("av-ca-0", ca);

        String tmfAlgorithm = TrustManagerFactory.getDefaultAlgorithm();
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(tmfAlgorithm);
        tmf.init(keyStore);
        try {
//            SSLContext sc = SSLContext.getInstance("SSL");
//            sc.init(null, trustAllCerts, new java.security.SecureRandom());
//            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            conn = (HttpsURLConnection) (new URL(url)).openConnection();
            conn.setSSLSocketFactory(new TLSSocketFactory(tmf.getTrustManagers()));
        } catch (Exception e) {
            LOGGER.fine(e.toString());
        }

        LOGGER.info("==========Post to PayPal=========");
        LOGGER.info("URL:" + Utils.excludeSensitiveInfo(url));
        LOGGER.info("Content:" + content);
//        HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length() : "0"));
        conn.setRequestProperty("Accept", "application/json");
        conn.setRequestProperty("Host", "api.sandbox.paypal.com");
        // LOGGER.info( "Auth:"+"Bearer " + accessToken);
        conn.setRequestProperty("Authorization", "Bearer " + accessToken);
//        conn.setRequestProperty("PayPal-Request-Id", requestId);
//        conn.setRequestProperty("PayPal-Partner-Attribution-Id", partnerCode);


        conn.setUseCaches(false);
        conn.setDoInput(true);
        conn.setDoOutput(true);
        //conn.setInstanceFollowRedirects(false);
        conn.setConnectTimeout(connectTimeout);
        conn.setReadTimeout(readTimeout);
        for (Map.Entry<String, List<String>> entries : conn.getRequestProperties().entrySet()) {
            String values = "";
            for (String value : entries.getValue()) {
                values += value + ",";
            }
            LOGGER.info(entries.getKey() + ":" + values);
        }
        conn.connect();

        // Send request params
        DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
        if (content != null) wr.writeBytes(content);
        wr.flush();
        wr.close();
        LOGGER.info("=======PayPal Response======");

        int responseCode = conn.getResponseCode();
        // LOGGER.fine("HttpResponseCode:" + responseCode);
        for (Map.Entry<String, List<String>> entries : conn.getHeaderFields().entrySet()) {
            String values = "";
            for (String value : entries.getValue()) {
                values += value + ",";
            }
            LOGGER.info(entries.getKey() + ":" + values);
        }
        //Get response content
        InputStream is = conn.getInputStream();
        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        byte[] buf = new byte[1000];
        int length;
        while ((length = is.read(buf)) != -1) {
            bout.write(buf, 0, length);
        }
        bout.flush();
        String body = new String(bout.toByteArray(), "UTF-8");
        bout.close();
        // LOGGER.info("HttpResponseContent:" + body);
        JsonParser parser = new JsonParser();
        com.google.gson.JsonObject jResponse = parser.parse(body).getAsJsonObject();
        conn.disconnect();
        LOGGER.info("=========END============");
        return jResponse;
    }

}
