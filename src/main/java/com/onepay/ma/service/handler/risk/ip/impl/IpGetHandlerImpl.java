package com.onepay.ma.service.handler.risk.ip.impl;

import com.onepay.ma.service.handler.risk.ip.IpGetHandler;
import com.onepay.ma.service.models.IpAddressInfo;
import com.onepay.ma.service.service.IpService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public class IpGetHandlerImpl implements IpGetHandler {

    public IpGetHandlerImpl(IpService ipService) {
        this.ipService = ipService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String ipAddress = rc.request().getParam("ip");
        JDBCClient jdbcClient = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);

        if(ipAddress != null) {
          Observable<IpAddressInfo> obs =  Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClient), f -> f.dispose()).flatMap(connection -> {
            return ipService.get(connection, ipAddress);
        });
            obs.subscribe(ipAddressInfo -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, ipAddressInfo);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }

    private IpService ipService;
}
