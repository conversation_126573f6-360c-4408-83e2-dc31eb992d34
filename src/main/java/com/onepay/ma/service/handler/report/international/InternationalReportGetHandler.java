package com.onepay.ma.service.handler.report.international;

import com.onepay.ma.service.handler.report.international.impl.InternationalReportGetHandlerImpl;
import com.onepay.ma.service.service.InternationalReportService;
import com.onepay.ma.service.service.MerchantService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalReportGetHandler extends Handler<RoutingContext>  {
    static InternationalReportGetHandlerImpl create(InternationalReportService internationalReportService, MerchantService merchantService){
        return new InternationalReportGetHandlerImpl(internationalReportService, merchantService);
    }
}
