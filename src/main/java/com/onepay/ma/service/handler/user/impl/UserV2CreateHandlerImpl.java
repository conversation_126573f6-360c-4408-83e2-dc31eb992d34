package com.onepay.ma.service.handler.user.impl;


import java.security.SecureRandom;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.user.UserV2CreateHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.RoleData;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.MailUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;

import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by tuydv on 12/04/18.
 */
public class UserV2CreateHandlerImpl implements UserV2CreateHandler {
    private static Logger LOGGER = Logger.getLogger(UserV2CreateHandlerImpl.class.getName());
    private final static Gson gson = new Gson();

    private static final String ALPHA_CAPS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String ALPHA = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMERIC = "**********";
    private static final String SPECIAL_CHARS = "@$!%?&";
    private static final String ALL_CHARACTERS = ALPHA + ALPHA_CAPS + NUMERIC + SPECIAL_CHARS;
    private static SecureRandom random = new SecureRandom();

    public UserV2CreateHandlerImpl(UserServiceV2 userService) {
        this.userService = userService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }

        String body = rc.getBodyAsString();
        UserData userV2 = gson.fromJson(body, UserData.class);
        userV2.setPhone(Utils.convertMobileBumber(userV2.getPhone()));
        String basePass = userV2.getPass();
        String genPass = (basePass == null || basePass.trim().isEmpty()) ? generatePassword() : basePass;
        if (userV2.getPass() == null || userV2.getPass().trim().isEmpty()) {
            userV2.setPass(userService.encodePassword(genPass, 1));
        }
        validateUser(userV2);
        String pass = userService.encodePassword(userV2.getPass(), 2);
        userV2.setPass(pass);
        userV2.setCreateId(userId);
        userV2.setAuthLevel(userV2.getAuthLevel() == null ? 1 : userV2.getAuthLevel());
        Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                .flatMap(sqlConnection -> {
                    connectionBackUp = sqlConnection;
                    return userService.getUserExisted(connectionBackUp, userV2.getEmail(), userV2.getPhone()).flatMap(userExisted -> {
                        // if(userExisted != null){
                        // throw IErrors.INVALID_USER_EXISTED;
                        // }
                        return this.checkPermission(connectionBackUp, userV2, userId).flatMap(aVoid1-> {
                            return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return userService.insert(connectionBackUp, userV2).map(u -> {
                                    // send email to user email
                                    String subject = "[OnePay] Thông báo kích hoạt tài khoản quản trị giao dịch / Merchant Administration account activation";
                                    String bodyM = "Kính gửi Quý ĐVCNTT,<br><br>OnePay xin thông báo tài khoản truy cập hệ thống Quản trị giao dịch trực tuyến OnePay đã được kích hoạt. Hệ thống được sử dụng để tìm kiếm, báo cáo, hoàn trả (nếu có) các giao dịch thanh toán qua OnePay.<br><br>"
                                            + "Để truy cập hệ thống, vui lòng sử dụng các thông tin sau:<br><br>"
                                            + "Địa chỉ truy cập: " + "<a href='" + PropsUtil.get("ma.url", "https://ma.onepay.vn") + "'>https://ma.onepay.vn</a><br/>"
                                            + "Email đăng nhập: " + userV2.getEmail() + "<br>"
                                            // + "Mật khẩu: <b>" +((basePass == null || basePass.trim().isEmpty()) ?  genPass  : basePass) + "</b><br><br>"
                                            + "Quý ĐVCNTT vui lòng đổi mật khẩu ngay sau khi đăng nhập lần đầu theo hướng dẫn "
                                            + "<a href='https://onepay.vn/documents/OnePay_Huong_dan_doi_mat_khau.pdf'>tại đây.</a><br/>"
                                            + "Nếu cần thêm thông tin hỗ trợ, Quý ĐVCNTT vui lòng liên hệ bộ phận Hỗ trợ khách hàng OnePay.<br/><br/>"
                                            + "Trân trọng cảm ơn!<br/><br/>"
                                            + "<b>Bộ phận Hỗ trợ khách hàng OnePay</b><br/>"
                                            + "Tầng 6, tòa tháp BIDV, 194 Trần Quang Khải, Hoàn Kiếm, Hà Nội<br/>"
                                            + "Điện thoại: (84) 24 ******** (máy lẻ. 118, 119, 120, 121, 125)<br/>"
                                            + "<EMAIL> | www.onepay.vn<br/><br/>"
                                            + "---------------------------------------------------<br/>"
                                            + "<i>"
                                            + "Dear Valued Merchant,<br/><br/>"
                                            + "OnePay would like to inform that your account to access OnePay Merchant Administration portal has been activated. You can use this portal to search, get report, refund (if any) for all the transactions which are processed by OnePay.<br/><br/>"
                                            + "To access the portal, please sign in with the following credentials:<br/><br/>"
                                            + "Address: " + "<a href='" + PropsUtil.get("ma.url", "https://ma.onepay.vn") + "'>https://ma.onepay.vn</a><br/>"
                                            + "Email: " + userV2.getEmail() + "<br>"
                                            // + "Password: <b>" +((basePass == null || basePass.trim().isEmpty()) ?  genPass  : basePass) + "</b><br><br>"
                                            + "Please change your password immediately after logging in for the first time, following "
                                            + "<a href='https://onepay.vn/documents/OnePay_How_to_change_password.pdf'>this guide.</a><br/>"
                                            + "If you need further assistance, please contact OnePay Service Support Department.<br/><br/>"
                                            + "Best Regards,<br/><br/>"
                                            + "<b>OnePay Service Support Department</b><br/>"
                                            + "6th Floor, BIDV Tower, 194 Tran Quang Khai Street, Hoan Kiem, Hanoi<br/>"
                                            + "Phone: (84) 24 ******** (ext. 118, 119, 120, 121, 125)<br/>"
                                            + "<EMAIL> | www.onepay.vn<br/><br/>"
                                            + "</i>";
                                    try {
                                        MailUtil.sendMail(userV2.getEmail(), subject, bodyM);
                                        MailUtil.sendMail("<EMAIL>", subject, bodyM);
                                    } catch (Exception e) {
                                        LOGGER.log(Level.SEVERE, "Unable to sent email ", e);
                                        throw IErrors.INTERNAL_SERVER_ERROR;
                                    }
                                    return u;
                                });
                            });
                        });
                    });
                }).subscribe(user -> {
                    if (user == null) {
                        if (connectionBackUp != null) {
                            connectionBackUp.rollbackObservable();
                        }
                        throw IErrors.VALIDATION_ERROR;
                    }
                    if (connectionBackUp != null) {
                        connectionBackUp.commitObservable();
                    }
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
                    rc.next();
                }, throwable -> {
                    if (connectionBackUp != null) {
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
    }


    public static String generatePassword() {
        LOGGER.info("create new user - generatePassword");
        List<Character> chars = new ArrayList<>();
        chars.add(ALPHA.charAt(random.nextInt(ALPHA.length())));
        chars.add(ALPHA_CAPS.charAt(random.nextInt(ALPHA_CAPS.length())));
        chars.add(NUMERIC.charAt(random.nextInt(NUMERIC.length())));
        chars.add(SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length())));

        for (int i = 4; i < 12; i++) {
            chars.add(ALL_CHARACTERS.charAt(random.nextInt(ALL_CHARACTERS.length())));
        }

        // Trộn ngẫu nhiên
        Collections.shuffle(chars);

        StringBuilder password = new StringBuilder();
        for (char c : chars) {
            password.append(c);
        }

        return password.toString();
    }


    /**
     * check permission of userV2
     * @param connOnline
     * @param userV2
     * @param loginUserId
     * @return
     * 
     * 1. check merchant Permission
     * 2. check role Permission
     */
    private Observable<Void> checkPermission(SQLConnection connOnline, UserData userV2, String loginUserId){
        return this.userService.getUserDataBySId(connOnline, loginUserId).flatMap(loginUser -> {
            // if(loginUser.getMerchants().size() == 0){
            //     throw IErrors.FORBIDDEN;
            // }
            // create map merchant id_type 
            Map<String, Merchant> loginMerchantIdMap = new HashMap<>();
            for(Merchant merchant : loginUser.getMerchants()){
                loginMerchantIdMap.put(merchant.getMerchant_id()+"_"+merchant.getType(), merchant);
            }

            // loop input merchant id, check if merchant is in loginMerchantIdMap
            for(Merchant inputMerchant : userV2.getMerchants()){
                String key = inputMerchant.getMerchant_id()+"_"+inputMerchant.getType();
                if(!loginMerchantIdMap.containsKey(key)){
                    LOGGER.log(Level.SEVERE, "[ CHECK PERMISSION ] Merchant not found in login user merchants: {0}", key);
                    throw IErrors.FORBIDDEN;
                }
            }

            // check role permission
            if(loginUser.getRoles().size() == 0){
                LOGGER.log(Level.SEVERE, "[ CHECK PERMISSION ] Login user has no roles");
                throw IErrors.FORBIDDEN;
            }

            // create map role id
            Map<Integer, RoleData> loginRoleIdMap = new HashMap<>();
            for(RoleData role : loginUser.getRoles()){
                loginRoleIdMap.put(role.getRole_id(), role);
            }

            // loop input role id, check if role is in loginRoleIdMap
            for(RoleData inputRole : userV2.getRoles()){
                if(!loginRoleIdMap.containsKey(inputRole.getRole_id())){
                    LOGGER.log(Level.SEVERE, "[ CHECK PERMISSION ] Role not found in login user roles: {0}", inputRole.getRole_id());
                    throw IErrors.FORBIDDEN;
                }
            }

            LOGGER.log(Level.INFO, "[ CHECK PERMISSION ] All checks passed successfully");
            return Observable.just(null);
        });
    }

    private void validateUser(UserData userV2) {

        if (userV2 == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        if (nullOrEmpty(userV2.getName())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => FULLNAME IS EMPTY ");
            throw IErrors.VALIDATION_FULLNAME_ERROR;
        }
        if (nullOrEmpty(userV2.getEmail())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => EMAIL IS EMPTY ");
            throw IErrors.VALIDATION_EMAIL_ERROR;
        }
        if (nullOrEmpty(userV2.getPhone())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => MOBILE IS EMPTY ");
            throw IErrors.VALIDATION_MOBILE_ERROR;
        }
        if (nullOrEmpty(userV2.getPass())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => PASSWORD IS EMPTY ");
            throw IErrors.VALIDATION_PASSWORD_ERROR;
        }
        if (!Utils.validateEmail(userV2.getEmail())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => EMAIL FORMAT ERRR ");
            throw IErrors.VALIDATION_EMAIL_ERROR;
        }

        if (StringUtils.isNotBlank(userV2.getDescription()) && !Utils.validateText(userV2.getDescription())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => DESC FORMAT ERRR");
            throw IErrors.VALIDATION_ERROR;
        }

        if (StringUtils.isNotBlank(userV2.getJobTitle()) && !Utils.validateText(userV2.getJobTitle())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => JOBTITLE FORMAT ERRR");
            throw IErrors.VALIDATION_JOB_TITLE_ERROR;
        }

        if (StringUtils.isNotBlank(userV2.getName()) && !Utils.validateText(userV2.getName())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => NAME FORMAT ERRR");
            throw IErrors.VALIDATION_FULLNAME_ERROR;
        }

        // if merchant id in userV2.getMerchants() contain "ALL" then forbidden
        for(Merchant merchant : userV2.getMerchants()){
            if(merchant.getMerchant_id()!=null && merchant.getMerchant_id().equals("ALL")){
                throw IErrors.FORBIDDEN;
            }
        }

    }

    public boolean nullOrEmpty(String str) {
        boolean reuslt = false;
        if (str == null || str.trim().equals("")) {
            reuslt = true;
        }
        return reuslt;
    }

    private SQLConnection connectionBackUp;
    private UserServiceV2 userService;

}
