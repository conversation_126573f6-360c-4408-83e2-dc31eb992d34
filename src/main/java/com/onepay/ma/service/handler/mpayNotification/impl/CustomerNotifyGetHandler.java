package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.notification.NotificationSearchQuery;
import com.onepay.ma.service.service.notification.CustomerAppNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 26-Mar-18.
 */
@Component
public class CustomerNotifyGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.request().getParam("user_id");

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (userId != null) {

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 10 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            String language = rc.request().getParam(ParamsPool.LANGUAGE) == null ? "en" : String.valueOf(rc.request().getParam(ParamsPool.LANGUAGE));

            NotificationSearchQuery query = new NotificationSearchQuery();
            query.setUserId(userId);
            query.setPage(page);
            query.setPageSize(pageSize);
            query.setLanguage(language);

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return customerAppNotifyService.getNotifyByDspId(connBackup, query);
                    }).subscribe(mpayNotificationBaseList -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayNotificationBaseList);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
            ;
        } else {
            throw IErrors.RESOURCE_NOT_FOUND;
        }

    }

    @Autowired
    private CustomerAppNotifyService customerAppNotifyService;
}
