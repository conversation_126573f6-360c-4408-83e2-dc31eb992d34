package com.onepay.ma.service.handler.promotion;

import com.onepay.ma.service.handler.promotion.impl.PromotionDeleteHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionDeleteHandler extends Handler<RoutingContext> {
    static PromotionDeleteHandlerImpl create(PromotionService promotionService, ApprovalService approvalService){
        return new PromotionDeleteHandlerImpl(promotionService, approvalService);

    }
}
