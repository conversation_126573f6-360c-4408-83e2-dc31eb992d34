package com.onepay.ma.service.handler.refund.domestic;

import com.onepay.ma.service.handler.refund.domestic.impl.DomesticRefundApprovalPatchHandler;
import com.onepay.ma.service.handler.refund.domestic.impl.DomesticRefundPatchHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DomesticRefundHandler {
    @Autowired
    DomesticRefundApprovalPatchHandler domesticRefundApprovalPatchHandler;
    @Autowired
    DomesticRefundPatchHandler domesticRefundPatchHandler;

    public Handler patchApproval(){
        return domesticRefundApprovalPatchHandler;
    }

    public Handler patchRefund() {
        return  domesticRefundPatchHandler;
    }
}
