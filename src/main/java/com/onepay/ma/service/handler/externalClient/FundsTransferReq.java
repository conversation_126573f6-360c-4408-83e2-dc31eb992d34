package com.onepay.ma.service.handler.externalClient;

public class FundsTransferReq {
    private String userId;
    private String xUserId;
    private String xRequestId;
    private String merchantId;
    private String merchantAccount;
    private String receiptAccountId;
    private String receiptBank;
    private String holderName;
    private String amount;
    private String remark;
    private String fundsTransferId;
    private String userName;
    private String password;
    private String createId;
    private String createName;

    public FundsTransferReq() {
    }

    public FundsTransferReq(String userId, String xUserId, String xRequestId, String merchantId, String merchantAccount, String receiptAccountId, String receiptBank, String holderName, String amount, String remark, String fundsTransferId, String userName, String password, String createId, String createName) {
        this.userId = userId;
        this.xUserId = xUserId;
        this.xRequestId = xRequestId;
        this.merchantId = merchantId;
        this.merchantAccount = merchantAccount;
        this.receiptAccountId = receiptAccountId;
        this.receiptBank = receiptBank;
        this.holderName = holderName;
        this.amount = amount;
        this.remark = remark;
        this.fundsTransferId = fundsTransferId;
        this.userName = userName;
        this.password = password;
        this.createId = createId;
        this.createName = createName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getxUserId() {
        return xUserId;
    }

    public void setxUserId(String xUserId) {
        this.xUserId = xUserId;
    }

    public String getxRequestId() {
        return xRequestId;
    }

    public void setxRequestId(String xRequestId) {
        this.xRequestId = xRequestId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public String getReceiptAccountId() {
        return receiptAccountId;
    }

    public void setReceiptAccountId(String receiptAccountId) {
        this.receiptAccountId = receiptAccountId;
    }

    public String getReceiptBank() {
        return receiptBank;
    }

    public void setReceiptBank(String receiptBank) {
        this.receiptBank = receiptBank;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFundsTransferId() {
        return fundsTransferId;
    }

    public void setFundsTransferId(String fundsTransferId) {
        this.fundsTransferId = fundsTransferId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}
