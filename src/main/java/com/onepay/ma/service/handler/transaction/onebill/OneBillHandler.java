package com.onepay.ma.service.handler.transaction.onebill;

import com.onepay.ma.service.handler.transaction.onebill.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OneBillHandler {

    @Autowired
    private OneBillTransactionGetHandler oneBillTransactionGetHandler;

    @Autowired
    private OneBillClientGetHandler oneBillClientGetHandler;

    @Autowired
    private OneBillDownloadHandler oneBillDownloadHandler;

    @Autowired
    private OneBillTransHistoryGetHandler  oneBillTransHistoryGetHandler;

    @Autowired
    private OneBillPatchHandler oneBillPatchHandler;

    public Handler download(){
        return oneBillDownloadHandler;
    }

    public Handler get(){
        return oneBillTransactionGetHandler;
    }

    public Handler patch(){
        return oneBillPatchHandler;
    }

    public Handler getHistory(){
        return oneBillTransHistoryGetHandler;
    }

    public Handler listClient(){
        return oneBillClientGetHandler;
    }
}
