package com.onepay.ma.service.handler.mpayPromotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReport;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReportQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.mpayPromotion.MpayPrReportService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 01-Feb-18.
 */
@Component
public class MpayPrReportGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ MPAY  PR Report GET ERROR ] => USER ID NOT FOUND");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ MPAY PR REPORT GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 6) {
            LOGGER.log(Level.SEVERE, "[ MPAY PR REPORT GET ]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        String interval = rc.request().getParam(ParamsPool.INTERVAL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.INTERVAL));

        MpayPrReportQuery query = new MpayPrReportQuery();
        query.setMerchant_id(merchantId);
        query.setFrom_date(fromDate);
        query.setTo_date(toDate);
        query.setInterval(interval);
        Observable<BaseList<MpayPrReport>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            // find user by S-id
                                            return userService.getOneAm(connBackup, userId).flatMap(user -> {

                                                return merchantService.list(connOnline, connBackup, merchantId, userId, "mpay").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    query.setMerchant_id(String.join(",", merchantIdList));

                                                    return this.mpayPrReportService.list(connOnline, query);
                                                });
                                            });
                                        });
                            });
                });

        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }



    @Autowired
    private UserService userService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MpayPrReportService  mpayPrReportService;

    private static Logger LOGGER = Logger.getLogger(MpayPrReportGetHandler.class.getName());

}
