package com.onepay.ma.service.handler.permission;

import com.onepay.ma.service.handler.permission.impl.PermissionPostHandlerImpl;
import com.onepay.ma.service.service.PermissionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface PermissionPostHandler extends Handler<RoutingContext> {
    static PermissionPostHandlerImpl create(PermissionService permissionService){
        return new PermissionPostHandlerImpl(permissionService);
    }
}
