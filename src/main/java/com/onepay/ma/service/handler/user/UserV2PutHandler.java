package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserV2PutHandlerImpl;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;  

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserV2PutHandler extends Handler<RoutingContext> {
    static UserV2PutHandlerImpl create(UserServiceV2 userService, CacheGuava cacheGuava, MerchantService merchantService){
        return new UserV2PutHandlerImpl(userService,  cacheGuava, merchantService);
    }
}
