package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostInternationalReportHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalReportService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FilePostInternationalReportHandlerImpl implements FilePostInternationalReportHandler {

    public FilePostInternationalReportHandlerImpl(InternationalReportService internationalReportService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.internationalReportService = internationalReportService;
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.cacheGuava = cacheGuava;
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL REPORT POST ] => TOTAL NOT FOUND");
            throw  IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        InternationalReportPostFile mapBody =  gson.fromJson(body, InternationalReportPostFile.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL REPORT POST ] => TOTAL NOT FOUND");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "international").flatMap(merchants -> {
                                                InternationalReportParameterFile parameter = new InternationalReportParameterFile();
                                                parameter.setKeywords(mapBody.getKeywords());
                                                parameter.setAcquirerId(mapBody.getAcquirer_id());
                                                parameter.setFromDate(mapBody.getFrom_date());
                                                parameter.setToDate(mapBody.getTo_date());
                                                parameter.setCardType(mapBody.getCard_type());
                                                parameter.setInterval(mapBody.getInterval());
                                                parameter.setMerchantId(mapBody.getMerchant_id());
                                                parameter.setCurrency(mapBody.getCurrency());
                                                parameter.setVersion(mapBody.getVersion());
                                                parameter.setLang(mapBody.getLang());

                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                parameter.setMerchantList(merchantIdList);
                                                return insertPostData(connBackUp, parameter, rc, userId, requestData, mapBody);

                                            });
                                        });
                            });
        }).subscribe(map -> {
                    if(connectBackUp != null){
                        connectBackUp.commitObservable();
                    }
                InternationalReportParameterFile parameter = (InternationalReportParameterFile) map.get("parameter");
                Message<InternationalReportParameterFile> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, rc.request().path(), downloadFastOutQueue, downloadFastInQueue);
            QueueProducer.sendMessage(message);

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }

    /**
     * insert post data
     * @param connBackUp
     * @param parameter
     * @param rc
     * @param userId
     * @param requestData
     * @param mapBody
     */
    private Observable<Map> insertPostData(SQLConnection connBackUp,InternationalReportParameterFile parameter, RoutingContext rc, String userId, Map requestData, InternationalReportPostFile mapBody){


            String fileName = "international" + StringPool.UNDERLINE + "report" + StringPool.UNDERLINE;
            long date = new java.util.Date().getTime();
            fileName += date;
            String fileHashName = "";
            Map data = new HashMap();
            data.put("file_name", fileName);
            data.put("parameter", parameter);

            try {
                fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
            } catch (NoSuchAlgorithmException e) {
                rc.fail(e);
            } catch (UnsupportedEncodingException e) {
                rc.fail(e);
            }
            requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
            requestData.put(ParamsPool.FILE_NAME, fileName);
            FileDownload fileDownload = new FileDownload();
            fileDownload.setUser(userId);
            fileDownload.setFile_type("international_report");
            fileDownload.setExt("xls");
            fileDownload.setFile_name(fileName);
            fileDownload.setFile_hash_name(fileHashName);
            fileDownload.setConditions(gson.toJson(mapBody));
        return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });



    }

    /**
     * get list report by currency data list
     * @param sqlConnection
     * @param listCurrency
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getTotalReportCurrency(SQLConnection sqlConnection, List<String> listCurrency, Map returnMap, InternationalReportParameter parameter, int index) {

        String currency = listCurrency.get(index);
        final int finalIndex = index;
        return Observable.just(currency).flatMap(currencyData -> {
            parameter.setCurrency(currencyData);
            //insert approval for user group
            return internationalReportService.getTotalByCurrency(sqlConnection, parameter).flatMap(integer -> {
//                if(integer > 45000){
//                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
//                }
                returnMap.put(currencyData, integer);
                if(finalIndex < listCurrency.size() - 1){
                    return getTotalReportCurrency(sqlConnection, listCurrency, returnMap, parameter, finalIndex + 1);
                }else{
                    return Observable.just(returnMap);
                }
            });
        });

    }


    /**
     * get list report by merchant data list
     * @param sqlConnection
     * @param listMerchant
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getTotalReportByMerchant(SQLConnection sqlConnection, List<String> listMerchant, Map returnMap, InternationalReportParameter parameter, int index) {
        if(listMerchant.size() <= 0){
            return Observable.just(returnMap);
        }
        String merchant = listMerchant.get(index);
        final int finalIndex = index;
        return Observable.just(merchant).flatMap(merchantData -> {
            parameter.setMerchantId(merchantData);
            //insert approval for user group
            return internationalReportService.getTotalByMerchant(sqlConnection, parameter).flatMap(integer -> {
//                if(integer > 45000){
//
//                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
//                }
                returnMap.put(merchantData, integer);
                if(finalIndex >= listMerchant.size() - 1){
                    return Observable.just(returnMap);
                }else{
                    return getTotalReportByMerchant(sqlConnection, listMerchant, returnMap, parameter, finalIndex + 1);
                }
            });
        });

    }

    private InternationalReportService internationalReportService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private CacheGuava cacheGuava;

    private SQLConnection connectBackUp;

    private MerchantService merchantService;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(FilePostInternationalReportHandler.class.getName());
}
