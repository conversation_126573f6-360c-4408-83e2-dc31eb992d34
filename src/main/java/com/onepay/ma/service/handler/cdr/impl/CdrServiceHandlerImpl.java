package com.onepay.ma.service.handler.cdr.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.cdr.CdrServiceGetHandler;
import com.onepay.ma.service.models.CdrServiceModel;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.*;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/2/16.
 */
public class CdrServiceHandlerImpl implements CdrServiceGetHandler {

    public CdrServiceHandlerImpl(CdrService cdrService) {
        this.cdrService = cdrService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();

        JDBCClient  clientCdr = rc.get(ParamsPool.CDR_DATASOURCE_NAME);
        String source = request.getParam(ParamsPool.SOURCE_CDR) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SOURCE_CDR));
        String[] sourceList = source.split(",");
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientCdr), f-> f.dispose()).flatMap(connCdr -> {
            return getListServiceBySource(connCdr, Arrays.asList(sourceList), new ArrayList<>(), 0);
        }).subscribe(cdrServiceModels -> {
            Map returnValue = new HashMap();
            returnValue.put("services", cdrServiceModels);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    private Observable<List<CdrServiceModel>> getListServiceBySource(SQLConnection sqlConn, List<String> sourceList, List<CdrServiceModel> listService, int index){
        if(sourceList.size() <= 0){
            return Observable.just(listService);
        }
        final int finalIndex = index;
        String source = sourceList.get(index);
        return Observable.just(source).flatMap(s -> {
            if(s.isEmpty()){
                return  Observable.just(listService);
            }
            return  cdrService.listService(sqlConn, s, StringPool.BLANK).flatMap(cdrServiceModels -> {
                listService.addAll(cdrServiceModels);
                if(finalIndex  >= sourceList.size() - 1){
                    return Observable.just(listService);
                }else{
                    return getListServiceBySource(sqlConn, sourceList, listService, finalIndex + 1);
                }
            });
        });
    }


    private CdrService cdrService;

}
