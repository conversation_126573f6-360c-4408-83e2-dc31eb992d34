package com.onepay.ma.service.handler.transaction.financial;

import com.onepay.ma.service.handler.transaction.financial.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tuydv on 09-5-18.
 */
@Component
public class FinancialTransactionHandler {

    @Autowired
    private FinancialTransactionGetHandlerImpl financialTransactionGetHandler;
    @Autowired
    private SamsungFinancialTransactionGetHandler samsungFinancialTransactionGetHandler;

    public Handler get() {
        return financialTransactionGetHandler;
    }

    public Handler getSSTrans() {
        return samsungFinancialTransactionGetHandler;
    }
}
