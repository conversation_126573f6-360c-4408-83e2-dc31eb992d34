package com.onepay.ma.service.handler.transaction.domestic;

import com.onepay.ma.service.handler.transaction.domestic.impl.DomesticTransactionGetHandler;
import com.onepay.ma.service.handler.transaction.domestic.impl.DomesticTransactionHistoryGetHandler;
import com.onepay.ma.service.handler.transaction.domestic.impl.DomesticTransactionPatchHandler;
import com.onepay.ma.service.handler.transaction.domestic.impl.SamsungDomesticTransactionGetHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DomesticTransactionHandler{

    @Autowired
    SamsungDomesticTransactionGetHandler samsungDomesticTransactionGetHandler;

    @Autowired
    DomesticTransactionGetHandler domesticTransactionGetHandler;

    @Autowired
    DomesticTransactionHistoryGetHandler domesticTransactionHistoryGetHandler;

    @Autowired
    DomesticTransactionPatchHandler domesticTransactionPatchHandler;

    public Handler getSamsungTransaction() {
        return samsungDomesticTransactionGetHandler;
    }

    public Handler get() {
        return domesticTransactionGetHandler;
    }
    public Handler patch() {
        return domesticTransactionPatchHandler;
    }
    public Handler getHistory() {
        return domesticTransactionHistoryGetHandler;
    }
}
