package com.onepay.ma.service.handler.refund.domestic.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.refund.domestic.DomesticRefundGetHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import com.onepay.ma.service.util.Utils;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class DomesticRefundGetHandlerImpl implements DomesticRefundGetHandler {

    public DomesticRefundGetHandlerImpl(DomesticRefundService domesticRefundService, MerchantService merchantService, ServerConfig serverConfig) {
        this.domesticRefundService = domesticRefundService;
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        final HttpServerRequest request = rc.request();
        String transactionId = request.getParam("transactionId");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {

                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            int checkFromDateDiffToDate = FunctionUtil.compareDateByMonth(oFromDate, oToDate, 6);
            int checkFromdateTooOld = FunctionUtil.compareDateByMonth(oFromDate, new Date(), 24);

            // Validate fromdate, todate too long
            if (checkFromDateDiffToDate < 0) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND ] => VALIDATION TOO LARGE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            // Validate fromdate after 2 years ago
            if (checkFromdateTooOld < 0) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC REFUND GET OVER 2 YEARS AGO]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_OLD_ERROR;
            }

            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            int transId = (rc.request().getParam(ParamsPool.TRANSACTION_ID) == null || rc.request().getParam(ParamsPool.TRANSACTION_ID).isEmpty()) ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));


            int status = (rc.request().getParam(ParamsPool.STATUS) == null || rc.request().getParam(ParamsPool.STATUS).isEmpty())? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.STATUS));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            if (pageSize > Utils.getPageSize()) {
                pageSize = Utils.getPageSize();
            }
            String acquirerId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID)).replace("|",",");

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));
            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

            String version=rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));

            String finalCardNumber = FunctionUtil.isBeginIsACard(cardNumber) && cardNumber.contains("*") ? cardNumber.replaceAll("\\*", "0") : cardNumber;

            DomesticRefundParameter parameter = new DomesticRefundParameter();
            parameter.setFromDate(fromDate);
            parameter.setToDate(toDate);
            parameter.setPage(page);
            parameter.setPageSize(pageSize);
            parameter.setTransactionId(transId);
            parameter.setStatus(status);
            parameter.setMerchantTransactionReference(merchantTransactionRef);
            parameter.setOrderInfo(orderInfo);
            parameter.setAcquirerId(acquirerId);
            parameter.setKeywords(keywords);
            parameter.setCardNumber(cardNumber);
            parameter.setCurrency(currency);
            parameter.setVersion(version);

            // IN CASE COMPARE WITH HASHED FULL CARD NUMBER
            if (FunctionUtil.isDomesticCardData(finalCardNumber)) {
                String encryptedCardNumber = FunctionUtil.oneSMHmac(finalCardNumber, serverConfig);
                parameter.setCardNumber(String.valueOf(encryptedCardNumber));
            }

            Observable<Transactions<DomesticRefund>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        //get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    //get back up connection
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return merchantService.list(connOnline, connBackUp, merchantId, userId, "domestic").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    parameter.setMerchantId(String.join(",", merchantIdList));

                                                    // LOGGER.log(Level.SEVERE, "START LIST DOMESTIC REFUND with parameter :" + gson.toJson(parameter));
                                                    return domesticRefundService.list(connReadOnly,connOnline,connBackUp, parameter);
                                                });
                                            });

                                });
                    });
            obs.subscribe(transactions -> {
                // LOGGER.log(Level.SEVERE, "FINAL RESULT GET DOMESTIC REFUND TRANS :" + gson.toJson(transactions));
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return domesticRefundService.get(connOnline, connBackup, transactionId);
                                });
                    }).subscribe(domesticRefund -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }


    }


    private DomesticRefundService domesticRefundService;

    private final static Logger LOGGER = Logger.getLogger(DomesticRefundGetHandler.class.getName());

    private MerchantService merchantService;

    private ServerConfig serverConfig;

    private Gson gson =  new Gson();
}
