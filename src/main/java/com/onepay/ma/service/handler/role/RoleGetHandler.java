package com.onepay.ma.service.handler.role;


import com.onepay.ma.service.handler.role.impl.RoleGetHandlerImpl;
import com.onepay.ma.service.service.RoleService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface RoleGetHandler extends Handler<RoutingContext> {
    static RoleGetHandlerImpl create(RoleService roleService){
        return new RoleGetHandlerImpl(roleService);
    }
}
