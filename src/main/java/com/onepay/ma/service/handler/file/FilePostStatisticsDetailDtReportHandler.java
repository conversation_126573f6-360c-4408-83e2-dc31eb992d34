package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostStatisticsDetailDtReportHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.StatisticsReportService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;


/**
 * Created by tuydv on 16/06/18.
 */
public interface FilePostStatisticsDetailDtReportHandler extends Handler<RoutingContext>  {
    static FilePostStatisticsDetailDtReportHandlerImpl create(StatisticsReportService statisticsReportService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue){
        return new FilePostStatisticsDetailDtReportHandlerImpl(statisticsReportService, serverConfig, downloadFastIn, downloadFastOut, fileService, downloadSlowInQueue, downloadSlowOutQueue);

    }
}
