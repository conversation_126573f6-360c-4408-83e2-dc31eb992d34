package com.onepay.ma.service.handler.permission;

import com.onepay.ma.service.handler.permission.impl.PermissionPatchHandlerImpl;
import com.onepay.ma.service.service.PermissionRoleService;
import com.onepay.ma.service.service.PermissionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface PermissionPatchHandler extends Handler<RoutingContext> {
    static PermissionPatchHandlerImpl create(PermissionService permissionService, PermissionRoleService permissionRoleService){
        return new PermissionPatchHandlerImpl(permissionService, permissionRoleService);
    }
}
