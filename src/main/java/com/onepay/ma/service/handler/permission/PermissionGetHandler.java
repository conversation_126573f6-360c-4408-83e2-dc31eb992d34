package com.onepay.ma.service.handler.permission;

import com.onepay.ma.service.handler.permission.impl.PermissionGetHandlerImpl;
import com.onepay.ma.service.service.PermissionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface PermissionGetHandler extends Handler<RoutingContext> {
    static PermissionGetHandlerImpl create(PermissionService permissionService){
        return new PermissionGetHandlerImpl(permissionService);
    }
}
