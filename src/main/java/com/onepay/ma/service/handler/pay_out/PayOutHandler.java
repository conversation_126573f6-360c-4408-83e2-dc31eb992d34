package com.onepay.ma.service.handler.pay_out;

import com.onepay.ma.service.handler.pay_out.pay_outImpl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PayOutHandler {

    @Autowired
    private FundsTransHistoryHandlerImpl fundsTransHistoryHandlerImpl;

    @Autowired
    private FundsTransHistoryPendingHandlerImpl fundsTransHistoryPendingHandlerImpl;

    @Autowired
    private FundsChangeStatusHandlerImpl fundsChangeStatusHandlerImpl;

    @Autowired
    private DownloadFundsTransHisHandlerImpl downloadFundsTransHisHandlerImpl;

    @Autowired
    private MerchantAccountHandlerImpl merchantAccountHandlerImpl;

    @Autowired
    private ReceivedBankHandlerImpl receivedBankHandlerImpl;

    @Autowired
    private TransferTransactionHandlerImpl transferTransactionHandlerImpl;

    @Autowired
    private FundsTransHistoryDetailHandlerImpl fundsTransHistoryDetailHandlerImpl;

    @Autowired
    private SummaryFundsTransHandlerImpl summaryFundsTransHandlerImpl;

    @Autowired
    private PartnerInfoHandlerImpl partnerInfoHandlerImpl;

    @Autowired
    private DownloadSummaryFundsTransHandlerImpl downloadSummaryFundsTransHandler;

    @Autowired
    private BatchDetailGetHandlerImpl batchDetailGetHandlerImpl;

    @Autowired
    private BatchDetailDownloadHandlerImpl batchDetailDownloadHandlerImpl;

    @Autowired
    private BatchGetHandlerImpl batchGetHandlerImpl;

    @Autowired
    private BatchHandlerImpl batchHandlerImpl;

    @Autowired
    private BatchChangeStatusHandlerImpl batchChangeStatusHandlerImpl;

    @Autowired
    private ReceivedInfoHandlerImpl receivedInfoHandlerImpl;

    @Autowired
    private OperatorHandlerImpl operatorHandlerImpl;

    @Autowired
    private OperatorCreateHandlerImpl operatorCreateHandlerImpl;

    @Autowired
    private OperatorUpdateHandlerImpl operatorUpdateHandlerImpl;

    @Autowired
    private OperatorChangeStatusHandlerImpl operatorChangeStatusHandlerImpl;

    @Autowired
    private OperatorResetPassHandlerImpl operatorResetPassHandlerImpl;

    @Autowired
    private OperatorChangePassHandlerImpl operatorChangePassHandlerImpl;

    @Autowired
    private OperatorCheckExistHandlerImpl operatorCheckExistHandlerImpl;

    @Autowired
    private BatchCheckAuthenHandlerImpl batchCheckAuthenHandlerImpl;

    @Autowired
    private GetMerchantHandlerImpl getMerchantHandlerImpl;

    public FundsTransHistoryHandlerImpl getFundsTransHistory() {
        return fundsTransHistoryHandlerImpl;
    }

    public Handler getFundsTransHistoryForApproval  () { return fundsTransHistoryPendingHandlerImpl;}
    public Handler fundsChangeStatus() {return fundsChangeStatusHandlerImpl;}

    public FundsTransHistoryDetailHandlerImpl getFundsTransById() {
        return fundsTransHistoryDetailHandlerImpl;
    }

    public ReceivedBankHandlerImpl getReceivedBank() {
        return receivedBankHandlerImpl;
    }

    public MerchantAccountHandlerImpl getMerchantAccount() {
        return merchantAccountHandlerImpl;
    }

    public TransferTransactionHandlerImpl getTransferTransaction() {
        return transferTransactionHandlerImpl;
    }
    public Handler partnerInfo() {
        return partnerInfoHandlerImpl;
    }
    public ReceivedInfoHandlerImpl getReceivedInfo() {
        return receivedInfoHandlerImpl;
    }

    public SummaryFundsTransHandlerImpl getSummaryFundsTransHandler() {
        return summaryFundsTransHandlerImpl;
    }

    public DownloadSummaryFundsTransHandlerImpl getDownloadSummaryFundsTransHandler() {
        return downloadSummaryFundsTransHandler;
    }

    public BatchDetailGetHandlerImpl getBatchDetail() {
        return batchDetailGetHandlerImpl;
    }

    public Handler getDownloadBatchDetailCSV(){
        return batchDetailDownloadHandlerImpl;
    }

    public Handler checkAuthenBatchApproval() { return batchCheckAuthenHandlerImpl;}
    public BatchGetHandlerImpl getBatch() {
        return batchGetHandlerImpl;
    }

    public BatchHandlerImpl fundsTransferBatch() {
        return batchHandlerImpl;
    }

    public BatchChangeStatusHandlerImpl changeStatusBatchHandler() {
        return batchChangeStatusHandlerImpl;
    }
    public OperatorHandlerImpl getOperator() {
        return operatorHandlerImpl;
    }
    public Handler insertOperator() {
        return operatorCreateHandlerImpl;
    }
    public Handler updateOperator() {
        return operatorUpdateHandlerImpl;
    }
    public OperatorResetPassHandlerImpl resetPass() {
        return operatorResetPassHandlerImpl;
    }
    public Handler changeStatusOperator() { return operatorChangeStatusHandlerImpl; }
    public Handler changePass () { return operatorChangePassHandlerImpl; }
    public Handler receiveByUserAndPass() { return operatorCheckExistHandlerImpl;}
    public Handler getDownloadFundsTransHandler(){ return downloadFundsTransHisHandlerImpl;}
    public Handler getMerchantByUser() { return getMerchantHandlerImpl;}

}
