package com.onepay.ma.service.handler.user.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.user.UserV2CreateHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.RoleData;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.request.UpdateUserReq;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by tuydv on 12/04/18.
 */
public class UserV2PutHandlerImpl implements UserV2CreateHandler {
    private static Logger LOGGER = Logger.getLogger(UserV2PutHandlerImpl.class.getName());
    private final static Gson gson = new Gson();

    public UserV2PutHandlerImpl(UserServiceV2 userService, CacheGuava cacheGuava, MerchantService merchantService) {
        this.userService = userService;
        this.cacheGuava = cacheGuava;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String sId = rc.request().getParam("id");
        String body = rc.getBodyAsString();
        UpdateUserReq userReq = gson.fromJson(body, UpdateUserReq.class);
        validateUser(userReq);
        UserData userV2 = new UserData();
        userV2.setName(userReq.getName());
        userV2.setJobTitle(userReq.getJobTitle());
        userV2.setStatus(userReq.getStatus());
        userV2.setMerchants(userReq.getMerchants());
        userV2.setRoles(userReq.getRoles());
        Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                .flatMap(sqlConn -> {
                    this.sqlConnection = sqlConn;
                    return merchantService.listAll(sqlConn, userId)
                            .flatMap(merchants -> {
                                if (!merchants.isEmpty()) {
                                    String merchantIds = merchants.stream()
                                            .map(Merchant::getMerchant_id)
                                            .collect(Collectors.joining(","));
                                    return userService.listUserByMerchantIds(sqlConn, userId, merchantIds);
                                } else {
                                    return userService.listByCreateId(sqlConn, userId);
                                }
                            });
                })
                .flatMap(listUser -> {
                    boolean exists = listUser.stream().anyMatch(m -> m.getS_id().equalsIgnoreCase(sId));
                    if (!exists) {
                        return Observable.error(IErrors.VALIDATION_ERROR);
                    }

                    return Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                            .flatMap(sqlConn -> {
                                this.sqlConnection = sqlConn;
                                if (userV2.getStatus() == null) {
                                    return this.checkPermission(this.sqlConnection, userV2, userId).flatMap(aVoid -> {
                                        return this.userService.update(this.sqlConnection, userV2, sId);
                                    });
                                } else {
                                    return this.userService.update(this.sqlConnection, userV2, sId);
                                }
                            });
                })
                .subscribe(user -> {
                    if (user == null) {
                        throw IErrors.VALIDATION_ERROR;
                    }

                    if (this.sqlConnection != null) {
                        this.sqlConnection.commitObservable();
                    }

                    cacheGuava.get().invalidate(sId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
                    cacheGuava.get().invalidate(sId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);

                    rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
                    rc.next();
                }, throwable -> {
                    if (this.sqlConnection != null) {
                        this.sqlConnection.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

    }

    /**
     * check permission of userV2
     * 
     * @param connOnline
     * @param userV2
     * @param loginUserId
     * @return
     * 
     * 1. check merchant Permission
     * 2. check role Permission
     */
    private Observable<Void> checkPermission(SQLConnection connOnline, UserData userV2, String loginUserId) {
        return this.userService.getUserDataBySId(connOnline, loginUserId).flatMap(loginUser -> {
            // if(loginUser.getMerchants().size() == 0){
            // throw IErrors.FORBIDDEN;
            // }
            // create map merchant id_type
            Map<String, Merchant> loginMerchantIdMap = new HashMap<>();
            for (Merchant merchant : loginUser.getMerchants()) {
                loginMerchantIdMap.put(merchant.getMerchant_id() + "_" + merchant.getType(), merchant);
            }
            // loop input merchant id, check if merchant is in loginMerchantIdMap
            for (Merchant inputMerchant : userV2.getMerchants()) {
                if (!loginMerchantIdMap.containsKey(inputMerchant.getMerchant_id() + "_" + inputMerchant.getType())) {
                    throw IErrors.FORBIDDEN;
                }
            }

            // check role permission
            if (loginUser.getRoles().size() == 0) {
                throw IErrors.FORBIDDEN;
            }
            // create map role id
            Map<Integer, RoleData> loginRoleIdMap = new HashMap<>();
            for (RoleData role : loginUser.getRoles()) {
                loginRoleIdMap.put(role.getRole_id(), role);
            }

            // loop input role id, check if role is in loginRoleIdMap
            for (RoleData inputRole : userV2.getRoles()) {
                if (!loginRoleIdMap.containsKey(inputRole.getRole_id())) {
                    throw IErrors.FORBIDDEN;
                }
            }
            return Observable.just(null);
        });
    }

    private void validateUser(UpdateUserReq userV2) {
        if (userV2 == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }

        if (userV2.getStatus() == null) {
            if (userV2.getName() == null) {
                LOGGER.log(Level.SEVERE, "[ USER CREATE ] => FULLNAME IS EMPTY ");
                throw IErrors.VALIDATION_FULLNAME_ERROR;
            }
            // if merchant id in userV2.getMerchants() contain "ALL" then forbidden
            for (Merchant merchant : userV2.getMerchants()) {
                if (merchant.getMerchant_id().equals("ALL")) {
                    throw IErrors.FORBIDDEN;
                }
            }
        }
        if (StringUtils.isNotBlank(userV2.getJobTitle()) && !Utils.validateText(userV2.getJobTitle())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => JOBTITLE FORMAT ERRR");
            throw IErrors.VALIDATION_JOB_TITLE_ERROR;
        }

        if (StringUtils.isNotBlank(userV2.getName()) && !Utils.validateText(userV2.getName())) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => NAME FORMAT ERRR");
            throw IErrors.VALIDATION_FULLNAME_ERROR;
        }
    }

    private CacheGuava cacheGuava;

    private MerchantService merchantService;

    private UserServiceV2 userService;

    private SQLConnection sqlConnection;

}
