package com.onepay.ma.service.handler.mpayNotification.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.notification.CustomerAppNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 28-Mar-18.
 */
@Component
public class CustomerNotifyPatchHandler implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String notificationId = rc.request().getParam("id");

        if (notificationId != null) {

            String body = rc.getBodyAsString();
            PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
            if (mapRequest == null) {
                LOGGER.log(Level.SEVERE, "[ NOTIFICATION SEE PATCH  ] =>  SEE DATA EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }


            if (mapRequest.getPath().equals("/see")) {

                //get back up connection
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackup -> {
                            return this.appNotificationService.see(connBackup, Integer.valueOf(notificationId));
                        }).subscribe(aVoid -> {
                    rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

            } else {
                throw IErrors.VALIDATION_ERROR;
            }
        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Autowired
    private CustomerAppNotifyService appNotificationService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(CustomerNotifyPatchHandler.class.getName());

}
