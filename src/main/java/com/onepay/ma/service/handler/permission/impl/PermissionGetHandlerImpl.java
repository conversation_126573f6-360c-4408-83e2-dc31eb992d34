package com.onepay.ma.service.handler.permission.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.permission.PermissionGetHandler;
import com.onepay.ma.service.models.PermissionData;
import com.onepay.ma.service.models.TreePermission;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public class PermissionGetHandlerImpl implements PermissionGetHandler {

    public PermissionGetHandlerImpl(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String permissionId = rc.request().getParam("permissionId");
        //SQLConnection sqlConnection = rc.get(ParamsPool.CONNECTION_BACKUP);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if(permissionId == null) {
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                       return permissionService.list(connBackUp, keywords);
            }).subscribe(permissions -> {
                Map map = new HashMap<>();
                map.put("permissions", permissionToTreeData(1, permissions));
                rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return permissionService.get(connBackUp, Integer.parseInt(permissionId));
            }).subscribe(permission -> {
                if(permission == null){
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                rc.put(ParamsPool.HANDLER_DATA_RESULT, permission);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }

    /**
     *
     * @param parentId
     * @param list
     * @return
     */
    private static List<TreePermission> permissionToTreeData(int parentId, List<PermissionData> list){
        TreePermission treePermission = null;

        List<TreePermission> returnTree = new ArrayList<>();
        if(list == null) return returnTree;

        for (PermissionData permission : list){
            int parent =  permission.getParent_id();
            if(parent == parentId){
                treePermission = new TreePermission();
                treePermission.setPermission_id(permission.getPermission_id());
                treePermission.setParent_id(permission.getParent_id());
                treePermission.setPermission_name(permission.getPermission_name());
                treePermission.setDescription(permission.getDescription());
                treePermission.setPath_regex(permission.getPath_regex());
                treePermission.setOrder(permission.getOrder());
                treePermission.setState(permission.getState());
                treePermission.setIs_abstract(permission.isIs_abstract());
                treePermission.setController_url(permission.getController_url());
                treePermission.setDirective_url(permission.getDirective_url());
                treePermission.setIcon(permission.getIcon());
                treePermission.setModule_name(permission.getModule_name());
                treePermission.setModule_url(permission.getModule_url());
                treePermission.setItem_name(permission.getItem_name());
                treePermission.setRoles(permission.getRoles());
                treePermission.setIs_root_permission(permission.is_root_permission());

                List<TreePermission> permissionList = permissionToTreeData(permission.getPermission_id(), list);
                treePermission.setSub_permissions(permissionList);
                if(parent == 1){
                    treePermission.setPermission_class("rootfolder");
                }else {
                    if (permissionList.size() > 0) {
                        treePermission.setPermission_class("folder");
                       // treePermission.setExpanded(true);
                    } else {
                        treePermission.setPermission_class("file");
                        treePermission.setSub_permissions(null);
                    }
                }
                returnTree.add(treePermission);
            }
        }

        return returnTree;
    }


    private PermissionService permissionService;


    private final static Logger LOGGER = Logger.getLogger(PermissionGetHandler.class.getName());
}
