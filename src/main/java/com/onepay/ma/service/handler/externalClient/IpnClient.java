package com.onepay.ma.service.handler.externalClient;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.util.PropsUtil;
import io.vertx.core.json.JsonObject;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class IpnClient {

    private static final String BASE_URL = PropsUtil.get("ipn.base_url", StringPool.BLANK);

    private static final Logger LOGGER = Logger.getLogger(IpnClient.class.getName());

    public static JsonNode accpectInternationalOrder(String transactionId) {
        return  postOrder("MSP", transactionId, "accepted");
    }
    public static JsonNode rejectInternationalOrder(String transactionId) {
        return  postOrder("MSP", transactionId, "rejected");
    }
    public static JsonNode accpectDomesticOrder(String transactionId) {
        return  postOrder("MSP", transactionId, "accepted");
    }
    public static JsonNode rejectDomesticOrder(String transactionId) {
        return  postOrder("MSP", transactionId, "rejected");
    }

    private static JsonNode postOrder(String service, String transactionId, String state) {
        JsonNode jsonReturn = null;


        // Request uri base
        String requestURI = BASE_URL + "/services/" + service + "/transactions/" + transactionId +"/messages";
        LOGGER.log(Level.INFO, "POST SENT MESSAGE URL: " + Utils.excludeSensitiveInfo(requestURI));
        String requestMethod = "POST";

        JsonObject body = new JsonObject();
        body.put("state", state);
        LOGGER.info("POST SENT MESSAGE content: " + body.encode());
        try {

            URL url = new URL(requestURI);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(body.encode());
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("POST ORDER responseCode:" + responseCode);
            LOGGER.info("POST ORDER  responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_ACCEPTED) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();

                LOGGER.log(Level.WARNING, "ERROR ON POST ORDER: " + Utils.mask(strReturn));
                throw  new ErrorException(responseCode, responseMsg, responseMsg, "", strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        LOGGER.log(Level.SEVERE, " POST ORDER RETURN CONTENT " + Utils.mask(jsonReturn));
        return jsonReturn;

    }
}
