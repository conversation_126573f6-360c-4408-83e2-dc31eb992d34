package com.onepay.ma.service.handler.externalClient;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;
import java.nio.charset.StandardCharsets;
import com.onepay.ma.service.models.pay_out.FundsTransSearchReq;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;

import java.util.logging.Level;

import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;

import static java.nio.charset.StandardCharsets.UTF_8;

public class OnePayoutClient {

    private static final Logger LOGGER = Logger.getLogger(OnePayoutClient.class.getName());

    private static String ONEPAY_MA_PAYOUT_SERVICE_BASE_URL = PropsUtil.get("onepay_payout_service_base_url", "");

    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static String ONEPAY_MSP_SERVICE_CLIENT_ID = PropsUtil.get("onepay_payout_service_client_id", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_KEY = PropsUtil.get("onepay_payout_service_client_key", "");
    private static String ONEPAY_MSP_SERVICE_REGION = PropsUtil.get("onepay_payout_service_region", "");
    private static String ONEPAY_MSP_SERVICE_NAME = PropsUtil.get("onepay_payout_service_name", "");


    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject getFundsTransHistory(String userId, String x_USER_ID, String x_REQUEST_ID, String fromDate, String toDate, String dateOf, String beneficiaryBanks, String merchantId, String funds_transfer_id, String transaction_id, String merchantAccount, String beneficiaryAccount, String state, Integer pageSize, Integer page) {
        JsonObject jsonReturn = null;
        String requestURI = "/onepayout/api/v1/partners/" + merchantId + "/histories";
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));

            Map<String, String> queryParamMap = new LinkedHashMap<>();
            queryParamMap.put("page_size", pageSize + "");
            queryParamMap.put("page", page + "");
            queryParamMap.put("from_date", fromDate);
            queryParamMap.put("to_date", toDate);
            queryParamMap.put("dateOf", dateOf);
            queryParamMap.put("beneficiaryBanks", beneficiaryBanks);
            queryParamMap.put("merchantId", merchantId);
            queryParamMap.put("funds_transfer_id", funds_transfer_id);
            queryParamMap.put("transaction_id", transaction_id);
            queryParamMap.put("merchant_account", merchantAccount);
            queryParamMap.put("beneficiaryAccount", beneficiaryAccount);
            queryParamMap.put("state", state);
            StringBuilder queryBefore = new StringBuilder("");
            StringBuilder queryAfter = new StringBuilder("?");
            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
                queryBefore.append(queryParam.getKey()).append("=").append(queryParam.getValue()).append("&");
                queryAfter.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }
            Map<String, String> queryParameters = new TreeMap<>();
            if (queryBefore != null && !"".equals(queryBefore)) {
                for (String kv : queryBefore.toString().split("&")) {
                    String[] kvArr = kv.split("=");
                    if (kvArr.length == 2)
                        queryParameters.put(kvArr[0], kvArr[1]);
                }
            }

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put("X-Request-Id", x_REQUEST_ID);
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParameters, signedHeaders,
                    new byte[]{}, requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI + queryAfter;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Accept"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setRequestProperty(x_USER_ID, userId);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                // String strReturn = response.toString();
                jsonReturn = new JsonObject(response.toString());
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT GET FUNDS TRANSFER HISTORY ERROR RESPONSE: " + Utils.mask(jsonObject));
                jsonReturn = jsonObject;
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject getFundsTransHistoryForApproval(FundsTransSearchReq req) {
        JsonObject jsonReturn = null;
        String requestURI = "/onepayout/api/v1/partners/" + req.getMerchantId() + "/histories_for_approval";
        String requestMethod = "GET";
        Date requestDate = new Date();

        int requestTimeOut = 60000;

        try {
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI) );

            Map<String, String> queryParamMap = new LinkedHashMap<>();
            queryParamMap.put("page_size", req.getPageSize() + "");
            queryParamMap.put("page", req.getPage() + "");
            queryParamMap.put("from_date", req.getFromDate());
            queryParamMap.put("to_date", req.getToDate());
            queryParamMap.put("funds_transfer_id", req.getFundsTransferId());
            queryParamMap.put("transaction_id", req.getTransactionId());
            queryParamMap.put("merchant_account", req.getMerchantAccount());
            StringBuilder queryBefore = new StringBuilder("");
            StringBuilder queryAfter = new StringBuilder("?");
            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
                queryBefore.append(queryParam.getKey()).append("=").append(queryParam.getValue()).append("&");
                queryAfter.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }
            Map<String, String> queryParameters = new TreeMap<>();
            if (queryBefore != null && !"".equals(queryBefore)) {
                for (String kv : queryBefore.toString().split("&")) {
                    String[] kvArr = kv.split("=");
                    if (kvArr.length == 2)
                        queryParameters.put(kvArr[0], kvArr[1]);
                }
            }

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put("X-Request-Id", req.getxRequestId());
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParameters, signedHeaders,
                    new byte[]{}, requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI + queryAfter;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Accept"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setRequestProperty(req.getxUserId(), req.getUserId());
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                // String strReturn = response.toString();
                jsonReturn = new JsonObject(response.toString());

                LOGGER.log(Level.INFO, "PAYOUT GET FUNDS TRANSFER HISTORY RESPONSE: " + Utils.mask(jsonReturn));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT GET FUNDS TRANSFER HISTORY ERROR RESPONSE: " + Utils.mask(jsonObject));
                jsonReturn = jsonObject;
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject getFundsTransHistoryById(String userId, String x_USER_ID, String x_REQUEST_ID, String accountId, String fundsTransferId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/onepayout/api/v1/accounts/" + accountId + "/funds_transfers/" + fundsTransferId;
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));

            Map<String, String> queryParamMap = new LinkedHashMap<>();
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put("X-Request-Id", x_REQUEST_ID);
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    new byte[]{}, requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Accept"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());


            connection.setRequestProperty(x_USER_ID, userId);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                // String strReturn = response.toString();
                jsonReturn = new JsonObject(response.toString());

                LOGGER.log(Level.INFO, "PAYOUT GET FUNDS TRANSFER HISTORY RESPONSE: " + Utils.mask(jsonReturn));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT GET FUNDS TRANSFER HISTORY ERROR RESPONSE: " + Utils.mask(jsonObject));
                jsonReturn = jsonObject;
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject getReceivedAccount(String userId, String x_USER_ID, String x_REQUEST_ID, String receiptBank, String merchantAccount, String receiptAccountId, String amount, String funds_transfer_id) {
        JsonObject jsonReturn = null;
        String requestURI = "/onepayout/api/v1/customers";
        String requestMethod = "GET";
        Date requestDate = new Date();
        int requestTimeOut = 60000;

        try {
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));

            Map<String, String> queryParamMap = new LinkedHashMap<>();
            queryParamMap.put("swift_code", receiptBank);
            queryParamMap.put("account_number", receiptAccountId);
            queryParamMap.put("card_number", "");
            queryParamMap.put("request_id", funds_transfer_id);
            queryParamMap.put("account_id", merchantAccount);
            queryParamMap.put("amount", amount);
             LOGGER.log(Level.INFO, "param po : " + queryParamMap.toString());
            StringBuilder queryBefore = new StringBuilder("");
            StringBuilder queryAfter = new StringBuilder("?");
            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
                queryBefore.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
                queryAfter.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }
            Map<String, String> queryParameters = new TreeMap<>();
            if (queryBefore != null && !"".equals(queryBefore)) {
                for (String kv : queryBefore.toString().split("&")) {
                    String[] kvArr = kv.split("=");
                    if (kvArr.length == 2)
                        queryParameters.put(kvArr[0], kvArr[1]);
                }
            }

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParameters, signedHeaders,
                    new byte[]{}, requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI + queryAfter;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Content-Type"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setRequestProperty(x_USER_ID, userId);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "PAYOUT GET Recevied Account RESPONSE: " + Utils.mask(jsonReturn) );
            } else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT GET FUNDS TRANSFER HISTORY ERROR RESPONSE: " + Utils.mask(jsonObject));
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR CALL BANKS]", e);
        }
        return jsonReturn;
    }

    public static JsonObject transferTransaction(FundsTransferReq request) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/onepayout/api/v1/accounts/" + request.getMerchantAccount() + "/funds_transfers/" + request.getFundsTransferId();
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;
        JsonObject jbody = new JsonObject();

        jbody.put("funds_transfer_info", request.getFundsTransferId());
        jbody.put("merchantId", request.getMerchantId());
        jbody.put("merchantAccount", request.getMerchantAccount());
        jbody.put("account_number", request.getReceiptAccountId());
        jbody.put("holder_name", request.getHolderName());
        jbody.put("swift_code", request.getReceiptBank());
        jbody.put("amount", request.getAmount());
        jbody.put("remark", request.getRemark());
        jbody.put("currency", "VND");
        jbody.put("card_number", "");
        jbody.put("op", "replace");
        JsonObject authen = new JsonObject();
        authen.put("user_name", request.getUserName());
        authen.put("password", request.getPassword());
        jbody.put("authentication", authen);
        JsonObject metaData = new JsonObject();
        metaData.put("creator_id", request.getCreateId());
        metaData.put("creator_name", request.getCreateName());
        jbody.put("meta_data", metaData);
        try {
            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));
            LOGGER.log(Level.INFO, " info: " + jbody.encode());

            Map<String, String> queryParamMap = new LinkedHashMap<>();

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put("X-Request-Id", request.getxRequestId());
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    jbody.encode().getBytes("UTF-8"), requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Accept"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setRequestProperty(request.getxUserId(), request.getUserId());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes("UTF-8").length));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {

                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.INFO, "PAYOUT TRANSFER RESPONSE: " + Utils.mask(jsonReturn));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT TRANSFER ERROR RESPONSE: " + Utils.mask(jsonObject));
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[Funds Transfer ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject changeStatusFunds(String fundsId, String state, String partnerID, String userName, String password, String userId, String x_USER_ID, String x_REQUEST_ID, String checkerId, String checkerName) {
        JsonObject jsonReturn = null;

        String requestURI = "/onepayout/api/v1/fund-transfer-change-status/" + fundsId;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;
        JsonObject jbody = new JsonObject();

        jbody.put("partner_id", partnerID);
        jbody.put("user_name", userName);
        jbody.put("password", password);
        jbody.put("state", state);
        jbody.put("verifier_id", checkerId);
        jbody.put("verifier_name", checkerName);

        try {
            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));
            LOGGER.log(Level.INFO, " info: " + jbody.encode());

            Map<String, String> queryParamMap = new LinkedHashMap<>();

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put("X-Request-Id", x_REQUEST_ID);
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    jbody.encode().getBytes("UTF-8"), requestDate, requestTimeOut);

            String aspOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty("Content-Type", signedHeaders.get("Content-Type"));
            connection.setRequestProperty("X-Request-Id", signedHeaders.get("X-Request-Id"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");

            connection.setRequestProperty(x_USER_ID, userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes("UTF-8").length));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {

                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.INFO, "PAYOUT TRANSFER RESPONSE: " + Utils.mask(jsonReturn) );
            } else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT CHANGE STATUS ERROR RESPONSE: " + Utils.mask(jsonObject));
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

}
