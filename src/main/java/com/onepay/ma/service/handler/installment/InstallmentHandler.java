package com.onepay.ma.service.handler.installment;

import com.onepay.ma.service.service.installment.InstallmentService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

public class InstallmentHandler {
    public static void getAllBank(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return InstallmentService.getAllBank(connOnline);
                }).subscribe(merchants -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }
}
