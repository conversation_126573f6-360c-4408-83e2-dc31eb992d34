package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostPromotionTransactionHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.PromotionTransactionService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FilePostPromotionTransactionHandlerImpl implements FilePostPromotionTransactionHandler {

    public FilePostPromotionTransactionHandlerImpl(PromotionTransactionService promotionTransactionService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.promotionTransactionService = promotionTransactionService;
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.cacheGuava = cacheGuava;
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE PROMOTION TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientPr = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

//        SQLConnection connPr = rc.get(ParamsPool.CONNECTION_PROMOTION);
//        SQLConnection connBackUp = rc.get(ParamsPool.CONNECTION_BACKUP);
//        SQLConnection connOnline = rc.get(ParamsPool.CONNECTION_ONLINE);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        PromotionTxnPostFile mapBody =  gson.fromJson(body, PromotionTxnPostFile.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE PROMOTION TRANSACTION POST ] =>  BODY EMPTY");
            throw IErrors.RESOURCE_NOT_FOUND;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientPr), f-> f.dispose())
                .flatMap(connPr -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "promotion").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                PromotionTransactionParameter parameter = new PromotionTransactionParameter();
                                                parameter.setKeywords(mapBody.getKeywords());
                                                parameter.setCardNumber(mapBody.getCard_number());
                                                parameter.setTransactionReference(mapBody.getMerchant_transaction_ref());
                                                parameter.setCardType(mapBody.getCard_type());
                                                parameter.setCurrency(mapBody.getCurrency());
                                                parameter.setFromDate(mapBody.getFrom_date());
                                                parameter.setToDate(mapBody.getTo_date());
                                                parameter.setMerchantId(String.join(",", merchantIdList));
                                                parameter.setOrderInfo(mapBody.getOrder_info());
                                                parameter.setTransactionId(mapBody.getTransaction_id());
                                                parameter.setPromotionSid(mapBody.getPromotion_id());
                                                parameter.setPromotion_code(mapBody.getPromotion_code());
                                                parameter.setPaygate(mapBody.getPaygate());
                                                parameter.setLang(mapBody.getLang());
                                                Map data = new HashMap();
                                                return promotionTransactionService.getTotalDownload(connPr, parameter).flatMap(integer -> {
                                                    if (integer == 0) {
                                                        LOGGER.log(Level.SEVERE, "[ FILE PROMOTION TRANSACTION POST ] =>  TOTAL NOT FOUND");
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }

                                                    if (integer > serverConfig.getRowLimit() ) {
                                                        throw IErrors.PASS_EXCEL_LIMIT;
                                                    }
                                                    String fileName = "promotion" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
                                                    long date = new java.util.Date().getTime();
                                                    fileName += date;
                                                    String fileHashName = "";
                                                    data.put("parameter", parameter);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("promotion_transaction");
                                                    if(integer <= serverConfig.getRowLevel()){
                                                        fileDownload.setExt("xlsx");
                                                    }else {
                                                        fileDownload.setExt("zip");
                                                    }
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }
            PromotionTransactionParameter parameter = (PromotionTransactionParameter) map.get("parameter");
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<PromotionTransactionParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //  fileDownload.setExt("zip");
                Message<PromotionTransactionParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });


    }

    private PromotionTransactionService promotionTransactionService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    private CacheGuava cacheGuava;

    private MerchantService merchantService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(FilePostPromotionTransactionHandler.class.getName());
}
