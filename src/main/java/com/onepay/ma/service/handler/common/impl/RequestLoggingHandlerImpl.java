package com.onepay.ma.service.handler.common.impl;


import com.google.gson.JsonSyntaxException;
import com.onepay.ma.service.handler.common.RequestLoggingHandler;
import io.vertx.core.json.JsonObject;
import com.onepay.ma.service.util.Utils;

import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/6/16.
 */

public class RequestLoggingHandlerImpl implements RequestLoggingHandler {
    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
        Set<String> headersName = request.headers().names();
        LOGGER.log(Level.INFO, "************* " + rc.request().method() + " " + Utils.mask(rc.request().uri()) +" *********");
        LOGGER.log(Level.INFO, "[REQUEST] [HEADERS] : " + Arrays.toString(headersName.toArray()));

        for (String header : headersName) {
            LOGGER.log(Level.INFO, header + " : " + request.getHeader(header));
        }

        LOGGER.log(Level.INFO, "[REQUEST] [BODY] : " + rc.getBodyAsString());
        rc.next();
    }

    private final static Logger LOGGER = Logger.getLogger(RequestLoggingHandlerImpl.class.getName());
}
