package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.service.pay_collect.UserConfigService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class GetUserByIdCollectHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    private UserConfigService configService;

    private static Logger LOGGER = Logger.getLogger(GetUserByIdCollectHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
                String user_id = rc.request().getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_ID));
                String merchant_id = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
                String user_name = rc.request().getParam(ParamsPool.USER_NAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_NAME));
                String reference = rc.request().getParam(ParamsPool.REFERENCE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REFERENCE));
                String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
                String page = rc.request().getParam(ParamsPool.PAGE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAGE));
                String page_size = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
                Map<String, String> mIn = new HashMap<>();
                mIn.put(ParamsPool.USER_ID, user_id);
                mIn.put(ParamsPool.USER_NAME, user_name);
                mIn.put(ParamsPool.REFERENCE, reference);
                mIn.put(ParamsPool.STATE, state);
                mIn.put(ParamsPool.PAGE, page);
                mIn.put(ParamsPool.PAGE_SIZE, page_size);
                mIn.put(ParamsPool.MERCHANT_ID, merchant_id);
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                        .flatMap(connPaycollect -> {
                            return this.configService.total(connPaycollect, mIn).flatMap(total -> {
                                return this.configService.search(connPaycollect, mIn).map(list -> {
                                    total.setList(list);
                                    return total;
                                });
                            });
                        }).subscribe(data -> {
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, data);
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "UserSearchCollectHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

}
