package com.onepay.ma.service.handler.approval;

import com.onepay.ma.service.handler.approval.impl.ApprovalPromotionGetHandlerImpl;
import com.onepay.ma.service.service.ApprovalService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public interface ApprovalPromotionGetHandler extends Handler<RoutingContext> {
    static ApprovalPromotionGetHandlerImpl create(ApprovalService approvalService, UserService userService){
        return new ApprovalPromotionGetHandlerImpl(approvalService, userService);
    }
}
