package com.onepay.ma.service.handler.transaction.onebill.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.onebill.OnebillTransaction;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import com.onepay.ma.service.util.Utils;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class OneBillTransactionGetHandler implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(OneBillTransactionGetHandler.class.getName());
    @Autowired
    private OneBillTransactionService oneBillTransactionService;
    @Autowired
    private UserService userService;
    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ BILLING TRANSACTION GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientMerchantPortal = rc.get(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ BILLING TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.VALIDATION_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ BILLING TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));
            String bankId = rc.request().getParam(ParamsPool.BANK_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_ID));
            String channel = rc.request().getParam(ParamsPool.CHANNEL_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CHANNEL_ID));
            String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            if (pageSize > Utils.getPageSize()) {
                pageSize = Utils.getPageSize();
            }
            OnebillTransactionQuery query = new OnebillTransactionQuery();
            query.setPage(page);
            query.setPageSize(pageSize);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setStatus(status);
            query.setTransactionType(transactionType);
            query.setChannel(channel);
            query.setBankId(bankId);
            query.setOrderInfo(orderInfo);
            Observable<BaseList<OnebillTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f -> f.dispose())
                    .flatMap(connOnebill -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackup -> {
                                                // find user by S-id
                                                return userService.getOneAm(connBackup, userId).flatMap(user -> {
                                                    return merchantService.list(connOnline, connBackup, "", userId, "billing").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        query.setProviderId(String.join(",", merchantIdList));
                                                        return this.oneBillTransactionService.search(connOnebill, query);
                                                    });
                                                });
                                            });
                                });
                    });

            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f -> f.dispose())
                    .flatMap(connOnebill -> {
                        return oneBillTransactionService.getById(connOnebill, Integer.valueOf(transactionId));
                    }).subscribe(transaction -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });;
        }
    }
}
