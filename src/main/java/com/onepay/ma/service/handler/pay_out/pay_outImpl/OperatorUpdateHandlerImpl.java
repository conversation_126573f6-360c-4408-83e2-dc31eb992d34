package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OperatorUpdateHandlerImpl implements Handler<RoutingContext> {
    private static Logger LOGGER = Logger.getLogger(OperatorUpdateHandlerImpl.class.getName());
    private final static Gson gson = new Gson();

    @Autowired
    private OperatorService operatorService;

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String body = rc.getBodyAsString();
        OperatorDTO operator = gson.fromJson(body, OperatorDTO.class);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(sqlConnection -> {
                    connectionBackUp = sqlConnection;
                    return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return operatorService.update(connectionBackUp, operator);
                    });
                }).subscribe(user -> {
            if (user == null) {
                if (connectionBackUp != null) {
                    connectionBackUp.rollbackObservable();
                }
                throw IErrors.VALIDATION_ERROR;
            }
            if (connectionBackUp != null) {
                connectionBackUp.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
            rc.next();
        }, throwable -> {
            if (connectionBackUp != null) {
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    private SQLConnection connectionBackUp;

}
