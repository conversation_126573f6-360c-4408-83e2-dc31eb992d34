package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.service.mpay.impl.SSMpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.mpay.SamsungMpayTransactionQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class SamSungMpayTransactionDownloadHandlerImpl implements Handler<RoutingContext> {

    private final static Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(SamSungMpayTransactionDownloadHandlerImpl.class.getName());
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private UserService userService;
    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE SAMSUNG MPAY TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        SamsungMpayTransactionQuery query = gson.fromJson(body, SamsungMpayTransactionQuery.class);
        if (query == null) {
            LOGGER.log(Level.SEVERE, "[ FILE SAMSUNG MPAY TRANSACTION POST ] =>  BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Map data = new HashMap();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {
                                    connectBackUp = connBackUp;
                                    // find user by S-id
                                    return userService.getOneAm(connectBackUp, userId).flatMap(user -> {


                                        return merchantService.list(connOnline, connectBackUp, query.getMerchantId(), userId, "mpay").flatMap(merchants -> {
                                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                            query.setMerchantId(String.join(",", merchantIdList));
                                                query.setTerminalId(StringPool.BLANK);
                                                return getObservable(rc, userId, query, requestData, data, connReadOnly, connOnline);
                                            });
                                    });
                                });
                            });
                }).subscribe(map -> {
            if (connectBackUp != null) {
                connectBackUp.commitObservable();
            }
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<SamsungMpayTransactionQuery> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //  fileDownload.setExt("zip");
                Message<SamsungMpayTransactionQuery> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if (connectBackUp != null) {
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
        
    }
    
    private Observable<Map> getObservable(RoutingContext rc, String userId, SamsungMpayTransactionQuery query, Map requestData,
                                          Map data, SQLConnection connReadOnly, SQLConnection connOnline) {
        return SSMpayTransactionService.getTotalDownload(connOnline, connReadOnly, query).flatMap(integer -> {
            if (integer == 0) {
                LOGGER.log(Level.SEVERE, "[ FILE SAMSUNG MPAY TRANSACTION POST ] =>  TOTAL NOT FOUND");
                throw IErrors.RESOURCE_NOT_FOUND;
            }

            if (integer > serverConfig.getRowLimit() ) {
                throw IErrors.PASS_EXCEL_LIMIT;
            }
            String fileName = "mpay" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
            long date = new java.util.Date().getTime();
            fileName += date;
            String fileHashName = "";
            data.put("parameter", query);
            data.put("file_name", fileName);
            data.put("row", integer);
            try {
                fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
            } catch (NoSuchAlgorithmException e) {
                rc.fail(e);
            } catch (UnsupportedEncodingException e) {
                rc.fail(e);
            }
            requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
            requestData.put(ParamsPool.FILE_NAME, fileName);
            FileDownload fileDownload = new FileDownload();
            fileDownload.setUser(userId);
            fileDownload.setFile_type("mpay_transaction");
            if (integer <= serverConfig.getRowLevel()) {
                fileDownload.setExt("xlsx");
            } else {
                fileDownload.setExt("zip");
            }
            fileDownload.setFile_name(fileName);
            fileDownload.setFile_hash_name(fileHashName);
            fileDownload.setConditions(gson.toJson(query));
            return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                    return data;
                });
            });
        });
    }
}
