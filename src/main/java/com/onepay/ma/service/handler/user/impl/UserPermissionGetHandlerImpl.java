package com.onepay.ma.service.handler.user.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserPermissionGetHandler;
import com.onepay.ma.service.models.Permissions;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/9/16.
 */
public class UserPermissionGetHandlerImpl implements UserPermissionGetHandler {
    public UserPermissionGetHandlerImpl(UserPermissionService userPermissionService, CacheGuava cacheGuava) {
        this.cacheGuava = cacheGuava;
        this.userPermissionService = userPermissionService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));
        if(userId.equals(StringPool.BLANK)){
            rc.fail(IErrors.VALIDATION_ERROR);
        }
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if(cacheGuava.get().getIfPresent(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION) == null) {
            LOGGER.log(Level.INFO, "======== GET DATA FROM DATABASE =========");
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose())
                    .flatMap(sqlConnection -> {
                        return userPermissionService.list(sqlConnection, userId);
                }).subscribe(permissions -> {
                cacheGuava.put(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION, permissions);
//                LOGGER.log(Level.FINE, gson.toJson(permissions));
                rc.put(ParamsPool.HANDLER_DATA_RESULT, permissions);
                rc.put(ParamsPool.HANDLER_LOG_RESULT, true);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });;
        }else{
            Permissions permissions = (Permissions) cacheGuava.get().getIfPresent(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
            LOGGER.log(Level.INFO, "======== GET DATA FROM CACHE =========");
//            LOGGER.log(Level.FINE, gson.toJson(permissions));
            rc.put(ParamsPool.HANDLER_DATA_RESULT, permissions);
            rc.put(ParamsPool.HANDLER_LOG_RESULT, true);
            rc.next();
        }

    }

    private CacheGuava cacheGuava;

    private static final Logger LOGGER = Logger.getLogger(UserPermissionGetHandler.class.getName());

    private UserPermissionService userPermissionService;

    private Gson gson = new Gson();

}
