package com.onepay.ma.service.handler.partner;

import com.onepay.ma.service.handler.partner.impl.PartnerGetHandlerImpl;
import com.onepay.ma.service.service.partner.PartnerService;
import com.onepay.ma.service.service.partner.UserPartnerService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by thaihv on 27/8/19.
 */
public interface PartnerGetHandler extends Handler<RoutingContext> {

    static PartnerGetHandlerImpl create(PartnerService partnerService, UserPartnerService userPartnerService) {
        return new PartnerGetHandlerImpl(partnerService, userPartnerService);
    }

}
