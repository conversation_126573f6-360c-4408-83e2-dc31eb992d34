package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.MailUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.text.SimpleDateFormat;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 28-Aug-17.
 */
@Component
public class MpayTransactionUpdateHandlerImpl implements Handler<RoutingContext> {
    private static final Logger logger = Logger.getLogger(MpayTransactionUpdateHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            logger.log(Level.SEVERE, "[ MPAY TRANSACTION LIST ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String transactionId = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));
        String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));
        String previousStatus = rc.request().getParam("previous_status") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("previous_status"));
        Observable<Void> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return updateStatus(clientOnline, transactionId, status);
                });
        // Observable<NewBaseList<MpayTransaction>> obs = Observable.using(SQLConnectionFactory::new, f ->
        // f.create(clientReadOnly), f -> f.dispose())
        // .flatMap(connReadOnly -> {
        // //get online connection
        // return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
        // .flatMap(connOnline -> {
        // return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
        // .flatMap(connBackup -> {
        // // find user by S-id
        // return setAutoCommitObservable(false).flatMap(aVoid -> {
        // return MpayTransactionService.updateStatus(connOnline, transactionId, status);
        // });
        // });
        // });
        // });

        // obs.subscribe(transactions -> {
        // rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
        // rc.next();
        // }, throwable -> {
        // rc.fail(throwable);
        // });
            try {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                StringBuilder subject = new StringBuilder("CẢNH BÁO THAY ĐỔI TRẠNG THÁI GIAO DỊCH ").append(transactionId);
                String host = "mail.onepay.com.vn";
                StringBuilder body = new StringBuilder("Dear anh Dương,<br>");
                body.append("Giao dịch sau đây đã được thay đổi trạng thái:<br>");
                body.append("Merchant ID: ").append(merchantId).append("<br>");
                body.append("Transaction ID: ").append(transactionId).append("<br>");
                body.append("Date: ").append(simpleDateFormat.format(System.currentTimeMillis())).append("<br>");
                body.append("Response Code: ").append(previousStatus).append(" to ").append(status).append("<br>");
                String toEmail = "<EMAIL>";
                MailUtil.sendMailDirectOnePAY(subject.toString(), body.toString(),toEmail, "", "", "<EMAIL>", "aCMvf5jCng8Y6pAdCUHQNx", "smtp", host, 25, "true");
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                rc.fail(e);
            }
        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private Observable<Void> updateStatus(JDBCClient clientOnline, String transactionId, String status) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return MpayTransactionService.updateStatus(connOnline, transactionId, status);
                });
    }
}
