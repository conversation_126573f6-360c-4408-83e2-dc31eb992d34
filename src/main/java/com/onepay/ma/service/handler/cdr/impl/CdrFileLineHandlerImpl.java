package com.onepay.ma.service.handler.cdr.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.cdr.CdrFileLineGetHandler;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by huynguy<PERSON> on 4/2/16.
 */
public class CdrFileLineHandlerImpl implements CdrFileLineGetHandler {

    public CdrFileLineHandlerImpl(CdrService cdrService) {
        this.cdrService = cdrService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
        JDBCClient clientCdr = rc.get(ParamsPool.CDR_DATASOURCE_NAME);
        //SQLConnection connCdr = rc.get(ParamsPool.CONNECTION_CDR);
        String source_left = request.getParam(ParamsPool.SOURCE_LEFT) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SOURCE_LEFT));

        String source_right = request.getParam(ParamsPool.SOURCE_RIGHT) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SOURCE_RIGHT));
        Map returnValue = new HashMap();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientCdr), f-> f.dispose())
                .flatMap(connCdr -> {
                    return cdrService.getFileLine(connCdr, source_left).flatMap(fileLine -> {
                        returnValue.put("source_left", fileLine);
                        return cdrService.getFileLine(connCdr, source_right).map(fileLine1 -> {
                            returnValue.put("source_right", fileLine1);
                            return returnValue;
                        });
                    });
                }).subscribe(map -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });




    }




    private CdrService cdrService;

}
