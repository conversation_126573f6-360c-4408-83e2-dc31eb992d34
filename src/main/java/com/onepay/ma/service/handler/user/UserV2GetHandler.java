package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserV2GetHandlerImpl;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.service.partner.UserPartnerService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserV2GetHandler extends Handler<RoutingContext> {
    static UserV2GetHandlerImpl create(UserServiceV2 userService, UserPartnerService userPartnerService){
        return new UserV2GetHandlerImpl(userService, userPartnerService);
    }
}
