package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import org.apache.commons.lang.StringUtils;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class OperatorHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(OperatorHandlerImpl.class.getName());

    @Autowired
    private OperatorService operatorService;
    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {

        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(fromDate);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ PAYOUT OPERATOR BATCH GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 6) {
            LOGGER.log(Level.SEVERE, "[ PAYOUT OPERATOR BATCH GET ]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String email = rc.request().getParam(ParamsPool.EMAIL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.EMAIL));
        String name = rc.request().getParam("name") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("name"));
        String merchantId = rc.request().getParam("merchantId") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("merchantId"));
        String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));
        if (pageSize > Utils.getPageSize()) {
            pageSize = Utils.getPageSize();
        }
        Map<String, String> mIn = new HashMap();
        mIn.put(ParamsPool.FROM_DATE, fromDate);
        mIn.put(ParamsPool.TO_DATE, toDate);
        mIn.put(ParamsPool.EMAIL, email);
        mIn.put("name", name);
        mIn.put(ParamsPool.STATE, state);
        mIn.put(ParamsPool.PAGESIZE, String.valueOf(pageSize));
        mIn.put(ParamsPool.PAGE, String.valueOf(page));
        String maMain = rc.request().getParam(ParamsPool.MA_MAIN) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MA_MAIN));
        LOGGER.log(Level.INFO, "xUserId: {}", xUserId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain v1: ");
            String finalMerchantsList = MaPermission.getMc(xUserId, xRequestId, partnerId, merchantId);
            mIn.put(ParamsPool.MERCHANT_ID, finalMerchantsList);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return operatorService.search(connOnline, mIn);
                }).subscribe(partners -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else  {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").flatMap(merchants -> {
                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList()); 
                                    mIn.put(ParamsPool.MERCHANT_ID,String.join(",", merchantIdList));                           
                                    return operatorService.search(connOnline, mIn);
                                });
                    });
                }).subscribe(partners -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }

    }

}
