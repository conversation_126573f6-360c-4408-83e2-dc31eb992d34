package com.onepay.ma.service.handler.cdr.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.cdr.CdrPutHandler;
import com.onepay.ma.service.models.CdrPutBody;
import com.onepay.ma.service.models.SourceCDR;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/2/16.
 */
public class CdrPutHandlerImpl implements CdrPutHandler {

    public CdrPutHandlerImpl(CdrService cdrService) {
        this.cdrService = cdrService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
       // String compareId = request.getParam("compareId") == null ? StringPool.BLANK : String.valueOf(request.getParam("compareId"));

        JDBCClient clientCdr = rc.get(ParamsPool.CDR_DATASOURCE_NAME);

//        if(!compareId.isEmpty()) {
            String json = rc.getBodyAsString();
            CdrPutBody putBody = gson.fromJson(json, CdrPutBody.class);
            Map returnMap = new HashMap();
            returnMap.put("source_left", false);
            returnMap.put("source_right", false);
            SourceCDR sourceLeft = putBody.getSource_left();
            SourceCDR sourceRight = putBody.getSource_right();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientCdr), f-> f.dispose()).flatMap(connCdr -> {
                        return connCdr.setAutoCommitObservable(false).flatMap(aVoid -> {
                            return cdrService.update(connCdr, sourceLeft.getId(), sourceLeft.getTrans_id(), sourceLeft.getResult_code(), sourceLeft.getDescription()).flatMap(integerLeft -> {
                                if (integerLeft == 1) {
                                    returnMap.put("source_left", true);
                                }
                                return cdrService.update(connCdr, sourceRight.getId(), sourceRight.getTrans_id(), sourceRight.getResult_code(), sourceRight.getDescription()).flatMap(integerRight -> {
                                    if (integerRight == 1) {
                                        returnMap.put("source_right", true);
                                    }
                                    return connCdr.commitObservable().map(aVoid1 -> {
                                        return returnMap;
                                    });
                                });
                            });
                        });
            }).subscribe(returnData -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnData);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        //}
    }




    private final static Gson gson = new Gson();
    private final static Logger LOGGER = Logger.getLogger(CdrPutHandlerImpl.class.getName());
    private CdrService cdrService;

}
