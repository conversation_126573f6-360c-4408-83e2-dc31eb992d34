package com.onepay.ma.service.handler.user.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserSessionDeleteHandler;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public class UserSessionDeleteHandlerImpl implements UserSessionDeleteHandler {

    public UserSessionDeleteHandlerImpl(CacheGuava cacheGuava, OneAMConfig oneAMConfig) {
        this.cacheGuava = cacheGuava;
        this.oneAMConfig = oneAMConfig;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));

        if(!userId.isEmpty()){
            cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
            cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
            Map returnMap = new HashMap();
            returnMap.put("return_url", oneAMConfig.getLogOutUrl() + StringPool.EQUAL + oneAMConfig.getContinueUrl());
            rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnMap);
            rc.next();
        }else{
            throw IErrors.VALIDATION_ERROR;
        }

    }


    private CacheGuava cacheGuava;

    private OneAMConfig oneAMConfig;

}
