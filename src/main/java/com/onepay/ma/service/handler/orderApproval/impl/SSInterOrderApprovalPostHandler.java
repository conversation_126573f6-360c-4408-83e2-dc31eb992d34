package com.onepay.ma.service.handler.orderApproval.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.externalClient.IpnClient;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.orderApproval.OrderApproval;
import com.onepay.ma.service.models.orderApproval.OrderApprovalPostModel;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.service.impl.MerchantPermitService;
import com.onepay.ma.service.service.international.SSInternationalTransactionService;
import com.onepay.ma.service.service.orderApproval.OrderApprovalService;
import com.onepay.ma.service.util.OneCreditUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class SSInterOrderApprovalPostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL TRANSACTION POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if (ipAddress == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        HttpClient httpClient = rc.vertx().createHttpClient();

        String body = rc.getBodyAsString();
        OrderApprovalPostModel mapBody = gson.fromJson(body, OrderApprovalPostModel.class);
        if (mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ ORDER APPROVAL POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        getTransactionById(clientOnline, clientOnline, mapBody.getTransaction_id()).subscribe(transaction -> {
            if (transaction == null ) {
                throw IErrors.RESOURCE_NOT_FOUND;
            }
            if(transaction.getOrder_status() != null && !transaction.getOrder_status().equalsIgnoreCase("pending") ) {
                throw IErrors.ORDER_HAS_ACCEPTED;
            }

            OrderApproval model = new OrderApproval();
            model.setTransaction_id(mapBody.getTransaction_id());
            model.setStatus(mapBody.getStatus());
            model.setDescription(mapBody.getDescription());
            model.setUser_confirm(userId);
            model.setType("international");



            // Do refund for reject request
            if (model.getStatus().equalsIgnoreCase("rejected")) {
                String transactionId = String.valueOf(transaction.getTransaction_id());
                String merchantId = transaction.getMerchant_id();
                String currencyCode = transaction.getAmount().getCurrency();
                double amount = transaction.getAmount().getTotal() - transaction.getAmount().getRefund_total();
                String transactionRef = transaction.getTransaction_reference();
                RefundData refundData = new RefundData(merchantId, transactionRef, null, amount, 1, currencyCode, "", 0);
                // Refund before reject order
                this.refund(rc, refundData, clientReadOnly, clientOnline, clientBackUp, transactionId, xRequestId, userId, httpClient, model);
            } else {
                if (ipnClient.accpectInternationalOrder(model.getTransaction_id()) != null) {
                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                        Map<String, Object> responseHashMap = new HashMap();
                        responseHashMap.put("order", orderApproval);
                        responseHashMap.put("refund", null);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
            }

        }, throwable -> {
            rc.fail(throwable);
        });

    }


    private Observable<SamsungInternationalTransaction> getTransactionById(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    //get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return SSInternationalTransactionService.get(connOnline, transactionId);
                            });
                });
    }

    /**
     * refund transaction
     *
     * @param rc
     * @param refundData
     * @param clientReadOnly
     * @param clientOnline
     * @param clientBackUp
     * @param transactionId
     * @param xRequestId
     * @param userId
     */
    private void refund(RoutingContext rc, RefundData refundData, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
                        String transactionId, String xRequestId, String userId, HttpClient httpClient, OrderApproval model) {
        if (refundData.getRefund_reference() == null && xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID OR REFUND REFERENCE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = xRequestId;
        if (refundData.getRefund_reference() == null || refundData.getRefund_reference().isEmpty()) {
            refundData.setRefund_reference(refundData.getMerchant_id() + "_" + requestId);
            if (refundData.getRefund_reference().length() > 32) {
                int largerInt = refundData.getRefund_reference().length() - 32 + 1;
                requestId = requestId.substring(0, requestId.length() - largerInt);
                refundData.setRefund_reference(refundData.getMerchant_id() + "_" + requestId);
            }
        }


        // check if refund_reference existed
        Observable<Object> checkFlag = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.internationalTransactionService.getHistoryByMerchantRef(connOnline, transactionId, refundData.getRefund_reference(), refundData.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                    return this.internationalRefundService.getApprovalByRef(connBackUp, Integer.valueOf(transactionId), refundData.getRefund_reference(), refundData.getMerchant_id()).map(refundApproval -> {

                                        if (internationalTransactionHistory != null) {
                                            if (refundData.getAmount() != internationalTransactionHistory.getAmount().getTotal()) {
                                                throw IErrors.REFUND_REJECTED;
                                            }
                                            return internationalTransactionHistory;
                                        } else if(refundApproval != null) {
                                            return refundApproval;
                                        }
                                        return null;
                                    });
                                });
                            });
                });

        checkFlag.subscribe(internationalRefund -> {
            if (internationalRefund != null) {
                if (ipnClient.rejectInternationalOrder(model.getTransaction_id()) != null) {
                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                        Map<String, Object> responseHashMap = new HashMap();
                        responseHashMap.put("order", orderApproval);
                        responseHashMap.put("refund", internationalRefund);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
            }else {
                Observable<Double> obsAmountRefund = getAmountRefund(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);
                // begin refund data
                obsAmountRefund.subscribe(amount -> {
                    Observable<Boolean> flagInsert = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return this.merchantPermitService.isMerchantPermittedRefundApproval(connBackUp, refundData.getMerchant_id(), RefundData.Type.INTERNATIONAL.getValue())
                                        .map(aBoolean -> {
                                            if (refundData.getMerchant_id().startsWith("OP_") && !refundData.getMerchant_id().equals("OP_PREPAID")) { // trong trưởng hợp ONESTART thì cho vào duyệt
                                                return true;
                                            }
                                            return aBoolean;
                                        });
                            });
                    flagInsert.subscribe(flag -> {
                        //flag true: INSERT vao bang tam REFUND APPROVAL elese insert luon
                        if (flag) {
                            LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                            return this.refundApprovalService.insertRequest(connBackUp, userId, refundData.getMerchant_id(),
                                                    transactionId, refundData.getAmount(), refundData.getCurrency(),
                                                    refundData.getRefund_reference(), RefundData.Type.INTERNATIONAL.getValue()).flatMap(approval -> {
                                                return Observable.just(approval);
                                            });
                                        });
                                    }).subscribe(stringObjectMap -> {
                                if (ipnClient.rejectInternationalOrder(model.getTransaction_id()) != null) {
                                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                                        Map<String, Object> responseHashMap = new HashMap();
                                        responseHashMap.put("order", orderApproval);
                                        responseHashMap.put("refund", stringObjectMap);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                                } else {
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        } else {
                            Observable<MerchantData> merchantObs = getMerchantObs(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);

                            merchantObs.subscribe(merchantData -> {
                                Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .subscribe(connOnline -> {
                                            if (merchantData == null) {
                                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => INVALID MERCHANT ");
                                                throw IErrors.VALIDATION_ERROR;
                                            }
                                            this.internationalTransactionService.get(connOnline, transactionId).subscribe(internationalTransaction -> {
                                                String vpcCommand = "refund";
                                        /* neu la GD cua Vietcombank + nguyen mon
                                         + Giao dich tien USD: >= 5.000 USD
                                         + Giao dich tien VND: >= 100.000.000 VND
                                         --> vpcCommand là refundWithHold
                                        */
                                                if (internationalTransaction != null && internationalTransaction.getAcquirer().getAcquirer_id() == 1 &&
                                                        internationalTransaction.getAmount().getTotal() == refundData.getAmount() &&
                                                        (("USD".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getAmount() >= 5000) ||
                                                                ("VND".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getAmount() >= *********))) {
                                                    vpcCommand = "refundWithHold";
                                                }
                                                Map data = OneCreditUtil.oneCreditData("REFUND", vpcCommand, refundData.getMerchant_id(), merchantData.getAccessCode()
                                                        , refundData.getRefund_reference(), internationalTransaction.getOcMerTxnRef(), refundData.getAmount(), userId, merchantData.getHashCode(), "2", refundData.getNote());

                                                Date dt = new Date();
                                                data.put("timeout", String.valueOf(refundConfig.getTimeout()));
                                                data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
                                                LOGGER.log(Level.INFO, "CLIENT REFUND REQUEST DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(data));
                                                JsonObject jsonObject = new JsonObject(data);

                                                HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());
                                                clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
                                                clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
                                                clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jsonObject.toString().getBytes(StandardCharsets.UTF_8), refundConfig.getSecureCode()));
                                                Buffer buffer = Buffer.buffer(jsonObject.toString());
                                                LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(jsonObject) );
                                                clientRequestToObs(rc, transactionId, clientOnline, clientBackUp, clientRequest, refundData, model);
                                                clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
                                                clientRequest.end(buffer);
                                            });
                                        });

                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        }
                    });
                }, throwable -> {
                    rc.fail(throwable);
                });
            }
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    private Observable<Double> getAmountRefund(String transactionId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RefundData refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return internationalTransactionService.listHistory(connOnline, connBackup, transactionId).map(internationalTransactionHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = refundData.getAmount();
                                                for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                    // Case purchase ->
                                                    if (his.getTransaction_type().toUpperCase().equals("PURCHASE")) {
                                                        remainAmt += his.getAmount().getTotal();
                                                    } else if ((his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content)
                                                            && Integer.valueOf(his.getStatus()) != RefundApproval.Status.REQUEST.code)
                                                            || (his.getAdvance_status()!= null && (his.getAdvance_status().equalsIgnoreCase("Rejected")
                                                                || his.getAdvance_status().equalsIgnoreCase("Merchant Rejected")
                                                                || his.getAdvance_status().equalsIgnoreCase("OnePAY Rejected")))
                                                            || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.APPROVED.content))) {
                                                        // Do nothing
                                                    } else {
                                                        if (his.getAdvance_status().equalsIgnoreCase("Successful"))
                                                            remainAmt -= his.getAmount().getTotal();
                                                    }
                                                }
                                                String str = String.format("%1.2f", remainAmt);
                                                remainAmt = Double.valueOf(str);
                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                    return refundAmt;
                                                } else {
                                                    LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                    throw IErrors.AMOUNT_REFUND_ERROR;
                                                }
                                            });
                                        });
                            });
                });
    }

    private Observable<MerchantData> getMerchantObs(String transactionId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RefundData refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return internationalTransactionService.listHistory(connOnline, connBackup, transactionId).flatMap(internationalTransactionHistories -> {
                                        double remainAmt = 0;
                                        double refundAmt = refundData.getAmount();
                                        for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                            if (his.getTransaction_type().toUpperCase().equals("PURCHASE")) {
                                                remainAmt = his.getAmount().getTotal();
                                            } else {
                                                remainAmt -= his.getAmount().getTotal();
                                            }
                                        }
                                        if (refundAmt <= remainAmt && remainAmt > 0) {
                                            return merchantService.getData(connOnline, refundData.getMerchant_id());
                                        } else {
                                            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REMAIN : " + remainAmt + " REFUND : " + refundAmt);
                                            throw IErrors.AMOUNT_REFUND_ERROR;
                                        }
                                    });
                                });
                    });
        });
    }

    private void clientRequestToObs(RoutingContext rc, String transactionId, JDBCClient clientOnline, JDBCClient clientBackUp, HttpClientRequest clientRequest, RefundData refundData, OrderApproval model) {
        clientRequest.toObservable().subscribe(response -> {
            int statusCode = response.statusCode();
            if (statusCode == HttpResponseStatus.OK.code()) {
                response.bodyHandler(buffer1 -> {
                    String json = buffer1.toString("UTF-8");
//                        LOGGER.log(Level.INFO, "===================================== OneCredit Response =====================================");
//                        LOGGER.log(Level.INFO, json);
//                        LOGGER.log(Level.INFO, "===================================== OneCredit Response =====================================");
                    Map mapVpc = gson.fromJson(json, Map.class);

                    LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA " + StringPool.SPACE + StringPool.COLON + Utils.mask(mapVpc));
                    if (mapVpc != null && mapVpc.get("vpc_response") != null) {
                        Map dataResp = (Map) mapVpc.get("vpc_response");
                        if ((dataResp.get("vpc_TxnResponseCode") != null && "0".equals(dataResp.get("vpc_TxnResponseCode") + "")
                                || mapVpc.get("command_status").toString().equalsIgnoreCase("WAIT_FOR_APPROVE"))) {
                            Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp1 -> {
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                .flatMap(connOnline2 -> {
                                                    return internationalTransactionService.getHistoryByMerchantRef(connOnline2, transactionId, dataResp.get("vpc_MerchTxnRef").toString(), refundData.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                        return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                            if (userData != null) {
                                                                internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                            }
                                                            return internationalTransactionHistory;
                                                        });

                                                    });
                                                });
                                    });

                            returnObs.subscribe(internationalTransactionHistory -> {
                                if (ipnClient.rejectInternationalOrder(model.getTransaction_id()) != null) {
                                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                                        Map<String, Object> responseHashMap = new HashMap();
                                        responseHashMap.put("order", orderApproval);
                                        responseHashMap.put("refund", internationalTransactionHistory);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                                } else {
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                            }, rc::fail);
                        } else {
                            LOGGER.log(Level.SEVERE, "Refund transaction failed - mapVpc", mapVpc == null ? "null" : Utils.mask(mapVpc));
                            LOGGER.log(Level.INFO, "===================================== END REFUND ==================================" + StringPool.NEW_LINE);
                            if (mapVpc.get("vpc_TxnResponseCode") != null) {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED");
                                throw IErrors.REFUND_FAILED;
                            } else {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                                // LOGGER.log(Level.SEVERE, mapVpc.toString());
                                throw IErrors.REFUND_FAILED;
                            }
                        }

                    } else {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED - mapVpc" + mapVpc == null ? "null" : Utils.mask(mapVpc));
                        throw IErrors.REFUND_FAILED;
                    }

                });

                response.exceptionHandler(throwable -> {
                    rc.fail(throwable);
                });
            } else {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED - " + Utils.mask(response));
                throw IErrors.REFUND_FAILED;
            }
        }, throwable -> {
            rc.fail(throwable);
        });
    }


    private Observable<OrderApproval> updateOrder(JDBCClient clientOnline, JDBCClient clientBackUp, OrderApproval orderApproval) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    //get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return SSInternationalTransactionService.updateOrder(connOnline, orderApproval.getTransaction_id(), orderApproval.getStatus(), orderApproval.getDescription()).flatMap(aVoid -> {
                                    return orderApprovalService.insert(connBackUp, orderApproval);
                                });
                            });
                });
    }


    @Autowired
    private OrderApprovalService orderApprovalService;

    @Autowired
    private IpnClient ipnClient;

    @Autowired
    private UserService userService;

    @Autowired
    private RefundConfig refundConfig;

    @Autowired
    private InternationalTransactionService internationalTransactionService;

    @Autowired
    private InternationalRefundService internationalRefundService;

    @Autowired
    private RefundApprovalService refundApprovalService;

    @Autowired
    private MerchantPermitService merchantPermitService;

    @Autowired
    private MerchantService merchantService;

    private static final String X_SECURE_HASH = "X-Secure-Hash";

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(SSInterOrderApprovalPostHandler.class.getName());
}
