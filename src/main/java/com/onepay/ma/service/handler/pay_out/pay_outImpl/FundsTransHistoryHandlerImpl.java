package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_out.FundsTransHistoryDto;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import groovy.json.JsonException;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class FundsTransHistoryHandlerImpl implements Handler<RoutingContext> {

    private static final Logger LOGGER = Logger.getLogger(FundsTransHistoryHandlerImpl.class.getName());

    @Autowired
    private MerchantService merchantService;
    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String partnerId = myEvent.get(ParamsPool.X_PARTNER_ID);
        String userId = myEvent.get(ParamsPool.USER_ID);
        JDBCClient clientBackUp = myEvent.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        JsonObject body = myEvent.getBodyAsJson();

        String fromDate = body.getString(ParamsPool.FROM_DATE);
        String toDate = body.getString(ParamsPool.TO_DATE);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date myFromDate;
        Date myToDate;
        try {
            myFromDate = dateFormat.parse(fromDate);
            myToDate = dateFormat.parse(toDate);
        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(myFromDate, myToDate);
        if (months > 12) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        String dateOf = body.getString("dateOf");
        String beneficiaryBanks = body.getString("beneficiaryBanks");
        String merchantId = body.getString("merchantId");
        String merchantTransId = body.getString("merchantTransId");
        String transactionId = body.getString("transactionId");
        String merchantAccount = body.getString("merchantAccount");
        String beneficiaryAccount = body.getString("beneficiaryAccount");
        String state = body.getString("state");
        Integer pageSize = body.getString("page_size") == null ? 20 : Integer.parseInt(body.getString("page_size"));
        if (pageSize > Utils.getPageSize()) {
            pageSize = Utils.getPageSize();
        }
        final int pageSizeFinal = pageSize;
        
        Integer page = body.getString("page") == null ? 1 : (Integer.parseInt(body.getString("page")) + 1);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    //get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").map(merchants -> {
                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                    String merchantIdx = String.join(",", merchantIdList);
                                    if (null == merchantIdx || merchantIdx.isEmpty()) {
                                        throw IErrors.FORBIDDEN;
                                    }
                                    JsonObject jo = OnePayoutClient.getFundsTransHistory(userId, xUserId, xRequestId, fromDate
                                            , toDate, dateOf, beneficiaryBanks, merchantIdx, merchantTransId, transactionId
                                            , merchantAccount, beneficiaryAccount, state, pageSizeFinal, page);
                                    List<FundsTransHistoryDto> fundsTransHistoryDtos = new ArrayList<>();
                                    if (null != jo) {
                                        JsonArray jsonArray = jo.getJsonArray("list");
                                        if (jsonArray != null) {
                                            jsonArray.forEach(obj -> {
                                                fundsTransHistoryDtos.add(bindFundsTransHistoryDtos((JsonObject) obj));
                                            });

                                        }
                                    }
                                    BaseList<FundsTransHistoryDto> bs = new BaseList<>();

                                    bs.setList(fundsTransHistoryDtos);
                                    bs.setTotal_items(jo == null ? 0 : jo.getInteger("total_items"));
                                    return bs;
                                });
                            });
                }).subscribe(a -> {

                myEvent.put(ParamsPool.HANDLER_DATA_RESULT, a);
                myEvent.next();
            }, throwable -> {
                myEvent.fail(throwable);
            });
    }

    /**
     * convert data from result set to firm banking transactions
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private FundsTransHistoryDto bindFundsTransHistoryDtos(JsonObject rs) {
        FundsTransHistoryDto fundsTransHistoryDto = new FundsTransHistoryDto();
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            fundsTransHistoryDto.setS_id(rs.getString("funds_transfer_id"));
            fundsTransHistoryDto.setMerchantId(rs.getString("merchant_id"));
            fundsTransHistoryDto.setMerchantAccount(rs.getString("account_id"));
            fundsTransHistoryDto.setMerchantName(null);
            fundsTransHistoryDto.setMerchantTransId(rs.getString("funds_transfer_id"));
            fundsTransHistoryDto.setBalanceBefore(rs.getString("before_account_balance") == null ? 0 : Double.parseDouble(rs.getString("before_account_balance")));
            fundsTransHistoryDto.setBalanceAfter(rs.getString("after_account_balance") == null ? 0 : Double.parseDouble(rs.getString("after_account_balance")));
            fundsTransHistoryDto.setBalanceBankBefore(rs.getString("before_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("before_bank_balance")));
            fundsTransHistoryDto.setBalanceBankAfter(rs.getString("after_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("after_bank_balance")));
            fundsTransHistoryDto.setReceivedAccountNumber(rs.getString("account_number"));
            fundsTransHistoryDto.setReceivedAccountName(rs.getString("holder_name"));
            fundsTransHistoryDto.setReceivedCardNumber(rs.getString("card_number"));
            fundsTransHistoryDto.setTransactionId(rs.getString("transaction_id"));
            fundsTransHistoryDto.setTransactionDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setAmount(rs.getString("amount") == null ? 0 : Double.parseDouble(rs.getString("amount")));
            fundsTransHistoryDto.setRemark(rs.getString("remark"));
            fundsTransHistoryDto.setState(rs.getString("state"));

            fundsTransHistoryDto.setBankTransactionId(null);
            fundsTransHistoryDto.setBeneficiarySwiftCode(rs.getString("swift_code"));
            fundsTransHistoryDto.setBeneficiaryBankName(rs.getString("bank_name"));
            fundsTransHistoryDto.setBankCode(rs.getString("response_code"));
            fundsTransHistoryDto.setBankMsg(rs.getString("message"));

            fundsTransHistoryDto.setCreatedDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setUpdatedDate(rs.getString("update_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("update_time")).getTime()));
            fundsTransHistoryDto.setFundDate(rs.getString("fund_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("fund_time")).getTime()));
            if (rs.containsKey("meta_data") && null != rs.getString("meta_data")) {
                fundsTransHistoryDto.setMetadata(rs.getString("meta_data"));
                if (isJSONValid(rs.getString("meta_data"))) {
                    JsonObject metaData = new JsonObject(rs.getString("meta_data"));
                    if (metaData.containsKey("creator_id")) {
                        fundsTransHistoryDto.setCreator_id(metaData.getString("creator_id"));
                    }
                    if (metaData.containsKey("creator_name")) {
                        fundsTransHistoryDto.setCreator_name(metaData.getString("creator_name"));
                    }
                    if (metaData.containsKey("verifier_id")) {
                        fundsTransHistoryDto.setVerifier_id(metaData.getString("verifier_id"));
                    }
                    if (metaData.containsKey("verifier_name")) {
                        fundsTransHistoryDto.setVerifier_name(metaData.getString("verifier_name"));
                    }
                }
            }
            fundsTransHistoryDto.setOperator(rs.getString("operator_user"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fundsTransHistoryDto;
    }

    public boolean isJSONValid(String test) {
        try {
            new JsonObject(test);
        } catch (JsonException ex) {
            try {
                new JsonArray(test);
            } catch (JsonException ex1) {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }
}
