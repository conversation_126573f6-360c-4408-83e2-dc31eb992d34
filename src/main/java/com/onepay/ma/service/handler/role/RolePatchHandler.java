package com.onepay.ma.service.handler.role;

import com.onepay.ma.service.handler.role.impl.RolePatchHandlerImpl;
import com.onepay.ma.service.service.PermissionRoleService;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface RolePatchHandler extends Handler<RoutingContext> {
    static RolePatchHandlerImpl create(RoleService roleService, PermissionRoleService permissionRoleService, CacheGuava cacheGuava){
        return new RolePatchHandlerImpl(roleService, permissionRoleService,cacheGuava);
    }
}
