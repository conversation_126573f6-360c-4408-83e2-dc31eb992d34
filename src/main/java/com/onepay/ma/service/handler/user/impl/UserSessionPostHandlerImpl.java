package com.onepay.ma.service.handler.user.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.user.UserSessionPostHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.http.HttpMethod;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static io.vertx.core.http.HttpHeaders.*;

/**
 * Created by huynguyen on 3/9/16.
 */
public class UserSessionPostHandlerImpl implements UserSessionPostHandler {
    public UserSessionPostHandlerImpl(OneAMConfig oneAMConfig, UserService userService, UserPermissionService userPermissionService, MerchantService merchantService) {
        this.oneAMConfig = oneAMConfig;
        this.userService = userService;
        this.userPermissionService = userPermissionService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
        String code = request.getParam("code") == null ? StringPool.BLANK : String.valueOf(request.getParam("code"));
        if(code.equals(StringPool.BLANK)){
            LOGGER.log(Level.SEVERE, "[ USER SESSION POST ] => INVALID CODE");
            throw IErrors.VALIDATION_ERROR;
        }

        String uri = oneAMConfig.getTokenUri();
        // create http client;
        HttpClient client = rc.vertx().createHttpClient();

        TokenOneAmParameter tokenOneAmParameter = new TokenOneAmParameter();
        tokenOneAmParameter.setCode(code);
        tokenOneAmParameter.setGrant_type(ParamsPool.AUTHORIZATION_CODE);

        String json = gson.toJson(tokenOneAmParameter);
        Buffer buffer = Buffer.buffer(json, "UTF-8");
        String userOneAm = oneAMConfig.getUser() + StringPool.COLON + oneAMConfig.getPassword();
        String headerAuthorization = "Basic" + StringPool.SPACE + Convert.toBase64String(userOneAm.getBytes()).replaceAll("\r|\n","");
        HttpClientRequest req = client.requestAbs(HttpMethod.POST, uri);
        LOGGER.info("USER SESSION POST LOGIN:" +" uri:" + Utils.excludeSensitiveInfo(uri) + " method: " + HttpMethod.POST + " body: " + json);
        req.putHeader(String.valueOf(AUTHORIZATION), headerAuthorization);
        req.putHeader(String.valueOf(ACCEPT), ParamsPool.JSON_HEADER);
        req.putHeader(String.valueOf(CONTENT_LENGTH), buffer.length() + StringPool.BLANK);
        req.toObservable().flatMap(httpClientResponse -> {
            int statusCode = httpClientResponse.statusCode();
            if (statusCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => RESULT :" + statusCode + " DESC : " + httpClientResponse.toString());
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return httpClientResponse.toObservable();
        }).subscribe(response -> {
            String data = response.toString("UTF-8");
            oneAMGetResources(rc, data, headerAuthorization, tokenOneAmParameter, buffer, client);
        }, throwable -> {
            rc.fail(throwable);
        });



        req.write(buffer);
        req.end();

    }

    /**
     * get data resources oneam
     * @param context
     * @param data
     * @param headerAuthorization
     * @param tokenOneAmParameter
     * @param bufferData
     * @param client
     */
    private void oneAMGetResources(RoutingContext context, String data, String headerAuthorization, TokenOneAmParameter tokenOneAmParameter, Buffer bufferData, HttpClient client){
        TokenResult token = gson.fromJson(data, TokenResult.class);

        String uriRes =  String.format(oneAMConfig.getResourceUri(), token.getAccessToken()) + StringPool.QUESTION
                + ParamsPool.CODE + StringPool.EQUAL+ tokenOneAmParameter.getCode() + StringPool.AMPERSAND
                + ParamsPool.GRANT_TYPE + StringPool.EQUAL + tokenOneAmParameter.getGrant_type();
        String headerRes = headerAuthorization + StringPool.SPACE + "Bearer" + StringPool.SPACE + token.getAccessToken();
        HttpClientRequest reqRes = client.requestAbs(HttpMethod.GET, uriRes);
        reqRes.putHeader(AUTHORIZATION + StringPool.BLANK, headerRes);
        reqRes.putHeader(String.valueOf(ACCEPT), ParamsPool.JSON_HEADER);
        reqRes.putHeader(String.valueOf(CONTENT_LENGTH), 0 + StringPool.BLANK);
        reqRes.toObservable().flatMap(httpClientResponse -> {
            int statusCode = httpClientResponse.statusCode();
            if (statusCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => RESULT :" + statusCode + " DESC : " + httpClientResponse.statusMessage());
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return httpClientResponse.toObservable();
        }).flatMap(dataRes -> {
            String resData = dataRes.toString("UTF-8");
            ResourcesResult resourcesResult = gson.fromJson(resData, ResourcesResult.class);
            JDBCClient clientDb = context.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

           return Observable.using(SQLConnectionFactory::new, f -> f.create(clientDb), f-> f.dispose())
                    .flatMap(sqlConnection -> {
                        return userService.getOneAm(sqlConnection, resourcesResult.getResources().getProfile().getId()).flatMap(user -> {
                            OneAmUser userAM = resourcesResult.getResources().getProfile();
                            if (user != null) {
                                String databaseUser = user.getS_id();
                                String oneAmUser = userAM.getId();
                                String hashUser = "";
                                String hashOneAmUser = "";
                                try {
                                    hashUser = Convert.hash(databaseUser);
                                    hashOneAmUser = Convert.hash(oneAmUser);
                                } catch (NoSuchAlgorithmException e) {
                                    LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                } catch (UnsupportedEncodingException e) {
                                    LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                                if (!hashUser.equals(hashOneAmUser)) {
                                    User userData = new User();
                                    // userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                                    String name =userAM.getLastName();
                                    if(userAM.getFirstName()!=null && !userAM.getFirstName().trim().equals("")){
                                        name +=StringPool.SPACE + userAM.getFirstName();
                                    }
                                    userData.setName(name);
                                    userData.setEmail(userAM.getEmail());
                                    userData.setAddress(userAM.getAddress());
                                    userData.setPhone(userAM.getMobile());
                                    userData.setS_id(userAM.getId());
                                    return userService.update(sqlConnection, userData).flatMap(user1 -> {
                                        return userPermissionService.list(sqlConnection, user1.getS_id()).map(permissions -> {
                                            UserSession userSession = new UserSession();
                                            userSession.setProfile(user1);
                                            List<Permission> permissionList = permissions.getPermissions();
                                            List<Role> roles = new ArrayList<>();
                                            permissionList.stream().filter(permission -> permission.getPath_regex() != null).forEach(permission -> {
                                                Role role = new Role();
                                                role.setPath_regex(permission.getPath_regex());
                                                roles.add(role);
                                            });
                                            userSession.setRoles(roles);
                                            return userSession;
                                        }).flatMap(userSession -> {
                                            return merchantService.listAll(sqlConnection, userSession.getProfile().getS_id()).map(merchants -> {
                                                userSession.setMerchants(merchants);
                                                return userSession;
                                            });
                                        });
                                    });

                                } else {
                                    return userPermissionService.list(sqlConnection, user.getS_id()).flatMap(permissions -> {
                                        UserSession userSession = new UserSession();
                                        userSession.setProfile(user);
                                        List<Permission> permissionList = permissions.getPermissions();
                                        List<Role> roles = new ArrayList<>();
                                        permissionList.stream().filter(permission -> permission.getPath_regex() != null).forEach(permission -> {
                                            Role role = new Role();
                                            role.setPath_regex(permission.getPath_regex());
                                            roles.add(role);
                                        });
                                        userSession.setRoles(roles);
                                        return Observable.just(userSession);
                                    }).flatMap(userSession -> {
                                        return merchantService.listAll(sqlConnection, userSession.getProfile().getS_id()).map(merchants -> {
                                            userSession.setMerchants(merchants);
                                            return userSession;
                                        });
                                    });
                                }
                            } else {
                                User userData = new User();
                                userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                                userData.setEmail(userAM.getEmail());
                                userData.setAddress(userAM.getAddress());
                                userData.setPhone(userAM.getMobile());
                                userData.setS_id(userAM.getId());
                                return userService.insert(sqlConnection, userData).flatMap(user1 -> {
                                    return userPermissionService.list(sqlConnection, user1.getS_id()).map(permissions -> {
                                        UserSession userSession = new UserSession();
                                        userSession.setProfile(user1);
                                        List<Permission> permissionList = permissions.getPermissions();
                                        List<Role> roles = new ArrayList<>();
                                        permissionList.stream().filter(permission -> permission.getPath_regex() != null).forEach(permission -> {
                                            Role role = new Role();
                                            role.setPath_regex(permission.getPath_regex());
                                            roles.add(role);
                                        });
                                        userSession.setRoles(roles);
                                        return userSession;
                                    }).flatMap(userSession -> {
                                        return merchantService.listAll(sqlConnection, userSession.getProfile().getS_id()).map(merchants -> {
                                            userSession.setMerchants(merchants);
                                            return userSession;
                                        });
                                    });
                                });
                            }

                        });
                    });
        }).subscribe(userSession -> {
            context.put(ParamsPool.HANDLER_DATA_RESULT, userSession);
            context.next();
        },throwable -> {
            context.fail(throwable);
        });

        reqRes.write(bufferData);
        reqRes.end();
    }



    private OneAMConfig oneAMConfig;

    private UserService userService;

    private MerchantService merchantService;

    private UserPermissionService userPermissionService;

    private Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(UserSessionPostHandler.class.getName());
}
