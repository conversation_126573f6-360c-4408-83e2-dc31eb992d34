package com.onepay.ma.service.handler.user.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.user.UserV2GetHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by hieunt on 16/10/23.
 */
public class UserV2GetHandlerV2Impl implements UserV2GetHandler {
    private static Logger LOGGER = Logger.getLogger(UserV2GetHandlerImpl.class.getName());

    public UserV2GetHandlerV2Impl(UserServiceV2 userService, MerchantService merchantService) {
        this.userService = userService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String sId = rc.request().getParam("s_id");
        if (sId == null) {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        return merchantService.listAll(sqlConnection, userId).flatMap(merchants -> {
                            if (merchants.size() > 0) {
                                // In case user was granted to at least one partner (After update requirement)
                                StringBuilder merchantIds = new StringBuilder();
                                for (Merchant merchant : merchants) {
                                    if (merchantIds.length() > 0) {
                                        merchantIds.append(",");
                                    }
                                    merchantIds.append(merchant.getMerchant_id());
                                }
                                return userService.listUserByMerchantIds(sqlConnection, userId, merchantIds.toString());
                            } else {
                                return userService.listByCreateId(sqlConnection, userId).flatMap(createdUsers -> {
                                    return Observable.just(createdUsers);
                                });
                            }
                        });
                    }).subscribe(listUser -> {
                Map returnMap = new HashMap();
                returnMap.put("users", listUser);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnMap);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConn -> {
                        return merchantService.listAll(sqlConn, userId)
                                .flatMap(merchants -> {
                                    if (!merchants.isEmpty()) {
                                        String merchantIds = merchants.stream()
                                                .map(Merchant::getMerchant_id)
                                                .collect(Collectors.joining(","));
                                        return userService.listUserByMerchantIds(sqlConn, userId, merchantIds);
                                    } else {
                                        return userService.listByCreateId(sqlConn, userId);
                                    }
                                });
                    }).flatMap(listUser -> {
                        boolean exists = listUser.stream().anyMatch(m -> m.getS_id().equalsIgnoreCase(sId));
                        if (!exists) {
                            return Observable.error(IErrors.VALIDATION_ERROR);
                        }

                        return Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                                .flatMap(sqlConnection -> {
                                    return userService.getUserDataBySId(sqlConnection, sId);
                                });
                    }).subscribe(user -> {
                        if (user == null) {
                            throw IErrors.VALIDATION_ERROR;
                        }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
        }
    }

    private UserServiceV2 userService;

    private MerchantService merchantService;
}
