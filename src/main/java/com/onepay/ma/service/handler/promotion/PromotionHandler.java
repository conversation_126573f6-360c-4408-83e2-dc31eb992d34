package com.onepay.ma.service.handler.promotion;

import com.onepay.ma.service.handler.promotion.impl.PromotionCodeGetHandler;
import com.onepay.ma.service.handler.promotion.impl.PromotionCodePatchHandler;
import com.onepay.ma.service.models.PromotionCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 04-Jan-17.
 */
@Component
public class PromotionHandler {


    @Autowired
    PromotionCodeGetHandler promotionCodeGetHandler;

    @Autowired
    PromotionCodePatchHandler promotionCodePatchHandler;

    public PromotionCodePatchHandler patchCode() {
        return this.promotionCodePatchHandler;
    }

    public PromotionCodeGetHandler getCode() {
        return this.promotionCodeGetHandler;
    }
}
