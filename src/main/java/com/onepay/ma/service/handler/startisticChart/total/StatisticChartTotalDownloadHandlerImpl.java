package com.onepay.ma.service.handler.startisticChart.total;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.startisticChart.international.StatisticChartIntGetHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.international.StatisticChartInterDowloadHandlerImpl;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:18 PM
 * To change this ma-service.
 */
@Component
public class StatisticChartTotalDownloadHandlerImpl implements Handler<RoutingContext> {
    private final static Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(StatisticChartInterDowloadHandlerImpl.class.getName());
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;
    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE TOTAL TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clienBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        JsonObject data =rc.getBodyAsJson();
        JsonArray mDatetf = data.getJsonArray("datetf");
        JsonObject date = mDatetf.getJsonObject(0);
        String datef = date.getString(ParamsPool.FROMDATE);
        String datet = date.getString(ParamsPool.TODATE);
        JsonArray merchantId = data.getJsonArray("merchantId");
        String sMerchantId = String.join(",",merchantId.getList());
        JsonArray payGate = data.getJsonArray("payGate");
        String sPayGate = String.join(",",payGate.getList());
        String target = String.join(",",data.getJsonArray("target").getList());
        String merchantQt = data.getString("merchantQt");
        String merchantNd = data.getString("merchantNd");
        JsonArray cardType = data.getJsonArray("cardType");
        String sCardType= String.join(",", cardType.getList()).replace("|",",");
        ChartStatisticQuery query = new ChartStatisticQuery();
        query.setPaygate(sPayGate);
        query.setFromDate(datef);
        query.setToDate(datet);
        query.setMerchantId(sMerchantId);
        query.setTarget(target);
        query.setMerchantQt(merchantQt);
        query.setMerchantNd(merchantNd);
        query.setCardType(sCardType);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Map mdata = new HashMap();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
                .flatMap(connBackup -> {
                    this.connectBackUp = connBackup;
                    return getObservable(rc, userId,query,requestData,mdata);
                }).subscribe(map -> {
            if (connectBackUp != null) {
                connectBackUp.commitObservable();
            }
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<ChartStatisticQuery> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //  fileDownload.setExt("zip");
                Message<ChartStatisticQuery> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if (connectBackUp != null) {
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }
    private Observable<Map> getObservable(RoutingContext rc, String userId, ChartStatisticQuery query, Map requestData, Map data) {
        String fileName = "overview" + StringPool.UNDERLINE + "chart" + StringPool.UNDERLINE;
        long date = new java.util.Date().getTime();
        fileName += date;
        String fileHashName = "";
        data.put("parameter", query);
        data.put("file_name", fileName);
        data.put("row", 1000);
        try {
            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("total_transaction_chart");
        fileDownload.setExt("xls");
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(query));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });
    }
}
