package com.onepay.ma.service.handler.transaction.onebill.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OneBillPatchHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if (ipAddress == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        if (xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ BILLING TRANSACTION PATCH] -> X-REQUEST-ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");

        JDBCClient clientMerchantPortal = rc.get(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (transactionId != null) {
            String body = rc.getBodyAsString();
            PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
            if (mapRequest == null) {
                LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND DATA EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            if (mapRequest.getPath().equals("/refund")) {
                PatchRequest<RefundData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
                if (mapPatchRequest.getValue() == null) {
                    throw IErrors.VALIDATION_ERROR;
                }

                Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f -> f.dispose())
                        .flatMap(connMerchantPortal -> {
                            // get back up connection
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackup -> {

                                        return oneBillTransactionService.listHistory(connMerchantPortal, Integer.valueOf(transactionId)).flatMap(histories -> {
                                            double remainAmt = 0;
                                            double refundAmt = mapPatchRequest.getValue().getAmount();
                                            for (OnebillTransactionHistory his : histories) {
                                                // Case purchase ->
                                                if (his.getTransactionType().equals("Settlement")) {
                                                    remainAmt += his.getAmount().getTotal();
                                                } else {
                                                    remainAmt -= his.getAmount().getTotal();
                                                }
                                            }

                                            if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                return this.userService.get(connBackup, userId).flatMap(userData -> {
                                                    return this.oneBillTransactionService.refund(connMerchantPortal, Integer.valueOf(transactionId), refundAmt, userData.getEmail());
                                                });
                                            } else {
                                                LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND ERROR  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                throw IErrors.AMOUNT_REFUND_ERROR;
                                            }
                                        });
                                    });

                        }).subscribe(amount -> {

                            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.CREATED.code());
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
            }

        }
    }

    @Autowired
    private OneBillTransactionService oneBillTransactionService;
    
    @Autowired
    private UserService userService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(OneBillPatchHandler.class.getName());
}
