package com.onepay.ma.service.handler.transaction.domestic.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.DomesticTransactionHistory;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import com.onepay.ma.service.util.StringPool;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/30/16.
 */
@Component
public class DomesticTransactionHistoryGetHandler implements Handler<RoutingContext> {



    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION HISTORY] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        String target = rc.request().getParam(ParamsPool.TARGET) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TARGET));
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (transactionId != null) {
            Observable<List<DomesticTransactionHistory>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        // get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    // get back up connection
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return DomesticTransactionService.getDetail(connOnline, transactionId, target).flatMap(domesticTransaction -> {
                                                    return merchantService.list(connOnline, connBackUp, "", userId, "domestic").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        if (null == merchantIdList || merchantIdList.isEmpty() || null == domesticTransaction || null == domesticTransaction.getMerchant_id() || (!merchantIdList.contains("ALL") && !merchantIdList.contains(domesticTransaction.getMerchant_id()))) {
                                                            LOGGER.info("DOMESTIC HIS MERCHANT ID PERMISSION : " + merchantIdList + " MERCHANT ID DETAIL: " + domesticTransaction);
                                                            throw IErrors.FORBIDDEN;
                                                        }
                                                        return DomesticTransactionService.listHistory(connReadOnly, connOnline, connBackUp, transactionId).flatMap(transactionHistories -> {
                                                            return getListUser(connBackUp, transactionHistories, 0);
                                                        });
                                                    });
                                                });

                                            });
                                });
                    });
            obs.subscribe(transaction -> {
                Map returnValue = new HashMap();
                returnValue.put("transactions", transaction);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }

    /**
     * get list user for list approval
     * 
     * @param sqlConnection
     * @param transactionHistories
     * @param index
     * @return
     */
    private Observable<List<DomesticTransactionHistory>> getListUser(SQLConnection sqlConnection, List<DomesticTransactionHistory> transactionHistories, int index) {
        if (transactionHistories.size() <= 0) {
            return Observable.just(transactionHistories);
        }
        DomesticTransactionHistory domesticTransactionHistory = transactionHistories.get(index);
        final int finalIndex = index;
        return Observable.just(domesticTransactionHistory).flatMap(transactionHistory -> {
            if (transactionHistory.getOperator_id() == null) {
                if (finalIndex >= transactionHistories.size() - 1) {
                    return Observable.just(transactionHistories);
                } else {
                    return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                }
            } else {
                // get user data
                return userService.get(sqlConnection, transactionHistory.getOperator_id()).flatMap(userData -> {
                    if (userData != null) {
                        domesticTransactionHistory.setOperator_id(userData.getEmail());
                    }
                    if (finalIndex >= transactionHistories.size() - 1) {
                        return Observable.just(transactionHistories);
                    } else {
                        return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                    }
                });
            }
        });

    }

    @Autowired
    private UserService userService;

    @Autowired
    private MerchantService merchantService;

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionHistoryGetHandler.class.getName());
}
