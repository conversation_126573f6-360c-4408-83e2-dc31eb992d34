package com.onepay.ma.service.handler.paypal;

import com.onepay.ma.service.handler.paypal.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PayPalHandler {

    @Autowired
    private PayPalDisputePostHandler payPalDisputePostHandler;

    @Autowired
    private MerchantOnboardHandler merchantOnboardHandler;

    @Autowired
    private PPMerchantRegisterHandler ppMerchantRegisterHandler;

    @Autowired
    private PPPartnerPostHandler ppPartnerPostHandler;

    @Autowired
    private SentMailRegisterPayPalHandler sentMailRegisterPayPalHandler;
    @Autowired
    private PayPalSentMessagePostHandler payPalSentMessagePostHandler;


    public Handler postDispute() {
        return payPalDisputePostHandler;
    }

    public Handler postMerchantOnboard() {
        return merchantOnboardHandler;
    }

    public Handler registerMerchantOnboard() {
        return ppMerchantRegisterHandler;
    }

    public Handler registerMerchantPost() {
        return ppPartnerPostHandler;
    }

    public Handler sentMailRegister() {
        return sentMailRegisterPayPalHandler;
    }
    public Handler sentMessageDispute() {
        return payPalSentMessagePostHandler;
    }



}
