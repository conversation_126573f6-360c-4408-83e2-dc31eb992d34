package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.handler.externalClient.PayCollectClient;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.service.pay_collect.PartnerConfigService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class UpsertUserCollectHandlerImpl implements Handler<RoutingContext> {
    private static Logger LOGGER = Logger.getLogger(UpsertUserCollectHandlerImpl.class.getName());

    @Autowired
    private PartnerConfigService partnerConfigService;

    @Override
    public void handle(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String userId = rc.get(ParamsPool.USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JsonObject data = rc.getBodyAsJson();
        LOGGER.info("PAYCOLLECT DATA BODY: " + data);
        validateUser(data);
        String id = data.getString("id");
        String partner_id = data.getString("partner_id");
        String reference_id = data.getString("reference");
        JsonObject dataConvert = new JsonObject();
        if (id.equals("0")) {
            dataConvert.put("merchant_id", data.getString("partner_id"));
            dataConvert.put("name", data.getString("name"));
            dataConvert.put("gender", data.getString("gender"));
            dataConvert.put("address", data.getString("address"));
            dataConvert.put("mobile_number", data.getString("mobile_number"));
            dataConvert.put("email", data.getString("email"));
            dataConvert.put("id_card", data.getString("id_card"));
            dataConvert.put("issue_date", data.getString("s_issue_date"));
            dataConvert.put("issue_by", data.getString("issue_by"));
            dataConvert.put("description", data.getString("description"));
        } else {
            dataConvert.put("state", decodeState(data.getString("state")));
            dataConvert.put("name", data.getString("name"));
        }

        LOGGER.info("PAYCOLLECT DATA CONVERT BODY: " + dataConvert);
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                        .flatMap(connPaycollect -> {
                            return partnerConfigService.getPartnerConfig(connPaycollect, partner_id);
                        })
                        .subscribe(partner -> {
                            if (partner != null) {
                                if (id.equals("0")) {
                                    JsonObject merchants = PayCollectClient.createAccount(userId, partner_id,
                                            reference_id, xUserId, xRequestId, dataConvert, partner);
                                    UserConfigDto fundsTransHistoryDtos = bindFundsUserConfigDtos(merchants);
                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, fundsTransHistoryDtos);
                                    rc.next();
                                } else {
                                    PayCollectClient.updateAccount(rc, id, userId, xUserId, dataConvert, partner);
                                }
                            } else {
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, false);
                                rc.next();
                            }
                        }, throwable -> {
                            rc.fail(throwable);
                        });
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "UserSearchCollectHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    private UserConfigDto bindFundsUserConfigDtos(JsonObject rs) {
        UserConfigDto fundsTransHistoryDto = null;
        if (rs.containsKey("id") && rs.getString("id") != null) {
            fundsTransHistoryDto = new UserConfigDto();
            fundsTransHistoryDto.setName(rs.getString("id").toString());
        }
        return fundsTransHistoryDto;
    }

    private String decodeState(String value) {
        String state = "inactive";
        if ("enabled".equalsIgnoreCase(value)) {
            state = "active";
        }
        return state;
    }

    private String encodeState(String value) {
        String state = "disabled";
        if ("active".equalsIgnoreCase(value)) {
            state = "enabled";
        }
        return state;
    }

    private void validateUser(JsonObject data) {

        if (data == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        if (data.containsKey("email") && StringUtils.isNotBlank(data.getString("email")) && !Utils.validateEmail(data.getString("email"))) {
            LOGGER.log(Level.SEVERE, "[PC USER CREATE ] => EMAIL FORMAT ERRR");
            throw IErrors.INVALID_EMAIL_ADDRESS;
        }

        if (data.containsKey("mobile_number") && StringUtils.isNotBlank(data.getString("mobile_number"))
                && !Utils.validatePhone(data.getString("mobile_number"))) {
            LOGGER.log(Level.SEVERE, "[PC USER CREATE ] => PHONE FORMAT ERRR");
            throw IErrors.INVALID_MOBILE_NUMBER;
        }

        if (data.containsKey("description") && StringUtils.isNotBlank(data.getString("description"))
                && !Utils.validateText(data.getString("description"))) {
            LOGGER.log(Level.SEVERE, "[PC USER CREATE ] => Description FORMAT ERRR");
            throw IErrors.VALIDATION_ERROR;
        }
    }

}
