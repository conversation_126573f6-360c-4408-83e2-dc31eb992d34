package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.BatchDetailService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class BatchCheckAuthenHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(BatchCheckAuthenHandlerImpl.class.getName());

    @Autowired
    private BatchDetailService batchDetailService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clienOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        String batchId = rc.request().getParam("batchId") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("batchId"));
        if (batchId == null || batchId.isEmpty()) {
            LOGGER.log(Level.SEVERE, "[ BATCH CHECK AUTHEN ] => PARTNER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }


        Observable.using(SQLConnectionFactory::new, f -> f.create(clienOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return batchDetailService.getBatchDetailAuthen(connOnline, batchId);
                }).subscribe(partners -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

}
