package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.mpay.MpayTransactionHistory;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.logging.Logger;

/**
 * Created by anhkh on 13-Jun-17.
 */
@Service
public class MpayTransactionHistoryGetHandler implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(MpayTransactionHistoryGetHandler.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (transactionId != null) {
            Observable<List<MpayTransactionHistory>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose()).flatMap(connReadOnly -> {
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return MpayTransactionService.get(connOnline, transactionId).flatMap(mpayTrans -> {
                                    return merchantService.list(connOnline, connBackUp, "", userId, "mpay").flatMap(merchants -> {
                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                        if (null == merchantIdList || merchantIdList.isEmpty() || null == mpayTrans || null == mpayTrans.getMerchantId() || (!merchantIdList.contains("ALL") && !merchantIdList.contains(mpayTrans.getMerchantId()))) {
                                            LOGGER.severe("MPAY MERCHANT ID PERMISSION : " + merchantIdList + " MERCHANT ID DETAIL: " + mpayTrans);
                                            throw IErrors.FORBIDDEN;
                                        }
                                        return MpayTransactionService.listHistory(connReadOnly, connOnline, connBackUp, transactionId).flatMap(transaction -> {
                                            return getListUser(connBackUp, transaction, 0);
                                        });
                                    });
                                });


                            });
                });
            });

            obs.subscribe(mpayTransactionHistories -> {
                Map returnValue = new HashMap();
                returnValue.put("transactions", mpayTransactionHistories);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }


    /**
     * get list user for list approval
     * 
     * @param sqlConnection
     * @param transactionHistories
     * @param index
     * @return
     */
    private Observable<List<MpayTransactionHistory>> getListUser(SQLConnection sqlConnection, List<MpayTransactionHistory> transactionHistories, int index) {
        if (transactionHistories.size() <= 0) {
            return Observable.just(transactionHistories);
        }
        MpayTransactionHistory mpayTransactionHistory = transactionHistories.get(index);
        final int finalIndex = index;
        return Observable.just(mpayTransactionHistory).flatMap(transactionHistory -> {
            if (transactionHistory.getOperator_id() == null) {
                if (finalIndex >= transactionHistories.size() - 1) {
                    return Observable.just(transactionHistories);
                } else {
                    return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                }
            } else {
                // get user data
                return userService.get(sqlConnection, transactionHistory.getOperator_id()).flatMap(userData -> {
                    if (userData != null) {
                        mpayTransactionHistory.setOperator_id(userData.getEmail());
                    }
                    if (finalIndex >= transactionHistories.size() - 1) {
                        return Observable.just(transactionHistories);
                    } else {
                        return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                    }
                });
            }
        });

    }

    @Autowired
    private UserService userService;

    @Autowired
    private MerchantService merchantService;
}
