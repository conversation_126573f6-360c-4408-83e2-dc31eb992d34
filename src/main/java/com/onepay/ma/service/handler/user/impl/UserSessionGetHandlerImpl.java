package com.onepay.ma.service.handler.user.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.report.domestic.DomesticReportGetHandler;
import com.onepay.ma.service.handler.user.UserSessionGetHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserTerminalService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/9/16.
 */
public class UserSessionGetHandlerImpl implements UserSessionGetHandler {
    public UserSessionGetHandlerImpl(OneAMConfig oneAMConfig, UserService userService, UserPermissionService userPermissionService, CacheGuava cacheGuava, MerchantService merchantService, UserTerminalService userTerminalService) {
        this.userTerminalService = userTerminalService;
        this.cacheGuava = cacheGuava;
        this.oneAMConfig = oneAMConfig;
        this.userService = userService;
        this.userPermissionService = userPermissionService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));

        if(userId.isEmpty()){
            LOGGER.log(Level.SEVERE, "[ DOMESTIC REPORT GET] => USER ID EMPTY");
           throw IErrors.VALIDATION_ERROR;
        }else{
            if(cacheGuava.get().getIfPresent(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION) == null) {
                Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose(), false)
                        .flatMap(sqlConnection -> {
                        return userService.getOneAm(sqlConnection, userId).flatMap(user -> {
                            //get list permission;
                            return userPermissionService.list(sqlConnection, userId).map(permissions -> {
                                UserSession userSession = new UserSession();
                                userSession.setProfile(user);
                                List<Permission> permissionList = permissions.getPermissions();
                                List<Role> roles = new ArrayList<>();
                                permissionList.stream().filter(permission -> permission.getPath_regex() != null).forEach(permission -> {
                                    Role role = new Role();
                                    role.setPath_regex(permission.getPath_regex());
                                    roles.add(role);
                                });
                                userSession.setRoles(roles);
                                return userSession;
                            }).flatMap(userSession -> {
                                return merchantService.listAll(sqlConnection, userSession.getProfile().getS_id()).map(merchants -> {
                                    userSession.setMerchants(merchants);
                                    return userSession;
                                });
                            }).flatMap(userSession -> {
                                if (userSession == null) {
                                    return Observable.just(userSession);
                                }
                                return userTerminalService.getTerminalBUserId(sqlConnection, userSession.getProfile().getN_id()).map(terminals -> {
//                                    this.setTerminalIntoUserData(terminals, userSession);
                                    return userSession;
                                });

                            });
                        });
                }).subscribe(userSession -> {
                    cacheGuava.put(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION, userSession);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, userSession);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
            }else{
                UserSession userSession = (UserSession) cacheGuava.get().getIfPresent(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, userSession);
                rc.next();
            }
        }

    }


    private void setTerminalIntoUserData(List<Terminal> terminals, UserSession userData){
        // group by Map <MerchantId, List<TerminalId>>
        Map<String, List<String>> terminalMap = terminals.stream()
                .collect(Collectors.groupingBy(Terminal::getMerchant_id,
                        Collectors.mapping(Terminal::getTerminal_id, Collectors.toList())));

        for (Merchant me: userData.getMerchants() ) {
            // Mpay merchant only
            List<String> terminalIds = terminalMap.get(me.getMerchant_id());
            if (me.getType().equalsIgnoreCase("mpay") && terminalIds != null) {
                if(terminalIds.get(0).equalsIgnoreCase("ALL")) {
                    me.setAllTerminal(true);
                }else {
                    me.setTerminalIdList(terminalIds);
                }
            }
        }
    }


    private UserTerminalService userTerminalService;

    private OneAMConfig oneAMConfig;

    private CacheGuava cacheGuava;

    private UserService userService;

    private MerchantService merchantService;

    private UserPermissionService userPermissionService;

    private static final Logger LOGGER = Logger.getLogger(DomesticReportGetHandler.class.getName());


}
