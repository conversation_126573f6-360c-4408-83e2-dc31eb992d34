/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */
/**
 * Created by duongtv on 24/8/2017.
 */
package com.onepay.ma.service.handler.tokenization.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.tokenization.TokenizationHandler;
import com.onepay.ma.service.handler.transaction.mpay.MpayTransactionHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.UserTokenization;
import com.onepay.ma.service.models.UserTokenizations;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.models.mpay.MpayTransactionQuery;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class TokenizationGetHandlerImpl implements TokenizationHandler {

    public TokenizationGetHandlerImpl(TokenService tokenizationService, UserService userService) {
        this.userService = userService;
        this.tokenizationService = tokenizationService;
    }

//    @Autowired
    private UserService userService;
//
//    @Autowired
    private TokenService tokenizationService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String userIdSearch = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String cardNo = rc.request().getParam("card_number") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("card_number"));
        String cardType = rc.request().getParam("card_type") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("card_type"));
        String userID = rc.request().getParam("user_id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("user_id"));
        String userGroup = rc.request().getParam("user_group") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("user_group"));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

        Observable<UserTokenizations> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return tokenizationService.listUserToken(connBackup, cardNo, userID,userGroup,page,pageSize);
                                        });
                            });
                });

        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }


    private static Logger LOGGER = Logger.getLogger(TokenizationHandler.class.getName());
}
