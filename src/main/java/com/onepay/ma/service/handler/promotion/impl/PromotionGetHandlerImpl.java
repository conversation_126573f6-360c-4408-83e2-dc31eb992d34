package com.onepay.ma.service.handler.promotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.promotion.PromotionGetHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Promotion;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class PromotionGetHandlerImpl implements PromotionGetHandler {

    public PromotionGetHandlerImpl(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService) {
        this.promotionService = promotionService;
        this.promotionDiscountService = promotionDiscountService;
        this.merchantService = merchantService;
        this.promotionMerchantService = promotionMerchantService;
        this.promotionRuleService = promotionRuleService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String promotionId = request.getParam("promotionId") == null ? StringPool.BLANK : String.valueOf(request.getParam("promotionId"));

        if(promotionId.isEmpty()){
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));
            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            int status = request.getParam(ParamsPool.STATUS) == null ? -1 : Integer.valueOf(request.getParam(ParamsPool.STATUS));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPr -> {
                                //get online connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .flatMap(connOnline -> {
                                            //get back up connection
                                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                    .flatMap(connBackUp -> {
                                                        return merchantService.list(connOnline, connBackUp, StringPool.BLANK, userId, "promotion").flatMap(merchants -> {
                                                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());

                                                            return promotionService.list(connPr, keywords, String.join(",", merchantIdList), page, status);
                                                        }).flatMap(promotions -> {
                                                            return getListDiscountServicePromotion(connPr, promotions.getPromotions(), 0).flatMap(promotionList -> {
                                                                return getListMerchantPromotion(connPr, promotionList, 0).flatMap(promotionList1 -> {

                                                                    return getListRulePromotion(connPr, promotionList, 0).map(promotionList2 -> {
                                                                        promotions.setPromotions(promotionList2);
                                                                        return promotions;
                                                                    });

                                                                });
                                                            });
                                                        });
                                                    });
                                        });
                            }).subscribe(promotions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, promotions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPr -> {
                        return promotionService.get(connPr, promotionId).flatMap(promotion -> {
                            return promotionDiscountService.listDiscount(connPr, StringPool.BLANK, promotionId).flatMap(discounts -> {
                                promotion.setDiscounts(discounts);
                                return promotionMerchantService.list(connPr, promotionId).flatMap(merchants -> {
                                    promotion.setMerchants(merchants);
                                    if (merchants.size() > 0) {
                                        promotion.setCurrency_code(merchants.get(0).getCurrency_code());
                                    }
                                    return promotionRuleService.list(connPr, promotionId).map(rules -> {
                                        promotion.setRules(rules);
                                        return promotion;
                                    });
                                });
                            });
                        });
                    }).subscribe(promotion -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, promotion);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }

    }

    /**
     * get list discount promotion
     * @param sqlConnPr
     * @param promotionList
     * @param index
     * @return
     */
    private Observable<List<Promotion>> getListDiscountServicePromotion(SQLConnection sqlConnPr, List<Promotion> promotionList, int index){
        if(promotionList.size() <= 0){
            return Observable.just(promotionList);
        }
        Promotion promotion = promotionList.get(index);
        final int finalIndex = index;
        return Observable.just(promotion).flatMap(serviceApproval -> {
            //get user data
            return promotionDiscountService.listDiscount(sqlConnPr, StringPool.BLANK, promotion.getPromotion_id()).flatMap(discounts -> {
                promotion.setDiscounts(discounts);
                if(finalIndex >= promotionList.size() - 1){
                    return Observable.just(promotionList);
                }else{
                    return getListDiscountServicePromotion(sqlConnPr, promotionList, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get list merchant promotion
     * @param sqlConnPr
     * @param promotionList
     * @param index
     * @return
     */
    private Observable<List<Promotion>> getListMerchantPromotion(SQLConnection sqlConnPr, List<Promotion> promotionList, int index){
        if(promotionList.size() <= 0){
            return Observable.just(promotionList);
        }
        Promotion promotion = promotionList.get(index);
        final int finalIndex = index;
        return Observable.just(promotion).flatMap(serviceApproval -> {
            //get user data
            return promotionMerchantService.list(sqlConnPr, promotion.getPromotion_id()).flatMap(merchants -> {
                promotion.setMerchants(merchants);
                if(merchants.size() > 0) {
                    promotion.setCurrency_code(merchants.get(0).getCurrency_code());
                }
                if(finalIndex >= promotionList.size() - 1){
                    return Observable.just(promotionList);
                }else{
                    return getListMerchantPromotion(sqlConnPr, promotionList, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get list rule promotion
     * @param sqlConnPr
     * @param promotionList
     * @param index
     * @return
     */
    private Observable<List<Promotion>> getListRulePromotion(SQLConnection sqlConnPr, List<Promotion> promotionList, int index){
        if(promotionList.size() <= 0){
            return Observable.just(promotionList);
        }
        Promotion promotion = promotionList.get(index);
        final int finalIndex = index;
        return Observable.just(promotion).flatMap(serviceApproval -> {
            //get user data
            return promotionRuleService.list(sqlConnPr, promotion.getPromotion_id()).flatMap(rules -> {
                promotion.setRules(rules);
                if(finalIndex >= promotionList.size() - 1){
                    return Observable.just(promotionList);
                }else{
                    return getListRulePromotion(sqlConnPr, promotionList, finalIndex + 1);
                }
            });
        });
    }


    private PromotionService promotionService;

    private PromotionDiscountService promotionDiscountService;

    private MerchantService merchantService;

    private PromotionMerchantService promotionMerchantService;

    private  PromotionRuleService promotionRuleService;
}
