package com.onepay.ma.service.handler.quicklink.Impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import com.onepay.ma.service.handler.externalClient.QuickLinkClient;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;

@Component
public class ExChangeTypeHandlerImpl implements Handler<RoutingContext> {

    private static final String OPERATOR = "operator";
    @Autowired
    private UserService userService;

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        JsonObject body = rc.getBodyAsJson();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(conBackUp -> {
                    return userService.get(conBackUp, xUserId).map(userData -> {
                        if (userData != null) {
                            body.put(OPERATOR, userData.getEmail());
                        }
                        return body;
                    });
                })
                .subscribe(a -> {
                    JsonObject res = QuickLinkClient.putExchangeRateType(linkId, xUserId, body);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
    }
}
