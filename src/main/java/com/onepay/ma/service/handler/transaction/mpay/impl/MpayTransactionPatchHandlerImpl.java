package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.service.mpay.impl.MocaMpayTransactionService;
import com.onepay.ma.service.service.mpay.impl.MpayRefundService;
import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.handler.transaction.domestic.impl.DomesticTransactionPatchHandler;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.mpay.MpayRefundReq;
import com.onepay.ma.service.models.mpay.MpayTransactionHistory;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.service.lock.LockService;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.subjects.BehaviorSubject;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 21-Jun-18.
 */
@Component
public class MpayTransactionPatchHandlerImpl implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        // String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        // if (ipAddress == null) {
        //     LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
        //     throw IErrors.VALIDATION_ERROR;
        // }
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        if (xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        final HttpServerRequest request = rc.request();
        // final IService iService = iService();
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (transactionId != null) {
            String body = rc.getBodyAsString();
            PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
            if (mapRequest == null) {
                LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND DATA EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            if (mapRequest.getPath().equals("/refund")) {
                PatchRequest<RefundData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {
                }.getType());
                boolean skipCallSynchronize = mapPatchRequest.isSkipCallSynchronize() == null || mapPatchRequest.isSkipCallSynchronize();
                if (mapPatchRequest.getValue() == null) {
                    throw IErrors.VALIDATION_ERROR;
                }
                mapPatchRequest.getValue().setCurrency(mapPatchRequest.getValue().getCurrency() == null ? "VND" : mapPatchRequest.getValue().getCurrency());

                // log ban ghi 14 tranh refund 2 lan cung luc
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), SQLConnectionFactory::dispose)
                        .flatMap(connBackUp -> {
                            long startTime = System.currentTimeMillis();
                            long delay = 2000;
                            return lock(connBackUp, transactionId, 120000, startTime, delay);
                        }).subscribe(isLock -> {
                            if(isLock) {
                                Observable<Double> obs = this.getAmount(clientReadOnly, clientOnline, clientBackUp, transactionId, mapPatchRequest);
                                // begin refund data
                                obs.subscribe(amount -> {

                                    // Moca check promotion
                                    // String transactionId = rc.request().getParam("id");
                                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), SQLConnectionFactory::dispose).flatMap(connOnline -> MocaMpayTransactionService.get(connOnline, transactionId)).subscribe(transaction -> {
                                        if (mapPatchRequest.getValue().getActualRefundAmount() != 0) {

                                            // MpayAmount amountDetail = transaction.getAmount();
                                            // double originalAmount = amountDetail.getTotal();
                                            // double refundedAmount = amountDetail.getRefundTotal();
                                            double inputAmountScreen = mapPatchRequest.getValue().getAmount();
                                            double actualRefundAmountScreen = mapPatchRequest.getValue().getActualRefundAmount();
                                            double offerDiscountAmount = transaction.getOfferDiscountAmount() != null ? transaction.getOfferDiscountAmount() : 0d;
                                            double paymentAmount = transaction.getPaymentAmount() != null ? transaction.getPaymentAmount() : 0d;


                                            // re-caculate actualRefund in backend
                                            // double merchantDiscount =
                                            // Math.round((inputAmountScreen/originalAmount)*transaction.getOfferDiscountAmount()*transaction.getMerchantDiscountRate());
                                            // double partnerDiscount =
                                            // Math.round((inputAmountScreen/originalAmount)*transaction.getOfferDiscountAmount()*transaction.getPartnerDiscountRate());
                                            // double actualRefundAmount = inputAmountScreen - merchantDiscount - partnerDiscount;
                                            double actualRefundAmount = Math.round(inputAmountScreen / (1 + (offerDiscountAmount / paymentAmount))); // update formular


                                            // check actualRefund
                                            if (actualRefundAmount != actualRefundAmountScreen) {
                                                LOGGER.log(Level.SEVERE, "[ VALIDATION ACTUAL_REFUND_AMOUNT PATCH  ] =>  ACTUAL_REFUND_AMOUNT INVALID");
                                                unlock(transactionId, clientBackUp);
                                                throw IErrors.VALIDATION_ERROR;
                                            }
                                        }

                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), SQLConnectionFactory::dispose)
                                                .flatMap(connBackUp -> this.merchantApproval.getMerchantApproval(connBackUp, transaction.getMerchantId()).flatMap(merchants -> {

                                                    // String requestId = xRequestId;
                                                    // fix bug no.3: https://git.onepay.vn/onepay/iportal/iportal-service/-/issues/9
                                                    // revert bug no.3:
                                                    String requestId = UUID.randomUUID().toString();
                                                    String refundRef;
                                                    if (mapPatchRequest.getValue().getRefund_reference() == null || mapPatchRequest.getValue().getRefund_reference().isEmpty()) {
                                                        // Init refund reference
                                                        refundRef = transaction.getMerchantId() + "_" + requestId;
                                                        if (refundRef.length() > 32) {
                                                            int largerInt = refundRef.length() - 32 + 1;
                                                            requestId = requestId.substring(0, requestId.length() - largerInt);
                                                            refundRef = transaction.getMerchantId() + "_" + requestId;
                                                        }
                                                    } else {
                                                        // Get refund reference
                                                        refundRef = mapPatchRequest.getValue().getRefund_reference();
                                                    }

                                                    final String refundReference = refundRef;
                                                    if (merchants == null || merchants.isEmpty()) {
                                                        LOGGER.log(Level.INFO, "****** CHECK INSERT REFUND REQUEST TO ONEPAY *****");
                                                        // Check history refund request
                                                        BehaviorSubject<Boolean> isOkSubject = BehaviorSubject.create();
//                                                        AtomicBoolean canInsertRefund = new AtomicBoolean(false);
                                                        this.refundApprovalService.getGetByTransRef(connBackUp, mapPatchRequest.getId())
                                                                .flatMap(Observable::just)
                                                                .subscribe(listRefundApproval -> Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), SQLConnectionFactory::dispose)
                                                                        .flatMap(connOnline -> this.checkHistoryRefund(connOnline, listRefundApproval, mapPatchRequest.getId(), mapPatchRequest.getValue().getStringHash()))
                                                                        .subscribe(isOkSubject::onNext, isOkSubject::onError, isOkSubject::onCompleted));
                                                        return isOkSubject.flatMap(isOk ->{
                                                            if (isOk) {
                                                                // Insert new request refund for OnePAY Approval
                                                                LOGGER.log(Level.INFO, "****** INSERT REFUND REQUEST TO ONEPAY *****");
                                                                return this.refundApprovalService.insertRequestOnepay3(connBackUp, userId, transaction.getMerchantId(),
                                                                        transactionId, mapPatchRequest.getValue().getAmount(),
                                                                        mapPatchRequest.getValue().getCurrency(), null, refundReference, RefundApproval.Status.REQUEST_ONEPAY.code, RefundData.Type.QR.getValue(), mapPatchRequest.getValue().getNote());
                                                            } else {
                                                                LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND HISTORY NOT MATCH");
                                                                return Observable.error(IErrors.INVALID_AMOUNT);
                                                            }
                                                        });
                                                    } else {
                                                        LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL WF MERCHANT APPROVAL *****");
                                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> this.refundApprovalService.insertRequest3(connBackUp, userId, transaction.getMerchantId(),
                                                                transactionId, mapPatchRequest.getValue().getAmount(), mapPatchRequest.getValue().getCurrency(), refundReference, RefundData.Type.QR.getValue(), mapPatchRequest.getValue().getNote()));
                                                    }
                                                })).subscribe(approval -> {
                                                    // call ep dong bo qr
                                                    if (skipCallSynchronize == false) {
                                                        
                                                        try {
                                                            OneSchedClient.synchronizeQR();
                                                        } catch (Exception e) {
                                                            LOGGER.log(Level.INFO, "qr call ep dong bo error " + e.getMessage());
                                                        }
                                                    }
                                                    unlock(transactionId, clientBackUp);
                                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, approval);
                                                    rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.CREATED.code());
                                                    rc.next();
                                                }, e -> {
                                                    LOGGER.log(Level.SEVERE, "[ INSERT REFUND APPROVAL ERROR  ] =>  " + e.getMessage());
                                                    unlock(transactionId, clientBackUp);
                                                    rc.fail(e);
                                                }, () -> LOGGER.log(Level.SEVERE, "[ INSERT REFUND APPROVAL COMPLETE  ]"));
                                    }, rc::fail);
                                }, rc::fail);
                            }
                        });
            }

        }
        
    }


    private void doRefund(RoutingContext rc, Map domesticRefund, JDBCClient clientOnline) {

        Object refund = domesticRefund.get("refund");
        if (refund instanceof MpayRefundReq) {

            final MpayRefundReq requestRefund = (MpayRefundReq) refund;
            
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {

                    JsonNode jsonOut = null;
                    try {
                        jsonOut = MSPClient.refund(requestRefund);
                    } catch (Exception e) {
        
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    if (jsonOut == null) {
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                return MpayRefundService.get(connOnline, jsonOut.get("id").asText());

            }).subscribe(refundData -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, refundData);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

            
        } else {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
            rc.next();
        }

    }
    
    private Observable<Map> checkIsMerchantApproval(JDBCClient clientBackUp, String transactionId
    , String userId, String ipAddress, Double amount, PatchRequest<RefundData> mapPatchRequest) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.merchantApproval.getMerchantApproval(connBackUp, mapPatchRequest.getValue().getMerchant_id())
//                                return this.merchantPermitService.isMerchantPermittedRefundApproval(connBackUp, mapPatchRequest.getValue().getMerchant_id(), mapPatchRequest.getValue().getType())
                            .flatMap(merchantApproval -> {
                                if (null != merchantApproval && !merchantApproval.isEmpty()) {
                                    LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                                    return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                        String tranRef = UUID.randomUUID().toString();
                                        return this.refundApprovalService.insertRequest(connBackUp, userId, mapPatchRequest.getValue().getMerchant_id(),
                                                transactionId, mapPatchRequest.getValue().getAmount(), "VND", tranRef
                                                , RefundData.Type.QR.getValue()).flatMap(approval -> {
                                            Map hashMap = new HashMap();
                                            hashMap.put("refund", approval);
                                            return Observable.just(hashMap);
                                        });

                                    });
                                } else {
                                   
                                    MpayRefundReq req = new MpayRefundReq();
                                    req.setAmount(amount);
                                    req.setMerchant_id(mapPatchRequest.getValue().getMerchant_id());
                                    req.setPayment_id(transactionId);
                                    req.setClient_id("MADM");
                                    req.setCurrency("VND");
                                    req.setOperator(userId);
                                    req.setReference(mapPatchRequest.getValue().getTransaction_reference());

                                    Map hashMap = new HashMap();
                                    hashMap.put("refund", req);
                                    return Observable.just(hashMap);

                                }
                            });
                });
    }

    
    private Observable<Double> getAmount(JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, PatchRequest<RefundData> mapPatchRequest) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            
                                            return MpayTransactionService.listHistory(connReadOnly, connOnline, connBackup, transactionId).map(qrTransactionHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = mapPatchRequest.getValue().getAmount();
                                                for (MpayTransactionHistory his : qrTransactionHistories) {
                                                    // LOGGER.info("qrTransactionHistories " + gson.toJson(his).toString() );
                                                    LOGGER.info("getTransaction_type " + his.getTransaction_type() );
                                                    LOGGER.info("getStatus " + his.getStatus() );
                                                    LOGGER.info("getAdvanced_status " + his.getAdvanced_status() );
                                                    // Case purchase ->
                                                    if ("Purchase".equalsIgnoreCase(his.getTransaction_type())) {
                                                        remainAmt += his.getAmount().getTotal();
                                                    } else if ("Successful".equalsIgnoreCase(his.getAdvanced_status()) // case refund success
                                                    || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content) // case request refund 405 || 401 => remainAmt += request refund amount
                                                            && (Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST.code)
                                                                    || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST_ONEPAY.code)))) {
                                                        remainAmt -= his.getAmount().getTotal();
                                                    }
                                                    LOGGER.info("remainAmt " + remainAmt );
                                                }
                                                
                                                LOGGER.info("refundAmt " + refundAmt );
                                                LOGGER.info("remainAmt " + remainAmt );

                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                    return refundAmt;
                                                } else {
                                                    LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                    throw IErrors.AMOUNT_REFUND_ERROR;
                                                }
                                            });
                                        });
                            });
                });
    }

    private Observable<Boolean> checkHistoryRefund(SQLConnection connOnline,List<JsonObject> approvalList,String paymentId,String stringHash) {
        if (stringHash == null || stringHash.isEmpty()) {
            LOGGER.info("stringHash is empty, khong check history refund");
            return Observable.just(true);
        }
        return MpayRefundService.getRefundByPaymentId(connOnline, paymentId).flatMap(refundData -> {
            String hisHash = "[]";
            List<JsonObject> hisHashList = new ArrayList<>();
            LOGGER.info("approvalList " + gson.toJson(approvalList).toString());
            LOGGER.info("refundData " + gson.toJson(refundData).toString());
            if (approvalList != null) {
                for (JsonObject refund : approvalList) {
                    if (refund.getInteger("N_STATUS") == 405) {
                        JsonObject his = new JsonObject();
                        his.put("S_ID", refund.getValue("N_ID").toString());
                        his.put("N_AMOUNT", String.valueOf(refund.getDouble("N_AMOUNT").longValue()));
                        his.put("S_STATE", "wfa");
                        his.put("S_TYPE", "REQUEST REFUND");
                        his.put("S_CURRENCY", refund.getString("S_CURRENCY"));
                        his.put("D_CREATE", refund.getValue("D_CREATE"));
                        hisHashList.add(his);
                    }
                }
            }
            if (refundData != null) {
                for (JsonObject refund : refundData) {
                    JsonObject his = new JsonObject();
                    String state = refund.getString("S_STATE");
                    String amount = String.valueOf(refund.getDouble("N_AMOUNT").longValue());
                    String type = "REFUND";
                    his.put("S_ID", refund.getString("S_ID"));
                    his.put("N_AMOUNT", amount);
                    his.put("S_STATE", state);
                    his.put("S_TYPE", type);
                    his.put("S_CURRENCY", refund.getString("S_CURRENCY"));
                    his.put("D_CREATE", refund.getString("D_CREATE"));
                    hisHashList.add(his);
                }
            }
            // sort by D_CREATE desc
            hisHashList.sort(
                    Comparator.comparing((JsonObject o) -> o.getString("D_CREATE"))
                            .reversed()  // Sắp xếp theo thứ tự giảm dần của D_CREATE
                            .thenComparing(o -> o.getString("S_ID")) // Sắp xếp theo S_ID nếu D_CREATE bằng nhau
            );

            // convert to string [S_AMOUNT, S_STATE, S_TYPE],[S_AMOUNT, S_STATE, S_TYPE,],.v.v
            if(!hisHashList.isEmpty()) {
                hisHash = hisHashList.stream().map(his -> "[" + his.getString("N_AMOUNT") + "," + his.getString("S_TYPE") + "," + his.getString("S_CURRENCY") + "," + his.getString("S_STATE")+ "]").reduce("", (a, b) -> a + b );
            }
            String stringHashDB = Utils.encodeHmacSha256Base64Url(hisHash, PropsUtil.get("key_hash_history_refund", "MDrR9%q`@ETJ_@[7B6$5jQ]CS4Z0qL}R-U,qfP8[ai&Oi|U|`BTc!V,C~T>^#M$"));
            LOGGER.info("hisHash " + hisHash);
            LOGGER.info("stringHashDB " + stringHashDB);
            LOGGER.info("stringHash " + stringHash);
            return Observable.just(stringHashDB.equals(stringHash));
        }).onErrorReturn(e -> {
            LOGGER.log(Level.SEVERE, "[ CHECK HISTORY REFUND ERROR  ] =>  " + e);
            return false;
        }).doOnNext(isOk -> LOGGER.info("[ CHECK HISTORY REFUND OK ? ] => " + isOk));
    }

    private Observable<Boolean> lock(SQLConnection connBackUp, String key, int timeoutMillis, long startTime, long delay) {
        return Observable.just(key).flatMap(approvalData -> {
            // insert approval for user group
            return this.lockService.lock(connBackUp, key, timeoutMillis, RefundData.Type.QR.getValue()).flatMap(lock -> {
                if (lock == true)
                    return Observable.just(lock);
                if (timeoutMillis < System.currentTimeMillis() - startTime) {
                    return Observable.just(lock);
                } else {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    return lock(connBackUp, key, timeoutMillis, startTime, delay);
                }
            });
        });

    }

    public void unlock(String key, JDBCClient clientBackUp) {
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.lockService.unlock(connBackUp, key, RefundData.Type.QR.getValue()).flatMap(lock -> {
                        return Observable.just(lock);
                    });
                }).subscribe();
    }

    @Autowired
    LockService lockService;

    @Autowired
    private MerchantApprovalService merchantApproval;
    @Autowired
    private RefundApprovalService refundApprovalService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionPatchHandler.class.getName());

}
