package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserPatchHandlerImpl;
import com.onepay.ma.service.service.UserMerchantService;
import com.onepay.ma.service.service.UserRoleService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserTerminalService;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/14/16.
 */
public interface UserPatchHandler extends Handler<RoutingContext> {
    static UserPatchHandlerImpl create(UserService userService, UserRoleService userRoleService, CacheGuava cacheGuava, UserMerchantService userMerchantService , UserTerminalService userTerminalService, UserPartnerService userPartnerService){
        return new UserPatchHandlerImpl(userService, userRoleService, cacheGuava, userMerchantService, userTerminalService, userPartnerService);
    }
}
