package com.onepay.ma.service.handler.pay_collect.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.pay_collect.UserQueryPayCollectDto;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import org.apache.commons.lang3.StringUtils;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class UserDownloadCollectHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(UserDownloadCollectHandlerImpl.class.getName());
    private final static Gson gson = new Gson();
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE TOTAL TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clienBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        JsonObject data = rc.getBodyAsJson();
        // LOGGER.info("Body data: " + data);
        String user_id = data.getString(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.USER_ID));
        String merchant_id = data.getString(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.MERCHANT_ID));
        String user_name = data.getString(ParamsPool.USER_NAME) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.USER_NAME));
        String acount = data.getString(ParamsPool.ACCOUNT) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.ACCOUNT));
        String bank = data.getString(ParamsPool.BANK) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.BANK));
        String lang = data.getString("lang");
        String reference = data.getString(ParamsPool.REFERENCE) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.REFERENCE));
        String state = data.getString(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(data.getString(ParamsPool.STATE));
        UserQueryPayCollectDto query = new UserQueryPayCollectDto();
        query.setUser_id(user_id);
        query.setUser_name(user_name);
        query.setAccount(acount);
        query.setBank(bank);
        query.setReference(reference);
        query.setState(state);
        query.setMerchant_id(merchant_id);
        query.setLang(lang);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        String maMain = data.getString(ParamsPool.MA_MAIN);
        LOGGER.log(Level.INFO, "userId: {}", userId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain download : ");
            String finalMerchantsList = MaPermission.getMc(userId, xRequestId, partnerId, merchant_id);
            query.setMerchant_id(finalMerchantsList);
            Map mdata = new HashMap();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        this.connectBackUp = connBackup;
                        return getObservable(rc, userId, query, requestData, mdata);
                    }).subscribe(map -> {
                        if (connectBackUp != null) {
                            connectBackUp.commitObservable();
                        }
                        int row = Integer.valueOf(map.get("row").toString());
                        LOGGER.log(Level.INFO, "maMain download map: " + map);
                        if (row <= serverConfig.getRowLevel()) {
                            // fileDownload.setExt("csv");
                            Message<UserQueryPayCollectDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                            QueueProducer.sendMessage(message);
                        } else {
                            // fileDownload.setExt("zip");
                            Message<UserQueryPayCollectDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                            QueueProducer.sendMessage(message);
                        }
                        rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                        rc.next();
                    }, throwable -> {
                        if (connectBackUp != null) {
                            connectBackUp.rollbackObservable();
                        }
                        rc.fail(throwable);
                    });
        } else  {
            Map mdata = new HashMap();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
                .flatMap(connBackup -> {
                    this.connectBackUp = connBackup;
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                            .flatMap(connPaycollect -> {
                                return merchantService.merchantPaycollects(connPaycollect, connBackup, merchant_id, userId,
                                        "paycollect").flatMap(merchants -> {
                                            List<String> merchantIdList =
                                                    merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                            query.setMerchant_id(String.join(",", merchantIdList));
                                            return getObservable(rc, userId, query, requestData, mdata);
                                        });
                            });
                }).subscribe(map -> {
                    if (connectBackUp != null) {
                        connectBackUp.commitObservable();
                    }
                    int row = Integer.valueOf(map.get("row").toString());
                    if (row <= serverConfig.getRowLevel()) {
                        // fileDownload.setExt("csv");
                        Message<UserQueryPayCollectDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                        QueueProducer.sendMessage(message);
                    } else {
                        // fileDownload.setExt("zip");
                        Message<UserQueryPayCollectDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                        QueueProducer.sendMessage(message);
                    }
                    rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                    rc.next();
                }, throwable -> {
                    if (connectBackUp != null) {
                        connectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
        }
    }

    private Observable<Map> getObservable(RoutingContext rc, String userId, UserQueryPayCollectDto query, Map requestData, Map data) {
        String fileName = "paycollect" + StringPool.UNDERLINE + "user_config" + StringPool.UNDERLINE;
        long date = new java.util.Date().getTime();
        fileName += date;
        String fileHashName = "";
        data.put("parameter", query);
        data.put("file_name", fileName);
        data.put("row", 1000);
        try {
            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("paycollect_user_config");
        fileDownload.setExt("xlsx");
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(query));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });
    }

    @Autowired
    private MerchantService merchantService;

}
