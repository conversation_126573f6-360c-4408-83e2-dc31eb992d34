package com.onepay.ma.service.handler.report.general.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.report.GeneralReport;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class GeneralReportGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
        String fromDate = rc.request().getParam("fromDate");
        String toDate = rc.request().getParam("toDate");
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 6) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));

        String merchant_ids = rc.request().getParam("merchant_ids") == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam("merchant_ids"));

        String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

        String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null
                ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

        String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));
        String transactionState = rc.request().getParam(ParamsPool.TRANSACTION_STATE) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_STATE));
        String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

        String transactionIdValue = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK
                : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));

        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0
                : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 100
                : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

        String finalCardNumber = FunctionUtil.isBeginIsACard(cardNumber) && cardNumber.contains("*")
                ? cardNumber.replaceAll("\\*", "0")
                : cardNumber;

        String version = rc.request().getParam(ParamsPool.VERSION) != null ? rc.request().getParam(ParamsPool.VERSION) : "";

        String appName = rc.request().getParam(ParamsPool.APPNAME) != null ? rc.request().getParam(ParamsPool.APPNAME) : "";

        String target = rc.request().getParam(ParamsPool.TARGET) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TARGET));

        GeneralReportQuery query = new GeneralReportQuery();
        query.setMerchantId(merchant_ids);
        query.setFromDate(fromDate);
        query.setToDate(toDate);
        query.setCardType(cardType);
        query.setPage(page);
        query.setPageSize(pageSize);
        query.setTransactionType(transactionType);
        query.setTransactionState(transactionState);
        query.setCardNumber(cardNumber);
        query.setMerchantTransactionRef(merchantTransactionRef);
        query.setOrderInfo(orderInfo);
        query.setTransactionId(transactionIdValue);
        query.setAppName(appName);
        query.setVersion(version);
        query.setTarget(target);
        // IN CASE COMPARE CARD NO HASH
        if (FunctionUtil.isCardData(finalCardNumber)) {
            rc.vertx().executeBlockingObservable(objectFuture -> {
                objectFuture.complete(FunctionUtil.oneSMHmac(finalCardNumber, serverConfig));
            }).subscribe(o -> {
                query.setCardNumber(String.valueOf(o));
                queryTransaction(query, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            });
        } else {
            queryTransaction(query, userId, clientReadOnly, clientOnline, clientBackUp, rc);
        }

    }

    private void queryTransaction(GeneralReportQuery query, String userId, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable<BaseList<GeneralReport>> obs = Observable
                .using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable
                                        .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService
                                                    .list(connOnline, connBackUp, query.getMerchantId(), userId, "international")
                                                    .flatMap(merchantsQt -> {
                                                        return merchantService
                                                                .list(connOnline, connBackUp, query.getMerchantId(), userId, "domestic")
                                                                .flatMap(merchantsNd -> {
                                                                    return merchantService
                                                                            .list(connOnline, connBackUp, query.getMerchantId(), userId, "mpay")
                                                                            .flatMap(merchantsQr -> {
                                                                                List<String> merchantIdQtList = merchantsQt.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                List<String> merchantIdNdList = merchantsNd.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                List<String> merchantIdQrList = merchantsQr.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                query.setMerchantIdQt(String.join(",", merchantIdQtList));
                                                                                query.setMerchantIdNd(String.join(",", merchantIdNdList));
                                                                                query.setMerchantIdQr(String.join(",", merchantIdQrList));

                                                                                return generalReportService.searchGeneralReport(connReadOnly,
                                                                                        connOnline, connBackUp, query);
                                                                            });
                                                                });
                                                    });
                                        });

                            });

                });
        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private GeneralReportService generalReportService;
}
