package com.onepay.ma.service.handler.user.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserGetHandler;
import com.onepay.ma.service.models.user.UserSearchQuery;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huynguyen on 3/9/16.
 */
public class UserGetHandlerImpl implements UserGetHandler {

    public UserGetHandlerImpl(UserService userService, UserPermissionService userPermissionService) {
        this.userService = userService;
        this.userPermissionService = userPermissionService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));

        if(userId.isEmpty()){
            int page = request.getParam(ParamsPool.PAGE) == null? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            int page_size = request.getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(request.getParam(ParamsPool.PAGE_SIZE));
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));
            String merchant_id = request.getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.MERCHANT_ID));
            String user_id = request.getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.USER_ID));
            String email = request.getParam(ParamsPool.EMAIL) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.EMAIL));
            String phone = request.getParam(ParamsPool.PHONE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.PHONE));

            UserSearchQuery query = new UserSearchQuery();
            query.setPage(page);
            query.setPageSize(page_size);
            query.setKeywords(keywords);
            query.setMerchant_id(merchant_id);
            query.setUser_id(user_id);
            query.setEmail(email);
            query.setPhone(phone);

            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose())
                    .flatMap(sqlConnection -> {
                        return userService.list(sqlConnection, query);

                    }).subscribe(users -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, users);
                rc.next();
            },throwable -> {
                rc.fail(throwable);
            });


        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose())
                    .flatMap(sqlConnection -> {
                        return userService.get(sqlConnection, userId);
            }).subscribe(user -> {
                if(user == null){
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
                rc.next();
            },throwable -> {
                rc.fail(throwable);
            });

        }

    }

    private UserService userService;

    private UserPermissionService userPermissionService;

}
