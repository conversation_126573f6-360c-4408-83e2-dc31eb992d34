package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.user.UserResetPassHandler;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OperatorChangePassHandlerImpl implements UserResetPassHandler {
    private static Logger LOGGER = Logger.getLogger(OperatorChangePassHandlerImpl.class.getName());


    @Autowired
    private OperatorService operatorService;

    @Autowired
    private EmailService emailService;

    @Override
    public void handle(RoutingContext rc) {
        try {
            LOGGER.info("START API RESET PASSWORD ");
            JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
            String userId = rc.get(ParamsPool.X_USER_ID);
     /*   if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }*/

            io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
            if (body == null) {
                LOGGER.log(Level.SEVERE, "[ OPERATOR RESET PASS ] => BODY POST IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;
            }
            String operatorId =  body.getString("operator_id") != null ? String.valueOf(body.getString("operator_id")).trim() : StringPool.BLANK;
            if (operatorId == null || operatorId.isEmpty()) {
                LOGGER.log(Level.SEVERE, "[ USER RESET PASS ] => S_ID POST IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;
            }
            String oldPass = body.getString("oldPassword") != null ? String.valueOf(body.getString("oldPassword")).trim() : StringPool.BLANK;
            if (oldPass == null || oldPass.isEmpty()) {
                LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => OLD PASS IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;
            }
            String newPass = body.getString("newPassword") != null ? String.valueOf(body.getString("newPassword")).trim() : StringPool.BLANK;
            if (newPass == null || newPass.isEmpty()) {
                LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => NEW PASS IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;

            }

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        connectionBackUp = sqlConnection;
                        return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                            return operatorService.getOperatorById(sqlConnection, operatorId).flatMap(operator -> {
                                if (operator == null || !oldPass.equals(operator.getPassword())) {
                                    LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => USER NOT EXIST IN SYSTEM  OR OLD PASSWORD NOT MATCH ");
                                    throw IErrors.RESOURCE_NOT_FOUND;
                                }

                                return operatorService.resetPass(sqlConnection, operatorId, newPass).flatMap(operatorData -> {
                                    Map map = new HashMap();
                                    map.put("operator", operatorData);
                                    return Observable.just(map);
                                });
                            });
                        });
                    }).subscribe(map -> {
                OperatorDTO operator = (OperatorDTO) map.get("operator");
                if (operator == null) {
                    if (connectionBackUp != null) {
                        connectionBackUp.rollbackObservable();
                    }
                    throw IErrors.VALIDATION_ERROR;
                } else {
                    emailService.sentMailResetPassOperator(operator, newPass);
                    connectionBackUp.commitObservable();
                }

                rc.put(ParamsPool.HANDLER_DATA_RESULT, operator);
                rc.next();
            }, throwable -> {
                if (connectionBackUp != null) {
                    connectionBackUp.rollbackObservable();
                }
                rc.fail(throwable);
            });
        }catch (Exception e){
            rc.fail(e);
        }

    }
    private SQLConnection connectionBackUp;

}
