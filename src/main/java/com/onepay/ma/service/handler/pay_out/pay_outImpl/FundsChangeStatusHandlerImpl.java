package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class FundsChangeStatusHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private UserService userService;

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                String xUserId = rc.get(ParamsPool.X_USER_ID);
                String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
                String userId = rc.get(ParamsPool.USER_ID);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                if (xUserId == null) {
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject body = rc.getBodyAsJson();

                String fundsId = body.getString("funds_id");
                String state = body.getString("state");
                String partnerID = null;
                if (body.containsKey("partner_id")) {
                    partnerID = body.getString("partner_id");
                }

                String userName = null;
                if (body.containsKey("user_name")) {
                    userName = body.getString("user_name");
                }
                String password = null;
                if (body.containsKey("password")) {
                    password = body.getString("password");
                }
                String finalPartnerID = partnerID;
                String finalUserName = userName;
                String finalPassword = password;
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            return userService.getOneAm(connBackUp, xUserId).map(user -> {
                                JsonObject result = OnePayoutClient.changeStatusFunds(fundsId, state, finalPartnerID, finalUserName, finalPassword, userId, xUserId, xRequestId, user.getS_id(), user.getName());
                                return result;
                            });
                        }).subscribe(a -> {
                    // call ep dong bo po
                    try {
                        OneSchedClient.synchronizePO();
                    } catch (Exception e) {
                        LOGGER.log(Level.INFO, "po call ep dong bo error " + e.getMessage());
                    }
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, a);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Change status operator Error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    private static Logger LOGGER = Logger.getLogger(FundsChangeStatusHandlerImpl.class.getName());

}
