package com.onepay.ma.service.handler.transaction.onebill.impl;

import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class OneBillClientGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientMerchantPortal = rc.get(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f -> f.dispose())
                .flatMap(connMerchantPortal -> {
                    return oneBillTransactionService.listClient(connMerchantPortal);
                }).subscribe(transaction -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    @Autowired
    private OneBillTransactionService oneBillTransactionService;
}
