package com.onepay.ma.service.handler.transaction.promotion.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.handler.transaction.promotion.PromotionTransactionPatchHandler;
import com.onepay.ma.service.service.PromotionTransactionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/1/16.
 */
public class PromotionTransactionPatchHandlerImpl implements PromotionTransactionPatchHandler {


    public PromotionTransactionPatchHandlerImpl(PromotionTransactionService promotionTransactionService) {
        this.promotionTransactionService = promotionTransactionService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ PROMOTION TRANSACTION PATCH ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if(ipAddress == null){
            LOGGER.log(Level.SEVERE, "[ PROMOTION TRANSACTION PATCH ] => IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        if(xRequestId == null){
            LOGGER.log(Level.SEVERE, "[ PROMOTION TRANSACTION PATCH ] => X-REQUEST-ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        //final HttpServerRequest request = rc.request();
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        if(transactionId != null){
            String body = rc.getBodyAsString();
            PatchRequest mapPatchRequest =  gson.fromJson(body, PatchRequest.class);
            if(mapPatchRequest == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            if(mapPatchRequest.getPath().equals("/revert")) {

                Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                        .flatMap(connPr -> {
                            connectionPR = connPr;
                            return connectionPR.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return promotionTransactionService.revert(connectionPR, transactionId).flatMap(integer -> {
                                    return connPr.commitObservable().map(aVoidC -> {
                                       return integer;
                                    });
                                });
                            });
                        }).subscribe(integer -> {
                            FunctionUtil.sendNextNullContext(rc, 202);
                        }, throwable -> {
                            if(connectionPR != null){
                                connectionPR.rollbackObservable();
                            }
                            rc.fail(throwable);
                        });

            }else{
                throw IErrors.VALIDATION_ERROR;
            }

        }else{
            throw IErrors.VALIDATION_ERROR;
        }

    }


    private SQLConnection connectionPR = null;

    private PromotionTransactionService promotionTransactionService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(PromotionTransactionPatchHandler.class.getName());

}
