package com.onepay.ma.service.handler.externalClient;

import static java.nio.charset.StandardCharsets.*;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gson.Gson;
import com.onepay.ma.service.models.PaymentLinkDto;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;

import io.vertx.core.json.JsonObject;

public class QuickLinkClient {

    private static final Logger LOGGER = Logger.getLogger(QuickLinkClient.class.getName());

    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static final String ONEPAY_QUICKLINK_SERVICE_BASE_URL = PropsUtil.get("onepay_quicklink_service_base_url", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    public static JsonObject getStaticLink(String userId, String xUserId, String xRequestId, String merchantIds, String merchantBNPLIds, String merchantAppleIds) {
        JsonObject jsonReturn = null;
        StringBuilder requestURI = new StringBuilder("/static-link?");
        if (!merchantIds.isEmpty() && merchantBNPLIds.isEmpty() && merchantAppleIds.isEmpty()) {
            requestURI.append("merchantIds=").append(merchantIds);
        } else if (merchantIds.isEmpty() && !merchantBNPLIds.isEmpty() && merchantAppleIds.isEmpty()) {
            requestURI.append("merchantBNPLIds=").append(merchantBNPLIds);
        } else if (merchantIds.isEmpty() && merchantBNPLIds.isEmpty() && !merchantAppleIds.isEmpty()) {
            requestURI.append("merchantAppleIds=").append(merchantAppleIds);
        } else {
            requestURI.append("merchantIds=").append(merchantIds).append("&merchantBNPLIds=").append(merchantBNPLIds).append("&merchantAppleIds=").append(merchantAppleIds);
        }
        String requestMethod = "GET";
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_QUICKLINK_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI.toString()));
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request(requestMethod, ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestURI.toString(),
                    signedHeaders, null, requestTimeOut, requestTimeOut);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Static Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Static Link Body Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject getStaticDetail(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/static-link/" + linkId;

        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Detail Static Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Detail Static Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject putStaticState(String linkId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/static-link-state/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Change State Static Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Change State Static Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getPaymentLink(Map<String, Object> mapBody) {
        JsonObject jsonReturn = new JsonObject();
        String requestURI = "/all-payment-link?";
        String requestMethod = "POST";
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            JsonObject body = new JsonObject(mapBody);
            LOGGER.info("===data in body: " + body.encode());
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request(requestMethod, ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestURI, signedHeaders, body.encode().getBytes(UTF_8), requestTimeOut, requestTimeOut);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Search Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Search Payment Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject download(PaymentLinkDto mapBody) {
        LOGGER.fine("===in download payment link:");
        JsonObject jsonReturn = new JsonObject();
        String requestURI = "/all-payment-link?";
        String requestMethod = "POST";
        // Milliseconds
        int requestTimeOut = 60000;
        try {
            JsonObject body = paymentLinkDtoToJsonObject(mapBody);
            LOGGER.fine("====Search body payment link:" + body.toString());
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request(requestMethod, ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestURI, signedHeaders, body.encode().getBytes(UTF_8), requestTimeOut, requestTimeOut);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Search Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Search Payment Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    private static JsonObject paymentLinkDtoToJsonObject(PaymentLinkDto paymentLinkDto) {
        JsonObject jObject = new JsonObject();
        jObject.put("keyword", paymentLinkDto.getKeyword());
        jObject.put("page", paymentLinkDto.getPage());
        jObject.put("pageSize", paymentLinkDto.getPageSize());
        jObject.put("state", paymentLinkDto.getState());
        jObject.put("installmentMerchantId", paymentLinkDto.getInstallmentMerchantId());
        jObject.put("paynowMerchantId", paymentLinkDto.getPaynowMerchantId());
        jObject.put("bnplMerchantId", paymentLinkDto.getBnplMerchantId());
        jObject.put("email", paymentLinkDto.getEmail());
        jObject.put("merchantIds", paymentLinkDto.getMerchantIds());
        jObject.put("merchantBNPLIds", paymentLinkDto.getMerchantBNPLIds());
        jObject.put("merchantAppleIds", paymentLinkDto.getMerchantAppleIds());
        jObject.put("fromDate", paymentLinkDto.getFromDate());
        jObject.put("toDate", paymentLinkDto.getToDate());
        
        return jObject;
    }

    public static JsonObject getPaymentLinkDetail(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/payment-link/" + linkId;
        LOGGER.fine(() -> "requestUri:" + Utils.excludeSensitiveInfo(requestUri));
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Detail Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Detail Payment Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }

    public static JsonObject putPaymentState(String linkId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/payment-link-state/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Change State Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Change State Payment Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject postPaymentLink(JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/payment-link";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("POST", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            } else if (resStatus == 409) {
                throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
            }
            LOGGER.fine(() -> "Create Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Create Payment Link Resopnse:" + resContent);

        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject putPaymentLink(String linkId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/payment-link/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            } else if (resStatus == 409) {
                throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
            }
            LOGGER.fine(() -> "Update Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Update Payment Link Resopnse:" + resContent);

        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getMerchantProfile() {
        JsonObject jsonReturn = null;
        String requestUri = "/merchant-profile?keywords=&page=0&pageSize=999999";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Get Merchant Profile Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Get Merchant Profile Payment Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getMerchantProfileById(String merchantIds, String merchantBNPLIds, String merchantAppleIds) {
        JsonObject jsonReturn = null;
        StringBuilder requestURI = new StringBuilder("/list-merchant-profile?");
        if (!merchantIds.isEmpty() && merchantBNPLIds.isEmpty() && merchantAppleIds.isEmpty()) {
            requestURI.append("merchantIds=").append(merchantIds);
        } else if (merchantIds.isEmpty() && !merchantBNPLIds.isEmpty() && merchantAppleIds.isEmpty()) {
            requestURI.append("merchantBNPLIds=").append(merchantBNPLIds);
        } else if (merchantIds.isEmpty() && merchantBNPLIds.isEmpty() && !merchantAppleIds.isEmpty()) {
            requestURI.append("merchantAppleIds=").append(merchantAppleIds);
        } else {
            requestURI.append("merchantIds=").append(merchantIds).append("&merchantBNPLIds=").append(merchantBNPLIds).append("&merchantAppleIds=").append(merchantAppleIds);
        }
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestURI, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Get Merchant Profile by profileIds Status Response:" + resStatus);
            LOGGER.fine(() -> "Get Merchant Profile by profileIds Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getMerchantIdByProfile(String merchantIds, String payMethod) {
        JsonObject jsonReturn = null;
        String requestUri = "/merchant-id-by-profile"; //?merchantIds=" + merchantIds + "&payMethod=" + payMethod;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            // build body
            JsonObject body = new JsonObject();
            body.put("merchantIds", merchantIds);
            body.put("payMethod", payMethod);
            // Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);
            Map<String, Object> resMap = request("POST", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);
            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            LOGGER.info("getMerchantIdByProfile resContent:" + resContent);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Get Merchant Id by profileIds Status Response:" + resStatus);
            LOGGER.fine(() -> "Get Merchant Id by profileIds Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static Map<String, Object> request(String method, String url, Map<String, String> headers, byte[] content, int connectTimeout, int readTimeout) {
        Map<String, Object> result = null;
        try {
            LOGGER.fine(() -> "url: " + Utils.excludeSensitiveInfo(url) + ", method: " + method + ", headers: " + headers + ", content: " + (content != null ? new String(content, UTF_8) : "null"));
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod(method);
            if (headers != null)
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    conn.setRequestProperty(header.getKey(), header.getValue());
                }
            if (method.matches("^(POST|PUT)$")) {
                conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length : 0));
                if (content != null && content.length > 0)
                    conn.setDoOutput(true);
            }
            LOGGER.fine("here 1");
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setInstanceFollowRedirects(false);
            conn.connect();
            // Send request content
            if (method.matches("^(POST|PUT)$") && content != null && content.length > 0) {
                DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
                wr.write(content);
                wr.flush();
                wr.close();
            }
            LOGGER.fine("here 2 " + conn.getResponseCode());
            int statusCode = conn.getResponseCode();
            result = new LinkedHashMap<>();
            result.put(HTTP_STATUS_CODE, statusCode);
            // Get response header
            Map<String, List<String>> resHeaders = conn.getHeaderFields();
            result.put(HTTP_HEADERS, resHeaders);

            // Get response content
            InputStream is = statusCode < 400 ? conn.getInputStream() : conn.getErrorStream();
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1)
                bout.write(buf, 0, length);
            is.close();
            bout.flush();
            byte[] resContent = bout.toByteArray();
            result.put(HTTP_CONTENT, resContent);
            bout.close();
            String logContent = new String(resContent, UTF_8);
            if (logContent.length() > 100)
                logContent = logContent.substring(0, 100) + "...";
            LOGGER.fine("status_code: " + statusCode + ", headers: " + resHeaders + ", content: " + logContent);
            // Keepalive
            // conn.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, e.getMessage(), e);
        }
        return result;
    }

    public static JsonObject getExchangeRate(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/exchange-rate/" + linkId;
        LOGGER.fine(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "get exchangeRate Response:" + resStatus);
            LOGGER.fine(() -> "get exchangeRate Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[GET EXCHANGE RATE ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }

    public static JsonObject getCurrencyByTempId(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/exchange-rate/getCurrencyByTemp/" + linkId;
        LOGGER.fine(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "get exchangeRate Response:" + resStatus);
            LOGGER.fine(() -> "get exchangeRate Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[GET EXCHANGE RATE ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }

    public static JsonObject getCurrencyWithOutVcb(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/exchange-rate/getCurrencyWithoutVcb/" + linkId;
        LOGGER.fine(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "get exchangeRate Response:" + resStatus);
            LOGGER.fine(() -> "get exchangeRate Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[GET EXCHANGE RATE ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }

    public static JsonObject getExchangeVCB(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/exchange-rate-vcb/" + linkId;
        LOGGER.fine(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "get exchangeRate VCB Response:" + resStatus);
            LOGGER.fine(() -> "get exchangeRate VCB Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[GET EXCHANGE RATE VCB ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }

    public static JsonObject putExchangeRate(String linkId, String userId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/exchange-rate/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put(ParamsPool.X_USER_ID, userId);

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "update exchange rate Response:" + resStatus);
            LOGGER.fine(() -> "update exchange rate Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject putExchangeRateState(String linkId, String userId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/exchange-rate/change-state/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put(ParamsPool.X_USER_ID, userId);

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Change State Exchange rate Status Response:" + resStatus);
            LOGGER.fine(() -> "Change State Exchange rate Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject putExchangeRateType(String linkId, String userId, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/exchange-rate/change-type/" + linkId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put(ParamsPool.X_USER_ID, userId);

            Map<String, Object> resMap = request("PUT", ONEPAY_QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Change Type Exchange rate Status Response:" + resStatus);
            LOGGER.fine(() -> "Change Type Exchange rate Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
}
