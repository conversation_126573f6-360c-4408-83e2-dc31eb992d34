package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.onepay.commons.util.Convert;
import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Logger;

/**
 * Created by anhkh on 10-Aug-17.
 */
@Component
public class MpayOrderQueryHandler implements Handler<RoutingContext> {

    private static final Logger logger = Logger.getLogger(MpayOrderQueryHandler.class.getName());
    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd\'T\'HHmmss\'Z\'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);

        String orderId = rc.request().getParam(ParamsPool.ORDER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_ID));
        String queryOut = "waiting";
        if (orderId.isEmpty()) {
            throw IErrors.VALIDATION_ERROR;
        }

        JsonNode jsonOut = MSPClient.queryTxn(orderId);

        if (jsonOut != null) {
            orderId = jsonOut.has("id") ? jsonOut.get("id").asText() : null;
            String state = jsonOut.has("state") ? jsonOut.get("state").asText() : null;
            String expiredTime = jsonOut.has("expire_time") ? jsonOut.get("expire_time").asText() : null;
            boolean blPayment = jsonOut.has("payments");
            if (state.equalsIgnoreCase("closed")) queryOut = "closed";
            else if (state.equalsIgnoreCase("paid")) queryOut = "paid";
            else if ("not_paid".equals(state) && blPayment && jsonOut.get("payments").findValuesAsText("state").contains("pending"))
                queryOut = "processing";
            else queryOut = "waiting";
            if (expiredTime != null) {
                String mspTimeFormat = "yyyyMMdd'T'HHmmss'Z'";
                Date dTimeout = Convert.toDate(expiredTime, mspTimeFormat);
                String sCDate = yyyyMMddTHHmmssZ.format(new Date());
                Date cDate = Convert.toDate(sCDate, mspTimeFormat);
                if (dTimeout.before(cDate)) queryOut = "timeout";
            }

        } else queryOut = "waiting"; //Chua tao Order

        logger.fine("queryOut=" + queryOut);

        final String orderId2 = orderId;
        final String queryOut2 = queryOut;
        if (orderId2 != null) {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return MpayTransactionService.getByOrderId(connOnline, orderId2);
                    }).subscribe(mpayTransaction -> {

                Map result = new HashMap();
                result.put("result", queryOut2);
                result.put("order_id", orderId2);
                result.put("payment", mpayTransaction);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
                rc.next();
            });
        } else {
            Map result = new HashMap();
            result.put("result", queryOut);
            result.put("order_id", orderId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
            rc.next();
        }
    }

}
