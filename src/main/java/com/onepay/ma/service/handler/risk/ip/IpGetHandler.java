package com.onepay.ma.service.handler.risk.ip;

import com.onepay.ma.service.handler.risk.ip.impl.IpGetHandlerImpl;
import com.onepay.ma.service.service.IpService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface IpGetHandler extends Handler<RoutingContext>  {
    static IpGetHandlerImpl create(IpService ipService){
        return new IpGetHandlerImpl(ipService);

    }
}
