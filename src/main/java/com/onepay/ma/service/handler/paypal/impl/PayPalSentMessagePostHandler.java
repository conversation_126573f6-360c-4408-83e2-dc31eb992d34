package com.onepay.ma.service.handler.paypal.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.externalClient.PayPalClient;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.paypal.*;
import com.onepay.ma.service.service.paypal.PaypalDisputeService;
import com.onepay.ma.service.service.paypal.PaypalNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class PayPalSentMessagePostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ PP SENT MESSAGE GENERATOR] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        // LOGGER.log(Level.WARNING, " PAYPAL DISPUTE SENT MESSAGE: " + rc.getBodyAsJson().encode());
        String disputeId = rc.request().getParam("id");
        if(disputeId ==null || disputeId.isEmpty()){
            LOGGER.log(Level.SEVERE, "[ PAYPAL DISPUTE SENT MESSAGE] => DisputeId is not exit");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient jdbcClientB = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JsonObject body =  rc.getBodyAsJson();

        String message =  body.getString("message");
        PaypalTransaction paypalTransaction = new PaypalTransaction();
        paypalTransaction.setDisputeId(disputeId);
        Observable<String> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClientB), f -> f.dispose())
                .flatMap(connBackup -> {
                connectionB = connBackup;
                    return connectionB.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return paypalDisputeService.getPaypalTransaction(connectionB,paypalTransaction).flatMap(listPaypalTrans -> {
                            if (listPaypalTrans == null ||listPaypalTrans.isEmpty()) {
                                throw IErrors.INTERNAL_SERVER_ERROR;
                            }
                            String merchantId = listPaypalTrans.get(0).getSellerMerchantId();
                            JsonNode js = PayPalClient.postSentMessage(disputeId, message,merchantId);
                            if (js == null) {
                                throw IErrors.INTERNAL_SERVER_ERROR;
                            }
                            JsonNode links = js.get("links");
                            Iterator<JsonNode> i = links.iterator();
                            while (i.hasNext()) {
                                JsonNode current = i.next();
                                if (current.get("method").asText().equalsIgnoreCase("GET")) {
                                    return Observable.just("OK");
                                }
                            }
                            LOGGER.log(Level.SEVERE, "[ PP MERCHANT GENERATOR] => URL NOT FOUND");
                            throw IErrors.INTERNAL_SERVER_ERROR;
                    });
                });
            });


        obs.subscribe(result -> {
            if(connectionB != null){
                connectionB.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
            rc.next();
        }, throwable -> {
            if(connectionB != null){
                connectionB.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }
    @Autowired
    private PaypalDisputeService paypalDisputeService;

    private SQLConnection connectionB;

    private static Logger LOGGER = Logger.getLogger(PayPalSentMessagePostHandler.class.getName());
}
