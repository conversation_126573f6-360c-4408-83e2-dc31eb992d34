package com.onepay.ma.service.handler.mpayNotification.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.DomesticRefundParameter;
import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.models.notification.MpayNotificationPostDto;
import com.onepay.ma.service.models.notification.MpayvnNotificationMsg;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.notification.CustomerAppNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 23-Mar-18.
 */
@Component
public class PostCustomerNotificationHandler implements Handler<RoutingContext> {


    @Autowired
    @Qualifier(value = "serviceQueueIn")
    private Queue serviceQueueIn;

    @Autowired
    @Qualifier(value = "serviceQueueOut")
    private Queue serviceQueueOut;

    @Override
    public void handle(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);

        final HttpServerRequest request = rc.request();
        LOGGER.log(Level.INFO, "LISTEN TO CUSTOMER NOTIFICATION/n");
        // LOGGER.log(Level.INFO, "CONTENT: " + rc.getBodyAsString());

        String body = rc.getBodyAsString();
        MpayvnNotificationMsg mapBody = gson.fromJson(body, MpayvnNotificationMsg.class);
        if (mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        mapBody.setSender(xUserId == null ? StringPool.BLANK : xUserId );

        Map requestData = new HashMap<>();

        Message<MpayvnNotificationMsg> message = new Message<>(mapBody, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), serviceQueueOut,serviceQueueIn );
        QueueProducer.sendMessage(message);



        rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
        rc.next();
    }

    private final static Gson gson = new Gson();


    private static Logger LOGGER = Logger.getLogger(PostCustomerNotificationHandler.class.getName());
}
