package com.onepay.ma.service.handler.refund.domestic.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.onecomm.payment.ws.*;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/1/16.
 */
@Component
public class DomesticRefundPatchHandler implements Handler<RoutingContext> {


    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if (ipAddress == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        HttpClient httpClient = rc.vertx().createHttpClient();
        String body = rc.getBodyAsString();
        PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
        if (mapRequest == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND DATA EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        if (mapRequest.getPath().equals("update")) {
            PatchRequest<RefundConfirmReq> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundConfirmReq>>() {
            }.getType());
            if (mapPatchRequest.getValue() == null) {
                LOGGER.log(Level.SEVERE, "[VALIDATE QUERY DATA PATCH] => REQUEST EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            LOGGER.log(Level.INFO, "***** BEGIN UPDATE *****");
            // LOGGER.log(Level.INFO, "DOMESTIC REFUND REQUEST " + mapPatchRequest.getValue());
            byte[] encoded = FunctionUtil.encodeHessian(mapPatchRequest.getValue());
            Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
            HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

            req.putHeader("Content-Type", "x-application/hessian");

            req.toObservable().subscribe(httpClientResponse -> {
                httpClientResponse.bodyHandler(responseData -> {
                    // String json = responseData.toString("UTF-8");

                    io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                    byte[] bytesData = new byte[bufferDataResp.length()];

                    for (int i = 0; i < bufferDataResp.length(); i++) {
                        byte data = bufferDataResp.getByte(i);
                        bytesData[i] = data;
                    }

                    RefundConfirmRes resp = (RefundConfirmRes) FunctionUtil.decodeHessian(bytesData);
                    LOGGER.log(Level.INFO, "DOMESTIC REFUND ONECOMM RESPONE " + Utils.mask(resp));
                    if (resp == null) {
                        LOGGER.log(Level.SEVERE, "[UPDATE REFUND RESULT ] => FAIL");
                        throw IErrors.UPDATE_ERROR;
                    } else {
                        Map result = new HashMap<>();
                        result.put("result_code", resp.getStatus());
                        if (resp.getStatus() != 1) {
                            LOGGER.log(Level.SEVERE, "[UPDATE REFUND  RESULT ] => CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
                            throw IErrors.UPDATE_ERROR;
                        } else {
                            onepayManual(rc, clientBackUp, clientOnline, mapPatchRequest.getValue().getRefund_transactionId(),result);
                        }

                    }

                });
                httpClientResponse.exceptionHandler(throwable -> {
                    rc.fail(throwable);
                });
            });

            req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
            req.write(buffer);
            req.end();
        }

    }

    private void onepayManual(RoutingContext rc, JDBCClient clientBackUp, JDBCClient clientOnline, Long refundTransactionId, Map result) {
        LOGGER.log(Level.INFO, "START UPDATE DATABASE ");
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return refundApprovalService.updateDomesticRefundQrStatus(connOnline, refundTransactionId);
                            });
                }).subscribe(qrRefund -> {
            // LOGGER.log(Level.INFO, "END UPDATE DATABASE qrRefund " + qrRefund);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    @Autowired
    RefundConfig refundConfig;

    @Autowired
    RefundApprovalService refundApprovalService;

    private final static Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(DomesticRefundPatchHandler.class.getName());
}
