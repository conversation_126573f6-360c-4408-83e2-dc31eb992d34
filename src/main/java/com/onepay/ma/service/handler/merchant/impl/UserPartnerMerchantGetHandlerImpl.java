package com.onepay.ma.service.handler.merchant.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.merchant.UserPartnerMerchantGetHandler;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

public class UserPartnerMerchantGetHandlerImpl implements UserPartnerMerchantGetHandler {

    public UserPartnerMerchantGetHandlerImpl(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientPaycollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String userId = request.getParam("userId") == null ? StringPool.BLANK : request.getParam("userId");
        if (!userId.isEmpty()) {
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS)).trim();
            String type = request.getParam(ParamsPool.TYPE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.TYPE));
            String currencyCode = request.getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.CURRENCY_CODE));
            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientPaycollect), f -> f.dispose())
                                .flatMap(connPaycollect -> {
                            return merchantService.listMerchantByListMerchantId(connOnline, connBackUp,connPaycollect, userId, keywords, page, type, currencyCode);
                        });
                    });
                }).subscribe(merchants -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            throw IErrors.VALIDATION_ERROR;
        }


    }

    private MerchantService merchantService;
}
