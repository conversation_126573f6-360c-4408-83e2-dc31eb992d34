package com.onepay.ma.service.handler.pay_collect.impl;

import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;

import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

@Component
public class GetAllMerchantHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    private MerchantService merchantService;

    public GetAllMerchantHandlerImpl(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    private static Logger LOGGER = Logger.getLogger(GetAllMerchantHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientPaycollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String userId = request.getParam("userId") == null ? StringPool.BLANK : request.getParam("userId");

        if (!userId.isEmpty()) {
            String type = request.getParam(ParamsPool.TYPE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.TYPE));
            
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientPaycollect), f -> f.dispose())
                                .flatMap(connPaycollect -> {
                            return merchantService.listAllMerchantPaycollect(connBackUp, connPaycollect, type, userId);
                        });
                    });
                }).subscribe(merchants -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }
    
}
