package com.onepay.ma.service.handler.payment.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.InternationalAuthPaymentService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 27-May-17.
 */
@Component
public class PaymentGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if(transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));
                oToDate = DateUtils.addMinutes(oToDate, 1);

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                fromDate = sdf.format(oFromDate);
                toDate = sdf.format(oToDate);

            } catch (Exception e) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if(months > 6){
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

            String authenticationState = rc.request().getParam(ParamsPool.AUTHENTICATION_STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHENTICATION_STATE));

            String authenticationCode = rc.request().getParam(ParamsPool.AUTHENTICATION_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHENTICATION_CODE));

            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            String transactionIdValue = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK  : rc.request().getParam(ParamsPool.TRANSACTION_ID);

            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK  : rc.request().getParam(ParamsPool.CARD_TYPE);

            String source = rc.request().getParam(ParamsPool.SOURCE) == null ? StringPool.BLANK  : rc.request().getParam(ParamsPool.SOURCE);

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            InternationalAuthPaymentQuery query = new InternationalAuthPaymentQuery();
            query.setPage(page);
            query.setPageSize(pageSize);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setCardNumber(cardNumber);
            query.setOrderInfo(orderInfo);
            query.setMerchantTransactionRef(merchantTransactionRef);
            query.setTransactionId(transactionIdValue);
            query.setAuthenticationState(authenticationState);
            query.setAuthenticationCode(authenticationCode);
            query.setCurrency(currency);
            query.setKeywords(keywords);
            query.setMerchantId(merchantId);
            query.setCardType(cardType);
            query.setSource(source);

            // IN CASE COMPARE CARD NO HASH
            if (FunctionUtil.isCardData(cardNumber)) {
                rc.vertx().executeBlockingObservable(objectFuture -> {
                    objectFuture.complete(FunctionUtil.oneSMHmac(cardNumber, serverConfig));
                }).subscribe(o -> {
                    query.setCardNumber(String.valueOf(o));
                    queryTransaction(query, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                });
            } else {
                queryTransaction(query, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            }

        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f-> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f-> f.dispose())
                                .flatMap(connBackup -> {
                                    return authPaymentService.get(connOnline, connBackup, transactionId);
                                });
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }


    private void queryTransaction(InternationalAuthPaymentQuery query, String merchantId, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable<Transactions<InternationalAuthPayment>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                query.setMerchantId(String.join(",", merchantIdList));
                                                return this.authPaymentService.list(connReadOnly, connOnline, query);
                                            });

                                        });

                            });

                });
        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    @Autowired
    private InternationalAuthPaymentService authPaymentService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ServerConfig serverConfig;
}
