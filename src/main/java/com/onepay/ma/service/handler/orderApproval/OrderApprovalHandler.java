package com.onepay.ma.service.handler.orderApproval;

import com.onepay.ma.service.handler.orderApproval.impl.SSDomsOrderApprovalPostHandler;
import com.onepay.ma.service.handler.orderApproval.impl.SSInterOrderApprovalPostHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrderApprovalHandler {

    @Autowired
    private SSInterOrderApprovalPostHandler ssInterOrderApprovalPostHandler;

    @Autowired
    private SSDomsOrderApprovalPostHandler ssDomsOrderApprovalPostHandler;

    public Handler postInternational() {
        return ssInterOrderApprovalPostHandler;
    }


    public Handler postDomestic() {
        return ssDomsOrderApprovalPostHandler;
    }

}
