package com.onepay.ma.service.handler.promotion.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.promotion.PromotionDeleteHandler;
import com.onepay.ma.service.models.Approval;
import com.onepay.ma.service.models.ApprovalCondition;
import com.onepay.ma.service.models.PromotionStatus;
import com.onepay.ma.service.service.ApprovalService;
import com.onepay.ma.service.service.PromotionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by huynguyen on 4/2/16.
 */
public class PromotionDeleteHandlerImpl implements PromotionDeleteHandler {

    public PromotionDeleteHandlerImpl(PromotionService promotionService, ApprovalService approvalService) {
        this.promotionService = promotionService;
        this.approvalService = approvalService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String promotionId = request.getParam("promotionId") == null ? StringPool.BLANK : String.valueOf(request.getParam("promotionId"));

        if(!promotionId.isEmpty()){
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPr -> {
                        connectionPR = connPr;
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                connectionBackUp = connBackUp;
                                return  promotionService.get(connPr, promotionId).flatMap(promotion -> {
                                    if(promotion == null){
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    if(promotion.getStatus() == 0) {
                                        return connPr.setAutoCommitObservable(false).flatMap(aVoid -> {
                                            return promotionService.updateStatus(connPr, promotion.getPromotion_id(), PromotionStatus.DELETED);
                                        });
                                    }else{
                                        String newValue = "5";
                                        Approval approval  = new Approval();
                                        approval.setUser(userId);
                                        approval.setSchema("ONEPR");
                                        approval.setName("PROMOTION UPDATE PROMOTION STATUS");

                                        approval.setNew_value(newValue);
                                        approval.setOld_value(String.valueOf(promotion.getStatus()));
                                        approval.setField("N_STATUS");
                                        approval.setDescription("delete promotion");
                                        ApprovalCondition approvalCondition = new ApprovalCondition();
                                        List<ApprovalCondition> approvalConditions = new ArrayList<>();
                                        approvalCondition.setField("PR_ID");
                                        approvalCondition.setValue(String.valueOf(promotion.getN_id()));
                                        approvalCondition.setName(promotion.getPromotion_id());
                                        approvalCondition.setType("string");
                                        approvalConditions.add(approvalCondition);
                                        String conditions = gson.toJson(approvalConditions);
                                        approval.setConditions(conditions);
                                        approval.setTable("TB_RULE");
                                        approval.setType("PR_UPDATE_STATUS");
                                        return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                            return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                                                return connBackUp.commitObservable().map(aVoid1 -> {
                                                   return approval1.getId();
                                                });
                                            });
                                        });
                                    }
                                });
                            });
                }).subscribe(o -> {
                    if(connectionBackUp != null){
                        connectionBackUp.commitObservable();
                    }
                    if(connectionPR != null){
                        connectionPR.commitObservable();
                    }
                    rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
                    rc.next();
                }, throwable -> {
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }
                    if(connectionPR != null){
                        connectionPR.rollbackObservable();
                    }
                    rc.fail(throwable);
                });


        }else{
           rc.fail(404);
        }

    }


    private SQLConnection connectionPR = null;

    private SQLConnection connectionBackUp = null;

    private PromotionService promotionService;

    private ApprovalService approvalService;

    private final static Gson gson = new Gson();
}
