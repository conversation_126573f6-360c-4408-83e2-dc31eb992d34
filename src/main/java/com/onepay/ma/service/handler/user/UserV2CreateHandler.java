package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserV2CreateHandlerImpl;
import com.onepay.ma.service.service.UserServiceV2;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserV2CreateHandler extends Handler<RoutingContext> {
    static UserV2CreateHandlerImpl create(UserServiceV2 userService){
        return new UserV2CreateHandlerImpl(userService);
    }
}
