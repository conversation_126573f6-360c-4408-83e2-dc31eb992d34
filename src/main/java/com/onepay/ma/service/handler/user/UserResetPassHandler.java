package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserResetPassHandlerImpl;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserResetPassHandler extends Handler<RoutingContext> {
    static UserResetPassHandlerImpl create(UserServiceV2 userService, EmailService emailService, MerchantService merchantService){
        return new UserResetPassHandlerImpl(userService, emailService, merchantService);
    }
}
