package com.onepay.ma.service.handler.merchant;

import com.onepay.ma.service.handler.merchant.impl.UserPartnerMerchantGetHandlerImpl;
import com.onepay.ma.service.service.MerchantService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

public interface UserPartnerMerchantGetHandler extends Handler<RoutingContext> {
    static UserPartnerMerchantGetHandlerImpl create (MerchantService merchantService) {
        return new UserPartnerMerchantGetHandlerImpl(merchantService);
    }
}
