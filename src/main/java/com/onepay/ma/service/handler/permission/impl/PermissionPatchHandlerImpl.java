package com.onepay.ma.service.handler.permission.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.permission.PermissionPatchHandler;
import com.onepay.ma.service.models.PermissionPatchPosParameter;
import com.onepay.ma.service.models.PositionPatchPermission;
import com.onepay.ma.service.models.RolePatch;
import com.onepay.ma.service.service.PermissionRoleService;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.List;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/12/16.
 */
public class PermissionPatchHandlerImpl implements PermissionPatchHandler {

    public PermissionPatchHandlerImpl(PermissionService permissionService, PermissionRoleService permissionRoleService) {
        this.permissionService = permissionService;
        this.permissionRoleService = permissionRoleService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String permissionId = rc.request().getParam("permissionId") == null ? StringPool.BLANK : String.valueOf( rc.request().getParam("permissionId"));
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String body = rc.getBodyAsString();
        PatchRequest requestData = gson.fromJson(body, PatchRequest.class);
        if(!permissionId.isEmpty()){
            if(requestData.getPath().equals("/role")) {
                PatchRequest<RolePatch> roleRequest = gson.fromJson(body, new TypeToken<PatchRequest<RolePatch>>(){}.getType());
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                    connectionBackUp = connBackUp;
                    return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return permissionService.get(connectionBackUp, Integer.valueOf(permissionId)).flatMap(permissionData -> {
                            if (permissionData == null) {
                                throw IErrors.RESOURCE_NOT_FOUND;
                            }
                            return permissionRoleService.delete(connectionBackUp, permissionData.getPermission_id()).flatMap(integer -> insertRoleList(connectionBackUp, roleRequest.getValue().getRoles(), permissionData.getPermission_id(), 0));
                        });
                    });
                }).subscribe(strings -> {
                    if(connectionBackUp != null){
                        connectionBackUp.commitObservable();
                    }
                    FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });


            }else{
                throw IErrors.VALIDATION_ERROR;
            }
        }else{
            if(requestData.getPath().equals("/position")) {
                PatchRequest<PermissionPatchPosParameter> patchRequest = gson.fromJson(body, new TypeToken<PatchRequest<PermissionPatchPosParameter>>(){}.getType());
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            connectionBackUp = connBackUp;
                            return updatePermissionParentOrderList(connBackUp, patchRequest.getValue().getPermissions(), 0);
                        }).subscribe(strings -> {
                        if(connectionBackUp != null){
                            connectionBackUp.commitObservable();
                        }
                        FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

            }else{
                throw IErrors.VALIDATION_ERROR;
            }
        }

    }


    /**
     * insert permission role list
     * @param sqlConnection
     * @param listRoleId
     * @param userId
     * @return
     */
    private Observable<List<String>> insertRoleList(SQLConnection sqlConnection, List<String> listRoleId, int userId, int index) {
        if(listRoleId.size() <= 0){
            return Observable.just(listRoleId);
        }
        int roleId = Integer.valueOf(listRoleId.get(index));
        final int finalIndex = index;
        return Observable.just(roleId).flatMap(approvalData -> {
            //insert approval for user group
            return permissionRoleService.insert(sqlConnection, userId, roleId).flatMap(integer -> {
                if(finalIndex >= listRoleId.size() - 1){
                    return Observable.just(listRoleId);
                }else{
                    return insertRoleList(sqlConnection, listRoleId, userId, finalIndex + 1);
                }
            });
        });

    }

    /**
     * update  parent list
     * @param sqlConnection
     * @param listData
     * @return
     */
    private Observable<List<PositionPatchPermission>> updatePermissionParentOrderList(SQLConnection sqlConnection, List<PositionPatchPermission> listData, int index) {
        if(listData.size() <= 0){
            return Observable.just(listData);
        }
        PositionPatchPermission positionPatch =listData.get(index);
        final int finalIndex = index;
        return Observable.just(positionPatch).flatMap(approvalData -> {
            //insert approval for user group
            return permissionService.updateParentOrder(sqlConnection, positionPatch.getParent_id(), positionPatch.getOrder(), positionPatch.getPermission_id()).flatMap(integer -> {
                if(finalIndex >= listData.size() - 1){
                    return Observable.just(listData);
                }else{
                    return updatePermissionParentOrderList(sqlConnection, listData, finalIndex + 1);
                }
            });
        });

    }

    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();

    private PermissionService permissionService;

    private PermissionRoleService permissionRoleService;

    private final static Logger LOGGER = Logger.getLogger(PermissionPatchHandler.class.getName());
}
