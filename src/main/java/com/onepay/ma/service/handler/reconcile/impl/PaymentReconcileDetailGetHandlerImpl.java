package com.onepay.ma.service.handler.reconcile.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationDetailQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationReportConvets;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class PaymentReconcileDetailGetHandlerImpl implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String baseDate = rc.request().getParam(ParamsPool.BASE_DATE);
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 3) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ? 1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        int bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));

        String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));
        String transType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));

        String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        ReconciliationDetailQuery parameter = new ReconciliationDetailQuery();
        parameter.setAcquirer(bankId);
        parameter.setBase_date(baseDate);
        parameter.setInterval(interval);
        parameter.setCard_type(cardType);
        parameter.setTransaction_type(transType);
        parameter.setTo_date(toDate);
        parameter.setFrom_date(fromDate);

        parameter.setCurrency(currency);
        Observable<ReconciliationReportConvets> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            //return merchant
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                Map reportData = new HashMap();

                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                parameter.setMerchant_id(String.join(",", merchantIdList));
                                                return reconcileService.listDetailPayment(connReadOnly, parameter).flatMap(internationalReports -> {
                                                    ReconciliationReportConvets report = new ReconciliationReportConvets();
                                                    report.setItems(internationalReports);
                                                    int totalTransCount = 0;
                                                    int totalPurchaseCount = 0;
                                                    int totalRefundCount = 0;
                                                    int totalVoidCount = 0;
                                                    int totalCaptureCount = 0;
                                                    int totalAuthoriseCount = 0;
                                                    double totalPurchaseAmount = 0;
                                                    double totalRefundAmount = 0;
                                                    double totalVoidAmount = 0;
                                                    double totalCaptureAmount = 0;
                                                    double totalAuthoriseAmount = 0;
                                                    for (Reconciliation reportDatas : internationalReports) {

                                                        totalVoidCount += reportDatas.getCount_void();
                                                        totalRefundCount += reportDatas.getCount_refund();
                                                        totalPurchaseCount += reportDatas.getCount_purchase();
                                                        totalCaptureCount += reportDatas.getCount_capture();
                                                        totalAuthoriseCount += reportDatas.getCount_authorise();
                                                        totalTransCount += reportDatas.getCount_transaction();

                                                        totalPurchaseAmount += reportDatas.getTotal_purchase();
                                                        totalRefundAmount += reportDatas.getTotal_refund();
                                                        totalVoidAmount += reportDatas.getTotal_void();
                                                        totalCaptureAmount += reportDatas.getTotal_capture();
                                                        totalAuthoriseAmount += reportDatas.getTotal_authorise();
                                                    }
                                                    report.setTotal_transaction_count(totalTransCount);
                                                    report.setTotal_authorise_count(totalAuthoriseCount);
                                                    report.setTotal_capture_count(totalCaptureCount);
                                                    report.setTotal_refund_count(totalRefundCount);
                                                    report.setTotal_void_count(totalVoidCount);
                                                    report.setTotal_purchase_count(totalPurchaseCount);

                                                    report.setTotal_purchase_total(totalPurchaseAmount);
                                                    report.setTotal_refund_total(totalRefundAmount);
                                                    report.setTotal_void_total(totalVoidAmount);
                                                    report.setTotal_capture_total(totalCaptureAmount);
                                                    report.setTotal_authorise_total(totalAuthoriseAmount);

                                                    return Observable.just(report);

                                                });
                                            });
                                        });
                            });
                });
        obs.subscribe(reports -> {
            Map returnReport = new HashMap();
            returnReport.put("reports", reports);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }


    @Autowired
    private ReconcileService reconcileService;
    @Autowired
    private MerchantService merchantService;
}
