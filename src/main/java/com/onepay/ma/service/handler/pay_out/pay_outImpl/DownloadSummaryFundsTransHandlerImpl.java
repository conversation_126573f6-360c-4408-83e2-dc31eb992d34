package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.jms.Queue;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.pay_out.SummaryQueryDto;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_out.SummaryFundsTransService;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

@Component
public class DownloadSummaryFundsTransHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private SummaryFundsTransService summaryFundsTransService;
    private static Logger LOGGER = Logger.getLogger(DownloadSummaryFundsTransHandlerImpl.class.getName());

    private final static Gson gson = new Gson();
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;
    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE TOTAL TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JsonObject body = rc.getBodyAsJson();
        JDBCClient clienBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        String fromDate = body.getString(ParamsPool.FROM_DATE);
        String toDate = body.getString(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => PARSE DATE ERROR", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if(months > 6){
            LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => REQUEST TOO LONG");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        String accountId = body.getString(ParamsPool.ACCOUNT_ID) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.ACCOUNT_ID));
        String merchantName = body.getString(ParamsPool.MERCHANT_NAME) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.MERCHANT_NAME));
        String merchantId = body.getString(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.MERCHANT_ID));
        String bankSenderSwiftcode = body.getString(ParamsPool.BANK_SENDER_SWIFTCODE) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.BANK_SENDER_SWIFTCODE));
        String bankReceiptSwiftcode = body.getString(ParamsPool.BANK_RECEIPT_SWIFTCODE) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.BANK_RECEIPT_SWIFTCODE));
        String timeInterval = body.getString(ParamsPool.TIME_INTERVAL) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.TIME_INTERVAL));
        String maMain = body.getString(ParamsPool.MA_MAIN);
        SummaryQueryDto query = new SummaryQueryDto();
        query.setFromDate(fromDate);
        query.setToDate(toDate);
        query.setMerchantId(merchantId);
        query.setMerchantName(merchantName);
        query.setMerchantAccount(accountId);
        query.setBankSender(bankSenderSwiftcode);
        query.setBankReceipt(bankReceiptSwiftcode);
        query.setTimeInterval(timeInterval);
        String timeIntervalConvert="";
        switch(timeInterval) {
            case "DAILY":
                timeIntervalConvert = "Daily Report";
                break;
            case "WEEKLY":
                timeIntervalConvert = "Weekly Report";
                break;
            case "MONTHLY":
                timeIntervalConvert = "Monthly Report";
                break;
            default:
                timeIntervalConvert = "Yearly Report";
        }
        query.setTimeIntervalConvert(timeIntervalConvert);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain download : ");
            String finalMerchantsList = MaPermission.getMc(userId, requestId, partnerId, merchantId);
            query.setMerchantId(finalMerchantsList);
            Map mdata = new HashMap();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
					.flatMap(connBackup -> {
                        this.connectBackUp = connBackup;
						return getObservable(rc, userId, query, requestData, mdata);
					}).subscribe(map -> {
						if (connectBackUp != null) {
							connectBackUp.commitObservable();
						}
						int row = Integer.valueOf(map.get("row").toString());
						if (row <= serverConfig.getRowLevel()) {
							Message<SummaryQueryDto> message = new Message<>(query, requestData,
									MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue,
									downloadFastInQueue);
							QueueProducer.sendMessage(message);
						} else {
							Message<SummaryQueryDto> message = new Message<>(query, requestData,
									MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue,
									downloadSlowInQueue);
							QueueProducer.sendMessage(message);
						}

						rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
						rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
						rc.next();
						LOGGER.log(Level.INFO, "FINAL MC 1 ");
					}, throwable -> {
						if (connectBackUp != null) {
							connectBackUp.rollbackObservable();
						}
						rc.fail(throwable);
					});
        } else  {
            Map mdata = new HashMap();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
                            .flatMap(connBackup -> {
                                this.connectBackUp = connBackup;
                            return merchantService.list(connOnline, connBackup, query.getMerchantId(), userId, "payout").flatMap(merchants -> {
                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList()); 
                                query.setMerchantId(String.join(",", merchantIdList));                           
                                return getObservable(rc, userId, query, requestData, mdata);
                        });
                    });
                }).subscribe(map -> {
            if (connectBackUp != null) {
                connectBackUp.commitObservable();
            }
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<SummaryQueryDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //  fileDownload.setExt("zip");
                Message<SummaryQueryDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if (connectBackUp != null) {
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
        }
    }
    private Observable<Map> getObservable(RoutingContext rc, String userId, SummaryQueryDto query, Map requestData, Map data) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(new Date());
        StringBuilder fileName = new StringBuilder("summary").append(StringPool.UNDERLINE).append("funds_tranfer").append(StringPool.UNDERLINE).append(date);
        String fileHashName = "";
        data.put("parameter", query);
        data.put("file_name", fileName);
        data.put("row", 1000);
        try {
            fileHashName = Convert.hash(fileName.toString() + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("summary_funds_tranfer");
        fileDownload.setExt("xls");
        fileDownload.setFile_name(fileName.toString());
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(query));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });
    }
}
