package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.service.pay_collect.TransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class GetAllBankHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    private TransactionService transactionService;

    private static Logger LOGGER = Logger.getLogger(GetAllBankHandlerImpl.class.getName());
    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                        .flatMap(connOnline -> {
                            return transactionService.getAllBank(connOnline);
                        }).subscribe(merchants -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "UserSearchCollectHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);

    }

}
