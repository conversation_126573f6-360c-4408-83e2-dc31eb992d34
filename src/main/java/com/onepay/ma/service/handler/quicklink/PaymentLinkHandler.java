package com.onepay.ma.service.handler.quicklink;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.jms.Queue;

import org.apache.commons.lang.StringUtils;

import io.vertx.core.json.JsonObject;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.FileUpload;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.QuickLinkClient;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.PaymentLinkDto;
import com.onepay.ma.service.models.quick_link.UploadFileDTO;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.impl.FileServiceImpl;
import com.onepay.ma.service.service.impl.MerchantServiceImpl;
import com.onepay.ma.service.service.quicklink.QuickLinkService;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.util.Utils;
public class PaymentLinkHandler {

    private static final String TOTAL_ITEMS = "totalItems";
    private static final String QUICKLINK = "quicklink";
    private static final String OPERATOR = "operator";
    private static final String PROFILE_ID = "profileId";
    private static boolean isIncludes;

    private static Logger logger = Logger.getLogger(PaymentLinkHandler.class.getName());

    private static MerchantService merchantService;

    private static FileService fileService;

    private static final Gson gson = new Gson();

    private static SQLConnection connectBackUp;

    public static void list(RoutingContext rc) {
        if (merchantService == null)
            merchantService = new MerchantServiceImpl();
        String xUserId = rc.get(ParamsPool.X_USER_ID);

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));
        if (pageSize > Utils.getPageSize()) {
            pageSize = Utils.getPageSize();
        }
        String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
        String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
        String installmentMerchantId = rc.request().getParam(ParamsPool.INSTALLMENT_MERC_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.INSTALLMENT_MERC_ID));
        String paynowMerchantId = rc.request().getParam(ParamsPool.PAYNOW_MERC_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAYNOW_MERC_ID));
        String bnplMerchantId = rc.request().getParam(ParamsPool.BNPL_MERC_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BNPL_MERC_ID));
        String appleMerchantId = rc.request().getParam(ParamsPool.APPLE_MERC_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.APPLE_MERC_ID));
        String email = rc.request().getParam(ParamsPool.EMAIL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.EMAIL));
        String fromDate = rc.request().getParam(ParamsPool.FROMDATE);
        String toDate = rc.request().getParam(ParamsPool.TODATE);

        Map<String, Object> mapBody = new HashMap<>();
        mapBody.put("page", page);
        mapBody.put("pageSize", pageSize);
        mapBody.put("keyword", keywords);
        mapBody.put("state", state);
        mapBody.put("installmentMerchantId", installmentMerchantId);
        mapBody.put("paynowMerchantId", paynowMerchantId);
        mapBody.put("bnplMerchantId", bnplMerchantId);
        mapBody.put("applepayMerchantId", appleMerchantId);
        mapBody.put("email", email);
        mapBody.put("fromDate", fromDate);
        mapBody.put("toDate", toDate);

        logger.info(() -> "merchantId QUICKLINK:" + merchantId);
        if (xUserId == null) {
            logger.log(Level.SEVERE, "[PAYMENT LINK REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                List<Observable<List<Merchant>>> listObservable = new ArrayList<>();
                                logger.info(() -> "===merchantId : " + merchantId + xUserId);
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "international"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "domestic"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "mpay"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "bnpl"));
                                return rx.Observable.zip(listObservable, resultUploads -> {
                                    List<Merchant> allowedMerchant = new ArrayList<>();
                                    for (int i = 0; i < resultUploads.length; i++) {
                                        allowedMerchant.addAll((List<Merchant>) resultUploads[i]);
                                    }
                                    logger.info(() -> "===disable allowedMerchant size : " + allowedMerchant.size());
                                    if (allowedMerchant.size() != 0) {
                                        String merchantIds = allowedMerchant.stream()
                                                .filter(item -> !item.getType().equalsIgnoreCase("bnpl"))
                                                .map(Merchant::getMerchant_id)
                                                .collect(Collectors.joining(","));
                                        String merchantBNPLIds = allowedMerchant.stream()
                                                .filter(item -> item.getType().equalsIgnoreCase("bnpl"))
                                                .map(Merchant::getMerchant_id)
                                                .collect(Collectors.joining(","));
                                        String merchantAppleIds = allowedMerchant.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("international"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));        
                                        if (!merchantIds.isEmpty() || !merchantBNPLIds.isEmpty() || !merchantAppleIds.isEmpty()) {
                                            logger.info(() -> "===disable ql pl merchantIds : " + merchantIds);
                                            mapBody.put("merchantIds", merchantIds);
                                            mapBody.put("merchantBNPLIds", merchantBNPLIds);
                                            mapBody.put("merchantAppleIds", merchantAppleIds);
                                            JsonObject result = QuickLinkClient.getPaymentLink(mapBody);
                                            logger.info(() -> "===disable ql pl get payment link size : " + result.getMap().size());
                                            return result.getMap().size() > 0 ? result.getMap() : new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", "true");
                                        } else {
                                            logger.info(() -> "===disable all merchant ids empty : ");
                                            return new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", "true");
                                        }
                                    } else {
                                        logger.info(() -> "===disable allowedMerchant size == 0 : ");
                                        JsonObject jResult = new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", "true");
                                        return Observable.just(jResult);
                                    }

                                });
                            });
                }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, rc::fail);

    }

    public static void download(RoutingContext rc, Queue downloadFastOutQueue, Queue downloadFastInQueue) {
        if (merchantService == null)
            merchantService = new MerchantServiceImpl();

        if (fileService == null)
            fileService = new FileServiceImpl();

        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        final HttpServerRequest request = rc.request();
        if (xUserId == null) {
            logger.log(Level.SEVERE, "[PAYMENT LINK REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JsonObject dataParam = rc.getBodyAsJson();
        String merchantId = dataParam.getString(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.MERCHANT_ID));
        String page = dataParam.getString(ParamsPool.PAGE) == null ? "0" : String.valueOf(dataParam.getString(ParamsPool.PAGE));
        String pageSize = dataParam.getString(ParamsPool.PAGESIZE) == null ? "50" : String.valueOf(dataParam.getString(ParamsPool.PAGESIZE));
        String keywords = dataParam.getString(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.KEY_WORDS));
        String state = dataParam.getString(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.STATE));
        String installmentMerchantId = dataParam.getString(ParamsPool.INSTALLMENT_MERC_ID) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.INSTALLMENT_MERC_ID));
        String paynowMerchantId = dataParam.getString(ParamsPool.PAYNOW_MERC_ID) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.PAYNOW_MERC_ID));
        String bnplMerchantId = dataParam.getString(ParamsPool.BNPL_MERC_ID) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.BNPL_MERC_ID));
        String appleMerchantId = dataParam.getString(ParamsPool.APPLE_MERC_ID) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.APPLE_MERC_ID));
        String email = dataParam.getString(ParamsPool.EMAIL) == null ? StringPool.BLANK : String.valueOf(dataParam.getString(ParamsPool.EMAIL));
        String fromDate = dataParam.getString(ParamsPool.FROMDATE);
        String toDate = dataParam.getString(ParamsPool.TODATE);
        String lang = dataParam.getString(ParamsPool.LANG);

        Map<String, Object> mapBody = new HashMap<>();
        mapBody.put("page", page);
        mapBody.put("pageSize", pageSize);
        mapBody.put("keyword", keywords);
        mapBody.put("state", state);
        mapBody.put("installmentMerchantId", installmentMerchantId);
        mapBody.put("paynowMerchantId", paynowMerchantId);
        mapBody.put("bnplMerchantId", bnplMerchantId);
        mapBody.put("applepayMerchantId", appleMerchantId);
        mapBody.put("email", email);
        mapBody.put("fromDate", fromDate);
        mapBody.put("toDate", toDate);
        mapBody.put("lang", lang);

        PaymentLinkDto paymentLinkDto = new PaymentLinkDto();
        paymentLinkDto.setPage(Integer.parseInt(page));
        paymentLinkDto.setPageSize(pageSize);
        paymentLinkDto.setKeyword(keywords);
        paymentLinkDto.setState(state);
        paymentLinkDto.setInstallmentMerchantId(installmentMerchantId);
        paymentLinkDto.setPaynowMerchantId(paynowMerchantId);
        paymentLinkDto.setBnplMerchantId(bnplMerchantId);
        paymentLinkDto.setEmail(email);
        paymentLinkDto.setFromDate(fromDate);
        paymentLinkDto.setToDate(toDate);
        paymentLinkDto.setLang(lang);

        logger.info(() -> "merchantId QUICKLINK:" + merchantId);

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        Map<String, Object> requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, xUserId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                connectBackUp = connBackUp;
                                List<Observable<List<Merchant>>> listObservable = new ArrayList<>();
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "international"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "domestic"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "mpay"));
                                listObservable.add(merchantService.list(connOnline, connBackUp, merchantId, xUserId, "bnpl"));
                                return rx.Observable.zip(listObservable, resultUploads -> {
                                    List<Merchant> allowedMerchant = new ArrayList<>();
                                    for (int i = 0; i < resultUploads.length; i++) {
                                        allowedMerchant.addAll((List<Merchant>) resultUploads[i]);
                                    }
                                    String merchantIds = allowedMerchant.stream()
                                            .filter(item -> !item.getType().equalsIgnoreCase("bnpl"))
                                            .map(Merchant::getMerchant_id)
                                            .collect(Collectors.joining(","));
                                    String merchantBNPLIds = allowedMerchant.stream()
                                            .filter(item -> item.getType().equalsIgnoreCase("bnpl"))
                                            .map(Merchant::getMerchant_id)
                                            .collect(Collectors.joining(","));

                                    String merchantAppleIds = allowedMerchant.stream()
                                            .filter(item -> !item.getMerchant_id().equalsIgnoreCase("international"))
                                            .map(Merchant::getMerchant_id)
                                            .collect(Collectors.joining(","));       
                                    Map<String, Object> data = new HashMap<>();
                                    if (merchantIds != null || merchantBNPLIds != null || null != merchantAppleIds) {
                                        paymentLinkDto.setMerchantIds(merchantIds);
                                        mapBody.put("merchantIds", merchantIds);
                                        paymentLinkDto.setMerchantBNPLIds(merchantBNPLIds);
                                        mapBody.put("merchantBNPLIds", merchantBNPLIds);

                                        paymentLinkDto.setMerchantAppleIds(merchantAppleIds);
                                        mapBody.put("merchantAppleIds", merchantAppleIds);
                                        String fileName = "quicklink" + StringPool.UNDERLINE + "paymentlink" + StringPool.UNDERLINE;
                                        long date = new java.util.Date().getTime();
                                        fileName += date;
                                        String fileHashName = "";
                                        data.put("parameter", paymentLinkDto);
                                        data.put("file_name", fileName);
                                        try {
                                            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + xUserId + date);
                                        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                                            rc.fail(e);
                                        }
                                        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                        requestData.put(ParamsPool.FILE_NAME, fileName);
                                        requestData.put(ParamsPool.FILE_EXT, "xlsx");
                                        FileDownload fileDownload = new FileDownload();
                                        fileDownload.setUser(xUserId);
                                        fileDownload.setFile_type("quicklink_paymentlink");
                                        fileDownload.setExt("xlsx");
                                        fileDownload.setFile_name(fileName);
                                        fileDownload.setFile_hash_name(fileHashName);
                                        fileDownload.setConditions(gson.toJson(mapBody));
                                        // return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                            return data;
                                        });
                                        // });
                                    } else {
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                });

                            });
                }).subscribe(returnReport -> {
            returnReport.subscribe(listResult -> {
                PaymentLinkDto parameter = (PaymentLinkDto) listResult.get("parameter");
                Message<PaymentLinkDto> message = new Message<>(parameter, requestData,
                        MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);

                rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, listResult);
                rc.next();
            }, rc::fail);

        }, rc::fail);
    }

    public static void detail(RoutingContext rc) {
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        try {
            JsonObject res = QuickLinkClient.getPaymentLinkDetail(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void changeState(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        JsonObject body = rc.getBodyAsJson();
        body.put(OPERATOR, xUserId);
        try {
            JsonObject res = QuickLinkClient.putPaymentState(linkId, body);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void create(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        JsonObject body = rc.getBodyAsJson();
        validateUser(body);
        body.put(OPERATOR, xUserId);
        try {
            JsonObject res = QuickLinkClient.postPaymentLink(body);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
            rc.next();
        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void upload(RoutingContext rc) {
        try {
            String temp = PropsUtil.get("onepay_quicklink_upload_url", "/opt/ufiles/ma");
            String extAllow = PropsUtil.get("onepay_quicklink_upload_ext", "png,jpg,jpeg,pdf,doc,docx");
            JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

            String xUserId = rc.get(ParamsPool.X_USER_ID);

            Files.createDirectories(Paths.get(temp));
            Set<FileUpload> fileUploads = rc.fileUploads();
            logger.info("fileUploads size:" + fileUploads.size());
            List<UploadFileDTO> uploadArr = new ArrayList<>();

            Random generator = new Random();
            for (FileUpload fileUpload : fileUploads) {
                String tempName = fileUpload.fileName();
                if (!tempName.startsWith("attach_") && !tempName.startsWith("term_")) continue;
                UploadFileDTO upload = new UploadFileDTO();
                Path originalPath = Paths.get(fileUpload.uploadedFileName());
                String[] a = tempName.split("_", 2);
                int b = a[1].indexOf(".");

                String extension = "";
                
                int i = tempName.lastIndexOf('.');
                if (i > 0) {
                    extension = tempName.substring(i + 1);
                }
                if (!extAllow.contains(extension.toLowerCase())) {
                    throw new IllegalArgumentException("Wrong extension : " + extension);
                }
                String fileName = a[1].substring(0, b) + "_" + new Date().getTime() + generator.nextInt(10) + "." + extension;
                Path copied = Paths.get(temp + File.separator + fileName);
                Files.copy(originalPath, copied, StandardCopyOption.REPLACE_EXISTING);
                upload.setName(fileName);
                upload.setType(tempName.split("_")[0]);
                // upload.setOriginalName(tempName.split("_")[1]);
                upload.setOriginalName(a[1]);
                upload.setExt(extension);
                uploadArr.add(upload);
            }

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return connBackUp.setAutoCommitObservable(false)
                                .flatMap(x -> {
                                    List<Observable<UploadFileDTO>> nguyen = new ArrayList<>();
                                    for (UploadFileDTO item : uploadArr) {
                                        nguyen.add(QuickLinkService.insertUploadFile(connBackUp, item, xUserId));
                                    }
                                    return rx.Observable.zip(nguyen, resultUploads -> {
                                        return connBackUp.commitObservable()
                                                .map(z -> {
                                                    return resultUploads;
                                                });
                                    });
                                });
                    }).subscribe(zipObserve -> {
                zipObserve.subscribe(listResult -> {
                    JsonObject res = new JsonObject();
                    res.put("result", Arrays.asList(listResult));
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, res.getMap());
                    rc.next();
                });
            }, throwable -> {
                logger.log(Level.SEVERE, "[ERROR SERVER] ", throwable);
                rc.fail(throwable);
            });
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR SERVER] ", e);
            rc.fail(e);
        }
    }

    public static void update(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        JsonObject body = rc.getBodyAsJson();
        body.put(OPERATOR, xUserId);
        validateUser(body);
        try {
            JsonObject res = QuickLinkClient.putPaymentLink(linkId, body);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
            rc.next();
        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void merchantProfile(RoutingContext rc) {
        try {
            JsonObject res = QuickLinkClient.getMerchantProfile();
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void merchantProfileId(RoutingContext rc) {
        if (merchantService == null)
            merchantService = new MerchantServiceImpl();
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

        if (xUserId == null) {
            logger.log(Level.SEVERE, "[PAYMENT LINK REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {


                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "international").flatMap(international -> {
                                    return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "domestic").flatMap(domestic -> {
                                        return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "mpay").flatMap(mpay -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "bnpl").map(bnpl -> {
                                                List<Merchant> allowedMerchant = new ArrayList<>();
                                                allowedMerchant.addAll(international);
                                                allowedMerchant.addAll(domestic);
                                                allowedMerchant.addAll(mpay);
    
                                                String merchantIds = allowedMerchant.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                String merchantBNPLIds = bnpl.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                String merchantAppleIds = international.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                if (merchantIds != null || merchantBNPLIds != null  || !merchantAppleIds.isEmpty()) {
                                                    JsonObject result = QuickLinkClient.getMerchantProfileById(merchantIds, merchantBNPLIds, merchantAppleIds);
                                                    return result.getMap().size() > 0 ? result.getMap() : new JsonObject().put(TOTAL_ITEMS, 0).put("list", "");
                                                } else {
                                                    return new JsonObject().put(TOTAL_ITEMS, 0).put("list", "");
                                                }
                                            });
                                        });
                                    });
                                });

                            });
                }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, rc::fail);
    }

    public static void merchantIdByProfile(RoutingContext rc) {
        if (merchantService == null)
            merchantService = new MerchantServiceImpl();
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        String payMethod = rc.request().getParam(ParamsPool.PAYMETHOD) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAYMETHOD));

        if (xUserId == null) {
            logger.log(Level.SEVERE, "[PAYMENT LINK REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "international").flatMap(international -> {
                                    return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "domestic").flatMap(domestic -> {
                                        return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "mpay").flatMap(mpay -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "bnpl").map(bnpl -> {
                                                logger.info("bnpl" + bnpl.size());
                                                List<Merchant> allowedMerchant = new ArrayList<>();
                                                allowedMerchant.addAll(international);
                                                allowedMerchant.addAll(domestic);
                                                allowedMerchant.addAll(mpay);
                                                allowedMerchant.addAll(bnpl);
                                                for (Merchant m : allowedMerchant) {
                                                    
                                                logger.info("allowedMerchant" +m.getMerchant_id()+ m.getType());
                                                }
                                                String merchantIds = allowedMerchant.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id).collect(Collectors.joining(","));
                                                logger.info("merchantIds: " + merchantIds);
                                                if (merchantIds != null && !merchantIds.isEmpty()) {
                                                    JsonObject result = QuickLinkClient.getMerchantIdByProfile(merchantIds, payMethod);
                                                    return (result != null && result.getMap().size() > 0) ? result.getMap() : new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", true);
                                                } else {
                                                    return new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", true);
                                                }
                                            });  
                                        });
                                    });
                                });
                            });
                }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, rc::fail);
    }

    private static void validateUser(JsonObject data) {
        if (data.containsKey("description") && StringUtils.isNotBlank(data.getString("description"))) {
            String cleaned = removeSpecialCharacters(data.getString("description"));
            data.put("description", cleaned);
        }

        // if (data.containsKey("description") && StringUtils.isNotBlank(data.getString("description"))
        //         && !Utils.validateText(data.getString("description"))) {
        //     logger.log(Level.SEVERE, "[PAYMENT LINK CREATE ] => DESC FORMAT ERRR");
        //     throw IErrors.VALIDATION_ERROR;
        // }
    }

    public static String removeSpecialCharacters(String input) {
        if (input == null)
            return null;
        // Loại bỏ thẻ HTML
        String noHtml = input.replaceAll("<[^>]*>", "");

        return noHtml;
    }


    // @Autowired
    // @Qualifier(value = "downloadFastQueueIn")
    // private static Queue downloadFastInQueue;

    // @Autowired
    // @Qualifier(value = "downloadFastQueueOut")
    // private static Queue downloadFastOutQueue;

    // @Autowired
    // @Qualifier(value = "downloadSlowQueueIn")
    // private static Queue downloadSlowInQueue;

    // @Autowired
    // @Qualifier(value = "downloadSlowQueueOut")
    // private static Queue downloadSlowOutQueue;
}

