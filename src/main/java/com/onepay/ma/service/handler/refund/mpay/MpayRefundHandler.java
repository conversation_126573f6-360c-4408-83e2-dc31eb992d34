package com.onepay.ma.service.handler.refund.mpay;

import com.onepay.ma.service.handler.refund.mpay.impl.MpayRefundApprovalGetHandler;
import com.onepay.ma.service.handler.refund.mpay.impl.MpayRefundDownloadHandler;
import com.onepay.ma.service.handler.refund.mpay.impl.MpayRefundGetHandler;
import com.onepay.ma.service.handler.refund.mpay.impl.MpayRefundPatchHandler;
import com.onepay.ma.service.handler.refund.mpay.impl.MpayRefundUpdateHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/3/16.
 */
@Component
public class MpayRefundHandler {
    @Autowired
    private MpayRefundDownloadHandler mpayRefundDownloadHandler;

    @Autowired
    private MpayRefundGetHandler mpayRefundGetHandler;

    @Autowired
    private MpayRefundPatchHandler mpayRefundPatchHandler;

    @Autowired
    private MpayRefundUpdateHandler mpayRefundUpdateHandler;

    @Autowired
    private MpayRefundApprovalGetHandler approvalGetHandler;

    public Handler getApproval() {
        return approvalGetHandler;
    }
    public Handler get() {
        return mpayRefundGetHandler;
    }

    public Handler download() {
        return mpayRefundDownloadHandler;
    }

    public Handler patch() {
        return mpayRefundPatchHandler;
    }
    public Handler update() {
        return mpayRefundUpdateHandler;
    }
}
