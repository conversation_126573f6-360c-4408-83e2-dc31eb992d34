package com.onepay.ma.service.handler.transaction.financial.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.models.financial.SamsungFinancialTransaction;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.financial.SSFinancialTransactionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Component
public class SamsungFinancialTransactionGetHandler implements Handler<RoutingContext> {


    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ F TRANSACTION GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String id = rc.request().getParam("id");
        // String merchant_id = rc.request().getParam("merchant_id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (id == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));
                oToDate = DateUtils.addMinutes(oToDate, 1);

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                fromDate = sdf.format(oFromDate);
                toDate = sdf.format(oToDate);

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.VALIDATION_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
            //String responseCode = rc.request().getParam(ParamsPool.RESPONSE_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.RESPONSE_CODE));
            String orderId = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));
            String transactionState = rc.request().getParam(ParamsPool.TRANSACTION_STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_STATE));
            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));
            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
            String orderRef = rc.request().getParam(ParamsPool.ORDER_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_REF));
            String currency = rc.request().getParam(ParamsPool.CURRENCY) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY));
            String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));
            String acquirerId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));
            String tranId = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));
            String merchant_trans_ref = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            String customerMobile = rc.request().getParam(ParamsPool.CUSTOMER_MOBILE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CUSTOMER_MOBILE));
            String customerEmail = rc.request().getParam(ParamsPool.CUSTOMER_EMAIL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CUSTOMER_EMAIL));
            String merchantWebsite = rc.request().getParam(ParamsPool.MERCHANT_WEBSITE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_WEBSITE));
            String fraudCheck = rc.request().getParam("fraud_check") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("fraud_check"));
            String authorisationCode = rc.request().getParam(ParamsPool.AUTHORISATION_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.AUTHORISATION_CODE));
            String orderStatus = rc.request().getParam(ParamsPool.ORDER_STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_STATUS));
            String riskAssessment = rc.request().getParam("risk_assessment") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("risk_assessment"));
            String hiddenColumn = rc.request().getParam("hidden_column") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("hidden_column"));
            FinancialTransactionQuery query = new FinancialTransactionQuery();
            query.setKeywords(keywords);
            query.setPage(page);
            query.setPageSize(pageSize);
            query.setMerchant_id(merchantId);
            // query.setResponse_code(responseCode);
            query.setOrder_info(orderId);
            query.setStatus(transactionState);
            query.setCard_number(cardNumber);
            query.setMerchant_transaction_ref(merchant_trans_ref);
            query.setCard_type(cardType);
            query.setCurrency(currency);
            query.setTransaction_type(transactionType);
            query.setAcquirer_id(acquirerId);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setTransaction_id(tranId);
            query.setAuthorisation_code(authorisationCode);
            query.setRisk_assessment(riskAssessment);
            query.setHidden_column(hiddenColumn);

            // SAMSUNG
            query.setCustomer_email(customerEmail);
            query.setCustomer_mobile(customerMobile);
            query.setMerchant_website(merchantWebsite);
            query.setFraud_check(fraudCheck);
            query.setOrder_status(orderStatus);


            // IN CASE COMPARE CARD NO HASH
            if (FunctionUtil.isCardData(cardNumber)) {
                rc.vertx().executeBlockingObservable(objectFuture -> {
                    objectFuture.complete(FunctionUtil.oneSMHmac(cardNumber, serverConfig));
                }).subscribe(o -> {
                    query.setCard_number(String.valueOf(o));
                    queryTransaction(query, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                });
            } else {
                queryTransaction(query, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            }


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return SSFinancialTransactionService.get(connOnline, id);
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }


    private void queryTransaction(FinancialTransactionQuery query, String merchantId, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable<Transactions<SamsungFinancialTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                query.setMerchant_id(String.join(",", merchantIdList));
                                                return SSFinancialTransactionService.search(connReadOnly, connOnline, query);
                                            });

                                        });

                            });

                });
        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private static Logger LOGGER = Logger.getLogger(SamsungFinancialTransactionGetHandler.class.getName());


    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ServerConfig serverConfig;
}
