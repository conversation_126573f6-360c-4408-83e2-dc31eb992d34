package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OperatorCheckExistHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(OperatorCheckExistHandlerImpl.class.getName());

    @Autowired
    private OperatorService operatorService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
        if (body == null) {
            LOGGER.log(Level.SEVERE, "[ OPERATOR CHECK EXIST ] => BODY POST IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String partnerID = body.getString("partnerID") != null ? String.valueOf(body.getString("partnerID")).trim() : StringPool.BLANK;
        if (partnerID == null || partnerID.isEmpty()) {
            LOGGER.log(Level.SEVERE, "[ OPERATOR CHECK EXIST ] => PARTNER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }

        String userName = body.getString("user_name") != null ? String.valueOf(body.getString("user_name")).trim() : StringPool.BLANK;
        if (userName == null || userName.isEmpty()) {
            LOGGER.log(Level.SEVERE, "[ OPERATOR CHECK EXIST ] => EMAIL IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String password = body.getString("password") != null ? String.valueOf(body.getString("password")).trim() : StringPool.BLANK;
        if (password == null || password.isEmpty()) {
            LOGGER.log(Level.SEVERE, "[ OPERATOR CHECK EXIST ] => PASSWORD IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return operatorService.getOperatorExisted(connOnline, partnerID, userName).flatMap(operatorExist -> {
                        if (null != operatorExist && !"active".equalsIgnoreCase(operatorExist.getState())) {
                            throw IErrors.OPERATOR_NOT_ACTIVE;
                        }
                        return operatorService.getByUserAndPass(connOnline, partnerID, userName, password);
                    });
                }).subscribe(partners -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

}
