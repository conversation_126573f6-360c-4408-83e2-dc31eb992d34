package com.onepay.ma.service.handler.transaction.international.impl;

import com.onepay.ma.service.handler.transaction.international.InternationalCaptureHistoryGetHandler;
import com.onepay.ma.service.handler.transaction.international.InternationalTransactionHistoryGetHandler;
import com.onepay.ma.service.models.InternationalTransactionHistory;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/30/16.
 */
public class InternationalCaptureHistoryGetHandlerImpl implements InternationalCaptureHistoryGetHandler {

    public InternationalCaptureHistoryGetHandlerImpl(InternationalTransactionService internationalTransactionService, MerchantService merchantService, UserService userService) {
        this.internationalTransactionService = internationalTransactionService;
        this.merchantService = merchantService;
        this.userService = userService;
    }



    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(transactionId != null) {
            Observable<List<InternationalTransactionHistory>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose()).flatMap(connReadOnly -> {
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f-> f.dispose()).flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f-> f.dispose())
                            .flatMap(connBackUp -> {
                                return internationalTransactionService.listHistory(connOnline, connBackUp, transactionId).flatMap(transaction -> {
                                    return getListUser(connBackUp, transaction, 0);
                                });
                            });
                    });
            });

            obs.subscribe(internationalTransactionHistories -> {
                Map returnValue = new HashMap();
                returnValue.put("transactions", internationalTransactionHistories);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }else{
            throw IErrors.VALIDATION_ERROR;
        }
    }

    /**
     * get list user for list approval
     * @param sqlConnection
     * @param transactionHistories
     * @param index
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListUser(SQLConnection sqlConnection, List<InternationalTransactionHistory> transactionHistories, int index){
        if(transactionHistories.size() <= 0){
            return Observable.just(transactionHistories);
        }
        InternationalTransactionHistory internationalTransactionHistory = transactionHistories.get(index);
        final int finalIndex = index;
        return Observable.just(internationalTransactionHistory).flatMap(transactionHistory -> {
            if(transactionHistory.getOperator_id() == null){
                if (finalIndex >= transactionHistories.size() - 1) {
                    return Observable.just(transactionHistories);
                } else {
                    return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                }
            }else {
                //get user data
                return userService.get(sqlConnection, transactionHistory.getOperator_id()).flatMap(userData -> {
                    if(userData != null) {
                        internationalTransactionHistory.setOperator_id(userData.getEmail());
                    }
                    if (finalIndex >= transactionHistories.size() - 1) {
                        return Observable.just(transactionHistories);
                    } else {
                        return getListUser(sqlConnection, transactionHistories, finalIndex + 1);
                    }
                });
            }
        });

    }

    private InternationalTransactionService internationalTransactionService;

    private UserService userService;

    private MerchantService merchantService;
}
