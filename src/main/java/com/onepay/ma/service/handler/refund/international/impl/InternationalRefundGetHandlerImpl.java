package com.onepay.ma.service.handler.refund.international.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.google.common.base.Strings;
import com.onepay.ma.service.models.InternationalRefund;
import com.onepay.ma.service.models.InternationalRefundParameter;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.MidService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huynguyen on 4/2/16.
 */
@Component
public class InternationalRefundGetHandlerImpl implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND GET ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String transactionId = rc.request().getParam("transactionId");
        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND GET ] => DATE PARSE ERROR", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND GET ] => USER ID EMPTY");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            int transId = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null || rc.request().getParam(ParamsPool.TRANSACTION_ID).isEmpty() ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            if (pageSize > Utils.getPageSize()) {
                pageSize = Utils.getPageSize();
            }
            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            String transactionType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));
            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));
            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
            Integer acquirerId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));
            Integer refundType = (Strings.isNullOrEmpty(rc.request().getParam(ParamsPool.REFUND_TYPE))) ? null : Integer.valueOf(rc.request().getParam(ParamsPool.REFUND_TYPE));// DuongPXT authorize captrue: add refund type
            

            String finalKeyWords = FunctionUtil.isBeginIsACard(keywords) && keywords.contains("*") ? keywords.replaceAll("\\*", "0") : keywords;
            String finalCardNumber = FunctionUtil.isBeginIsACard(cardNumber) && cardNumber.contains("*") ? cardNumber.replaceAll("\\*", "0") : cardNumber;
            InternationalRefundParameter internationalRefundParameter = new InternationalRefundParameter();
            internationalRefundParameter.setCurrency(currency);
            internationalRefundParameter.setFromDate(fromDate);
            internationalRefundParameter.setToDate(toDate);
            internationalRefundParameter.setPage(page);
            internationalRefundParameter.setPageSize(pageSize);
            internationalRefundParameter.setTransactionId(transId);
            internationalRefundParameter.setStatus(status);
            internationalRefundParameter.setTransactionType(transactionType);
            internationalRefundParameter.setOrderInfo(orderInfo);
            internationalRefundParameter.setTransactionReference(merchantTransactionRef);
            internationalRefundParameter.setCardNumber(cardNumber);
            internationalRefundParameter.setCardType(cardType);
            internationalRefundParameter.setAcquirerId(acquirerId);
            internationalRefundParameter.setRefundType(refundType);// DuongPXT authorize captrue: add refund type


            internationalRefundParameter.setKeywords(keywords);

            if (FunctionUtil.isCardData(finalKeyWords)) {
                rc.vertx().executeBlockingObservable(objectFuture -> {
                    objectFuture.complete(FunctionUtil.oneSMHmac(finalKeyWords, serverConfig));
                }).subscribe(o -> {
                    internationalRefundParameter.setKeywords(String.valueOf(o));
                    queryTransaction(internationalRefundParameter, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                });

            } else {
                if (FunctionUtil.isCardData(finalCardNumber)) {
                    rc.vertx().executeBlockingObservable(objectFuture -> {
                        objectFuture.complete(FunctionUtil.oneSMHmac(finalCardNumber, serverConfig));
                    }).subscribe(o -> {
                        internationalRefundParameter.setCardNumber(String.valueOf(o));
                        queryTransaction(internationalRefundParameter, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                    });
                } else {
                    queryTransaction(internationalRefundParameter, merchantId, userId, clientReadOnly, clientOnline, clientBackUp, rc);
                }
            }


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                        return internationalRefundService.get(connOnline, connBackup, transactionId).flatMap(internationalRefund -> {
                                        return midService.getByMerchantId(connBackup, internationalRefund.getMerchant_id()).map(s -> {
                                            internationalRefund.setMid_no(s);
                                            return internationalRefund;
                                        });
                                    });
                                });
                    }).subscribe(domesticRefund -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }


    }

    private void queryTransaction(InternationalRefundParameter internationalRefundParameter, String merchantId, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable<Transactions<InternationalRefund>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                internationalRefundParameter.setMerchantId(String.join(",", merchantIdList));
                                                return internationalRefundService.list(connReadOnly, connOnline, connBackUp, internationalRefundParameter);
                                            });
                                        });

                            });
                });

        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    @Autowired
    private MidService midService;

    @Autowired
    private InternationalRefundService internationalRefundService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ServerConfig serverConfig;

    private final static Logger LOGGER = Logger.getLogger(InternationalRefundGetHandlerImpl.class.getName());
}
