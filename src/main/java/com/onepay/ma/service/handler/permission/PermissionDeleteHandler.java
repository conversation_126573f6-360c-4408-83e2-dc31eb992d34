package com.onepay.ma.service.handler.permission;

import com.onepay.ma.service.handler.permission.impl.PermissionDeleteHandlerImpl;
import com.onepay.ma.service.service.PermissionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface PermissionDeleteHandler extends Handler<RoutingContext> {
    static PermissionDeleteHandlerImpl create(PermissionService permissionService){
        return new PermissionDeleteHandlerImpl(permissionService);
    }
}
