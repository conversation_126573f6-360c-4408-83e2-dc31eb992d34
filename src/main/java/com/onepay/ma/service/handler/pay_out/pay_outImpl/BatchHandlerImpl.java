package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.pay_out.BatchService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.netty.handler.codec.http.HttpResponseStatus;
import rx.Observable;

import javax.jms.Queue;
import java.io.FileWriter;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class BatchHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    @Qualifier(value = "serviceQueueIn")
    private Queue serviceQueueIn;

    @Autowired
    @Qualifier(value = "serviceQueueOut")
    private Queue serviceQueueOut;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private BatchService batchService;

    @Autowired
    private UserService userService;

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient client = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                String xUserId = rc.get(ParamsPool.X_USER_ID);
                if (xUserId == null) {
                    throw IErrors.FORBIDDEN;
                }
                String ip = rc.get(ParamsPool.X_REAL_IP);
                String requestId = rc.get(ParamsPool.X_REQUEST_ID);

                final HttpServerRequest request = rc.request();
                LOGGER.log(Level.INFO, "LISTEN TO FUNDS TRANSFER IMPORT");
                Map input = new HashMap();
//                Utils.execBlocking(rc, () -> {
                    rc.fileUploads().stream().map(fileUpload -> {
                        input.put("file_name", fileUpload.fileName());
                        input.put("file_type", fileUpload.contentType());
                        try {
                            Reader reader = Files.newBufferedReader(Paths.get(fileUpload.uploadedFileName()));
                            CSVReader csvReader = new CSVReader(reader);
                            List<String[]> allRows = csvReader.readAll();
                            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh mm");
                            String strDate = dateFormat.format(new Date());
                            String fileName = strDate + "_" + fileUpload.fileName();
                            StringBuilder patch = new StringBuilder(serverConfig.getExportLocation()).append("/").append(fileName);
                            CSVWriter writer = new CSVWriter(new FileWriter(patch.toString()));
                            writer.writeAll(allRows);
                            String header = Arrays.toString(allRows.get(0));
                            writer.close();

                            Map<String, String> param = new HashMap();
                            param.put("ACCOUNT_ID", xUserId);
                            param.put("STATE", BatchState.CREATE.getResponse());
                            param.put("DESC", "");
                            param.put("NAME", fileName);
                            param.put("FILE_NAME", fileUpload.fileName());
                            param.put("PATH", patch.toString());
                            param.put("HEADER", header);
                            input.put("patch", patch.toString());
                            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                                    .flatMap(sqlConnection -> {
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {
                                            return userService.getOneAm(connBackUp, xUserId).flatMap(user -> {
                                                if (user == null) {
                                                    throw IErrors.INVALID_USER_EMAIL_EXISTED;
                                                }
                                                param.put("ACCOUNT_LOGIN", user.getEmail());
                                                return sqlConnection.setAutoCommitObservable(true).flatMap(aVoid -> {
                                                    return batchService.insert(sqlConnection, param);
                                                });
                                            });
                                        });
                                    }).subscribe(batch -> {
                                if (batch == null) {
                                    if (connectionBackUp != null) {
                                        connectionBackUp.rollbackObservable();
                                    }
                                    throw IErrors.VALIDATION_ERROR;
                                }
                                if (connectionBackUp != null) {
                                    connectionBackUp.commitObservable();
                                }
                                input.put("batchId", batch.getId());

                                Map requestData = new HashMap<>();
                                requestData.put(ParamsPool.X_USER_ID, xUserId);
                                requestData.put(ParamsPool.X_REQUEST_ID, requestId);
                                requestData.put(ParamsPool.X_REAL_IP, ip);

                                Message<Map> message = new Message<>(input, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), serviceQueueOut, serviceQueueIn);
                                QueueProducer.sendMessage(message);
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, batch);
                                rc.next();
                            }, throwable -> {
                                if (connectionBackUp != null) {
                                    connectionBackUp.rollbackObservable();
                                }
                                rc.fail(throwable);
                            });

                        } catch (Exception e) {
                            LOGGER.log(Level.SEVERE, "ERROR TO BATCH FUNDS TRANSFER: ", e);
                            throw IErrors.INTERNAL_SERVER_ERROR;
                        }
                        return null;
                    }).collect(Collectors.toList());
//                });

                rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
                rc.next();
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Import funds transfer  batch Error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    private static Logger LOGGER = Logger.getLogger(BatchHandlerImpl.class.getName());

    private SQLConnection connectionBackUp;
}
