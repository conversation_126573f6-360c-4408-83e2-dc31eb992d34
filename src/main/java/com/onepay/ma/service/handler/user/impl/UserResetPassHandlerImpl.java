package com.onepay.ma.service.handler.user.impl;


import com.google.gson.Gson;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.handler.user.UserResetPassHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by tuydv on 12/04/18.
 */
public class UserResetPassHandlerImpl implements UserResetPassHandler {
    private static Logger LOGGER = Logger.getLogger(UserResetPassHandlerImpl.class.getName());
    private final static Gson gson = new Gson();
    /**
     * different dictionaries used
     */
    private static final String ALPHA_CAPS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String ALPHA = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMERIC = "**********";
    private static final String SPECIAL_CHARS = "@$!%?&";
    private static final String ALL_CHARACTERS = ALPHA + ALPHA_CAPS + NUMERIC + SPECIAL_CHARS;
    private static SecureRandom random = new SecureRandom();

    public UserResetPassHandlerImpl(UserServiceV2 userService, EmailService emailService, MerchantService merchantService) {
        this.userService = userService;
        this.emailService = emailService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
		HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        String platform = Utils.detectPlatform(request.getHeader("User-Agent"));
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER RESET PASS ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
        if (body == null) {
            LOGGER.log(Level.SEVERE, "[ USER RESET PASS ] => BODY POST IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String s_id = body.getString(ParamsPool.S_ID) != null ? String.valueOf(body.getString(ParamsPool.S_ID)).trim() : StringPool.BLANK;
        if (s_id == null || s_id.equals("")) {
            LOGGER.log(Level.SEVERE, "[ USER RESET PASS ] => S_ID POST IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        AtomicReference<String> newPassTmp = new AtomicReference<>();
        Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                .flatMap(sqlConn -> {
                    return merchantService.listAll(sqlConn, userId)
                            .flatMap(merchants -> {
                                if (!merchants.isEmpty()) {
                                    String merchantIds = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.joining(","));
                                    return userService.listUserByMerchantIds(sqlConn, userId, merchantIds);
                                } else {
                                    return userService.listByCreateId(sqlConn, userId);
                                }
                            });
                }).flatMap(listUser -> {
                    List<String> sIdList = listUser.stream()
                            .map(UserData::getS_id)
                            .collect(Collectors.toList());
                    LOGGER.log(Level.INFO, "[ USER RESET PASS ] listUser " + sIdList);
                    boolean exists = listUser.stream().anyMatch(m -> m.getS_id().equalsIgnoreCase(s_id));
                    if (!exists) {
                        return Observable.error(IErrors.VALIDATION_ERROR);
                    }
                    LOGGER.severe("New password : " + newPassTmp);

                    return Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                            .flatMap(sqlConnection -> {
                                connectionBackUp = sqlConnection;
                                return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                    return userService.listRecentPasswords(sqlConnection, s_id).flatMap(recentPasswords -> {
                                    // Lấy lịch sử 2 mật khẩu gần đây của user và generate pass không thuộc 2 mật khẩu gần đây
                                    do {
                                        newPassTmp.set(generatePassword());
                                    } while (recentPasswords.contains(newPassTmp));
                                    LOGGER.severe("New password : " + newPassTmp);
                                    String newPass = userService.encodePassword(newPassTmp.get(), 2);

                                    return userService.getUserDataBySId(sqlConnection, userId).flatMap(userAdmin -> {
                                        return userService.resetPass(sqlConnection, s_id, userService.encodePassword(newPass, 2), platform)
                                                .flatMap(userData -> {
                                                    Map map = new HashMap();
                                                    map.put("admin", userAdmin);
                                                    map.put("user", userData);
                                                    return Observable.just(map);
                                                });
                                        });
                                     });
                                });
                            });
                }).subscribe(map -> {
                    UserData userData = (UserData) map.get("user");
                    UserData userAdmin = (UserData) map.get("admin");
                    if (userData == null) {
                        if (connectionBackUp != null) {
                            connectionBackUp.rollbackObservable();
                        }
                        throw IErrors.VALIDATION_ERROR;
                    } else {
                        emailService.sentMail(userData.getEmail(), userAdmin.getEmail(), userData.getName(), newPassTmp.get());
                        connectionBackUp.commitObservable();
                    }

                    rc.put(ParamsPool.HANDLER_DATA_RESULT, userData);
                    rc.next();
                }, throwable -> {
                    if (connectionBackUp != null) {
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
    }

    public static String generatePassword() {
        List<Character> chars = new ArrayList<>();
        chars.add(ALPHA.charAt(random.nextInt(ALPHA.length())));
        chars.add(ALPHA_CAPS.charAt(random.nextInt(ALPHA_CAPS.length())));
        chars.add(NUMERIC.charAt(random.nextInt(NUMERIC.length())));
        chars.add(SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length())));

        for (int i = 4; i < 12; i++) {
            chars.add(ALL_CHARACTERS.charAt(random.nextInt(ALL_CHARACTERS.length())));
        }

        // Trộn ngẫu nhiên
        Collections.shuffle(chars);

        StringBuilder password = new StringBuilder();
        for (char c : chars) {
            password.append(c);
        }

        return password.toString();
    }

    private MerchantService merchantService;
    private UserServiceV2 userService;
    private EmailService emailService;
    private SQLConnection connectionBackUp;

}
