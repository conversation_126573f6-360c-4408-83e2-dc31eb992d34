package com.onepay.ma.service.handler.startisticChart.international;

import com.google.gson.Gson;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class StatisticChartInterDowloadHandlerImpl implements Handler<RoutingContext> {
    private final static Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(StatisticChartInterDowloadHandlerImpl.class.getName());
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    @Qualifier("downloadMultiFileIn")
    private Queue downloadMultiInQueue;

    @Autowired
    @Qualifier("downloadMultiFileOut")
    private Queue downloadMultiOutQueue;

    @Autowired
    private StatisticChartService statisticChartService;

    @Autowired
    private UserService userService;
    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE MPAY TRANSACTION POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clienBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        JsonObject data =rc.getBodyAsJson();
        JsonArray merchantId = data.getJsonArray("merchantId");
        String sMerchantId = String.join(",",merchantId.getList());
        String target = String.join(",",data.getJsonArray("target").getList());
        JsonArray cardType = data.getJsonArray("cardType");
        String sCardType= String.join(",", cardType.getList()).replace("|",",");
        List<ChartStatisticQuery> querys = new ArrayList<>();

        JsonArray mDatetf = data.getJsonArray("datetf");
        if (mDatetf != null) {
            for (int i=0;i<mDatetf.size();i++){
                ChartStatisticQuery query = new ChartStatisticQuery();
                JsonObject date = mDatetf.getJsonObject(i);
                String datef = date.getString(ParamsPool.FROMDATE);
                String datet = date.getString(ParamsPool.TODATE);
                String dateName = date.getString(ParamsPool.DATE_NAME);
                query.setFromDate(datef);
                query.setToDate(datet);
                query.setMerchantId(sMerchantId);
                query.setTarget(target);
                query.setCardType(sCardType);
                query.setFileName(dateName);
                querys.add(query);
            }
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        Integer countData = querys.size();
        Map mdata = new HashMap();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clienBackUp), f -> f.dispose())
            .flatMap(connBackup -> {
                this.connectBackUp = connBackup;
                                return getObservable(rc, userId,querys.get(0),requestData,mdata, countData);
    }).subscribe(map -> {
        if (connectBackUp != null) {
            connectBackUp.commitObservable();
        }
            if (countData > 1) {
                Message<List<ChartStatisticQuery>> message = new Message<>(querys, requestData, 2, request.path(), downloadMultiOutQueue, downloadMultiInQueue);
                QueueProducer.sendMessage(message);
            } else {
                ChartStatisticQuery query = querys.get(0);
                Message<ChartStatisticQuery> message = new Message<>(query, requestData, 1, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            }
            
        rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
        rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
        rc.next();
    }, throwable -> {
        if (connectBackUp != null) {
            connectBackUp.rollbackObservable();
        }
        rc.fail(throwable);
    });
}
    private Observable<Map> getObservable(RoutingContext rc, String userId, ChartStatisticQuery query, Map requestData, Map data, Integer countData) {
            String fileName = "international" + StringPool.UNDERLINE + "chart" + StringPool.UNDERLINE;
            long date = new java.util.Date().getTime();
            fileName += date;
            String fileHashName = "";
            data.put("parameter", query);
            data.put("file_name", fileName);
            data.put("row", 1000);
            try {
                fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
            } catch (NoSuchAlgorithmException e) {
                rc.fail(e);
            } catch (UnsupportedEncodingException e) {
                rc.fail(e);
            }
            requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
            requestData.put(ParamsPool.FILE_NAME, fileName);
            FileDownload fileDownload = new FileDownload();
            fileDownload.setUser(userId);
            fileDownload.setFile_type("international_transaction_chart");
            if ( countData > 1) {
                fileDownload.setExt("zip");
            } else {
                fileDownload.setExt("xls");
            }
            fileDownload.setFile_name(fileName);
            fileDownload.setFile_hash_name(fileHashName);
            fileDownload.setConditions(gson.toJson(query));
            return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                    return data;
                });
            });
        }

}
