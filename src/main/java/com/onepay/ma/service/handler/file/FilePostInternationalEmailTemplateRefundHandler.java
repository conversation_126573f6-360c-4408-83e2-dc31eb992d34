package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostInternationalEmailTemplateRefundImpl;
import com.onepay.ma.service.handler.file.impl.FilePostInternationalRefundHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 10/14/2020
 * Time: 9:42 AM
 * To change this ma-web.
 */

public interface FilePostInternationalEmailTemplateRefundHandler extends Handler<RoutingContext> {
    static FilePostInternationalEmailTemplateRefundImpl create(MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue){
        return new FilePostInternationalEmailTemplateRefundImpl(merchantService, serverConfig, downloadFastIn, downloadFastOut, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue);

    }
}
