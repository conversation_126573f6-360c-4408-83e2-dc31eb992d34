package com.onepay.ma.service.handler.pay_collect;

import com.onepay.ma.service.handler.pay_collect.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PayCollectHandler {
    @Autowired
    private UserSearchCollectHandlerImpl userSearchCollectHandler;

    @Autowired
    private UserDownloadCollectHandlerImpl userDownloadCollectHandler;

    @Autowired
    private UpsertUserCollectHandlerImpl upsertUserCollectHandler;

    @Autowired
    private GetAllUserHandlerImpl getAllUserHandler;

    @Autowired
    private GetUserCollectHandlerImpl getUserCollectHandler;

    @Autowired
    private GetAllMerchantHandlerImpl getAllMerchantHandler;

    //USER CONFIG
    public UserSearchCollectHandlerImpl searchUser() {
        return userSearchCollectHandler;
    }

    public UserDownloadCollectHandlerImpl downloadUser() {
        return userDownloadCollectHandler;
    }

    public UpsertUserCollectHandlerImpl upsertUser() {
        return upsertUserCollectHandler;
    }

    public GetUserCollectHandlerImpl getUser() {
        return getUserCollectHandler;
    }

    public GetAllUserHandlerImpl all() {
        return getAllUserHandler;
    }

    public GetAllMerchantHandlerImpl getAllMerchant() {
        return getAllMerchantHandler;
    }
    //USER CONFIG

    //TRANSACTION
    @Autowired
    private TransactionHandlerImpl transactionHandler;
    @Autowired
    private TransactionGetHandlerImpl transactionGetHandler;
    @Autowired
    private FilePostTransactionHandlerImpl filePostTransactionHandler;
    @Autowired
    private GetAllBankHandlerImpl getAllBankHandler;
    public Handler getAllBank() {return getAllBankHandler;}
    public Handler searchTransaction() {return transactionHandler;}
    public Handler getTransaction() {
        return transactionGetHandler;
    }
    public Handler downloadTransaction() {
        return this.filePostTransactionHandler;
    }
    //TRANSACTION

    //REPORT
    @Autowired
    private ReportSearchHandlerImpl reportSearchHandler;
    @Autowired
    private FilePostReportHandlerImpl filePostReportHandler;
    public Handler searchReport() {
        return reportSearchHandler;
    }

    public Handler downloadReport() {
        return this.filePostReportHandler;
    }
    //REPORT
}
