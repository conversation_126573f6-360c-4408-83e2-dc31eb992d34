package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserChangePassHandlerImpl;
import com.onepay.ma.service.service.UserServiceV2;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserChangePassHandler extends Handler<RoutingContext> {
    static UserChangePassHandlerImpl create(UserServiceV2 userService){
        return new UserChangePassHandlerImpl(userService);
    }
}
