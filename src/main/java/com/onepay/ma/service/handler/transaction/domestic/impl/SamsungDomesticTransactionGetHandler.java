package com.onepay.ma.service.handler.transaction.domestic.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.service.domestic.SSDomesticTransactionService;
import com.onepay.ma.service.service.orderApproval.OrderApprovalService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/30/16.
 */
@Component
public class SamsungDomesticTransactionGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        String target = rc.request().getParam(ParamsPool.TARGET) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TARGET));
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => PARSE DATE ERROR", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ DOMESTIC TRANSACTION GET] => REQUEST TOO LONG");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

            String bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID)).replace("|", ",");

            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            int transactionIdValue = (rc.request().getParam(ParamsPool.TRANSACTION_ID) == null || rc.request().getParam(ParamsPool.TRANSACTION_ID).isEmpty()) ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? null : String.valueOf(rc.request().getParam(ParamsPool.STATUS));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));

            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            String customerMobile = rc.request().getParam(ParamsPool.CUSTOMER_MOBILE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CUSTOMER_MOBILE));
            String customerEmail = rc.request().getParam(ParamsPool.CUSTOMER_EMAIL) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CUSTOMER_EMAIL));
            String merchantWebsite = rc.request().getParam(ParamsPool.MERCHANT_WEBSITE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_WEBSITE));
            String fraudCheck = rc.request().getParam("fraud_check") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("fraud_check"));
            String orderStatus = rc.request().getParam(ParamsPool.ORDER_STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_STATUS));

            String finalCardNumber = FunctionUtil.isBeginIsACard(cardNumber) && cardNumber.contains("*") ? cardNumber.replaceAll("\\*", "0") : cardNumber;

            DomesticTxnParameter domesticTxnParameter = new DomesticTxnParameter();
            domesticTxnParameter.setPage(page);
            domesticTxnParameter.setKeywords(keywords);
            domesticTxnParameter.setPageSize(pageSize);
            domesticTxnParameter.setFromDate(fromDate);
            domesticTxnParameter.setToDate(toDate);
            domesticTxnParameter.setCardNumber(finalCardNumber);
            domesticTxnParameter.setMerchantTransactionRef(merchantTransactionRef);
            domesticTxnParameter.setOrderInfo(orderInfo);
            domesticTxnParameter.setTransactionId(transactionIdValue);
            domesticTxnParameter.setBankId(bankId);
            domesticTxnParameter.setStatus(status);
            domesticTxnParameter.setCustomer_email(customerEmail);
            domesticTxnParameter.setCustomer_mobile(customerMobile);
            domesticTxnParameter.setMerchant_website(merchantWebsite);
            domesticTxnParameter.setFraud_check(fraudCheck);
            domesticTxnParameter.setTarget(target);
            domesticTxnParameter.setOrder_status(orderStatus);

            // IN CASE COMPARE WITH HASHED FULL CARD NUMBER
            if (FunctionUtil.isDomesticCardData(finalCardNumber)) {
                String encryptedCardNumber = FunctionUtil.oneSMHmac(finalCardNumber, serverConfig);
                domesticTxnParameter.setCardNumber(String.valueOf(encryptedCardNumber));
                LOGGER.info("HASH CARD NUMBER: " + encryptedCardNumber);
            }

            Observable<Transactions<SamsungDomesticTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        // get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    // get back up connection
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return merchantService.list(connOnline, connBackUp, merchantId, userId, "domestic").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    domesticTxnParameter.setMerchantId(String.join(",", merchantIdList));
                                                    return SSDomesticTransactionService.list(connReadOnly, connOnline, domesticTxnParameter);
                                                });
                                            });
                                });
                    });
            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                .flatMap(connReadOnly -> {

                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return SSDomesticTransactionService.get(connOnline, transactionId, target).flatMap(samsungDomesticTransaction -> {
                                                    return merchantService.list(connOnline, connBackUp, "", userId, "domestic").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        if (null == merchantIdList || merchantIdList.isEmpty() || null == samsungDomesticTransaction || null == samsungDomesticTransaction.getMerchant_id() || (!merchantIdList.contains("ALL") && !merchantIdList.contains(samsungDomesticTransaction.getMerchant_id()))) {
                                                            LOGGER.severe("DOMESTIC SS MERCHANT ID PERMISSION : " + merchantIdList + " MERCHANT ID DETAIL: " + samsungDomesticTransaction);
                                                            throw IErrors.FORBIDDEN;
                                                        }
                                                        return DomesticTransactionService.listHistory(connReadOnly, connOnline, connBackUp, transactionId).flatMap(history -> {
                                                            return orderApprovalService.getByTransactionId(connBackUp, transactionId).map(orderApproval -> {

                                                                samsungDomesticTransaction.setOrder_date_confirm(orderApproval == null ? null : orderApproval.getCreate_time());
                                                                samsungDomesticTransaction.setOrder_user_confirm(orderApproval == null ? null : orderApproval.getUser_confirm());

                                                                double amount = 0;
                                                                for (DomesticTransactionHistory his : history) {
                                                                    if ((his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content)
                                                                            && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST.code) == 0)
                                                                            || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST_ONEPAY.content)
                                                                                    && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST_ONEPAY.code) == 0)
                                                                            || (his.getTransaction_type().equalsIgnoreCase("REFUND")
                                                                                    && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST_EXTERNAL.code) == 0)) {
                                                                        amount += his.getAmount().getTotal();
                                                                    }
                                                                }

                                                                samsungDomesticTransaction.setWait_for_approval_amount(amount);

                                                                return samsungDomesticTransaction;
                                                            });
                                                        });
                                                    });
                                                });
                                            });
                                });
                    }).subscribe(transaction -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
        }
    }


    @Autowired
    private OrderApprovalService orderApprovalService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ServerConfig serverConfig;

    private static final Logger LOGGER = Logger.getLogger(SamsungDomesticTransactionGetHandler.class.getName());
}
