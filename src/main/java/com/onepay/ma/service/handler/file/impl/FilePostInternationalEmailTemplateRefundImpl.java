package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.file.FilePostInternationalEmailTemplateRefundHandler;
import com.onepay.ma.service.handler.file.FilePostInternationalRefundHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 10/14/2020
 * Time: 9:42 AM
 * To change this ma-web.
 */

public class FilePostInternationalEmailTemplateRefundImpl implements FilePostInternationalEmailTemplateRefundHandler {
    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private CacheGuava cacheGuava;

    private SQLConnection connectBackUp;

    private MerchantService merchantService;
    private static final Logger LOGGER = Logger.getLogger(FilePostInternationalEmailTemplateRefundHandler.class.getName());
    private final static Gson gson = new Gson();
    public FilePostInternationalEmailTemplateRefundImpl(MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.cacheGuava = cacheGuava;
        this.fileService = fileService;
    }
    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL REFUND TEMPLATE POST ] => USER ID EMPTY");
            throw IErrors.RESOURCE_NOT_FOUND;
        }
        final HttpServerRequest request = rc.request();
        String body = rc.getBodyAsString();
        InternationalRefundEmailTemplateParameter parameter =  gson.fromJson(body, InternationalRefundEmailTemplateParameter.class);
        if(parameter == null) {
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL REFUND TEMPLATE POST ] => BODY EMPTY");
            throw IErrors.RESOURCE_NOT_FOUND;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            Map data = new HashMap();
                                            String fileName = "international" + StringPool.UNDERLINE + "refund" + StringPool.UNDERLINE+ "email_template" + StringPool.UNDERLINE;
                                            long date = new java.util.Date().getTime();
                                            fileName += date;
                                            String fileHashName = "";
                                            data.put("parameter", parameter);
                                            data.put("file_name", fileName);
                                            try {
                                                fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                            } catch (NoSuchAlgorithmException e) {
                                                rc.fail(e);
                                            } catch (UnsupportedEncodingException e) {
                                                rc.fail(e);
                                            }
                                            requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                            requestData.put(ParamsPool.FILE_NAME, fileName);
                                            FileDownload fileDownload = new FileDownload();
                                            fileDownload.setUser(userId);
                                            fileDownload.setFile_type("international_refund_email_template");
                                            fileDownload.setFile_name(fileName);
                                            fileDownload.setFile_hash_name(fileHashName);
                                            fileDownload.setConditions("");
                                            fileDownload.setExt("xls");
                                            return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                    return data;
                                                });
                                            });


                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }
            Message<InternationalRefundEmailTemplateParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
            QueueProducer.sendMessage(message);
            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }
}
