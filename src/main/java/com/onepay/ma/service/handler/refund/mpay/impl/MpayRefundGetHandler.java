package com.onepay.ma.service.handler.refund.mpay.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.service.mpay.impl.MpayRefundService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.refund.mpay.MpayRefundHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.mpay.MpayRefund;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
@Component
public class MpayRefundGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ Mpay REFUND ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        final HttpServerRequest request = rc.request();
        String transactionId = request.getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROMDATE);
            String toDate = rc.request().getParam(ParamsPool.TODATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(fromDate);
                oToDate = df.parse(toDate);

            } catch (Exception e) {

                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ Mpay REFUND ] => VALIDATION TOO LARGE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String merchantName = rc.request().getParam(ParamsPool.MERCHANTNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTNAME));

            String acqCode = rc.request().getParam(ParamsPool.ACQCODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACQCODE));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANTID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTID));

            String transId = rc.request().getParam(ParamsPool.TRANSACTIONID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTIONID));

            String transRef = rc.request().getParam(ParamsPool.MERCHANTTRANSACTIONREF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTTRANSACTIONREF));

            String orderInfo = rc.request().getParam(ParamsPool.ORDERINFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDERINFO));

            String bankId = rc.request().getParam(ParamsPool.BANKID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANKID));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));

            int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 100 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));

            String masking = rc.request().getParam(ParamsPool.MASKING) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MASKING));

            String appName = rc.request().getParam(ParamsPool.APPNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.APPNAME));

            String qrId = rc.request().getParam(ParamsPool.QRID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.QRID));

            String clientId = rc.request().getParam(ParamsPool.CLIENT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CLIENT_ID));

            String version = rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));

            String bankMerchantId = rc.request().getParam(ParamsPool.BANK_MERCH_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_MERCH_ID));

            String bankTerminalId = rc.request().getParam(ParamsPool.BANK_TERMI_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_TERMI_ID));

            MpayRefundParameter parameter = new MpayRefundParameter();
            parameter.setFromDate(fromDate);
            parameter.setToDate(toDate);
            parameter.setPage(page);
            parameter.setPageSize(pageSize);
            parameter.setTransactionId(transId);
            parameter.setStatus(status);
            parameter.setOrderInfo(orderInfo);
            parameter.setBankId(bankId);
            parameter.setMerchantTransactionReference(transRef);
            parameter.setMerchantName(merchantName);
            parameter.setAcqCode(acqCode);
            parameter.setMasking(masking);
            parameter.setAppName(appName);
            parameter.setQrId(qrId);
            parameter.setClientId(clientId);
            parameter.setVersion(version);
            parameter.setBankMerchantId(bankMerchantId);
            parameter.setBankTerminalId(bankTerminalId);

            Observable<NewBaseList<MpayRefund>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        //get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    //get back up connection
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackUp -> {
                                                return merchantService.list(connOnline, connBackUp, merchantId, userId, "mpay").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    parameter.setMerchantId(String.join(",", merchantIdList));

                                                    // LOGGER.log(Level.SEVERE, "START LIST Mpay REFUND with parameter :" + gson.toJson(parameter));
                                                    return MpayRefundService.list(connReadOnly,connOnline, parameter);
                                                });
                                            });

                                });
                    });
            obs.subscribe(transactions -> {
                // LOGGER.log(Level.SEVERE, "FINAL RESULT GET Mpay REFUND TRANS :" + gson.toJson(transactions));
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return MpayRefundService.get(connOnline, transactionId);
                                });
                    }).subscribe(MpayRefund -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, MpayRefund);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }


    }


    private final static Logger LOGGER = Logger.getLogger(MpayRefundHandler.class.getName());

    @Autowired
    private MerchantService merchantService;

    private Gson gson =  new Gson();
}
