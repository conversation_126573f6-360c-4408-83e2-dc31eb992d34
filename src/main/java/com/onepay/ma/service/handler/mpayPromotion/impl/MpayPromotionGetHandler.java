package com.onepay.ma.service.handler.mpayPromotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.service.mpayPromotion.MpayPromotionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 26-Jan-18.
 */
@Component
public class MpayPromotionGetHandler implements Handler<RoutingContext> {
    private static Logger LOGGER = Logger.getLogger(MpayPromotionGetHandler.class.getName());
    @Autowired
    private MpayPromotionService mpayPromotionService;

    @Autowired
    private UserService userService;

    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ MPAY  PR TRANSACTION GET ERROR ] => USER ID NOT FOUND");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ MPAY PR TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.VALIDATION_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ MPAY PR TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }

            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));
            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
            String orderId = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));
            String mobile = rc.request().getParam(ParamsPool.MOBILE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MOBILE));
            String merchantTnxRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));
            String instrumentNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));
            String transactionIdValue = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));
            String promotionId = rc.request().getParam(ParamsPool.PROMOTION_SID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PROMOTION_SID));
            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));
            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            MpayPromotionQuery query = new MpayPromotionQuery();
            query.setPage(page);
            query.setKeywords(keywords);
            query.setMerchant_id(merchantId);
            query.setMerchant_transaction_ref(merchantTnxRef);
            query.setOrder_info(orderId);
            query.setCard_number(instrumentNumber);
            query.setTransaction_id(transactionIdValue);
            query.setPageSize(pageSize);
            query.setCurrency(currency);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setPromotion_id(promotionId);
            query.setStatus(status);
            query.setMobile(mobile);
            query.setCard_type(cardType);
            Observable<Transactions<MpayPromotionTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        //get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackup -> {
                                                // find user by S-id
                                                return userService.getOneAm(connBackup, userId).flatMap(user -> {

                                                    return merchantService.list(connOnline, connBackup, merchantId, userId, "mpay").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        query.setMerchant_id(String.join(",", merchantIdList));

                                                        query.setTerminal_id(StringPool.BLANK);
                                                        return this.mpayPromotionService.search(connReadOnly, connOnline, query);
                                                    });
                                                });
                                            });
                                });
                    });

            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return mpayPromotionService.get(connOnline, transactionId);
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }
}
