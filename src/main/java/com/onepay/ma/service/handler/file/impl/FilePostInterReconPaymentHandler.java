package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
//import com.onepay.ma.service.models.reconciliation.PaymentReconcileParameterFile;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class FilePostInterReconPaymentHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL RECONCILIATION PAYMENT POST ] => USER ID NOT FOUND");
            throw  IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        ReconciliationQuery mapBody =  gson.fromJson(body, ReconciliationQuery.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL RECONCILIATION PAYMENT POST ] => BODY NOT FOUND");
            throw IErrors.VALIDATION_ERROR;
        }

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = mapBody.getFrom_date();
        String toDate = mapBody.getTo_date();
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 3) {
            throw  IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "international").flatMap(merchants -> {
//                                                PaymentReconcileParameterFile parameter = new PaymentReconcileParameterFile();
//                                                parameter.setAcquirerId(mapBody.getAcquirer());
//                                                parameter.setFromDate(mapBody.getFrom_date());
//                                                parameter.setToDate(mapBody.getTo_date());
//                                                parameter.setCardType(mapBody.getCard_type());
//                                                parameter.setInterval(mapBody.getInterval());
//                                                parameter.setMerchantId(mapBody.getMerchant_id());
//                                                parameter.setCurrency(mapBody.getCurrency());
//                                                parameter.setTransaction_type(mapBody.getTransaction_type());

                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                mapBody.setMerchant_id(String.join(",",merchantIdList));
                                                return insertPostData(connBackUp,  rc, userId, requestData, mapBody);

                                            });
                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }
            ReconciliationQuery parameter = (ReconciliationQuery) map.get("parameter");
            Message<ReconciliationQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, rc.request().path(), downloadFastOutQueue, downloadFastInQueue);
            QueueProducer.sendMessage(message);

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }

    /**
     * insert post data
     * @param connBackUp
     * @param rc
     * @param userId
     * @param requestData
     * @param mapBody
     */
    private Observable<Map> insertPostData(SQLConnection connBackUp,  RoutingContext rc, String userId, Map requestData, ReconciliationQuery mapBody){


        String fileName = "payment" + StringPool.UNDERLINE + "reconciliation" + StringPool.UNDERLINE;
        long date = new java.util.Date().getTime();
        fileName += date;
        String fileHashName = "";
        Map data = new HashMap();
        data.put("file_name", fileName);
        data.put("parameter", mapBody);

        try {
            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("Reconciliation");
        fileDownload.setExt("xls");
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(mapBody));
        return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });



    }

    /**
     * get list report by currency data list
     * @param listCurrency
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getTotalReportCurrency(List<String> listCurrency, Map returnMap, InternationalReportParameter parameter, int index) {

        String currency = listCurrency.get(index);
        final int finalIndex = index;
        return Observable.just(currency).flatMap(currencyData -> {
            parameter.setCurrency(currencyData);
//                if(integer > 45000){
//                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
//                }
            returnMap.put(currencyData, 999999);
            if(finalIndex < listCurrency.size() - 1){
                return getTotalReportCurrency( listCurrency, returnMap, parameter, finalIndex + 1);
            }else{
                return Observable.just(returnMap);
            }
        });

    }

    @Autowired
    private ReconcileService reconcileService;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(FilePostInterReconPaymentHandler.class.getName());
}
