package com.onepay.ma.service.handler.user.impl;


import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserChangePassHandler;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 12/04/18.
 */
public class UserChangePassHandlerImpl implements UserChangePassHandler {
    private static Logger LOGGER = Logger.getLogger(UserChangePassHandlerImpl.class.getName());

    public UserChangePassHandlerImpl(UserServiceV2 userService) {
        this.userService = userService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        // String userId ="874735D1B347FA85C91D321CE0A12843";
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => USER ID IS EMPTY " );
            throw IErrors.VALIDATION_ERROR;
        }
        io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
        if(body == null) {
            LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] => BODY PATCH IS EMPTY " );
            throw IErrors.VALIDATION_ERROR;
        }
        String passTmp = body.getString(ParamsPool.PASS) != null ? String.valueOf(body.getString(ParamsPool.PASS)) : StringPool.BLANK;
        String newPassTmp = body.getString(ParamsPool.NEW_PASS) != null ? String.valueOf(body.getString(ParamsPool.NEW_PASS)) : StringPool.BLANK;
        validatePass(passTmp, newPassTmp);
        String pass = userService.encodePassword(passTmp,2);
        String newPass = userService.encodePassword(newPassTmp,2);
        Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose())
                .flatMap(sqlConnection -> {
                    return userService.changePass(sqlConnection, userId,pass,newPass);
                }).subscribe(integer -> {
                    if(integer != 200){
                        throw IErrors.VALIDATION_ERROR;
                    }
                    rc.put(ParamsPool.HANDLER_DATA_CODE, integer);
                    rc.next();
                },throwable -> {
                    rc.fail(throwable);
                });
    }
    private void validatePass(String pass, String newPass){
        if("".equals(newPass)||pass.equals(newPass)){
            throw IErrors.INVALID_USER_PASSSAME;
        }
    }


    private UserServiceV2 userService;

}
