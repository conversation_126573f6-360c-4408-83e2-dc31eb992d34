package com.onepay.ma.service.handler.transaction.promotion;

import com.onepay.ma.service.handler.transaction.promotion.impl.PromotionTransactionGetHandlerImpl;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.PromotionTransactionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface PromotionTransactionGetHandler extends Handler<RoutingContext>  {
    static PromotionTransactionGetHandlerImpl create(PromotionTransactionService promotionTransactionService, MerchantService merchantService){
        return new PromotionTransactionGetHandlerImpl(promotionTransactionService, merchantService);

    }
}
