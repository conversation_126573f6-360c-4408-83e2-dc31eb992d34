package com.onepay.ma.service.handler.externalClient;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.onepay.ma.service.models.mpay.MpayRefundReq;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.OneCreditUtil;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringUtils;
import com.onepay.ma.service.util.Utils;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;

/**
 * Created by HungDX on 08-Aug-16
 */
public class MSPClient {


    private static final Logger LOGGER = Logger.getLogger(MSPClient.class.getName());
    private static String ONEPAY_MSP_SERVICE_NAME = PropsUtil.get("onepay_msp_service_name", "");
    private static String ONEPAY_MSP_SERVICE_REGION = PropsUtil.get("onepay_msp_service_region", "");
    private static String ONEPAY_MSP_SERVICE_BASE_URL = PropsUtil.get("onepay_msp_service_base_url", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_ID = PropsUtil.get("onepay_msp_service_client_id", "");
    // private static String ONEPAY_MSP_SERVICE_AUTH_TYPE = "ows1_request";
    // private static String ONEPAY_MSP_SERVICE_AUTH_ALGORITHM = "OWS1-HMAC-SHA256";
    private static String ONEPAY_MSP_SERVICE_CLIENT_KEY = PropsUtil.get("onepay_msp_service_client_key", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static String ONEPAY_MSP_SERVICE_EXTEND_PREFIX = PropsUtil.get("onepay_msp_service_extend_prefix","/msp-extend/api/v1");
    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonNode queryTxn(String orderId) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/invoices/" + orderId;

        String requestMethod = "GET";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();
        // queryParamMap.put("onecomm_txn_id", transId);
        // queryParamMap.put("merchant_id", merchantId);
        // queryParamMap.put("bank_id", bankId);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    new byte[] {}, requestDate, requestTimeOut);

            StringBuilder queryString = new StringBuilder("?");

            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
                queryString.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI + queryString;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;

    }

    public static JsonObject voidPurchase(String merchantId, String purchaseRef, String voidReference, Double amount, String accessCode, String hashCode, String userId, String note) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        // String requestURI = "/msp/api/v1/vpc/merchants/" + merchantId + "/purchases/" + URLEncoder.encode(purchaseRef) + "/purchase_voids/" + URLEncoder.encode(voidReference);
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(merchantId)) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(merchantId).append("/purchases/").append(URLEncoder.encode(purchaseRef)).append("/purchase_voids/").append(URLEncoder.encode(voidReference));
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/merchants/").append(merchantId).append("/purchases/").append(URLEncoder.encode(purchaseRef)).append("/purchase_voids/").append(URLEncoder.encode(voidReference));
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "PUT";
        // Map<String, Object> req = new HashMap<>();

        Map<String, String> data = OneCreditUtil.mspData("purchase_void", merchantId,
                accessCode, voidReference, purchaseRef,
                amount, userId, hashCode, "2", "");
        // req.put("vpc", order);

        // JsonObject jOrder = new JsonObject(order);
        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);

        try {

            LOGGER.log(Level.INFO, "PUT TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url: " + Utils.excludeSensitiveInfo(requestURI.toString()));
            LOGGER.log(Level.INFO, " info: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "text/html");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Utils.mask(jsonReturn));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }


    public static JsonObject refundPurchaseQT(String merchantId, String purchaseRef, String refundRef, Double amount, String accessCode, String hashCode, String userId, String note) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(merchantId)) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/refunds2");
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/refunds2");
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "POST";

        Map<String, String> data = OneCreditUtil.mspData("refund", merchantId,
                accessCode, refundRef, purchaseRef,
                amount, userId, hashCode, "2", note);

        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.log(Level.INFO, "POST TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url refundQT: " + Utils.excludeSensitiveInfo(requestURI.toString()));
            LOGGER.log(Level.INFO, " info refundQT: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("QT responseCode: " + responseCode);
            LOGGER.info("QT responseMsg: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Utils.mask(jsonReturn));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[QT REFUND ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject reverseDueQT(String merchantId, String purchaseRef, String refundRef, Double amount,
            String accessCode, String hashCode, String userId, String note) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base

        Map<String, String> data = OneCreditUtil.reverseDueMspData("refund", merchantId,
                accessCode, refundRef, purchaseRef,amount, userId, hashCode, "2", note);

        StringBuilder requestURI = new StringBuilder(ONEPAY_MSP_SERVICE_EXTEND_PREFIX).append("/vpc/refunds");
        StringBuilder mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "POST";

        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info("url reverseDue: " + requestURI.toString());
            LOGGER.info(" info reverseDue: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER,
                    signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER,
                    signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER,
                    onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "reverseDue responseCode: " + responseCode);
            LOGGER.info(() -> "reverseDue responseMsg: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.info("MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[reverseDue ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject refundPurchaseND(Map<String, String> data) throws Exception {
        JsonObject jsonReturn = null;

        StringBuilder requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/refunds2");
        StringBuilder mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);;

        String requestMethod = "POST";

        StringBuilder body = new StringBuilder("");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue() + "", "UTF-8")).append("&");
        }
        String dataParam = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                dataParam.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.log(Level.INFO, "POST TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url refund ND: " + Utils.excludeSensitiveInfo(requestURI.toString()));
            LOGGER.log(Level.INFO, " info refund ND: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.write(dataParam.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("ND refund responseCode: " + responseCode);
            LOGGER.info("QT refund responseMsg: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Utils.mask(jsonReturn));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ND REFUND ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject refundCapture(String merchantId, String captureRef, String refundRef, Double amount, String accessCode, String hashCode, String userId, String note) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        // String requestURI = "/msp/api/v1/vpc/merchants/" + merchantId + "/captures/" + captureRef +
        // "/capture_refunds/" + refundRef;
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(merchantId)) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(merchantId).append("/captures/").append(captureRef).append("/capture_refunds/").append(refundRef);
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/merchants/").append(merchantId).append("/captures/").append(captureRef).append("/capture_refunds/").append(refundRef);
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "PUT";

        Map<String, String> data = OneCreditUtil.mspData("capture_refund", merchantId,
                accessCode, refundRef, captureRef,
                amount, userId, hashCode, "2", note);

        Date requestDate = new Date();
        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);
        try {

            LOGGER.log(Level.INFO, "PUT TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url refund capture: " + Utils.excludeSensitiveInfo(mspOrderStateQueryURL.toString()));
            LOGGER.log(Level.INFO, " info refund capture: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());


            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Utils.mask(jsonReturn));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonNode refund(MpayRefundReq req) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/refunds";

        String requestMethod = "POST";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();
        // queryParamMap.put("onecomm_txn_id", transId);

        JsonObject jOrder = new JsonObject();
        jOrder.put("payment_id", req.getPayment_id());
        jOrder.put("merchant_id", req.getMerchant_id());
        jOrder.put("reference", req.getReference());
        jOrder.put("currency", req.getCurrency() == null ? "VND" : req.getCurrency());
        jOrder.put("client_id", req.getClient_id());
        jOrder.put("amount", req.getAmount());
        jOrder.put("operator", req.getOperator());
        jOrder.put("note", req.getNotes());
        if (null != req.getPromotion() && !req.getPromotion().isEmpty()) {
            jOrder.put("promotion", req.getPromotion());
        }
        jOrder.put("direct", req.getDirect());
        LOGGER.log(Level.INFO, "MSP_CLIENT REFUND REQUEST: " + Utils.mask(jOrder));
        try {
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI, queryParamMap, new LinkedHashMap<>(),
                    jOrder.encode().getBytes("UTF-8"), requestDate, requestTimeOut);

            // String queryString = "?";

            // for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
            // queryString += queryParam.getKey() + "=" + URLEncoder.encode(queryParam.getValue(), "UTF-8") +
            // "&";
            // }

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jOrder.encode().getBytes("UTF-8").length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jOrder.encode().getBytes("UTF-8"));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);

                LOGGER.log(Level.SEVERE, "MSP_CLIENT REFUND RESPONSE: " + Utils.mask(jsonReturn));
                // xử lý case msp-service trả về lỗi khi validate refund amount
            } else if (responseCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                // Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        LOGGER.log(Level.SEVERE, "MSP jsonReturn " + Utils.mask(jsonReturn));
        return jsonReturn;
    }

    public static JsonNode post(String qrData) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/invoices";

        String requestMethod = "POST";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();
        // queryParamMap.put("onecomm_txn_id", transId);

        ObjectNode jOrder = JsonNodeFactory.instance.objectNode();
        jOrder.put("input_type", "QR");
        jOrder.put("raw_data", qrData);
        try {

            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI, queryParamMap, new LinkedHashMap<>(),
                    jOrder.toString().getBytes("UTF-8"), requestDate, requestTimeOut);

            // String queryString = "?";

            // for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
            // queryString += queryParam.getKey() + "=" + URLEncoder.encode(queryParam.getValue(), "UTF-8") +
            // "&";
            // }

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jOrder.toString().getBytes("UTF-8").length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(jOrder.toString());
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonNode queryTxn(String qrMerchantId, String qrReference) {
        JsonNode jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/invoices";

        String requestMethod = "GET";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();
        // queryParamMap.put("onecomm_txn_id", transId);
        // queryParamMap.put("merchant_id", merchantId);
        // queryParamMap.put("bank_id", bankId);
        queryParamMap.put("merchant_id", qrMerchantId);
        queryParamMap.put("merchant_order_id", qrReference);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    new byte[] {}, requestDate, requestTimeOut);

            StringBuilder queryString = new StringBuilder("?");

            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
                queryString.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI + queryString;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = objectMapper.readTree(strReturn);
            } else if (responseCode == HttpURLConnection.HTTP_UNAUTHORIZED) {
                LOGGER.info("Invalid signature: \n" + onePAYServiceAuthorization.getDebugInfo());
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;

    }

    public static Map capture(Map<String, String> requestParam) throws IOException {
        Map mapReturn = new HashMap();
        // Request uri base
        // String requestURI = "/msp/api/v1/vpc/merchants/" + requestParam.get("vpc_Merchant") +
        // "/captures/" + requestParam.get("vpc_MerchTxnRef");
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(requestParam.get("vpc_Merchant"))) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(requestParam.get("vpc_Merchant")).append("/captures/").append(requestParam.get("vpc_MerchTxnRef"));
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/merchants/").append(requestParam.get("vpc_Merchant")).append("/captures/").append(requestParam.get("vpc_MerchTxnRef"));
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "PUT";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        try {

            StringBuilder queryString = new StringBuilder("");

            for (Map.Entry<String, String> queryParam : requestParam.entrySet()) {
                queryString.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(), queryParamMap, new LinkedHashMap<>(),
                    queryString.toString().getBytes("UTF-8"), requestDate, requestTimeOut);

            LOGGER.log(Level.SEVERE, "MSP_CLIENT CAPRURE REQUEST: " + Utils.excludeSensitiveInfo(mspOrderStateQueryURL.toString()));
            LOGGER.log(Level.SEVERE, "MSP_CLIENT CAPRURE QUERY PARAM : " + queryString);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            // connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(queryString.toString().getBytes("UTF-8").length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(queryString.toString());
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("Capture responseCode:" + responseCode);
            LOGGER.info("Capture responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                for (String kv : strReturn.split("&")) {
                    if (kv != null && kv.contains("=")) {
                        String[] aryKV = kv.split("=");
                        if (aryKV.length == 2) {
                            mapReturn.put(aryKV[0], aryKV[1]);
                        } else {
                            mapReturn.put(aryKV[0], "");
                        }
                    }
                }
            } else if (responseCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                for (String kv : strReturn.split("&")) {
                    if (kv != null && kv.contains("=")) {
                        String[] aryKV = kv.split("=");
                        if (aryKV.length == 2) {
                            mapReturn.put(aryKV[0], aryKV[1]);
                        } else {
                            mapReturn.put(aryKV[0], "");
                        }
                    }
                }
                // vpc_TxnResponseCode
                if (mapReturn.size() > 0) {
                    LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + mapReturn.getOrDefault("vpc_TxnResponseCode", "") + ", DESC: " + mapReturn.getOrDefault("vpc_Message", ""));
                    throw IErrors.INVALID_AMOUNT;
                }
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        // LOGGER.log(Level.INFO, "MSP_CLIENT CAPTURE RESPONSE: " + StringUtils.mapToString(mapReturn));
        LOGGER.log(Level.INFO, "MSP_CLIENT CAPTURE RESPONSE: " + Utils.mask(mapReturn));
        return mapReturn;
    }

    public static Map voidAPI(Map<String, String> requestParam) throws IOException {
        Map mapReturn = new HashMap();
        // String requestURI = "/msp/api/v1/vpc/merchants/" + requestParam.get("vpc_Merchant");
        StringBuilder requestURI = null;
        String requestMethod = "PUT";

        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(requestParam.get("vpc_Merchant"))) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(requestParam.get("vpc_Merchant"));
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/merchants/").append(requestParam.get("vpc_Merchant"));
        }
        String vpc_Command = requestParam.get("vpc_Command");
        if ("capture_refund_void".equals(vpc_Command)) {
            requestURI.append("/capture_refunds/").append(requestParam.get("vpc_OrgMerchTxnRef")).append("/capture_refund_voids/").append(requestParam.get("vpc_MerchTxnRef"));
        } else if ("capture_void".equals(vpc_Command)) {
            requestURI.append("/captures/").append(requestParam.get("vpc_OrgMerchTxnRef")).append("/capture_voids/").append(requestParam.get("vpc_MerchTxnRef"));
        } else if ("authorize_void".equals(vpc_Command)) {
            requestURI.append("/authorizes/").append(requestParam.get("vpc_OrgMerchTxnRef")).append("/authorize_voids/").append(requestParam.get("vpc_MerchTxnRef"));
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        try {
            StringBuilder queryString = new StringBuilder("");

            for (Map.Entry<String, String> queryParam : requestParam.entrySet()) {
                queryString.append(queryParam.getKey()).append("=").append(URLEncoder.encode(queryParam.getValue(), "UTF-8")).append("&");
            }
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(), queryParamMap, new LinkedHashMap<>(),
                    queryString.toString().getBytes("UTF-8"), requestDate, requestTimeOut);

            LOGGER.info("Void request:" + Utils.excludeSensitiveInfo(mspOrderStateQueryURL.toString()));
            LOGGER.info("Void query:" + queryString);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            // connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(queryString.toString().getBytes("UTF-8").length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.writeBytes(queryString.toString());
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("Void responseCode:" + responseCode);
            LOGGER.info("Void responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                for (String kv : strReturn.split("&")) {
                    if (kv != null && kv.contains("=")) {
                        String[] aryKV = kv.split("=");
                        if (aryKV.length == 2) {
                            mapReturn.put(aryKV[0], aryKV[1]);
                        } else {
                            mapReturn.put(aryKV[0], "");
                        }
                    }
                }
            } else if (responseCode == HttpURLConnection.HTTP_BAD_REQUEST) {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                for (String kv : strReturn.split("&")) {
                    if (kv != null && kv.contains("=")) {
                        String[] aryKV = kv.split("=");
                        if (aryKV.length == 2) {
                            mapReturn.put(aryKV[0], aryKV[1]);
                        } else {
                            mapReturn.put(aryKV[0], "");
                        }
                    }
                }
                // vpc_TxnResponseCode
                if (mapReturn.size() > 0) {
                    LOGGER.log(Level.SEVERE, "MSP_CLIENT PUT [ERROR] CODE: " + mapReturn.getOrDefault("vpc_TxnResponseCode", "") + ", DESC: " + mapReturn.getOrDefault("vpc_Message", ""));
                    throw IErrors.INVALID_AMOUNT;
                }
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT PUT [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        LOGGER.log(Level.INFO, "MSP_CLIENT VOID RESPONSE: " + Utils.mask(mapReturn));
        return mapReturn;
    }

    public static JsonObject voidRefund(String merchantId, String refundRef, String voidRef, double amount, String accessCode, String hashCode, String userId) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        // String requestURI = "/msp/api/v1/vpc/refunds2";
        // String requestURI = "/msp/api/v1/vpc/merchants/" + merchantId + "/refunds/" + refundRef +
        // "/voids/" + voidRef;
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        if (Utils.isBigMerchant(merchantId)) {
            requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(merchantId).append("/refunds/").append(refundRef).append("/voids/").append(voidRef);
        } else {
            requestURI = new StringBuilder("/msp/api/v1/vpc/merchants/").append(merchantId).append("/refunds/").append(refundRef).append("/voids/").append(voidRef);
        }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);

        String requestMethod = "PUT";

        Map<String, String> data = OneCreditUtil.mspData("voidRefundPurchase", merchantId,
                accessCode, voidRef, refundRef, amount, userId, hashCode, "2", "");
        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.log(Level.INFO, "POST TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url: " + Utils.excludeSensitiveInfo(requestURI.toString()));
            LOGGER.log(Level.INFO, " info: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode void refund: " + responseCode);
            LOGGER.info("responseMsg void refund: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Utils.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Utils.mask(jsonReturn));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }
}
