package com.onepay.ma.service.handler.merchant.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.service.UserMerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 28-Sep-17.
 */
@Component
public class UserMerchantGetHandler implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {

        final HttpServerRequest request = rc.request();
        String merchantId = request.getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.MERCHANT_ID));
        String keyword = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));

        JDBCClient clientBackup = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                .flatMap(connBankup -> {
                    return userMerchantService.findUserByMerchant(connBankup, merchantId, keyword);
                }).subscribe(userBaseList -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, userBaseList);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    @Autowired
    private UserMerchantService userMerchantService;
}
