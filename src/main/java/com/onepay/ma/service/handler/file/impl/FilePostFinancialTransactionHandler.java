package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.financial.FinancialTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by tuydv on 5/8/18.
 */
@Component
public class FilePostFinancialTransactionHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE FINANCIAL TRANSACTION POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        FinancialTransactionQuery mapBody = gson.fromJson(body, FinancialTransactionQuery.class);
        if (mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE FINANCIAL TRANSACTION POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        String finalKeyWords = FunctionUtil.isBeginIsACard(mapBody.getKeywords()) && mapBody.getKeywords().contains("*") ? mapBody.getKeywords().replaceAll("\\*", "0") : mapBody.getKeywords();
        String finalCardNumber = FunctionUtil.isBeginIsACard(mapBody.getCard_number()) && mapBody.getCard_number().contains("*") ? mapBody.getCard_number().replaceAll("\\*", "0") : mapBody.getCard_number();

        if (FunctionUtil.isCardData(finalKeyWords)) {
            rc.vertx().executeBlockingObservable(objectFuture -> {
                objectFuture.complete(FunctionUtil.oneSMHmac(finalKeyWords, serverConfig));
            }).subscribe(o -> {
                mapBody.setKeywords(String.valueOf(o));
            });
            insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
        } else {
            if (FunctionUtil.isCardData(finalCardNumber)) {
                rc.vertx().executeBlockingObservable(objectFuture -> {
                    objectFuture.complete(FunctionUtil.oneSMHmac(finalCardNumber, serverConfig));
                }).subscribe(o -> {
                    mapBody.setCard_number(String.valueOf(o));
                });
                insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            } else {
                insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
            }
        }
    }

    private void insertDowload(FinancialTransactionQuery query, Map requestData, HttpServerRequest request, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc) {
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return merchantService.list(connOnline, connBackUp, query.getMerchant_id(), userId, "international").flatMap(merchants -> {
                                                return merchantService.list(connOnline, connBackUp, "", userId, "international").flatMap(merchantsAll-> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                query.setMerchant_id(String.join(",", merchantIdList));
                                                convertFormDateToDate(query);
                                                Map data = new HashMap();
                                                return FinancialTransactionService.getTotalDownload(connOnline, connReadOnly, connectBackUp, query).flatMap(integer -> {
                                                    if (integer == 0) {
                                                        LOGGER.log(Level.SEVERE, "[ FILE FINANCIAL TRANSACTION POST ] => TOTAL NOT FOUND");
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }

                                                    if (integer > serverConfig.getRowLimit()) {
                                                        throw IErrors.PASS_EXCEL_LIMIT;
                                                    }
                                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                                                    String date = sdf.format(new Date());
                                                    String fileName = generateDownloadFileName(merchantsAll.stream().map(Merchant::getMerchant_id).collect(Collectors.toList()));
                                                    String fileHashName = "";
                                                    data.put("parameter", query);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    requestData.put(ParamsPool.FILE_EXT, "xlsx");
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("International_transaction");
                                                    if (integer <= serverConfig.getRowLevel()) {
                                                        fileDownload.setExt("xlsx");
                                                    } else {
                                                        fileDownload.setExt("zip");
                                                    }
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(query));
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                                    });
                            });
                }).subscribe(map -> {
                    if (connectBackUp != null) {
                        connectBackUp.commitObservable();
                    }
                    FinancialTransactionQuery parameter = (FinancialTransactionQuery) map.get("parameter");
                    int row = Integer.valueOf(map.get("row").toString());
                    if (row <= serverConfig.getRowLevel()) {
                        // fileDownload.setExt("csv");
                        Message<FinancialTransactionQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                        QueueProducer.sendMessage(message);
                    } else {
                        // fileDownload.setExt("zip");
                        Message<FinancialTransactionQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                        QueueProducer.sendMessage(message);
                    }

                    rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                    rc.next();
                }, throwable -> {
                    if (connectBackUp != null) {
                        connectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

    }

    private String generateDownloadFileName(List<String> loginUserMerchantId) {
        List<String> bigMerchantId = new ArrayList<>();
        bigMerchantId.addAll(Arrays.asList(PropsUtil.get("bigMerchantId", "").split(",")));

        // bigMerchantId now contains only the elements which are also contained in loginUserMerchantId.
        LOGGER.log(Level.INFO, "bigMerchantId: " +bigMerchantId.toString());
        LOGGER.log(Level.INFO, "loginUserMerchantId: " +loginUserMerchantId.toString());
        bigMerchantId.retainAll(loginUserMerchantId);
        
        if (bigMerchantId.size() > 0) {

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String date = sdf.format(new Date());
            return "International Card Transaction_" + date;
        }
        long date = new java.util.Date().getTime();
        return "Transaction" + StringPool.UNDERLINE + date;



    }

    /**
     * Convert FromDate,ToDate Str(dd/MM/yyyy hh:mm a)--> Str(YYYY-MM-dd HH:mm:ss)
     */
    private FinancialTransactionQuery convertFormDateToDate(FinancialTransactionQuery query) {

        String strFromDate = query.getFrom_date();
        String strToDate = query.getTo_date();
        try {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            Date oFromDate = df.parse(strFromDate);
            Date oToDate = df.parse(strToDate);
            oToDate = DateUtils.addMinutes(oToDate, 1);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            strFromDate = sdf.format(oFromDate);
            strToDate = sdf.format(oToDate);
            query.setFromDate(query.getFrom_date());
            query.setToDate(query.getTo_date());
            query.setFrom_date(strFromDate);
            query.setTo_date(strToDate);
        } catch (Exception e) {

        }
        return query;
    }

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier(value = "downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier(value = "downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(FilePostFinancialTransactionHandler.class.getName());
}
