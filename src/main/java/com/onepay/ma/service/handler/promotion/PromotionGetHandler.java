package com.onepay.ma.service.handler.promotion;

import com.onepay.ma.service.handler.promotion.impl.PromotionGetHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionGetHandler extends Handler<RoutingContext> {
    static PromotionGetHandlerImpl create(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService){
        return new PromotionGetHandlerImpl(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService);

    }
}
