package com.onepay.ma.service.handler.mpayNotification.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.service.notification.AppNotificationConfigService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 30-Sep-17.
 */
@Component
public class MpayNotificationConfigPutHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        String body = rc.getBodyAsString();
        MpayNotificationConfig mapBody = gson.fromJson(body, MpayNotificationConfig.class);
        if (mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        String terminalId = mapBody.getTerminalIds() != null ? String.join(StringPool.COMMA,mapBody.getTerminalIds()) : StringPool.BLANK;
        String storeId = mapBody.getStoreIds() != null ? String.join(StringPool.COMMA,mapBody.getStoreIds()) : StringPool.BLANK;
        String userId = mapBody.getUserIds() != null ? String.join(StringPool.COMMA,mapBody.getUserIds()) : StringPool.BLANK;
        String type = mapBody.getTypes() != null ? String.join(StringPool.COMMA,mapBody.getTypes()) : StringPool.BLANK;

        mapBody.setTerminalId(terminalId);
        mapBody.setStoreId(storeId);
        mapBody.setUserId(userId);
        mapBody.setType(type);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(sqlBackUp -> {
                    sqlConnectionBackup = sqlBackUp;
                    return sqlConnectionBackup.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return appNotificationConfigService.update(sqlBackUp, mapBody);
                    });
                }).subscribe(config -> {
            if(sqlConnectionBackup != null) {
                sqlConnectionBackup.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_RESULT, config);
            rc.next();
        }, throwable -> {
            if (sqlConnectionBackup != null) {
                sqlConnectionBackup.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }


    private SQLConnection sqlConnectionBackup;

    private final static Gson gson = new Gson();

    @Autowired
    private AppNotificationConfigService appNotificationConfigService;
}
