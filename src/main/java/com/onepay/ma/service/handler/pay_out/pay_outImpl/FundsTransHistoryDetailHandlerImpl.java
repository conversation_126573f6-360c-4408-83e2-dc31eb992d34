package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import com.onepay.ma.service.models.pay_out.FundsTransHistoryDto;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import groovy.json.JsonException;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

@Component
public class FundsTransHistoryDetailHandlerImpl implements Handler<RoutingContext> {

    // @Autowired
    // private FundsTransHistoryService fundsTransHistoryService;
    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String userId = myEvent.get(ParamsPool.USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String accountId = myEvent.request().getParam("accountId");
        String funds_transfer_id = myEvent.request().getParam("fundsTransferId");
        JsonObject jo = OnePayoutClient.getFundsTransHistoryById(userId, xUserId, xRequestId, accountId, funds_transfer_id);
        FundsTransHistoryDto fundsTransHistoryDtos = bindFundsTransHistoryDtos(jo, null);
        myEvent.put(ParamsPool.HANDLER_DATA_RESULT, fundsTransHistoryDtos);
        myEvent.next();

    }

    /**
     * convert data from result set to firm banking transactions
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private FundsTransHistoryDto bindFundsTransHistoryDtos(JsonObject rs, String merchantId) {
        FundsTransHistoryDto fundsTransHistoryDto = new FundsTransHistoryDto();
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            fundsTransHistoryDto.setS_id(rs.getString("transaction_id"));

            fundsTransHistoryDto.setMerchantId(rs.getString("merchant_id"));
            fundsTransHistoryDto.setMerchantAccount(rs.getString("account_id"));
            fundsTransHistoryDto.setMerchantName(null);
            fundsTransHistoryDto.setMerchantTransId(rs.getString("funds_transfer_id"));
            fundsTransHistoryDto.setBalanceBefore(rs.getString("before_account_balance") == null ? 0 : Double.parseDouble(rs.getString("before_account_balance")));
            fundsTransHistoryDto.setBalanceAfter(rs.getString("after_account_balance") == null ? 0 : Double.parseDouble(rs.getString("after_account_balance")));
            fundsTransHistoryDto.setBalanceBankBefore(rs.getString("before_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("before_bank_balance")));
            fundsTransHistoryDto.setBalanceBankAfter(rs.getString("after_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("after_bank_balance")));
            fundsTransHistoryDto.setReceivedAccountNumber(rs.getString("account_number"));
            fundsTransHistoryDto.setReceivedAccountName(rs.getString("holder_name"));
            fundsTransHistoryDto.setReceivedCardNumber(rs.getString("card_number"));
            fundsTransHistoryDto.setTransactionId(rs.getString("transaction_id"));
            fundsTransHistoryDto.setTransactionDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setAmount(rs.getString("amount") == null ? 0 : Double.parseDouble(rs.getString("amount")));
            fundsTransHistoryDto.setRemark(rs.getString("remark"));
            fundsTransHistoryDto.setState(rs.getString("state"));

            fundsTransHistoryDto.setBankTransactionId(null);
            fundsTransHistoryDto.setBeneficiarySwiftCode(rs.getString("swift_code"));
            fundsTransHistoryDto.setBeneficiaryBankName((rs.containsKey("bank_name") && StringUtils.isNotBlank(rs.getString("bank_name")))? rs.getString("bank_name"): rs.getString("receipt_bank_name"));
            fundsTransHistoryDto.setBankCode(rs.getString("response_code"));
            fundsTransHistoryDto.setBankMsg(rs.getString("message"));

            fundsTransHistoryDto.setCreatedDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setUpdatedDate(rs.getString("update_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("update_time")).getTime()));
            fundsTransHistoryDto.setFundDate(rs.getString("fund_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("fund_time")).getTime()));
            if (rs.containsKey("meta_data") && null != rs.getString("meta_data")) {
                fundsTransHistoryDto.setMetadata(rs.getString("meta_data"));
                if (isJSONValid(rs.getString("meta_data"))) {
                    JsonObject metaData = new JsonObject(rs.getString("meta_data"));
                    if (metaData.containsKey("creator_id")) {
                        fundsTransHistoryDto.setCreator_id(metaData.getString("creator_id"));
                    }
                    if (metaData.containsKey("creator_name")) {
                        fundsTransHistoryDto.setCreator_name(metaData.getString("creator_name"));
                    }
                    if (metaData.containsKey("verifier_id")) {
                        fundsTransHistoryDto.setVerifier_id(metaData.getString("verifier_id"));
                    }
                    if (metaData.containsKey("verifier_name")) {
                        fundsTransHistoryDto.setVerifier_name(metaData.getString("verifier_name"));
                    }
                }
            }
            fundsTransHistoryDto.setOperator(rs.getString("operator_user"));

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fundsTransHistoryDto;
    }

    public boolean isJSONValid(String test) {
        try {
            new JsonObject(test);
        } catch (JsonException ex) {
            try {
                new JsonArray(test);
            } catch (JsonException ex1) {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }
}
