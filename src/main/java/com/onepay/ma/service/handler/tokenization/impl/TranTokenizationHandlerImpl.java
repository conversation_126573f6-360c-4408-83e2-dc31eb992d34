/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.onepay.ma.service.handler.tokenization.impl;

import com.onepay.commons.util.Convert;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.tokenization.TokenizationHandler;
import com.onepay.ma.service.handler.tokenization.TranTokenizationHandler;
import com.onepay.ma.service.models.TokenizationTranParams;
import com.onepay.ma.service.models.TokenizationTrans;
import com.onepay.ma.service.models.UserTokenization;
import com.onepay.ma.service.service.TokenService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class TranTokenizationHandlerImpl implements TranTokenizationHandler {

    public TranTokenizationHandlerImpl(TokenService tokenizationService, UserService userService) {
        this.userService = userService;
        this.tokenizationService = tokenizationService;
    }

    private UserService userService;
    private TokenService tokenizationService;


    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ TRANSACTION TOKEN GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String userIdSearch = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);

        Date oFromDate= Convert.toDate(fromDate,"dd/MM/yyyy hh:mm a");
        Date oToDate= Convert.toDate(toDate,"dd/MM/yyyy hh:mm a");

        int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if(months > 6){
            LOGGER.log(Level.SEVERE, "[ TOKENIZATION TRANSACTION SEARCH GET] => REQUEST TOO LONG");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String tranId = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));
        String cardNo = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));
        String userIdin = rc.request().getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_ID));
        String userGroup = rc.request().getParam(ParamsPool.USER_GROUP_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_GROUP_ID));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
        TokenizationTranParams params = new TokenizationTranParams();
        params.setTranId(tranId);
        params.setCardNo(cardNo);
        params.setUserId(userIdin);
        params.setUserGroup(userGroup);
        params.setFromDate(fromDate);
        params.setToDate(toDate);
        params.setPage(page);
        params.setPageSize(pageSize);
        Observable<TokenizationTrans> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return tokenizationService.listTranToken(connBackup, params);
                                        });
                            });
                });

        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }


    private static Logger LOGGER = Logger.getLogger(TranTokenizationHandler.class.getName());
}
