package com.onepay.ma.service.handler.user.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserDeleteHandler;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public class UserDeleteHandlerImpl implements UserDeleteHandler {

    String url_logout =  PropsUtil.get("one_am.logout_2", ""); // mOnePAY
    String url_continute =  PropsUtil.get("one_am.continue_2", ""); // mOnePAY

    public UserDeleteHandlerImpl(CacheGuava cacheGuava, OneAMConfig oneAMConfig) {
        this.cacheGuava = cacheGuava;
        this.oneAMConfig = oneAMConfig;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));

        if(!userId.isEmpty()){
            cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
            cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
        }
        Map returnMap = new HashMap();
        returnMap.put("return_url", url_logout + StringPool.EQUAL + url_continute);
        rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
        rc.put(ParamsPool.HANDLER_DATA_RESULT, returnMap);
        rc.next();

    }


    private CacheGuava cacheGuava;

    private OneAMConfig oneAMConfig;

}
