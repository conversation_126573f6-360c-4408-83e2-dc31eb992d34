package com.onepay.ma.service.handler.transaction.international.impl;

import com.esotericsoftware.kryo.util.Util;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.handler.externalClient.CardHelperClient;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.service.impl.MerchantPermitService;
import com.onepay.ma.service.service.installment.InstallmentService;
import com.onepay.ma.service.service.lock.LockService;
import com.onepay.ma.service.service.refundCapture.RefundCaptureService;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import static java.nio.charset.StandardCharsets.UTF_8;
import java.nio.charset.StandardCharsets;

/**
 * Created by huynguyen on 4/1/16.
 */
@Component
public class InternationalTransactionPatchHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    MerchantPermitService merchantPermitService;

    @Autowired
    MerchantApprovalService merchantApproval;

    @Autowired
    RefundApprovalService refundApprovalService;

    @Autowired
    UserService userService;

    @Autowired
    RefundConfig refundConfig;

    @Autowired
    InternationalTransactionService internationalTransactionService;

    @Autowired
    InternationalRefundService internationalRefundService;

    @Autowired
    MerchantService merchantService;

    @Autowired
    GeneralReportService generalReportService;

    @Autowired
    LockService lockService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if (ipAddress == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String transactionId = rc.request().getParam("id");
        final HttpClient httpClient = rc.vertx().createHttpClient();
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        if (transactionId != null) {
            String body = rc.getBodyAsString();
            PatchRequest mapPatchRequest = gson.fromJson(body, PatchRequest.class);
            if (mapPatchRequest == null) {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> BODY EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
            // if (refundData.getValue().getTransaction_reference() == null) {
            //     long currentTime = System.currentTimeMillis();
            //     String merchantTxnRef = refundData.getValue().getMerchant_id() + "_" + currentTime;
            //     String requestId = xRequestId;
            //     if (merchantTxnRef.length() > 32) {
            //         int largerInt = merchantTxnRef.length() - 32 + 3;
            //         requestId = xRequestId.substring(0, requestId.length() - largerInt);
            //         merchantTxnRef = xRequestId + "_R_" + refundData.getValue().getMerchant_id();

            //     }
            //     refundData.getValue().setTransaction_reference(merchantTxnRef);
            // }
            Boolean skipCallSynchronize = mapPatchRequest.isSkipCallSynchronize() == null ? true : mapPatchRequest.isSkipCallSynchronize();
            LOGGER.log(Level.INFO, "****** SKIP SYNC REAL TIME INTERNATIONAL = " + skipCallSynchronize);

            // check if msp transaction id existed
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        if (mapPatchRequest.getPath().equals("/capture")) {
                            return internationalTransactionService.get(connOnline, transactionId).flatMap(interTrans -> {
                                refundData.getValue().setOrg_transaction_reference(interTrans.getTransaction_reference()); 
                                LocalDateTime settlementTime = interTrans.getTransaction_time().toLocalDateTime();
                                return internationalTransactionService.getBrandConfig(connOnline, interTrans.getCard().getCard_type().toLowerCase()).flatMap(brandConfig -> {
                                    JsonObject data = new JsonObject(brandConfig.getData());
                                    int rangeDate = 7;
                                    if (data.containsKey("capture_cutoff_time")) {
                                        JsonObject arr = data.getJsonObject("capture_cutoff_time");
                                        if (null != arr && !arr.isEmpty()) {
                                            rangeDate = arr.getInteger("capture_date_cutoff");
                                        }
                                    }
                                    LocalDateTime cutOffDay = settlementTime.plusDays(rangeDate);
                                    LocalDateTime now = LocalDateTime.now();
                                    if (!now.isBefore(cutOffDay) && !now.equals(cutOffDay)) {
                                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy hh:mm a");
                                        StringBuilder messageVi = new StringBuilder("Giao dịch đã quá hạn quyết toán (trước ").append(cutOffDay.format(formatter)).append(")");
                                        StringBuilder messageEn = new StringBuilder("This transaction is overdue for capture (before ").append(cutOffDay.format(formatter)).append(")");
                                        JsonObject result = new JsonObject();
                                        result.put("message_en", messageEn);
                                        result.put("message_vi", messageVi);
                                        throw new ErrorException(500, "OUT_OF_DATE_TRANSACTION", result.toString(), "", "");
                                    }
                                    return Observable.just(brandConfig);
                                });
                            });
                        }
                        try {
                            Integer.parseInt(transactionId);
                            return Observable.just(transactionId);
                        } catch (NumberFormatException ex) {
                            return generalReportService.getPurchaseByMspId(connOnline, transactionId);
                        }
                    }).subscribe(i -> {
                        String transactionIdS = i == null ? transactionId : i.toString();
                        if (mapPatchRequest.getPath().equals("/refund")) {
                            refund(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionIdS, xRequestId, userId, httpClient, skipCallSynchronize);
                        } else if (mapPatchRequest.getPath().equals("/recharge")) {
                            recharge(rc, body, clientOnline, xRequestId, ipAddress, skipCallSynchronize);
                        } else if (mapPatchRequest.getPath().equals("/recharge2D")) {
                            recharge2D(rc, body, clientOnline, transactionIdS, xRequestId, skipCallSynchronize);
                        } else if (mapPatchRequest.getPath().equals("/void-purchase")) {
                            voidPurchase(rc, transactionIdS, xRequestId, userId, clientOnline, clientBackUp, refundData);
                        } else if (mapPatchRequest.getPath().equals("/void-authorize")) {
                            voidAuthorize(rc, transactionIdS, xRequestId, userId, clientOnline, refundData.getValue());
                        } else if (mapPatchRequest.getPath().equals("/void-capture")) {
                            voidCapture(rc, transactionIdS, xRequestId, userId, clientOnline, refundData.getValue());
                        } else if (mapPatchRequest.getPath().equals("/void-capture-refund")) {
                            voidCaptureRefund(rc, transactionIdS, xRequestId, userId, clientOnline, refundData.getValue());
                        } else if (mapPatchRequest.getPath().equals("/refund-onepay-manual")) {
                            refundOnePAYManual(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionIdS, xRequestId, userId, httpClient);
                        } else if (mapPatchRequest.getPath().equals("/reverse-due-to-dispute")) {
                            // reverseDueToDispute(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionIdS, xRequestId, userId, httpClient);
                            reverseDueToDispute2(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionIdS,
                                    xRequestId, userId, httpClient, skipCallSynchronize);

                        } else if (mapPatchRequest.getPath().equals("/capture")) {
                            capture(rc, refundData, clientOnline, xRequestId, userId, ipAddress, skipCallSynchronize);
                        } else {
                            throw IErrors.VALIDATION_ERROR;
                        }
                    }, throwable -> {
                        rc.fail(throwable);
                    });


        } else {
            throw IErrors.VALIDATION_ERROR;
        }

    }


    /**
     * recharge
     *
     * @param rc
     * @param body
     * @param clientOnline
     * @param xRequestId
     * @param ipAddress
     */
    private void recharge2D(RoutingContext rc, String body, JDBCClient clientOnline, String transactionId, String requestId, Boolean skipCallSynchronize) {
        PatchRequest<RechargeData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RechargeData>>() {}.getType());



        JsonObject jsonReturn = null;
        // Map requestParam = captureData(refundData.getValue(), merchantData, userId, xRequestId);

        long currentTime = System.currentTimeMillis();
        String merchantTxnRef = refundData.getValue().getMerchant_id() + "_" + currentTime;
        if (merchantTxnRef.length() > 32) {
            int largerInt = merchantTxnRef.length() - 32 + 3;
            requestId = requestId.substring(0, requestId.length() - largerInt);
            merchantTxnRef = requestId + "_R_" + refundData.getValue().getMerchant_id();

        }
        try {
            jsonReturn = CardHelperClient.recharge(refundData.getValue().getMerchant_id(), refundData.getValue().getTransaction_reference(), merchantTxnRef, refundData.getValue().getOrder_info(), refundData.getValue().getAmount(), requestId);
        } catch (Exception ex) {
            throw IErrors.RECHARGE_FAILED;
        }
        if (jsonReturn == null || jsonReturn.size() == 0) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        String responseCode = jsonReturn.containsKey("response_code") ? jsonReturn.getString("response_code") : "";
        JsonObject result = new JsonObject();
        rc.put(ParamsPool.HANDLER_DATA_CODE, "200");
        if (responseCode.equalsIgnoreCase("00")) {
            result.put("status", 200);
            result.put("message_en", "Recharge successful");
            result.put("message_vi", "Recharge thành công");
        } else {
            String messageVi = jsonReturn.containsKey("message_vi") ? jsonReturn.getString("message_vi") : "Hệ thống tạm thời gián đoạn";
            String messageEn = jsonReturn.containsKey("message_en") ? jsonReturn.getString("message_en") : "Internal Server Error";
            result.put("status", 500);
            result.put("message_en", "Recharge Fail: " + messageEn);
            result.put("message_vi", "Recharge Lỗi: " + messageVi);
        }
        rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
        LOGGER.info("ParamsPool.HANDLER_DATA_RESULT: " + result);
        rc.next();
    }

    private void capture(RoutingContext rc, PatchRequest<RefundData> refundData, JDBCClient clientOnline, String xRequestId, String userId, String ipAddress, Boolean skipCallSynchronize) {
        // PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
        Observable<MerchantData> merchantObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return merchantService.getData(connOnline, refundData.getValue().getMerchant_id());
        });
        merchantObs.subscribe(merchantData -> {
            Map jsonReturn = null;

                if (refundData.getValue().getTransaction_reference() == null || refundData.getValue().getTransaction_reference().isEmpty()) {
                    // Init refund reference
                    long currentTime = System.currentTimeMillis();
                    String merchantTxnRef = refundData.getValue().getMerchant_id() + "_" + currentTime;
                    // String requestId = xRequestId;
                    if (merchantTxnRef.length() > 32) {
                        int largerInt = merchantTxnRef.length() - 32 + 3;
                        merchantTxnRef = merchantTxnRef.substring(0, merchantTxnRef.length() - largerInt);
                    }
                    refundData.getValue().setTransaction_reference(merchantTxnRef);
                }
                Map requestParam = captureData(refundData.getValue(), merchantData, userId, xRequestId);
                try {
                    jsonReturn = MSPClient.capture(requestParam);
                } catch (Exception ex) {
                    if (ex instanceof ErrorException) {
                        throw IErrors.INVALID_AMOUNT;
                    } else
                        throw IErrors.CAPTURE_MSP_ERROR;
                }
                if (jsonReturn == null || jsonReturn.size() == 0) {
                    throw IErrors.CAPTURE_MSP_NO_RESPONSE;
                }

                // call ep dong bo international
                if (skipCallSynchronize == false) {
                    try {
                        OneSchedClient.synchronizeInterCaptureViewSync();
                    } catch (Exception e) {
                        LOGGER.log(Level.INFO, "international call ep dong bo capture error " + e.getMessage());
                    }
                }
                String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();
                rc.put(ParamsPool.HANDLER_DATA_CODE, "200");
                JsonObject result = new JsonObject();
                LOGGER.info("responseCode: " + responseCode);
                LOGGER.info("responseCode.equalsIgnoreCase(\"0\"): " + responseCode.equalsIgnoreCase("0"));
                if (responseCode.equalsIgnoreCase("0")) {
                    String merchantTxnRef = jsonReturn.getOrDefault("vpc_MerchTxnRef", "").toString();
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                        return internationalTransactionService.getCaptureTransByRef(connOnline, refundData.getValue().getMerchant_id(), merchantTxnRef).map(captureDb -> {
                            return captureDb;
                        });
                    }).subscribe(dataCapture -> {
                        LOGGER.info("dataCapture: " + dataCapture);
                            result.put("status", 200);
                            result.put("message_en", "Capture successful");
                            result.put("message_vi", "Capture thành công");
                            result.put("transaction_id", dataCapture.getTransaction_id());
                            result.put("merchant_transaction_ref", merchantTxnRef);
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
                            rc.next();
                    }, throwable -> {
                        LOGGER.info("merchantTxnRef: " + merchantTxnRef);
                        result.put("status", 200);
                        result.put("message_en", "Capture successful");
                        result.put("message_vi", "Capture thành công");
                        result.put("merchant_transaction_ref", merchantTxnRef);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
                        rc.next();
                    });
                } else {
                    result.put("status", 500);
                    result.put("message_en", "Capture MSP Fail");
                    result.put("message_vi", "Capture MSP Lỗi");
                    result.put("transaction_id", "");
                    result.put("merchant_transaction_ref", "");
                    LOGGER.info("result: " + result);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
                    rc.next();
                }
            }, throwable -> {
                rc.fail(throwable);
            });
        // }, throwable -> {
        //     rc.fail(throwable);
        // });
    }

    private Map captureData(RefundData refundData, MerchantData merchantData, String userId, String requestId) {
        Map mIn = new HashMap();
        mIn.put("vpc_Command", "capture");
        mIn.put("vpc_Merchant", refundData.getMerchant_id());
        mIn.put("vpc_AccessCode", merchantData.getAccessCode());
        mIn.put("vpc_MerchTxnRef", refundData.getTransaction_reference());
        mIn.put("vpc_OrgMerchTxnRef", refundData.getOrg_transaction_reference());
        mIn.put("vpc_Amount", Convert.toString(refundData.getAmount() * 100, "0"));
        mIn.put("vpc_Operator", userId);
        mIn.put("vpc_ClientId", "MADM_SERVICE");
        mIn.put("vpc_Version", "2");

        // ObjectMapper mapper = new ObjectMapper();
        // Map<String, String> reqParams = mapper.convertValue(mIn, new TypeReference<Map<String,
        // String>>(){});
        String secureHash = OneCreditUtil.createMerchantHash(mIn, merchantData.getHashCode(), "2");
        mIn.put("vpc_SecureHash", secureHash);

        return mIn;
    }

    /**
     * create recharge data
     *
     * @param rechargeData
     * @param merchantData
     * @param ipAddress
     * @param requestId
     * @return
     */
    private Map rechargeData(RechargeData rechargeData, MerchantData merchantData, String ipAddress, String requestId) {
        Map req = new HashMap();
        req.put("command_id", "RECHARGE");
        Map vpc = new HashMap();
        vpc.put("vpc_AccessCode", merchantData.getAccessCode());
        vpc.put("vpc_OrderInfo", rechargeData.getOrder_info());
        // vpc.put("vpc_ReturnURL", "");
        vpc.put("vpc_Version", "2");
        // vpc.put("vpc_Locale", "");
        vpc.put("vpc_TicketNo", ipAddress);
        if (rechargeData.getCvv() != null) {
            vpc.put("vpc_CardSecurityCode", String.valueOf(rechargeData.getCvv()));
        } else {
            vpc.put("vpc_CardSecurityCode", "0");
        }
        vpc.put("vpc_Merchant", rechargeData.getMerchant_id());
        vpc.put("vpc_Amount", com.onepay.commons.util.Convert.toString(rechargeData.getAmount() * 100, "0"));
        String merchantTxnRef = requestId + "_R_" + rechargeData.getMerchant_id();
        if (merchantTxnRef.length() > 32) {
            int largerInt = merchantTxnRef.length() - 32 + 3;
            requestId = requestId.substring(0, requestId.length() - largerInt);
            merchantTxnRef = requestId + "_R_" + rechargeData.getMerchant_id();
        }
        vpc.put("vpc_MerchTxnRef", merchantTxnRef);
        vpc.put("vpc_OrgMerchTxnRef", rechargeData.getTransaction_reference());
        vpc.put("vpc_SecureHash", OneCreditUtil.createMerchantHash(vpc, merchantData.getHashCode(), "2"));
        req.put("vpc", vpc);
        LOGGER.log(Level.INFO, "RECHARGE REQUEST = " + req);

        return req;
    }

    /**
     * refund transaction
     *
     * @param rc
     * @param body
     * @param clientReadOnly
     * @param clientOnline
     * @param clientBackUp
     * @param transactionId
     * @param xRequestId
     * @param userId
     */

    private static String SAMSUNG_MERCHANT_APPROVALS = PropsUtil.get("samsung.merchant.approvals", "");

    private void refund(RoutingContext rc, String body, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
            String transactionId, String xRequestId, String userId, HttpClient httpClient, Boolean skipCallSynchronize) {
        PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());

        if (refundData.getValue().getRefund_reference() == null && xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID OR REFUND REFERENCE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        // String refundRef =
        if (refundData.getValue().getRefund_reference() == null || refundData.getValue().getRefund_reference().isEmpty()) {
            // Init refund reference
            long currentTime = System.currentTimeMillis();
            String merchantTxnRef = refundData.getValue().getMerchant_id() + "_" + currentTime;
            // String requestId = xRequestId;
            if (merchantTxnRef.length() > 32) {
                int largerInt = merchantTxnRef.length() - 32 + 3;
                merchantTxnRef = merchantTxnRef.substring(0, merchantTxnRef.length() - largerInt);
            }
            refundData.getValue().setRefund_reference(merchantTxnRef);
        }

        // check if refund_reference existed
        Observable<Object> checkFlag = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.internationalTransactionService.getHistoryByMerchantRef(connOnline, transactionId, refundData.getValue().getRefund_reference(), refundData.getValue().getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                    return this.internationalRefundService.getApprovalByRef(connBackUp, Integer.valueOf(transactionId), refundData.getValue().getRefund_reference(), refundData.getValue().getMerchant_id()).map(refundApproval -> {

                                        if (internationalTransactionHistory != null) {
                                            if (refundData.getValue().getAmount() != internationalTransactionHistory.getAmount().getTotal()) {
                                                throw IErrors.REFUND_REJECTED;
                                            }
                                            return internationalTransactionHistory;
                                        } else if (refundApproval != null) {
                                            return refundApproval;
                                        }
                                        return null;
                                    });
                                });
                            });
                });
        // log ban ghi 14 tranh refund 2 lan cung luc
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    long startTime = System.currentTimeMillis();
                    long delay = 2000;
                    return lock(connBackUp, transactionId, 120000, startTime, delay);
                }).subscribe(isLock -> {
                    // Duongpxt authorize captrue: if refund type = 3 => refund capture
                    if(isLock == true) {
                        int refundType = Objects.isNull(refundData.getValue().getType()) ? 0 : refundData.getValue().getType();
                        if (refundType == 3) {
                            // refund capture
                            LOGGER.info("===================Refund Capture===================");
                            RefundCaptureService refundCaptureService = new RefundCaptureService(merchantApproval, refundApprovalService, userService, refundConfig, internationalTransactionService, internationalRefundService, merchantService);
                            refundCaptureService.refundCapture(rc, checkFlag, clientReadOnly, clientOnline, clientBackUp, transactionId, refundData, userId, skipCallSynchronize);

                        } else {
                            // refund purchase
                            LOGGER.info("===================Refund Purchase===================");
                            refundPurchase(rc, checkFlag, clientReadOnly, clientOnline, clientBackUp, transactionId, refundData, userId, httpClient, skipCallSynchronize);
                        }
                    } else {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND LOCKED => REFUND IN PROCESS ");
                        throw IErrors.REFUND_IN_PROCESS;
                    }
                }, throwable -> {
                    rc.fail(throwable);
                });
    }

    private Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> getConfirmStep(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.internationalTransactionService.get(connOnline, transactionId).flatMap(interTransaction -> {
                                    return this.internationalTransactionService.getContarctTypeMsp(connOnline, transactionId).flatMap(contractType -> {
                                        return this.merchantPermitService.getMerchantRefundPermit(connBackUp, interTransaction.getMerchant_id(), RefundData.Type.INTERNATIONAL.getValue()).map(merchantPermit -> {
                                            RefundData.NUMBER_OF_STEP_CONFIRMATION result = RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;

                                            if (merchantPermit != null && (merchantPermit.getDoubleConfirmation() == 1 || merchantPermit.getDoubleConfirmation() == 0)) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE;
                                            }
                                            // check isBigmerchant
                                            if (Utils.isBigMerchant(interTransaction.getMerchant_id())) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            }
                                            if (contractType != null && contractType.equalsIgnoreCase("2B")) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }
                                            if (contractType != null && contractType.equalsIgnoreCase("3B")) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            }
                                            int acquirerId = interTransaction.getAcquirer().getAcquirer_id();
                                            if (8 == acquirerId) {
                                                // hotfix special case for samsung
                                                List<String> samsungMerchantApprovals = Arrays.asList(SAMSUNG_MERCHANT_APPROVALS.split(","));
                                                if (samsungMerchantApprovals.contains(interTransaction.getMerchant_id())) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                } else {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                            }
                                            if (11 == acquirerId || 7 == acquirerId || 9 == acquirerId || 3 == acquirerId) {
                                                // acq =Sacombank. vpb onepay phai duyet
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            if (interTransaction.getMerchant_id().startsWith("OP_") && !interTransaction.getMerchant_id().equals("OP_PREPAID")) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }
                                            if (merchantPermit != null && merchantPermit.getDoubleConfirmation() == 2) {
                                                // case need onepay approve
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            return result;
                                        });
                                    });
                                });
                            });
                });
    }

    private Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> getMerchantApproval(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.internationalTransactionService.get(connOnline, transactionId).flatMap(interTransaction -> {
                                    return this.internationalTransactionService.getContarctTypeMsp(connOnline, transactionId).flatMap(contractType -> {
                                        return this.merchantApproval.getMerchantApproval(connBackUp, interTransaction.getMerchant_id()).map(merchantId -> {
                                            RefundData.NUMBER_OF_STEP_CONFIRMATION result = RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            // check isBigmerchant
                                            if (Utils.isBigMerchant(interTransaction.getMerchant_id())) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            }
                                            if (null != contractType && "3B".equalsIgnoreCase(contractType)) {
                                                if (null != merchantId && !merchantId.isEmpty()) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE;
                                                } else  {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                            } else if (null != contractType && contractType.equalsIgnoreCase("2B")) {
                                                if (null != merchantId && !merchantId.isEmpty()) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE;
                                                } else  {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                }
                                            }

                                            int acquirerId = interTransaction.getAcquirer().getAcquirer_id();
                                            if (8 == acquirerId) {
                                                // hotfix special case for samsung
                                                List<String> samsungMerchantApprovals = Arrays.asList(SAMSUNG_MERCHANT_APPROVALS.split(","));
                                                if (samsungMerchantApprovals.contains(interTransaction.getMerchant_id())) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                } else {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                            } else if (11 == acquirerId || 7 == acquirerId || 9 == acquirerId || 3 == acquirerId) {
                                                // acq =Sacombank. vpb onepay phai duyet
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            if (interTransaction.getMerchant_id().startsWith("OP_") && !interTransaction.getMerchant_id().equals("OP_PREPAID")) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            return result;
                                        });
                                    });
                                });
                            });
                });
    }

    private void refundPurchase(RoutingContext rc, Observable<Object> checkFlag, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, PatchRequest<RefundData> refundData,
            String userId, HttpClient httpClient, Boolean skipCallSynchronize) {

        checkFlag.subscribe(internationalRefund -> {
            if (internationalRefund != null) {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalRefund);
                rc.next();

            } else {
                Observable<Double> obsAmountRefund = getAmountRefund(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);
                // begin refund data
                obsAmountRefund.subscribe(amount -> {
                    Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> flagInsert = getMerchantApproval(clientOnline, clientBackUp, transactionId);
                    flagInsert.subscribe(flag -> {
                        /**
                         * flag = 0: duyet luon
                         * flag = 1: duyet lan 1 boi merchant admin
                         * flag = 2: duyet lan 2 onepay admin
                         */
                        if (flag == RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE) {
                            LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                            return this.refundApprovalService.insertRequest3(connBackUp, userId, refundData.getValue().getMerchant_id(),
                                                    transactionId, refundData.getValue().getAmount(), refundData.getValue().getCurrency(),
                                                    refundData.getValue().getRefund_reference(), RefundData.Type.INTERNATIONAL.getValue(), refundData.getValue().getNote()).flatMap(approval -> {
                                                        return Observable.just(approval);
                                                    });
                                        });
                                    }).subscribe(stringObjectMap -> {
                                        // call ep dong bo international

                                        if (skipCallSynchronize == false) {
                                            try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                            } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                            }
                                        }
                                        unlock(transactionId, clientBackUp);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                        } else if (flag == RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO) {

                            LOGGER.log(Level.INFO, "****** INSERT REFUND ONEPAY APPROVAL *****");
                            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .flatMap(connOnline -> {
                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                            return this.refundApprovalService.insertRequestOnepay3(connBackUp, userId, refundData.getValue().getMerchant_id(),
                                                    transactionId, refundData.getValue().getAmount(),
                                                    refundData.getValue().getCurrency(), null, refundData.getValue().getRefund_reference(),
                                                    RefundApproval.Status.REQUEST_ONEPAY.code,
                                                    RefundData.Type.INTERNATIONAL.getValue(), refundData.getValue().getNote()).flatMap(res -> {
                                                        return InstallmentService.rejectInstallment(connOnline, transactionId).flatMap(res2 -> {
                                                            return Observable.just(res);
                                                    });
                                                    });
                                        });
                                        });
                                    }).subscribe(stringObjectMap -> {
                                        // call ep dong bo international
                                        if (skipCallSynchronize == false) {
                                            try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                            } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                            }
                                        }
                                        unlock(transactionId, clientBackUp);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                        } else {
                            Observable<Map> merchantObs = getMerchantObs(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData).flatMap(merchantData -> {

                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .flatMap(connOnline -> {
                                            if (merchantData == null) {
                                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => INVALID MERCHANT ");
                                                throw IErrors.VALIDATION_ERROR;
                                            }

                                            return this.internationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                                                return this.generalReportService.getPurchaseByRef(connOnline, internationalTransaction.getMerchant_id(), internationalTransaction.getOcMerTxnRef()).map(s -> {
                                                    Map m = new HashMap<>();
                                                    m.put("transaction", internationalTransaction);
                                                    m.put("merchant", merchantData);
                                                    m.put("ref", s);
                                                    return m;
                                                });

                                            });
                                        });
                            });

                            merchantObs.subscribe(map -> {
                                InternationalTransaction i = (InternationalTransaction) map.get("transaction");
                                MerchantData m = (MerchantData) map.get("merchant");
                                if (map.get("ref") == null) {
                                    refundOnecredit(i, rc, clientReadOnly, clientOnline, clientBackUp, refundData, userId, httpClient, m);
                                } else {

                                    refundMsp(i, rc, clientReadOnly, clientOnline, clientBackUp, refundData, userId, httpClient, m);
                                }
                                // call ep dong bo international
                                if (skipCallSynchronize == false) {
                                    try {
                                        OneSchedClient.synchronizeInterRefundViewSync();
                                    } catch (Exception e) {
                                        LOGGER.log(Level.WARNING, "international call ep dong bo error " + e.getMessage());
                                    }
                                }
                                unlock(transactionId, clientBackUp);
                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        }
                    });
                }, throwable -> {
                    rc.fail(throwable);
                });
            }
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    private void reverseDueToDisputeProcess(RoutingContext rc, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, PatchRequest<RefundData> refundData,
            String userId, HttpClient httpClient, Boolean skipCallSynchronize) {
        Observable<Map> merchantObs = getMerchantObs(transactionId, clientReadOnly, clientOnline,
                clientBackUp, refundData).flatMap(merchantData -> {

                    return Observable
                            .using(SQLConnectionFactory::new, f -> f.create(clientOnline),
                                    f -> f.dispose())
                            .flatMap(connOnline -> {
                                if (merchantData == null) {
                                    LOGGER.log(Level.SEVERE,
                                            "[ INTERNATIONAL REVERTSE DISPUTE TRANSACTION PATCH] => INVALID MERCHANT ");
                                    throw IErrors.VALIDATION_ERROR;
                                }

                                return this.internationalTransactionService
                                        .get(connOnline, transactionId)
                                        .flatMap(internationalTransaction -> {
                                            return this.generalReportService.getPurchaseByRef(
                                                    connOnline,
                                                    internationalTransaction.getMerchant_id(),
                                                    internationalTransaction.getOcMerTxnRef())
                                                    .map(s -> {
                                                        Map m = new HashMap<>();
                                                        m.put("transaction",
                                                                internationalTransaction);
                                                        m.put("merchant", merchantData);
                                                        m.put("ref", s);
                                                        return m;
                                                    });

                                        });
                            });
                });

        merchantObs.subscribe(map -> {
            InternationalTransaction i = (InternationalTransaction) map.get("transaction");
            MerchantData m = (MerchantData) map.get("merchant");

            reverseDueMsp(i, rc, clientReadOnly, clientOnline, clientBackUp, refundData, userId, httpClient, m);

            // call ep dong bo international
            if (skipCallSynchronize == false) {
                try {
                    OneSchedClient.synchronizeInterRefundViewSync();
                } catch (Exception e) {
                    LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                }
            }
            unlock(transactionId, clientBackUp);
        }, throwable -> {
            rc.fail(throwable);
        });

    }
    
    private void reverseDueToDispute2(RoutingContext rc, String body, JDBCClient clientReadOnly, JDBCClient clientOnline,
            JDBCClient clientBackUp,
            String transactionId, String xRequestId, String userId, HttpClient httpClient,
            Boolean skipCallSynchronize) {
        PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {
        }.getType());

        if (refundData.getValue().getRefund_reference() == null && xRequestId == null) {
            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID OR REFUND REFERENCE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        // String refundRef =
        if (refundData.getValue().getRefund_reference() == null
                || refundData.getValue().getRefund_reference().isEmpty()) {
            // Init refund reference
            long currentTime = System.currentTimeMillis();
            String merchantTxnRef = refundData.getValue().getMerchant_id() + "_" + currentTime;
            // String requestId = xRequestId;
            if (merchantTxnRef.length() > 32) {
                int largerInt = merchantTxnRef.length() - 32 + 3;
                merchantTxnRef = merchantTxnRef.substring(0, merchantTxnRef.length() - largerInt);
            }
            refundData.getValue().setRefund_reference(merchantTxnRef);
        }

        // check if refund_reference existed
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                                return this.internationalTransactionService
                                        .getHistoryByMerchantRef(connOnline, transactionId,
                                                refundData.getValue().getRefund_reference(),
                                                refundData.getValue().getMerchant_id())
                                        .flatMap(internationalTransactionHistory -> {
                                            return this.internationalRefundService
                                                    .getApprovalByRef(connBackUp, Integer.valueOf(transactionId),
                                                            refundData.getValue().getRefund_reference(),
                                                            refundData.getValue().getMerchant_id())
                                                    .map(refundApproval -> {

                                                        if (internationalTransactionHistory != null) {
                                                            if (refundData.getValue()
                                                                    .getAmount() != internationalTransactionHistory
                                                                            .getAmount().getTotal()) {
                                                                throw IErrors.REFUND_REJECTED;
                                                            }
                                                            return internationalTransactionHistory;
                                                        } else if (refundApproval != null) {
                                                            return refundApproval;
                                                        }
                                                        return null;
                                                    });
                                        });
                        });
                });
        // log ban ghi 14 tranh refund 2 lan cung luc
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    long startTime = System.currentTimeMillis();
                    long delay = 2000;
                    // return lock(connBackUp, transactionId, 120000, startTime, delay, 0);
                    return lock(connBackUp, transactionId, 120000, startTime, delay);
                }).subscribe(isLock -> {
                    if (isLock == true) {
                        reverseDueToDisputeProcess(rc, clientReadOnly, clientOnline, clientBackUp, transactionId, refundData,
                                userId, httpClient, skipCallSynchronize);
                    } else {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND LOCKED => REFUND IN PROCESS ");
                        throw IErrors.REFUND_IN_PROCESS;
                    }
                }, throwable -> {
                    rc.fail(throwable);
                });
    }

    private void refundOnecredit(InternationalTransaction internationalTransaction, RoutingContext rc, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData,
            String userId, HttpClient httpClient, MerchantData merchantData) {
        String vpcCommand = "refund";
        /* neu la GD cua Vietcombank + nguyen mon
            + Giao dich tien USD: >= 5.000 USD
            + Giao dich tien VND: >= 100.000.000 VND
            --> vpcCommand là refundWithHold
        */
        if (internationalTransaction != null && internationalTransaction.getAcquirer().getAcquirer_id() == 1 &&
                internationalTransaction.getAmount().getTotal() == refundData.getValue().getAmount() &&
                (("USD".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getValue().getAmount() >= 5000) ||
                        ("VND".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getValue().getAmount() >= *********))) {
            vpcCommand = "refundWithHold";
        }
        Map data = OneCreditUtil.oneCreditData("REFUND", vpcCommand, refundData.getValue().getMerchant_id(), merchantData.getAccessCode(), refundData.getValue().getRefund_reference(), internationalTransaction.getOcMerTxnRef(), refundData.getValue().getAmount(), userId, merchantData.getHashCode(), "2", refundData.getValue().getNote());


        Date dt = new Date();
        data.put("timeout", String.valueOf(refundConfig.getTimeout()));
        data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
        JsonObject jsonObject = new JsonObject(data);

        HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());
        clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
        clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
        clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jsonObject.toString().getBytes(UTF_8), refundConfig.getSecureCode()));
        Buffer buffer = Buffer.buffer(jsonObject.toString());
        // LOGGER.log(Level.INFO, "CLIENT REFUND RREQUEST DATA" + StringPool.SPACE + StringPool.COLON + jsonObject.toString());
        // LOGGER.log(Level.INFO, "MERCHANT DATA: " + StringPool.SPACE + StringPool.COLON + merchantData.getHashCode() + " / " + merchantData.getAccessCode());
        clientRequestToObs(rc, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp, clientRequest, refundData);
        clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
        clientRequest.end(buffer);

    }

    private void refundMsp(InternationalTransaction internationalTransaction, RoutingContext rc, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData,
            String userId, HttpClient httpClient, MerchantData merchantData) {
        try {

            JsonObject mapVpc = MSPClient.refundPurchaseQT(internationalTransaction.getMerchant_id(), internationalTransaction.getTransaction_reference(),
                    refundData.getValue().getRefund_reference(), refundData.getValue().getAmount(), merchantData.getAccessCode(), merchantData.getHashCode(), userId, refundData.getValue().getNote());
            clientRequestToObs(rc, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp, mapVpc, refundData);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "===================================== END VOID PURCHASE ==================================");
            rc.fail(ex);
        }
    }

    private void reverseDueMsp(InternationalTransaction internationalTransaction, RoutingContext rc,
            JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData,
            String userId, HttpClient httpClient, MerchantData merchantData) {
        try {
            JsonObject mapVpc = MSPClient.reverseDueQT(internationalTransaction.getMerchant_id(),
                    internationalTransaction.getTransaction_reference(),
                    refundData.getValue().getRefund_reference(), refundData.getValue().getAmount(),
                    merchantData.getAccessCode(), merchantData.getHashCode(), userId, refundData.getValue().getNote());
            clientRequestToObs(rc, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp,
                    mapVpc, refundData);
        } catch (Exception ex) {
            LOGGER.log(Level.INFO,
                    "===================================== END VOID PURCHASE ==================================");
            rc.fail(ex);
        }
    }

    /**
     * recharge
     *
     * @param rc
     * @param body
     * @param clientOnline
     * @param xRequestId
     * @param ipAddress
     */
    private void recharge(RoutingContext rc, String body, JDBCClient clientOnline, String xRequestId, String ipAddress, Boolean skipCallSynchronize) {
        // LOGGER.log(Level.INFO, "===================================== BEGIN RECHARGE
        // ==================================");
        PatchRequest<RechargeData> rechargeData = gson.fromJson(body, new TypeToken<PatchRequest<RechargeData>>() {}.getType());
        Observable<MerchantData> merchantObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return merchantService.getData(connOnline, rechargeData.getValue().getMerchant_id());
        });

        merchantObs.subscribe(merchantData -> {
            String requestId = xRequestId;
            Map requestParam = rechargeData(rechargeData.getValue(), merchantData, ipAddress, requestId);
            Date dt = new Date();
            requestParam.put("timeout", String.valueOf(refundConfig.getTimeout()));
            requestParam.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
            JsonObject jsonObject = new JsonObject(requestParam);
            final HttpClient httpClient = rc.vertx().createHttpClient();
            LOGGER.log(Level.INFO, "CLIENT RECHARGE REQUEST DATA");
            HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());

            clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
            clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
            clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jsonObject.toString().getBytes(StandardCharsets.UTF_8), refundConfig.getSecureCode()));
            Buffer buffer = Buffer.buffer(jsonObject.toString());
            clientRequest.toObservable().subscribe(response -> {
                int statusCode = response.statusCode();
                if (statusCode == HttpResponseStatus.OK.code()) {
                    response.bodyHandler(buffer1 -> {
                        String json = buffer1.toString("UTF-8");
                        // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                        // =====================================");
                        // LOGGER.log(Level.INFO, json);
                        // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                        // =====================================");
                        Map mapVpc = gson.fromJson(json, Map.class);

                        LOGGER.log(Level.INFO, "CLIENT RECHARGE RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(mapVpc));
                        if (mapVpc != null && mapVpc.get("command_status").equals("REDIRECT")) {
                            Map dataResp = (Map) mapVpc.get("redirect_form");
                            if (dataResp != null) {
                                Map redirectFormMap = (Map) dataResp.get("fields");
                                if ("0".equals(redirectFormMap.get("vpc_TxnResponseCode") + "")) {
                                    // call ep dong bo international
                                    if (skipCallSynchronize == false) {
                                        try {
                                            OneSchedClient.synchronizeInterPurchaseViewSync();
                                        } catch (Exception e) {
                                            LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                                        }
                                    }
                                    rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
                                    rc.next();
                                } else {
                                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL RECHARGE TRANSACTION PATCH] => RECHARGE FAILED " + Utils.mask(mapVpc));
                                    throw IErrors.RECHARGE_FAILED;
                                }
                            } else {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL RECHARGE TRANSACTION PATCH] => RECHARGE FAILED " + Utils.mask(mapVpc));
                                throw IErrors.RECHARGE_FAILED;
                            }


                        } else {
                            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL RECHARGE TRANSACTION PATCH] => RECHARGE FAILED " + Utils.mask(mapVpc));
                            throw IErrors.RECHARGE_FAILED;
                        }

                    });

                    response.exceptionHandler(throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL RECHARGE TRANSACTION PATCH] => RECHARGE FAILED " +  Utils.mask(response));
                    throw IErrors.RECHARGE_FAILED;
                }
            }, throwable -> {
                rc.fail(throwable);
            });
            clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
            clientRequest.end(buffer);
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private void voidPurchaseMsp(RoutingContext rc, Map map, String xRequestId, String userId, JDBCClient clientOnline, JDBCClient clientBackUp, RefundData refundData ) {
        // Observable<Map> transactionObs = null;
        // transactionObs = getTransactionPurchase(transactionId, clientOnline);
        // transactionObs.subscribe(map -> {
        InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
        String txnID = "" + internationalTransaction.getTransaction_id();
        MerchantData merchantData = (MerchantData) map.get("merchant");
        

        if (refundData.getTransaction_reference() == null || refundData.getTransaction_reference().isEmpty()) {
                // Init refund reference
                String requestId = xRequestId;
                String voidReference = "VOID_P_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                if (voidReference.length() > 32) {
                    int largerInt = voidReference.length() - 32 + 1;
                    requestId = requestId.substring(0, requestId.length() - largerInt);
                    voidReference = "VOID_P_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                }
                refundData.setTransaction_reference(voidReference);
            }
        try {

            JsonObject mapVpc = MSPClient.voidPurchase(internationalTransaction.getMerchant_id(), internationalTransaction.getTransaction_reference(),
                refundData.getTransaction_reference(), internationalTransaction.getAmount().getTotal(), merchantData.getAccessCode(), merchantData.getHashCode(), userId, "");
            if (mapVpc != null) {
                if ("0".equals(mapVpc.getString("vpc_TxnResponseCode") + "")) {
                    Observable<InternationalTransactionHistory> historyObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {

                            return InstallmentService.rejectInstallment(connOnline, txnID).flatMap(aVoid -> {
                                return internationalTransactionService.getHistoryByMerchantRef(connOnline, txnID, mapVpc.getString("vpc_MerchTxnRef").toString(), internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                    return userService.get(connBackUp, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                        if (userData != null) {
                                            internationalTransactionHistory.setOperator_id(userData.getEmail());
                                        }
                                        // call ep dong bo international
                                        try {
                                            OneSchedClient.synchronizeInterVoidViewSync();
                                        } catch (Exception e) {
                                            LOGGER.log(Level.INFO, "international call ep dong bo void error "
                                                    + e.getMessage());
                                        }
                                        return internationalTransactionHistory;
                                    });
                                });
                            });
                        });
                    });

                    historyObs.subscribe(internationalTransactionHistory -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });

                } else {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE TRANSACTION PATCH] => VOID PURCHASE FAILED " + mapVpc.toString());
                    throw IErrors.VOID_PURCHASE_FAILED;
                }

            } else {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE TRANSACTION PATCH] => VOID PURCHASE FAILED " + mapVpc.toString());
                throw IErrors.VOID_PURCHASE_FAILED;
            }
        } catch (Exception ex) {
            LOGGER.log(Level.INFO, "===================================== END VOID PURCHASE ==================================");
            rc.fail(ex);
        }
        // }, throwable -> {
        // LOGGER.log(Level.INFO, "===================================== END VOID PURCHASE
        // ==================================");
        // rc.fail(throwable);
        // });

    }

    private void voidPurchase(RoutingContext rc, String transactionId, String xRequestId, String userId, JDBCClient clientOnline, JDBCClient clientBackUp,PatchRequest<RefundData> refundData) {

        // PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
        Observable<Map> transactionObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return getTransactionPurchase(transactionId, clientOnline).flatMap(mapTransaction -> {
                InternationalTransaction purchase = (InternationalTransaction) mapTransaction.get("transaction");
                return generalReportService.getPurchaseByRef(connOnline, purchase.getMerchant_id(), purchase.getTransaction_reference()).flatMap(a -> {
                    mapTransaction.put("msp_trans_id", a);
                    return Observable.just(mapTransaction);
                });
            });
        });
        transactionObs.subscribe(m -> {
            if (m.get("msp_trans_id") == null) {
                voidPurchaseOnecredit(rc, m, xRequestId, userId, clientOnline, clientBackUp,refundData.getValue());
            } else {
                voidPurchaseMsp(rc, m, xRequestId, userId, clientOnline, clientBackUp,refundData.getValue());
            }

            // call ep dong bo international
            // try {
            //     OneSchedClient.synchronizeInterVoidViewSync();
            // } catch (Exception e) {
            //     LOGGER.log(Level.INFO, "international call ep dong bo void error " + e.getMessage());
            // }

        }, throwable -> {
            LOGGER.log(Level.WARNING, "===================================== END VOID PURCHASE ==================================");
            rc.fail(throwable);
        });

    }

    private void voidPurchaseOnecredit(RoutingContext rc, Map map, String xRequestId, String userId, JDBCClient clientOnline, JDBCClient clientBackUp, RefundData refundData) {
        // Observable<Map> transactionObs = null;
        // transactionObs = getTransactionPurchase(transactionId, clientOnline);
        // transactionObs.subscribe(map -> {
        InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
        String txnID = "" + internationalTransaction.getTransaction_id();
        MerchantData merchantData = (MerchantData) map.get("merchant");
        if (refundData.getTransaction_reference() == null || refundData.getTransaction_reference().isEmpty()) {
            // Init refund reference
            String requestId = xRequestId;
            String voidReference = "VOID_P_" + internationalTransaction.getMerchant_id() + "_" + requestId;
            if (voidReference.length() > 32) {
                int largerInt = voidReference.length() - 32 + 1;
                requestId = requestId.substring(0, requestId.length() - largerInt);
                voidReference = "VOID_P_" + internationalTransaction.getMerchant_id() + "_" + requestId;
            }
            refundData.setTransaction_reference(voidReference);
        }
        Map data = OneCreditUtil.oneCreditData("VOID_PURCHASE", "voidPurchase", internationalTransaction.getMerchant_id(),
                merchantData.getAccessCode(), refundData.getTransaction_reference(), internationalTransaction.getOcMerTxnRef(),
                internationalTransaction.getAmount().getTotal(), userId, merchantData.getHashCode(), "2", "");
        Date dt = new Date();
        data.put("timeout", String.valueOf(refundConfig.getTimeout()));
        data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
        JsonObject jsonObject = new JsonObject(data);
        final HttpClient httpClient = rc.vertx().createHttpClient();

        LOGGER.log(Level.INFO, "CLIENT VOID PURCHASE REQUEST DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(data));
        HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());

        clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
        clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
        clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jsonObject.toString().getBytes(StandardCharsets.UTF_8), refundConfig.getSecureCode()));
        Buffer buffer = Buffer.buffer(jsonObject.toString());

        clientRequest.toObservable().subscribe(response -> {
            int statusCode = response.statusCode();
            if (statusCode == HttpResponseStatus.OK.code()) {
                response.bodyHandler(buffer1 -> {
                    String json = buffer1.toString("UTF-8");
                    // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                    // =====================================");
                    // LOGGER.log(Level.INFO, json);
                    // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                    // =====================================");
                    Map mapVpc = gson.fromJson(json, Map.class);

                    LOGGER.log(Level.INFO, "CLIENT VOID PURCHASE RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(mapVpc));
                    if (mapVpc != null && mapVpc.get("vpc_response") != null) {
                        Map dataResp = (Map) mapVpc.get("vpc_response");
                        if ("0".equals(dataResp.get("vpc_TxnResponseCode") + "")) {
                            Observable<InternationalTransactionHistory> historyObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {

                                    return InstallmentService.rejectInstallment(connOnline, txnID).flatMap(aVoid -> {
                                        return internationalTransactionService.getHistoryByMerchantRef(connOnline, txnID, dataResp.get("vpc_MerchTxnRef").toString(), internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                            return userService.get(connBackUp, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                if (userData != null) {
                                                    internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                }
                                                // call ep dong bo international
                                                try {
                                                    OneSchedClient.synchronizeInterVoidViewSync();
                                                } catch (Exception e) {
                                                    LOGGER.log(Level.INFO, "international call ep dong bo void error "
                                                            + e.getMessage());
                                                }
                                                return internationalTransactionHistory;
                                            });
                                        });
                                    });
                                });
                            });

                            historyObs.subscribe(internationalTransactionHistory -> {
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                rc.next();
                            }, throwable -> {
                                rc.fail(throwable);
                            });

                        } else {
                            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE TRANSACTION PATCH] => VOID PURCHASE FAILED " + Utils.mask(mapVpc));
                            throw IErrors.VOID_PURCHASE_FAILED;
                        }

                    } else {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE TRANSACTION PATCH] => VOID PURCHASE FAILED " + Utils.mask(mapVpc));
                        throw IErrors.VOID_PURCHASE_FAILED;
                    }

                });

                response.exceptionHandler(throwable -> {
                    rc.fail(throwable);
                });
            } else {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE TRANSACTION PATCH] => VOID PURCHASE FAILED " + Utils.mask(response));
                throw IErrors.VOID_PURCHASE_FAILED;
            }
        }, throwable -> {
            rc.fail(throwable);
        });
        clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
        clientRequest.end(buffer);
        // }, throwable -> {
        // LOGGER.log(Level.INFO, "===================================== END VOID PURCHASE
        // ==================================");
        // rc.fail(throwable);
        // });

    }

    /**
     * void Authorize
     *
     * @param rc
     * @param transactionId
     * @param xRequestId
     * @param userId
     * @param clientOnline3
     * @param clientBackUp
     */
    private void voidAuthorize(RoutingContext rc, String transactionId, String xRequestId, String userId, JDBCClient clientOnline, RefundData refundData) {
        LOGGER.log(Level.INFO, "===================================== VOID AUTHORIZE ==================================");

        Observable<Map> transactionObs = null;
        String txnRef = new JsonObject(rc.getBodyAsString()).getJsonObject("value").getString("transaction_reference");
        if ("MSP".equalsIgnoreCase(userId)) {
            transactionObs = getTransactionPurchaseByRef(txnRef, clientOnline);
        } else {
            transactionObs = getTransactionAuthorize(transactionId, clientOnline);
        }

        transactionObs.subscribe(map -> {
            InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
            MerchantData merchantData = (MerchantData) map.get("merchant");
            if (refundData.getTransaction_reference() == null || refundData.getTransaction_reference().isEmpty()) {
                // Init refund reference
                String requestId = xRequestId;
                String voidReference = "VOID_A_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                if (voidReference.length() > 32) {
                    int largerInt = voidReference.length() - 32 + 1;
                    requestId = requestId.substring(0, requestId.length() - largerInt);
                    voidReference = "VOID_A_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                }
                refundData.setTransaction_reference(voidReference);
            }

            Map<String, String> requestParam = new HashMap<>();

            requestParam.put("vpc_Command", "authorize_void");
            requestParam.put("vpc_Merchant", internationalTransaction.getMerchant_id());
            requestParam.put("vpc_AccessCode", merchantData.getAccessCode());
            requestParam.put("vpc_MerchTxnRef", refundData.getTransaction_reference());
            requestParam.put("vpc_OrgMerchTxnRef", internationalTransaction.getTransaction_reference());
            requestParam.put("vpc_Amount", Convert.toString(internationalTransaction.getAmount().getTotal() * 100, "0"));
            requestParam.put("vpc_Operator", userId);
            requestParam.put("vpc_Version", "2");
            String secureHash = OneCreditUtil.createMerchantHash(requestParam, merchantData.getHashCode(), "2");
            requestParam.put("vpc_SecureHash", secureHash);

            Map jsonReturn = new HashMap<>();
            try {
                jsonReturn = MSPClient.voidAPI(requestParam);
            } catch (Exception ex) {
                throw IErrors.VOID_MSP_ERROR;
            }
            if (jsonReturn == null || jsonReturn.size() == 0) {
                throw IErrors.CAPTURE_MSP_NO_RESPONSE;
            }

            // call ep dong bo international
            try {
                OneSchedClient.synchronizeInterVoidViewSync();
            } catch (Exception e) {
                LOGGER.log(Level.INFO, "international call ep dong bo void error " + e.getMessage());
            }

            String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();
            String responseTxnRef = jsonReturn.getOrDefault("vpc_MerchTxnRef", "").toString();

            // InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
            // internationalTransactionHistory.setStatus(responseCode.equals("0") ? "200" : responseCode);
            // InternationalTransactionHistory internationalTransactionHistory = 
            
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                return internationalTransactionService.getHistoryByMerchantRef(connOnline, internationalTransaction.getTransaction_id()+"", responseTxnRef, internationalTransaction.getMerchant_id());
            }).subscribe(internationalTransactionHistory -> {
                
                rc.put(ParamsPool.HANDLER_DATA_CODE, Objects.equals(responseCode, "0") ? "200" : "500");
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                rc.next();
            }, throwable -> {
                LOGGER.log(Level.WARNING, "===================================== END VOID AUTHORIZE ==================================");
                rc.fail(throwable);
            });

        }, throwable -> {
            LOGGER.log(Level.WARNING, "===================================== END VOID AUTHORIZE ==================================");
            rc.fail(throwable);
        });

    }

    /**
     * void Capture
     *
     * @param rc
     * @param transactionId
     * @param xRequestId
     * @param userId
     * @param clientOnline3
     * @param clientBackUp
     */
    private void voidCapture(RoutingContext rc, String transactionId, String xRequestId, String userId, JDBCClient clientOnline, RefundData refundData) {
        LOGGER.log(Level.INFO, "===================================== VOID CAPTURE ==================================");

        Observable<Map> transactionObs = null;
        String txnRef = new JsonObject(rc.getBodyAsString()).getJsonObject("value").getString("transaction_reference");
        if ("MSP".equalsIgnoreCase(userId)) {
            transactionObs = getTransactionPurchaseByRef(txnRef, clientOnline);
        } else {
            transactionObs = getTransactionPurchase(transactionId, clientOnline);
        }

        transactionObs.subscribe(map -> {
            InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
            MerchantData merchantData = (MerchantData) map.get("merchant");
            if (refundData.getTransaction_reference() == null || refundData.getTransaction_reference().isEmpty()) {
                // Init refund reference
                String requestId = xRequestId;
                String voidReference = "VOID_C_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                if (voidReference.length() > 32) {
                    int largerInt = voidReference.length() - 32 + 1;
                    requestId = requestId.substring(0, requestId.length() - largerInt);
                    voidReference = "VOID_C_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                }
                refundData.setTransaction_reference(voidReference);
            }

            Map<String, String> requestParam = new HashMap<>();

            requestParam.put("vpc_Command", "capture_void");
            requestParam.put("vpc_Merchant", internationalTransaction.getMerchant_id());
            requestParam.put("vpc_AccessCode", merchantData.getAccessCode());
            requestParam.put("vpc_MerchTxnRef", refundData.getTransaction_reference());
            requestParam.put("vpc_OrgMerchTxnRef", internationalTransaction.getTransaction_reference());
            requestParam.put("vpc_Amount", Convert.toString(internationalTransaction.getAmount().getTotal() * 100, "0"));
            requestParam.put("vpc_Operator", userId);
            requestParam.put("vpc_Version", "2");
            String secureHash = OneCreditUtil.createMerchantHash(requestParam, merchantData.getHashCode(), "2");
            requestParam.put("vpc_SecureHash", secureHash);

            Map jsonReturn = null;
            try {
                jsonReturn = MSPClient.voidAPI(requestParam);
            } catch (Exception ex) {
                throw IErrors.VOID_MSP_ERROR;
            }
            if (jsonReturn == null || jsonReturn.size() == 0) {
                throw IErrors.CAPTURE_MSP_NO_RESPONSE;
            }

            // call ep dong bo international
            try {
                OneSchedClient.synchronizeInterVoidViewSync();
            } catch (Exception e) {
                LOGGER.log(Level.INFO, "international call ep dong bo void error " + e.getMessage());
            }

            // String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();

            // InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
            // internationalTransactionHistory.setStatus(responseCode.equals("0") ? "200" : responseCode);
            

            String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();
            String responseTxnRef = jsonReturn.getOrDefault("vpc_MerchTxnRef", "").toString();

            // InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
            // internationalTransactionHistory.setStatus(responseCode.equals("0") ? "200" : responseCode);
            // InternationalTransactionHistory internationalTransactionHistory = 
            
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                return internationalTransactionService.getHistoryByMerchantRef(connOnline, internationalTransaction.getTransaction_id()+"", responseTxnRef, internationalTransaction.getMerchant_id());
            }).subscribe(internationalTransactionHistory -> {
                
                rc.put(ParamsPool.HANDLER_DATA_CODE, Objects.equals(responseCode, "0") ? "200" : "500");
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                rc.next();
            }, throwable -> {
                LOGGER.log(Level.WARNING, "===================================== END VOID AUTHORIZE ==================================");
                rc.fail(throwable);
            });

            // rc.put(ParamsPool.HANDLER_DATA_CODE, Objects.equals(responseCode, "0") ? "200" : "500");
            // rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
            // rc.next();
        }, throwable -> {
            LOGGER.log(Level.INFO, "===================================== END VOID CAPTURE ==================================");
            rc.fail(throwable);
        });
    }

    private void voidCaptureRefund(RoutingContext rc, String transactionId, String xRequestId, String userId, JDBCClient clientOnline, RefundData refundData) {
        LOGGER.log(Level.INFO, "===================================== VOID CAPTURE REFUND ==================================");

        Observable<Map> transactionObs = null;
        String txnRef = new JsonObject(rc.getBodyAsString()).getJsonObject("value").getString("transaction_reference");
        if ("MSP".equalsIgnoreCase(userId)) {
            transactionObs = getTransactionPurchaseByRef(txnRef, clientOnline);
        } else {
            transactionObs = getTransactionPurchase(transactionId, clientOnline);
        }

        transactionObs.subscribe(map -> {
            InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
            MerchantData merchantData = (MerchantData) map.get("merchant");
            if (refundData.getTransaction_reference() == null || refundData.getTransaction_reference().isEmpty()) {
                // Init refund reference
                String requestId = xRequestId;
                String voidReference = "VOID_RC_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                if (voidReference.length() > 32) {
                    int largerInt = voidReference.length() - 32 + 1;
                    requestId = requestId.substring(0, requestId.length() - largerInt);
                    voidReference = "VOID_RC_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                }
                refundData.setTransaction_reference(voidReference);
            }


            Map<String, String> requestParam = new HashMap<>();

            requestParam.put("vpc_Command", "capture_refund_void");
            requestParam.put("vpc_Merchant", internationalTransaction.getMerchant_id());
            requestParam.put("vpc_AccessCode", merchantData.getAccessCode());
            requestParam.put("vpc_MerchTxnRef", refundData.getTransaction_reference());
            requestParam.put("vpc_OrgMerchTxnRef", internationalTransaction.getTransaction_reference());
            requestParam.put("vpc_Amount", Convert.toString(internationalTransaction.getAmount().getTotal() * 100, "0"));
            requestParam.put("vpc_Operator", userId);
            requestParam.put("vpc_Version", "2");
            String secureHash = OneCreditUtil.createMerchantHash(requestParam, merchantData.getHashCode(), "2");
            requestParam.put("vpc_SecureHash", secureHash);

            Map jsonReturn = null;
            try {
                jsonReturn = MSPClient.voidAPI(requestParam);
            } catch (Exception ex) {
                throw IErrors.VOID_MSP_ERROR;
            }
            if (jsonReturn == null || jsonReturn.size() == 0) {
                throw IErrors.CAPTURE_MSP_NO_RESPONSE;
            }

            // call ep dong bo international
            try {
                OneSchedClient.synchronizeInterVoidViewSync();
            } catch (Exception e) {
                LOGGER.log(Level.INFO, "international call ep dong bo void error " + e.getMessage());
            }

            // String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();

            // InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
            // internationalTransactionHistory.setStatus(responseCode.equals("0") ? "200" : responseCode);

            // rc.put(ParamsPool.HANDLER_DATA_CODE, Objects.equals(responseCode, "0") ? "200" : "500");
            // rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
            // rc.next();

            

            String responseCode = jsonReturn.getOrDefault("vpc_TxnResponseCode", "").toString();
            String responseTxnRef = jsonReturn.getOrDefault("vpc_MerchTxnRef", "").toString();

            // InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
            // internationalTransactionHistory.setStatus(responseCode.equals("0") ? "200" : responseCode);
            // InternationalTransactionHistory internationalTransactionHistory = 
            LOGGER.info("Void refund capture trans id : " + internationalTransaction.getTransaction_id());
            LOGGER.info("Void refund capture responseTxnRef : " + responseTxnRef);
            LOGGER.info("Void refund capture merchant ID : " + internationalTransaction.getMerchant_id());
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                return internationalTransactionService.getHistoryByMerchantRef(connOnline, internationalTransaction.getTransaction_id()+"", responseTxnRef, internationalTransaction.getMerchant_id());
            }).subscribe(internationalTransactionHistory -> {
                rc.put(ParamsPool.HANDLER_DATA_CODE, Objects.equals(responseCode, "0") ? "200" : "500");
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                rc.next();
            }, throwable -> {
                LOGGER.log(Level.WARNING, "===================================== END VOID AUTHORIZE ==================================");
                rc.fail(throwable);
            });
        }, throwable -> {
            LOGGER.log(Level.INFO, "===================================== END VOID CAPTURE REFUND ==================================");
            rc.fail(throwable);
        });
    }

    private void refundOnePAYManual(RoutingContext rc, String body, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
            String transactionId, String xRequestId, String userId, HttpClient httpClient) {
        PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
        Observable<Double> obsAmountRefund = getAmountRefund(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);
        // begin refund data
        obsAmountRefund.subscribe(amount -> {
            Observable<MerchantData> merchantObs = getMerchantObs(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);

            merchantObs.subscribe(merchantData -> {
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp1 -> {
                            backUpCon = connBackUp1;
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                    .flatMap(connOnline -> {
                                        onlineCon = connOnline;
                                        // if (merchantData == null) {
                                        // LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND ONEPAY MANUAL TRANSACTION ] => INVALID MERCHANT
                                        // ");
                                        // throw IErrors.VALIDATION_ERROR;
                                        // }
                                        return onlineCon.setAutoCommitObservable(false).flatMap(aVoid -> {

                                            return this.internationalTransactionService.get(onlineCon, transactionId).flatMap(internationalTransaction -> {
                                                String requestId = xRequestId;
                                                String refundReference = refundData.getValue().getMerchant_id() + "_" + requestId;
                                                if (refundReference.length() > 32) {
                                                    int largerInt = refundReference.length() - 32 + 1;
                                                    requestId = requestId.substring(0, requestId.length() - largerInt);
                                                    refundReference = refundData.getValue().getMerchant_id() + "_" + requestId;
                                                }
                                                final String transRef = refundReference;
                                                return this.internationalTransactionService.insertOnePAYManual(onlineCon, Integer.parseInt(transactionId), amount, refundReference, internationalTransaction.getMerchant_id()).flatMap(integer -> {

                                                    return internationalTransactionService.getHistoryByMerchantRef(onlineCon, transactionId, transRef, internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                        return userService.get(backUpCon, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                            if (userData != null) {
                                                                internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                            }
                                                            return internationalTransactionHistory;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                                    });
                        }).subscribe(internationalTransactionHistory -> {
                            if (onlineCon != null) {
                                onlineCon.commitObservable();
                            }
                            if (backUpCon != null) {
                                backUpCon.commitObservable();
                            }

                            // call ep dong bo international
                            try {
                                OneSchedClient.synchronizeInterRefundViewSync();
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                            }

                            rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                            rc.next();
                        }, throwable -> {
                            if (onlineCon != null) {
                                onlineCon.rollbackObservable();
                            }
                            if (backUpCon != null) {
                                backUpCon.rollbackObservable();
                            }
                            rc.fail(throwable);
                        });

            }, throwable -> {
                rc.fail(throwable);
            });
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private void reverseDueToDispute(RoutingContext rc, String body, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
            String transactionId, String xRequestId, String userId, HttpClient httpClient) {
        PatchRequest<RefundData> refundData = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
        Observable<Double> obsAmountRefund = getAmountRefund(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);
        // begin refund data
        obsAmountRefund.subscribe(amount -> {
            Observable<MerchantData> merchantObs = getMerchantObs(transactionId, clientReadOnly, clientOnline, clientBackUp, refundData);

            merchantObs.subscribe(merchantData -> {
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp1 -> {
                            backUpCon = connBackUp1;
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                    .flatMap(connOnline -> {
                                        onlineCon = connOnline;
                                        // if (merchantData == null) {
                                        // LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND ONEPAY MANUAL TRANSACTION ] => INVALID MERCHANT
                                        // ");
                                        // throw IErrors.VALIDATION_ERROR;
                                        // }
                                        return onlineCon.setAutoCommitObservable(false).flatMap(aVoid -> {

                                            return this.internationalTransactionService.get(onlineCon, transactionId).flatMap(internationalTransaction -> {
                                                String requestId = xRequestId;
                                                String refundReference = refundData.getValue().getMerchant_id() + "_" + requestId;
                                                if (refundReference.length() > 32) {
                                                    int largerInt = refundReference.length() - 32 + 1;
                                                    requestId = requestId.substring(0, requestId.length() - largerInt);
                                                    refundReference = refundData.getValue().getMerchant_id() + "_" + requestId;
                                                }
                                                final String transRef = refundReference;
                                                return this.internationalTransactionService.insertReverseDue(onlineCon, Integer.parseInt(transactionId), amount, refundReference, internationalTransaction.getMerchant_id()).flatMap(integer -> {

                                                    return internationalTransactionService.getHistoryByMerchantRef(onlineCon, transactionId, transRef, internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                        return userService.get(backUpCon, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                            if (userData != null) {
                                                                internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                            }
                                                            return internationalTransactionHistory;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                                    });
                        }).subscribe(internationalTransactionHistory -> {
                            if (onlineCon != null) {
                                onlineCon.commitObservable();
                            }
                            if (backUpCon != null) {
                                backUpCon.commitObservable();
                            }

                            // call ep dong bo international
                            try {
                                OneSchedClient.synchronizeInterRefundViewSync();
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                            }

                            rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                            rc.next();
                        }, throwable -> {
                            if (onlineCon != null) {
                                onlineCon.rollbackObservable();
                            }
                            if (backUpCon != null) {
                                backUpCon.rollbackObservable();
                            }
                            rc.fail(throwable);
                        });

            }, throwable -> {
                rc.fail(throwable);
            });
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private Observable<Double> getAmountRefund(String transactionId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return internationalTransactionService.listHistory(connOnline, connBackup, transactionId).map(interTransHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = refundData.getValue().getAmount();
                                                for (InternationalTransactionHistory his : interTransHistories) {
                                                    // Case purchase ->
                                                    if ("Purchase".equalsIgnoreCase(his.getTransaction_type()) || ("Void Refund".equalsIgnoreCase(his.getTransaction_type()) && "Successful".equalsIgnoreCase(his.getAdvance_status()))) { // case purchase => remainAmt += purchase amount
                                                        LOGGER.info("QT his : " + his.getTransaction_type() + " / " + his.getAdvance_status() + " / " + his.getStatus());
                                                        remainAmt += his.getAmount().getTotal();
                                                        LOGGER.info("remainAmt : " + remainAmt);
                                                    } else if (his.getAdvance_status().equalsIgnoreCase("Successful") // case refund success => remainAmt += refund amount
                                                            || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content) // case request refund 405 || 401 => remainAmt += request refund amount
                                                                    && (Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST.code)
                                                                            || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST_ONEPAY.code)))) {
                                                        LOGGER.info("QT his : " + his.getTransaction_type() + " / " + his.getAdvance_status() + " / " + his.getStatus());
                                                        remainAmt -= his.getAmount().getTotal();
                                                        LOGGER.info("remainAmt : " + remainAmt);
                                                    }

                                                }
                                                String str = String.format("%1.2f", remainAmt);
                                                remainAmt = Double.valueOf(str);
                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                    return refundAmt;
                                                } else {
                                                    LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                    throw IErrors.AMOUNT_REFUND_ERROR;
                                                }
                                            });
                                        });
                            });
                });
    }


    private Observable<MerchantData> getMerchantObs(String transactionId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return internationalTransactionService.listHistory(connOnline, connBackup, transactionId).flatMap(interTransHistories -> {
                                        double remainAmt = 0;
                                        double refundAmt = refundData.getValue().getAmount();
                                        for (InternationalTransactionHistory his : interTransHistories) {
                                            if ("Purchase".equalsIgnoreCase(his.getTransaction_type()) || ("Void Refund".equalsIgnoreCase(his.getTransaction_type()) && "Successful".equalsIgnoreCase(his.getAdvance_status()))) {
                                                remainAmt = his.getAmount().getTotal();
                                            } else if (his.getAdvance_status().equalsIgnoreCase("Successful") // case refund success => remainAmt += refund amount
                                                    || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content) // case request refund 405 || 401 => remainAmt += request refund amount
                                                            && (Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST.code)
                                                                    || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST_ONEPAY.code)))) {
                                                remainAmt -= his.getAmount().getTotal();
                                            }
                                        }
                                        if (refundAmt <= remainAmt && remainAmt > 0) {
                                            return merchantService.getData(connOnline, refundData.getValue().getMerchant_id());
                                        } else {
                                            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REMAIN : " + remainAmt + " REFUND : " + refundAmt);
                                            throw IErrors.AMOUNT_REFUND_ERROR;
                                        }
                                    });
                                });
                    });
        });
    }

    /**
     * @param transactionId
     * @param clientOnline
     * @return
     */
    private Observable<Map> getTransactionPurchase(String transactionId, JDBCClient clientOnline) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return internationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                if (internationalTransaction == null) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION NOT FOUND");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                if (!internationalTransaction.isCan_void()) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE, CAN VOID: " + internationalTransaction.isCan_void());
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                }

                Calendar transactionTime = Calendar.getInstance();
                Timestamp date = internationalTransaction.getTransaction_time();
                transactionTime.setTime(date);
                // cal.set(Calendar.MILLISECOND, 0);
                // cal.set(Calendar.SECOND, 0);
                // cal.set(Calendar.MINUTE, 0);
                // int cutoff
                // switch (internationalTransaction.getAcquirer().getAcquirer_id()) {
                // case 1:
                // }
                Calendar vcbMigsCutOff = Calendar.getInstance();
                vcbMigsCutOff.setTime(new Date());
                vcbMigsCutOff.set(Calendar.MILLISECOND, 0);
                vcbMigsCutOff.set(Calendar.SECOND, 0);
                vcbMigsCutOff.set(Calendar.MINUTE, 0);
                vcbMigsCutOff.set(Calendar.HOUR_OF_DAY, 17);


                Calendar vcbCyberCutoff = Calendar.getInstance();
                vcbCyberCutoff.setTime(new Date());
                vcbCyberCutoff.set(Calendar.MILLISECOND, 0);
                vcbCyberCutoff.set(Calendar.SECOND, 0);
                vcbCyberCutoff.set(Calendar.MINUTE, 0);
                vcbCyberCutoff.set(Calendar.HOUR_OF_DAY, 13);

                Calendar vtbCyberCutoff = Calendar.getInstance();
                vtbCyberCutoff.setTime(new Date());
                vtbCyberCutoff.set(Calendar.MILLISECOND, 0);
                vtbCyberCutoff.set(Calendar.SECOND, 0);
                vtbCyberCutoff.set(Calendar.MINUTE, 0);
                vtbCyberCutoff.set(Calendar.HOUR_OF_DAY, 3);

                Calendar scbMpgsCutoff = Calendar.getInstance();
                scbMpgsCutoff.setTime(new Date());
                scbMpgsCutoff.set(Calendar.MILLISECOND, 0);
                scbMpgsCutoff.set(Calendar.SECOND, 0);
                scbMpgsCutoff.set(Calendar.MINUTE, 0);
                scbMpgsCutoff.set(Calendar.HOUR_OF_DAY, 19);

                Calendar bidvCyberCutoff = Calendar.getInstance();
                bidvCyberCutoff.setTime(new Date());
                bidvCyberCutoff.set(Calendar.MILLISECOND, 0);
                bidvCyberCutoff.set(Calendar.SECOND, 0);
                bidvCyberCutoff.set(Calendar.MINUTE, 0);
                bidvCyberCutoff.set(Calendar.HOUR_OF_DAY, 22);

                Calendar techombankCyberCutoff = Calendar.getInstance();
                techombankCyberCutoff.setTime(new Date());
                techombankCyberCutoff.set(Calendar.MILLISECOND, 0);
                techombankCyberCutoff.set(Calendar.SECOND, 0);
                techombankCyberCutoff.set(Calendar.MINUTE, 0);
                techombankCyberCutoff.set(Calendar.HOUR_OF_DAY, 15);

                Calendar vpbMpgsCutoff = Calendar.getInstance();
                vpbMpgsCutoff.setTime(new Date());
                vpbMpgsCutoff.set(Calendar.MILLISECOND, 0);
                vpbMpgsCutoff.set(Calendar.SECOND, 0);
                vpbMpgsCutoff.set(Calendar.MINUTE, 0);
                vpbMpgsCutoff.set(Calendar.HOUR_OF_DAY, 15);

                Calendar kBankCutOff = Calendar.getInstance();
                kBankCutOff.setTime(new Date());
                kBankCutOff.set(Calendar.MILLISECOND, 0);
                kBankCutOff.set(Calendar.SECOND, 0);
                kBankCutOff.set(Calendar.MINUTE, 0);
                kBankCutOff.set(Calendar.HOUR_OF_DAY, 17);

                Calendar vpbCyberCutOff = Calendar.getInstance();
                vpbCyberCutOff.setTime(new Date());
                vpbCyberCutOff.set(Calendar.MILLISECOND, 0);
                vpbCyberCutOff.set(Calendar.SECOND, 0);
                vpbCyberCutOff.set(Calendar.MINUTE, 0);
                vpbCyberCutOff.set(Calendar.HOUR_OF_DAY, 24);

                // Date d_to = null;
                // if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
                // cal.set(Calendar.HOUR_OF_DAY, 17);
                // } else {
                // cal.set(Calendar.HOUR_OF_DAY, 17);
                // cal.add(Calendar.DATE, 1);
                // }
                // d_to = cal.getTime();

                Date now = new Date();
                LOGGER.log(Level.INFO, "ACQUIRER: " + internationalTransaction.getAcquirer().getAcquirer_id() + " NOW: " + now + " TRANS: " + transactionTime.getTime());
                LOGGER.log(Level.INFO, "vcbMigsCutOff: " + vcbMigsCutOff.getTime());
                LOGGER.log(Level.INFO, "vcbCyberCutoff: " + vcbCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "vtbCyberCutoff: " + vtbCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "scbCyberCutoff: " + scbMpgsCutoff.getTime());
                LOGGER.log(Level.INFO, "bidvCyberCutoff: " + bidvCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "techombankCyberCutoff: " + techombankCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "vpbMpgsCutoff: " + vpbMpgsCutoff.getTime());
                LOGGER.log(Level.INFO, "kBankCutOff: " + kBankCutOff.getTime());
                LOGGER.log(Level.INFO, "vpbCyberCutOff: " + vpbCyberCutOff.getTime());
                // CASE Vietcombank – MiGS && Vietcombank – MPGS
                if ((internationalTransaction.getAcquirer().getAcquirer_id() == 1 || internationalTransaction.getAcquirer().getAcquirer_id() == 5)
                        && now.compareTo(vcbMigsCutOff.getTime()) >= 0
                        && transactionTime.getTime().compareTo(vcbMigsCutOff.getTime()) < 0) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE vcbMigsCutOff");
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                } else if (internationalTransaction.getAcquirer().getAcquirer_id() == 12 && now.compareTo(kBankCutOff.getTime()) >= 0
                        && transactionTime.getTime().compareTo(kBankCutOff.getTime()) < 0) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE kBankCutOff");
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                } else if (internationalTransaction.getAcquirer().getAcquirer_id() == 13 && now.compareTo(vpbCyberCutOff.getTime()) >= 0
                        && transactionTime.getTime().compareTo(vpbCyberCutOff.getTime()) < 0) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE vpbCyberCutOff");
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                } else {
                    Map returnMap = new HashMap();
                    returnMap.put("transaction", internationalTransaction);
                    return merchantService.getData(connOnline, internationalTransaction.getMerchant_id()).map(merchantData -> {
                        returnMap.put("merchant", merchantData);
                        return returnMap;
                    });
                }
            });
        });
    }

    /**
     * @param transactionId
     * @param clientOnline
     * @return
     */
    private Observable<Map> getTransactionAuthorize(String transactionId, JDBCClient clientOnline) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return internationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                if (internationalTransaction == null) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID AUTHORIZE PATCH] => TRANSACTION NOT FOUND");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                if (!internationalTransaction.isCan_void()) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID AUTHORIZE PATCH] => TRANSACTION OUT OF DATE, CAN VOID: " + internationalTransaction.isCan_void());
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                }
                if (internationalTransaction.getTransaction_time() == null) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID AUTHORIZE PATCH] => TRANSACTION TIME NOT FOUND");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }

                Calendar transactionTimeCutoff = Calendar.getInstance();
                Timestamp date = internationalTransaction.getTransaction_time();
                transactionTimeCutoff.setTime(date);
                transactionTimeCutoff.set(Calendar.MILLISECOND, 59);
                transactionTimeCutoff.set(Calendar.SECOND, 59);
                transactionTimeCutoff.set(Calendar.MINUTE, 59);
                transactionTimeCutoff.set(Calendar.HOUR_OF_DAY, 23);

                // Calendar transactionTimeCutoff = Calendar.getInstance();
                // transactionTimeCutoff.setTime(date);
                transactionTimeCutoff.add(Calendar.DATE, 30);

                Date now = new Date();
                LOGGER.log(Level.INFO, "now: " + now);
                LOGGER.log(Level.INFO, "transactionTime cut off: " + transactionTimeCutoff.getTime());

                if (now.compareTo(transactionTimeCutoff.getTime()) >= 0) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID AUTHORIZE PATCH] => TRANSACTION OUT OF DATE CUT OFF");
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                } else {
                    Map returnMap = new HashMap();
                    returnMap.put("transaction", internationalTransaction);
                    return merchantService.getData(connOnline, internationalTransaction.getMerchant_id()).map(merchantData -> {
                        returnMap.put("merchant", merchantData);
                        return returnMap;
                    });
                }
            });
        });
    }

    /**
     * get transaction by merchant tran ref
     * 
     * <AUTHOR>
     * @param transactionId
     * @param clientOnline
     * @return
     */
    private Observable<Map> getTransactionPurchaseByRef(String txnRef, JDBCClient clientOnline) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return internationalTransactionService.getByRef(connOnline, txnRef).flatMap(internationalTransaction -> {
                if (internationalTransaction == null) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION NOT FOUND");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                if (!internationalTransaction.isCan_void()) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE, CAN VOID: " + internationalTransaction.isCan_void());
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                }

                Calendar transactionTime = Calendar.getInstance();
                Timestamp date = internationalTransaction.getTransaction_time();
                transactionTime.setTime(date);
                // cal.set(Calendar.MILLISECOND, 0);
                // cal.set(Calendar.SECOND, 0);
                // cal.set(Calendar.MINUTE, 0);
                // int cutoff
                // switch (internationalTransaction.getAcquirer().getAcquirer_id()) {
                // case 1:
                // }
                Calendar vcbMigsCutOff = Calendar.getInstance();
                vcbMigsCutOff.setTime(new Date());
                vcbMigsCutOff.set(Calendar.MILLISECOND, 0);
                vcbMigsCutOff.set(Calendar.SECOND, 0);
                vcbMigsCutOff.set(Calendar.MINUTE, 0);
                vcbMigsCutOff.set(Calendar.HOUR_OF_DAY, 17);


                Calendar vcbCyberCutoff = Calendar.getInstance();
                vcbCyberCutoff.setTime(new Date());
                vcbCyberCutoff.set(Calendar.MILLISECOND, 0);
                vcbCyberCutoff.set(Calendar.SECOND, 0);
                vcbCyberCutoff.set(Calendar.MINUTE, 0);
                vcbCyberCutoff.set(Calendar.HOUR_OF_DAY, 13);

                Calendar vtbCyberCutoff = Calendar.getInstance();
                vtbCyberCutoff.setTime(new Date());
                vtbCyberCutoff.set(Calendar.MILLISECOND, 0);
                vtbCyberCutoff.set(Calendar.SECOND, 0);
                vtbCyberCutoff.set(Calendar.MINUTE, 0);
                vtbCyberCutoff.set(Calendar.HOUR_OF_DAY, 3);

                Calendar scbMpgsCutoff = Calendar.getInstance();
                scbMpgsCutoff.setTime(new Date());
                scbMpgsCutoff.set(Calendar.MILLISECOND, 0);
                scbMpgsCutoff.set(Calendar.SECOND, 0);
                scbMpgsCutoff.set(Calendar.MINUTE, 0);
                scbMpgsCutoff.set(Calendar.HOUR_OF_DAY, 19);

                Calendar bidvCyberCutoff = Calendar.getInstance();
                bidvCyberCutoff.setTime(new Date());
                bidvCyberCutoff.set(Calendar.MILLISECOND, 0);
                bidvCyberCutoff.set(Calendar.SECOND, 0);
                bidvCyberCutoff.set(Calendar.MINUTE, 0);
                bidvCyberCutoff.set(Calendar.HOUR_OF_DAY, 22);

                Calendar techombankCyberCutoff = Calendar.getInstance();
                techombankCyberCutoff.setTime(new Date());
                techombankCyberCutoff.set(Calendar.MILLISECOND, 0);
                techombankCyberCutoff.set(Calendar.SECOND, 0);
                techombankCyberCutoff.set(Calendar.MINUTE, 0);
                techombankCyberCutoff.set(Calendar.HOUR_OF_DAY, 15);

                Calendar vpbMpgsCutoff = Calendar.getInstance();
                vpbMpgsCutoff.setTime(new Date());
                vpbMpgsCutoff.set(Calendar.MILLISECOND, 0);
                vpbMpgsCutoff.set(Calendar.SECOND, 0);
                vpbMpgsCutoff.set(Calendar.MINUTE, 0);
                vpbMpgsCutoff.set(Calendar.HOUR_OF_DAY, 15);

                // Date d_to = null;
                // if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
                // cal.set(Calendar.HOUR_OF_DAY, 17);
                // } else {
                // cal.set(Calendar.HOUR_OF_DAY, 17);
                // cal.add(Calendar.DATE, 1);
                // }
                // d_to = cal.getTime();

                Date now = new Date();
                LOGGER.log(Level.INFO, "ACQUIRER: " + internationalTransaction.getAcquirer().getAcquirer_id() + " NOW: " + now + " TRANS: " + transactionTime.getTime());
                LOGGER.log(Level.INFO, "vcbMigsCutOff: " + vcbMigsCutOff.getTime());
                LOGGER.log(Level.INFO, "vcbCyberCutoff: " + vcbCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "vtbCyberCutoff: " + vtbCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "scbCyberCutoff: " + scbMpgsCutoff.getTime());
                LOGGER.log(Level.INFO, "bidvCyberCutoff: " + bidvCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "techombankCyberCutoff: " + techombankCyberCutoff.getTime());
                LOGGER.log(Level.INFO, "vpbMpgsCutoff: " + vpbMpgsCutoff.getTime());
                // CASE Vietcombank – MiGS && Vietcombank – MPGS
                if ((internationalTransaction.getAcquirer().getAcquirer_id() == 1 || internationalTransaction.getAcquirer().getAcquirer_id() == 5)
                        && now.compareTo(vcbMigsCutOff.getTime()) >= 0
                        && transactionTime.getTime().compareTo(vcbMigsCutOff.getTime()) < 0) {
                    LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID PURCHASE PATCH] => TRANSACTION OUT OF DATE vcbMigsCutOff");
                    throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                } else {
                    Map returnMap = new HashMap();
                    returnMap.put("transaction", internationalTransaction);
                    return merchantService.getData(connOnline, internationalTransaction.getMerchant_id()).map(merchantData -> {
                        returnMap.put("merchant", merchantData);
                        return returnMap;
                    });
                }
            });
        });
    }

    private void clientRequestToObs(RoutingContext rc, String transactionId, JDBCClient clientOnline, JDBCClient clientBackUp, JsonObject dataResp, PatchRequest<RefundData> refundData) {


        if (dataResp != null && dataResp.getString("vpc_TxnResponseCode") != null && "0".equals(dataResp.getString("vpc_TxnResponseCode") + "")) {
            Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp1 -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return InstallmentService.rejectInstallment(connOnline, transactionId).flatMap(aVoid -> {
                                        return internationalTransactionService.getHistoryByMerchantRef(connOnline, transactionId, dataResp.getString("vpc_MerchTxnRef").toString(), refundData.getValue().getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                            return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                if (userData != null) {
                                                    internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                }
                                                return internationalTransactionHistory;
                                            });

                                        });

                                    });
                                });
                    });

            returnObs.subscribe(internationalTransactionHistory -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                rc.next();
            }, rc::fail);
        } else {
            LOGGER.log(Level.SEVERE, "Refund transaction failed ", Utils.mask(dataResp));
            LOGGER.log(Level.INFO, "===================================== END REFUND ==================================" + StringPool.NEW_LINE);
            if (dataResp.getString("vpc_TxnResponseCode") != null) {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED");
                throw IErrors.REFUND_FAILED;
            } else {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                // LOGGER.log(Level.SEVERE, dataResp.encodePrettily());
                throw IErrors.REFUND_FAILED;
            }
        }
    }

    private void clientRequestToObs(RoutingContext rc, String transactionId, JDBCClient clientOnline, JDBCClient clientBackUp, HttpClientRequest clientRequest, PatchRequest<RefundData> refundData) {
        clientRequest.toObservable().subscribe(response -> {
            int statusCode = response.statusCode();
            if (statusCode == HttpResponseStatus.OK.code()) {
                response.bodyHandler(buffer1 -> {
                    String json = buffer1.toString("UTF-8");
                    // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                    // =====================================");
                    // LOGGER.log(Level.INFO, json);
                    // LOGGER.log(Level.INFO, "===================================== OneCredit Response
                    // =====================================");
                    Map mapVpc = gson.fromJson(json, Map.class);

                    LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(mapVpc));
                    if (mapVpc != null && mapVpc.get("vpc_response") != null) {
                        Map dataResp = (Map) mapVpc.get("vpc_response");
                        if ((dataResp.get("vpc_TxnResponseCode") != null && "0".equals(dataResp.get("vpc_TxnResponseCode") + "")
                                || mapVpc.get("command_status").toString().equalsIgnoreCase("WAIT_FOR_APPROVE"))) {
                            Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp1 -> {
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                .flatMap(connOnline -> {
                                                    return InstallmentService.rejectInstallment(connOnline, transactionId).flatMap(aVoid -> {
                                                        return internationalTransactionService.getHistoryByMerchantRef(connOnline, transactionId, dataResp.get("vpc_MerchTxnRef").toString(), refundData.getValue().getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                            return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                                if (userData != null) {
                                                                    internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                                }
                                                                return internationalTransactionHistory;
                                                            });

                                                        });

                                                    });
                                                });
                                    });

                            returnObs.subscribe(internationalTransactionHistory -> {
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                rc.next();
                            }, rc::fail);
                        } else {
                            LOGGER.log(Level.SEVERE, "Refund transaction failed ", Utils.mask(mapVpc));
                            LOGGER.log(Level.INFO, "===================================== END REFUND ==================================" + StringPool.NEW_LINE);
                            if (mapVpc.get("vpc_TxnResponseCode") != null) {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED");
                                throw IErrors.REFUND_FAILED;
                            } else {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                                // LOGGER.log(Level.SEVERE, mapVpc.toString());
                                throw IErrors.REFUND_FAILED;
                            }
                        }

                    } else {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED " + Utils.mask(mapVpc));
                        throw IErrors.REFUND_FAILED;
                    }

                });

                response.exceptionHandler(throwable -> {
                    rc.fail(throwable);
                });
            } else {
                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED " + Utils.mask(response));
                throw IErrors.REFUND_FAILED;
            }
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    private Observable<Boolean> lock(SQLConnection connBackUp, String key, int timeoutMillis, long startTime, long delay) {
        return Observable.just(key).flatMap(approvalData -> {
            // insert approval for user group
            return this.lockService.lock(connBackUp, key, timeoutMillis, RefundData.Type.INTERNATIONAL.getValue()).flatMap(lock -> {
                return Observable.just(lock);
                // if (lock == true) {
                //     return Observable.just(lock);
                // }
                // if (timeoutMillis < System.currentTimeMillis() - startTime) {
                //     return Observable.just(lock);
                // } else {
                //     try {
                //         Thread.sleep(delay);
                //     } catch (InterruptedException e) {
                //         throw IErrors.INTERNAL_SERVER_ERROR;
                //     }
                //     return lock(connBackUp, key, timeoutMillis, startTime, delay);
                // }
            });
        });

    }

    public void unlock(String key, JDBCClient clientBackUp) {
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.lockService.unlock(connBackUp, key, RefundData.Type.INTERNATIONAL.getValue()).flatMap(lock -> {
                        return Observable.just(lock);
                    });
                }).subscribe();
    }

    private SQLConnection onlineCon;

    private SQLConnection backUpCon;

    private final static Gson gson = new Gson();

    private static final String X_SECURE_HASH = "X-Secure-Hash";

    private static final Logger LOGGER = Logger.getLogger(InternationalTransactionPatchHandlerImpl.class.getName());
}
