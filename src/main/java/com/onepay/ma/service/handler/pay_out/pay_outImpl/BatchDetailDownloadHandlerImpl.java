package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.pay_out.BatchDetailQueryDto;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class BatchDetailDownloadHandlerImpl implements Handler<RoutingContext> {
    private final static Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(BatchDetailDownloadHandlerImpl.class.getName());
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    @Qualifier("downloadMultiFileIn")
    private Queue downloadMultiInQueue;

    @Autowired
    @Qualifier("downloadMultiFileOut")
    private Queue downloadMultiOutQueue;

    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE BATCH DETAIL DOWNLOAD POST ] =>  USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        JsonObject bodyJson = rc.getBodyAsJson();

        String batchId = bodyJson.getString("batchId") == null ? StringPool.BLANK : String.valueOf(bodyJson.getString("batchId"));
        String swiftCode = bodyJson.getString("bankSender") == null ? StringPool.BLANK : String.valueOf(bodyJson.getString("bankSender"));
        String holderName = bodyJson.getString("holderName") == null ? StringPool.BLANK : String.valueOf(bodyJson.getString("holderName"));
        String state = bodyJson.getString(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(bodyJson.getString(ParamsPool.STATE));
        BatchDetailQueryDto query = new BatchDetailQueryDto(batchId, swiftCode, holderName, state);
        query.setPage(0);
        query.setPageSize(Integer.MAX_VALUE);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Map mdata = new HashMap();
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackup -> {
                    this.connectBackUp = connBackup;
                    return getObservable(rc, userId, query, requestData, mdata);
                }).subscribe(map -> {
            if (connectBackUp != null) {
                connectBackUp.commitObservable();
            }
            Message<BatchDetailQueryDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue );
            QueueProducer.sendMessage(message);

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if (connectBackUp != null) {
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    private Observable<Map> getObservable(RoutingContext rc, String userId, BatchDetailQueryDto query, Map requestData, Map data) {
        String fileName = "payout" + StringPool.UNDERLINE + "batch_detail" + StringPool.UNDERLINE;
        long date = new java.util.Date().getTime();
        fileName += date;
        String fileHashName = "";
        data.put("parameter", query);
        data.put("file_name", fileName);
        data.put("row", 1000);
        try {
            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("payout_batch_detail");
        fileDownload.setExt("csv");
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(query));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(resp -> {
                return data;
            });
        });
    }

}
