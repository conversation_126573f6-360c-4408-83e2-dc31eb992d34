package com.onepay.ma.service.handler.externalClient;

import io.vertx.core.json.JsonObject;


import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;

import static java.nio.charset.StandardCharsets.UTF_8;

public class OneSchedClient {

    private static final Logger LOGGER = Logger.getLogger(OneSchedClient.class.getName());
    private static String ONESCHED_SERVICE_BASE_URL = PropsUtil.get("onesched-service-url", "");
    private static String ONESCHED_SERVICE_TIMEOUT = PropsUtil.get("onesched-service-timeout", "60000");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject synchronize(String jobId, String timeSkip) throws Exception {
        int expires = 300;
        // Request uri base
        String requestReference = jobId + new Date().getTime();
        String requestURI = "/scheduler/api/v1/jobs/" + jobId + "/executes/exe" + requestReference;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = Integer.parseInt(ONESCHED_SERVICE_TIMEOUT);
        JsonObject jbody = new JsonObject();
        jbody.put("time_skip", timeSkip);
        
        String schedulerUrl = ONESCHED_SERVICE_BASE_URL + requestURI;
        LOGGER.log(Level.INFO, "PUT TO SCHEDULER-SERVICE");
        LOGGER.log(Level.INFO, "url: " + Utils.excludeSensitiveInfo(schedulerUrl) );
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        URL url = new URL(schedulerUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod(requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            LOGGER.log(Level.SEVERE, "SCHEDULER SYNCHRONIZE: " + Utils.mask(strReturn) );
            return new JsonObject(strReturn);

        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            return new JsonObject();
        }
    }
    public static JsonObject synchronize2(String jobId, String timeSkip) throws Exception {
        int expires = 300;
        // Request uri base
        String requestReference = jobId + new Date().getTime();
        String requestURI = "/scheduler/api/v2/jobs/" + jobId + "/executes/exe" + requestReference;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = Integer.parseInt(ONESCHED_SERVICE_TIMEOUT);
        JsonObject jbody = new JsonObject();
        jbody.put("time_skip", timeSkip);
        
        String schedulerUrl = ONESCHED_SERVICE_BASE_URL + requestURI;
        LOGGER.log(Level.INFO, "PUT TO SCHEDULER-SERVICE");
        LOGGER.log(Level.INFO, "url: " + Utils.excludeSensitiveInfo(schedulerUrl) );
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        URL url = new URL(schedulerUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod(requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            LOGGER.log(Level.SEVERE, "SCHEDULER SYNCHRONIZE: " + Utils.mask(strReturn) );
            return new JsonObject(strReturn);

        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            return new JsonObject();
        }
    }

    public static JsonObject synchronizeInternationalViewSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-international_view_sync_id", "");
        String timeSkip = "0s";
        return synchronize(jobId, timeSkip);
    }

    public static JsonObject synchronizeInternationalFullSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-international_full_sync_id", "");
        String timeSkip = "0s";
        return synchronize(jobId, timeSkip);
    }

    public static JsonObject synchronizeInterPurchaseViewSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-inter_pc_view_sync_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

    public static JsonObject synchronizeInterRefundViewSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-inter_rf_view_sync_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

    public static JsonObject synchronizeInterCaptureViewSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-inter_ct_view_sync_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

    public static JsonObject synchronizeInterVoidViewSync() throws Exception {
        String jobId = PropsUtil.get("onesched-service-inter_void_view_sync_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

    public static JsonObject synchronizeQR() throws Exception {
        String jobId = PropsUtil.get("onesched-service-qr_synx_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

    public static JsonObject synchronizePO() throws Exception {
        String jobId = PropsUtil.get("onesched-service-po_synx_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }
}
