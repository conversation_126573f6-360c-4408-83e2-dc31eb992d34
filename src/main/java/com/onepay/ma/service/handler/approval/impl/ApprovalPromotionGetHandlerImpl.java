package com.onepay.ma.service.handler.approval.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.handler.approval.ApprovalPromotionGetHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.ApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/6/16.
 */

public class ApprovalPromotionGetHandlerImpl implements ApprovalPromotionGetHandler {

    public ApprovalPromotionGetHandlerImpl(ApprovalService approvalService, UserService userService) {
        this.userService = userService;
        this.approvalService = approvalService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String keyword = request.getParam(ParamsPool.KEY_WORDS) != null ? String.valueOf(request.getParam(ParamsPool.KEY_WORDS))  : "";
        String status = request.getParam(ParamsPool.STATUS) != null ? String.valueOf(request.getParam(ParamsPool.STATUS))  : "";
        int page = request.getParam(ParamsPool.PAGE) != null ? Integer.valueOf(request.getParam(ParamsPool.PAGE))  : 0;
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return approvalService.list(connBackUp, ApprovalType.PROMOTION, keyword, page, status).flatMap(approvals -> {

                        List<Approval> approvalList = approvals.getApprovals();
                        List<PromotionApproval> paymentServiceApprovalList = new ArrayList<>();
                        for (Approval approval : approvalList) {
                            PromotionApproval approvalData = new PromotionApproval();
                            approvalData.setName(approval.getName());
                            approvalData.setId(approval.getId());
                            approvalData.setNew_value(approval.getNew_value());
                            approvalData.setOld_value(approval.getOld_value());
                            approvalData.setStatus(approval.getStatus());
                            approvalData.setUser(approval.getUser());
                            approvalData.setDescription(approval.getDescription());
                            approvalData.setField(approval.getField());
                            approvalData.setUser_confirm(approval.getUser_confirm());
                            approvalData.setType(approval.getType());
                            approvalData.setRequest_time(approval.getRequest_time());
                            String conditions = approval.getConditions();
                            List<ApprovalCondition> conditionApprovals = gson.fromJson(conditions, new TypeToken<List<ApprovalCondition>>() {
                            }.getType());
                            for (ApprovalCondition approvalCondition : conditionApprovals) {
                                if (approvalCondition.getField().equals("PR_NAME")) {
                                    approvalData.setPromotion_name(approvalCondition.getValue());
                                    break;
                                }
                            }
                            for (ApprovalCondition approvalCondition : conditionApprovals) {
                                if (approvalCondition.getField().equals("PR_CURRENCY")) {
                                    approvalData.setCurrency(approvalCondition.getValue());
                                    break;
                                }
                            }
                            paymentServiceApprovalList.add(approvalData);
                        }

                        ApprovalsData<PromotionApproval> paymentServiceApproval = new ApprovalsData<>();
                        paymentServiceApproval.setApprovals(paymentServiceApprovalList);
                        paymentServiceApproval.setTotal_items(approvals.getTotal_items());
                        return Observable.just(paymentServiceApproval);
                    }).flatMap(paymentServiceApprovalApprovalsData -> {
                        return getListUser(connBackUp, paymentServiceApprovalApprovalsData.getApprovals(), 0).map(paymentServiceApprovals -> {
                            paymentServiceApprovalApprovalsData.setApprovals(paymentServiceApprovals);
                            return paymentServiceApprovalApprovalsData;
                        });
                    });
        }).subscribe(paymentServiceApprovalApprovalsData -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, paymentServiceApprovalApprovalsData);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });


    }
    /**
     * get list merchant for list promotion approval
     * @param sqlConnection
     * @param promotionApprovals
     * @param index
     * @return
     */
    private Observable<List<PromotionApproval>> getListUser(SQLConnection sqlConnection, List<PromotionApproval> promotionApprovals, int index){
        if(promotionApprovals.size() <= 0){
            return Observable.just(promotionApprovals);
        }
        PromotionApproval paymentServiceApproval = promotionApprovals.get(index);
        final int finalIndex = index;
        return Observable.just(paymentServiceApproval).flatMap(serviceApproval -> {
            //get user data
            return userService.get(sqlConnection, serviceApproval.getUser()).map(userData -> {
                if(userData != null) {
                    serviceApproval.setUser(userData.getEmail());
                }
                return serviceApproval;
            }).flatMap(serviceApproval1 -> {
                if(serviceApproval.getUser_confirm() != null && !serviceApproval.getUser_confirm().isEmpty()) {
                    return userService.get(sqlConnection, serviceApproval.getUser_confirm()).map(userData -> {
                        if(userData != null){
                            serviceApproval.setUser_confirm(userData.getEmail());
                        }

                        return Observable.just(serviceApproval);
                    });
                }else{
                    return  Observable.just(serviceApproval1);
                }
            }).flatMap(paymentServiceApprovalObservable -> {
                if(finalIndex >= promotionApprovals.size() - 1){
                    return Observable.just(promotionApprovals);
                }else{
                    return getListUser(sqlConnection, promotionApprovals, finalIndex + 1);
                }
            });
        });

    }
    private ApprovalService approvalService;

    private UserService userService;

    private final static Gson gson = new Gson();

}
