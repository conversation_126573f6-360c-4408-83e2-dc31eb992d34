/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */
/**
 * Created by duongtv on 24/8/2017.
 */
package com.onepay.ma.service.handler.tokenization;
/**
 * Created by duongtv on 24/8/2017.
 */
import com.onepay.ma.service.handler.tokenization.impl.TokenizationGetHandlerImpl;
import com.onepay.ma.service.handler.tokenization.impl.TranTokenizationHandlerImpl;
import com.onepay.ma.service.service.TokenService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

public interface TranTokenizationHandler extends Handler<RoutingContext> {

    static TranTokenizationHandlerImpl create(TokenService tokenizationService, UserService userService){
        return new TranTokenizationHandlerImpl(tokenizationService, userService);
    }
}
