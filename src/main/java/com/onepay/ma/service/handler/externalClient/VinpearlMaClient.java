package com.onepay.ma.service.handler.externalClient;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.models.vinpearl.VinpearlResponseDto;
import com.onepay.ma.service.util.*;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import vn.onepay.ows.Authorization;

public class VinpearlMaClient {
    private static final Logger LOGGER = Logger.getLogger(VinpearlMaClient.class.getName());
    private static String ONEPAY_MA_SERVICE_BASE_URL = PropsUtil.get("onepay_ma_service_base_url", "http://localhost/ma-service");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    public static void vpRefund(RoutingContext rc, int serviceType, String transactionId, JsonObject jbody, String userId, String xRequestId) {
        // 0: Domestic; 1: International; 2: QR, 3: Capture,4:Upos,5:BNPL,6:VietQR,7:DirectDebit
        String requestURI = "";
        HttpMethod method = HttpMethod.PATCH;
        if (serviceType == RefundData.Type.DOMESTIC.getValue()) { // 0 - URi for domestic
            requestURI = "/api/v1/domestic/transaction/" + transactionId;
        }
        if (serviceType == RefundData.Type.INTERNATIONAL.getValue() || serviceType == RefundData.Type.REFUND_CAPTURE.getValue()) { // 1,3 - URi for international,capture
            requestURI = "/api/v1/international/transaction/" + transactionId;
        }
        if (serviceType == RefundData.Type.QR.getValue()) { // 2 - URi for QR
            requestURI = "/api/v1/transaction/mpay/" + transactionId;
        }
        if (serviceType == RefundData.Type.UPOS.getValue()) { // 4 - URi for UPOS chua lam
//            requestURI = "chua lam" + transactionId;
        }
        if (serviceType == RefundData.Type.BNPL.getValue()) { // 5 - URi for BNPL
            requestURI = "/api/v1/bnpl/transaction/" + transactionId + "/request-refund";
            method = HttpMethod.POST;
        }
        if (serviceType == RefundData.Type.VIETQR.getValue()) { // 6 - URi for VietQR
            requestURI = "/api/v1/vietqr/transaction/" + transactionId;
        }
        if (serviceType == RefundData.Type.DIRECT_DEBIT.getValue()) { // 7 - URi for DirectDebit
            requestURI = "/api/v1/direct-debit/transaction/" + transactionId;
        }
        if (requestURI.isEmpty()) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        patchRefund2(rc, method ,requestURI, jbody, userId, xRequestId);
    }

    private static JsonObject patchRefund(String requestURI,JsonObject jbody, String userId, String xRequestId) {
        JsonObject jsonReturn = null;
        Date requestDate = new Date();
        int requestTimeOut = 60000;
        try {
            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");

            connection.setRequestProperty("X-USER-ID", userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + jsonReturn.toString());
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }
    private static void patchRefund2(RoutingContext rc,HttpMethod method, String requestURI, JsonObject jbody, String userId, String xRequestId) {
        HttpClient httpClient = rc.vertx().createHttpClient();
        try {
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            String updateAccountCollectURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;
            Buffer buffer = Buffer.buffer(jbody.toString());
            HttpClientRequest clientRequest = httpClient.requestAbs(method, updateAccountCollectURL);
            String operatorId = jbody.getString("operator");
            clientRequest.putHeader("Accept", "application/json")
                    .putHeader("Content-Type", "application/json")
                    .putHeader("Content-Length", String.valueOf(jbody.toString().length()))
                    .putHeader("X-USER-ID", Utils.nvl(operatorId, "MSP-EXTEND"))
                    .putHeader("X-REAL-IP", "127.0.0.1")
                    .putHeader("X-Request-ID", xRequestId);
            clientRequest.toObservable().subscribe(httpClientResponse -> {
                int statusCode = httpClientResponse.statusCode();
                LOGGER.info("Refund WFMA statusCode: " + statusCode);
                if (statusCode == HttpResponseStatus.CREATED.code() || statusCode == HttpResponseStatus.OK.code()) {
                    httpClientResponse.bodyHandler(buffer1 -> {
                        String json = buffer1.toString("UTF-8");
                        LOGGER.log(Level.SEVERE, "Call create Refund WFMA: " + json);
                        JsonObject responseJson = new JsonObject(json);
                        VinpearlResponseDto vinpearlResponseDto = new VinpearlResponseDto();
                        if (json != null && !json.isEmpty()) {
                            LOGGER.info(() -> "--- SUCCESS ---");
                            vinpearlResponseDto.setMessage("The refund request has been successfully created, awaiting merchant approval");
                            vinpearlResponseDto.setName("approved");
                            vinpearlResponseDto.setStatusCode(200);
                        } else  {
                            LOGGER.info(() -> "--- FAILED ---");
                            vinpearlResponseDto.setMessage("Refund request cannot be made, please contact OnePay");
                            vinpearlResponseDto.setName("false");
                            vinpearlResponseDto.setStatusCode(200);
                        }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, vinpearlResponseDto);
                        rc.next();
                    });
                    httpClientResponse.exceptionHandler(rc::fail);
                } else {
                    LOGGER.log(Level.SEVERE, "[Call create Refund WFMA FAILED: " + httpClientResponse.statusCode());
                    rc.fail(IErrors.INTERNAL_SERVER_ERROR);
                }
            });
            clientRequest.end(buffer);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            rc.fail(IErrors.INTERNAL_SERVER_ERROR);
        }
    }
}
