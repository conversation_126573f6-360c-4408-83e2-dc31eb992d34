package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostInternationalReportHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalReportService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;


/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/15/16.
 */
public interface FilePostInternationalReportHandler extends Handler<RoutingContext>  {
    static FilePostInternationalReportHandlerImpl create(InternationalReportService internationalReportService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue){
        return new FilePostInternationalReportHandlerImpl(internationalReportService, merchantService, serverConfig, downloadFastIn, downloadFastOut, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue);

    }
}
