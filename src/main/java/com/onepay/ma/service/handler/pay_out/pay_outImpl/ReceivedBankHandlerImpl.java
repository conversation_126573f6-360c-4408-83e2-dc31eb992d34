package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.service.pay_out.ReceivedBankService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class ReceivedBankHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private ReceivedBankService receivedBankService;

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient myClientReadOnly = myEvent.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient myClientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient myClientBackUp = myEvent.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        Observable<Object> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(myClientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(myClientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(myClientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                             
                                                    return receivedBankService.getListReceivedBank(connOnline).map(listReceivedBank -> {

                                                        BaseList baseList = new BaseList();
                                                        baseList.setTotal_items(listReceivedBank.size());
                                                        baseList.setList(listReceivedBank);

                                                        return  baseList;
                                                   
                                             });
                                            //return list firm banking transaction

                                        });
                            });
                });
        obs.subscribe(listReceivedBank -> {
            myEvent.put(ParamsPool.HANDLER_DATA_RESULT, listReceivedBank);
            myEvent.next();
        }, throwable -> {
            myEvent.fail(throwable);
        });

    }
}
