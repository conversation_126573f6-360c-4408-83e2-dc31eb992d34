package com.onepay.ma.service.handler.externalClient;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import java.util.logging.Level;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import io.netty.handler.codec.http.HttpResponseStatus;
import static java.nio.charset.StandardCharsets.UTF_8;


/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/10/2020
 * Time: 9:10 AM
 * To change this ma-web.
 */

public class PayCollectClient {
    private static final Logger LOGGER = Logger.getLogger(PayCollectClient.class.getName());

    private static String ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL = PropsUtil.get("onepay_paycollect_service_base_url", "");
    private static String ONEPAY_PAYCOLLECT_SERVICE_NAME = PropsUtil.get("onepay_paycollect_service_name", "");
    private static String ONEPAY_PAYCOLLECT_SERVICE_REGION = PropsUtil.get("onepay_paycollect_service_region", "");
    private static String ONEPAY_PAYCOLLECT_SERVICE_CLIENT_ID = PropsUtil.get("onepay_paycollect_service_client_id", "");
    private static String ONEPAY_PAYCOLLECT_SERVICE_CLIENT_KEY = PropsUtil.get("onepay_paycollect_service_client_key", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject createAccount(String userId ,String partner_id ,String reference_id , String x_USER_ID, String x_REQUEST_ID, JsonObject bdJson, JsonObject partner) {
        JsonObject  jsonReturn = null;
        // Request uri base
        String requestURI = "/paycollect/api/v1/partners/"+partner_id+"/users/"+reference_id;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // LOGGER.log(Level.INFO, "BODY partner:"+ partner.encode());
        // Milliseconds
        int requestTimeOut = 60000;
        Map<String, String> queryParamMap = new LinkedHashMap<>();
        try {
            // Create service signature with OnePAY Web Service Signature algorithm
            // LOGGER.log(Level.INFO, "BODY bdJson:"+ bdJson.encode());

            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
//            signedHeaders.put("X-Request-Id", x_REQUEST_ID);
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));


            Authorization onePAYServiceAuthorization = new Authorization(
                    partner.getString("S_ID"), partner.getString("S_KEY"),
                    ONEPAY_PAYCOLLECT_SERVICE_REGION, ONEPAY_PAYCOLLECT_SERVICE_NAME, requestMethod, requestURI, queryParamMap, signedHeaders,
                    bdJson.encode().getBytes(UTF_8), requestDate, requestTimeOut);
            LOGGER.log(Level.INFO, "POST TO PAYCOLLECT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI) );
            String createAccountCollectURL = ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(createAccountCollectURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(6000);
            connection.setReadTimeout(6000);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(bdJson.encode().getBytes(UTF_8).length));
//            connection.setRequestProperty("X-Request-Id", x_REQUEST_ID);
//            connection.setRequestProperty("X-Real-IP", "127.0.0.1");

            connection.setRequestProperty(x_USER_ID, userId);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(bdJson.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
//            bdJson.writeToBuffer().getBytes();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();

            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + Utils.mask(response) );
//                ObjectMapper objectMapper = new ObjectMapper();
                jsonReturn = new JsonObject(strReturn);
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static void updateAccount(RoutingContext rc, String id, String userId, String x_USER_ID, JsonObject bdJson, JsonObject partner) {
        String requestURI = "/paycollect/api/v1/users/" + id;
        String requestMethod = "PATCH";
        Date requestDate = new Date();
        int requestTimeOut = 60000;
        Map<String, String> queryParamMap = new LinkedHashMap<>();
        HttpClient httpClient = rc.vertx().createHttpClient();
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    partner.getString("S_ID"), partner.getString("S_KEY"),
                    ONEPAY_PAYCOLLECT_SERVICE_REGION, ONEPAY_PAYCOLLECT_SERVICE_NAME, requestMethod, requestURI, queryParamMap, signedHeaders,
                    bdJson.encode().getBytes(), requestDate, requestTimeOut);

            LOGGER.log(Level.INFO, "PATCH TO PAYCOLLECT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI) );
            String updateAccountCollectURL = ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            Buffer buffer = Buffer.buffer(bdJson.toString());
            HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.PATCH, updateAccountCollectURL);
            clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
            clientRequest.putHeader("X-OP-Authorization", onePAYServiceAuthorization.toString());
            clientRequest.putHeader("Accept", "application/json");
            clientRequest.putHeader("Content-Type", "application/json");
            clientRequest.putHeader("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            clientRequest.putHeader("X-OP-Expires", String.valueOf(requestTimeOut));
            clientRequest.putHeader("Content-Length", "" + Integer.toString(bdJson.encode().getBytes(UTF_8).length));
            clientRequest.toObservable().subscribe(httpClientResponse -> {
                int statusCode = httpClientResponse.statusCode();
                if (statusCode == HttpResponseStatus.CREATED.code() || statusCode == HttpResponseStatus.OK.code()) {
                    httpClientResponse.bodyHandler(buffer1 -> {
                        String json = buffer1.toString("UTF-8");
                        LOGGER.log(Level.SEVERE, "PAYCOLLECT UPDATE ACCOUNT Return Body: " + json);
                        JsonObject responseJson = new JsonObject(json);
                        UserConfigDto fundsTransHistoryDto = null;
                        if (responseJson.containsKey("id") && responseJson.getString("id") != null) {
                            fundsTransHistoryDto = new UserConfigDto();
                            fundsTransHistoryDto.setName(responseJson.getString("id").toString());
                        }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, fundsTransHistoryDto);
                        rc.next();
                    });

                    httpClientResponse.exceptionHandler(throwable -> {
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    });
                } else {
                    LOGGER.log(Level.SEVERE, "[ PAYCOLLECT UPDATE ACCOUNT FAILED : " + httpClientResponse.toString());
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
            });

            clientRequest.end(buffer);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
    }

    public static JsonObject getAccount(String userId ,String x_USER_ID, String x_REQUEST_ID,String user, JsonObject partner) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/paycollect/api/v1/users/"+user;
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
//            signedHeaders.put("X-Request-Id", x_REQUEST_ID);
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    partner.getString("S_ID"), partner.getString("S_KEY"),
                    ONEPAY_PAYCOLLECT_SERVICE_REGION, ONEPAY_PAYCOLLECT_SERVICE_NAME, requestMethod, requestURI, new LinkedHashMap(), signedHeaders,
                    new byte[]{}, requestDate, requestTimeOut);
            LOGGER.log(Level.INFO, "GET TO ONEPAYOUT-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + Utils.excludeSensitiveInfo(requestURI));
            // LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String aspOrderStateQueryURL = ONEPAY_MA_PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());

//            connection.setRequestProperty(x_USER_ID, userId);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                // String strReturn = response.toString();
                jsonReturn = new JsonObject(response.toString());

                LOGGER.log(Level.INFO, "PAYCOLLECT GET ACCOUNT RESPONSE: " + Utils.mask(jsonReturn));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYCOLLECT GET ACCOUNT RESPONSE: " + Utils.mask(jsonObject));
                jsonReturn = jsonObject;
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }
    private static void setVerb(HttpURLConnection cn, String verb) throws IOException {

        switch (verb) {
          case "GET":
          case "POST":
          case "HEAD":
          case "OPTIONS":
          case "PUT":
          case "DELETE":
          case "TRACE":
            cn.setRequestMethod(verb);
            break;
          default:
            // set a dummy POST verb
            cn.setRequestMethod("POST");
            try {
              // Change protected field called "method" of public class HttpURLConnection
              setProtectedFieldValue(HttpURLConnection.class, "method", cn, verb);
            } catch (Exception ex) {
              throw new IOException(ex);
            }
            break;
        }
      }
      
      public static <T> void setProtectedFieldValue(Class<T> clazz, String fieldName, T object, Object newValue) throws Exception {
          Field field = clazz.getDeclaredField(fieldName);
      
          field.setAccessible(true);
          field.set(object, newValue);
       }
    private static void allowMethods(String... methods) {
        try {
            Field methodsField = HttpURLConnection.class.getDeclaredField("methods");

            Field modifiersField = Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(methodsField, methodsField.getModifiers() & ~Modifier.FINAL);

            methodsField.setAccessible(true);

            String[] oldMethods = (String[]) methodsField.get(null);
            Set<String> methodsSet = new LinkedHashSet<>(Arrays.asList(oldMethods));
            methodsSet.addAll(Arrays.asList(methods));
            String[] newMethods = methodsSet.toArray(new String[0]);

            methodsField.set(null/*static field*/, newMethods);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }
}
