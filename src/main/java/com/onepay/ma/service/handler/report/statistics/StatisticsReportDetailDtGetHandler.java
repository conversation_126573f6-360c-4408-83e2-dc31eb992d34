package com.onepay.ma.service.handler.report.statistics;

import com.onepay.ma.service.handler.report.statistics.impl.StatisticsReportDetailDtGetHandlerImpl;
import com.onepay.ma.service.service.StatisticsReportService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/6/18.
 */
public interface StatisticsReportDetailDtGetHandler extends Handler<RoutingContext>  {
    static StatisticsReportDetailDtGetHandlerImpl create(StatisticsReportService statisticsReportService){
        return new StatisticsReportDetailDtGetHandlerImpl(statisticsReportService);
    }
}
