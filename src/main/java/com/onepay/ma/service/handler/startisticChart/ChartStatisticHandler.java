package com.onepay.ma.service.handler.startisticChart;

import com.onepay.ma.service.handler.startisticChart.domestic.StatisticChartDmsDetailHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.domestic.StatisticChartDmsDowloadHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.domestic.StatisticChartDmsGetHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.international.StatisticChartIntDetailHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.international.StatisticChartIntGetHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.international.StatisticChartInterDowloadHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.total.StatisticChartTotalDmsDownloadHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.total.StatisticChartTotalDownloadHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.total.StatisticChartTotalGetHandlerImpl;
import com.onepay.ma.service.handler.startisticChart.total.StatisticChartTotalInterDownloadHandlerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ChartStatisticHandler {
    @Autowired
    private StatisticChartIntGetHandlerImpl statisticChartIntGetHandler;
    @Autowired
    private StatisticChartDmsGetHandlerImpl statisticChartDomesticGetHandler;
    @Autowired
    private StatisticChartTotalGetHandlerImpl statisticChartTotalGetHandler;
    @Autowired
    private StatisticChartIntDetailHandlerImpl statisticChartIntDetailHandler;
    @Autowired
    private StatisticChartDmsDetailHandlerImpl statisticChartDmsDetailHandler;

    @Autowired
    private StatisticChartDmsDowloadHandlerImpl statisticChartDmsDowloadHandler;

    @Autowired
    private StatisticChartInterDowloadHandlerImpl statisticChartInterDowloadHandlerImpl;

    @Autowired
    private StatisticChartTotalDownloadHandlerImpl statisticChartTotalDownloadHandler;

    @Autowired
    private StatisticChartTotalInterDownloadHandlerImpl statisticChartTotalInterDownloadHandler;

    @Autowired
    private StatisticChartTotalDmsDownloadHandlerImpl statisticChartTotalDmsDownloadHandler;

    public StatisticChartTotalGetHandlerImpl getTotal() {
        return statisticChartTotalGetHandler;
    }


    public StatisticChartDmsGetHandlerImpl getDomestic() {
        return statisticChartDomesticGetHandler;
    }

    public StatisticChartIntGetHandlerImpl getInternational() {
        return statisticChartIntGetHandler;
    }

    public StatisticChartDmsDetailHandlerImpl getDmsDetail() {
        return statisticChartDmsDetailHandler;
    }

    public StatisticChartIntDetailHandlerImpl getIntDetail() {
        return statisticChartIntDetailHandler;
    }

    public StatisticChartDmsDowloadHandlerImpl downloadDomestic() {
        return statisticChartDmsDowloadHandler;
    }

    public StatisticChartInterDowloadHandlerImpl downloadInternational() {
        return statisticChartInterDowloadHandlerImpl;
    }

    public StatisticChartTotalDownloadHandlerImpl downloadTotal() {
        return statisticChartTotalDownloadHandler;
    }

    public StatisticChartTotalInterDownloadHandlerImpl downloadTotalInter() {
        return statisticChartTotalInterDownloadHandler;
    }

    public StatisticChartTotalDmsDownloadHandlerImpl downloadTotalDms() {
        return statisticChartTotalDmsDownloadHandler;
    }

}
