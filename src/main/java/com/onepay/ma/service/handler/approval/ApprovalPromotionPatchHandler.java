package com.onepay.ma.service.handler.approval;


import com.onepay.ma.service.handler.approval.impl.ApprovalPromotionPatchHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */
public interface ApprovalPromotionPatchHandler extends Handler<RoutingContext> {
    static ApprovalPromotionPatchHandlerImpl create(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, PromotionRuleParamService promotionRuleParamService, ApprovalService approvalService){
        return new ApprovalPromotionPatchHandlerImpl(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, promotionRuleParamService, approvalService);
    }
}
