package com.onepay.ma.service.handler.user.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.transaction.promotion.impl.PromotionTransactionPatchHandlerImpl;
import com.onepay.ma.service.handler.user.UserPatchHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.MerchantPatch;
import com.onepay.ma.service.models.RolePatch;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.models.partner.PartnerPatch;
import com.onepay.ma.service.service.UserMerchantService;
import com.onepay.ma.service.service.UserRoleService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserTerminalService;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/9/16.
 */
public class UserPatchHandlerImpl implements UserPatchHandler {

    public UserPatchHandlerImpl(UserService userService, UserRoleService userRoleService, CacheGuava cacheGuava, UserMerchantService userMerchantService, UserTerminalService userTerminalService, UserPartnerService userPartnerService) {
        this.userService = userService;
        this.userRoleService = userRoleService;
        this.cacheGuava = cacheGuava;
        this.userMerchantService = userMerchantService;
        this.userTerminalService = userTerminalService;
        this.userPartnerService = userPartnerService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        //SQLConnection sqlConnection = rc.get(ParamsPool.CONNECTION_BACKUP);
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = request.getParam("userId") == null ? StringPool.BLANK : String.valueOf(request.getParam("userId"));

        if (!userId.isEmpty()) {
            String body = rc.getBodyAsString();
            PatchRequest requestData = gson.fromJson(body, PatchRequest.class);
            if (requestData.getPath().equals("/role")) {
                PatchRequest<RolePatch> roleUserRequest = gson.fromJson(body, new TypeToken<PatchRequest<RolePatch>>() {
                }.getType());
                Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                        .flatMap(sqlConnection -> {
                            connnectBackUp = sqlConnection;
                            return sqlConnection.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return userService.get(sqlConnection, userId).flatMap(user -> {
                                    if (user == null) {
                                        LOGGER.log(Level.SEVERE, "[ USER PATCH ] => USER NOT FOUND");
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    return userRoleService.delete(sqlConnection, user.getN_id()).flatMap(integer -> {
                                        return insertUserRoleList(sqlConnection, roleUserRequest.getValue().getRoles(), user.getN_id(), 0);

                                    });
                                });
                            });
                        }).subscribe(strings -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.commitObservable();
                    }
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
                    FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });


            } else if (requestData.getPath().equals("/merchant")) {
                PatchRequest<MerchantPatch> merchantUserRequest = gson.fromJson(body, new TypeToken<PatchRequest<MerchantPatch>>() {
                }.getType());
                List<Merchant> merchants = merchantUserRequest.getValue().getMerchants();
//                if(merchants.size() <= 0){
//                    throw new InvalidValueException("Invalid merchant data");
//                }
                Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                        .flatMap(sqlConnection -> {
                            connnectBackUp = sqlConnection;
                            return sqlConnection.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return userService.get(sqlConnection, userId).flatMap(user -> {
                                    if (user == null) {
                                        LOGGER.log(Level.SEVERE, "[ USER PATCH ] => USER NOT FOUND");
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    return userMerchantService.deleteByUserId(sqlConnection, user.getN_id(), merchantUserRequest.getValue().getType())
                                            .flatMap(integer -> {
                                                return insertUserMerchantList(sqlConnection, merchants, user.getN_id(), 0);
                                            });
                                });
                            });
                        }).subscribe(strings -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.commitObservable();
                    }
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
                    FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

            } else if (requestData.getPath().equals("/partner")) {
                PatchRequest<PartnerPatch> partnerUserRequest =
                        gson.fromJson(body, new TypeToken<PatchRequest<PartnerPatch>>() {}.getType());
                List<Partner> partners = partnerUserRequest.getValue().getPartners();

                Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                        .flatMap(sqlConnection -> {
                            connnectBackUp = sqlConnection;
                            return sqlConnection.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return userService.get(sqlConnection, userId).flatMap(user -> {
                                    if (user == null) {
                                        LOGGER.log(Level.SEVERE, "[ USER PATCH ] => USER NOT FOUND");
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    return userPartnerService.deleteByUserId(sqlConnection, user.getN_id())
                                            .flatMap(responseCode -> {
                                                return insertUserPartnerList(sqlConnection, partners, user.getN_id(), 0);
                                            });
                                });
                            });
                        }).subscribe(strings -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.commitObservable();
                    }
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_PERMISSION);
                    cacheGuava.get().invalidate(userId + StringPool.UNDERLINE + ParamsPool.USER_SESSION);
                    FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if (connnectBackUp != null) {
                        connnectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
            } else {
                throw IErrors.VALIDATION_ERROR;
            }
        }

    }

    /**
     * insert user role list
     *
     * @param sqlConnection
     * @param listRoleId
     * @param userId
     * @return
     */
    private Observable<List<String>> insertUserRoleList(SQLConnection sqlConnection, List<String> listRoleId, int userId, int index) {
        if (listRoleId.size() <= 0) {
            return Observable.just(listRoleId);
        }
        String roleId = listRoleId.get(index);
        final int finalIndex = index;
        return Observable.just(roleId).flatMap(approvalData -> {
            //insert approval for user group
            return userRoleService.insert(sqlConnection, userId, roleId).flatMap(integer -> {
                if (finalIndex >= listRoleId.size() - 1) {
                    return Observable.just(listRoleId);
                } else {
                    return insertUserRoleList(sqlConnection, listRoleId, userId, finalIndex + 1);
                }
            });
        });

    }

    /**
     * insert user merchant list
     *
     * @param sqlConnection
     * @param merchantList
     * @param userId
     * @return
     */
    private Observable<List<Merchant>> insertUserMerchantList(SQLConnection sqlConnection, List<Merchant> merchantList, int userId, int index) {
        if (merchantList.size() <= 0) {
            return Observable.just(merchantList);
        }
        Merchant merchant = merchantList.get(index);
        final int finalIndex = index;

        // CASE MPAY ONLY
        if (merchant.getType().equals("mpay")) {
            return Observable.just(merchant).flatMap(merchantData -> {
                //insert approval for user group
                return userMerchantService.insert(sqlConnection, merchantData, userId).flatMap(integer -> {


                    // delete user terminal record by userId
                    if(index == 0) {    // remove once only
                        return userTerminalService.deleteByUserId(sqlConnection, userId).flatMap(result -> {
                            // Insert mpay terminal
                            return this.insertUserTerminal(sqlConnection, merchant.getTerminalIdList(), merchant, userId, 0)
                                    .flatMap(in -> {

                                        if (finalIndex >= merchantList.size() - 1) {
                                            return Observable.just(merchantList);
                                        } else {
                                            return insertUserMerchantList(sqlConnection, merchantList, userId, finalIndex + 1);
                                        }
                                    });
                        });
                    }
                        // Insert mpay terminal
                        return this.insertUserTerminal(sqlConnection, merchant.getTerminalIdList(), merchant, userId, 0)
                                .flatMap(in -> {

                                    if (finalIndex >= merchantList.size() - 1) {
                                        return Observable.just(merchantList);
                                    } else {
                                        return insertUserMerchantList(sqlConnection, merchantList, userId, finalIndex + 1);
                                    }
                                });
                });
            });
        }

        return Observable.just(merchant).flatMap(merchantData -> {
            //insert approval for user group
            return userMerchantService.insert(sqlConnection, merchantData, userId).flatMap(integer -> {

                if (finalIndex >= merchantList.size() - 1) {
                    return Observable.just(merchantList);
                } else {
                    return insertUserMerchantList(sqlConnection, merchantList, userId, finalIndex + 1);
                }
            });
        });


    }

    /**
     * Insert user partner list
     * @param sqlConnection
     * @param partners
     * @param userId
     * @param index
     * @return
     */
    private  Observable<List<Partner>> insertUserPartnerList(SQLConnection sqlConnection, List<Partner> partners, int userId, int index) {
        // Return if list is empty
        if (partners == null || partners.size() == 0) {
            return Observable.just(partners);
        }

        // Get current element
        Partner partner = partners.get(index);

        return Observable.just(partner).flatMap(partnerData -> {
            // Insert current element
            return userPartnerService.insert(sqlConnection, userId, partner.getId()).flatMap(responseCode -> {
                if (index == partners.size() -1) {
                    // Return if last element is inserted. (It means all elements are already inserted)
                    return Observable.just(partners);
                } else {
                    // Continue insert next element (Recursive)
                    return insertUserPartnerList(sqlConnection, partners, userId, index + 1);
                }
            });
        });
    }


    private Observable<Integer> insertUserTerminal(SQLConnection sqlConnection, List<String> terminalList, Merchant merchant, int userId, int index) {

        // Case all terminal
        if (merchant.isAllTerminal() || merchant.getTerminalIdList() == null || merchant.getTerminalIdList().isEmpty()) {
            return this.userTerminalService.insert(sqlConnection, userId, merchant.getMerchant_id(), "ALL").flatMap(integer -> {
                return Observable.just(integer);
            });
        } else {


            String terminalId = terminalList.get(index);
            final int finalIndex = index;
            return Observable.just(terminalId).flatMap(terminalIdD -> {
                //insert approval for user group
                return userTerminalService.insert(sqlConnection, userId, merchant.getMerchant_id(), terminalIdD).flatMap(integer -> {
                    if (finalIndex >= terminalList.size() - 1) {
                        return Observable.just(integer);
                    } else {
                        return insertUserTerminal(sqlConnection, terminalList, merchant, userId, finalIndex + 1);
                    }
                });
            });
        }

    }


    private SQLConnection connnectBackUp;

    private UserService userService;

    private UserRoleService userRoleService;

    private UserMerchantService userMerchantService;

    private UserTerminalService userTerminalService;

    private UserPartnerService userPartnerService;

    private CacheGuava cacheGuava;

    private static Logger LOGGER = Logger.getLogger(UserPatchHandler.class.getName());

    private final static Gson gson = new Gson();

}
