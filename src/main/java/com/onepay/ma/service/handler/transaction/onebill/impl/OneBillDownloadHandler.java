package com.onepay.ma.service.handler.transaction.onebill.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class OneBillDownloadHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientMerchantPortal = rc.get(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        OnebillTransactionQuery mapBody =  gson.fromJson(body, OnebillTransactionQuery.class);
        if(mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f-> f.dispose())
                .flatMap(connMerchantPortal -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return merchantService.list(connOnline, connectBackUp, "", userId, "billing").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                mapBody.setProviderId(String.join(",", String.join(",",merchantIdList)));
                                                Map data = new HashMap();
                                                return  oneBillTransactionService.total(connMerchantPortal, mapBody).flatMap(integer -> {
                                                    if (integer == 0) {
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }
                                                    if(integer >= serverConfig.getRowLevel()) {

                                                        throw IErrors.REACH_MAX_EXCEL_ROW;
                                                    }
                                                    String fileName = "billing" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
                                                    long date = new java.util.Date().getTime();
                                                    fileName += date;
                                                    String fileHashName = "";
                                                    data.put("parameter", mapBody);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("billing_transaction");
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                    if (integer <= serverConfig.getRowLevel()) {
                                                        fileDownload.setExt("xls");
                                                    }else{
                                                        fileDownload.setExt("zip");
                                                    }
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }
            OnebillTransactionQuery parameter = (OnebillTransactionQuery) map.get("parameter");
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<OnebillTransactionQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //fileDownload.setExt("zip");
                Message<OnebillTransactionQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }

    @Autowired
    private OneBillTransactionService oneBillTransactionService;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(OneBillDownloadHandler.class.getName());
}
