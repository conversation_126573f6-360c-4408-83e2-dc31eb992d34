package com.onepay.ma.service.handler.orderApproval.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.externalClient.IpnClient;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.orderApproval.OrderApproval;
import com.onepay.ma.service.models.orderApproval.OrderApprovalPostModel;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.service.domestic.SSDomesticTransactionService;
import com.onepay.ma.service.service.impl.MerchantPermitService;
import com.onepay.ma.service.service.orderApproval.OrderApprovalService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.onecomm.payment.ws.QueryReq;
import com.onepay.onecomm.payment.ws.QueryRes;
import com.onepay.onecomm.payment.ws.RefundReq;
import com.onepay.onecomm.payment.ws.RefundRes;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.core.http.HttpClientResponse;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Subscriber;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class SSDomsOrderApprovalPostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE DOMESTIC TRANSACTION POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String ipAddress = rc.get(ParamsPool.X_REAL_IP);
        if (ipAddress == null) {
            LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        HttpClient httpClient = rc.vertx().createHttpClient();

        String body = rc.getBodyAsString();
        OrderApprovalPostModel mapBody = gson.fromJson(body, OrderApprovalPostModel.class);
        if (mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ ORDER APPROVAL POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        getTransactionById(clientOnline, clientOnline, mapBody.getTransaction_id(), mapBody.getTarget()).subscribe(transaction -> {
            if (transaction == null ) {
                throw IErrors.RESOURCE_NOT_FOUND;
            }
            if(transaction.getOrder_status() != null && !transaction.getOrder_status().equalsIgnoreCase("pending") ) {
                throw IErrors.ORDER_HAS_ACCEPTED;
            }

            OrderApproval model = new OrderApproval();
            model.setTransaction_id(mapBody.getTransaction_id());
            model.setStatus(mapBody.getStatus());
            model.setDescription(mapBody.getDescription());
            model.setUser_confirm(userId);
            model.setType("domestic");

            String transactionId = String.valueOf(transaction.getTransaction_id());
            String merchantId = transaction.getMerchant_id();
            String currencyCode = transaction.getAmount().getCurrency();
            double transAmount = transaction.getAmount().getTotal() - transaction.getAmount().getRefund_total();
            String transactionRef = transaction.getMerchant_transaction_ref();
            String operatorId = userId;
            String clientIp = ipAddress;
            long refund_transactionId = 0;
            RefundData refundData = new RefundData(merchantId, transactionRef, null, transAmount, 1, currencyCode, "", 0);
            RefundReq refundReq = new RefundReq(transaction.getTransaction_id(), merchantId, currencyCode, transAmount, operatorId, clientIp, refund_transactionId);


            // Do refund for reject request
            if (model.getStatus().equalsIgnoreCase("rejected")) {

                // check if  transaction existed
                Observable<Void> stObs = this.getTransaction(clientOnline, model.getTransaction_id()).flatMap(domesticTransaction -> {
                    if (domesticTransaction == null) {
                        throw IErrors.RESOURCE_NOT_FOUND;
                    }
                    if (domesticTransaction.getStatus().equals("300")) { // check if status === 300
                        return this.query(domesticTransaction.getMerchant_id(), domesticTransaction.getAmount().getCurrency(), model.getTransaction_id(), userId, ipAddress, httpClient).map(aVoid -> {
                            return null;
                        });
                    }
                    return Observable.just(null);
                });

                stObs.subscribe(o -> {
                    this.getAmount(clientReadOnly, clientOnline, clientBackUp, model.getTransaction_id(), refundData).subscribe(amount -> {

                        this.checkIsMerchantApproval(clientBackUp, transactionId, userId, ipAddress, amount, refundData).subscribe(domesticRefund -> {
                            Object refund = domesticRefund.get("refund");
                            if (refund instanceof RefundReq) {
                                this.doRefund(rc, refundReq, httpClient, clientOnline, clientBackUp, model);
                            } else {
                                if (ipnClient.rejectDomesticOrder(model.getTransaction_id()) != null) {
                                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                                        Map<String, Object> responseHashMap = new HashMap();
                                        responseHashMap.put("order", orderApproval);
                                        responseHashMap.put("refund", domesticRefund.get("refund"));
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                                } else {
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }

                            }
                            //    LOGGER.log(Level.INFO, domesticRefund.toString());
                        }, throwable -> {
                            rc.fail(throwable);
                        });
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                }, throwable -> {
                    rc.fail(throwable);
                });

            } else {
                if (ipnClient.accpectDomesticOrder(model.getTransaction_id()) != null) {
                    updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                        Map<String, Object> responseHashMap = new HashMap();
                        responseHashMap.put("order", orderApproval);
                        responseHashMap.put("refund", null);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
            }

        }, throwable -> {
            rc.fail(throwable);
        });

    }

    private void doRefund(RoutingContext rc, RefundReq refundReq, HttpClient httpClient, JDBCClient clientOnline, JDBCClient clientBackUp, OrderApproval model) {
        Map<String, Object> responseHashMap = new HashMap();

        byte[] encoded = FunctionUtil.encodeHessian(refundReq);
        Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
        HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

        req.putHeader("Content-Type", "x-application/hessian");
        LOGGER.log(Level.INFO, "CLIENT REFUND REQUEST DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(gson.toJson(refundReq)));
        req.toObservable().subscribe(httpClientResponse -> {
            httpClientResponse.bodyHandler(responseData -> {
                // String json = responseData.toString("UTF-8");
                io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                byte[] bytesData = new byte[bufferDataResp.length()];

                for (int i = 0; i < bufferDataResp.length(); i++) {
                    byte data = bufferDataResp.getByte(i);
                    bytesData[i] = data;
                }

                // LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + gson.toJson(FunctionUtil.decodeHessian(bytesData)));
                RefundRes resp = (RefundRes) FunctionUtil.decodeHessian(bytesData);
                if (resp.getStatus() != 400 && resp.getStatus() != 300) {
                    LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ] =>  CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
                    throw IErrors.REFUND_FAILED;
                } else {
                    // Refund success, then update order
                    if (ipnClient.rejectDomesticOrder(model.getTransaction_id()) != null) {
                        updateOrder(clientOnline, clientBackUp, model).subscribe(orderApproval -> {
                            getRefundData(clientOnline, clientBackUp, resp).subscribe(stringObject -> {
                                responseHashMap.put("order", orderApproval);
                                responseHashMap.put("refund", stringObject);
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
                                rc.next();
                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        }, throwable -> {
                            rc.fail(throwable);
                        });
                    } else {
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                }

            });
            httpClientResponse.exceptionHandler(throwable -> {
                rc.fail(throwable);
            });
        });

        req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
        req.write(buffer);
        req.end();
    }

    private Observable<Double> getAmount(JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, RefundData refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {

                                            return DomesticTransactionService.listHistory(connReadOnly, connOnline, connBackup, transactionId).map(domesticTransactionHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = refundData.getAmount();
                                                for (DomesticTransactionHistory his : domesticTransactionHistories) {
                                                    // Case purchase ->
                                                    if (his.getTransaction_type().toUpperCase().equals("PURCHASE")) {
                                                        remainAmt += his.getAmount().getTotal();
                                                    } else if ((his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content)
                                                            && Integer.valueOf(his.getStatus()) != RefundApproval.Status.REQUEST.code)
                                                            || (his.getAdvanced_status()!= null && (his.getAdvanced_status()).equalsIgnoreCase("Rejected")
                                                                || his.getAdvanced_status().equalsIgnoreCase("Merchant Rejected")
                                                                || his.getAdvanced_status().equalsIgnoreCase("OnePAY Rejected"))
                                                            || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.APPROVED.content))) {
                                                        // Do nothing
                                                    } else {
                                                        if (his.getStatus().equalsIgnoreCase("400"))
                                                            remainAmt -= his.getAmount().getTotal();
                                                    }
                                                }

                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                    return refundAmt;
                                                } else {
                                                    LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                    throw IErrors.AMOUNT_REFUND_ERROR;
                                                }
                                            });
                                        });
                            });
                });
    }

    private Observable<Map> checkIsMerchantApproval(JDBCClient clientBackUp, String transactionId, String userId, String ipAddress, Double amount, RefundData refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.merchantPermitService.isMerchantPermittedRefundApproval(connBackUp, refundData.getMerchant_id(), RefundData.Type.DOMESTIC.getValue())
                            .flatMap(aBoolean -> {
                                if (aBoolean) {
                                    LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                                    return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                        return this.refundApprovalService.insertRequest(connBackUp, userId, refundData.getMerchant_id(),
                                                transactionId, refundData.getAmount(), "VND", ""
                                                , RefundData.Type.DOMESTIC.getValue()).flatMap(approval -> {
                                            Map hashMap = new HashMap();
                                            hashMap.put("refund", approval);
                                            return Observable.just(hashMap);
                                        });

                                    });
                                } else {
                                    RefundReq requestRefund = new RefundReq(Long.valueOf(transactionId),
                                            refundData.getMerchant_id(), "VND",
                                            amount, userId, ipAddress, 0);
                                    Map hashMap = new HashMap();
                                    hashMap.put("refund", requestRefund);
                                    return Observable.just(hashMap);
                                }
                            });
                });
    }

    private Observable query(String merchantId, String currencyCode, String transactionId, String userId, String ipAddress, HttpClient httpClient) {

        return Observable.create( (Subscriber<? super HttpClientResponse> subscriber) -> {
            if ( subscriber.isUnsubscribed() ) {
                return;
            }

            LOGGER.log(Level.INFO, "***** BEGIN QUERY *****");
            QueryReq requestQuery = new QueryReq(Long.parseLong(transactionId), merchantId, currencyCode, 0.0, userId, ipAddress, 0);
            byte[] encoded = FunctionUtil.encodeHessian(requestQuery);
            Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
            HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

            req.putHeader("Content-Type", "x-application/hessian");

            req.toObservable().subscribe(httpClientResponse -> {
                httpClientResponse.bodyHandler(responseData -> {
                    // String json = responseData.toString("UTF-8");

                    io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                    byte[] bytesData = new byte[bufferDataResp.length()];

                    for (int i = 0; i < bufferDataResp.length(); i++) {
                        byte data = bufferDataResp.getByte(i);
                        bytesData[i] = data;
                    }

                    QueryRes resp = (QueryRes) FunctionUtil.decodeHessian(bytesData);
                    if (resp == null) {
                        LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CONNECTION TO BANK ERROR.");
                        subscriber.onError(IErrors.REFUND_FAILED);
                    } else {
                        Map result = new HashMap<>();
                        result.put("result_code", resp.getStatus());
                        if (resp.getStatus() != 400) {
                            LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
//                                    subscriber.onNext(null);
//                                    subscriber.onCompleted();
                            subscriber.onError(IErrors.REFUND_FAILED);
                        }else {
                            subscriber.onNext(null);
                            subscriber.onCompleted();
                        }

                    }

                });
                httpClientResponse.exceptionHandler(throwable -> {
                    subscriber.onError(IErrors.REFUND_FAILED);
                });
            });

            req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
            req.write(buffer);
            req.end();
        });
    }

    private Observable<SamsungDomesticTransaction> getTransactionById(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId,String targer) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    //get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return SSDomesticTransactionService.get(connOnline, transactionId, targer);
                            });
                });
    }

    private Observable<OrderApproval> updateOrder(JDBCClient clientOnline, JDBCClient clientBackUp, OrderApproval orderApproval) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    //get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return SSDomesticTransactionService.updateOrder(connOnline, orderApproval.getTransaction_id(), orderApproval.getStatus(), orderApproval.getDescription()).flatMap(aVoid -> {
                                    return orderApprovalService.insert(connBackUp, orderApproval);
                                });
                            });
                });
    }

    private Observable<Object> getRefundData(JDBCClient clientOnline, JDBCClient clientBackUp, RefundRes resp) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(conBackUp -> {
                                return domesticRefundService.get(connOnline, conBackUp, String.valueOf(resp.getRefund_transactionId())).flatMap(domesticRefund2 -> {
                                    if (domesticRefund2.getOperator_id() != null) {
                                        return userService.get(conBackUp, domesticRefund2.getOperator_id()).map(userData -> {
                                            if (userData != null) {
                                                domesticRefund2.setOperator_id(userData.getEmail());
                                            }

                                            return domesticRefund2;
                                        });
                                    } else {
                                        // TODO:
                                        return Observable.just(null);
                                    }

                                });
                            });
                });
    }

    private Observable<DomesticTransaction> getTransaction(JDBCClient clientOnline, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return DomesticTransactionService.get(connOnline, transactionId);
                });
    }

    @Autowired
    private OrderApprovalService orderApprovalService;

    @Autowired
    private MerchantPermitService merchantPermitService;

    @Autowired
    private RefundApprovalService refundApprovalService;

    @Autowired
    private UserService userService;

    @Autowired
    private RefundConfig refundConfig;

    @Autowired
    private IpnClient ipnClient;

    @Autowired
    private DomesticRefundService domesticRefundService;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(SSDomsOrderApprovalPostHandler.class.getName());
}
