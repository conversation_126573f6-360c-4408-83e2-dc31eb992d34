package com.onepay.ma.service.handler.role.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.role.RoleDeleteHandler;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/12/16.
 */
public class RoleDeleteHandlerImpl implements RoleDeleteHandler {

    public RoleDeleteHandlerImpl(RoleService roleService) {
        this.roleService = roleService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String roleId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(roleId == null){
            LOGGER.log(Level.SEVERE, "[PROMOTION DISCOUNT PARAM] => ROLE ID ERROR");
            throw IErrors.VALIDATION_ERROR;
        }
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    connectionBackUp = connBackUp;
                    return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return roleService.delete(connectionBackUp, roleId);
                    });
                }).subscribe(integer -> {
            if(connectionBackUp != null){
                connectionBackUp.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_RESULT, null);
            rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
            rc.next();
        }, throwable -> {
            if(connectionBackUp != null){
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });


    }

    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();

    private RoleService roleService;

    private static final Logger LOGGER = Logger.getLogger(RoleService.class.getName());
}
