package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 23-Nov-16.
 */
@Component
public class FileHandler {


    @Autowired
    private FilePostReconcileHandler filePostReconcileHandler;



    @Autowired
    private FilePostInterReconPaymentHandler filePostInterReconPaymentHandler;

    @Autowired
    private FilePostDetailReconPaymentHandler filePostDetailReconPaymentHandler;

    @Autowired
    private FilePostDomesticTransactionHandler filePostDomesticTransactionHandler;

    @Autowired
    private FilePostSSDomesticTransactionHandler filePostSamsungDomesticTransactionHandler;

    @Autowired
    private FilePostFinancialTransactionHandler filePostFinancialTransactionHandler;

    @Autowired
    private FilePostSSFinancialTransactionHandler filePostSSFinancialTransactionHandler;

    public Handler downloadPaymentReconcile() {
        return this.filePostInterReconPaymentHandler;
    }

    public Handler downloadDomesticTransaction() {
        return this.filePostDomesticTransactionHandler;
    }
    public Handler downloadSSDomesticTransaction() {
        return this.filePostSamsungDomesticTransactionHandler;
    }

    public Handler downloadPaymentDetailReconcile() {
        return this.filePostDetailReconPaymentHandler;
    }

    public Handler downloadReconcile() {
        return this.filePostReconcileHandler;
    }

    public Handler downloadFinancial() {
        return  filePostFinancialTransactionHandler;
    }

    public Handler downloadSSFinancial() {
        return  filePostFinancialTransactionHandler;
    }



}
