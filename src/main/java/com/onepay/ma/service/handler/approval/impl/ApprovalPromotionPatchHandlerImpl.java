package com.onepay.ma.service.handler.approval.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.approval.ApprovalPromotionPatchHandler;
import com.onepay.ma.service.handler.user.UserPatchHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * Created by huynguyen on 3/6/16.
 */

public class ApprovalPromotionPatchHandlerImpl implements ApprovalPromotionPatchHandler {

    public ApprovalPromotionPatchHandlerImpl(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, PromotionRuleParamService promotionRuleParamService, ApprovalService approvalService) {
        this.promotionService = promotionService;
        this.promotionDiscountService = promotionDiscountService;
        this.merchantService = merchantService;
        this.promotionMerchantService = promotionMerchantService;
        this.promotionRuleService = promotionRuleService;
        this.promotionRuleParamService = promotionRuleParamService;
        this.approvalService = approvalService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => USER ID IS EMPTY " );
            throw IErrors.VALIDATION_ERROR;
        }
        final HttpServerRequest request = rc.request();
        String approvalId = request.getParam("id");
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(approvalId != null) {
            String body = rc.getBodyAsString();
            PatchRequest<Map> mapPatchRequest =  gson.fromJson(body, PatchRequest.class);
            if(mapPatchRequest == null) {
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => BODY PATCH IS EMPTY " );
                throw IErrors.VALIDATION_ERROR;
            }
            Map bodyValue = mapPatchRequest.getValue();
            String status = bodyValue.get(ParamsPool.STATUS) != null ? String.valueOf(bodyValue.get(ParamsPool.STATUS)) : StringPool.BLANK;
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPr -> {
                            connectionPR = connPr;
                            //get online connection
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        connectionBackUp = connBackUp;
                                        return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                            return approvalService.update(connBackUp,  Integer.parseInt(approvalId), status, userId).flatMap(approval -> {
                                                String field = approval.getField();
                                                String conditions = approval.getConditions();
                                                String newValue = approval.getNew_value();
                                                String type = approval.getType();
                                                if (status.toUpperCase().equals("APPROVED")) {

                                                    ApprovalData updateApprovalData = new ApprovalData();
                                                    updateApprovalData.setType(type);
                                                    updateApprovalData.setField(field);
                                                    updateApprovalData.setNew_value(newValue);

                                                    List<ApprovalCondition> conditionApprovals = gson.fromJson(conditions, new TypeToken<List<ApprovalCondition>>() {
                                                    }.getType());
                                                    updateApprovalData.setOther_conditions(conditionApprovals);
                                                    int promotionId = -1;
                                                    for (ApprovalCondition approvalCondition : conditionApprovals) {
                                                        if (approvalCondition.getField().equals("PR_ID")) {
                                                            promotionId = Integer.valueOf(approvalCondition.getValue());
                                                            break;
                                                        }
                                                    }

                                                    if(promotionId == -1){
                                                        LOGGER.log(Level.SEVERE, "[ PROMOTION APPROVAL ] => PROMOTION APPROVAL NOT FOUND");
                                                        throw IErrors.VALIDATION_ERROR;
                                                    }
                                                    int finalPromotionId = promotionId;
                                                    return connectionPR.setAutoCommitObservable(false).flatMap(aVoidC -> {
                                                        return updateDataApprove(connPr, finalPromotionId, updateApprovalData);
                                                    });

                                                }else{
                                                    ApprovalData updateApprovalData = new ApprovalData();
                                                    updateApprovalData.setType(type);
                                                    updateApprovalData.setField(field);
                                                    updateApprovalData.setNew_value(newValue);

                                                    List<ApprovalCondition> conditionApprovals = gson.fromJson(conditions, new TypeToken<List<ApprovalCondition>>() {
                                                    }.getType());
                                                    updateApprovalData.setOther_conditions(conditionApprovals);
                                                    int promotionId = -1;
                                                    for (ApprovalCondition approvalCondition : conditionApprovals) {
                                                        if (approvalCondition.getField().equals("PR_ID")) {
                                                            promotionId = Integer.valueOf(approvalCondition.getValue());
                                                            break;
                                                        }
                                                    }

                                                    if(promotionId == -1){
                                                        LOGGER.log(Level.SEVERE, "[ PROMOTION APPROVAL ] => PROMOTION APPROVAL NOT FOUND");
                                                        throw IErrors.VALIDATION_ERROR;
                                                    }
                                                    int finalPromotionId = promotionId;
                                                    return connectionPR.setAutoCommitObservable(false).flatMap(aVoidC -> {
                                                        return updateDataReject(connPr, finalPromotionId, updateApprovalData);
                                                    });

                                                }
                                            });
                                        });
                                    });
                        }).subscribe(r -> {
                    if(connectionPR != null){
                        connectionPR.commitObservable();
                    }
                    if(connectionBackUp != null){
                        connectionBackUp.commitObservable();
                    }
                    FunctionUtil.sendNextNullContext(rc, 202);
                }, throwable -> {
                    if(connectionPR != null){
                        connectionPR.rollbackObservable();
                    }
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }

                    rc.fail(throwable);
//                    FunctionUtil.sendNextNullContext(rc, 202);
                    });

        }else{
            throw IErrors.VALIDATION_ERROR;
        }

    }
    private Observable<ApprovalData> updateDataReject(SQLConnection connPr, int promotionId, ApprovalData updateApprovalData) {
        if(updateApprovalData.getType().toUpperCase().equals("PR_UPDATE_STATUS")){

            String sql = "UPDATE ONEPR.TB_PR"
                    + " SET "
                    + updateApprovalData.getField() + StringPool.EQUAL + StringPool.QUESTION
                    + " WHERE N_ID = ?";
            JsonArray params = new JsonArray();
            String newValue = (String) updateApprovalData.getNew_value();
            if(PromotionStatus.DELETED.getValue() == Integer.valueOf(newValue)){
                return Observable.just(updateApprovalData);
            }
            if(PromotionStatus.APPROVED.getValue() == Integer.valueOf(newValue)){
                newValue = String.valueOf(PromotionStatus.CREATED.getValue());
            }else if(PromotionStatus.ACTIVE.getValue() == Integer.valueOf(newValue)){
                newValue = String.valueOf(PromotionStatus.APPROVED.getValue());
            }
            params.add(newValue).add(promotionId);
            return  promotionService.updateByField(connPr, sql, params).flatMap(integer -> {
                return Observable.just(updateApprovalData);
            });
        }else{
            return Observable.just(updateApprovalData);
        }
    }
    /**
     * update approval approved
     * @param connPr
     * @param promotionId
     * @param updateApprovalData
     * @return
     */
    private Observable<ApprovalData> updateDataApprove(SQLConnection connPr, int promotionId, ApprovalData updateApprovalData){

        if(updateApprovalData.getType().toLowerCase().equals("pr_link_merchant_approval")){
            return  promotionMerchantService.delete(connPr, promotionId).flatMap(integer -> {
                List<String> merchants = gson.fromJson(String.valueOf(updateApprovalData.getNew_value()), new TypeToken<List<String>>(){}.getType());
                return insertMerchantList(connPr, promotionId, merchants, 0).flatMap(strings -> {
                    return Observable.just(updateApprovalData);
                });
            });
        }else if(updateApprovalData.getType().toLowerCase().equals("pr_link_discount_approval")){
            List<PromotionDiscount> discounts =  gson.fromJson((String.valueOf(updateApprovalData.getNew_value())), new TypeToken<List<PromotionDiscount>>(){}.getType());
            // validate discount
            validateDiscountTypes(discounts);
            return  promotionDiscountService.delete(connPr, promotionId).flatMap(integer -> {
                // insert promotion discount
                return insertPromotionDiscountData(connPr, promotionId, discounts, 0).flatMap(strings -> {
                    return Observable.just(updateApprovalData);
                });
            });

        }else if(updateApprovalData.getType().toLowerCase().equals("pr_insert_rule_approval")){
            List<PromotionRule> rules =  gson.fromJson((String.valueOf(updateApprovalData.getNew_value())), new TypeToken<List<PromotionRule>>(){}.getType());
            return  insertPromotionRuleData(connPr, promotionId, rules, 0).flatMap(integer -> {
                return Observable.just(updateApprovalData);
            });

        }else if(updateApprovalData.getType().toLowerCase().equals("pr_update_rule_approval")){
            String field = updateApprovalData.getField();

            int ruleId = -1;
            List<ApprovalCondition> otherConditions = updateApprovalData.getOther_conditions();
            for (ApprovalCondition otherCondition : otherConditions){
                if(otherCondition.getField().toLowerCase().equals("n_id")){
                    ruleId = Integer.valueOf(otherCondition.getValue());
                    break;
                }
            }
            String newValue = (String) updateApprovalData.getNew_value();
            String type = "";
            if(!field.equals("N_ORDER")) {
                return promotionRuleService.updateByField(connPr, ruleId, field, newValue).flatMap(integer -> {
                    return Observable.just(updateApprovalData);
                });
            }else{
                return promotionRuleService.updateOrder(connPr, promotionId, ruleId, newValue).flatMap(integer -> {
                    return Observable.just(updateApprovalData);
                });
            }

        }else if(updateApprovalData.getType().toLowerCase().equals("pr_delete_rule_approval")){
            int ruleId = -1;
            List<ApprovalCondition> otherConditions = updateApprovalData.getOther_conditions();
            for (ApprovalCondition otherCondition : otherConditions){
                if(otherCondition.getField().toLowerCase().equals("n_id")){
                    ruleId = Integer.valueOf(otherCondition.getValue());
                    break;
                }
            }
            //int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
            return promotionRuleService.delete(connPr, promotionId, ruleId).flatMap(integer -> {
                return Observable.just(updateApprovalData);
            });
        }else if(updateApprovalData.getType().toLowerCase().equals("pr_update_rule_param_approval")){
            int ruleTypeId = 0;
            int ruleParamId = 0;
            List<ApprovalCondition> otherConditions = updateApprovalData.getOther_conditions();
            for (ApprovalCondition otherCondition : otherConditions){
                if(otherCondition.getField().toLowerCase().equals("rule_type_id")){
                    ruleTypeId = Integer.valueOf(otherCondition.getValue());
                }else if(otherCondition.getField().toLowerCase().equals("rule_param_id")){
                    ruleParamId = Integer.valueOf(otherCondition.getValue());
                }
            }
            List<PromotionRuleParam> rules =  gson.fromJson(String.valueOf(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRuleParam>>(){}.getType());

            return updatePromotionRuleParam(connPr, ruleTypeId, ruleParamId, rules, 0).flatMap(promotionRuleParams -> {
                return Observable.just(updateApprovalData);
            });
        }else if(updateApprovalData.getType().toLowerCase().equals("pr_insert_rule_param_approval")){
            int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
            List<PromotionRuleParam> rules =  gson.fromJson((String.valueOf(updateApprovalData.getNew_value())), new TypeToken<List<PromotionRuleParam>>(){}.getType());
            return insertPromotionRuleParam(connPr, ruleId, rules, 0).flatMap(promotionRuleParams -> {
                return Observable.just(updateApprovalData);
            });
        }else if(updateApprovalData.getType().toLowerCase().equals("pr_delete_rule_param_approval")){
            int ruleId = 0;
            int ruleParamId = 0;
            List<ApprovalCondition> otherConditions = updateApprovalData.getOther_conditions();
            for (ApprovalCondition otherCondition : otherConditions){
                if(otherCondition.getField().toLowerCase().equals("rule_id")){
                    ruleId = Integer.valueOf(otherCondition.getValue());
                }else if(otherCondition.getField().toLowerCase().equals("rule_param_id")){
                    ruleParamId = Integer.valueOf(otherCondition.getValue());
                }
            }
            return promotionRuleParamService.delete(connPr, ruleId, ruleParamId).flatMap(integer -> {
                return Observable.just(updateApprovalData);
            });
        }else{
            String field = updateApprovalData.getField();
            String newValue = "";
            String sql = "UPDATE ONEPR.TB_PR"
                    + " SET "
                    + field + StringPool.EQUAL + StringPool.QUESTION
                    + " WHERE N_ID = ?";
            JsonArray params = new JsonArray();
            if(field.equals("PROMOTION_TIME")){

                Map<String, Object> map = gson.fromJson(String.valueOf(updateApprovalData.getNew_value()), Map.class);
                String startTime = map.get("start_time") == null ? StringPool.BLANK : String.valueOf(map.get("start_time"));
                String endTime = map.get("end_time") == null ? StringPool.BLANK : String.valueOf(map.get("end_time"));
                try {
                    Date startTimeO = DATE_TIME_FORMAT.parse(startTime);
                    Date endTimeO = DATE_TIME_FORMAT.parse(endTime);
                    if(endTimeO.before(startTimeO)){
                        LOGGER.log(Level.SEVERE, "[ PROMOTION APPROVAL ] => START TIME MUST BE BEFORE END TIME");
                        throw IErrors.VALIDATION_ERROR;
                    }
                } catch (ParseException e) {
                    LOGGER.log(Level.SEVERE, "[ PROMOTION APPROVAL ] => PARSE DATE ERROR", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                sql = "UPDATE ONEPR.TB_PR"
                        + " SET "
                        + " D_START = TO_TIMESTAMP(?, 'DD/MM/YYYY HH:MI AM')"
                        + StringPool.COMMA
                        + " D_END = TO_TIMESTAMP(?, 'DD/MM/YYYY HH:MI AM') "
                        + " WHERE N_ID = ?";
                params.add(startTime)
                        .add(endTime)
                        .add(promotionId);

            }else{
                newValue = (String) updateApprovalData.getNew_value();
                params.add(newValue).add(promotionId);
            }


            String finalNewValue = newValue;
            return  promotionService.updateByField(connPr, sql, params).flatMap(integer -> {
                if(field.toUpperCase().equals("N_STATUS") && finalNewValue.equals("2")){
                    return promotionService.updateApproveDate(connPr, promotionId).flatMap(integer1 -> {
                        return Observable.just(updateApprovalData);
                    });
                }
                return Observable.just(updateApprovalData);
            });

        }
    }

    /**
     * insert list promotion discount data
     * @param connPr
     * @param promotionId
     * @param discounts
     * @param index
     * @return
     */
    private Observable<List<PromotionDiscount>> insertPromotionDiscountData(SQLConnection connPr, int promotionId, List<PromotionDiscount> discounts, int index){
        if(discounts.size() <= 0){
            return Observable.just(discounts);
        }
        final int finalIndex = index;
        PromotionDiscount discount = discounts.get(index);
        return Observable.just(discount).flatMap(s -> {
            return  promotionDiscountService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= discounts.size() - 1){
                    return Observable.just(discounts);
                }else{
                    return insertPromotionDiscountData(connPr, promotionId, discounts, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert list merchant data
     * @param connPr
     * @param promotionId
     * @param merchants
     * @param index
     * @return
     */
    private Observable<List<String>> insertMerchantList(SQLConnection connPr, int promotionId, List<String> merchants, int index){
        if(merchants.size() <= 0){
            return Observable.just(merchants);
        }
        final int finalIndex = index;
        String merchant = merchants.get(index);
        return Observable.just(merchant).flatMap(s -> {
            return  promotionMerchantService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= merchants.size() - 1){
                    return Observable.just(merchants);
                }else{
                    return insertMerchantList(connPr, promotionId, merchants, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert list promotion rule data
     * @param connPr
     * @param promotionId
     * @param rules
     * @param index
     * @return
     */
    private Observable<List<PromotionRule>> insertPromotionRuleData(SQLConnection connPr, int promotionId, List<PromotionRule> rules, int index){
        if(rules.size() <= 0){
            return Observable.just(rules);
        }
        final int finalIndex = index;
        PromotionRule rule = rules.get(index);
        return Observable.just(rule).flatMap(s -> {
            return  promotionRuleService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= rules.size() - 1){
                    return Observable.just(rules);
                }else{
                    return insertPromotionRuleData(connPr, promotionId, rules, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get promotion data
     * @param sqlConnPr
     * @param promotionId
     * @return
     */
    private Observable<Promotion> getPromotionData(SQLConnection sqlConnPr, String promotionId){
        return promotionService.get(sqlConnPr, promotionId).flatMap(promotionData -> {
            //get discount data
            return promotionDiscountService.listDiscount(sqlConnPr, StringPool.BLANK, promotionData.getPromotion_id()).flatMap(discountsData -> {
                promotionData.setDiscounts(discountsData);
                // get merchant data
                return promotionMerchantService.list(sqlConnPr, promotionData.getPromotion_id()).flatMap(merchantsData -> {
                    promotionData.setMerchants(merchantsData);
                    //get rule data
                    return promotionRuleService.list(sqlConnPr, promotionData.getPromotion_id()).map(rulesData -> {
                        promotionData.setRules(rulesData);
                        //return final promotion data
                        return promotionData;
                    });
                });
            });
        });
    }

    /**
     * insert list promotion rule param
     * @param sqlConn
     * @param promotionRuleParams
     * @param index
     * @return
     */
    private Observable<List<PromotionRuleParam>> insertPromotionRuleParam(SQLConnection sqlConn, int ruleId, List<PromotionRuleParam> promotionRuleParams, int index){
        if(promotionRuleParams.size() <= 0){
            return Observable.just(promotionRuleParams);
        }
        PromotionRuleParam promotionRuleParam = promotionRuleParams.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRuleParam).flatMap(serviceApproval -> {
            //get user data
            return promotionRuleParamService.insert(sqlConn, ruleId, promotionRuleParam).flatMap(integer -> {
                if(finalIndex >= promotionRuleParams.size() - 1){
                    return Observable.just(promotionRuleParams);
                }else{
                    return insertPromotionRuleParam(sqlConn, ruleId, promotionRuleParams, finalIndex + 1);
                }
            });
        });
    }

    /**
     * update list promotion rule param
     * @param sqlConn
     * @param promotionRuleParams
     * @param index
     * @return
     */
    private Observable<List<PromotionRuleParam>> updatePromotionRuleParam(SQLConnection sqlConn, int ruleTypeId, int ruleParamId,  List<PromotionRuleParam> promotionRuleParams, int index){
        if(promotionRuleParams.size() <= 0){
            return Observable.just(promotionRuleParams);
        }
        PromotionRuleParam promotionRuleParam = promotionRuleParams.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRuleParam).flatMap(ruleParam -> {
            if(ruleParam.getRule_param_value() != null && !ruleParam.getRule_param_value().isEmpty()) {
                //get regex
                return promotionRuleParamService.getRegexRuleTYpe(sqlConn, ruleTypeId, ruleParam.getRule_param_name()).flatMap(s -> {
                    if (s == null || s.isEmpty()) {
                        LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => Empty validation regex with rule type = " + ruleTypeId + " and param name = " + ruleParam.getRule_param_name());
                        throw IErrors.VALIDATION_ERROR;
                    }
                    final Pattern pattern = Pattern.compile(s);

                    if (!pattern.matcher(ruleParam.getRule_param_value()).matches()) {
                        LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => Invalid param value format with paramName " + ruleParam.getRule_param_name());
                        throw IErrors.VALIDATION_ERROR;
                    }
                    //update promotion rule
                    return promotionRuleParamService.update(sqlConn, ruleParamId, promotionRuleParam).flatMap(integer -> {
                        if(finalIndex >= promotionRuleParams.size() - 1){
                            return Observable.just(promotionRuleParams);
                        }else{
                            return updatePromotionRuleParam(sqlConn, ruleTypeId, ruleParamId, promotionRuleParams, finalIndex + 1);
                        }
                    });
                });
            }else{
                //update promotion rule
                return promotionRuleParamService.update(sqlConn, ruleParamId, promotionRuleParam).flatMap(integer -> {
                    if(finalIndex >= promotionRuleParams.size() - 1){
                        return Observable.just(promotionRuleParams);
                    }else{
                        return updatePromotionRuleParam(sqlConn, ruleTypeId, ruleParamId, promotionRuleParams, finalIndex + 1);
                    }
                });
            }


        });
    }

    /**
     * validate discount
     * @param promotionDiscounts
     */
    private void validateDiscountTypes(List<PromotionDiscount> promotionDiscounts) {

        int lastTypeId = 0;

        if (promotionDiscounts == null || promotionDiscounts.size() <= 0) {
            LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => DISCOUNT TYPE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        for (PromotionDiscount promotionDiscount : promotionDiscounts) {

            int discountType = promotionDiscount.getDiscount_type().getDiscount_type_id();
            if (discountType <= 0) {
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => DISCOUNT TYPE WRONG TYPE ID");
                throw IErrors.VALIDATION_ERROR;
            }
            if(discountType != ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID){

                if(promotionDiscount.getDiscount_value() == null){
                    LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => INVALID DISCOUNT VALUE FOR " + discountType);
                    throw IErrors.VALIDATION_ERROR;
                }
            }else{
                double discountValue = 0;
                for (Map.Entry<String,Object> map : promotionDiscount.getDiscount_value().get(0).entrySet()){
                    discountValue = Double.valueOf(map.getValue().toString());
                }
                if(discountValue < 0){
                    LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => INVALID DISCOUNT VALUE LOWER THAN 0");
                    throw IErrors.VALIDATION_ERROR;
                }
            }
            if (lastTypeId == ParamsPool.DISCOUNT_TYPE_PERCENT_ID
                    && (discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID)) {
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => DISCOUNT TYPES CONTAIN BOTH PERCENT AND FIX TYPE");
                throw IErrors.VALIDATION_ERROR;

            } else if (lastTypeId == ParamsPool.DISCOUNT_TYPE_FIX_ID && (discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID
                    || discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_MAX_PER_TXN_ID)) {
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => DISCOUNT TYPES CONTAIN BOTH PERCENT AND FIX TYPE");
                throw IErrors.VALIDATION_ERROR;
            } else {

                lastTypeId = discountType;

            }

        }


    }

    private SQLConnection connectionPR = null;

    private SQLConnection connectionBackUp = null;

    private PromotionService promotionService;

    private PromotionDiscountService promotionDiscountService;

    private MerchantService merchantService;

    private PromotionMerchantService promotionMerchantService;

    private ApprovalService approvalService;

    private PromotionRuleService promotionRuleService;

    private PromotionRuleParamService promotionRuleParamService;


    private static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("dd/MM/yyyy hh:mm a");

    private final static Gson gson = new Gson();

    private static Logger LOGGER = Logger.getLogger(UserPatchHandler.class.getName());

}
