package com.onepay.ma.service.handler.paypal.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.externalClient.PayPalClient;
import com.onepay.ma.service.models.paypal.PaypalNotify;
import com.onepay.ma.service.service.paypal.PaypalMerchantService;
import com.onepay.ma.service.service.paypal.PaypalNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class MerchantOnboardHandler implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {
        LOGGER.log(Level.INFO, "RECEIVE PAYPAL MERCHANT ONBOARD NOTIFICATION: " + rc.getBodyAsJson().encode());


        JDBCClient jdbcClientB = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JsonObject body = rc.getBodyAsJson();

        PaypalNotify notify = new PaypalNotify();
        notify.setId(body.getString("id"));
        notify.setEventType(body.getString("event_type"));
        notify.setResourceType(body.getString("resource_type"));
        notify.setSummary(StringPool.BLANK);
        notify.setEventVersion(StringPool.BLANK);
        notify.setResource(body.getJsonObject("resource").encode());
        notify.setLinks(body.getJsonObject("resource").getJsonArray("links").encode());


        String merchantId = body.getJsonObject("resource").getString("merchant_id");
        JsonNode accountJs = PayPalClient.getAccountStatus(merchantId);

        Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClientB), f -> f.dispose())
                .flatMap(connBackup -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                connectionO = connOnline;
                                connectionB = connBackup;
                                return connectionB.setAutoCommitObservable(false).flatMap(aVoid -> {
                                    return connectionO.setAutoCommitObservable(false).flatMap(aVoid1 -> {
                                        return paypalNotifyService.insert(connBackup, notify).flatMap(aVoid2 -> {

                                            return this.paypalMerchantService.insert(connectionO
                                                    , accountJs.get("tracking_id").asInt()
                                                    , accountJs.get("merchant_id").asText()
                                                    , accountJs.get("payments_receivable").asText()
                                                    , accountJs.get("primary_email_confirmed").asText());

                                        });
                                    });
                                });
                            });
                }).subscribe(mpayNotificationBaseList -> {
            if (connectionB != null) {
                connectionB.commitObservable();
            }
            if (connectionO != null) {
                connectionO.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.CREATED.code());
            rc.next();
        }, throwable -> {
            if (connectionB != null) {
                connectionB.rollbackObservable();
            }
            if (connectionO != null) {
                connectionO.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }


    @Autowired
    private PaypalNotifyService paypalNotifyService;

    private SQLConnection connectionB;
    private SQLConnection connectionO;
    @Autowired
    private PaypalMerchantService paypalMerchantService;

    private final static Gson gson = new Gson();
    private static Logger LOGGER = Logger.getLogger(PayPalDisputePostHandler.class.getName());
}
