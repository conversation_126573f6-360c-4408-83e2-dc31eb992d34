package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.util.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import com.onepay.ma.service.handler.externalClient.OnePayoutClient;

import io.vertx.core.json.JsonObject;

@Component
public class ReceivedInfoHandlerImpl implements Handler<RoutingContext> {

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String userId = myEvent.get(ParamsPool.USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        String funds_transfer_id = myEvent.request().getParam("funds_transfer_id");
        String receiptBank = myEvent.request().getParam("receiptBank");
        String receiptAccountId = myEvent.request().getParam("receiptAccountId");
        String amount = myEvent.request().getParam("amount");
        String accountId = myEvent.request().getParam("merchantAccount");

        JsonObject jo = OnePayoutClient.getReceivedAccount(userId, xUserId, xRequestId, receiptBank, accountId, receiptAccountId, amount, funds_transfer_id);
        Map<String, Object> responseHashMap = new HashMap();
        responseHashMap.put("result", jo.getString("holder_name") != null ? jo.getString("holder_name") : "ERROR");
        responseHashMap.put("message", jo.getString("message"));
        responseHashMap.put("message_vi", jo.getString("message_vi"));
        myEvent.put(ParamsPool.HANDLER_DATA_RESULT, responseHashMap);
        myEvent.next();

    }
}
