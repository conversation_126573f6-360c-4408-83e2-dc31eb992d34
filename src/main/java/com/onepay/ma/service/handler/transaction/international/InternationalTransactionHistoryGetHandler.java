package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.InternationalTransactionHistoryGetHandlerImpl;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalTransactionHistoryGetHandler extends Handler<RoutingContext>  {
    static InternationalTransactionHistoryGetHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, UserService userService){
        return new InternationalTransactionHistoryGetHandlerImpl(internationalTransactionService, merchantService, userService);

    }
}
