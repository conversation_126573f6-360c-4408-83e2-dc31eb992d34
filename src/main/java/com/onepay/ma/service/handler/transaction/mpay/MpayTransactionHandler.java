package com.onepay.ma.service.handler.transaction.mpay;

import com.onepay.ma.service.handler.transaction.mpay.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 7/5/2016.
 */
@Component
public class MpayTransactionHandler {

    @Autowired
    private MpayTransactionGetHandlerImpl mpayTransactionGetHandler;

    @Autowired
    private SamSungMpayTransactionGetHandlerImpl samsungMpayTransactionGetHandler;

    @Autowired
    private MocaMpayTransactionGetHandlerImpl mocaMpayTransactionGetHandler;

    @Autowired
    private MpayTransactionHistoryGetHandler mpayTransactionHistoryGetHandler;

    @Autowired
    private MpayOrderGetHandler mpayOrderGetHandler;

    @Autowired
    private MpayOrderPostHandler mpayOrderPostHandler;

    @Autowired
    private MpayOrderQueryHandler mpayOrderQueryHandler;

    @Autowired
    private MpayTransactionListHandler mpayTransactionListHandler;

    @Autowired
    private MpayTransactionPatchHandlerImpl mpayTransactionPatchHandler;

    @Autowired
    private MpayTransactionDownloadHandler mpayTransactionDownloadHandler;

    @Autowired
    private SamSungMpayTransactionDownloadHandlerImpl samsungMpayTransactionDownloadHandler;

    @Autowired
    private MocaMpayTransactionDownloadHandler mocaMpayTransactionDownloadHandler;

    @Autowired
    private MpayTransactionUpdateHandlerImpl mpayTransactionUpdateHandler;

    public Handler listTransaction() {
        return mpayTransactionListHandler;
    }

    public Handler update() {
        return mpayTransactionUpdateHandler;
    }

    public Handler downloadTransaction() {
        return mpayTransactionDownloadHandler;
    }

    public Handler downloadSSTransaction() {
        return samsungMpayTransactionDownloadHandler;
    }

    public Handler downloadMocaTransaction() {
        return mocaMpayTransactionDownloadHandler;
    }

    public Handler queryOrder() {
        return this.mpayOrderQueryHandler;
    }

    public Handler getOrder() {
        return this.mpayOrderGetHandler;
    }

    public Handler postOrder() {
        return this.mpayOrderPostHandler;
    }

    public Handler get() {
        return mpayTransactionGetHandler;
    }

    public Handler getSSTrans() {
        return samsungMpayTransactionGetHandler;
    }

    public Handler getMocaTrans() {
        return mocaMpayTransactionGetHandler;
    }

    public Handler getHistories() {
        return mpayTransactionHistoryGetHandler;
    }

    public Handler patch() {return mpayTransactionPatchHandler;}

}
