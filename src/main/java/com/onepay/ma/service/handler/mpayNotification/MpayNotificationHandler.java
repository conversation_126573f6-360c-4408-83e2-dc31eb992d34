package com.onepay.ma.service.handler.mpayNotification;

import com.onepay.ma.service.handler.mpayNotification.impl.*;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 26-Sep-17.
 */
@Component
public class MpayNotificationHandler {

    @Autowired
    private MpayNotificationConfigGetHandler mpayNotificationConfigGetHandler;

    @Autowired
    private MpayNotificationConfigPostHandler mpayNotificationConfigPostHandler;

    @Autowired
    private MpayNotificationConfigPutHandler mpayNotificationConfigPutHandler;

    @Autowired
    private MpayNotificationConfigDeleteHandler mpayNotificationDeleteHandler;

    @Autowired
    private PushMerchantNotificationHandler pushMerchantNotificationHandler;

    @Autowired
    private PushAppNotificationHandler pushAppNotificationHandler;

    @Autowired
    private PostCustomerNotificationHandler pushCustomerNotificationHandler;

    @Autowired
    private PushOPNotificationHandler pushOPNotificationHandler;

    @Autowired
    private OPNotificationGetHandler opNotificationGetHandler;


    @Autowired
    private MpayNotificationGetHandler mpayNotificationGetHandler;

    @Autowired
    private MpayNotificationPatchHandler mpayNotificationPatchHandler;
    @Autowired
    private CustomerNotifyMsgGetHandler customerNotifyMsgGetHandler;

    @Autowired
    private CustomerNotifyGetHandler customerNotifyGetHandler;

    @Autowired
    private CustomerNotifyPatchHandler customerNotifyPatchHandler;


    public Handler putConfig() {
        return mpayNotificationConfigPutHandler;
    }
    public Handler patchNotify() {
        return mpayNotificationPatchHandler;
    }
    public Handler getConfig() {
        return mpayNotificationConfigGetHandler;
    }
    public Handler getNotify() {
        return mpayNotificationGetHandler;
    }
    public Handler deleteConfig() {
        return mpayNotificationDeleteHandler;
    }
    public Handler postConfig() {
        return mpayNotificationConfigPostHandler;
    }
    public Handler pushNotification() {
        return pushMerchantNotificationHandler;
    }

    public Handler pushAppNotify() {
        return pushAppNotificationHandler;
    }

    public Handler getCustomerNotify() {return customerNotifyGetHandler;}

    public Handler getCustomerMsg() {return customerNotifyMsgGetHandler;}

    public Handler patchCustomerNotification() {return customerNotifyPatchHandler;}

    public Handler pushCustomerNotification() {
        return pushCustomerNotificationHandler;
    }

    public Handler postMobileNotification() {
        return  pushOPNotificationHandler;
    }
    public Handler getMobileNotification() {
        return opNotificationGetHandler;
    }
}

