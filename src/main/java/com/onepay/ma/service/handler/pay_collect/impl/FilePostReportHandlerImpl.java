package com.onepay.ma.service.handler.pay_collect.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_collect.ReportService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FilePostReportHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private ReportService reportService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ FILE PAY COLLECT REPORT POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        final HttpServerRequest request = rc.request();
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        JsonObject bodyJson = rc.getBodyAsJson();
        if (bodyJson == null) {
            LOGGER.log(Level.SEVERE, "[ FILE PAY COLLECT REPORT POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        String fromDate = bodyJson.getString(ParamsPool.FROM_DATE);
        String toDate = bodyJson.getString(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(fromDate);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ PAY COLLECT REPORT GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        int checkFromDateDiffToDate = FunctionUtil.compareDateByMonth(oFromDate, oToDate, 6);
        int checkFromdateTooOld = FunctionUtil.compareDateByMonth(oFromDate, new Date(), 24);

        // Validate fromdate, todate too long
        if (checkFromDateDiffToDate <= 0) {
            LOGGER.log(Level.SEVERE, "[ PAY COLLECT REPORT GET ]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        // Validate fromdate after 2 years ago
        if (checkFromdateTooOld <= 0) {
            LOGGER.log(Level.SEVERE,"[ PAY COLLECT REPORT DOWNLOAD SEARCH GET OVER 2 YEARS AGO]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_OLD_ERROR;
        }

        String virtualAccId = bodyJson.getString("virtualAccId") == null ? StringPool.BLANK : bodyJson.getString("virtualAccId");
        // String bankTxnRef = bodyJson.getString("bankTxnRef") == null ? StringPool.BLANK :
        // String.valueOf(bodyJson.getString("bankTxnRef"));
        String receivedBankList = bodyJson.getString("receivedBanks") == null ? StringPool.BLANK : String.valueOf(bodyJson.getString("receivedBanks"));
        // String senderBankList = bodyJson.getString("senderBanks") == null ? StringPool.BLANK :
        // String.valueOf(bodyJson.getString("senderBanks"));
        String interval = bodyJson.getString("interval");
        String merchantId = bodyJson.getString("merchantId");
        String groupBy = bodyJson.getString("groupBy");
        String lang = bodyJson.getString("lang");
        ReportSearchReq searchRequest = new ReportSearchReq(fromDate, toDate, virtualAccId, interval, 0, Integer.MAX_VALUE, receivedBankList, merchantId, groupBy);
        searchRequest.setLang(lang);
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        String maMain = bodyJson.getString(ParamsPool.MA_MAIN);
        LOGGER.log(Level.INFO, "userId: {}", userId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain download : ");
            String finalMerchantsList = MaPermission.getMc(userId, requestId, partnerId, merchantId);

            searchRequest.setMerchantId(finalMerchantsList);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                .flatMap(connPaycollect -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            connectBackUp = connBackUp;
                            Map data = new HashMap();
                            return reportService.total(connPaycollect, searchRequest).flatMap(integer -> {
                                if (integer == 0) {
                                    LOGGER.log(Level.SEVERE, "[ FILE PAY COLLECT REPORT POST ] => TOTAL NOT FOUND");
                                    throw IErrors.NO_DATA_FOUND;
                                }
                                if (integer > serverConfig.getRowLimit()) {
                                    throw IErrors.PASS_EXCEL_LIMIT;
                                }
                                return getObservable(rc, userId, searchRequest, requestData, data, integer);
                            });
                        });
                }).subscribe(map -> {
                    if (connectBackUp != null) {
                        connectBackUp.commitObservable();
                    }
                    ReportSearchReq parameter = (ReportSearchReq) map.get("parameter");
                    int row = Integer.valueOf(map.get("row").toString());
                    if (row <= serverConfig.getRowLevel()) {
                        Message<ReportSearchReq> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                        QueueProducer.sendMessage(message);
                    } else {
                        Message<ReportSearchReq> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                        QueueProducer.sendMessage(message);
                    }
                    rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                    rc.next();
                }, throwable -> {
                    if (connectBackUp != null) {
                        connectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
            
        } else  {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                .flatMap(connPaycollect -> {
                    // get back up connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                connectBackUp = connBackUp;
                                Map data = new HashMap();
                                return merchantService.merchantPaycollects(connPaycollect, connBackUp, merchantId, userId,
                                        "paycollect").flatMap(merchants -> {
                                            List<String> merchantIdList =
                                                    merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                            searchRequest.setMerchantId(String.join(",", merchantIdList));
                                            return reportService.total(connPaycollect, searchRequest).flatMap(integer -> {
                                                if (integer == 0) {
                                                    LOGGER.log(Level.SEVERE, "[ FILE PAY COLLECT REPORT POST ] => TOTAL NOT FOUND");
                                                    throw IErrors.RESOURCE_NOT_FOUND;
                                                }

                                                if (integer > serverConfig.getRowLimit()) {
                                                    throw IErrors.PASS_EXCEL_LIMIT;
                                                }
                                                return getObservable(rc, userId, searchRequest, requestData, data, integer);
                                            });
                                        });
                            });
                }).subscribe(map -> {
                    if (connectBackUp != null) {
                        connectBackUp.commitObservable();
                    }
                    ReportSearchReq parameter = (ReportSearchReq) map.get("parameter");
                    int row = Integer.valueOf(map.get("row").toString());
                    if (row <= serverConfig.getRowLevel()) {
                        // fileDownload.setExt("csv");
                        Message<ReportSearchReq> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                        QueueProducer.sendMessage(message);
                    } else {
                        // fileDownload.setExt("zip");
                        Message<ReportSearchReq> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                        QueueProducer.sendMessage(message);
                    }

                    rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                    rc.next();
                }, throwable -> {
                    if (connectBackUp != null) {
                        connectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
            }
    }

    private Observable<Map> getObservable(RoutingContext rc, String userId, ReportSearchReq searchRequest, Map requestData, Map data, Integer total) {
        long date = new Date().getTime();
        String fileName = "pay-collect" + StringPool.UNDERLINE + "report" + StringPool.UNDERLINE;
        fileName += date;
        String fileHashName = "";
        data.put("parameter", searchRequest);
        data.put("file_name", fileName);
        data.put("row", total);
        try {
            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
        } catch (Exception e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("pay-collect_report");
        if (total <= serverConfig.getRowLevel()) {
            fileDownload.setExt("xls");
        } else {
            fileDownload.setExt("zip");
        }
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(searchRequest));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });
    }

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier(value = "downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier(value = "downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(FilePostReportHandlerImpl.class.getName());
}
