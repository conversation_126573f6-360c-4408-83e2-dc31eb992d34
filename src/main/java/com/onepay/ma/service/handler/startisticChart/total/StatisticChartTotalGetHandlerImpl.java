package com.onepay.ma.service.handler.startisticChart.total;

import com.onepay.ma.service.handler.startisticChart.international.StatisticChartIntGetHandlerImpl;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.chartStatistics.ChartStatistics;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:18 PM
 * To change this ma-service.
 */
@Component
public class StatisticChartTotalGetHandlerImpl implements Handler<RoutingContext> {
    @Autowired
    private StatisticChartService statisticChartService;
    private static Logger LOGGER = Logger.getLogger(StatisticChartIntGetHandlerImpl.class.getName());
    @Autowired
    private MerchantService merchantService;
    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                String userId = rc.get(ParamsPool.X_USER_ID);
                if (userId == null) {
                    LOGGER.log(Level.SEVERE, "[ F TRANSACTION GET ] => BODY PATCH IS EMPTY ");
                    throw IErrors.VALIDATION_ERROR;
                }
                JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

                JsonObject data =rc.getBodyAsJson();
                JsonArray mDatetf = data.getJsonArray("datetf");
                JsonArray merchantId = data.getJsonArray("merchantId");
                String sMerchantId = String.join(",",merchantId.getList());
                JsonArray cardType = data.getJsonArray("cardType");
                String sCardType= String.join(",", cardType.getList()).replace("|",",");
                JsonArray paygate = data.getJsonArray("payGate");
                JsonArray card = data.getJsonArray("card");
                JsonArray bank = data.getJsonArray("bank");
                String target = String.join(",",data.getJsonArray("target").getList());
                List arrCard = card.getList();
                List arrBank = bank.getList();
                List arr = paygate.getList();
                Map<String,String> mIn = new HashMap<>();
                mIn.put("merchant_id",sMerchantId);
                mIn.put("card_type",sCardType);
                mIn.put(ParamsPool.MERCHANT_ND,sMerchantId);
                mIn.put(ParamsPool.MERCHANT_QT,sMerchantId);
                if (arr.size()>0) {
                    if (sMerchantId != null && sMerchantId.length() > 0) {
                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return getListServiceBySource(connBackup, new ArrayList<>(), mIn, mDatetf, 0, arr, arrCard, arrBank,target);
                                }).subscribe(chartdata -> {
                            Map returnValue = new HashMap();
                            returnValue.put("chartStatistic", chartdata);
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
                    } else {
                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                .flatMap(connReadOnly -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                            .flatMap(connOnline -> {
                                                //get back up connection
                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                        .flatMap(connBackUp -> {
                                                            String pay1="";
                                                            if (arr.size()==1){
                                                                if (arr.get(0) == "ND"){
                                                                    pay1 = "domestic";
                                                                    }else{
                                                                    pay1 = "international";
                                                                }
                                                            }
                                                            if (arr.size()>1){
                                                                pay1 =  "domestic";
                                                            }
                                                            return merchantService.list(connOnline, connBackUp, "", userId, pay1).flatMap(merchants -> {
                                                                String pay2="";
                                                                if (arr.size()==1){
                                                                    if (arr.get(0) == "ND"){
                                                                        pay2 = "domestic";
                                                                    }else{
                                                                        pay2 = "international";
                                                                    }
                                                                }
                                                                if (arr.size()>1){
                                                                    pay2 =  "international";
                                                                }
                                                                return merchantService.list(connOnline, connBackUp, "", userId, pay2).flatMap(merchants2 -> {
                                                                    String merchantND = "";
                                                                    String merchantQT = "";
                                                                    if (arr.size()==1){
                                                                        if (arr.get(0).equals("ND")){
                                                                        for (Merchant mer : merchants) {
                                                                            merchantND += mer.getMerchant_id() + ",";
                                                                        }}else{
                                                                            for (Merchant mer : merchants) {
                                                                                merchantQT += mer.getMerchant_id() + ",";
                                                                            }
                                                                        }
                                                                    }else{

                                                                    for (Merchant mer : merchants) {
                                                                        merchantND += mer.getMerchant_id() + ",";
                                                                    }

                                                                    for (Merchant MerchantId1 : merchants2) {
                                                                            merchantQT += MerchantId1.getMerchant_name() + ",";
                                                                    }
                                                                    }

                                                                    mIn.put("merchant_nd", merchantND);
                                                                    mIn.put("merchant_qt", merchantQT);
                                                                    mIn.put("card_type", sCardType);
                                                                    return getListServiceBySource(connReadOnly, new ArrayList<>(), mIn, mDatetf, 0, arr, arrCard, arrBank,target);
                                                                });
                                                            });
                                                        });
                                            });
                                }).subscribe(chartdata -> {
                            Map returnValue = new HashMap();
                            returnValue.put("chartStatistic", chartdata);
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
                    }
                }else{
                    Map returnValue = new HashMap();

                    List<ChartStatistics> datanull = new ArrayList<>();
                    for (Object c : arrCard) {
                                ChartStatistics chartsv = new ChartStatistics();
                                chartsv.setCountS(0);
                                chartsv.setCountF(0);
                                chartsv.setPaygate("QT");
                                chartsv.setCardType(c.toString());
                                datanull.add(chartsv);
                            }
                    for (Object b : arrBank) {
                        ChartStatistics chartsv = new ChartStatistics();
                                chartsv.setCountS(0);
                                chartsv.setCountF(0);
                                chartsv.setPaygate("ND");
                                chartsv.setCardType(b.toString());
                        datanull.add(chartsv);
                            }
                    returnValue.put("chartStatistic",datanull);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                    rc.next();
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "StatisticChartInternationalGetHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);

    }
    private Observable<List<Map>> getListServiceBySource(SQLConnection sqlConn, List<Map> listChart, Map<String,String> sourceList, JsonArray mDatetf, int index,List arr,List arrCard,List arrBank,String target){
        if(mDatetf.size() <= 0){
            return null;
        }
        final int finalIndex = index;
        JsonObject date = mDatetf.getJsonObject(index);
        String datef = date.getString(ParamsPool.FROMDATE);
        String datet = date.getString(ParamsPool.TODATE);
        String paygate1="";
        if (arr.size()==1){paygate1 = (String) arr.get(0);}
        return  this.statisticChartService.listAll(sqlConn, sourceList,datef,datet,paygate1,arr,arrCard,arrBank,target).flatMap(serviceModels -> {
            Map dt = new HashMap();
            dt.put("data",datef +'-'+datet);
            dt.put("merchantQT",sourceList.get(ParamsPool.MERCHANT_QT));
            dt.put("merchantND",sourceList.get(ParamsPool.MERCHANT_ND));
            dt.put("charsv",serviceModels);
            listChart.add(dt);
            if(finalIndex  >= mDatetf.size() - 1){
                return Observable.just(listChart);
            }else{
                return getListServiceBySource(sqlConn, listChart, sourceList,mDatetf,finalIndex + 1,arr,arrCard,arrBank,target);
            }
        });
    }
}
