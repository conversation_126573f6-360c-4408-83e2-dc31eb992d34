package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.models.mpay.MpayTransactionQuery;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 28-Aug-17.
 */
@Component
public class MpayTransactionListHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ MPAY TRANSACTION LIST ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

        MpayTransactionQuery query = new MpayTransactionQuery();
        query.setPage(page);
        query.setMerchantId(merchantId);
        query.setPageSize(pageSize);

        Observable<NewBaseList<MpayTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            // find user by S-id
                                            return userService.getOneAm(connBackup, userId).flatMap(user -> {

                                                return merchantService.list(connOnline, connBackup, merchantId, userId, "mpay").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    query.setMerchantId(String.join(",", merchantIdList));

                                                    // find terminal permitted
//                                                        return this.userTerminalService.getTerminalMapByUserId(connBackup, user.getN_id()).flatMap(permitMerchantTerminalMap -> {
//
//                                                            // Case all merchant + terminal
//                                                            if (permitMerchantTerminalMap.get("ALL") != null || merchantId.equals(StringPool.BLANK)) {
//                                                                query.setTerminalId(StringPool.BLANK);
//                                                                return mpayTransactionService.list(connReadOnly, connOnline, query);
//                                                            }
//
//                                                            return this.terminalService.mapTerminalIdsByMerchant(connBackup, query.getMerchantId()).flatMap(merchantTermnalMap -> {
//
//                                                                // for each merchant
//                                                                String[] merchantList = merchantId.split(StringPool.COMMA);
//                                                                List<String> terminalIds = new ArrayList<>();
//                                                                for (String me : merchantList) {
//                                                                    List<String> permitTerminalIds = permitMerchantTerminalMap.get(me.trim());
//                                                                    if (!permitTerminalIds.isEmpty()) {
//
//                                                                        // case All terminal -> find all terminal Id
//                                                                        if (permitTerminalIds.get(0).equalsIgnoreCase("ALL")) {
//                                                                            // find all terminal existed base on merchant Id
//                                                                            terminalIds.addAll(merchantTermnalMap.get(me));
//                                                                        } else {
//                                                                            terminalIds.addAll(permitTerminalIds);
//                                                                        }
//                                                                    }
//                                                                }
//
////                                                                query.setTerminalId(String.join(",", terminalIds));
//                                                                query.setTerminalId(StringPool.BLANK);
//                                                                return this.mpayTransactionService.list(connReadOnly, connOnline, query);
//                                                            });
//                                                        });
                                                    query.setTerminalId(StringPool.BLANK);
                                                    return MpayTransactionService.list(connReadOnly, connOnline, query);
                                                });
                                            });
                                        });
                            });
                });

        obs.subscribe(transactions -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    @Autowired
    private UserService userService;


    @Autowired
    private MerchantService merchantService;

    private static Logger LOGGER = Logger.getLogger(MpayTransactionListHandler.class.getName());
}
