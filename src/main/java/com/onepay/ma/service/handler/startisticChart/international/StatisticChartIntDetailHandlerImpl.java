package com.onepay.ma.service.handler.startisticChart.international;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.IErrors;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:18 PM
 * To change this ma-service.
 */
@Component
public class StatisticChartIntDetailHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private StatisticChartService statisticChartService;

    @Autowired
    private MerchantService merchantService;

    private static Logger LOGGER = Logger.getLogger(StatisticChartIntDetailHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {

                String userId = rc.get(ParamsPool.X_USER_ID);
                if (userId == null) {
                    LOGGER.log(Level.SEVERE, "[ F TRANSACTION GET ] => BODY PATCH IS EMPTY ");
                    throw IErrors.VALIDATION_ERROR;
                }
                JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                JsonObject data =rc.getBodyAsJson();
                String datef = data.getString("datef");
                String datet = data.getString("datet");
                String sMerchantId = data.getString("merchantId");
                JsonArray cardType = data.getJsonArray("cardType");
                String sCardType= String.join(",", cardType.getList());
                Map<String,String> mIn = new HashMap<>();

                if (sMerchantId != null && (sMerchantId.equals("") == false) && sMerchantId.length()>0){
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                        .flatMap(connBackup -> {
                            mIn.put("merchant_id",sMerchantId);
                            mIn.put("card_type",sCardType);
                            return this.statisticChartService.listfail(connBackup,sMerchantId,sCardType,datef,datet,"QT","");
                        }).subscribe(chartdata -> {
                            Map returnValue = new HashMap();
                            returnValue.put("chartStatisticDetail", chartdata);
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
                }else{
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                            .flatMap(connReadOnly -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .flatMap(connOnline -> {
                                            //get back up connection
                                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                    .flatMap(connBackUp -> {
                                                        return merchantService.list(connOnline, connBackUp, "", userId, "international").flatMap(merchants -> {
                                                            String merchantid="";
                                                            for (Merchant mer : merchants){
                                                                merchantid += mer.getMerchant_id()+",";
                                                            }
                                                            mIn.put("merchant_id",merchantid);
                                                            mIn.put("card_type",sCardType);
                                                            return this.statisticChartService.listfail(connOnline,sMerchantId,sCardType,datef,datet,"QT","");
                                                        });

                                                    });
                                        });
                            }).subscribe(chartdata -> {
                                Map returnValue = new HashMap();
                    returnValue.put("chartStatisticDetail", chartdata);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                                rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "StatisticChartInternationalGetHandlerImpl: ", e);
                rc.fail(e);
            }
        }, false, null);
    }
    
}
