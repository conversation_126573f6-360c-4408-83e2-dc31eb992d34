package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.service.pay_collect.TransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class TransactionGetHandlerImpl implements Handler<RoutingContext> {


    private static final Logger LOGGER = Logger.getLogger(TransactionGetHandlerImpl.class.getName());

    @Autowired
    private TransactionService transactionService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ TRANSACTION GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                .flatMap(connPaycollect -> {
                    return transactionService.getTransactionById(connPaycollect, transactionId).flatMap(transaction -> {
                        return Observable.just(transaction);
                    });
                }).subscribe(transaction -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
    }
}
