package com.onepay.ma.service.handler.token;

import com.onepay.ma.service.handler.token.impl.AppTokenDeleteHandler;
import com.onepay.ma.service.handler.token.impl.AppTokenPostHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 22-Sep-17.
 */
@Component
public class AppTokenHandler {

    @Autowired
    private AppTokenPostHandler appTokenPostHandler;

    @Autowired
    private AppTokenDeleteHandler appTokenDeleteHandler;

    public Handler post() {
        return appTokenPostHandler;
    }
    public Handler delete() {
        return appTokenDeleteHandler;
    }
}
