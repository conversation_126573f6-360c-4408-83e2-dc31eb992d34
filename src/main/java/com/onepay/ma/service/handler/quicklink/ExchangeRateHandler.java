package com.onepay.ma.service.handler.quicklink;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;
import com.onepay.ma.service.handler.quicklink.Impl.ExChangeStatusHandlerImpl;
import com.onepay.ma.service.handler.quicklink.Impl.ExChangeTypeHandlerImpl;
import com.onepay.ma.service.handler.quicklink.Impl.ExUsertHandlerImpl;

@Component
public class ExchangeRateHandler {

    @Autowired
    ExChangeStatusHandlerImpl exChangeStatusHandlerImpl;

    @Autowired
    ExChangeTypeHandlerImpl exChangeTypeHandlerImpl;


    @Autowired
    ExUsertHandlerImpl exUsertHandlerImpl;

    public Handler changeState() {
        return exChangeStatusHandlerImpl;
    }

    public Handler upsertExchangeRate() {
        return exUsertHandlerImpl;
    }

    public Handler changeType() {
        return exChangeTypeHandlerImpl;
    }


}
