package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.handler.externalClient.OnePayoutClient;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_out.FundsTransHistoryDto;
import com.onepay.ma.service.models.pay_out.FundsTransSearchReq;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import groovy.json.JsonException;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.onepay.ma.service.util.Utils;
@Component
public class FundsTransHistoryPendingHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private MerchantService merchantService;

    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String partnerId = myEvent.get(ParamsPool.X_PARTNER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        JDBCClient clientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = myEvent.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = myEvent.get(ParamsPool.USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        JsonObject body = myEvent.getBodyAsJson();

        String fromDate = body.getString(ParamsPool.FROM_DATE);
        String toDate = body.getString(ParamsPool.TO_DATE);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date myFromDate;
        Date myToDate;
        try {
            myFromDate = dateFormat.parse(fromDate);
            myToDate = dateFormat.parse(toDate);
        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(myFromDate, myToDate);
        if (months > 12) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        // String receivedBankList = body.getString("receivedBankList");
        String merchantId = body.getString("merchantId");
        // String receivedAccountNumber = body.getString("receivedAccountNumber");
        String funds_transfer_id = body.getString("merchantTransId");
        String transaction_id = body.getString("transactionId");
        String merchantAccount = body.getString("merchantAccount");
        // String state = body.getString("state");
        Integer pageSize = body.getString("page_size") == null ? 20 : Integer.parseInt(body.getString("page_size"));
        Integer page = body.getString("page") == null ? 1 : (Integer.parseInt(body.getString("page")) + 1);
        if (pageSize > Utils.getPageSize()) {
            pageSize = Utils.getPageSize();
        }
        final int pageSizeFinal = pageSize;
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").map(merchants -> {
                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                            String merchantIdx = String.join(",", merchantIdList);
                            if (null == merchantIdx || merchantIdx.isEmpty()) {
                                throw IErrors.FORBIDDEN;
                            }
                            FundsTransSearchReq req = new FundsTransSearchReq(userId,xUserId, xRequestId, fromDate, toDate, merchantIdx, funds_transfer_id, transaction_id, merchantAccount, pageSizeFinal, page);
                            JsonObject jo = OnePayoutClient.getFundsTransHistoryForApproval(req);

                            List<FundsTransHistoryDto> fundsTransHistoryDtos = new ArrayList<>();
                            JsonArray jsonArray = jo.getJsonArray("list");

                            BaseList<FundsTransHistoryDto> bs =new BaseList<>();
                            if(jsonArray != null) {
                                jsonArray.forEach(obj-> {
                                    fundsTransHistoryDtos.add(bindFundsTransHistoryDtos((JsonObject)obj));
                                });

                            }
                            bs.setList(fundsTransHistoryDtos);
                            bs.setTotal_items(jo == null ? 0 :jo.getInteger("total_items"));

                            return bs;

                        });
                    });
                }).subscribe(a -> {
                    myEvent.put(ParamsPool.HANDLER_DATA_RESULT, a);
                    myEvent.next();
                }, throwable -> {
                    myEvent.fail(throwable);
                });
    }

    /**
     * convert data from result set to firm banking transactions
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private FundsTransHistoryDto bindFundsTransHistoryDtos(JsonObject rs) {
        FundsTransHistoryDto fundsTransHistoryDto = new FundsTransHistoryDto();
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("GMT"));
        try {
            fundsTransHistoryDto.setS_id(rs.getString("funds_transfer_id"));

            fundsTransHistoryDto.setMerchantId(rs.containsKey("merchant_id") ? rs.getString("merchant_id") : "");
            fundsTransHistoryDto.setMerchantAccount(rs.getString("account_id"));
            fundsTransHistoryDto.setMerchantName(null);
            fundsTransHistoryDto.setMerchantTransId(rs.getString("funds_transfer_id"));
            fundsTransHistoryDto.setBalanceBefore(rs.getString("before_account_balance") == null ? 0 : Double.parseDouble(rs.getString("before_account_balance")));
            fundsTransHistoryDto.setBalanceAfter(rs.getString("after_account_balance") == null ? 0 : Double.parseDouble(rs.getString("after_account_balance")));
            fundsTransHistoryDto.setBalanceBankBefore(rs.getString("before_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("before_bank_balance")));
            fundsTransHistoryDto.setBalanceBankAfter(rs.getString("after_bank_balance") == null ? 0 : Double.parseDouble(rs.getString("after_bank_balance")));
            fundsTransHistoryDto.setReceivedAccountNumber(rs.getString("account_number"));
            fundsTransHistoryDto.setReceivedAccountName(rs.getString("holder_name"));
            fundsTransHistoryDto.setReceivedCardNumber(rs.getString("card_number"));
            fundsTransHistoryDto.setTransactionId(rs.getString("transaction_id"));
            fundsTransHistoryDto.setTransactionDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
            fundsTransHistoryDto.setAmount(rs.getString("amount") == null ? 0 : Double.parseDouble(rs.getString("amount")));
            fundsTransHistoryDto.setRemark(rs.getString("remark"));
            fundsTransHistoryDto.setState(rs.getString("state"));
            fundsTransHistoryDto.setBankTransactionId(null);
            fundsTransHistoryDto.setBeneficiarySwiftCode(rs.getString("swift_code"));
            fundsTransHistoryDto.setBeneficiaryBankName(rs.getString("bank_name"));
            fundsTransHistoryDto.setBankCode(rs.getString("response_code"));
            fundsTransHistoryDto.setBankMsg(rs.getString("message"));

            fundsTransHistoryDto.setCreatedDate(rs.getString("create_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("create_time")).getTime()));
//            fundsTransHistoryDto.setUpdatedDate(rs.getString("update_time") == null ? null : new Timestamp(yyyyMMddTHHmmssZ.parse(rs.getString("update_time")).getTime()));
            if (rs.containsKey("check_approval")) {
                fundsTransHistoryDto.setCheckApproval(rs.getInteger("check_approval"));
            }
            if (rs.containsKey("check_operator")) {
                fundsTransHistoryDto.setCheckOperator(rs.getInteger("check_operator"));
            }
            if (rs.containsKey("meta_data") && null != rs.getString("meta_data")) {
                fundsTransHistoryDto.setMetadata(rs.getString("meta_data"));
                if (isJSONValid(rs.getString("meta_data"))) {
                    JsonObject metaData = new JsonObject(rs.getString("meta_data"));
                    if (metaData.containsKey("creator_id")) {
                        fundsTransHistoryDto.setCreator_id(metaData.getString("creator_id"));
                    }
                    if (metaData.containsKey("creator_name")) {
                        fundsTransHistoryDto.setCreator_name(metaData.getString("creator_name"));
                    }
                    if (metaData.containsKey("verifier_id")) {
                        fundsTransHistoryDto.setVerifier_id(metaData.getString("verifier_id"));
                    }
                    if (metaData.containsKey("verifier_name")) {
                        fundsTransHistoryDto.setVerifier_name(metaData.getString("verifier_name"));
                    }
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return fundsTransHistoryDto;
    }

    public boolean isJSONValid(String test) {
        try {
            new JsonObject(test);
        } catch (JsonException ex) {
            try {
                new JsonArray(test);
            } catch (JsonException ex1) {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }
}
