package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserSessionDeleteHandlerImpl;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/14/16.
 */
public interface UserSessionDeleteHandler extends Handler<RoutingContext> {
    static UserSessionDeleteHandlerImpl create(CacheGuava cacheGuava, OneAMConfig oneAMConfig){
        return new UserSessionDeleteHandlerImpl(cacheGuava, oneAMConfig);
    }
}
