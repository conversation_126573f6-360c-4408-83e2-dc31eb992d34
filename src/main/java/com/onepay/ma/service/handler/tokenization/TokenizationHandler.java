package com.onepay.ma.service.handler.tokenization;

import com.onepay.ma.service.handler.tokenization.impl.TokenizationGetHandlerImpl;
import com.onepay.ma.service.service.TokenService;
import com.onepay.ma.service.service.UserService;
import io.vertx.rxjava.ext.web.RoutingContext;
import io.vertx.core.Handler;

/**
 * Created by duongtv on 24/8/2017.
 */
public interface TokenizationHandler extends Handler<RoutingContext> {
    static TokenizationGetHandlerImpl create(TokenService tokenizationService, UserService userService){
        return new TokenizationGetHandlerImpl(tokenizationService, userService);
    }

}
