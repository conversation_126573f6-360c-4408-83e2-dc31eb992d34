package com.onepay.ma.service.handler.financial_report;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import oracle.jdbc.OracleTypes;
import rx.Observable;
import io.vertx.core.Handler;

public class FinancialReportHandler {

    private static Logger logger = Logger.getLogger(FinancialReportHandler.class.getName());

    public static final String GET_MERCHANT_PAYMENT_BY_USER_ID = "{call GET_MERCHANTS_PAYMENT_BY_USER(?,?,?,?)}";

    public static void getListMerchantPaymentByUserId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
        try {
            JDBCClient clientBackUp = ctx.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
            JsonObject param = ctx.getBodyAsJson();
            String userId = param.getString("userId");
            if (userId == null) {
                logger.log(Level.SEVERE,
                        ctx.get("request_uuid") + ": " + "[ FINANCIAL REPORT ERROR ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return getListMerchantPayment(connBackUp, userId);
                    }).subscribe(merchants -> { 
                        String merchantId = "".equals(param.getString("requestMerchants")) ? "" : param.getString("requestMerchants");
                        logger.log(Level.INFO, "requestMerchants: " + merchantId);
                        List<String> requestMerchantsList = new ArrayList<String>(Arrays.asList(merchantId.split(",")));
                        List<String> legalMerchantsList = merchants;
                        List<String> finalMerchantsList = new ArrayList<String>();

                        logger.log(Level.INFO, "Illigal Merchants: " + legalMerchantsList);
                        logger.log(Level.INFO, "Request Param Merchants: " + requestMerchantsList.size());
                        if ("".equals(merchantId)) {
                            logger.log(Level.INFO, "Select ALL merchants");
                            finalMerchantsList = legalMerchantsList;
                        } else {
                            if ("ALL".equals(legalMerchantsList.get(0))) {
                                finalMerchantsList = requestMerchantsList;
                            } else {
                                logger.log(Level.INFO, "Select some merchants");
                                requestMerchantsList.retainAll(legalMerchantsList);
                                finalMerchantsList = requestMerchantsList;
                            }
                        }
                        // String finalMerchants = String.join(",", finalMerchantsList);
                        logger.log(Level.INFO, "final Merchants: " + finalMerchantsList);

                        Map response = new HashMap();
                        response.put("legalMerchants", legalMerchantsList);
                        response.put("finalMerchants", finalMerchantsList);

                        ctx.put(ParamsPool.HANDLER_DATA_RESULT, response);
                        ctx.next();
                    }, throwable -> {
                        ctx.fail(throwable);
                    });
        } catch (Exception e) {
            logger.log(Level.WARNING, ctx.get("request_uuid") + ": " + "FINANCIAL REPORT ERROR: ", e);
            ctx.fail(e);
        }
        }, false, asyncResult -> {
        });
    }

    private static Observable<List<String>> getListMerchantPayment(SQLConnection connBackUp, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(GET_MERCHANT_PAYMENT_BY_USER_ID, inParams, outParams).map(result -> {
            List<String> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                String merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }
            return merchantList;
        });
    }

    private static String bindMerchant(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        // String merchantName = rs.getString("S_MERCHANT_NAME");
        // String currency = rs.getString("S_CURRENCY_CODE");
        // String type = rs.getString("S_TYPE");

        return merchantId;
    }

}
