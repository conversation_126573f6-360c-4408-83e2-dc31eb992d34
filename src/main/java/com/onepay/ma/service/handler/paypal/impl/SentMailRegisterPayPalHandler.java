package com.onepay.ma.service.handler.paypal.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.user.impl.UserResetPassHandlerImpl;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class SentMailRegisterPayPalHandler implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(UserResetPassHandlerImpl.class.getName());
    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ USER SENT MAIL ] => USER ID IS EMPTY " );
            throw IErrors.VALIDATION_ERROR;
        }
        JsonObject body = rc.getBodyAsJson();

        String ppAccount = body.getString("ppAccount");
        String merchantIds = body.getString("merchantIds");
        if (merchantIds == null || merchantIds.isEmpty()|| ppAccount == null || ppAccount.isEmpty() ) {
            LOGGER.log(Level.SEVERE, "[ USER SENT MAIL ] => BODY IS ERROR FORMAT " );
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackup = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    connectionO = connOnline;
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                            .flatMap(connBackup -> {
                                connectionB = connBackup;
                                return connectionB.setAutoCommitObservable(false).flatMap(aVoid -> {
                                    return connectionO.setAutoCommitObservable(false).flatMap(aVoid1 -> {
                                        return userServiceV2.getUserDataBySId(connectionB, userId).flatMap(userData -> {
                                            if (userData == null) {
                                                LOGGER.log(Level.SEVERE, "[ USER SENT MAIL ] => USER ID NOT EXIT IN DATABASE ");
                                                throw IErrors.INTERNAL_SERVER_ERROR;
                                            }
                                            return userServiceV2.getPartnerIdByUserId(connectionB, userId).flatMap(partner -> {
                                                if(partner == null) {
                                                    throw IErrors.INVALID_USER_PARTNER;
                                                }
                                                return emailService.sentMailRegisterPP(userData,partner,ppAccount);

                                            });
                                        });
                                    });
                                });
                            });
                }).subscribe(aVoid -> {
            if (connectionO != null) {
                connectionO.commitObservable();
            }
            if (connectionB != null) {
                connectionB.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
            rc.next();
        }, throwable -> {
            if (connectionO != null) {
                connectionO.rollbackObservable();
            }
            if (connectionB != null) {
                connectionB.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    SQLConnection connectionO;
    SQLConnection connectionB;

    @Autowired
    private UserServiceV2 userServiceV2;
    @Autowired
    private EmailService emailService;

}
