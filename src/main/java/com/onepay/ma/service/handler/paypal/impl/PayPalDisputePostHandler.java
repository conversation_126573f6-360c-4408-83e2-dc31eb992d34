package com.onepay.ma.service.handler.paypal.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.onepay.ma.service.util.DateTimeUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.externalClient.PayPalClient;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.paypal.*;
import com.onepay.ma.service.service.paypal.PaypalDisputeService;
import com.onepay.ma.service.service.paypal.PaypalMerchantService;
import com.onepay.ma.service.service.paypal.PaypalNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class PayPalDisputePostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        // LOGGER.log(Level.WARNING, "RECEIVE PAYPAL DISPUTE NOTIFICATION: " + rc.getBodyAsJson().encode());

        JDBCClient jdbcClientB = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient jdbcClientO = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JsonObject body = rc.getBodyAsJson();

        PaypalNotify notify = new PaypalNotify();
        notify.setId(body.getString("id"));
        notify.setEventType(body.getString("event_type"));
        notify.setResourceType(body.getString("resource_type"));
        notify.setSummary(body.getString("summary"));
        notify.setResource(body.getJsonObject("resource").encode());
        notify.setLinks(body.getJsonArray("links").encode());
        notify.setEventVersion(StringPool.BLANK);

        JsonObject transaction = body.getJsonObject("resource");
        JsonArray disputedTransactions = transaction.getJsonArray("disputed_transactions");
        JsonObject disputedTransactionInput = disputedTransactions.getJsonObject(0);
        JsonObject seller = disputedTransactionInput.getJsonObject("seller");
        //String payer_id = seller.getString("merchant_id");
        String seller_transaction_id = disputedTransactionInput.getString("seller_transaction_id");

        Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClientO), f -> f.dispose())
                .flatMap(connOnline -> {

        return Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClientB), f -> f.dispose())
                .flatMap(connBackup -> {
                    connectionB = connBackup;
                    return connectionB.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return paypalMerchantService.getppMerchantIdByTransNo(connOnline, seller_transaction_id).flatMap(payer_id -> {
                            LOGGER.log(Level.INFO, " PAYPAL DISPUTE payer_id =: " + payer_id);
                            return paypalNotifyService.insert(connBackup, notify).flatMap(aVoid1 -> {
                                JsonNode js = PayPalClient.getDisputeDetail(transaction.getString("dispute_id"), payer_id==null?seller.getString("merchant_id"):payer_id);

                                // Create Insert model
                                PaypalDispute dispute = new PaypalDispute();
                                dispute.setCreateDate(getTimestampByJsonNode(js,"create_time"));
                                dispute.setUpdateDate(getTimestampByJsonNode(js,"update_time"));
                                dispute.setId(getStringByJsonNode(js,"dispute_id"));
                                dispute.setReason(getStringByJsonNode(js,"reason"));
                                dispute.setStatus(getStringByJsonNode(js,"status"));
                                dispute.setContent(js.toString());
                                // Amount
                                Amount amout = new Amount();
                                if (js.get("dispute_amount") != null) {
                                    amout.setCurrency(getStringByJsonNode(js.get("dispute_amount"),"currency_code"));
                                    amout.setTotal(getDoubleByJsonNode(js.get("dispute_amount"),"value"));
                                }
                                dispute.setAmount(amout);
                                dispute.setSellerResponseDueDate(getTimestampByJsonNode(js,"seller_response_due_date"));
                                if (js.get("dispute_outcome") != null) {
                                    dispute.setOutcomeCode(getStringByJsonNode(js.get("dispute_outcome"), "outcome_code"));
                                }
                                //refundAmount
                                Amount refundAmount = new Amount();
                                if (js.get("offer") != null && js.get("offer").get("buyer_requested_amount") !=null ) {
                                    refundAmount.setCurrency(getStringByJsonNode(js.get("offer").get("buyer_requested_amount"),"currency_code"));
                                    refundAmount.setTotal(getDoubleByJsonNode(js.get("offer").get("buyer_requested_amount"),"value"));
                                }
                                dispute.setRefundAmount(refundAmount);
                                dispute.setDisputeLifeCycleStage(getStringByJsonNode(js,"dispute_life_cycle_stage"));
                                dispute.setDisputeChannel(getStringByJsonNode(js,"dispute_channel"));
                                dispute.setExtensions(getStringByJsonNode(js,"extensions"));

                                JsonNode disputedTransaction = js.get("disputed_transactions").get(0);
                                PaypalTransaction paypalTransaction = new PaypalTransaction();
                                paypalTransaction.setBuyerTransactionId(getStringByJsonNode(disputedTransaction,"buyer_transaction_id") );
                                paypalTransaction.setSellerTransactionId(getStringByJsonNode(disputedTransaction,"seller_transaction_id"));
                                paypalTransaction.setTransactionStatus(getStringByJsonNode(disputedTransaction,"transaction_status"));

                                Amount gross_amount = new Amount();
                                if (disputedTransaction.get("gross_amount") != null) {
                                    gross_amount.setCurrency(getStringByJsonNode(disputedTransaction.get("gross_amount"),"currency_code") );
                                    gross_amount.setTotal(getDoubleByJsonNode(disputedTransaction.get("gross_amount"),"value"));
                                }
                                paypalTransaction.setGrossAmount(gross_amount);

                                paypalTransaction.setInvoiceNumber(getStringByJsonNode(disputedTransaction,"invoice_number"));
                                paypalTransaction.setCustom(getStringByJsonNode(disputedTransaction,"custom"));

                                if (disputedTransaction.get("buyer") != null) {
                                    paypalTransaction.setBuyerEmail(getStringByJsonNode(disputedTransaction.get("buyer"),"email"));
                                    paypalTransaction.setBuyerPayerId(getStringByJsonNode(disputedTransaction.get("buyer"),"payer_id"));
                                    paypalTransaction.setBuyerName(getStringByJsonNode(disputedTransaction.get("buyer"),"name"));
                                }

                                if (disputedTransaction.get("seller") != null) {
                                    paypalTransaction.setSellerEmail(getStringByJsonNode(disputedTransaction.get("seller"),"email"));
                                    paypalTransaction.setSellerMerchantId(getStringByJsonNode(disputedTransaction.get("seller"),"merchant_id"));
                                    paypalTransaction.setSellerName(getStringByJsonNode(disputedTransaction.get("seller"),"name"));
                                }
                                paypalTransaction.setCreatDate(getTimestampByJsonNode(disputedTransaction,"create_time"));
                                paypalTransaction.setSellerProtection(getStringByJsonNode(disputedTransaction,"seller_protection_eligible"));

                                return paypalDisputeService.insertOrUpdateDisputeTransaction(connectionB, paypalTransaction, dispute.getId()).flatMap(aVoid3 -> {
                                    return paypalDisputeService.insertDispute(connectionB, dispute).flatMap(dispute_Id -> {
                                        JsonNode messagesJn = js.get("messages");
                                        if(messagesJn==null){
                                            LOGGER.log(Level.SEVERE, "messages is null");
                                            return Observable.just(null);
                                        }else {
                                            List<PaypalMessage> messages = new ArrayList<PaypalMessage>();
                                            for (int i = 0; i < messagesJn.size(); i++) {
                                                JsonNode jsonNode = messagesJn.get(i);
                                                PaypalMessage message = new PaypalMessage();
                                                message.setPosted_by(getStringByJsonNode(jsonNode, "posted_by"));
                                                message.setTimePosted(getTimestampByJsonNode(jsonNode, "time_posted"));
                                                message.setContent(getStringByJsonNode(jsonNode, "content"));
                                                messages.add(message);
                                            }
                                            return paypalDisputeService.insertListPaypalMessage(connectionB, messages, dispute.getId(), dispute_Id).flatMap(aVoid5 -> {
                                                try {
                                                    JsonNode evidenceJson = js.get("evidences").get(0);
                                                    PaypalEvidence evidence = new PaypalEvidence();
                                                    evidence.setDisputeId(dispute.getId());
                                                    evidence.setnDisputeId(dispute_Id);
                                                    evidence.setType(getStringByJsonNode(evidenceJson, "evidence_type"));
                                                    evidence.setSource(getStringByJsonNode(evidenceJson, "source"));
                                                    JsonNode tracking_infJn = evidenceJson.get("tracking_info");
                                                    List<PaypalTrackingInfo> listTrackingInfor = new ArrayList<PaypalTrackingInfo>();
                                                    for (int i = 0; i < tracking_infJn.size(); i++) {
                                                        JsonNode jsonNode = tracking_infJn.get(i);
                                                        PaypalTrackingInfo paypalTrackingInfo = new PaypalTrackingInfo();
                                                        paypalTrackingInfo.setCarrierName(getStringByJsonNode(jsonNode, "carrier_name"));
                                                        paypalTrackingInfo.setNumber(jsonNode.get("tracking_number") == null ? null : jsonNode.get("tracking_number").asInt());
                                                        listTrackingInfor.add(paypalTrackingInfo);
                                                    }
                                                    evidence.setTrackingInfo(listTrackingInfor);
                                                    return paypalDisputeService.insertPaypalEvidence(connectionB, evidence).flatMap(aVoid4 -> {
                                                        return Observable.just(null);
                                                    });

                                                } catch (Exception e) {
                                                    LOGGER.log(Level.WARNING, "evidences is null");
                                                }
                                                return Observable.just(null);
                                            });
                                        }
                                    });
                                });
                            });
                        });
                    });
                });

                }).subscribe(mpayNotificationBaseList -> {
            if (connectionB != null) {
                connectionB.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.CREATED.code());
            rc.next();
        }, throwable -> {
            if (connectionB != null) {
                connectionB.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }
    private String getStringByJsonNode (JsonNode js, String fieldName ){
        String result =null;
        if(js.get(fieldName) !=null){
            if(js.get(fieldName).toString().startsWith("\"")){
                result = js.get(fieldName).toString().substring(0);
            }
            if(js.get(fieldName).toString().endsWith("\"")){
                result = result.substring(1,result.length()-1);
            }
        }
        return result;
    }
    private Double getDoubleByJsonNode (JsonNode js, String fieldName ){
        return js.get(fieldName)==null?null:js.get(fieldName).asDouble();
    }

    private Timestamp getTimestampByJsonNode (JsonNode js, String fieldName ){
        JsonNode jsCreateDate = js.get(fieldName);
        return jsCreateDate == null ? null : DateTimeUtil.convertToGTM7(DateTimeUtil.convertStringtoTimestamp(jsCreateDate.asText(), DateTimeUtil.DateTemplate.YYYY_MM_DDTHHmmssZ));

    }



//    private Observable<List<JsonObject>> insertTransaction(SQLConnection connection, List<UserGroup> listData, String channelId, int index){
//        if(listData.size() <= 0){
//            return  Observable.just(listData);
//        }
//        UserGroup userGroup = listData.get(index);
//        final int finalIndex = index;
//        return Observable.just(userGroup).flatMap(approvalData -> {
//            //insert approval for user group
//            return  channelUserGroupService.insert(connection, channelId, userGroup.getUser_group_id()).flatMap(userGroup1 -> {
//                if(finalIndex >= listData.size() - 1){
//                    return Observable.just(listData);
//                }else{
//                    return insertChannelUserGroup(connection, listData, channelId, finalIndex + 1);
//                }
//            });
//        });
//
//    }

    @Autowired
    private PaypalNotifyService paypalNotifyService;
    @Autowired
    private PaypalDisputeService paypalDisputeService;
    @Autowired
    private PaypalMerchantService paypalMerchantService;

    private SQLConnection connectionB;

    private static Logger LOGGER = Logger.getLogger(PayPalDisputePostHandler.class.getName());
}
