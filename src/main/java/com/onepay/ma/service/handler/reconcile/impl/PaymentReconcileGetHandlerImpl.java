package com.onepay.ma.service.handler.reconcile.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportConvert;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationReportConvets;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class PaymentReconcileGetHandlerImpl implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);

        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ? 1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        if (months > 3) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        int bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));

        String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));
        String transType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));

        String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        ReconciliationQuery parameter = new ReconciliationQuery();
        parameter.setAcquirer(bankId);
        parameter.setFrom_date(fromDate);
        parameter.setTo_date(toDate);
        parameter.setInterval(interval);
        parameter.setCard_type(cardType);
        parameter.setTransaction_type(transType);
        parameter.setCurrency(currency);
        Observable<Map> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            //return merchant
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                Map reportData = new HashMap();
                                                String[] currencyList = {"VND", "USD"};
                                                if (!currency.isEmpty()) {
                                                    currencyList = new String[1];
                                                    currencyList[0] = currency;
                                                }

                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                parameter.setMerchant_id(String.join(",", merchantIdList));
                                                return getListReportByCurrency(connReadOnly, Arrays.asList(currencyList), reportData, parameter, 0);

                                            });
                                        });
                            });
                });
        obs.subscribe(reports -> {
            Map returnReport = new HashMap();
            returnReport.put("reports", reports);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    /**
     * get list report by currency data list
     *
     * @param sqlConnection
     * @param listCurrency
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getListReportByCurrency(SQLConnection sqlConnection, List<String> listCurrency, Map returnMap, ReconciliationQuery parameter, int index) {
        if (listCurrency.size() <= 0) {
            return Observable.just(returnMap);
        }
        String currency = listCurrency.get(index);
        final int finalIndex = index;
        return Observable.just(currency).flatMap(currencyData -> {
            parameter.setCurrency(currencyData);
            return reconcileService.listPayment(sqlConnection, parameter).flatMap(internationalReports -> {
                ReconciliationReportConvets report = new ReconciliationReportConvets();
                report.setItems(internationalReports);
                int totalTransCount = 0;
                int totalPurchaseCount = 0;
                int totalRefundCount = 0;
                int totalVoidCount = 0;
                int totalCaptureCount = 0;
                int totalAuthoriseCount = 0;
                double totalPurchaseAmount = 0;
                double totalRefundAmount = 0;
                double totalVoidAmount = 0;
                double totalCaptureAmount = 0;
                double totalAuthoriseAmount = 0;
                for (Reconciliation reportData : internationalReports) {

                    totalVoidCount += reportData.getCount_void();
                    totalRefundCount += reportData.getCount_refund();
                    totalPurchaseCount += reportData.getCount_purchase();
                    totalCaptureCount += reportData.getCount_capture();
                    totalAuthoriseCount += reportData.getCount_authorise();
                    totalTransCount += reportData.getCount_transaction();

                    totalPurchaseAmount += reportData.getTotal_purchase();
                    totalRefundAmount += reportData.getTotal_refund();
                    totalVoidAmount += reportData.getTotal_void();
                    totalCaptureAmount += reportData.getTotal_capture();
                    totalAuthoriseAmount += reportData.getTotal_authorise();
                }
                report.setTotal_transaction_count(totalTransCount);
                report.setTotal_authorise_count(totalAuthoriseCount);
                report.setTotal_capture_count(totalCaptureCount);
                report.setTotal_refund_count(totalRefundCount);
                report.setTotal_void_count(totalVoidCount);
                report.setTotal_purchase_count(totalPurchaseCount);

                report.setTotal_purchase_total(totalPurchaseAmount);
                report.setTotal_refund_total(totalRefundAmount);
                report.setTotal_void_total(totalVoidAmount);
                report.setTotal_capture_total(totalCaptureAmount);
                report.setTotal_authorise_total(totalAuthoriseAmount);

                if (internationalReports.size() > 0) {
                    report.setCurrency(internationalReports.get(0).getCurrency());
                }
                returnMap.put(currency, report);
                if (finalIndex >= listCurrency.size() - 1) {
                    return Observable.just(returnMap);
                } else {
                    return getListReportByCurrency(sqlConnection, listCurrency, returnMap, parameter, finalIndex + 1);
                }
            });
        });
    }

    @Autowired
    private ReconcileService reconcileService;
    @Autowired
    private MerchantService merchantService;
}