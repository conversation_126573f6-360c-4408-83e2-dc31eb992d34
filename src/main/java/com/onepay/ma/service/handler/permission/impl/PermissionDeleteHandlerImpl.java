package com.onepay.ma.service.handler.permission.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.permission.PermissionDeleteHandler;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/12/16.
 */
public class PermissionDeleteHandlerImpl implements PermissionDeleteHandler {

    public PermissionDeleteHandlerImpl(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String permissionId = rc.request().getParam("permissionId");

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(permissionId == null){
            LOGGER.log(Level.SEVERE, "[ PERMISSTION DELETE ] =>  INVALID PERMISSION ID");
            throw IErrors.VALIDATION_ERROR;
        }
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    connectionBackUp = connBackUp;
                    return connBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return permissionService.delete(connBackUp, Integer.valueOf(permissionId));
                    });
                }).subscribe(integer -> {
            if(connectionBackUp != null){
                connectionBackUp.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_RESULT, null);
            rc.put(ParamsPool.HANDLER_DATA_CODE, 200);
            rc.next();
        }, throwable -> {
            if(connectionBackUp != null){
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });



    }

    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();

    private PermissionService permissionService;

    private final static Logger LOGGER = Logger.getLogger(PermissionDeleteHandler.class.getName());
}
