package com.onepay.ma.service.handler.role.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.RoleParam;
import com.onepay.ma.service.handler.role.RolePostHandler;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public class RolePostHandlerImpl implements RolePostHandler {

    public RolePostHandlerImpl(RoleService roleService) {
        this.roleService = roleService;
    }

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        String json = rc.getBodyAsString();
        RoleParam parameter = gson.fromJson(json, RoleParam.class);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    connectionBackUp = connBackUp;
                    return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return roleService.insert(connectionBackUp, parameter);
                    });
                }).subscribe(roleData -> {
            if(connectionBackUp != null){
                connectionBackUp.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_RESULT, roleData);
            rc.next();
        }, throwable -> {
            if(connectionBackUp != null){
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });



    }


    private SQLConnection connectionBackUp;
    private final Gson gson = new Gson();
    private RoleService roleService;
}
