package com.onepay.ma.service.handler.refund.international.impl;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.models.InternationalTransactionHistory;
import com.onepay.ma.service.models.MerchantData;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.RefundConfig;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.VoidRefundData;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.service.installment.InstallmentService;
import com.onepay.ma.service.service.refundCapture.RefundCaptureService;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.OneCreditUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;
import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Created by huynguyen on 4/1/16.
 */
@Component
public class InternationalRefundPatchHandlerImpl implements Handler<RoutingContext> {

        @Override
        public void handle(RoutingContext rc) {
                String userId = rc.get(ParamsPool.X_USER_ID);
                if (userId == null) {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND PATCH ] => USER ID EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
                if (xRequestId == null) {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND PATCH ] =>  REQUEST EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                }
                // String xRequestId ="51fsdfsd6B5CBF9A31BDDEFDSFDSFE380D89" ;
                String transactionId = rc.request().getParam("transactionID");
                final HttpServerRequest request = rc.request();
                final HttpClient httpClient = rc.vertx().createHttpClient();
                JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                if (transactionId != null) {
                        String body = rc.getBodyAsString();
                        PatchRequest mapPatchRequest = gson.fromJson(body, PatchRequest.class);
                        if (mapPatchRequest == null) {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND PATCH ] => BODY EMPTY");
                                throw IErrors.VALIDATION_ERROR;
                        }

                        Observable<RefundApproval> obsBackUp = Observable
                                        .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                                return this.refundApprovalService.get(connBackUp, Integer.valueOf(transactionId))
                                                                .map(approval -> {
                                                                        if (approval.getStatus().compareTo(RefundApproval.Status.REQUEST.code) != 0
                                                                                        && approval.getStatus().compareTo(RefundApproval.Status.REQUEST_ONEPAY.code) != 0) {
                                                                                LOGGER.log(Level.SEVERE, "[ International REFUND  APPROVAL] => REFUND APPORVAL DONE ALREADY");
                                                                                throw IErrors.DUPLICATE_REFUND_ERROR;
                                                                        }
                                                                        // LOGGER.log(Level.INFO, "REFUND APPROVAL INFO:" + approval.toString());
                                                                        return approval;
                                                                });
                                        });

                        RefundCaptureService refundCaptureService = new RefundCaptureService(merchantApproval,
                                        refundApprovalService, userService, refundConfig, internationalTransactionService,
                                        internationalRefundService, merchantService);
                                        Boolean skipCallSynchronize = mapPatchRequest.isSkipCallSynchronize() == null ? true: mapPatchRequest.isSkipCallSynchronize();
                                        LOGGER.log(Level.INFO, "****** SKIP SYNC REAL TIME INTERNATIONAL = " + skipCallSynchronize);
                        if (mapPatchRequest.getPath().equals("/approve")) {
                                // DuongPXT authorize capture: n_type = 3 => refund capture
                                obsBackUp.subscribe(refundApproval -> {
                                        if (refundApproval.getN_type() == 3) {
                                                refundCaptureService.approve(rc, clientReadOnly, clientOnline, clientBackUp, transactionId,
                                                                xRequestId, userId, httpClient, obsBackUp, skipCallSynchronize);
                                        } else {
                                                approve(rc, clientReadOnly, clientOnline, clientBackUp, transactionId, xRequestId, userId,
                                                                httpClient, obsBackUp, skipCallSynchronize);
                                        }
                                });
                        } else if (mapPatchRequest.getPath().equals("/reject")) {
                                reject(rc, clientOnline, clientBackUp, userId, obsBackUp, skipCallSynchronize);
                        } else if (mapPatchRequest.getPath().equals("/manual")) {
                                obsBackUp.subscribe(refundApproval -> {
                                        // DuongPXT authorize capture: n_type = 3 => refund capture
                                        if (refundApproval.getN_type() == 3) {
                                                refundCaptureService.refundCaptureOnePAYManual(rc, body, clientReadOnly, clientOnline,
                                                                clientBackUp, transactionId, xRequestId, userId, httpClient, obsBackUp);
                                        } else {
                                                refundOnePAYManual(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionId,
                                                                xRequestId, userId, httpClient, obsBackUp);
                                        }
                                });

                        } else if (mapPatchRequest.getPath().equals("/reverseDue")) {
                                refundOnePAYReverseDue(rc, body, clientReadOnly, clientOnline, clientBackUp, transactionId, xRequestId,
                                                userId, httpClient, obsBackUp, skipCallSynchronize);
                        } else if (mapPatchRequest.getPath().equals("/void-refund")) {
                                voidRefund(rc, body, clientOnline, clientBackUp, transactionId, xRequestId, userId, httpClient, skipCallSynchronize);
                        } else {
                                throw IErrors.VALIDATION_ERROR;
                        }

                } else {
                        throw IErrors.VALIDATION_ERROR;
                }

        }

        private static String SAMSUNG_MERCHANT_APPROVALS = PropsUtil.get("samsung.merchant.approvals", "");


        private Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> getMerchantApproval(JDBCClient clientOnline, JDBCClient clientBackup, String transactionId, RefundApproval approval) {
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose()).flatMap(connBackUp -> {
                                return this.internationalTransactionService.get(connOnline, transactionId).flatMap(interTransaction -> {
                                        return this.internationalTransactionService.getContarctTypeMsp(connOnline, transactionId).map(contractType -> {
                                               if (Utils.isBigMerchant(interTransaction.getMerchant_id())) {
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                                if (approval.getStatus().equals(RefundApproval.Status.REQUEST_ONEPAY.code)) {
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                                if (contractType != null && contractType.equalsIgnoreCase("2B")) {
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                }
                                                if (contractType != null && contractType.equalsIgnoreCase("3B")) {
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                                int acquirerId = interTransaction.getAcquirer().getAcquirer_id();
                                                if (8 == acquirerId) {
                                                        // hotfix special case for samsung
                                                        List<String> samsungMerchantApprovals = Arrays.asList(SAMSUNG_MERCHANT_APPROVALS.split(","));
                                                        if (samsungMerchantApprovals.contains(interTransaction.getMerchant_id())) {
                                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                        } else {
                                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                        }
                                                } else if ((11 == acquirerId || 7 == acquirerId || 9 == acquirerId || 3 == acquirerId)) {
                                                        // acq =Sacombank. vpb onepay phai duyet
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                }

                                                if (interTransaction.getMerchant_id().startsWith("OP_") && !interTransaction.getMerchant_id().equals("OP_PREPAID")) {
                                                        return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                }

                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                        });
                                });
                        });
                });
        }

        private void approve(RoutingContext rc, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
                        String transactionId, String xRequestId, String userId, HttpClient httpClient, Observable<RefundApproval> obsBackUp, Boolean skipCallSynchronize) {
                
                obsBackUp.subscribe(approval -> {
                        Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> doubleConfirmationObs = getMerchantApproval(clientOnline, clientBackUp, approval.getOriginal_transaction_id(), approval);
                        doubleConfirmationObs.subscribe(isDoubleConfirmation -> {
                                if (isDoubleConfirmation == RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO) {
                                        LOGGER.log(Level.INFO, "****** INSERT REFUND REQUEST TO ONEPAY *****");
                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                        .flatMap(connBackUp -> {
                                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                                .flatMap(connOnline -> {
                                                                return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                                                        String note = new JsonObject(approval.getData()).getString("note");
                                                                        return this.refundApprovalService.insertRequestOnepay3(connBackUp, userId,
                                                                                        approval.getMerchant_id(), approval.getOriginal_transaction_id(),
                                                                                        approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                                        approval.getTransaction_id(), approval.getMerchant_transaction_ref(),
                                                                                        RefundApproval.Status.REQUEST_ONEPAY.code,
                                                                                        RefundData.Type.INTERNATIONAL.getValue(), note).flatMap(res -> {
                                                                                        return InstallmentService.rejectInstallment(connOnline, approval.getOriginal_transaction_id()).flatMap(res2 -> {
                                                                                                return Observable.just(res);
                                                                                        });
                                                                                        });
                                                                });
                                                                });
                                                        }).subscribe(stringObjectMap -> {
                                                                if (skipCallSynchronize == false) {
                                                                        // call ep dong bo international
                                                                        try {
                                                                                OneSchedClient.synchronizeInterRefundViewSync();
                                                                        } catch (Exception e) {
                                                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                                                        }
                                                                }
                                                                rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                                                rc.next();
                                                        }, throwable -> {
                                                                rc.fail(throwable);
                                                        });
                                } else if (isDoubleConfirmation == RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO) {
                                        Observable<MerchantData> merchantObs = getMerchantObs(clientReadOnly, clientOnline, clientBackUp, approval);

                                        merchantObs.subscribe(merchantData -> {
                                                Observable<Map> mapObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                                .flatMap(connOnline -> {
                                                                        if (merchantData == null) {
                                                                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => INVALID MERCHANT ");
                                                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp),
                                                                                                f -> f.dispose()).flatMap(connBackUp -> {
                                                                                                        return this.refundApprovalService.updateErrorRefundStatus(
                                                                                                                        connBackUp, Integer.parseInt(transactionId), 500,
                                                                                                                        "There was a validation issue with your request").flatMap(a -> {
                                                                                                                                throw IErrors.VALIDATION_ERROR;
                                                                                                                        });
                                                                                                });
                                                                        }
                                                                        return this.internationalTransactionService.get(connOnline, approval.getOriginal_transaction_id().toString()).flatMap(internationalTransaction -> {
                                                                                // // TODO: find and update refund record with refund
                                                                                return this.generalReportService.getPurchaseByRef(connOnline, internationalTransaction.getMerchant_id(), internationalTransaction.getOcMerTxnRef()).map(s -> {
                                                                                        Map m = new HashMap<>();
                                                                                        m.put("transaction", internationalTransaction);
                                                                                        m.put("merchant", merchantData);
                                                                                        m.put("ref", s);
                                                                                        return m;

                                                                                });

                                                                        });
                                                                });
                                                mapObs.subscribe(mp -> {
                                                        Map map = (Map) mp;
                                                        InternationalTransaction i = (InternationalTransaction) map.get("transaction");
                                                        MerchantData m = (MerchantData) map.get("merchant");
                                                        if (map.get("ref") == null) {
                                                                refundOnecredit(i, rc, clientReadOnly, clientOnline, clientBackUp, approval, userId, httpClient, m, skipCallSynchronize);
                                                        } else {
                                                                refundMsp(i, rc, clientReadOnly, clientOnline, clientBackUp, approval, userId, httpClient, m, skipCallSynchronize);
                                                        }
                                                }, throwable -> {
                                                        rc.fail(throwable);
                                                });
                                        }, throwable -> {
                                                rc.fail(throwable);
                                        });
                                } else {
                                        LOGGER.log(Level.SEVERE, "[ International REFUND  APPROVAL] => REFUND APPROVAL DONE ALREADY");
                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                        .flatMap(connBackUp -> {
                                                                return this.refundApprovalService.updateErrorRefundStatus(connBackUp, Integer.parseInt(transactionId), 500, "This approval has already approved|rejected");
                                                        }).subscribe();
                                        throw new ErrorException(500, "DUPLICATE_REFUND_ERROR", 500 + "|" + "This approval has already approved|rejected", "", "This approval has already approved|rejected");
                                }
                        });

                });

        }

        private void refundOnecredit(InternationalTransaction internationalTransaction, RoutingContext rc, JDBCClient clientReadOnly,
                        JDBCClient clientOnline, JDBCClient clientBackUp, RefundApproval refundData,
                        String userId, HttpClient httpClient, MerchantData merchantData, Boolean skipCallSynchronize) {
                String vpcCommand = "refund";
                /* neu la GD cua Vietcombank + nguyen mon
                    + Giao dich tien USD: >= 5.000 USD
                    + Giao dich tien VND: >= 100.000.000 VND
                    --> vpcCommand là refundWithHold
                */
                if (internationalTransaction != null && internationalTransaction.getAcquirer().getAcquirer_id() == 1 &&
                                internationalTransaction.getAmount().getTotal() == refundData.getAmount().getTotal() &&
                                (("USD".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getAmount().getTotal() >= 5000) ||
                                                ("VND".equals(internationalTransaction.getAmount().getCurrency()) && refundData.getAmount().getTotal() >= *********))) {
                        vpcCommand = "refundWithHold";
                }
                String note = refundData.getData() == null ? "" : new JsonObject(refundData.getData()).getString("note");
                Map data = OneCreditUtil.oneCreditData("REFUND", vpcCommand, refundData.getMerchant_id(), merchantData.getAccessCode(), refundData.getMerchant_transaction_ref(), internationalTransaction.getOcMerTxnRef(), refundData.getAmount().getTotal(), userId, merchantData.getHashCode(), "2", note);


                Date dt = new Date();
                data.put("timeout", String.valueOf(refundConfig.getTimeout()));
                data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
                JsonObject jsonObject = new JsonObject(data);

                HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());
                clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
                clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
                clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jsonObject.toString().getBytes(UTF_8), refundConfig.getSecureCode()));
                Buffer buffer = Buffer.buffer(jsonObject.toString());
                // LOGGER.log(Level.INFO, "CLIENT REFUND RREQUEST DATA" + StringPool.SPACE + StringPool.COLON + jsonObject.toString());
                // LOGGER.log(Level.INFO, "MERCHANT DATA: " + StringPool.SPACE + StringPool.COLON + merchantData.getHashCode() + " / " + merchantData.getAccessCode());
                clientRequestToObs(rc, userId, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp, clientRequest, refundData, skipCallSynchronize);
                clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
                clientRequest.end(buffer);

        }

        private void refundMsp(InternationalTransaction internationalTransaction, RoutingContext rc, JDBCClient clientReadOnly,
                        JDBCClient clientOnline, JDBCClient clientBackUp, RefundApproval refundData,
                        String userId, HttpClient httpClient, MerchantData merchantData, Boolean skipCallSynchronize) {
                try {

                        JsonObject mapVpc = MSPClient.refundPurchaseQT(internationalTransaction.getMerchant_id(), internationalTransaction.getTransaction_reference(),
                                        refundData.getMerchant_transaction_ref(), refundData.getAmount().getTotal(), merchantData.getAccessCode(), merchantData.getHashCode(), userId, refundData.getNote());
                        clientRequestToObs(rc, userId, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp, mapVpc, refundData, skipCallSynchronize);
                } catch (Exception ex) {
                        LOGGER.log(Level.INFO, "===================================== END REFUND ==================================");
                        rc.fail(ex);
                }
        }

        private void reject(RoutingContext rc, JDBCClient clientOnline, JDBCClient clientBackUp, String userId,
                        Observable<RefundApproval> obsBackUp, Boolean skipCallSynchronize) {

                obsBackUp.subscribe(approval -> {

                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                                        return this.refundApprovalService.insertReject(connBackUp, userId,
                                                        approval.getMerchant_id(), approval.getOriginal_transaction_id(),
                                                        approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                        approval.getTransaction_id(), RefundData.Type.INTERNATIONAL.getValue(),
                                                        approval.getMerchant_transaction_ref()).flatMap(approval1 -> {
                                                                return this.internationalRefundService.get(connOnline, connBackUp,
                                                                                approval.getTransaction_id().toString()).flatMap(InternationalRefund -> {
                                                                                        return userService.get(connBackUp, InternationalRefund.getOperator()).map(userData -> {
                                                                                                if (userData != null) {
                                                                                                        InternationalRefund.setOperator(userData.getEmail());
                                                                                                }
                                                                                                return InternationalRefund;
                                                                                        });
                                                                                });
                                                        });
                                });
                        }).subscribe(InternationalRefund -> {
                                if (skipCallSynchronize == false) {
                                        // call ep dong bo international
                                        try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                        } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                        }
                                }
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, InternationalRefund);
                                rc.next();
                        }, throwable -> {
                                rc.fail(throwable);
                        });
                }, throwable -> {
                        rc.fail(throwable);
                });
        }

        private void refundOnePAYManual(RoutingContext rc, String body, JDBCClient clientReadOnly, JDBCClient clientOnline,
                        JDBCClient clientBackUp, String transactionId, String xRequestId, String userId, HttpClient httpClient,
                        Observable<RefundApproval> obsBackUp) {
                obsBackUp.subscribe(refundApproval -> {
                        Observable<Double> obsAmountRefund = getAmountRefund(refundApproval.getOriginal_transaction_id().toString(), clientReadOnly, clientOnline, clientBackUp, refundApproval);
                        // begin refund data
                        obsAmountRefund.subscribe(amount -> {
                                Observable<MerchantData> merchantObs = getMerchantObs(clientReadOnly, clientOnline, clientBackUp, refundApproval);

                                merchantObs.subscribe(merchantData -> {
                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                                                        onlineCon = connOnline;
                                                        // if (merchantData == null) {
                                                        // LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND ONEPAY MANUAL
                                                        // TRANSACTION ] => INVALID MERCHANT
                                                        // ");
                                                        // throw IErrors.VALIDATION_ERROR;
                                                        // }
                                                        // return onlineCon.setAutoCommitObservable(false).flatMap(aVoid -> {

                                                                return this.internationalTransactionService.get(onlineCon, refundApproval.getOriginal_transaction_id().toString()).flatMap(internationalTransaction -> {
                                                                        String requestId = xRequestId;
                                                                        String refundReference = refundApproval.getMerchant_id() + "_" + requestId;
                                                                        if (refundReference.length() > 32) {
                                                                                int largerInt = refundReference.length() - 32 + 1;
                                                                                requestId = requestId.substring(0, requestId.length() - largerInt);
                                                                                refundReference = refundApproval.getMerchant_id() + "_" + requestId;
                                                                        }
                                                                        final String transRef = refundReference;
                                                                        return this.internationalTransactionService.insertOnePAYManual(onlineCon, Integer.valueOf(refundApproval.getOriginal_transaction_id()),
                                                                                        amount, refundReference, internationalTransaction.getMerchant_id()).flatMap(integer -> {

                                                                                                return internationalTransactionService.getHistoryByMerchantRef(onlineCon, refundApproval.getOriginal_transaction_id().toString(),
                                                                                                                transRef, internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                                                                                        return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).flatMap(userData -> {
                                                                                                                                return this.refundApprovalService.insertApprove(connBackUp1, userId, refundApproval.getMerchant_id(),
                                                                                                                                                refundApproval.getOriginal_transaction_id(), refundApproval.getAmount().getTotal(),
                                                                                                                                                refundApproval.getAmount().getCurrency(),
                                                                                                                                                Integer.valueOf(transactionId), refundApproval.getMerchant_transaction_ref(),
                                                                                                                                                400, RefundData.Type.INTERNATIONAL.getValue()).flatMap(approval1 -> {
                                                                                                                                                        return this.internationalRefundService.get(connOnline, connBackUp1, refundApproval.getTransaction_id().toString()).map(InternationalRefund -> {

                                                                                                                                                                if (userData != null) {
                                                                                                                                                                        InternationalRefund.setOperator(userData.getEmail());
                                                                                                                                                                }
                                                                                                                                                                return internationalTransactionHistory;
                                                                                                                                                        });
                                                                                                                                                });
                                                                                                                        });
                                                                                                                });
                                                                                        });
                                                                });
                                                        // });
                                                });
                                        }).subscribe(internationalTransactionHistory -> {
                                                if (onlineCon != null) {
                                                        onlineCon.commitObservable();
                                                }
                                                try {
                                                        OneSchedClient.synchronizeInterRefundViewSync();
                                                } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                                                }
                                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                                rc.next();
                                        }, throwable -> {
                                                if (onlineCon != null) {
                                                        onlineCon.rollbackObservable();
                                                }
                                                if (backUpCon != null) {
                                                        backUpCon.rollbackObservable();
                                                }
                                                rc.fail(throwable);
                                        });

                                }, throwable -> {
                                        rc.fail(throwable);
                                });
                        }, throwable -> {
                                rc.fail(throwable);
                        });
                });

        }

        private void refundOnePAYReverseDue(RoutingContext rc, String body, JDBCClient clientReadOnly,
                        JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, String xRequestId, String userId,
                        HttpClient httpClient, Observable<RefundApproval> obsBackUp, Boolean isNeedSynchronize) {
                obsBackUp.subscribe(refundApproval -> {
                        Observable<Double> obsAmountRefund = getAmountRefund(refundApproval.getOriginal_transaction_id().toString(), clientReadOnly, clientOnline, clientBackUp, refundApproval);
                        // begin refund data
                        obsAmountRefund.subscribe(amount -> {
                                Observable<MerchantData> merchantObs = getMerchantObs(clientReadOnly, clientOnline, clientBackUp, refundApproval);

                                merchantObs.subscribe(merchantData -> {
                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                                                backUpCon = connBackUp1;
                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                                                        onlineCon = connOnline;
                                                        if (merchantData == null) {
                                                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND ONEPAY REVERSE DUE TO DISPUTE TRANSACTION ] => INVALID MERCHANT ");
                                                                throw IErrors.VALIDATION_ERROR;
                                                        }
                                                        return onlineCon.setAutoCommitObservable(false).flatMap(aVoid -> {

                                                                return this.internationalTransactionService.get(onlineCon, refundApproval.getOriginal_transaction_id().toString()).flatMap(internationalTransaction -> {
                                                                        String requestId = xRequestId;
                                                                        String refundReference = refundApproval.getMerchant_id()
                                                                                        + "_" + requestId;
                                                                        if (refundReference.length() > 32) {
                                                                                int largerInt = refundReference.length() - 32 + 1;
                                                                                requestId = requestId.substring(0, requestId.length() - largerInt);
                                                                                refundReference = refundApproval.getMerchant_id() + "_" + requestId;
                                                                        }
                                                                        final String transRef = refundReference;
                                                                        return this.internationalTransactionService.insertReverseDue(onlineCon, Integer.valueOf(refundApproval.getOriginal_transaction_id()), amount, refundReference, internationalTransaction.getMerchant_id()).flatMap(integer -> {
                                                                                return internationalTransactionService.getHistoryByMerchantRef(onlineCon, refundApproval.getOriginal_transaction_id().toString(), transRef, internationalTransaction.getMerchant_id()).flatMap(internationalTransactionHistory -> {
                                                                                        return userService.get(backUpCon, internationalTransactionHistory.getOperator_id()).flatMap(userData -> {
                                                                                                return this.refundApprovalService.insertApprove(connBackUp1, userId, refundApproval.getMerchant_id(), refundApproval.getOriginal_transaction_id(), refundApproval.getAmount().getTotal(), refundApproval.getAmount().getCurrency(), Integer.valueOf(transactionId), refundApproval.getMerchant_transaction_ref(), 400, RefundData.Type.INTERNATIONAL.getValue()).flatMap(approval1 -> {
                                                                                                        return this.internationalRefundService.get(connOnline, backUpCon,
                                                                                                                        refundApproval.getTransaction_id().toString()).map(InternationalRefund -> {

                                                                                                                                if (userData != null) {
                                                                                                                                        InternationalRefund.setOperator(userData.getEmail());
                                                                                                                                }
                                                                                                                                return internationalTransactionHistory;
                                                                                                                        });
                                                                                                });
                                                                                        });
                                                                                });
                                                                        });
                                                                });
                                                        });
                                                });
                                        }).subscribe(internationalTransactionHistory -> {
                                                if (onlineCon != null) {
                                                        onlineCon.commitObservable();
                                                }
                                                if (backUpCon != null) {
                                                        backUpCon.commitObservable();
                                                }
                                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                                rc.next();
                                        }, throwable -> {
                                                if (onlineCon != null) {
                                                        onlineCon.rollbackObservable();
                                                }
                                                if (backUpCon != null) {
                                                        backUpCon.rollbackObservable();
                                                }
                                                rc.fail(throwable);
                                        });

                                }, throwable -> {
                                        rc.fail(throwable);
                                });
                        }, throwable -> {
                                rc.fail(throwable);
                        });
                });

        }

        private Observable<Double> getAmountRefund(String transactionId, JDBCClient clientReadOnly, JDBCClient clientOnline,
                        JDBCClient clientBackUp, RefundApproval refundApproval) {
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                .flatMap(connReadOnly -> {
                                        // get online connection
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                        .flatMap(connOnline -> {
                                                                // get back up connection
                                                                return Observable
                                                                                .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                                .flatMap(connBackup -> {
                                                                                        return internationalTransactionService
                                                                                                        .listHistory(connOnline, connBackup, transactionId)
                                                                                                        .map(internationalTransactionHistories -> {
                                                                                                                double remainAmt = 0;
                                                                                                                double refundAmt = refundApproval.getAmount().getTotal();
                                                                                                                for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                                                                                        // Case purchase ->
                                                                                                                        if (his.getTransaction_type().toUpperCase()
                                                                                                                                        .equals("PURCHASE")) {
                                                                                                                                remainAmt += his.getAmount().getTotal();
                                                                                                                        } else if ((his.getTransaction_type().equalsIgnoreCase(
                                                                                                                                        RefundApproval.Status.REQUEST.content)
                                                                                                                                        && Integer.valueOf(his
                                                                                                                                                        .getStatus()) != RefundApproval.Status.REQUEST.code)
                                                                                                                                        || (his.getAdvance_status() != null && (his
                                                                                                                                                        .getAdvance_status()
                                                                                                                                                        .equalsIgnoreCase("Rejected")
                                                                                                                                                        || his.getAdvance_status().equalsIgnoreCase(
                                                                                                                                                                        "Merchant Rejected")
                                                                                                                                                        || his.getAdvance_status().equalsIgnoreCase(
                                                                                                                                                                        "OnePAY Rejected")))
                                                                                                                                        || (his.getTransaction_type().equalsIgnoreCase(
                                                                                                                                                        RefundApproval.Status.APPROVED.content))) {
                                                                                                                                // Do nothing
                                                                                                                        } else {
                                                                                                                                if (his.getAdvance_status()
                                                                                                                                                .equalsIgnoreCase("Successful"))
                                                                                                                                        remainAmt -= his.getAmount().getTotal();
                                                                                                                        }
                                                                                                                }
                                                                                                                String str = String.format("%1.2f", remainAmt);
                                                                                                                remainAmt = Double.valueOf(str);
                                                                                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                                                                                        return refundAmt;
                                                                                                                } else {
                                                                                                                        LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : "
                                                                                                                                        + remainAmt + " DESCRIPTION : " + refundAmt);
                                                                                                                        throw IErrors.AMOUNT_REFUND_ERROR;
                                                                                                                }
                                                                                                        });
                                                                                });
                                                        });
                                });
        }



        private void voidRefund(RoutingContext rc, String body, JDBCClient clientOnline, JDBCClient clientBackUp,
                        String transactionId, String xRequestId, String userId, HttpClient httpClient, Boolean skipCallSynchronize) {
                PatchRequest<VoidRefundData> voidRefundDataPatchRequest = gson.fromJson(body,
                                new TypeToken<PatchRequest<VoidRefundData>>() {}.getType());
                String originalTransaction = voidRefundDataPatchRequest.getValue().getOriginal_transaction_id();
                if (originalTransaction == null) {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID REFUND PATCH ] => ORIGINAL TRANSACTION ID EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                }
                Observable<Map> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                        // get international transaction
                                        return internationalTransactionService.get(connOnline, transactionId)
                                                        .flatMap(internationalTransaction -> {
                                                                if (internationalTransaction == null) {
                                                                        LOGGER.log(Level.SEVERE,
                                                                                        "[ INTERNATIONAL VOID REFUND PATCH ] => NOT FOUND TRANSACTION");
                                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                                }
                                                                if (!internationalTransaction.isCan_void()) {
                                                                        // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                                                        LOGGER.log(Level.SEVERE,
                                                                                        "[ INTERNATIONAL VOID REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                                                        throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                                                        // throw new VoidPurchaseException("Transaction is out of date.", 500);
                                                                }
                                                                LOGGER.info("***** VOID REFUND CHECK BEGIN ****");
                                                                Calendar cal = Calendar.getInstance();
                                                                Timestamp date = internationalTransaction.getTransaction_time();
                                                                cal.setTime(date);
                                                                cal.set(Calendar.MILLISECOND, 0);
                                                                cal.set(Calendar.SECOND, 0);
                                                                cal.set(Calendar.MINUTE, 0);
                                                                Date d_to = null;
                                                                if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
                                                                        cal.set(Calendar.HOUR_OF_DAY, 17);
                                                                } else {
                                                                        cal.set(Calendar.HOUR_OF_DAY, 17);
                                                                        cal.add(Calendar.DATE, 1);
                                                                }
                                                                d_to = cal.getTime();

                                                                if ((new Date()).getTime() > d_to.getTime()) {
                                                                        // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                                                        // LOGGER.info("--------------- VOID REFUND CHECK END ------------------- ");
                                                                        LOGGER.log(Level.SEVERE,
                                                                                        "[ INTERNATIONAL REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                                                        throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                                                        // throw new VoidPurchaseException("Transaction is out of date.", 500);
                                                                } else {
                                                                        Map returnMap = new HashMap();
                                                                        returnMap.put("transaction", internationalTransaction);

                                                                        return merchantService
                                                                                        .getData(connOnline, internationalTransaction.getMerchant_id())
                                                                                        .map(merchantData -> {
                                                                                                returnMap.put("merchant", merchantData);
                                                                                                return returnMap;
                                                                                        });
                                                                }

                                                        });
                                });
                obs.subscribe(map -> {
                        LOGGER.log(Level.INFO,
                                        "===================================== BEGIN VOID PURCHASE ==================================");
                        InternationalTransaction internationalTransaction = (InternationalTransaction) map.get("transaction");
                        MerchantData merchantData = (MerchantData) map.get("merchant");
                        String requestId = xRequestId;
                        String voidReference = "VOID_R_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                        if (voidReference.length() > 32) {
                                int largerInt = voidReference.length() - 32 + 1;
                                requestId = requestId.substring(0, requestId.length() - largerInt);
                                voidReference = "VOID_R_" + internationalTransaction.getMerchant_id() + "_" + requestId;
                        }
                        Map data = OneCreditUtil.oneCreditData("VOID_REFUND", "voidRefund",
                                        internationalTransaction.getMerchant_id(), merchantData.getAccessCode(), voidReference,
                                        internationalTransaction.getOcMerTxnRef(),
                                        internationalTransaction.getAmount().getTotal(), userId, merchantData.getHashCode(), "2", "");
                        Date dt = new Date();
                        data.put("timeout", String.valueOf(refundConfig.getTimeout()));
                        data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));
                        JsonObject jsonObject = new JsonObject(data);
                        HttpClientRequest clientRequest = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCreditUrl());

                        clientRequest.putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, "application/json");
                        clientRequest.putHeader(HttpHeaders.USER_AGENT + StringPool.BLANK, "Onecredit HTTP Client");
                        clientRequest.putHeader(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(
                                        jsonObject.toString().getBytes(StandardCharsets.UTF_8), refundConfig.getSecureCode()));
                        Buffer buffer = Buffer.buffer(jsonObject.toString());

                        clientRequest.toObservable().subscribe(response -> {
                                int statusCode = response.statusCode();
                                // call ep dong bo international
                                if (skipCallSynchronize == false) {
                                        try {
                                                OneSchedClient.synchronizeInterVoidViewSync();
                                        } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo void error " + e.getMessage());
                                        }
                                }
                                if (statusCode == HttpResponseStatus.OK.code()) {
                                        response.bodyHandler(buffer1 -> {
                                                String json = buffer1.toString("UTF-8");
                                                LOGGER.log(Level.INFO,
                                                                "===================================== OneCredit Response =====================================");
                                                LOGGER.log(Level.INFO, Utils.mask(json) );
                                                LOGGER.log(Level.INFO,
                                                                "===================================== OneCredit Response =====================================");
                                                Map mapVpc = gson.fromJson(json, Map.class);
                                                if (mapVpc != null && mapVpc.get("vpc_response") != null) {
                                                        Map dataResp = (Map) mapVpc.get("vpc_response");
                                                        if ("0".equals(dataResp.get("vpc_TxnResponseCode") + "")) {
                                                                Observable<InternationalTransactionHistory> historyObs = Observable
                                                                                .using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                                                .flatMap(connOnline -> {
                                                                                        return Observable.using(SQLConnectionFactory::new,
                                                                                                        f -> f.create(clientBackUp), f -> f.dispose())
                                                                                                        .flatMap(connBackUp -> {
                                                                                                                return internationalTransactionService
                                                                                                                                .getHistoryByMerchantRef(connOnline,
                                                                                                                                                originalTransaction,
                                                                                                                                                dataResp.get("vpc_MerchTxnRef").toString(),
                                                                                                                                                internationalTransaction.getMerchant_id())
                                                                                                                                .flatMap(internationalTransactionHistory -> {
                                                                                                                                        return userService
                                                                                                                                                        .get(connBackUp,
                                                                                                                                                                        internationalTransactionHistory
                                                                                                                                                                                        .getOperator_id())
                                                                                                                                                        .map(userData -> {
                                                                                                                                                                if (userData != null) {
                                                                                                                                                                        internationalTransactionHistory
                                                                                                                                                                                        .setOperator_id(userData
                                                                                                                                                                                                        .getEmail());
                                                                                                                                                                }
                                                                                                                                                                return internationalTransactionHistory;
                                                                                                                                                        });
                                                                                                                                });
                                                                                                        });
                                                                                });
                                                                historyObs.subscribe(internationalTransactionHistory -> {
                                                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                                                        rc.next();
                                                                }, throwable -> {
                                                                        rc.fail(throwable);
                                                                });

                                                        } else {
                                                                LOGGER.log(Level.SEVERE,
                                                                                "[ INTERNATIONAL VOID REFUND PATCH FAILED ] => VOID REFUND TRANSACTION CODE:"
                                                                                                + Utils.mask(dataResp));
                                                                throw IErrors.VOID_REFUND_FAILED;
                                                        }

                                                } else {
                                                        ;
                                                        LOGGER.log(Level.SEVERE,
                                                                        "[ INTERNATIONAL VOID REFUND PATCH FAILED ] => VOID REFUND TRANSACTION CODE:"
                                                                                        + Utils.mask(mapVpc));
                                                        throw IErrors.VOID_REFUND_FAILED;
                                                }

                                        });

                                        response.exceptionHandler(throwable -> {
                                                rc.fail(throwable);
                                        });
                                } else {
                                        LOGGER.log(Level.SEVERE,
                                                        "[ INTERNATIONAL VOID REFUND PATCH FAILED ] => VOID REFUND TRANSACTION CODE:"
                                                                        + Utils.mask(response));
                                        throw IErrors.VOID_REFUND_FAILED;
                                }
                        }, throwable -> {
                                rc.fail(throwable);
                        });
                        clientRequest.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, String.valueOf(buffer.length()));
                        clientRequest.end(buffer);
                }, throwable -> {
                        LOGGER.log(Level.INFO,
                                        "===================================== END VOID REFUND ==================================");
                        rc.fail(throwable);
                });

        }


        private void voidRefundMsp(InternationalTransaction internationalTransaction, RoutingContext rc,
                        JDBCClient clientOnline, JDBCClient clientBackUp, String voidReference,
                        // RefundApproval refundData,
                        String userId, HttpClient httpClient, MerchantData merchantData, String originalTransaction) {
                try {

                        JsonObject mapVpc = MSPClient.voidRefund(internationalTransaction.getMerchant_id(), internationalTransaction.getTransaction_reference(),
                                voidReference, internationalTransaction.getAmount().getTotal(), merchantData.getAccessCode(), merchantData.getHashCode(), userId);
                        clientRequestToObsVoidRefund(rc, userId, internationalTransaction.getTransaction_id() + "", clientOnline, clientBackUp, mapVpc, originalTransaction, internationalTransaction);

                } catch (Exception ex) {
                        LOGGER.log(Level.INFO, "===================================== END VOID PURCHASE ==================================");
                        rc.fail(ex);
                }
        }

        private Observable<MerchantData> getMerchantObs(JDBCClient clientReadOnly, JDBCClient clientOnline,
                        JDBCClient clientBackUp, RefundApproval approval) {
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose()).flatMap(connReadOnly -> {
                                // get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackup -> {
                                        return internationalTransactionService.listHistory(connOnline, connBackup, approval.getOriginal_transaction_id().toString()).flatMap(internationalTransactionHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = approval.getAmount().getTotal();
                                                for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                        // Case purchase ->
                                                        if ("Purchase".equalsIgnoreCase(his.getTransaction_type()) || ("Void Refund".equalsIgnoreCase(his.getTransaction_type()) && "Successful".equalsIgnoreCase(his.getAdvance_status()))) {
                                                                remainAmt += his.getAmount().getTotal();
                                                        } else if ("Refund".equalsIgnoreCase(his.getTransaction_type()) && his.getAdvance_status().equalsIgnoreCase("Successful")) {
                                                                remainAmt -= his.getAmount().getTotal();
                                                        }
                                                }
                                                if (refundAmt <= remainAmt && remainAmt > 0) {
                                                        return merchantService.getData(connOnline,
                                                                        approval.getMerchant_id());
                                                } else {

                                                        LOGGER.log(Level.SEVERE,
                                                                        "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REMAIN : "
                                                                                        + remainAmt + " REFUND : " + refundAmt);
                                                        this.refundApprovalService.updateErrorRefundStatus(
                                                                        connBackup, approval.getTransaction_id(), 400,
                                                                        "Amount Refund Error");
                                                        throw new ErrorException(500, "Amount Refund Error",
                                                                        400 + "|" + "Amount Refund Error", "Refund Failed",
                                                                        "Refund Failed");

                                                }
                                        });
                                });
                        });
                });
        }

        private void clientRequestToObs(RoutingContext rc, String userId, String transactionId, JDBCClient clientOnline, JDBCClient clientBackUp, HttpClientRequest clientRequest, RefundApproval approval, Boolean skipCallSynchronize) {
                clientRequest.toObservable().subscribe(response -> {
                        int statusCode = response.statusCode();
                        String statusMessage = response.statusMessage();
                        if (statusCode == HttpResponseStatus.OK.code()) {
                                response.bodyHandler(buffer1 -> {
                                        String json = buffer1.toString("UTF-8");
                                        Map mapVpc = gson.fromJson(json, Map.class);
                                        if (mapVpc != null && mapVpc.get("vpc_response") != null) {
                                                Map dataResp = (Map) mapVpc.get("vpc_response");
                                                if ((dataResp.get("vpc_TxnResponseCode") != null && "0".equals(dataResp.get("vpc_TxnResponseCode") + "")) || (mapVpc.get("command_status").equals("WAIT_FOR_APPROVE"))) {
                                                        Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline1 -> {
                                                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                                                                        return InstallmentService.rejectInstallment(connOnline1, approval.getOriginal_transaction_id().toString()).flatMap(aVoid -> {
                                                                                return internationalTransactionService.getHistoryByMerchantRef(connOnline1, approval.getOriginal_transaction_id().toString(),
                                                                                                dataResp.get("vpc_MerchTxnRef").toString(), approval.getMerchant_id())
                                                                                                .flatMap(internationalTransactionHistory -> {
                                                                                                        return this.refundApprovalService.insertApprove(connBackUp1, userId,
                                                                                                                        approval.getMerchant_id(), approval.getOriginal_transaction_id(),
                                                                                                                        approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                                                                                                        approval.getTransaction_id(), approval.getMerchant_transaction_ref(),
                                                                                                                        400, RefundData.Type.INTERNATIONAL.getValue())
                                                                                                                        .flatMap(approval1 -> {
                                                                                                                                return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                                                                                                        if (userData != null) {
                                                                                                                                                internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                                                                                                        }
                                                                                                                                        return internationalTransactionHistory;
                                                                                                                                });
                                                                                                                        });
                                                                                                });
                                                                        });

                                                                });
                                                        });
                                                        returnObs.subscribe(internationalTransactionHistory -> {
                                                                if (skipCallSynchronize == false) {
                                                                        // call ep dong bo international
                                                                        try {
                                                                                OneSchedClient.synchronizeInterRefundViewSync();
                                                                        } catch (Exception e) {
                                                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                                                        }
                                                                }
                                                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                                                rc.next();
                                                        }, rc::fail);
                                                } else {
                                                        LOGGER.log(Level.SEVERE, "Refund transaction failed ", Utils.mask(mapVpc));
                                                        LOGGER.log(Level.INFO,
                                                                        "===================================== END REFUND =================================="
                                                                                        + StringPool.NEW_LINE);
                                                        if (mapVpc.get("vpc_TxnResponseCode") != null) {
                                                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED");
                                                                // throw IErrors.REFUND_FAILED;
                                                        } else {
                                                                LOGGER.log(Level.SEVERE,
                                                                                "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                                                                // LOGGER.log(Level.SEVERE, mapVpc.toString());
                                                                // throw IErrors.REFUND_FAILED;
                                                        }
                                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                        .flatMap(connBackUp -> {
                                                                                return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                                                                                Integer.parseInt(transactionId), statusCode, statusMessage);
                                                                        }).subscribe();
                                                }

                                        } else {
                                                LOGGER.log(Level.SEVERE,
                                                                "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED " + Utils.mask(mapVpc));
                                                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                .flatMap(connBackUp -> {
                                                                        return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                                                                        Integer.parseInt(transactionId), statusCode, statusMessage);
                                                                }).subscribe();
                                                throw new ErrorException(500, "REFUND_FAILED", statusCode + "|" + statusMessage,
                                                                "Refund Failed", "Refund Failed");
                                        }

                                });
                                response.exceptionHandler(throwable -> {
                                        rc.fail(throwable);
                                });
                        } else {
                                LOGGER.log(Level.SEVERE,
                                                "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED " + Utils.mask(response) );
                                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                .flatMap(connBackUp -> {
                                                        return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                                                        Integer.parseInt(transactionId), statusCode, statusMessage);
                                                }).subscribe();
                                throw new ErrorException(500, "REFUND_FAILED", statusCode + "|" + statusMessage, "Refund Failed",
                                                "Refund Failed");
                        }
                }, throwable -> {
                        rc.fail(throwable);
                });
        }

        private void clientRequestToObs(RoutingContext rc, String userId, String transactionId, JDBCClient clientOnline,
                        JDBCClient clientBackUp, JsonObject dataResp, RefundApproval approval, Boolean skipCallSynchronize) {
                if (dataResp != null && (dataResp.getString("vpc_TxnResponseCode") != null && "0".equals(dataResp.getString("vpc_TxnResponseCode") + ""))) {
                        Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline1 -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                                        return InstallmentService.rejectInstallment(connOnline1, approval.getOriginal_transaction_id().toString()).flatMap(aVoid -> {
                                                return internationalTransactionService.getHistoryByMerchantRef(connOnline1, approval.getOriginal_transaction_id().toString(),
                                                                dataResp.getString("vpc_MerchTxnRef").toString(), approval.getMerchant_id())
                                                                .flatMap(internationalTransactionHistory -> {
                                                                        LOGGER.info("approve record: " + approval.getTransaction_id());
                                                                        return this.refundApprovalService.insertApprove(connBackUp1, userId, approval.getMerchant_id(),
                                                                                        approval.getOriginal_transaction_id(), approval.getAmount().getTotal(),
                                                                                        approval.getAmount().getCurrency(), approval.getTransaction_id(),
                                                                                        approval.getMerchant_transaction_ref(), 400, RefundData.Type.INTERNATIONAL.getValue())
                                                                                        .flatMap(approval1 -> {
                                                                                                // if (approval1.getStatus() != 400 && approval1.getStatus() != 300) {
                                                                                                // // return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f ->
                                                                                                // f.dispose()).flatMap(connBackUp -> {
                                                                                                // return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                                                                                // Integer.parseInt(transactionId), 500, "").flatMap(a -> {
                                                                                                // throw IErrors.REFUND_FAILED;
                                                                                                // });
                                                                                                // // });
                                                                                                // }
                                                                                                return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                                                                                        if (userData != null) {
                                                                                                                internationalTransactionHistory.setOperator_id(userData.getEmail());
                                                                                                        }
                                                                                                        return internationalTransactionHistory;
                                                                                                });
                                                                                        });
                                                                });
                                        });

                                });
                        });
                        returnObs.subscribe(internationalTransactionHistory -> {
                                if(skipCallSynchronize == false) {
                                        // call ep dong bo international
                                        try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                        } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                        }
                                }
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                rc.next();
                        }, rc::fail);
                } else {
                        LOGGER.log(Level.INFO, "===================================== END REFUND ==================================" + StringPool.NEW_LINE);
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED", Utils.mask(dataResp));
                        if (dataResp.getString("vpc_TxnResponseCode") != null) {
                                LOGGER.log(Level.WARNING, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED");
                                // throw IErrors.REFUND_FAILED;
                        } else {
                                LOGGER.log(Level.WARNING,
                                                "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                                // LOGGER.log(Level.SEVERE, dataResp.encodePrettily());
                                // throw IErrors.REFUND_FAILED;
                        }
                        // Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        // .flatMap(connBackUp -> {
                        // return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                        // Integer.parseInt(transactionId), statusCode, statusMessage);
                        // }).subscribe();
                }
        }

        private void clientRequestToObsVoidRefund(RoutingContext rc, String userId, String transactionId, JDBCClient clientOnline, JDBCClient clientBackUp, JsonObject mapJsonVpc, String originalTransaction, InternationalTransaction internationalTransaction) {
                        if ("0".equals(mapJsonVpc.getString("vpc_TxnResponseCode") + "")) {
                                Observable<InternationalTransactionHistory> historyObs = Observable
                                                .using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                                .flatMap(connOnline -> {
                                                        return Observable.using(SQLConnectionFactory::new,
                                                                        f -> f.create(clientBackUp), f -> f.dispose())
                                                                        .flatMap(connBackUp -> {
                                                                                return internationalTransactionService
                                                                                                .getHistoryByMerchantRef(connOnline,
                                                                                                                originalTransaction,
                                                                                                                mapJsonVpc.getString("vpc_MerchTxnRef").toString(),
                                                                                                                internationalTransaction.getMerchant_id())
                                                                                                .flatMap(internationalTransactionHistory -> {
                                                                                                        return userService
                                                                                                                        .get(connBackUp,
                                                                                                                                        internationalTransactionHistory
                                                                                                                                                        .getOperator_id())
                                                                                                                        .map(userData -> {
                                                                                                                                if (userData != null) {
                                                                                                                                        internationalTransactionHistory
                                                                                                                                                        .setOperator_id(userData
                                                                                                                                                                        .getEmail());
                                                                                                                                }
                                                                                                                                return internationalTransactionHistory;
                                                                                                                        });
                                                                                                });
                                                                        });
                                                });
                                historyObs.subscribe(internationalTransactionHistory -> {
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                                        rc.next();
                                }, throwable -> {
                                        rc.fail(throwable);
                                });
                
                        } else {
                                LOGGER.log(Level.SEVERE,
                                                "[ INTERNATIONAL VOID REFUND PATCH FAILED ] => VOID REFUND TRANSACTION CODE:"
                                                                + Utils.mask(mapJsonVpc));
                                throw IErrors.VOID_REFUND_FAILED;
                        }
                

        }

        private Observable<Map> get(SQLConnection connOnline, String transactionId) {
                return internationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                        if (internationalTransaction == null) {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID REFUND PATCH ] => NOT FOUND TRANSACTION");
                                throw IErrors.RESOURCE_NOT_FOUND;
                        }
                        if (!internationalTransaction.isCan_void()) {
                                // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                // throw new VoidPurchaseException("Transaction is out of date.", 500);
                        }
                        LOGGER.info("***** VOID REFUND CHECK BEGIN ****");
                        Calendar cal = Calendar.getInstance();
                        Timestamp date = internationalTransaction.getTransaction_time();
                        cal.setTime(date);
                        cal.set(Calendar.MILLISECOND, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MINUTE, 0);
                        Date d_to = null;
                        if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
                                cal.set(Calendar.HOUR_OF_DAY, 17);
                        } else {
                                cal.set(Calendar.HOUR_OF_DAY, 17);
                                cal.add(Calendar.DATE, 1);
                        }
                        d_to = cal.getTime();

                        if ((new Date()).getTime() > d_to.getTime()) {
                                // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                // LOGGER.info("--------------- VOID REFUND CHECK END ------------------- ");
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                // throw new VoidPurchaseException("Transaction is out of date.", 500);
                        } else {
                                Map returnMap = new HashMap();
                                returnMap.put("transaction", internationalTransaction);

                                return merchantService.getData(connOnline, internationalTransaction.getMerchant_id())
                                                .map(merchantData -> {
                                                        returnMap.put("merchant", merchantData);
                                                        return returnMap;
                                                });
                        }
                });
        }

        private Observable<Map> getByRef(SQLConnection connOnline, String txnRef) {
                return internationalTransactionService.getByRef(connOnline, txnRef).flatMap(internationalTransaction -> {
                        if (internationalTransaction == null) {
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID REFUND PATCH ] => NOT FOUND TRANSACTION");
                                throw IErrors.RESOURCE_NOT_FOUND;
                        }
                        if (!internationalTransaction.isCan_void()) {
                                // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL VOID REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                // throw new VoidPurchaseException("Transaction is out of date.", 500);
                        }
                        LOGGER.info("***** VOID REFUND CHECK BEGIN ****");
                        Calendar cal = Calendar.getInstance();
                        Timestamp date = internationalTransaction.getTransaction_time();
                        cal.setTime(date);
                        cal.set(Calendar.MILLISECOND, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MINUTE, 0);
                        Date d_to = null;
                        if (cal.get(Calendar.HOUR_OF_DAY) < 17) {
                                cal.set(Calendar.HOUR_OF_DAY, 17);
                        } else {
                                cal.set(Calendar.HOUR_OF_DAY, 17);
                                cal.add(Calendar.DATE, 1);
                        }
                        d_to = cal.getTime();

                        if ((new Date()).getTime() > d_to.getTime()) {
                                // LOGGER.log(Level.SEVERE, "The transaction is out of date.");
                                // LOGGER.info("--------------- VOID REFUND CHECK END ------------------- ");
                                LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND PATCH ] => TRANSACTION OUT OF DATE");
                                throw IErrors.OUT_OF_DATE_TRANSACTION_ERROR;
                                // throw new VoidPurchaseException("Transaction is out of date.", 500);
                        } else {
                                Map returnMap = new HashMap();
                                returnMap.put("transaction", internationalTransaction);

                                return merchantService.getData(connOnline, internationalTransaction.getMerchant_id())
                                                .map(merchantData -> {
                                                        returnMap.put("merchant", merchantData);
                                                        return returnMap;
                                                });
                        }
                });
        }

        private SQLConnection onlineCon;

        private SQLConnection backUpCon;


        @Autowired
        private RefundApprovalService refundApprovalService;

        @Autowired
        private InternationalRefundService internationalRefundService;

        @Autowired
        private UserService userService;

        @Autowired
        private RefundConfig refundConfig;

        @Autowired
        private InternationalTransactionService internationalTransactionService;

        @Autowired
        private MerchantService merchantService;

        @Autowired
        private MerchantApprovalService merchantApproval;

        @Autowired
        GeneralReportService generalReportService;


        private final static Gson gson = new Gson();
        private static final String X_SECURE_HASH = "X-Secure-Hash";
        private static final Logger LOGGER = Logger.getLogger(InternationalRefundPatchHandlerImpl.class.getName());

}
