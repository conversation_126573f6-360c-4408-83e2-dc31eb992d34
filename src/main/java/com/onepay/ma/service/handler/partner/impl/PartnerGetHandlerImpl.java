package com.onepay.ma.service.handler.partner.impl;

import com.onepay.ma.service.handler.partner.PartnerGetHandler;
import com.onepay.ma.service.service.partner.PartnerService;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;

import rx.Observable;

/**
 * Created by thaihv on 27/8/19.
 */
public class PartnerGetHandlerImpl implements PartnerGetHandler {

    private PartnerService partnerService;

    private UserPartnerService userPartnerService;

    public PartnerGetHandlerImpl(PartnerService partnerService, UserPartnerService userPartnerService) {
        this.partnerService = partnerService;
        this.userPartnerService = userPartnerService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientBackup = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String partnerId = rc.request().getParam("id");
        String userId = rc.request().getParam("user_id");

        if (partnerId != null) {
            // Get one by partner id

        } else {
            String keyword = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? "" : rc.request().getParam(ParamsPool.KEY_WORDS);
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 50 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            if (userId != null) {
                // Get many by user id
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                        .flatMap(connBackup -> {
                            return this.userPartnerService.getPartnersByUserId(connBackup, userId);
                        }).subscribe(partners -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

            } else {
                // Get all
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                        .flatMap(connBackup -> {
                            return partnerService.list(connBackup, page, pageSize, keyword);
                        }).subscribe(partners -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
            }
        }
    }
}
