package com.onepay.ma.service.handler.token.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.service.apptoken.AppTokenService;
import com.onepay.ma.service.service.notification.AppNotificationConfigService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 23-Oct-17.
 */
@Component
public class AppTokenDeleteHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String apptoken = rc.request().getParam("id");
        if (apptoken == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(sqlBackUp -> {
                    sqlConnectionBackup = sqlBackUp;
                    return sqlConnectionBackup.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return appTokenService.removeToken(sqlBackUp, apptoken);
                    });
                }).subscribe(config -> {
            if(sqlConnectionBackup != null) {
                sqlConnectionBackup.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
            rc.next();
        }, throwable -> {
            if (sqlConnectionBackup != null) {
                sqlConnectionBackup.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }


    private SQLConnection sqlConnectionBackup;

    @Autowired
    private AppTokenService appTokenService;
}
