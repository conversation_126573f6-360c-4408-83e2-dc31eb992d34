package com.onepay.ma.service.handler.merchant;

import com.onepay.ma.service.handler.merchant.impl.MerchantGetHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/23/16.
 */
public interface MerchantGetHandler extends Handler<RoutingContext> {
    static MerchantGetHandlerImpl create(MerchantService merchantService){
        return new MerchantGetHandlerImpl(merchantService);

    }
}