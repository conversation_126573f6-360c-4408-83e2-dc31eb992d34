package com.onepay.ma.service.handler.user.impl;


import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.user.UserV2GetHandler;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 12/04/18.
 */
public class UserV2GetHandlerImpl implements UserV2GetHandler {
    private static Logger LOGGER = Logger.getLogger(UserV2GetHandlerImpl.class.getName());
    private final static Gson gson = new Gson();

    public UserV2GetHandlerImpl(UserServiceV2 userService, UserPartnerService userPartnerService) {
        this.userService = userService;
        this.userPartnerService = userPartnerService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
//        String userId ="874735D1B347FA85C91D321CE0A12843";
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ USER CREATE ] => USER ID IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String sId = rc.request().getParam("s_id");
        if (sId == null) {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        return userPartnerService.getPartnersByUserId(sqlConnection, userId).flatMap(partners -> {
                            if (partners.getTotal_items() > 0) {
                                // In case user was granted to at least one partner (After update requirement)
                                StringBuilder partnerIds = new StringBuilder();
                                for (Partner partner : partners.getPartners()) {
                                    if (partnerIds.length() > 0) {
                                        partnerIds.append(",");
                                    }
                                    partnerIds.append(partner.getId());
                                }
                                return userService.listUserByPartnerIds(sqlConnection, userId, partnerIds.toString());
                            } else {
                                return userService.listByCreateId(sqlConnection, userId).flatMap(createdUsers -> {
                                    return Observable.just(createdUsers);
                                });
                            }
                        });
                    }).subscribe(listUser -> {
                Map returnMap = new HashMap();
                returnMap.put("users", listUser);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnMap);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                .flatMap(sqlConnection -> {
                    return userService.getUserDataBySId(sqlConnection, sId);
                }).subscribe(userData -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, userData);
            rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }
    }

    private UserServiceV2 userService;

    private UserPartnerService userPartnerService;

}
