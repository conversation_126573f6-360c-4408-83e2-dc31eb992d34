package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.service.mpay.impl.MocaMpayTransactionService;
import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.mpay.MocaMpayTransaction;
import com.onepay.ma.service.models.mpay.MocaMpayTransactionQuery;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.models.mpay.MpayTransactionQuery;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 7/5/2016.
 */
@Component
public class MocaMpayTransactionGetHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(MpayTransactionGetHandlerImpl.class.getName());

    @Autowired
    private UserService userService;
    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ MPAY TRANSACTION GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROMDATE);
            String toDate = rc.request().getParam(ParamsPool.TODATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(fromDate);
                oToDate = df.parse(toDate);

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ MPAY TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.VALIDATION_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ MPAY TRANSACTION GET ]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            System.out.println("Vào service");
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));
            String merchantId = rc.request().getParam(ParamsPool.MERCHANTID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTID));
            String orderId = rc.request().getParam(ParamsPool.ORDERINFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDERINFO));
            String acq_code = rc.request().getParam(ParamsPool.ACQCODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ACQCODE));
            String merchant_name = rc.request().getParam(ParamsPool.MERCHANTNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTNAME));
            String bankId = rc.request().getParam(ParamsPool.BANKID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANKID));
            String merchantTnxRef = rc.request().getParam(ParamsPool.MERCHANTTRANSACTIONREF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTTRANSACTIONREF));
            String bankTransactionId = rc.request().getParam(ParamsPool.BANKTRANSACTIONID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANKTRANSACTIONID));
//            String instrumentBrandId = rc.request().getParam(ParamsPool.INSTRUMENT_BRAND_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.INSTRUMENT_BRAND_ID));
            String instrumentNumber = rc.request().getParam(ParamsPool.INSTRUMENTNUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.INSTRUMENTNUMBER));
            String merchantTransactionID = rc.request().getParam(ParamsPool.TRANSACTIONID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTIONID));
            String customerTransactionId = rc.request().getParam(ParamsPool.CUSTOMERTRANSACTIONID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CUSTOMERTRANSACTIONID));
            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));


            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));

            String masking = rc.request().getParam(ParamsPool.MASKING) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MASKING));
            String qrId = rc.request().getParam(ParamsPool.QRID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.QRID));
            String invoiceId = rc.request().getParam(ParamsPool.INVOICE_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.INVOICE_ID));
            String clientId = rc.request().getParam(ParamsPool.CLIENT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CLIENT_ID));
            String appName = rc.request().getParam(ParamsPool.APPNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.APPNAME));
            String version = rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));
            String bankMerchantId = rc.request().getParam(ParamsPool.BANK_MERCH_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_MERCH_ID));
            String bankTerminalId = rc.request().getParam(ParamsPool.BANK_TERMI_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANK_TERMI_ID));

            String transType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));
            MocaMpayTransactionQuery query = new MocaMpayTransactionQuery();
            query.setPage(page);
            query.setKeywords(keywords);
            query.setMerchantId(merchantId);
            query.setMerchantTransactionRef(merchantTnxRef);
            query.setBankId(bankId);
            query.setOrderInfo(orderId);
            query.setInstrumentNumber(instrumentNumber);
            query.setTransactionId(merchantTransactionID);
            query.setBankTransactionId(bankTransactionId);
            query.setPageSize(pageSize);
            query.setCurrency(currency);
            query.setFromDate(fromDate);
            query.setToDate(toDate);
            query.setStatus(status);
            query.setAcqCode(acq_code);
            query.setMerchantName(merchant_name);
            query.setCustomerTransactionId(customerTransactionId);
            query.setAppName(appName);
            query.setMasking(masking);
            query.setQrId(qrId);
            query.setInvoiceId(invoiceId);
            query.setClientId(clientId);
            query.setVersion(version);
            query.setBankMerchantId(bankMerchantId);
            query.setBankTerminalId(bankTerminalId);

            query.setTransType(transType);
            
            Observable<NewBaseList<MocaMpayTransaction>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        // get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackup -> {
                                                // find user by S-id
                                                return userService.getOneAm(connBackup, userId).flatMap(user -> {

                                                    return merchantService.list(connOnline, connBackup, merchantId, userId, "mpay").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        query.setMerchantId(String.join(",", merchantIdList));
                                                        query.setTerminalId(StringPool.BLANK);
                                                        return MocaMpayTransactionService.search(connReadOnly, connOnline, connBackup, query);
                                                    });
                                                });
                                            });
                                });
                    });

            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return MocaMpayTransactionService.get(connOnline, transactionId).flatMap(mpayTrans -> {
                                        return merchantService.list(connOnline, connBackup, "", userId, "mpay").flatMap(merchants -> {
                                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                            if (null == merchantIdList || merchantIdList.isEmpty() || null == mpayTrans || null == mpayTrans.getMerchantId() || (!merchantIdList.contains("ALL") && !merchantIdList.contains(mpayTrans.getMerchantId()))) {
                                                LOGGER.info("MPAY MERCHANT ID PERMISSION : " + merchantIdList + " MERCHANT ID DETAIL: " + mpayTrans.getMerchantId());
                                                throw IErrors.FORBIDDEN;
                                            }
                                            return Observable.just(mpayTrans);
                                        });
                                    });
                                });
                    }).subscribe(transaction -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
        }
    }
}
