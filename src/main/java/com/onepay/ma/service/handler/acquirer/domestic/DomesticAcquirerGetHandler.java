package com.onepay.ma.service.handler.acquirer.domestic;

import com.onepay.ma.service.handler.acquirer.domestic.impl.DomesticAcquirerGetHandlerImpl;
import com.onepay.ma.service.service.AcquirerService;
import com.onepay.ma.service.service.MerchantService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface DomesticAcquirerGetHandler extends Handler<RoutingContext> {
        static DomesticAcquirerGetHandlerImpl create(AcquirerService acquirerService,  MerchantService merchantService){
            return new DomesticAcquirerGetHandlerImpl(acquirerService, merchantService);
        }
}
