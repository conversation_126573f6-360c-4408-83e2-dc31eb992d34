package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostDomesticReportHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.DomesticReportService;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FilePostDomesticReportHandlerImpl implements FilePostDomesticReportHandler {

    public FilePostDomesticReportHandlerImpl(DomesticReportService domesticReportService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.domesticReportService = domesticReportService;
        this.merchantService = merchantService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.cacheGuava = cacheGuava;
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
//        SQLConnection connReadOnly = rc.get(ParamsPool.CONNECTION_READONLY);
//        SQLConnection connBackUp = rc.get(ParamsPool.CONNECTION_BACKUP);
//        SQLConnection connOnline = rc.get(ParamsPool.CONNECTION_ONLINE);
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        DomesticReportPostFile mapBody =  gson.fromJson(body, DomesticReportPostFile.class);
        if(mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = mapBody.getFrom_date();
        String toDate = mapBody.getTo_date();
        
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            fromDate = sdf.format(oFromDate);
            toDate = sdf.format(oToDate);

        } catch (Exception e) {
            throw IErrors.VALIDATION_ERROR;
        }
        // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        int checkFromDateDiffToDate = FunctionUtil.compareDateByMonth(oFromDate, oToDate, 6);
        int checkFromdateTooOld = FunctionUtil.compareDateByMonth(oFromDate, new Date(), 24);

        // Validate fromdate, todate too long
        if (checkFromDateDiffToDate <= 0) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        // Validate fromdate after 2 years ago
        if (checkFromdateTooOld <= 0) {
            throw IErrors.SEARCH_TOO_OLD_ERROR;
        }


        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "domestic").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                DomesticReportParameter parameter = new DomesticReportParameter();
                                                parameter.setKeywords(mapBody.getKeywords());
                                                parameter.setAcquirerId(mapBody.getAcquirer_id().replace("|",","));
                                                parameter.setFromDate(mapBody.getFrom_date());
                                                parameter.setToDate(mapBody.getTo_date());
                                                parameter.setMerchantId(String.join(",", merchantIdList));
                                                parameter.setInterval(mapBody.getInterval());
                                                parameter.setVersion(mapBody.getVersion() == null ? "" : "v2" );
                                                parameter.setLang(mapBody.getLang());
                                                Map data = new HashMap();
                                                return domesticReportService.getTotalReport(connReadOnly, parameter).flatMap(integer -> {
//                                                    int integer = domesticReports.size();
                                                    if (integer == 0) {
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }
                                                    String fileName = "domestic" + StringPool.UNDERLINE + "report" + StringPool.UNDERLINE;
                                                    long date = new java.util.Date().getTime();
                                                    fileName += date;
                                                    String fileHashName = "";
                                                    data.put("parameter", parameter);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("payment_report");
                                                    fileDownload.setExt("xls");
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }

            DomesticReportParameter parameter = (DomesticReportParameter) map.get("parameter");
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<DomesticReportParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //fileDownload.setExt("zip");
                Message<DomesticReportParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });






    }

    private DomesticReportService domesticReportService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private CacheGuava cacheGuava;

    private SQLConnection connectBackUp;

    private MerchantService merchantService;

    private final static Gson gson = new Gson();
}
