package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.InternationalTransactionAuthorizeGetHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.InternationalTransactionGetHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.MidService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalTransactionAuthorizeGetHandler extends Handler<RoutingContext>  {
    static InternationalTransactionAuthorizeGetHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, MidService midService, ServerConfig serverConfig){
        return new InternationalTransactionAuthorizeGetHandlerImpl(internationalTransactionService, merchantService, midService, serverConfig);
    }
}
