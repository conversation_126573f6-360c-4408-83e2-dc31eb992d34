package com.onepay.ma.service.handler.reconcile;

import com.onepay.ma.service.handler.reconcile.impl.PaymentReconcileDetailGetHandlerImpl;
import com.onepay.ma.service.handler.reconcile.impl.PaymentReconcileGetHandlerImpl;
import com.onepay.ma.service.handler.reconcile.impl.ReconcileGetHandlerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tuydv on 19/09/18.
 */
@Component
public class ReconcileHandler {

    @Autowired
    private ReconcileGetHandlerImpl reconcileGetHandler;

    @Autowired
    private PaymentReconcileGetHandlerImpl paymentReconcileGetHandler;

    @Autowired
    private PaymentReconcileDetailGetHandlerImpl paymentReconcileDetailGetHandler;


    public ReconcileGetHandlerImpl getReconcile() {
        return reconcileGetHandler;
    }

    public PaymentReconcileGetHandlerImpl getPayment() {
        return paymentReconcileGetHandler;
    }
    public PaymentReconcileDetailGetHandlerImpl getPaymentDetail() {
        return paymentReconcileDetailGetHandler;
    }
}
