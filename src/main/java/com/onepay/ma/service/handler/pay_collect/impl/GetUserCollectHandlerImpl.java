package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.service.pay_collect.UserConfigService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/5/2020
 * Time: 11:55 AM
 * To change this ma-web.
 */
@Component
public class GetUserCollectHandlerImpl implements Handler<RoutingContext> {
    private static Logger LOGGER = Logger.getLogger(GetUserCollectHandlerImpl.class.getName());
    @Autowired
    private UserConfigService userConfigService;
    @Override
    public void handle(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String userId = rc.get(ParamsPool.USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String user_id = rc.request().getParam("user_id");

        JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                .flatMap(connPaycollect -> {
                    return userConfigService.get(connPaycollect, user_id);
                })
                .subscribe(jo -> {
                    // LOGGER.info("BODY JSON: "+ jo);
                    UserConfigDto dto = bindFundsUserConfigDtos(jo);

                    rc.put(ParamsPool.HANDLER_DATA_RESULT, dto);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

//        JsonObject jo = PayCollectClient.getAccount(userId, xUserId, xRequestId,account_id,user_id);
//        UserConfigDto dto = bindFundsUserConfigDtos(jo);
//        LOGGER.info("BODY JSON: "+ dto);
//        rc.put(ParamsPool.HANDLER_DATA_RESULT, dto);
//        rc.next();

    }
    private UserConfigDto bindFundsUserConfigDtos(JsonObject jsonObject) {
        UserConfigDto dto = new UserConfigDto();
//        dto.setId(jsonObject.getString("S_ID"));
//        dto.setName();




        dto.setName(jsonObject.getString("S_FULL_NAME"));
        dto.setMobile_number(jsonObject.getString("S_MOBILE"));
        dto.setDescription(jsonObject.getString("S_DESC"));
        dto.setEmail(jsonObject.getString("S_EMAIL"));
        dto.setGender(jsonObject.getString("S_GENDER"));
        dto.setId(jsonObject.getString("S_ID"));
        dto.setId_card(jsonObject.getString("S_ID_CARD"));
        dto.setDate_of_birth(jsonObject.getString("D_DATE_OF_BIRTH") != null &&  jsonObject.getString("D_DATE_OF_BIRTH") != ""? java.sql.Timestamp.valueOf(jsonObject.getString("D_DATE_OF_BIRTH")): null );
        dto.setIssue_date(jsonObject.getString("S_ISSUE_DATE") != null && jsonObject.getString("S_ISSUE_DATE") != ""? java.sql.Timestamp.valueOf(jsonObject.getString("S_ISSUE_DATE")): null );
        dto.setAddress(jsonObject.getString("S_ADDRESS"));
        dto.setIssue_by(jsonObject.getString("S_ISSUE_BY"));
        dto.setPartner_id(jsonObject.getString("S_PARTNER_ID"));
        dto.setPartner_name(jsonObject.getString("S_PARTNER_NAME"));
        dto.setReference(jsonObject.getString("S_REFERENCE_ID"));
        dto.setState(jsonObject.getString("S_STATE"));
        dto.setAccount_id(jsonObject.getString("S_ACCOUNT_ID"));
        dto.setAccount_number(jsonObject.getString("S_BANK_ACCOUNT"));
        dto.setBank_name(jsonObject.getString("S_BANK_NAME"));
        dto.setBranch_name(jsonObject.getString("S_BRANCH_NAME"));
//        JsonArray array = jsonObject.getJsonArray("user_profiles");
//        List<UserProfileDto> dtoList = new ArrayList<>();
//        UserProfileDto profileDto;
//        for(int i=0; i< array.size();i++){
//            profileDto = new UserProfileDto();
//            SimpleDateFormat transDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            SimpleDateFormat transDateFormat2 = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
//            try {
//                Date fromDate = transDateFormat.parse(array.getJsonObject(i).getString("D_FROM"));
//                profileDto.setFrom_date(transDateFormat2.format(fromDate));
//                Date toDate = transDateFormat.parse(array.getJsonObject(i).getString("D_TO"));
//                profileDto.setTo_date(transDateFormat2.format(toDate));
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//            profileDto.setUser_id(array.getJsonObject(i).getString("S_USER_ID"));
//            profileDto.setInterval(array.getJsonObject(i).getString("S_INTERVAL"));
//            profileDto.setId(array.getJsonObject(i).getString("S_ID"));
//            profileDto.setExpire(Integer.parseInt(array.getJsonObject(i).getString("N_EXPIRE")));
//            profileDto.setAmount(array.getJsonObject(i).getString("N_COLLECT_AMOUNT"));
//            profileDto.setDescription(array.getJsonObject(i).getString("S_DESC"));
//            profileDto.setType(array.getJsonObject(i).getString("S_TYPE"));
//            profileDto.setState(array.getJsonObject(i).getString("S_STATE"));
//            dtoList.add(profileDto);
//        }
//        dto.setUser_profiles(dtoList);
        return dto;
    }


}
