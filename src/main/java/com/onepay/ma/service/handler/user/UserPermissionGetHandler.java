package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserPermissionGetHandlerImpl;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/14/16.
 */
public interface UserPermissionGetHandler extends Handler<RoutingContext> {
    static UserPermissionGetHandlerImpl create(UserPermissionService userPermissionService, CacheGuava cacheGuava){
        return new UserPermissionGetHandlerImpl(userPermissionService, cacheGuava);
    }
}
