package com.onepay.ma.service.handler.transaction.international.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.MidService;
import com.onepay.ma.service.service.international.SSInternationalTransactionService;
import com.onepay.ma.service.service.orderApproval.OrderApprovalService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.logging.Logger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 3/30/16.
 */
@Component
public class SamsungInternationalTransactionGetHandlerImpl implements Handler<RoutingContext> {
    private static final Logger LOGGER = Logger.getLogger(SamsungInternationalTransactionGetHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                            .flatMap(connReadOnly -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return SSInternationalTransactionService.get(connOnline, transactionId).flatMap(internationalTransaction -> {
                                                return merchantService.list(connOnline, connBackup, "", userId, "international").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    if (null == merchantIdList || merchantIdList.isEmpty() || null == internationalTransaction || null == internationalTransaction.getMerchant_id() || (!merchantIdList.contains("ALL") && !merchantIdList.contains(internationalTransaction.getMerchant_id()))) {
                                                        LOGGER.severe("INTERNATIONAL SS MERCHANT ID PERMISSION : " + merchantIdList + " MERCHANT ID DETAIL: " + internationalTransaction);
                                                        throw IErrors.FORBIDDEN;
                                                    }
                                                    return orderApprovalService.getByTransactionId(connBackup, transactionId).flatMap(orderApproval -> {
                                                        return internationalTransactionService.listHistory(connOnline,  connBackup, transactionId).flatMap(histories -> {

                                                            return this.midService.getByMerchantId(connBackup, internationalTransaction.getMerchant_id()).map(mid_no -> {
                                                                double amount = 0;
                                                                for (InternationalTransactionHistory his : histories) {
                                                                    if ((his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content)
                                                                            && Integer.valueOf(his.getStatus()).compareTo(RefundApproval.Status.REQUEST.code) == 0)) {
                                                                        amount += his.getAmount().getTotal();
                                                                    }
                                                                }
                                                                internationalTransaction.setWait_for_approval_amount(amount);

                                                                internationalTransaction.setMid_no(mid_no);
                                                                internationalTransaction.setOrder_date_confirm(orderApproval == null ? null : orderApproval.getCreate_time());
                                                                internationalTransaction.setOrder_user_confirm(orderApproval == null ? null : orderApproval.getUser_confirm());
                                                                return internationalTransaction;
                                                            });
                                                        });
                                                    });
                                                });
                                            });
                                        });
                            });
                }).subscribe(transaction -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });
    }

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private InternationalTransactionService internationalTransactionService;

    @Autowired
    private MidService midService;


    @Autowired
    private OrderApprovalService orderApprovalService;
}
