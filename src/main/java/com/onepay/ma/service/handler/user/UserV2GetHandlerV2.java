package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserV2GetHandlerV2Impl;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserServiceV2;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by tuydv on 12/04/18.
 */
public interface UserV2GetHandlerV2 extends Handler<RoutingContext> {
    static UserV2GetHandlerV2Impl create(UserServiceV2 userService, MerchantService merchantService){
        return new UserV2GetHandlerV2Impl(userService, merchantService);
    }
}
