package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserSessionPostHandlerImpl;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */

public interface UserSessionPostHandler extends Handler<RoutingContext> {
    static UserSessionPostHandlerImpl create(OneAMConfig oneAMConfig, UserService userService, UserPermissionService userPermissionService, MerchantService merchantService){
        return new UserSessionPostHandlerImpl(oneAMConfig, userService, userPermissionService, merchantService);
    }
}
