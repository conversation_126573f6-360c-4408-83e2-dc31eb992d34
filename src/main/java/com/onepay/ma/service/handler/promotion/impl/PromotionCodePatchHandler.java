package com.onepay.ma.service.handler.promotion.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.PromotionCodeService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 06-Jan-17.
 */
@Component
public class PromotionCodePatchHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ PORMOTION CODE PATCH] -> USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String promotionCodeId = rc.request().getParam("id");
        final HttpServerRequest request = rc.request();
        final HttpClient httpClient = rc.vertx().createHttpClient();


        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if (promotionCodeId != null) {
            String body = rc.getBodyAsString();
            PatchRequest mapPatchRequest = gson.fromJson(body, PatchRequest.class);
            if (mapPatchRequest == null) {
                LOGGER.log(Level.SEVERE, "[ PORMOTION CODE PATCH] -> BODY EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }


            if (mapPatchRequest.getPath().equals("/use")) {
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackup -> {
                            return this.promotionCodeService.useCode(connBackup, promotionCodeId).map(code -> {
                                return code;
                            });
                        }).subscribe(code -> {
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, code);
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

            }

        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Autowired
    private PromotionCodeService promotionCodeService;


    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(PromotionCodePatchHandler.class.getName());
}
