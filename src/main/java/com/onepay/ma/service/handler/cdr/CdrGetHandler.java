package com.onepay.ma.service.handler.cdr;

import com.onepay.ma.service.handler.cdr.impl.CdrGetHandlerImpl;
import com.onepay.ma.service.service.CdrService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface CdrGetHandler extends Handler<RoutingContext> {
    static CdrGetHandlerImpl create(CdrService cdrService){
        return new CdrGetHandlerImpl(cdrService);

    }
}
