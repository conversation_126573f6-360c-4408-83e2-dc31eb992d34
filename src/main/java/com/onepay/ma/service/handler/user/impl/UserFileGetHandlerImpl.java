package com.onepay.ma.service.handler.user.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.user.UserFileGetHandler;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/9/16.
 */
public class UserFileGetHandlerImpl implements UserFileGetHandler {
    public UserFileGetHandlerImpl(FileService fileService) {
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        HttpServerRequest request = rc.request();
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId.isEmpty()){
           throw IErrors.VALIDATION_ERROR;
        }else{
            String keyword = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));
            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f-> f.dispose()).flatMap(connection -> {
                return fileService.list(connection, keyword, userId, page);
            }).subscribe(fileDownloads -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, fileDownloads);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }

    }

    private FileService fileService;

    private Gson gson = new Gson();
}
