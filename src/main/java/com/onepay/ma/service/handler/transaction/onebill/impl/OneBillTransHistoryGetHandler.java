package com.onepay.ma.service.handler.transaction.onebill.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class OneBillTransHistoryGetHandler implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {
        String transactionId = rc.request().getParam("id");
        JDBCClient clientMerchantPortal = rc.get(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME);

        if (transactionId != null) {
            Observable<List<OnebillTransactionHistory>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientMerchantPortal), f -> f.dispose()).flatMap(connMerchantPortal -> {
                return oneBillTransactionService.listHistory(connMerchantPortal, Integer.valueOf(transactionId));
            });

            obs.subscribe(mpayTransactionHistories -> {
                Map returnValue = new HashMap();
                returnValue.put("transactions", mpayTransactionHistories);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, returnValue);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        } else {
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Autowired
    private OneBillTransactionService oneBillTransactionService;
}
