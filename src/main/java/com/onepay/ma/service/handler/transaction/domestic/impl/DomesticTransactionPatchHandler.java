package com.onepay.ma.service.handler.transaction.domestic.impl;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import com.onepay.ma.service.service.MerchantService;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.service.lock.LockService;
import com.onepay.ma.service.util.DomesticRefundUtil;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import com.onepay.onecomm.payment.ws.QueryReq;
import com.onepay.onecomm.payment.ws.QueryRes;
import com.onepay.onecomm.payment.ws.RefundReq;
import com.onepay.onecomm.payment.ws.RefundRes;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.rxjava.core.buffer.Buffer;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.core.http.HttpClientRequest;
import io.vertx.rxjava.core.http.HttpClientResponse;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Subscriber;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/1/16.
 */
@Component
public class DomesticTransactionPatchHandler implements Handler<RoutingContext> {


    @Override
    public void handle(RoutingContext rc) {
        Utils.execBlocking(rc, () -> {
            String userId = rc.get(ParamsPool.X_USER_ID);
            if (userId == null) {
                LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String ipAddress = rc.get(ParamsPool.X_REAL_IP);
            if (ipAddress == null) {
                LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  IP EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String transactionId = rc.request().getParam("id");
            final HttpServerRequest request = rc.request();
            // final IService iService = iService();
            JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
            JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
            JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
            HttpClient httpClient = rc.vertx().createHttpClient();
            if (transactionId != null) {
                String body = rc.getBodyAsString();
                PatchRequest mapRequest = gson.fromJson(body, PatchRequest.class);
                Boolean skipCallSynchronize = mapRequest.isSkipCallSynchronize() == null ? true : mapRequest.isSkipCallSynchronize();
                if (mapRequest == null) {
                    LOGGER.log(Level.SEVERE, "[ VALIDATION REFUND PATCH  ] =>  REFUND DATA EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                if (mapRequest.getPath().equals("/refund")) {
                    PatchRequest<RefundData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());
                    if (mapPatchRequest.getValue() == null) {
                        throw IErrors.VALIDATION_ERROR;
                    }

                    // log ban ghi 14 tranh refund 2 lan cung luc
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                long startTime = System.currentTimeMillis();
                                long delay = 2000;
                                return lock(connBackUp, transactionId, 120000, startTime, delay);
                            }).subscribe(isLock -> {
                                if(isLock) {
                                    // check if transaction existed
                                    Observable<DomesticTransaction> stObs = this.getTransaction(clientOnline, transactionId).flatMap(domesticTransaction -> {
                                        if (domesticTransaction == null) {
                                            throw IErrors.RESOURCE_NOT_FOUND;
                                        }
                                        if (domesticTransaction.getStatus().equals("300")) { // check if status === 300
                                            return this.query(domesticTransaction.getMerchant_id(), domesticTransaction.getAmount().getCurrency(), transactionId, userId, ipAddress, httpClient).map(aVoid -> {
                                                return domesticTransaction;
                                            });
                                        }
                                        return Observable.just(domesticTransaction);
                                    });


                                    // Observable<Boolean> queryObs = this.query(body, transactionId, userId, ipAddress, httpClient);
                                    stObs.subscribe(o -> {
                                        this.getAmount(clientReadOnly, clientOnline, clientBackUp, transactionId, mapPatchRequest).subscribe(amount -> {
                                            String merPurchaseRef = Utils.isBigMerchant(o.getMerchant_id()) ? o.getMsp_merchant_transaction_ref() : o.getMerchant_transaction_ref();
                                            this.checkIsMerchantApproval(clientBackUp, clientOnline, transactionId, userId, ipAddress, amount, merPurchaseRef, mapPatchRequest).subscribe(domesticRefund -> {
                                                Object refund = domesticRefund.get("refund");
                                                if (refund instanceof Map) {
                                                    this.doRefund2(rc, domesticRefund, httpClient, clientOnline, clientBackUp, merPurchaseRef, userId, transactionId, skipCallSynchronize);
                                                } else {
                                                    // call ep dong bo
                                                    if (skipCallSynchronize == false) {
                                                        String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                                                        try {
                                                            OneSchedClient.synchronize(jobId, "0s");
                                                        } catch (Exception e) {
                                                            LOGGER.log(Level.INFO, "domestic_synx error " + e.getMessage());
                                                        }
                                                    }
                                                    unlock(transactionId, clientBackUp);
                                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
                                                    rc.next();
                                                }
                                                // LOGGER.log(Level.INFO, domesticRefund.toString());
                                            }, throwable -> {
                                                rc.fail(throwable);
                                            });
                                        }, throwable -> {
                                            rc.fail(throwable);
                                        });
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                                }
                            });
                } else if (mapRequest.getPath().equals("/query")) {


                    PatchRequest<QueryData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<QueryData>>() {}.getType());
                    if (mapPatchRequest.getValue() == null) {
                        LOGGER.log(Level.SEVERE, "[VALIDATE QUERY DATA PATCH] => REQUEST EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                    }
                    LOGGER.log(Level.INFO, "***** BEGIN QUERY *****");
                    QueryReq requestQuery = new QueryReq(Long.parseLong(transactionId), mapPatchRequest.getValue().getMerchant_id(), mapPatchRequest.getValue().getCurrency_code(), 0.0, userId, ipAddress, 0);
                    byte[] encoded = FunctionUtil.encodeHessian(requestQuery);
                    Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
                    HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

                    req.putHeader("Content-Type", "x-application/hessian");

                    req.toObservable().subscribe(httpClientResponse -> {
                        httpClientResponse.bodyHandler(responseData -> {
                            // String json = responseData.toString("UTF-8");

                            io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                            byte[] bytesData = new byte[bufferDataResp.length()];

                            for (int i = 0; i < bufferDataResp.length(); i++) {
                                byte data = bufferDataResp.getByte(i);
                                bytesData[i] = data;
                            }

                            QueryRes resp = (QueryRes) FunctionUtil.decodeHessian(bytesData);
                            if (resp == null) {
                                LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CONNECTION TO BANK ERROR.");
                                throw IErrors.QUERY_ERROR;
                            } else {
                                Map result = new HashMap<>();
                                result.put("result_code", resp.getStatus());
                                if (resp.getStatus() != 400) {
                                    LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
                                    throw IErrors.QUERY_ERROR;
                                } else {
                                    rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
                                    rc.next();
                                }

                            }

                        });
                        httpClientResponse.exceptionHandler(throwable -> {
                            rc.fail(throwable);
                        });
                    });

                    req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
                    req.write(buffer);
                    req.end();
                }

            }
        });
    }

    private Observable query(String merchantId, String currencyCode, String transactionId, String userId, String ipAddress, HttpClient httpClient) {

        return Observable.create((Subscriber<? super HttpClientResponse> subscriber) -> {
            if (subscriber.isUnsubscribed()) {
                return;
            }

            LOGGER.log(Level.INFO, "***** BEGIN QUERY *****");
            QueryReq requestQuery = new QueryReq(Long.parseLong(transactionId), merchantId, currencyCode, 0.0, userId, ipAddress, 0);
            byte[] encoded = FunctionUtil.encodeHessian(requestQuery);
            Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
            HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

            req.putHeader("Content-Type", "x-application/hessian");

            req.toObservable().subscribe(httpClientResponse -> {
                httpClientResponse.bodyHandler(responseData -> {
                    // String json = responseData.toString("UTF-8");

                    io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                    byte[] bytesData = new byte[bufferDataResp.length()];

                    for (int i = 0; i < bufferDataResp.length(); i++) {
                        byte data = bufferDataResp.getByte(i);
                        bytesData[i] = data;
                    }

                    QueryRes resp = (QueryRes) FunctionUtil.decodeHessian(bytesData);
                    if (resp == null) {
                        LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CONNECTION TO BANK ERROR.");
                        subscriber.onError(IErrors.REFUND_FAILED);
                    } else {
                        Map result = new HashMap<>();
                        result.put("result_code", resp.getStatus());
                        if (resp.getStatus() != 400) {
                            LOGGER.log(Level.SEVERE, "[QUERY TRANSACTION RESULT ] => CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
                            // subscriber.onNext(null);
                            // subscriber.onCompleted();
                            subscriber.onError(IErrors.REFUND_FAILED);
                        } else {
                            subscriber.onNext(null);
                            subscriber.onCompleted();
                        }

                    }

                });
                httpClientResponse.exceptionHandler(throwable -> {
                    subscriber.onError(IErrors.REFUND_FAILED);
                });
            });

            req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
            req.write(buffer);
            req.end();
        });
    }

    private Observable<DomesticTransaction> getTransaction(JDBCClient clientOnline, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return DomesticTransactionService.get(connOnline, transactionId);
                });
    }

    private Observable<Map> checkIsMerchantApproval(JDBCClient clientBackUp, JDBCClient clientOnline, String transactionId, String userId, String ipAddress, Double amount, String transactionRef, PatchRequest<RefundData> mapPatchRequest) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {

                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.merchantApproval.getMerchantApproval(connBackUp, mapPatchRequest.getValue().getMerchant_id())
                                        .flatMap(merchantApproval -> {
                                            String refundRef = UUID.randomUUID().toString();
                                            if (null != merchantApproval && !merchantApproval.isEmpty()) {
                                                LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                                                // return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                                return this.refundApprovalService.insertRequest3(connBackUp, userId, mapPatchRequest.getValue().getMerchant_id(),
                                                        transactionId, mapPatchRequest.getValue().getAmount(), "VND", refundRef, RefundData.Type.DOMESTIC.getValue(), mapPatchRequest.getValue().getNote()).flatMap(approval -> {

                                                            Map hashMap = new HashMap();
                                                            hashMap.put("refund", approval);
                                                            return Observable.just(hashMap);
                                                        });
                                            } else {
                                                // RefundReq requestRefund = new RefundReq(Long.valueOf(transactionId),
                                                // mapPatchRequest.getValue().getMerchant_id(), "VND",
                                                // amount, userId, ipAddress, 0);
                                                return this.merchantService.getDomesticData(connOnline, mapPatchRequest.getValue().getMerchant_id()).flatMap(merchantData -> {

                                                    Map refund = DomesticRefundUtil.createRefundUrl(mapPatchRequest.getValue().getMerchant_id(), merchantData.getAccessCode(), merchantData.getHashCode(), refundRef, transactionRef, userId, amount, mapPatchRequest.getValue().getNote(), ipAddress);
                                                    // Map requestRefund = new HashMap();
                                                    // requestRefund
                                                    Map hashMap = new HashMap();
                                                    hashMap.put("refund", refund);
                                                    return Observable.just(hashMap);
                                                });

                                            }
                                        });
                            });
                });
    }

    // Refund with String url
    private void doRefund2(RoutingContext rc, Map domesticRefund, HttpClient httpClient, JDBCClient clientOnline, JDBCClient clientBackUp, String purchaseRef, String userId, String transactionId, Boolean skipCallSynchronize)  {

        Object refund = domesticRefund.get("refund");
        if (refund instanceof Map) {
            String merchantId = ((Map<String, String>) refund).get("vpc_Merchant");
            if (Utils.isBigMerchant(merchantId)) {
                // TODO
                Map<String, String> dataPush = (Map<String, String>) refund;
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                            return merchantService.getDomesticData(connOnline, merchantId).flatMap(merchantData -> {
                                return Observable.just(merchantData);
                            });
                        }).subscribe(domData -> {
                            try {
                                JsonObject mapVpc = MSPClient.refundPurchaseND(dataPush);
                                // call ep dong bo
                                if (skipCallSynchronize == false) {
                                    String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                                    try {
                                        OneSchedClient.synchronize(jobId, "0s");
                                    } catch (Exception e) {
                                        LOGGER.log(Level.INFO, "domestic_synx error " + e.getMessage());
                                    }
                                }
                                unlock(transactionId, clientBackUp);
                                if (!mapVpc.containsKey("vpc_TxnResponseCode") || !mapVpc.getString("vpc_TxnResponseCode").equalsIgnoreCase("0")) {
                                    LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ND] =>  CODE : " + mapVpc.getString("vpc_TxnResponseCode") + " DESCRIPTION : " + mapVpc.getString("vpc_Message"));
                                    throw IErrors.REFUND_FAILED;
                                } else {
                                    getRefundData(clientOnline, clientBackUp, mapVpc.getString("vpc_MerchTxnRef")).subscribe(stringObjectMap -> {
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                                }
                            } catch (Exception ex) {
                                LOGGER.log(Level.WARNING, "REFUND DOM ERROR " + ex.getMessage());
                            }
                        }, throwable -> {
                            rc.fail(throwable);
                        });
            } else {
                byte[] encoded = FunctionUtil.encodeHessian(refund);
                Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
                HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

                req.putHeader("Content-Type", "x-application/hessian");
                LOGGER.log(Level.INFO, "CLIENT REFUND REQUEST DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(refund) );
                req.toObservable().subscribe(httpClientResponse -> {
                    httpClientResponse.bodyHandler(responseData -> {
                        // String json = responseData.toString("UTF-8");
                        io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                        byte[] bytesData = new byte[bufferDataResp.length()];

                        for (int i = 0; i < bufferDataResp.length(); i++) {
                            byte data = bufferDataResp.getByte(i);
                            bytesData[i] = data;
                        }
                        LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(FunctionUtil.decodeHessian(bytesData)) );

                        // call ep dong bo
                        if (skipCallSynchronize == false) {
                            String jobId = PropsUtil.get("onesched-service-domestic_synx_id", "");
                            try {
                                OneSchedClient.synchronize(jobId, "0s");
                            } catch (Exception e) {
                                LOGGER.log(Level.INFO, "domestic_synx error " + e.getMessage());
                            }
                        }
                        unlock(transactionId, clientBackUp);
                        Map<String, String> resp = (Map<String, String>) FunctionUtil.decodeHessian(bytesData);
                        if (!resp.get("vpc_TxnResponseCode").equalsIgnoreCase("0")) {
                            LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ] =>  CODE : " + resp.get("vpc_TxnResponseCode") + " DESCRIPTION : " + resp.get("vpc_Message"));
                            throw IErrors.REFUND_FAILED;
                        } else {
                            getRefundData(clientOnline, clientBackUp, resp.get("vpc_MerchTxnRef")).subscribe(stringObjectMap -> {
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                rc.next();
                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        }


                    });
                    httpClientResponse.exceptionHandler(throwable -> {
                        rc.fail(throwable);
                    });
                });

                req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
                req.write(buffer);
                req.end();
            }
        } else {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
            rc.next();
        }
        
    }

    // Refund with Request refund
    private void doRefund(RoutingContext rc, Map domesticRefund, HttpClient httpClient, JDBCClient clientOnline, JDBCClient clientBackUp) {

        Object refund = domesticRefund.get("refund");
        if (refund instanceof RefundReq) {

            RefundReq requestRefund = (RefundReq) refund;
            byte[] encoded = FunctionUtil.encodeHessian(requestRefund);
            Buffer buffer = Buffer.newInstance(io.vertx.core.buffer.Buffer.buffer(encoded));
            HttpClientRequest req = httpClient.requestAbs(HttpMethod.POST, refundConfig.getOneCommUrl());

            req.putHeader("Content-Type", "x-application/hessian");
            LOGGER.log(Level.INFO, "CLIENT REFUND REQUEST DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(requestRefund));
            req.toObservable().subscribe(httpClientResponse -> {
                httpClientResponse.bodyHandler(responseData -> {
                    // String json = responseData.toString("UTF-8");
                    io.vertx.core.buffer.Buffer bufferDataResp = (io.vertx.core.buffer.Buffer) responseData.getDelegate();
                    byte[] bytesData = new byte[bufferDataResp.length()];

                    for (int i = 0; i < bufferDataResp.length(); i++) {
                        byte data = bufferDataResp.getByte(i);
                        bytesData[i] = data;
                    }

                    LOGGER.log(Level.INFO, "CLIENT REFUND RESPONSE DATA" + StringPool.SPACE + StringPool.COLON + Utils.mask(FunctionUtil.decodeHessian(bytesData)));
                    RefundRes resp = (RefundRes) FunctionUtil.decodeHessian(bytesData);
                    if (resp.getStatus() != 400 && resp.getStatus() != 300) {
                        LOGGER.log(Level.SEVERE, "[RESULT FROM REFUND ] =>  CODE : " + resp.getStatus() + " DESCRIPTION : " + resp.getDescription());
                        throw IErrors.REFUND_FAILED;
                    } else {
                        getRefundData(clientOnline, clientBackUp, resp).subscribe(stringObjectMap -> {
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                            rc.next();
                        }, throwable -> {
                            rc.fail(throwable);
                        });
                    }
                });
                httpClientResponse.exceptionHandler(throwable -> {
                    rc.fail(throwable);
                });
            });

            req.putHeader(HttpHeaders.CONTENT_LENGTH + StringPool.BLANK, buffer.length() + StringPool.BLANK);
            req.write(buffer);
            req.end();
        } else {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, domesticRefund);
            rc.next();
        }

    }

    private Observable<Map<String, Object>> getRefundData(JDBCClient clientOnline, JDBCClient clientBackUp, String ref) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(conBackUp -> {
                                return domesticRefundService.getByRef(connOnline, ref).flatMap(domesticRefund2 -> {
                                    return userService.get(conBackUp, domesticRefund2.getOperator_id()).map(userData -> {
                                        if (userData != null) {
                                            domesticRefund2.setOperator_id(userData.getEmail());
                                        }

                                        Map<String, Object> hashMap = new HashMap();
                                        hashMap.put("refund", domesticRefund2);
                                        return hashMap;
                                    });
                                });
                            });
                });
    }


    private Observable<Map<String, Object>> getRefundData(JDBCClient clientOnline, JDBCClient clientBackUp, RefundRes resp) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(conBackUp -> {
                                return domesticRefundService.get(connOnline, conBackUp, String.valueOf(resp.getRefund_transactionId())).flatMap(domesticRefund2 -> {
                                    return userService.get(conBackUp, domesticRefund2.getOperator_id()).map(userData -> {
                                        if (userData != null) {
                                            domesticRefund2.setOperator_id(userData.getEmail());
                                        }

                                        Map<String, Object> hashMap = new HashMap();
                                        hashMap.put("refund", domesticRefund2);
                                        return hashMap;
                                    });
                                });
                            });
                });
    }

    private Observable<Double> getAmount(JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, PatchRequest<RefundData> mapPatchRequest) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {

                                            return DomesticTransactionService.listHistory(connReadOnly, connOnline, connBackup, transactionId).map(domesticTransactionHistories -> {
                                                double remainAmt = 0;
                                                double refundAmt = mapPatchRequest.getValue().getAmount();
                                                for (DomesticTransactionHistory his : domesticTransactionHistories) {
                                                    // Case purchase ->
                                                    if ("Purchase".equalsIgnoreCase(his.getTransaction_type())) {
                                                        remainAmt += his.getAmount().getTotal();
                                                    } else if (his.getAdvanced_status().equalsIgnoreCase("Successful") // case refund success
                                                    || (his.getTransaction_type().equalsIgnoreCase(RefundApproval.Status.REQUEST.content) // case request refund 405 || 401 => remainAmt += request refund amount
                                                            && (Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST.code)
                                                                    || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST_ONEPAY.code)
                                                                    || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.REQUEST_EXTERNAL.code)
                                                                    || Integer.valueOf(his.getStatus()).equals(RefundApproval.Status.WTF_ONEPAY_APPROVAL.code)))) {
                                                        remainAmt -= his.getAmount().getTotal();
                                                    }
                                                }

                                                if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                    return refundAmt;
                                                } else {
                                                    LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " + remainAmt + " DESCRIPTION : " + refundAmt);
                                                    throw IErrors.AMOUNT_REFUND_ERROR;
                                                }
                                            });
                                        });
                            });
                });
    }

    private Observable<Boolean> lock(SQLConnection connBackUp, String key, int timeoutMillis, long startTime, long delay) {
        return Observable.just(key).flatMap(approvalData -> {
            // insert approval for user group
            return this.lockService.lock(connBackUp, key, timeoutMillis, RefundData.Type.DOMESTIC.getValue()).flatMap(lock -> {
                if (lock == true)
                    return Observable.just(lock);
                if (timeoutMillis < System.currentTimeMillis() - startTime) {
                    return Observable.just(lock);
                } else {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    return lock(connBackUp, key, timeoutMillis, startTime, delay);
                }
            });
        });

    }

    public void unlock(String key, JDBCClient clientBackUp) {
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.lockService.unlock(connBackUp, key, RefundData.Type.DOMESTIC.getValue()).flatMap(lock -> {
                        return Observable.just(lock);
                    });
                }).subscribe();
    }

    @Autowired
    LockService lockService;

    @Autowired
    private MerchantApprovalService merchantApproval;

    @Autowired
    private RefundApprovalService refundApprovalService;

    @Autowired
    private UserService userService;

    @Autowired
    private RefundConfig refundConfig;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private DomesticRefundService domesticRefundService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionPatchHandler.class.getName());

}
