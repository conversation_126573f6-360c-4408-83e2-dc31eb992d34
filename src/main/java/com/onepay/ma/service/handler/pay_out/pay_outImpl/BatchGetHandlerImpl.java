package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.BatchDetailService;
import com.onepay.ma.service.service.pay_out.BatchService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


@Component
public class BatchGetHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(BatchGetHandlerImpl.class.getName());

    @Autowired
    private BatchService batchService;

    @Autowired
    private BatchDetailService batchDetailService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline= rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(fromDate);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ PAYOUT FUNDS TRANSFER BATCH GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        if (months > 6) {
            LOGGER.log(Level.SEVERE, "[ PAYOUT FUNDS TRANSFER BATCH GET ]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        String name = rc.request().getParam("name") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("name"));
        String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));

        Map<String, String> mIn = new HashMap();
        mIn.put(ParamsPool.FROM_DATE, fromDate);
        mIn.put(ParamsPool.TO_DATE, toDate);
        mIn.put("name", name);
        mIn.put(ParamsPool.STATE, state);
        mIn.put(ParamsPool.PAGESIZE, String.valueOf(pageSize));
        mIn.put(ParamsPool.PAGE, String.valueOf(page));

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return batchService.search(connOnline, mIn);
                }).subscribe(partners -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

}
