package com.onepay.ma.service.handler.transaction.promotion;

import com.onepay.ma.service.handler.transaction.promotion.impl.PromotionTransactionPatchHandlerImpl;
import com.onepay.ma.service.service.PromotionTransactionService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public interface PromotionTransactionPatchHandler extends Handler<RoutingContext> {
    static PromotionTransactionPatchHandlerImpl create(PromotionTransactionService promotionTransactionService){
        return new PromotionTransactionPatchHandlerImpl(promotionTransactionService);

    }
}
