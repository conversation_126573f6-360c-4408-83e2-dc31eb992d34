package com.onepay.ma.service.handler.quicklink;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import com.onepay.ma.service.handler.externalClient.QuickLinkClient;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.impl.MerchantServiceImpl;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import rx.Observable;

public class StaticLinkHandler {

    private static final String TOTAL_ITEMS = "totalItems";
    private static final String PROFILE_ID = "profileId";

    private static Logger logger = Logger.getLogger(StaticLinkHandler.class.getName());

    private static MerchantService merchantService;
    private static boolean isIncludes;

    public static void list(RoutingContext rc) {
        isIncludes = false;
        if (merchantService == null)
            merchantService = new MerchantServiceImpl();
        String userId = rc.get(ParamsPool.USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        logger.info(() -> "merchantId QUICKLINK:" + merchantId);
        if (xUserId == null) {
            logger.log(Level.SEVERE, "[STATIC LINK REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    // get back up connection
                    return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "international").flatMap(international -> {
                                    return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "domestic").flatMap(domestic -> {
                                        return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "mpay").flatMap(mpay -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "bnpl").map(bnpl -> {
                                                List<Merchant> allowedMerchant = new ArrayList<>();
                                                allowedMerchant.addAll(international);
                                                allowedMerchant.addAll(domestic);
                                                allowedMerchant.addAll(mpay);
                                                String merchantIds = allowedMerchant.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                String merchantIdBNPL = bnpl.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                String merchantAppleIds = international.stream()
                                                        .filter(item -> !item.getMerchant_id().equalsIgnoreCase("ALL"))
                                                        .map(Merchant::getMerchant_id)
                                                        .collect(Collectors.joining(","));
                                                if (!merchantIds.isEmpty() || !merchantIdBNPL.isEmpty() || !merchantAppleIds.isEmpty()) {
                                                    JsonObject result = QuickLinkClient.getStaticLink(userId, xUserId, xRequestId, merchantIds, merchantIdBNPL, merchantAppleIds);
                                                    return (result != null && result.getMap().size() > 0
                                                            && result.getMap().get("totalItems") != null && (int) result.getMap().get("totalItems") > 0)
                                                            ? result.getMap() : new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", true);
                                                } else {
                                                    return new JsonObject().put(TOTAL_ITEMS, 0).put("list", "").put("profileEmpty", true);
                                                }
                                            });
                                        });
                                    });
                                });
                            });
                }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, rc::fail);

        
    }

    public static List<Map<String, Object>> checkMerchantPermit(List<Merchant> merchants, JsonArray listReturn) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Merchant merchant : merchants) {
            String profileId = merchant.getMerchant_id();
            Map<String, Object> temp = new HashMap<>();
            temp.put(PROFILE_ID, profileId);
            List<String> arr = new ArrayList<>();
            for (int i = 0; i < listReturn.size(); i++) {
                if (listReturn.getJsonObject(i).getString("id").equals(profileId)) {
                    arr.add(listReturn.getJsonObject(i).getString("merchantId"));
                }
            }
            temp.put("list", arr);
            result.add(temp);
        }
        return result;
    }

    public static void detail(RoutingContext rc) {
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        try {
            JsonObject res = QuickLinkClient.getStaticDetail(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void changeState(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        JsonObject body = rc.getBodyAsJson();
        body.put("operator", xUserId);
        try {
            JsonObject res = QuickLinkClient.putStaticState(linkId, body);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }
}
