package com.onepay.ma.service.handler.terminal.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.service.TerminalService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by anhkh on 7/12/2016.
 */
public class TerminalGetHandler implements Handler<RoutingContext> {

    private TerminalService terminalService;

    public TerminalGetHandler(TerminalService terminalService) {
        this.terminalService = terminalService;
    }

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String merchantId = request.getParam("merchant_id") == null ? StringPool.BLANK : String.valueOf(request.getParam("merchant_id"));

        if(!merchantId.isEmpty()){
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));

            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return terminalService.getTerminalsByMerchant(connOnline, keywords, page, merchantId);
                    }).subscribe(terminals -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, terminals);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }else{
            throw IErrors.VALIDATION_ERROR;
        }

    }
}
