package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Terminal;
import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.models.notification.NotificationConfigSearchQuery;
import com.onepay.ma.service.models.shop.Store;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.TerminalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.notification.impl.AppNotificationConfigServiceImpl;
import com.onepay.ma.service.service.store.StoreService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * Created by anhkh on 26-Sep-17.
 */
@Component
public class MpayNotificationConfigGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String configId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);


        if (configId == null) {

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
            String terminalId = rc.request().getParam(ParamsPool.TERMINAL_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TERMINAL_ID));
            String shopId = rc.request().getParam(ParamsPool.SHOP_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.SHOP_ID));
            String type = rc.request().getParam(ParamsPool.TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TYPE));
            String userId = rc.request().getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_ID));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            NotificationConfigSearchQuery searchQuery = new NotificationConfigSearchQuery();
            searchQuery.setMerchantId(merchantId);
            searchQuery.setShopId(shopId);
            searchQuery.setTerminalId(terminalId);
            searchQuery.setType(type);
            searchQuery.setUserId(userId);
            searchQuery.setPage(page);
            searchQuery.setPageSize(pageSize);


            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return this.appNotificationService.search(connBackup, searchQuery).flatMap(configs -> {
                                        List<String> terminalIds = new ArrayList<>();
                                        List<String> storeIds = new ArrayList<>();
                                        List<String> userids = new ArrayList<>();
                                        for (MpayNotificationConfig config : configs.getList()) {
                                            terminalIds.addAll(config.getTerminalIds());
                                            storeIds.addAll(config.getStoreIds());
                                            userids.addAll(config.getUserIds());
                                        }
                                        return userService.listByIds(connBackup, String.join(StringPool.COMMA, userids.stream().distinct().collect(Collectors.toList()))).flatMap(userList -> {
                                        return terminalService.getTerminalsByIds(connOnline, String.join(StringPool.COMMA, terminalIds.stream().distinct().collect(Collectors.toList()))).flatMap(terminalList -> {
                                            return storeService.getStoresByIds(connOnline, String.join(StringPool.COMMA, storeIds.stream().distinct().collect(Collectors.toList()))).flatMap(storeList -> {
                                                Map<String, Terminal> terminalMap = terminalList.stream().collect(Collectors.toMap(Terminal::getTerminal_id, Function.identity()));
                                                Map<String, Store> storeMap = storeList.stream().collect(Collectors.toMap(Store::getStore_id, Function.identity()));
                                                Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getS_id, Function.identity()));
                                                for (MpayNotificationConfig config : configs.getList()) {

                                                    // set terminals
                                                    config.setTerminals(config.getTerminalIds().stream().map(tid -> {
                                                        return terminalMap.get(tid);
                                                    }).collect(Collectors.toList()));
                                                    config.setDisplayTerminalId(String.join(StringPool.COMMA, config.getTerminals()
                                                            .stream()
                                                            .map(Terminal::getTerminal_name)
                                                            .collect(Collectors.toList())
                                                    ));

                                                    // set stores
                                                    config.setStores(config.getStoreIds().stream().map(sid -> {
                                                        return storeMap.get(sid);
                                                    }).collect(Collectors.toList()));
                                                    config.setDisplayStoreId(String.join(StringPool.COMMA, config.getStores()
                                                            .stream()
                                                            .map(Store::getStore_name)
                                                            .collect(Collectors.toList())
                                                    ));

                                                    // set user
                                                    config.setUsers(config.getUserIds().stream().map(sid -> {
                                                        return userMap.get(sid);
                                                    }).filter(user -> user!= null).collect(Collectors.toList()));
                                                    config.setDisplayUserId(String.join(StringPool.COMMA, config.getUsers()
                                                            .stream()
                                                            .map(User::getEmail)
                                                            .collect(Collectors.toList())
                                                    ));

                                                }

                                                return Observable.just(configs);
                                            });
                                        });
                                        });
                                    });
                                });
                    }).subscribe(mpayNotificationConfigBaseList -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayNotificationConfigBaseList);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return this.appNotificationService.get(connBackup, Integer.valueOf(configId));
                    }).subscribe(config -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, config);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
            ;
        }

    }

    @Autowired
    private AppNotificationConfigServiceImpl appNotificationService;

    @Autowired
    private MerchantService merchantService;


    @Autowired
    private UserService userService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private StoreService storeService;

    private static Logger LOGGER = Logger.getLogger(MpayNotificationConfigGetHandler.class.getName());
}
