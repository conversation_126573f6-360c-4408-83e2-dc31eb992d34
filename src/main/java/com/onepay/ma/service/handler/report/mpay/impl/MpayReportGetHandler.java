package com.onepay.ma.service.handler.report.mpay.impl;

import com.onepay.ma.service.service.mpay.impl.MpayReportService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.mpay.MpayReport;
import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 04-Jun-18.
 */
@Component
public class MpayReportGetHandler implements Handler<RoutingContext> {

    @Autowired
    private MerchantService merchantService;


    private static final Logger LOGGER = Logger.getLogger(MpayReportGetHandler.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[MPAY REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROMDATE);
        String toDate = rc.request().getParam(ParamsPool.TODATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);

        } catch (Exception e) {

            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(oFromDate, oToDate);

        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ? 1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        if (interval == 1) {
            if (months > 1) {
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
        } else if (interval == 2) {
            if (months > 3) {
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
        } else {
            if (months > 12) {
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
        }
        String bankId = rc.request().getParam(ParamsPool.BANKID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.BANKID));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANTID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTID));
        String merchantName = rc.request().getParam(ParamsPool.MERCHANTNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANTNAME));
        String appName = rc.request().getParam(ParamsPool.APPNAME) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.APPNAME));
        String clientId = rc.request().getParam(ParamsPool.CLIENT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CLIENT_ID));
        String version = rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));


        MpayReportQuery query = new MpayReportQuery();
        query.setFromDate(fromDate);
        query.setToDate(toDate);
        query.setInterval(interval);
        query.setBankId(bankId);
        query.setMerchantName(merchantName);
        query.setAppName(appName);
        query.setClientId(clientId);
        query.setVersion(version);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return rx.Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {

                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "mpay").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                query.setMerchantId(String.join(",", merchantIdList));
                                                return MpayReportService.list(connReadOnly, query).map(mpayReports -> {
                                                    Map returnReport = new HashMap();

                                                    //if (domesticReportConverts.size() > 0) {
                                                    int totalTransCount = 0;
                                                    int totalRefundCount = 0;
                                                    double totalTransAmount = 0;
                                                    double totalRefundAmount = 0;
                                                    for (MpayReport items : mpayReports) {
                                                        totalTransCount += items.getTransactionCount();
                                                        totalRefundCount += items.getRefundCount();
                                                        totalTransAmount += items.getTransactionTotal();
                                                        totalRefundAmount += items.getRefundTotal();
                                                    }
                                                    returnReport.put("reports", mpayReports);
                                                    returnReport.put(ParamsPool.TOTALREFUNDTOTAL, totalRefundAmount);
                                                    returnReport.put(ParamsPool.TOTALTRANSACTIONTOTAL, totalTransAmount);
                                                    returnReport.put(ParamsPool.TOTALREFUNDCOUNT, totalRefundCount);
                                                    returnReport.put(ParamsPool.TOTALTRANSACTIONCOUNT, totalTransCount);
                                                    //}
                                                    return returnReport;
                                                });
                                            });
                                        });
                            });
                }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });
    }
}
