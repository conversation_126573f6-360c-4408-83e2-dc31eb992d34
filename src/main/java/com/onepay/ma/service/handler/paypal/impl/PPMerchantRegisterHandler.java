package com.onepay.ma.service.handler.paypal.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.externalClient.PayPalClient;
import com.onepay.ma.service.models.DomesticTransaction;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.service.partner.PartnerService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class PPMerchantRegisterHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {


//        PayPalClient.signupPrefillMerchant();
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ PP MERCHANT GENERATOR] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        Observable<Map> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackup -> {
                    return userServiceV2.getUserDataBySId(connBackup, userId).flatMap(userData -> {
                        return userServiceV2.getPartnerIdByUserId(connBackup, userId).flatMap(partner -> {
                            if (partner == null) {
                                throw IErrors.INVALID_USER_PARTNER;
                            }
                            return partnerService.getPartnerById(connBackup, partner.getN_partner_id()).map(partner1 -> {
                                JsonNode rePP = PayPalClient.signupPrefillMerchant(userData, partner1);
                                if (rePP == null) {
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                                JsonNode links = rePP.get("links");
                                Iterator<JsonNode> i = links.iterator();
                                while (i.hasNext()) {
                                    JsonNode current = i.next();
                                    if (current.get("rel").asText().equalsIgnoreCase("action_url")) {
                                        ObjectMapper mapper = new ObjectMapper();
                                        Map<String, Object> result = mapper.convertValue(current, Map.class);
                                        return result;
                                    }
                                }
                                LOGGER.log(Level.SEVERE, "[ PP MERCHANT GENERATOR] => URL NOT FOUND");
                                throw IErrors.INTERNAL_SERVER_ERROR;
                            });
                        });
                    });
                });

        obs.subscribe(result -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    @Autowired
    private UserServiceV2 userServiceV2;


    @Autowired
    private PartnerService partnerService;

    private static final Logger LOGGER = Logger.getLogger(PPMerchantRegisterHandler.class.getName());
}
