package com.onepay.ma.service.handler.externalClient;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.ma.service.util.PropsUtil;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;

/**
 * Created by HungDX on 08-Aug-16
 */
public class CardHelperClient {


    private static final Logger LOGGER = Logger.getLogger(CardHelperClient.class.getName());
    private static String ONEPAY_CARDHERLER_SERVICE_BASE_URL = PropsUtil.get("onepay_card_helper_service_base_url", "");
    private static String ONEPAY_CARDHERLER_SERVICE_CLIENT_ID = PropsUtil.get("onepay_card_helper_service_client_id", "");
    private static String ONEPAY_CARDHERLER_SERVICE_CLIENT_KEY = PropsUtil.get("onepay_card_helper_service_client_key", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }



    public static JsonObject recharge(String merchantId, String orgTxnRef, String txnRef, String orderInfo, Double amount, String xRequestId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/card-helper/api/v1/recharge";
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 120000;

        JsonObject jo = new JsonObject();
        jo.put("merchant_id", merchantId);
        jo.put("org_merchant_txn_ref", orgTxnRef);
        jo.put("merchant_txn_ref", txnRef);
        jo.put("order_info", orderInfo);
        jo.put("amount", String.format("%.0f", amount * 100));
        try {
            // // Create service signature with OnePAY Web Service Signature algorithm
            LOGGER.log(Level.INFO, "BODY bdJson:" + jo.encodePrettily());
            LOGGER.log(Level.INFO, "POST TO CARDHELPER-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_CARDHERLER_SERVICE_BASE_URL + requestURI);
            String rechareUrl = ONEPAY_CARDHERLER_SERVICE_BASE_URL + requestURI;



            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "text/html");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));


            URL url = new URL(rechareUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(requestTimeOut);
            connection.setReadTimeout(requestTimeOut);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jo.encode().getBytes(UTF_8).length));
            connection.setRequestProperty("X-OP-Request-ID", xRequestId);
            connection.setRequestProperty("Authorization", "Basic " + Base64.getEncoder().encodeToString((ONEPAY_CARDHERLER_SERVICE_CLIENT_ID + ":" + ONEPAY_CARDHERLER_SERVICE_CLIENT_KEY).getBytes()));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jo.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();

            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
            jsonReturn = new JsonObject(strReturn);
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

}
