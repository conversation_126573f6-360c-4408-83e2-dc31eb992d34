package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserFileGetHandlerImpl;
import com.onepay.ma.service.service.FileService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/9/16.
 */

public interface UserFileGetHandler extends Handler<RoutingContext> {
    static UserFileGetHandlerImpl create(FileService fileService){
        return new UserFileGetHandlerImpl(fileService);
    }
}
