package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_out.MerchantAccountService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import org.apache.commons.lang.StringUtils;
import io.vertx.core.json.JsonObject;

@Component
public class MerchantAccountHandlerImpl implements Handler<RoutingContext> {

    private static final Logger LOGGER = Logger.getLogger(MerchantAccountHandlerImpl.class.getName());

    @Autowired
    private MerchantAccountService merchantAccountService;

    @Autowired
    private MerchantService merchantService;

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String partnerId = myEvent.get(ParamsPool.X_PARTNER_ID);
        JDBCClient myClientReadOnly = myEvent.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient myClientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient myClientBackUp = myEvent.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        JsonObject body = myEvent.getBodyAsJson();

        String merchantId = body.getString("merchantId");
        String maMain = body.getString(ParamsPool.MA_MAIN);
        LOGGER.log(Level.INFO, "xUserId: {}", xUserId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain v1: ");
            String merchantIds = MaPermission.getMc(xUserId, xRequestId, partnerId, merchantId);
            Observable<Object> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(myClientOnline), f -> f.dispose()).flatMap(connOnline -> {
                return merchantAccountService.getListMerchantAccount(connOnline, merchantIds)
                    .map(listMerchantAccount -> {
                        BaseList baseList = new BaseList();
                        baseList.setTotal_items(listMerchantAccount.size());
                        baseList.setList(listMerchantAccount);
                        return baseList;
                    });

            });

            obs.subscribe(listMerchantAccount -> {
                myEvent.put(ParamsPool.HANDLER_DATA_RESULT, listMerchantAccount);
                myEvent.next();
            }, throwable -> {
                myEvent.fail(throwable);
            });
        } else  {
            Observable<Object> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(myClientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(myClientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(myClientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {

                                            return merchantService.list(connOnline, connBackUp, merchantId, xUserId, "payout").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                return merchantAccountService.getListMerchantAccount(connOnline, String.join(",", merchantIdList)).map(listMerchantAccount -> {
                                                    BaseList baseList = new BaseList();
                                                    baseList.setTotal_items(listMerchantAccount.size());
                                                    baseList.setList(listMerchantAccount);
                                                    return  baseList;
                                                });
                                             });
                                            //return list firm banking transaction

                                        });
                            });
                });
            obs.subscribe(listMerchantAccount -> {
                myEvent.put(ParamsPool.HANDLER_DATA_RESULT, listMerchantAccount);
                myEvent.next();
            }, throwable -> {
                myEvent.fail(throwable);
            });
        }

    }
}
