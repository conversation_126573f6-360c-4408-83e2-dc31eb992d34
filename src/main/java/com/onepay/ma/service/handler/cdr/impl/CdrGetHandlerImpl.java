package com.onepay.ma.service.handler.cdr.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.cdr.CdrGetHandler;
import com.onepay.ma.service.handler.user.UserPatchHandler;
import com.onepay.ma.service.models.CdrParameter;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class CdrGetHandlerImpl implements CdrGetHandler {

    public CdrGetHandlerImpl(CdrService cdrService) {
        this.cdrService = cdrService;
    }

    @Override
    public void handle(RoutingContext rc) {
        final HttpServerRequest request = rc.request();
        String compareId = request.getParam("compareId") == null ? StringPool.BLANK : String.valueOf(request.getParam("compareId"));

        JDBCClient clientCdr = rc.get(ParamsPool.CDR_DATASOURCE_NAME);

        if(compareId.isEmpty()) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => BODY PATCH IS EMPTY " );
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if(months > 12){
                LOGGER.log(Level.SEVERE, "[ APPROVAL PROMOTION PATCH ] => SEARCH TOO LARGE NARROW DATA " );
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
               // throw new InvalidValueException("This search you have requested is too large.  Please select information or narrow date range to within 1 year and retry.");
            }
            String transactionType = request.getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.TRANSACTION_TYPE));

            String info = request.getParam(ParamsPool.INFO) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.INFO));

            String state = request.getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.STATE));

            String service = request.getParam(ParamsPool.SERVICE) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SERVICE));

            String sourceLeft = request.getParam(ParamsPool.SOURCE_LEFT) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SOURCE_LEFT));

            String sourceRight = request.getParam(ParamsPool.SOURCE_RIGHT) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.SOURCE_RIGHT));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            CdrParameter parameter = new CdrParameter();
            parameter.setFromDate(fromDate);
            parameter.setToDate(toDate);
            parameter.setTransactionType(transactionType);
            parameter.setInfo(info);
            parameter.setState(state);
            parameter.setService(service);
            parameter.setSourceLeft(sourceLeft);
            parameter.setSourceRight(sourceRight);
            parameter.setPageSize(pageSize);
            parameter.setPage(page);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientCdr), f-> f.dispose()).flatMap(connCdr -> {
                return cdrService.list(connCdr, parameter);
            }).subscribe(compareCdrTransactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, compareCdrTransactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });;

        }else{

        }

    }


    private static Logger LOGGER = Logger.getLogger(CdrGetHandler.class.getName());

    private CdrService cdrService;

}
