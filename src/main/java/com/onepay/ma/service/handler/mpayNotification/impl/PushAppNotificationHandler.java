package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.DateTimeUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.models.notification.MpayNotificationPostDto;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserMerchantService;
import com.onepay.ma.service.service.mpay.MpayOrderService;
import com.onepay.ma.service.service.apptoken.AppTokenService;
import com.onepay.ma.service.service.notification.AppNotificationConfigService;
import com.onepay.ma.service.service.notification.AppNotificationService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class PushAppNotificationHandler implements Handler<RoutingContext> {

    public static String API_KEY = PropsUtil.get("onepay_google_notifucation_api_key", ""); // mOnePAY

    public static String API_URL = PropsUtil.get("onepay_google_notifucation_api_url", ""); // mOnePAY

    // public static String VI_SUCCESS = "Giao dich THANH CONG tai Taifar Xa Dan (Merchant Name):
    // 360,000 VND - so the *176 - 12/09 14:20:05";
    public static String VI_SUCCESS_FORMAT = "Giao dich THANH CONG tai {0}: {1} {2} - so the {3} - {4}";
    public static String EN_SUCCESS_FORMAT = "Payment SUCCESS at {0}: {1} {2} - card No. {3} - {4}";
    public static String VI_FAIL_FORMAT = "Giao dich THAT BAI tai {0}: {1} {2} - so the {3} - {4}";
    public static String EN_FAIL_FORMAT = "Payment FAILED at {0}: {1} {2} - card No. {3} - {4}";

    public static String VI_PUSH_HEADER = "Thông báo giao dịch thành công";
    public static String VI_PUSH_CONTENT = "{0} Số tiền: {1}{2} {3}. {4} thanh toán qua {5}";
    public static String EN_PUSH_HEADER = "New successful transaction notification";
    public static String EN_PUSH_CONTENT = "{0} Amount: {1}{2} {3}. {4} payment via {5}";


    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);

        LOGGER.log(Level.INFO, "LISTEN TO APP NOTIFICATION/n");
        // LOGGER.log(Level.INFO, "CONTENT: " + rc.getBodyAsString());

        JsonObject body = rc.getBodyAsJson();
        if (body == null) {
            LOGGER.log(Level.SEVERE, "APP NOTIFICATION ERROR: invalid content");
            throw IErrors.VALIDATION_ERROR;
        }

        String eventType = body.getString(ParamsPool.EVENT_TYPE);
        String resourceTyps = body.getString(ParamsPool.RESOURCE_TYPE);
        JsonObject resource = body.getJsonObject(ParamsPool.RESOURCE);
        JsonObject instrument = resource.getJsonObject(ParamsPool.INSTRUMENT);
        JsonObject parent = resource.getJsonObject(resourceTyps.equals("payment") ? ParamsPool.INVOICE : ParamsPool.PARENT_RESOURCE);
        if (parent == null) {
            LOGGER.log(Level.SEVERE, "mPAY NOTIFICATION ERROR: invalid parent content");
            throw IErrors.VALIDATION_ERROR;
        }
        String merchantId = parent.getString(ParamsPool.MERCHANT_ID);
        if (merchantId == null) {
            LOGGER.log(Level.SEVERE, "mPAY NOTIFICATION ERROR: invalid merchant ID");
            throw IErrors.MERCHANT_DATA_ERROR;
        }
        JsonObject resourceContent = resource.getJsonObject("content");
        String resourceContentVI = resourceContent == null ? StringPool.BLANK : resourceContent.getString("vi");
        String resourceContentEN = resourceContent == null ? StringPool.BLANK : resourceContent.getString("en");
        JsonObject resourceTitle = resource.getJsonObject("title");
        String resourceTitleVI = resourceTitle == null ? StringPool.BLANK : resourceTitle.getString("vi");
        String resourceTitleEN = resourceTitle == null ? StringPool.BLANK : resourceTitle.getString("en");
        Double amount = resource.getDouble(ParamsPool.AMOUNT) == null ? null : resource.getDouble(ParamsPool.AMOUNT);
        String currency = resource.getString(ParamsPool.CURRENCY) == null ? StringPool.BLANK : resource.getString(ParamsPool.CURRENCY);
        String cardNumber = instrument.getString(ParamsPool.NUMBER) == null ? StringPool.BLANK : instrument.getString(ParamsPool.NUMBER).substring(13);
        String terminalId = parent.getString(ParamsPool.TERMINAL_ID) == null ? StringPool.BLANK : parent.getString(ParamsPool.TERMINAL_ID);
        String storeId = parent.getString(ParamsPool.STORE_ID) == null ? StringPool.BLANK : parent.getString(ParamsPool.STORE_ID);
        String summary = parent.getString(ParamsPool.SUMMARY) == null ? StringPool.BLANK : parent.getString(ParamsPool.SUMMARY);
        String invoice_id = parent.getString(ParamsPool.ID) == null ? StringPool.BLANK : parent.getString(ParamsPool.ID);
        String state = parent.getString(ParamsPool.STATE) == null ? StringPool.BLANK : parent.getString(ParamsPool.STATE);
        Date createTime = parent.getString(ParamsPool.CREATE_TIME) == null ? null : DateTimeUtil.convertToGTM7(DateTimeUtil.convertStringtoDate(parent.getString(ParamsPool.CREATE_TIME), DateTimeUtil.DateTemplate.YYYYMMDDTHHmmssZ));

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(sqlBackUp -> {
                    this.connectionBackup = sqlBackUp;
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(sqlOnline -> {
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                                        .flatMap(sqlReadOnly -> {
                                            return mpayOrderService.getTransactionByInvoice(sqlOnline, invoice_id).flatMap(transaction -> {
                                                if (transaction == null) {
                                                    LOGGER.log(Level.SEVERE, "APP NOTIFICATION ERROR: transaction success not found");
                                                    throw IErrors.RESOURCE_NOT_FOUND;
                                                }

                                                String service = String.valueOf(transaction.get("service"));
                                                String transId = String.valueOf(transaction.get("id"));
                                                String orderInfo = String.valueOf(transaction.get("orderInfo"));
                                                String cardNum = String.valueOf(transaction.get("cardNumber"));
                                                String cardType = String.valueOf(transaction.get("cardType"));
                                                String transType = String.valueOf(transaction.get("transactionType"));
                                                String transStatus = String.valueOf(transaction.get("transactionStatus"));
                                                String sDate = String.valueOf(transaction.get("transactionDate"));

                                                Timestamp transDate = Timestamp.valueOf(sDate);
                                                JsonObject target = new JsonObject();
                                                target.put("invoice_id", invoice_id);
                                                target.put("payment_time", parent.getString(ParamsPool.CREATE_TIME));
                                                target.put("service", service);
                                                target.put("id", transId);
                                                target.put("amount", amount);
                                                target.put("currency", currency);
                                                target.put("orderInfo", orderInfo);
                                                target.put("cardNumber", cardNum);
                                                target.put("cardType", cardType);
                                                target.put("transactionType", transType);
                                                target.put("transactionStatus", transStatus);
                                                target.put("transactionDate", new SimpleDateFormat("dd/MM/YYYY HH:mm").format(transDate));

                                                return this.merchantService.getMpayMerchant(sqlReadOnly, merchantId).flatMap(mpayMerchant -> {
                                                    return userMerchantService.listUserIdByMerchant(sqlBackUp, merchantId, service).flatMap(foundedUserIds -> {
                                                        if (foundedUserIds.isEmpty()) {
                                                            throw IErrors.RESOURCE_NOT_FOUND;
                                                        }
                                                        List<String> distintUserIds = Arrays.asList(String.join(StringPool.COMMA, foundedUserIds).split(StringPool.COMMA)).stream().distinct().collect(Collectors.toList());
                                                        String userIdsString = String.join(StringPool.COMMA, distintUserIds);

                                                        LOGGER.log(Level.INFO, "FOUNDED USER ID: " + userIdsString);

                                                        // convert to list of notification
                                                        List<MpayNotificationPostDto> notifications = distintUserIds.stream().map(userId -> {

                                                            MpayNotificationPostDto notification = new MpayNotificationPostDto();

                                                            String formatVI = eventType.equals("PAYMENT.APPROVED") ? VI_SUCCESS_FORMAT : VI_FAIL_FORMAT;
                                                            String formatEN = eventType.equals("PAYMENT.APPROVED") ? EN_SUCCESS_FORMAT : EN_FAIL_FORMAT;
                                                            notification.setContent_en(MessageFormat.format(formatEN, mpayMerchant.getName(), amount, currency, cardNumber, DateTimeUtil.convertDatetoString(createTime, DateTimeUtil.DateTemplate.DD_MM_HH_mm_ss)));
                                                            notification.setContent_vi(MessageFormat.format(formatVI, mpayMerchant.getName(), amount, currency, cardNumber, DateTimeUtil.convertDatetoString(createTime, DateTimeUtil.DateTemplate.DD_MM_HH_mm_ss)));

                                                            notification.setHeader_en(eventType.equals("PAYMENT.APPROVED") ? "Payment successful" : "Payment failed");
                                                            notification.setHeader_vi(eventType.equals("PAYMENT.APPROVED") ? "Giao dịch thành công" : "Giao dịch thất bại");

                                                            // case notification
                                                            if(eventType.equalsIgnoreCase("NOTIFICATION")) {
                                                                notification.setContent_en(resourceContentEN);
                                                                notification.setContent_vi(resourceContentVI);
                                                                notification.setHeader_en(resourceTitleEN);
                                                                notification.setHeader_vi(resourceTitleVI);
                                                            }

                                                            notification.setTarget(target.encode());
                                                            notification.setCategory("TRANSACTION");
                                                            notification.setUserId(userId);
                                                            return notification;
                                                        }).collect(Collectors.toList());

                                                        return connectionBackup.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                            // Store notification by UserId
                                                            return this.insertNotificationForUser(connectionBackup, notifications, 0).flatMap(mpayNotifications -> {
                                                                return appTokenService.findByUserIds(connectionBackup, userIdsString).map(foundedToken -> {

                                                                    foundedToken = foundedToken.stream().distinct().collect(Collectors.toList());

                                                                    LOGGER.log(Level.INFO, "FOUNDED TOKEN: " + foundedToken);

                                                                    try {
                                                                        String userTokenFireBase = Utils.getAccessToken();
           
                                                                        for (AppToken token : foundedToken) {
                                                                            // send notification
                                                                            String header = "vi".equals(token.getLanguage()) ? VI_PUSH_HEADER : EN_PUSH_HEADER;
                                                                            String messageContent = formatPushContent(token.getLanguage(), transDate, transType, amount, currency, orderInfo, cardType, service);
                                                                            if ("vi".equals(token.getLanguage())) {
                                                                                header = new String(header.getBytes(StandardCharsets.UTF_8));
                                                                                messageContent = new String(messageContent.getBytes(StandardCharsets.UTF_8));
                                                                            }
                                                                            this.sendNotification(target.encode(), header, messageContent, token, 
                                                                                    userTokenFireBase);
                                                                        }
                                                                    } catch (IOException e) {
                                                                        LOGGER.log(Level.SEVERE,"Notify not send ", e);
                                                                        LOGGER.log(Level.SEVERE,"userTokenFireBase cant get, Error! ", e);
                                                                    }
                                                                    return foundedToken;
                                                                });
                                                            });
                                                        });
                                                    });
                                                });

                                            });
                                        });
                            });

                }).subscribe(r -> {
                    if (connectionBackup != null) {
                        connectionBackup.commitObservable();
                    }

                    rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
                    rc.next();
                }, throwable -> {

                    if (connectionBackup != null) {
                        connectionBackup.rollbackObservable();
                    }
                    rc.fail(throwable);
                });
    }

    private String formatPushContent(String lang, Date transDate, String transType, Double amount, String currency, String orderInfo, String cardType, String service) {
        String[] positiveTypes = {"Purchase", "Capture", "Authorize", "Pay Later"};
        String[] negativeTypes = {"Refund", "Refund Capture", "Void Purchase", "Void Capture", "Request Refund"};
        String amountPrefix = "";
        if (Arrays.stream(positiveTypes).anyMatch(transType::equals)) {
            amountPrefix = "+";
        } else if (Arrays.stream(negativeTypes).anyMatch(transType::equals)) {
            amountPrefix = "-";
        }

        if ("bnpl".equals(service)) {
            cardType = "BNPL " + cardType;
        }

        String format = "vi".equals(lang) ? VI_PUSH_CONTENT : EN_PUSH_CONTENT;

        String messageContent = MessageFormat.format(format, new SimpleDateFormat("HH:mm dd/MM/YYYY").format(transDate),
                amountPrefix, amount, currency, orderInfo, cardType);
        return messageContent;
    }

    private Observable<List<MpayNotificationPostDto>> insertNotificationForUser(SQLConnection connectionBackup, List<MpayNotificationPostDto> listData, int index) {
        if (listData.size() <= 0) {
            return Observable.just(listData);
        }
        MpayNotificationPostDto notify = listData.get(index);
        final int finalIndex = index;
        return Observable.just(notify).flatMap(notifyData -> {
            // insert approval for user group
            return this.appNotificationService.insert(connectionBackup, notifyData).flatMap(id -> {
                if (finalIndex >= listData.size() - 1) {
                    return Observable.just(listData);
                } else {
                    return insertNotificationForUser(connectionBackup, listData, finalIndex + 1);
                }
            });
        });

    }

    private void sendNotification(String invoice, String messageHeader, String messageContent, AppToken token, String userTokenFireBase) {

        try {

            // Prepare JSON containing the GCM message content. What to send and where to send.
            JsonObject jGcmData = new JsonObject();
            JsonObject jGcmMessNew = new JsonObject();

            if (token.getOs().equalsIgnoreCase("IOS")) {
                JsonObject jNotification = new JsonObject();
                jNotification.put("title", messageHeader);
                jNotification.put("body", messageContent);
                // jNotification.put("sound", "default");
                // jNotification.put("badge", 1);
                jGcmData.put("notification", jNotification);
                jGcmData.put("priority", "high");
            }

            if (token.getOs().equalsIgnoreCase("ANDROID")) {
                JsonObject jNotification = new JsonObject();
                jNotification.put("title", messageHeader);
                jNotification.put("body", messageContent);
                // jNotification.put("sound", "default");
                jGcmData.put("notification", jNotification);
            }
            // jGcmData.put("to", token.getToken());
            jGcmData.put("token", token.getToken());

            JsonObject jData = new JsonObject();
            jData.put("target", invoice);
            // What to send in GCM message.
            jGcmData.put("data", jData);
            // What to send in GCM message New.
            jGcmMessNew.put("message", jGcmData);

            // Create connection to send GCM Message request.
            URL url = new URL(API_URL);
            // URL url = new URL("https://fcm.googleapis.com/fcm/send");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // conn.setRequestProperty("Authorization", "key=" + API_KEY);
            conn.setRequestProperty("Authorization", "Bearer " + userTokenFireBase);

            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);


            LOGGER.log(Level.INFO, "SEND NOTIFICATION TO TOKEN: " + token.getToken());
            LOGGER.log(Level.INFO, "SEND NOTIFICATION WITH LANGUAGE: " + token.getLanguage());
            LOGGER.log(Level.INFO, "SEND NOTIFICATION WITH CONTENT: " + jGcmMessNew.toString());
            // Send GCM message content.
            OutputStream outputStream = conn.getOutputStream();
            outputStream.write(jGcmMessNew.toString().getBytes());
            // Read GCM response.
            InputStream inputStream = conn.getInputStream();
            String resp = IOUtils.toString(inputStream);

            // LOGGER.log(Level.INFO, "RESPONSE: " + resp);
            LOGGER.log(Level.INFO, "Check your device/emulator for notification or logcat for " +
                    "confirmation of the receipt of the GCM message.");
        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "Unable to send GCM message.");
            LOGGER.log(Level.WARNING, "Please ensure that API_KEY has been replaced by the server " +
                    "API key, and that the device's registration token is correct (if specified).");
            LOGGER.log(Level.WARNING, "Error ", e);
        }
    }


    @Autowired
    private AppNotificationConfigService appNotificationConfigService;


    @Autowired
    private AppTokenService appTokenService;

    @Autowired
    private AppNotificationService appNotificationService;

    @Autowired
    private MpayOrderService mpayOrderService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private UserMerchantService userMerchantService;

    private SQLConnection connectionBackup;

    private static Logger LOGGER = Logger.getLogger(PushAppNotificationHandler.class.getName());
}
