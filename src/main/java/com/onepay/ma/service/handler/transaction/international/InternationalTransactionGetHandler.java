package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.InternationalTransactionGetHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.MidService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalTransactionGetHandler extends Handler<RoutingContext>  {
    static InternationalTransactionGetHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, MidService midService, ServerConfig serverConfig){
        return new InternationalTransactionGetHandlerImpl(internationalTransactionService, merchantService, midService, serverConfig);

    }
}
