package com.onepay.ma.service.handler.role.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.role.RolePutHandler;
import com.onepay.ma.service.models.RoleParam;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/12/16.
 */
public class RolePutHandlerImpl implements RolePutHandler {

    public RolePutHandlerImpl(RoleService roleService, CacheGuava cacheGuava) {
        this.roleService = roleService;
        this.cacheGuava = cacheGuava;
    }

    @Override
    public void handle(RoutingContext rc) {
        String roleId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(roleId == null) {
            throw IErrors.VALIDATION_ERROR;
        }else{
            String json = rc.getBodyAsString();
            RoleParam param = gson.fromJson(json, RoleParam.class);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        connectionBackUp = connBackUp;
                        return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                            return roleService.update(connectionBackUp, param, roleId);
                        });
                    }).subscribe(roleData -> {
                if(connectionBackUp != null){
                    connectionBackUp.commitObservable();
                }
                cacheGuava.get().invalidateAll();
                rc.put(ParamsPool.HANDLER_DATA_RESULT, roleData);
                rc.next();
            }, throwable -> {
                if(connectionBackUp != null){
                    connectionBackUp.rollbackObservable();
                }
                rc.fail(throwable);
            });

        }
    }


    private SQLConnection connectionBackUp;
    private final Gson gson =new Gson();
    private CacheGuava cacheGuava;
    private RoleService roleService;
    private static final Logger LOGGER = Logger.getLogger(RolePutHandler.class.getName());
}
