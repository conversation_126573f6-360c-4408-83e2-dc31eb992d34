package com.onepay.ma.service.handler.user.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.user.UserProfileGetHandler;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by anhkh on 10-Apr-18.
 */
public class UserProfileGetHandlerImpl implements UserProfileGetHandler {


    public UserProfileGetHandlerImpl(UserService userService) {
        this.userService = userService;
    }

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient client = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId.isEmpty()) {
            throw IErrors.RESOURCE_NOT_FOUND;
        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return userService.getProfileInfo(sqlConnection, connBackup, userId);
                                });
                    }).subscribe(user -> {
                if (user == null) {
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
                rc.put(ParamsPool.HANDLER_DATA_RESULT, user);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

        }

    }

    private UserService userService;
}
