package com.onepay.ma.service.handler.report.general.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component
public class GeneralReportDownloadHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        // JsonObject bodyJson = rc.getBodyAsJson();
        // GeneralReportQuery mapBody = new GeneralReportQuery();
        // mapBody.setCardNumber(bodyJson.getString("cardNumber"));
        // mapBody.setCardType(bodyJson.getString("cardType"));
        // mapBody.setFrom_date(bodyJson.getString("fromDate"));
        // mapBody.setTo_date(bodyJson.getString("toDate"));
        // mapBody.setMerchantId(bodyJson.getString("merchantId"));
        // mapBody.setMerchantTransactionRef(bodyJson.getString("merchantTransactionRef"));
        // mapBody.setTransactionId(bodyJson.getString("transactionId"));
        // mapBody.setOrderInfo(bodyJson.getString("orderInfo"));
        // mapBody.setTransactionType(bodyJson.getString("transactionType"));
        // mapBody.setTransactionState(bodyJson.getString("transactionState"));
        String body = rc.getBodyAsString();
        String target = rc.request().getParam(ParamsPool.TARGET) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TARGET));
        GeneralReportQuery mapBody = gson.fromJson(body, GeneralReportQuery.class);
        mapBody.setFrom_date(rc.get("fromDate"));
        mapBody.setTo_date(rc.get("toDate"));
        mapBody.setTarget(target);
        if (mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return merchantService
                                                    .list(connOnline, connectBackUp, mapBody.getMerchantId(), userId, "international")
                                                    .flatMap(merchantsQt -> {
                                                        return merchantService
                                                                .list(connOnline, connectBackUp, mapBody.getMerchantId(), userId, "domestic")
                                                                .flatMap(merchantsNd -> {
                                                                    return merchantService
                                                                            .list(connOnline, connectBackUp, mapBody.getMerchantId(), userId, "mpay")
                                                                            .flatMap(merchantsQr -> {
                                                                                List<String> merchantIdQtList = merchantsQt.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                List<String> merchantIdNdList = merchantsNd.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                List<String> merchantIdQrList = merchantsQr.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                                mapBody.setMerchantIdQt(String.join(",", merchantIdQtList));
                                                                                mapBody.setMerchantIdNd(String.join(",", merchantIdNdList));
                                                                                mapBody.setMerchantIdQr(String.join(",", merchantIdQrList));

                                                                                Map data = new HashMap();
                                                                                return generalReportService.totalRecord(connOnline, connReadOnly, connectBackUp, mapBody).flatMap(integer -> {
                                                                                    if (integer == 0) {
                                                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                                                    }
                                                                                    if (integer >= serverConfig.getRowLevel()) {

                                                                                        throw IErrors.REACH_MAX_EXCEL_ROW;
                                                                                    }
                                                                                    String fileName = "general" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
                                                                                    long date = new java.util.Date().getTime();
                                                                                    fileName += date;
                                                                                    String fileHashName = "";
                                                                                    data.put("parameter", mapBody);
                                                                                    data.put("file_name", fileName);
                                                                                    data.put("row", integer);
                                                                                    try {
                                                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                                                    } catch (NoSuchAlgorithmException e) {
                                                                                        rc.fail(e);
                                                                                    } catch (UnsupportedEncodingException e) {
                                                                                        rc.fail(e);
                                                                                    }
                                                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                                                    requestData.put(ParamsPool.FILE_EXT, "xlsx");
                                                                                    FileDownload fileDownload = new FileDownload();
                                                                                    fileDownload.setUser(userId);
                                                                                    fileDownload.setFile_type("general_transaction");
                                                                                    fileDownload.setFile_name(fileName);
                                                                                    fileDownload.setFile_hash_name(fileHashName);
                                                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                                                    if (integer <= serverConfig.getRowLevel()) {
                                                                                        fileDownload.setExt("xlsx");
                                                                                    } else {
                                                                                        fileDownload.setExt("zip");
                                                                                    }
                                                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                                                            return data;
                                                                                        });
                                                                                    });
                                                                                });
                                                                            });
                                                                });
                                                    });
                                        });
                            });
                }).subscribe(map -> {
                    if (connectBackUp != null) {
                        connectBackUp.commitObservable();
                    }
                    GeneralReportQuery parameter = (GeneralReportQuery) map.get("parameter");
                    int row = Integer.valueOf(map.get("row").toString());
                    if (row <= serverConfig.getRowLevel()) {
                        // fileDownload.setExt("csv");
                        Message<GeneralReportQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                        QueueProducer.sendMessage(message);
                    } else {
                        // fileDownload.setExt("zip");
                        Message<GeneralReportQuery> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                        QueueProducer.sendMessage(message);
                    }

                    rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                    rc.next();
                }, throwable -> {
                    if (connectBackUp != null) {
                        connectBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

    }

    @Autowired
    private GeneralReportService generalReportService;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();


    private static final Logger LOGGER = Logger.getLogger(GeneralReportDownloadHandler.class.getName());
}
