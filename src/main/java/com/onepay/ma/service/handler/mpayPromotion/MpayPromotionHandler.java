package com.onepay.ma.service.handler.mpayPromotion;

import com.onepay.ma.service.handler.mpayPromotion.impl.MpayPrReportGetHandler;
import com.onepay.ma.service.handler.mpayPromotion.impl.MpayPromotionGetHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 26-Jan-18.
 */
@Component
public class MpayPromotionHandler {

    @Autowired
    private MpayPromotionGetHandler mpayPromotionGetHandler;

    @Autowired
    private MpayPrReportGetHandler mpayPrReportGetHandler;


    public Handler get() {
        return mpayPromotionGetHandler;
    }

    public Handler getReport() {
        return mpayPrReportGetHandler;
    }
}
