package com.onepay.ma.service.handler.store.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.service.store.StoreService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 29-Sep-17.
 */
@Component
public class StoreGetHandler implements Handler<RoutingContext> {

    @Autowired
    private StoreService storeService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String merchantId = request.getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.MERCHANT_ID));

        if (!merchantId.isEmpty()) {
            String keywords = request.getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(request.getParam(ParamsPool.KEY_WORDS));

            int page = request.getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(request.getParam(ParamsPool.PAGE));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return storeService.searchStoresByMerchant(connOnline, keywords, page, merchantId);
                    }).subscribe(terminals -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, terminals);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            throw IErrors.VALIDATION_ERROR;
        }

    }
}