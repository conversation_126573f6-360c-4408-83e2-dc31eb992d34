package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.PayOutPartnerService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class PartnerInfoHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private PayOutPartnerService partnerService;

    /**
     * Something has happened, so handle it.
     *
     * @param myEvent the event to handle
     */
    @Override
    public void handle(RoutingContext myEvent) {

        /*String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }*/
        JDBCClient clientOnline = myEvent.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        String partnerId = myEvent.request().getParam("partnerId") == null ? StringPool.BLANK : String.valueOf(myEvent.request().getParam("partnerId"));
        Observable<Object> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return partnerService.getByPartnerId(connOnline, partnerId);
                });
        obs.subscribe(listReceivedBank -> {
            myEvent.put(ParamsPool.HANDLER_DATA_RESULT, listReceivedBank);
            myEvent.next();
        }, throwable -> {
            myEvent.fail(throwable);
        });

    }
}
