package com.onepay.ma.service.handler.promotion.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.promotion.PromotionPostHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/2/16.
 */
public class PromotionPostHandlerImpl implements PromotionPostHandler {

    public PromotionPostHandlerImpl(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, ApprovalService approvalService) {
        this.promotionService = promotionService;
        this.promotionDiscountService = promotionDiscountService;
        this.merchantService = merchantService;
        this.promotionMerchantService = promotionMerchantService;
        this.promotionRuleService = promotionRuleService;
        this.approvalService = approvalService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => DINVALID DISCOUNT TYPE ");
            throw IErrors.VALIDATION_ERROR;
        }
//        SQLConnection connPr = rc.get(ParamsPool.CONNECTION_PROMOTION);
//        SQLConnection connBackUp = rc.get(ParamsPool.CONNECTION_BACKUP);
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        PromotionParam mapBody =  gson.fromJson(body, PromotionParam.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => INVALID DISCOUNT TYPE ");
            throw IErrors.VALIDATION_ERROR;
        }

        if(mapBody.getPromotion_name() == null){
            LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => INVALID PROMOTION NAME ");
            throw IErrors.VALIDATION_ERROR;
        }

        if(mapBody.getMerchants().size() <= 0){
            LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => INVALID MERCHANT DATA");
            throw IErrors.MERCHANT_DATA_ERROR;
        }

        mapBody.setUser_id(userId);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                .flatMap(connPr -> {
                connectionPR = connPr;
                //get online connection
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            connectionBackUp = connBackUp;
                            return connPr.setAutoCommitObservable(false).flatMap(aVoid -> {
                                // insert promotion data
                                return promotionService.insert(connPr, mapBody).flatMap(promotion -> {
                                    // insert merchant list
                                    return insertMerchantList(connPr, promotion.getN_id(), mapBody.getMerchants(), 0).flatMap(strings -> {
                                        // insert promotion discount
                                        validateDiscountTypes(mapBody.getDiscounts());
                                        return insertPromotionDiscountData(connPr, promotion.getN_id(), mapBody.getDiscounts(), 0).flatMap(discounts -> {
                                            // insert promotion rule data
                                            return insertPromotionRuleData(connPr, promotion.getN_id(), mapBody.getRules(), 0).flatMap(rules -> {
                                                // get promotion data
                                                if(mapBody.getCommand() != null && mapBody.getCommand().equals("send-to-approve")) {
                                                    // update to send to approve
                                                    return promotionService.updateStatus(connPr, promotion.getPromotion_id(), PromotionStatus.SEND_TO_APPROVE).flatMap(integer -> {
                                                        String newValue = "2";
                                                        Approval approval  = new Approval();
                                                        approval.setUser(userId);
                                                        approval.setSchema("ONEPR");
                                                        approval.setName("PROMOTION UPDATE PROMOTION STATUS");

                                                        approval.setNew_value(newValue);
                                                        approval.setOld_value(String.valueOf(promotion.getStatus()));
                                                        approval.setField("N_STATUS");
                                                        approval.setDescription("Change status of promotion from created to approved");
                                                        ApprovalCondition approvalCondition = new ApprovalCondition();
                                                        List<ApprovalCondition> approvalConditions = new ArrayList<>();
                                                        approvalCondition.setField("PR_ID");
                                                        approvalCondition.setValue(String.valueOf(promotion.getN_id()));
                                                        approvalCondition.setName(promotion.getPromotion_id());
                                                        approvalCondition.setType("string");
                                                        approvalConditions.add(approvalCondition);
                                                        String conditions = gson.toJson(approvalConditions);
                                                        approval.setConditions(conditions);
                                                        approval.setTable("TB_RULE");
                                                        approval.setType("PR_UPDATE_STATUS");
                                                        return connBackUp.setAutoCommitObservable(false).flatMap(aVoid1 -> {
                                                            return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                                                                // get promotion data final
                                                                return getPromotionData(connPr, promotion.getPromotion_id());
                                                            });
                                                        });
                                                    });
                                                }else {
                                                    // get promotion data final
                                                    return getPromotionData(connPr, promotion.getPromotion_id());
                                                }
                                            });
                                        });
                                    });
                                });
                            });
                        });
            }).subscribe(promotion -> {
                if(connectionPR != null) {
                    connectionPR.commitObservable();
                }
                if(connectionBackUp != null){
                    connectionBackUp.commitObservable();
                }
                rc.put(ParamsPool.HANDLER_DATA_RESULT, promotion);
                rc.put(ParamsPool.HANDLER_DATA_CODE, 201);
                rc.next();
        }, throwable -> {
            if(connectionPR != null) {
                connectionPR.rollbackObservable();
            }
            if(connectionBackUp != null){
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });


    }

    /**
     * insert list merchant data
     * @param connPr
     * @param promotionId
     * @param merchants
     * @param index
     * @return
     */
    private Observable<List<String>> insertMerchantList(SQLConnection connPr, int promotionId, List<String> merchants, int index){
        if(merchants.size() <= 0){
            return Observable.just(merchants);
        }
        final int finalIndex = index;
        String merchant = merchants.get(index);
        return Observable.just(merchant).flatMap(s -> {
           return  promotionMerchantService.insert(connPr, promotionId, s).flatMap(integer -> {
               if(finalIndex >= merchants.size() - 1){
                   return Observable.just(merchants);
               }else{
                   return insertMerchantList(connPr, promotionId, merchants, finalIndex + 1);
               }
           });
        });
    }

    /**
     * insert list promotion discount data
     * @param connPr
     * @param promotionId
     * @param discounts
     * @param index
     * @return
     */
    private Observable<List<PromotionDiscount>> insertPromotionDiscountData(SQLConnection connPr, int promotionId, List<PromotionDiscount> discounts, int index){
        if(discounts.size() <= 0){
            return Observable.just(discounts);
        }
        final int finalIndex = index;
        PromotionDiscount discount = discounts.get(index);
        return Observable.just(discount).flatMap(s -> {
            return  promotionDiscountService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= discounts.size() - 1){
                    return Observable.just(discounts);
                }else{
                    return insertPromotionDiscountData(connPr, promotionId, discounts, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert list promotion rule data
     * @param connPr
     * @param promotionId
     * @param rules
     * @param index
     * @return
     */
    private Observable<List<PromotionRule>> insertPromotionRuleData(SQLConnection connPr, int promotionId, List<PromotionRule> rules, int index){
        if(rules.size() <= 0){
            return Observable.just(rules);
        }
        final int finalIndex = index;
        PromotionRule rule = rules.get(index);
        return Observable.just(rule).flatMap(s -> {
            return  promotionRuleService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= rules.size() - 1){
                    return Observable.just(rules);
                }else{
                    return insertPromotionRuleData(connPr, promotionId, rules, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get promotion data
     * @param sqlConnPr
     * @param promotionId
     * @return
     */
    private Observable<Promotion> getPromotionData(SQLConnection sqlConnPr, String promotionId){
        return promotionService.get(sqlConnPr, promotionId).flatMap(promotionData -> {
            //get discount data
            return promotionDiscountService.listDiscount(sqlConnPr, StringPool.BLANK, promotionData.getPromotion_id()).flatMap(discountsData -> {
                promotionData.setDiscounts(discountsData);
                // get merchant data
                return promotionMerchantService.list(sqlConnPr, promotionData.getPromotion_id()).flatMap(merchantsData -> {
                    //promotion.setMerchants(merchants);
                    if(merchantsData.size() > 0) {
                        promotionData.setCurrency_code(merchantsData.get(0).getCurrency_code());
                    }
                    promotionData.setMerchants(merchantsData);
                    //get rule data
                    return promotionRuleService.list(sqlConnPr, promotionData.getPromotion_id()).map(rulesData -> {
                        promotionData.setRules(rulesData);
                        //return final promotion data
                        return promotionData;
                    });
                });
            });
        });
    }

    /**
     * validate discount
     * @param promotionDiscounts
     */
    private void validateDiscountTypes(List<PromotionDiscount> promotionDiscounts) {

        int lastTypeId = 0;

        if (promotionDiscounts == null || promotionDiscounts.size() <= 0) {
            LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => INVALID DISCOUNT TYPE ");
            throw IErrors.VALIDATION_ERROR;
        }
        for (PromotionDiscount promotionDiscount : promotionDiscounts) {

            int discountType = promotionDiscount.getDiscount_type().getDiscount_type_id();
            if (discountType <= 0) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => INVALID DISCOUNT TYPE ID");
                throw IErrors.VALIDATION_ERROR;
            }
            if(discountType == ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID){

                if(promotionDiscount.getDiscount_value() == null){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => DINVALID DISCOUNT VALUE " + discountType);
                    throw IErrors.VALIDATION_ERROR;
                }
            }else{
                double discountValue = 0;
                for (Map.Entry<String,Object> map : promotionDiscount.getDiscount_value().get(0).entrySet()){
                    discountValue = Double.valueOf(map.getValue().toString());
                }
                if(discountValue < 0){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => DISCOUNT VALUE MUST BE HIGHER THAN 0");
                    throw IErrors.VALIDATION_ERROR;
                }
            }
            if (lastTypeId == ParamsPool.DISCOUNT_TYPE_PERCENT_ID
                    && (discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID)) {

                LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => DISCOUNT TYPES CONTAINS BOTH PERCENT AND FIX TYPE");
                throw IErrors.VALIDATION_ERROR;
            } else if (lastTypeId == ParamsPool.DISCOUNT_TYPE_FIX_ID && (discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID
                    || discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_MAX_PER_TXN_ID)) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION POST VALIDATION ERROR ] => DISCOUNT TYPES  CONTAINS BOTH PERCENT AND FIX TYPE");
                throw IErrors.VALIDATION_ERROR;

            } else {

                lastTypeId = discountType;

            }

        }


    }


    private SQLConnection connectionPR = null;

    private SQLConnection connectionBackUp = null;

    private PromotionService promotionService;

    private PromotionDiscountService promotionDiscountService;

    private MerchantService merchantService;

    private ApprovalService approvalService;

    private PromotionMerchantService promotionMerchantService;

    private PromotionRuleService promotionRuleService;

    private final static Gson gson = new Gson();

    private final static Logger LOGGER = Logger.getLogger(PromotionPostHandler.class.getName());
}
