package com.onepay.ma.service.handler.reconcile.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportConvert;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by  tuydv on 19/09/18.
 */
@Component
public class ReconcileGetHandlerImpl implements Handler<RoutingContext> {


    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);

        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ?   1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        if (months > 2) {
                throw  IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        int bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 :  Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));

        String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

        String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        InternationalReportParameter parameter = new InternationalReportParameter();
        parameter.setAcquirerId(bankId);
        parameter.setFromDate(fromDate);
        parameter.setToDate(toDate);
        parameter.setInterval(interval);
        parameter.setCardType(cardType);
        Observable<Map> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            //return merchant
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                Map reportData = new HashMap();
                                                        String[] currencyList = {"VND", "USD", "THB", "SGD", "MYR", "IDR", "JPY", "KRW","TWD","CNY"};
                                                        if (!currency.isEmpty()) {
                                                            currencyList = new String[1];
                                                            currencyList[0] = currency;
                                                        }

                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        parameter.setMerchantId(String.join(",", merchantIdList));
                                                        return getListReportByCurrency(connReadOnly, Arrays.asList(currencyList), reportData, parameter, 0);

                                            });
                                        });
                            });
                });
        obs.subscribe(reports -> {
            Map returnReport = new HashMap();
            returnReport.put("reports", reports);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    /**
     * get list report by currency data list
     * @param sqlConnection
     * @param listCurrency
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getListReportByCurrency(SQLConnection sqlConnection, List<String> listCurrency, Map returnMap, InternationalReportParameter parameter, int index) {
        if (listCurrency.size() <= 0) {
            return Observable.just(returnMap);
        }
        String currency = listCurrency.get(index);
        final int finalIndex = index;
        return Observable.just(currency).flatMap(currencyData -> {
            parameter.setCurrency(currencyData);
                return reconcileService.listByCurrency(sqlConnection, parameter).flatMap(internationalReports -> {
                    InternationalReportConvert report = new InternationalReportConvert();
                    report.setItems(internationalReports);
                    int totalTransCount = 0;
                    double totalTransAmount = 0;
                    double totalRefundAmount = 0;
                    double totalVoidTransAmount = 0;
                    for (InternationalReport reportData : internationalReports) {
                        totalTransCount += reportData.getTransaction_count();
                        totalTransAmount += reportData.getTransaction_total();
                        totalRefundAmount += reportData.getRefund_total();
                        totalVoidTransAmount += reportData.getVoid_transaction_total();
                    }
                    report.setTotal_transaction_count(totalTransCount);
                    report.setTotal_transaction_total(totalTransAmount);
                    report.setTotal_refund_total(totalRefundAmount);
                    report.setTotal_void_transaction_total(totalVoidTransAmount);
                    report.setCurrency(currency);
                    if (internationalReports.size() > 0) {
                        report.setCurrency(internationalReports.get(0).getCurrency());
                    }
                    returnMap.put(currency, report);
                    if (finalIndex >= listCurrency.size() - 1) {
                        return Observable.just(returnMap);
                    } else {
                        return getListReportByCurrency(sqlConnection, listCurrency, returnMap, parameter, finalIndex + 1);
                    }
                });
        });
    }

    @Autowired
    private ReconcileService reconcileService;
    @Autowired
    private MerchantService merchantService;
}
