package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class OperatorChangeStatusHandlerImpl implements Handler<RoutingContext> {

    @Autowired
    private OperatorService operatorService;

    @Override
    public void handle(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
                String xUserId = rc.get(ParamsPool.X_USER_ID);
                if (xUserId == null) {
                    throw IErrors.FORBIDDEN;
                }

                JsonObject bodyAsJson = rc.getBodyAsJson();

                if (null == bodyAsJson || !bodyAsJson.containsKey("operatorId") || !bodyAsJson.containsKey("state")) {
                    throw IErrors.VALIDATION_ERROR;
                }
                if ("deleted".equalsIgnoreCase(bodyAsJson.getString("state"))) {
                    String state = bodyAsJson.getString("state") + "_" + System.currentTimeMillis();
                    bodyAsJson.put("state", state);
                }
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                        .flatMap(connOnline -> {
                            return operatorService.updateState(connOnline, bodyAsJson);
                        }).subscribe(operator -> {
                    operator.setPassword(null);
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, operator);
                    rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
                    rc.next();
                }, throwable -> {
                    rc.fail(throwable);
                });

            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Change status operator Error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    private static Logger LOGGER = Logger.getLogger(OperatorChangeStatusHandlerImpl.class.getName());

}
