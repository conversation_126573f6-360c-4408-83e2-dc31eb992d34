package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.notification.NotificationSearchQuery;
import com.onepay.ma.service.service.notification.AppNotificationService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.logging.Logger;

/**
 * Created by anhkh on 10-Oct-17.
 */
@Component
public class MpayNotificationGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


            String userId = rc.request().getParam(ParamsPool.USER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.USER_ID));
            String language = rc.request().getParam(ParamsPool.LANGUAGE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.LANGUAGE));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            NotificationSearchQuery searchQuery = new NotificationSearchQuery();
            searchQuery.setUserId(userId);
            searchQuery.setPage(page);
            searchQuery.setPageSize(pageSize);
            searchQuery.setLanguage(language);


            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return this.appNotificationService.search(connBackup, searchQuery);
                    }).subscribe(mpayNotificationBaseList -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayNotificationBaseList);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });

    }

    @Autowired
    private AppNotificationService appNotificationService;


    private static Logger LOGGER = Logger.getLogger(MpayNotificationGetHandler.class.getName());
}
