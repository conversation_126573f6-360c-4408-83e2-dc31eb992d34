package com.onepay.ma.service.handler.pay_collect.impl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_collect.ReportDto;
import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.pay_collect.ReportService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
/**
 * Created by danhnt on 04/12/2020.
 */

@Component
public class ReportSearchHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(ReportSearchHandlerImpl.class.getName());

    @Autowired
    private ReportService reportService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientPayCollect = rc.get(ParamsPool.PAYCOLLECT_DATASOURCE_NAME);
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(fromDate);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ PAY COLLECT REPORT GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
        int checkFromDateDiffToDate = FunctionUtil.compareDateByMonth(oFromDate, oToDate, 6);
        int checkFromdateTooOld = FunctionUtil.compareDateByMonth(oFromDate, new Date(), 24);

        // Validate fromdate, todate too long
        if (checkFromDateDiffToDate <= 0) {
            LOGGER.log(Level.SEVERE, "[ PAY COLLECT REPORT GET ]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }

        // Validate fromdate after 2 years ago
        if (checkFromdateTooOld <= 0) {
            LOGGER.log(Level.SEVERE,"[ PAY COLLECT REPORT SEARCH GET OVER 2 YEARS AGO]  => INVALID DATE ");
            throw IErrors.SEARCH_TOO_OLD_ERROR;
        }
        
        String virtualAccId = rc.request().getParam("virtualAccId") == null ? StringPool.BLANK : rc.request().getParam("virtualAccId");
//        String bankTxnRef = rc.request().getParam("bankTxnRef") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("bankTxnRef"));
        String receivedBankList = rc.request().getParam("receivedBanks") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("receivedBanks"));
//        String senderBankList = rc.request().getParam("senderBanks") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("senderBanks"));
        String interval = rc.request().getParam("interval");
        String merchantId = rc.request().getParam("merchantId");
        int page = 0;
        int pageSize = Integer.MAX_VALUE;
        String groupBy = rc.request().getParam("groupBy");

        ReportSearchReq searchRequest = new ReportSearchReq(fromDate, toDate, virtualAccId, interval, page, pageSize, receivedBankList, merchantId, groupBy);
        String maMain = rc.request().getParam(ParamsPool.MA_MAIN) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MA_MAIN));
        LOGGER.log(Level.INFO, "userId: {}", userId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        if (StringUtils.isNotBlank(maMain)) {
            LOGGER.log(Level.INFO, "maMain v1: ");
            String finalMerchantsList = MaPermission.getMc(userId, xRequestId, partnerId, merchantId);
            searchRequest.setMerchantId(finalMerchantsList);
            Observable<BaseList<ReportDto>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
            .flatMap(connPaycollect -> {
                return reportService.search(connPaycollect, searchRequest);
            });
            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {
            Observable<BaseList<ReportDto>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientPayCollect), f -> f.dispose())
                .flatMap(connPaycollect -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                //return reportService.search(connPaycollect, searchRequest);
                                return merchantService.merchantPaycollects(connPaycollect, connBackUp, merchantId, userId, "paycollect").flatMap(merchants -> {
                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                    if (merchantIdList != null && !merchantIdList.isEmpty()) { // user k dc phan quyen all mid
                                        searchRequest.setMerchantId(String.join(",", merchantIdList));
                                    }
                                    if (merchantIdList.isEmpty()) { // user k dc phan quyen mid nao
                                        searchRequest.setMerchantId("partner_id");
                                    }
                                    return reportService.search(connPaycollect, searchRequest);
                                });
                            });
                });
            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }

    @Autowired
    private MerchantService merchantService;

}
