package com.onepay.ma.service.handler.permission.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.PutPermissionParam;
import com.onepay.ma.service.handler.permission.PermissionPutHandler;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huynguyen on 3/12/16.
 */
public class PermissionPutHandlerImpl implements PermissionPutHandler {

    public PermissionPutHandlerImpl(PermissionService permissionService, CacheGuava cacheGuava) {
        this.permissionService = permissionService;
        this.cacheGuava = cacheGuava;
    }

    @Override
    public void handle(RoutingContext rc) {
        String permissionId = rc.request().getParam("permissionId");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(permissionId == null) {
            throw IErrors.VALIDATION_ERROR;
        }else{
            String json = rc.getBodyAsString();
            PutPermissionParam permissionParam = gson.fromJson(json, PutPermissionParam.class);
            permissionParam.setPermission_id(Integer.valueOf(permissionId));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        connectionBackUp = connBackUp;
                        return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                            return permissionService.update(connectionBackUp, permissionParam);
                        });
                    }).subscribe(permission -> {
                if(connectionBackUp != null){
                    connectionBackUp.commitObservable();
                }
                cacheGuava.get().invalidateAll();
                rc.put(ParamsPool.HANDLER_DATA_RESULT, permission);
                rc.next();

            }, throwable -> {
                if(connectionBackUp != null){
                    connectionBackUp.rollbackObservable();
                }
                rc.fail(throwable);
            });

        }
    }

    private CacheGuava cacheGuava;

    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();
    private PermissionService permissionService;
}
