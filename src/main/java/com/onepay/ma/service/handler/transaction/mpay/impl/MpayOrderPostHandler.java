package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.mpay.MpayOrder;
import com.onepay.ma.service.models.mpay.MpayOrderPostModel;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.QrUtils;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.sql.Timestamp;
import java.util.Base64;

/**
 * Created by anhkh on 09-Aug-17.
 */
@Component
public class MpayOrderPostHandler implements Handler<RoutingContext> {

    private final static Gson gson = new Gson();
    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {

//        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
//        if (xRequestId == null) {
//            throw IErrors.VALIDATION_ERROR;
//        }

        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);


//        final HttpServerRequest request = rc.request();
        String body = rc.getBodyAsString();
        MpayOrderPostModel mapBody = gson.fromJson(body, MpayOrderPostModel.class);
        if (mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
//        String qrReference = xRequestId.substring(0,10);
        String qrReference = String.valueOf(System.currentTimeMillis() + hashCode() + Math.random());

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(readonlyConn -> {
                    return this.merchantService.getMpayMerchant(readonlyConn, mapBody.getMerchantId()).map(merchant -> {
                        mapBody.setCountryCode(merchant.getCountry());
                        mapBody.setMerchantCity(merchant.getCity());
                        mapBody.setMerchantCategoryCode(merchant.getCategoryCode());
                        mapBody.setMerchantName(merchant.getName());
                        mapBody.setReferenceId(qrReference);

                        String qrData = QrUtils.createMpayQrData(mapBody);

                        JsonNode jsonOut = null;
                        try {
                            jsonOut = MSPClient.post(Base64.getEncoder().encodeToString(qrData.getBytes("utf-8")));
                        } catch (Exception e) {

                            throw IErrors.INTERNAL_SERVER_ERROR;
                        }

                        if (jsonOut == null) {
                            throw IErrors.INTERNAL_SERVER_ERROR;
                        }
                        String orderId = jsonOut.has("id") ? jsonOut.get("id").asText() : null;
                        String state = jsonOut.has("state") ? jsonOut.get("state").asText() : null;

                        MpayOrder mpayOrder = new MpayOrder();
                        mpayOrder.setOrder_id(orderId);
                        Amount amount = new Amount();
                        amount.setCurrency(mapBody.getCurrencyCode());
                        amount.setTotal(mapBody.getAmount());
                        mpayOrder.setAmount(amount);
                        mpayOrder.setOrder_info(mapBody.getBillNumber());
                        mpayOrder.setMerchant_id(merchant.getId());
                        mpayOrder.setMerchant_name(merchant.getName());
                        mpayOrder.setQrData(qrData);
                        mpayOrder.setMerchant_order_ref(qrReference);
                        mpayOrder.setCreate_time(new Timestamp(System.currentTimeMillis()));
                        mpayOrder.setStatus(state);
                        return mpayOrder;

                    });
                }).subscribe(mpayOrder -> {


            if (mpayOrder != null) {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayOrder);
                rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.CREATED.code());
                rc.next();
            } else {

                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, throwable -> {
            rc.fail(throwable);
        });
    }
}
