package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FileGetHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/15/16.
 */
public interface FileGetHandler extends Handler<RoutingContext>  {
    static FileGetHandlerImpl create(ServerConfig serverConfig, FileService fileService){
        return new FileGetHandlerImpl(serverConfig, fileService);

    }
}
