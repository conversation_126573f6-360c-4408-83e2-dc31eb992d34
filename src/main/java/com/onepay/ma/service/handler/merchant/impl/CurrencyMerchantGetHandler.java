package com.onepay.ma.service.handler.merchant.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 21-Aug-17.
 */
@Component
public class CurrencyMerchantGetHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        String merchantId = rc.request().getParam("id");
        if(merchantId != null){

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return merchantService.getCurrenciesByMerchantId(connOnline,  merchantId);
                    }).subscribe(merchants -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, merchants);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }else{
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Autowired
    private MerchantService merchantService;
}
