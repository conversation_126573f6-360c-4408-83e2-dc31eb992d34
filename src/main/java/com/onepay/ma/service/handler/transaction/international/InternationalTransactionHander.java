package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.BrandConfigGetHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.InternationalTransactionPatchHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.SSFinancialTransactionDownloadHandler;
import com.onepay.ma.service.handler.transaction.international.impl.SamsungInternationalAuthorizeTransactionGetHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.SamsungInternationalCaptureTransactionGetHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.SamsungInternationalRefundCaptureTransactionGetHandlerImpl;
import com.onepay.ma.service.handler.transaction.international.impl.SamsungInternationalTransactionGetHandlerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;

@Component
public class InternationalTransactionHander {

    @Autowired
    private SamsungInternationalTransactionGetHandlerImpl samsungInternationalTransactionGetHandler;

    @Autowired
    private SamsungInternationalAuthorizeTransactionGetHandlerImpl samsungInternationalAuthorizeTransactionGetHandler;

    @Autowired
    private SamsungInternationalCaptureTransactionGetHandlerImpl samsungInternationalCaptureTransactionGetHandler;

    @Autowired
    private SamsungInternationalRefundCaptureTransactionGetHandlerImpl samsungInternationalRefundCaptureTransactionGetHandler;

    @Autowired
    private SSFinancialTransactionDownloadHandler ssFinancialTransactionDownloadHandler;
    @Autowired
    private InternationalTransactionPatchHandlerImpl internationalTransactionPatchHandlerImpl;

    @Autowired
    private BrandConfigGetHandlerImpl brandConfigGetHandlerImpl;

    public Handler getSSTrans() {
        return  samsungInternationalTransactionGetHandler;
    }

    public Handler getSSAuthorizeTrans() { return  samsungInternationalAuthorizeTransactionGetHandler; }

    public Handler getSSCaptureTrans() { return  samsungInternationalCaptureTransactionGetHandler; }

    public Handler getSSRefundCaptureTrans() { return  samsungInternationalRefundCaptureTransactionGetHandler; }

    public Handler downloadSSFinancialTrans() {
        return  ssFinancialTransactionDownloadHandler;
    }

    public Handler getBrandConfig() {
        return  brandConfigGetHandlerImpl;
    }

    public Handler patch() {
        return  internationalTransactionPatchHandlerImpl;
    }

}
