package com.onepay.ma.service.handler.user;

import com.onepay.ma.service.handler.user.impl.UserProfileGetHandlerImpl;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by anhkh on 10-Apr-18.
 */
public interface UserProfileGetHandler extends Handler<RoutingContext> {


    static UserProfileGetHandlerImpl create(UserService userService){
        return new UserProfileGetHandlerImpl(userService);
    }
}
