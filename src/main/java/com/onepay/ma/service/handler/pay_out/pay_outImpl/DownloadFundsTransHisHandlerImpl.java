package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.jms.Queue;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.models.pay_out.FundsTransHisQueryDto;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

@Component
public class DownloadFundsTransHisHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(DownloadFundsTransHisHandlerImpl.class.getName());

    private final static Gson gson = new Gson();
    @Autowired
    ServerConfig serverConfig;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier("downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier("downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier("downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier("downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;
    private SQLConnection connectBackUp;

    @Override
    public void handle(RoutingContext rc) {
        String xUserId = rc.get(ParamsPool.X_USER_ID);
        String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        String partnerId = rc.get(ParamsPool.X_PARTNER_ID);
        if (xUserId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        final HttpServerRequest request = rc.request();
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JsonObject body = rc.getBodyAsJson();
        String fromDate = body.getString(ParamsPool.FROM_DATE);
        String toDate = body.getString(ParamsPool.TO_DATE);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date myFromDate;
        Date myToDate;
        try {
            myFromDate = dateFormat.parse(fromDate);
            myToDate = dateFormat.parse(toDate);
        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months = FunctionUtil.monthsBetween(myFromDate, myToDate);
        if (months > 12) {
            throw IErrors.SEARCH_TOO_LARGE_ERROR;
        }
        String merchantId = body.getString("merchantId");
        String merchantAccount = body.getString("merchantAccount");
        String fundsTransferId = body.getString("merchantTransId");
        String transactionId = body.getString("transactionId");
        String state = body.getString("state");
        String dateOf = body.getString("dateOf");
        String beneficiaryBanks = body.getString("beneficiaryBanks");
        String beneficiaryAccount = body.getString("beneficiaryAccount");
        String reportType = body.getString(ParamsPool.DOWNLOADTYPE) == null ? StringPool.BLANK : String.valueOf(body.getString(ParamsPool.DOWNLOADTYPE));
        String maMain = body.getString("maMain");
        LOGGER.log(Level.INFO, "xUserId: {}", xUserId);
        LOGGER.log(Level.INFO, "userId: {}", userId);
        LOGGER.log(Level.INFO, "partnerId: {}", partnerId);
        LOGGER.log(Level.INFO, "maMain: {}", maMain);
        FundsTransHisQueryDto query = new FundsTransHisQueryDto();
        query.setUserId(xUserId);
        query.setxRequestId(xRequestId);
        query.setxUserId(xUserId);
        query.setFromDate(fromDate);
        query.setToDate(toDate);
        query.setMerchantId(merchantId);
        query.setMerchantAccount(merchantAccount);
        query.setFunds_transfer_id(fundsTransferId);
        query.setTransaction_id(transactionId);
        query.setPage(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setState(state);
        query.setDownloadType(reportType);
        query.setDateOf(dateOf);
        query.setBeneficiaryBanks(beneficiaryBanks);
        query.setBeneficiaryAccount(beneficiaryAccount);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, xRequestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
            Map mdata = new HashMap();
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    this.connectBackUp = connBackup;
                                    return merchantService.list(connOnline, connBackup, query.getMerchantId(), userId, "payout").flatMap(merchants -> {
                                        java.util.List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                        query.setMerchantId(String.join(",", merchantIdList));
                                        return getObservable(rc, userId, query, requestData, mdata);
                                    });
                                });
                    }).subscribe(map -> {
                if (connectBackUp != null) {
                    connectBackUp.commitObservable();
                }
                int row = Integer.valueOf(map.get("row").toString());
                if (row <= serverConfig.getRowLevel()) {
                    //fileDownload.setExt("csv");
                    Message<FundsTransHisQueryDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                    QueueProducer.sendMessage(message);
                } else {
                    //  fileDownload.setExt("zip");
                    Message<FundsTransHisQueryDto> message = new Message<>(query, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                    QueueProducer.sendMessage(message);
                }

                rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                rc.next();
            }, throwable -> {
                if (connectBackUp != null) {
                    connectBackUp.rollbackObservable();
                }
                rc.fail(throwable);
            });
    }

    private Observable<Map> getObservable(RoutingContext rc, String userId, FundsTransHisQueryDto query, Map requestData, Map data) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(new Date());
        StringBuilder fileName = new StringBuilder("funds_transfer").append(StringPool.UNDERLINE).append("history").append(StringPool.UNDERLINE).append(date);
        String fileHashName = "";
        data.put("parameter", query);
        data.put("file_name", fileName);
        data.put("row", 1000);
        try {
            fileHashName = Convert.hash(fileName.toString() + StringPool.UNDERLINE + userId + date);
        } catch (NoSuchAlgorithmException e) {
            rc.fail(e);
        } catch (UnsupportedEncodingException e) {
            rc.fail(e);
        }
        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
        requestData.put(ParamsPool.FILE_NAME, fileName);
        FileDownload fileDownload = new FileDownload();
        fileDownload.setUser(userId);
        fileDownload.setFile_type("funds_transfer_history");
        if (ParamsPool.CSV.equals(query.getDownloadType())) {
            fileDownload.setExt("csv");
        } else {
            fileDownload.setExt("xls");
        }

        fileDownload.setFile_name(fileName.toString());
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setConditions(gson.toJson(query));
        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
            return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                return data;
            });
        });
    }
}
