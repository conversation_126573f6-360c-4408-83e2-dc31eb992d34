package com.onepay.ma.service.handler.token.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.service.apptoken.AppTokenService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 22-Sep-17.
 */
@Component
public class AppTokenPostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String body = rc.getBodyAsString();
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        Map<String, String> bodyMap = gson.fromJson(body, type);
        if (bodyMap == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String token = bodyMap.get("token");

        String os = bodyMap.get("os");
        String os_version = bodyMap.get("os_version");
        String language = bodyMap.get("language");
        if (token == null || language == null) {
            LOGGER.log(Level.SEVERE, "[ APP TOKEN POST VALIDATION ERROR ] => INVALID token Or Language ");
            throw IErrors.VALIDATION_ERROR;
        }
        AppToken postDto = new AppToken();
        postDto.setToken(token);
        postDto.setLanguage(language);
        postDto.setUserId(userId);
        postDto.setOs(os);
        postDto.setOs_version(os_version);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    this.connBackup = connBackUp;
                    return this.connBackup.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return this.appTokenService.insert(connBackUp, postDto);
                    });
                }).subscribe(aVoid -> {
            if (connBackup != null) {
                connBackup.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_CODE, 201);
            rc.next();
        }, throwable -> {
            if (connBackup != null) {
                connBackup.rollbackObservable();
            }
            rc.fail(throwable);
        });


    }

    @Autowired
    private AppTokenService appTokenService;


    private final static Gson gson = new Gson();

    private SQLConnection connBackup;

    private final static Logger LOGGER = Logger.getLogger(AppTokenPostHandler.class.getName());
}
