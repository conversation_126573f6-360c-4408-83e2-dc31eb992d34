package com.onepay.ma.service.handler.vinpearl.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.handler.externalClient.VinpearlMaClient;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class VinpearlTransactionRefundHandlerImpl implements Handler<RoutingContext> {
    
    @Autowired
    MerchantApprovalService merchantApproval;

    @Override
    public void handle(RoutingContext rc) {
        try {
            String userId = rc.get(ParamsPool.X_USER_ID);
            if (userId == null) {
                LOGGER.severe(() -> "[ VINPEARL TRANSACTION REFUND] -> USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String xRequestId = rc.get(ParamsPool.X_REQUEST_ID);
            String transactionId = rc.request().getParam("id");

            JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

            String body = rc.getBodyAsString();
            PatchRequest<RefundData> mapPatchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RefundData>>() {}.getType());

            if (mapPatchRequest == null|| mapPatchRequest.getService() == null) {
                LOGGER.severe(() -> "[ VINPEARL TRANSACTION REFUND] -> BODY EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }

            Integer serviceType = getServiceType(mapPatchRequest.getService());
            String merchantId = mapPatchRequest.getValue() != null ? Utils.nvl(mapPatchRequest.getValue().getMerchant_id(), rc.request().getParam("merchant_id")) : rc.request().getParam("merchant_id");
            Observable<Boolean> obsIsMerchantApproval = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), SQLConnectionFactory::dispose)
                    .flatMap(connBackUp -> this.merchantApproval.getMerchantApproval(connBackUp, merchantId )
                            .map(merchantPermit -> {
                                // boolean isQR = serviceType == RefundData.Type.QR.getValue();
                                // if (isQR && (merchantPermit != null) && merchantPermit.getDoubleConfirmation() != 1) { // Giao dich QR bat buoc = 1 (ca merchant va OP duyet)
                                //     LOGGER.info("[ VINPEARL TRANSACTION REFUND] -> Merchant cau hinh sai, QR bat buoc cau hinh Merchant + OP duyet");
                                //     return false;
                                // }
                                return (merchantPermit != null && !merchantPermit.isEmpty());
                            }));

            obsIsMerchantApproval.subscribe(isMerchantApproval -> {
                if (Boolean.TRUE.equals(isMerchantApproval)) {
                    LOGGER.info(() -> "--- MERCHANT APPROVAL ---");
                    VinpearlMaClient.vpRefund(rc, serviceType, transactionId, rc.getBodyAsJson(), userId, xRequestId);
                } else {
                    Map<String, Object> res = new HashMap();
                    LOGGER.info(() -> "--- IGNORE APPROVAL ---");
                    res.put("status_code", 400);
                    res.put("name", "IGNORE");
                    res.put("message", "Not config merchant refund approval.");
                    rc.put(ParamsPool.HANDLER_DATA_RESULT, res);
                    rc.next();
                }
            }, rc::fail);
        } catch (Exception e) {
            LOGGER.log(Level.WARNING,"",e);
            rc.fail(e);
        }
    }

    private Integer getServiceType(String service) {
        if (RefundData.Type.DOMESTIC.name().equalsIgnoreCase(service)) {
            return RefundData.Type.DOMESTIC.getValue();
        } else if (RefundData.Type.INTERNATIONAL.name().equalsIgnoreCase(service)) {
            return RefundData.Type.INTERNATIONAL.getValue();
        } else if (RefundData.Type.QR.name().equalsIgnoreCase(service)) {
            return RefundData.Type.QR.getValue();
        } else if (RefundData.Type.BNPL.name().equalsIgnoreCase(service)) {
            return RefundData.Type.BNPL.getValue();
        } else if (RefundData.Type.VIETQR.name().equalsIgnoreCase(service)) {
            return RefundData.Type.VIETQR.getValue();
        } else if (RefundData.Type.DIRECT_DEBIT.name().equalsIgnoreCase(service)) {
            return RefundData.Type.DIRECT_DEBIT.getValue();
        } else {
            LOGGER.severe(() -> "[ VINPEARL TRANSACTION REFUND] -> INVALID SERVICE TYPE");
            throw IErrors.VALIDATION_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(VinpearlTransactionRefundHandlerImpl.class.getName());

    private static final Gson gson = new Gson();
}
