package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.service.pay_out.BatchDetailService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

@Component
public class BatchDetailGetHandlerImpl implements Handler<RoutingContext> {

    private static Logger LOGGER = Logger.getLogger(BatchDetailGetHandlerImpl.class.getName());

    @Autowired
    private BatchDetailService batchDetailService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);

        String batchId = rc.request().getParam("batchId") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("batchId"));
        String swiftCode = rc.request().getParam("bankSender") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("bankSender"));
        String holderName = rc.request().getParam("holderName") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("holderName"));
        String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
        int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
        int pageSize = rc.request().getParam(ParamsPool.PAGESIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGESIZE));

        Map<String, String> mIn = new HashMap();
        mIn.put("batch_id", batchId);
        mIn.put("swift_code", swiftCode);
        mIn.put("holder_name", holderName);
        mIn.put(ParamsPool.STATE, state);
        mIn.put(ParamsPool.PAGESIZE, String.valueOf(pageSize));
        mIn.put(ParamsPool.PAGE, String.valueOf(page));

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return batchDetailService.search(connOnline, mIn);
                }).subscribe(partners -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, partners);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

}
