package com.onepay.ma.service.handler.transaction.mpay.impl;

import com.onepay.ma.service.service.mpay.impl.MpayTransactionService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpay.MpayOrder;
import com.onepay.ma.service.models.mpay.MpayOrderQuery;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.service.mpay.MpayOrderService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 08-Aug-17.
 */
@Component
public class MpayOrderGetHandler implements Handler<RoutingContext> {

    @Autowired
    private MpayOrderService mpayOrderService;



    @Autowired
    private UserTerminalService userTerminalService;

    @Autowired
    private UserService userService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private MerchantService merchantService;

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            LOGGER.log(Level.SEVERE, "[ MPAY TRANSACTION GET ] => BODY PATCH IS EMPTY ");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        if (transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);
            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ MPAY ORDER GET ]  => INVALID DATE ");
                throw IErrors.VALIDATION_ERROR;
            }
            int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if (months > 6) {
                LOGGER.log(Level.SEVERE, "[ MPAY ORDER GET ]  => INVALID DATE ");
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));
            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
            String orderId = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));
            String merchantTnxRef = rc.request().getParam(ParamsPool.MERCHANT_ORDER_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ORDER_REF));
            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));
            String transactionIdValue = rc.request().getParam(ParamsPool.ORDER_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_ID));
            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            MpayOrderQuery query = new MpayOrderQuery();
            query.setPage(page);
            query.setKeywords(keywords);
            query.setMerchant_id(merchantId);
            query.setMerchant_order_ref(merchantTnxRef);
            query.setOrder_info(orderId);
            query.setOrder_id(transactionIdValue);
            query.setPageSize(pageSize);
            query.setCurrency(currency);
            query.setFrom_date(fromDate);
            query.setTo_date(toDate);
            query.setStatus(status);
            query.setCard_number(cardNumber);

            Observable<Transactions<MpayOrder>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                    .flatMap(connReadOnly -> {
                        //get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                            .flatMap(connBackup -> {
                                                // find user by S-id
                                                return userService.getOneAm(connBackup, userId).flatMap(user -> {
                                                    return merchantService.list(connOnline, connBackup, merchantId, userId, "mpay").flatMap(merchants -> {
                                                        List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                        query.setMerchant_id(String.join(",", merchantIdList));
                                                        query.setTerminal_id(StringPool.BLANK);
                                                        return this.mpayOrderService.list(connReadOnly, connOnline, query);
                                                    });

                                                });
                                            });
                                });
                    });

            obs.subscribe(transactions -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });


        } else {
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                    .flatMap(connOnline -> {

                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackup -> {
                                    return mpayOrderService.get(connOnline, transactionId).flatMap(mpayOrder -> {
                                        return merchantService.list(connOnline, connBackup, mpayOrder.getMerchant_id(), userId, "mpay").flatMap(merchants -> {

                                            if (merchants.isEmpty()) {
                                                throw IErrors.FORBIDDEN;
                                            }

                                            return MpayTransactionService.getByOrderId(connOnline, mpayOrder.getOrder_id()).map(mpayTransactions -> {
                                                mpayOrder.setMpayTransactions(mpayTransactions);
                                                return mpayOrder;
                                            });
                                        });
                                    });
                                });
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
            ;
        }
    }

    private String convertListToId(List<String> list) {
        java.lang.String result = StringPool.BLANK;

        for (int i = 0; i < list.size(); i++) {
            if (i != list.size() - 1) {
                result += list.get(i) + StringPool.COMMA;
            } else {
                result += list.get(i);
            }
        }

        return result;
    }


    private static Logger LOGGER = Logger.getLogger(MpayOrderGetHandler.class.getName());
}
