package com.onepay.ma.service.handler.merchant;

import com.onepay.ma.service.handler.merchant.impl.CurrencyMerchantGetHandler;
import com.onepay.ma.service.handler.merchant.impl.CustomerMerchantGetHandler;
import com.onepay.ma.service.handler.merchant.impl.UserMerchantGetHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 21-Aug-17.
 */
@Component
public class MerchantHander {
    @Autowired
    private CurrencyMerchantGetHandler currencyMerchantGetHandler;
    @Autowired
    private CustomerMerchantGetHandler customerMerchantGetHandler;
    @Autowired
    private UserMerchantGetHandler userMerchantGetHandler;

    public Handler getCurrencieMerchant() {
        return currencyMerchantGetHandler;
    }

    public Handler getCustomerMerchant() {
        return customerMerchantGetHandler;
    }

    public Handler findUserByMerchant() {
        return userMerchantGetHandler;
    }
}
