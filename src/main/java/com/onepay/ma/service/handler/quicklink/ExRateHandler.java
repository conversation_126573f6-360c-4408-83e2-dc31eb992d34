package com.onepay.ma.service.handler.quicklink;

import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import com.onepay.ma.service.handler.externalClient.QuickLinkClient;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.StringPool;

public class ExRateHandler {
    private  Logger logger = Logger.getLogger(ExchangeRateHandler.class.getName());

    public static void getExchangeRate(RoutingContext rc) {
        String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
        try {
            JsonObject result = QuickLinkClient.getExchangeRate(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void getCurrency(RoutingContext rc) {
        try {
            String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
            JsonObject result = QuickLinkClient.getCurrencyByTempId(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void getCurrencyWithoutVCB(RoutingContext rc) {
        try {
            String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
            JsonObject result = QuickLinkClient.getCurrencyWithOutVcb(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

    public static void getExchangeRateVCB(RoutingContext rc) {
        try {
            String linkId = rc.request().getParam("id") == null ? StringPool.BLANK : String.valueOf(rc.request().getParam("id"));
            JsonObject result = QuickLinkClient.getExchangeVCB(linkId);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, result.getMap());
            rc.next();
        } catch (Exception e) {
            rc.fail(e);
        }
    }

}
