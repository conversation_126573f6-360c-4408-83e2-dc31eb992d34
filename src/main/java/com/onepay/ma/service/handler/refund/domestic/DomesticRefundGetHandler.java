package com.onepay.ma.service.handler.refund.domestic;

import com.onepay.ma.service.handler.refund.domestic.impl.DomesticRefundGetHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.MerchantService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface DomesticRefundGetHandler extends Handler<RoutingContext> {
    static DomesticRefundGetHandlerImpl create(DomesticRefundService domesticRefundService, MerchantService merchantService, ServerConfig serverConfig){
        return new DomesticRefundGetHandlerImpl(domesticRefundService, merchantService, serverConfig);

    }
}
