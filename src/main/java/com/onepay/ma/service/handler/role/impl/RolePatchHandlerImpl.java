package com.onepay.ma.service.handler.role.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.handler.permission.PermissionPatchHandler;
import com.onepay.ma.service.handler.role.RolePatchHandler;
import com.onepay.ma.service.models.PermissionPatch;
import com.onepay.ma.service.models.PositionPatchRole;
import com.onepay.ma.service.models.RolePatchPosParameter;
import com.onepay.ma.service.service.PermissionRoleService;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.List;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/12/16.
 */
public class RolePatchHandlerImpl implements PermissionPatchHandler {

    public RolePatchHandlerImpl(RoleService roleService, PermissionRoleService permissionRoleService, CacheGuava cacheGuava) {
        this.roleService = roleService;
        this.permissionRoleService = permissionRoleService;
        this.cacheGuava = cacheGuava;
    }

    @Override
    public void handle(RoutingContext rc) {
        String roleId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String body = rc.getBodyAsString();
        PatchRequest requestData = gson.fromJson(body, PatchRequest.class);
        if(roleId != null && !roleId.isEmpty()){

            if(requestData.getPath().equals("/permission")) {
                PatchRequest<PermissionPatch> patchRequest = gson.fromJson(body, new TypeToken<PatchRequest<PermissionPatch>>(){}.getType());
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            connectionBackUp = connBackUp;
                            return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return roleService.get(connectionBackUp, roleId).flatMap(roleData -> {
                                    if (roleData == null) {
                                        throw IErrors.RESOURCE_NOT_FOUND;
                                    }
                                    return permissionRoleService.deleteByRoleId(connectionBackUp, roleData.getRole_id()).flatMap(integer -> insertPermissionList(connectionBackUp, patchRequest.getValue().getPermissions(), roleData.getRole_id(), 0));
                                });
                            });
                        }).subscribe(strings -> {
                            if(connectionBackUp != null){
                                connectionBackUp.commitObservable();
                            }
                            cacheGuava.get().invalidateAll();
                            FunctionUtil.sendNextNullContext(rc, 202);

                }, throwable -> {
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });

            }else{
                throw IErrors.VALIDATION_ERROR;
            }
        }else{
            if(requestData.getPath().equals("/position")) {
                PatchRequest<RolePatchPosParameter> patchRequest = gson.fromJson(body, new TypeToken<PatchRequest<RolePatchPosParameter>>(){}.getType());
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            connectionBackUp = connBackUp;
                            return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                return updateRoleParentOrderList(connectionBackUp, patchRequest.getValue().getRoles(), 0);
                                });

                            }).subscribe(positionPatchRoles -> {
                    if(connectionBackUp != null){
                        connectionBackUp.commitObservable();
                    }
                    FunctionUtil.sendNextNullContext(rc, 202);
                    cacheGuava.get().invalidateAll();
                }, throwable -> {
                    if(connectionBackUp != null){
                        connectionBackUp.rollbackObservable();
                    }
                    rc.fail(throwable);
                });


            }else{
                throw IErrors.VALIDATION_ERROR;
            }
        }

    }


    /**
     * insert permission role list
     * @param sqlConnection
     * @param listPermission
     * @param roleId
     * @return
     */
    private Observable<List<String>> insertPermissionList(SQLConnection sqlConnection, List<String> listPermission, int roleId, int index) {
        if(listPermission.size() <= 0){
            return Observable.just(listPermission);
        }
        int permissionId = Integer.valueOf(listPermission.get(index));
        final int finalIndex = index;
        return Observable.just(permissionId).flatMap(approvalData -> {
            //insert approval for user group
            return permissionRoleService.insert(sqlConnection, permissionId, roleId).flatMap(integer -> {
                if(finalIndex >= listPermission.size() - 1){
                    return Observable.just(listPermission);
                }else{
                    return insertPermissionList(sqlConnection, listPermission, roleId, finalIndex + 1);
                }
            });
        });

    }

    /**
     * update  role parent list
     * @param sqlConnection
     * @param listData
     * @return
     */
    private Observable<List<PositionPatchRole>> updateRoleParentOrderList(SQLConnection sqlConnection, List<PositionPatchRole> listData, int index) {
        if(listData.size() <= 0){
            return Observable.just(listData);
        }
        PositionPatchRole positionPatchRole =listData.get(index);
        final int finalIndex = index;
        return Observable.just(positionPatchRole).flatMap(approvalData -> {
            //insert approval for user group
            return roleService.updateParentOrder(sqlConnection, positionPatchRole.getParent_id(), positionPatchRole.getOrder(), positionPatchRole.getRole_id()).flatMap(integer -> {
                if(finalIndex >= listData.size() - 1){
                    return Observable.just(listData);
                }else{
                    return updateRoleParentOrderList(sqlConnection, listData, finalIndex + 1);
                }
            });
        });

    }

    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();
    private RoleService roleService;
    private CacheGuava cacheGuava;
    private PermissionRoleService permissionRoleService;

    private static final Logger LOGGER = Logger.getLogger(RolePatchHandler.class.getName());
}
