package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.google.gson.Gson;
import com.onepay.ma.service.handler.user.UserResetPassHandler;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


@Component
public class OperatorResetPassHandlerImpl implements UserResetPassHandler {
    private static Logger LOGGER = Logger.getLogger(OperatorResetPassHandlerImpl.class.getName());
    private final static Gson gson = new Gson();
    /**
     * different dictionaries used
     */
    private static final String ALPHA_CAPS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String ALPHA = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMERIC = "**********";
    private static final String SPECIAL_CHARS = "!@#$%^&*_=+-/";
    private static SecureRandom random = new SecureRandom();

    @Autowired
    private OperatorService operatorService;

    @Autowired
    private EmailService emailService;

    @Override
    public void handle(RoutingContext rc) {
        try {
            LOGGER.info("START API RESETPASSWORD ");
            JDBCClient client = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
            String userId = rc.get(ParamsPool.X_USER_ID);
     /*   if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }*/
            io.vertx.core.json.JsonObject body = rc.getBodyAsJson();
            if (body == null) {
                LOGGER.log(Level.SEVERE, "[ OPERATOR RESET PASS ] => BODY POST IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;
            }
            String s_id = body.getString("operatorId") != null ? String.valueOf(body.getString("operatorId")).trim() : StringPool.BLANK;
            if (s_id == null || s_id.equals("")) {
                LOGGER.log(Level.SEVERE, "[ USER RESET PASS ] => S_ID POST IS EMPTY ");
                throw IErrors.VALIDATION_ERROR;
            }
            String newPass = generatePassword();
            LOGGER.severe("New password : " + newPass);
            Observable.using(SQLConnectionFactory::new, f -> f.create(client), f -> f.dispose())
                    .flatMap(sqlConnection -> {
                        connectionBackUp = sqlConnection;
                        return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                            return operatorService.getOperatorById(sqlConnection, s_id).flatMap(userAdmin -> {
                                return operatorService.resetPass(sqlConnection, s_id,newPass).flatMap(userData -> {
                                    userData.setPassword(newPass);
                                    Map map = new HashMap();
                                    map.put("admin", userAdmin);
                                    map.put("operator", userData);
                                    return Observable.just(map);
                                });
                            });
                        });
                    }).subscribe(map -> {
                OperatorDTO operator = (OperatorDTO) map.get("operator");
                if (operator == null) {
                    if (connectionBackUp != null) {
                        connectionBackUp.rollbackObservable();
                    }
                    throw IErrors.VALIDATION_ERROR;
                } else {
                    emailService.sentMailCreateOperator(operator, newPass);
                    connectionBackUp.commitObservable();
                }

                rc.put(ParamsPool.HANDLER_DATA_RESULT, operator);
                rc.next();
            }, throwable -> {
                if (connectionBackUp != null) {
                    connectionBackUp.rollbackObservable();
                }
                rc.fail(throwable);
            });
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private static String generatePassword() {
        String result = "";
        result += generateString(6, ALPHA_CAPS + ALPHA);
        result += generateString(6, NUMERIC);
        return result;
    }

    private static String generateString(int len, String dic) {
        String result = "";
        for (int i = 0; i < len; i++) {
            int index = random.nextInt(dic.length());
            result += dic.charAt(index);
        }
        return result;
    }

    private SQLConnection connectionBackUp;

}
