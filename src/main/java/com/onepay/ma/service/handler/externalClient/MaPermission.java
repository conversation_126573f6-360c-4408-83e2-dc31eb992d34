package com.onepay.ma.service.handler.externalClient;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import java.nio.charset.StandardCharsets;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.Utils;
import java.util.logging.Level;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;

public class MaPermission {

    private static final Logger LOGGER = Logger.getLogger(MaPermission.class.getName());

    private static String ONEPAY_MA_PERMISSION_SERVICE_BASE_URL = PropsUtil.get("onepay_ma_permisssion_base_url", "");

    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static String getMc(String userId, String xRequestId, String partnerId, String merchantReq) {
        JsonObject merchant = MaPermission.getMerchantByUser(userId, xRequestId, partnerId);
        if (null == merchant) {
            throw IErrors.FORBIDDEN;
        }
        JsonArray list = merchant.getJsonArray("list");
        if (null == list) {
            throw IErrors.FORBIDDEN;
        }
        List<String> merchantIdDbs = list.stream().map(obj -> ((JsonObject) obj).getString("id"))
                .collect(Collectors.toList());
        LOGGER.log(Level.INFO, "fund search merchantIds Db : " + String.join(",", merchantIdDbs));
        LOGGER.log(Level.INFO, "requestMerchants: " + merchantReq);
        List<String> requestMerchantsList = new ArrayList<String>(Arrays.asList(merchantReq.split(",")));
        List<String> finalMerchantsList = new ArrayList<String>();
        LOGGER.log(Level.INFO, "Illigal Merchants: " + merchantIdDbs);
        if (StringUtils.isBlank(merchantReq)) {
            LOGGER.log(Level.INFO, "Select ALL merchants");
            finalMerchantsList = merchantIdDbs;
        } else {
            if ("ALL".equals(merchantIdDbs.get(0))) {
                finalMerchantsList = requestMerchantsList;
            } else {
                LOGGER.log(Level.INFO, "Select some merchants");
                requestMerchantsList.retainAll(merchantIdDbs);
                finalMerchantsList = requestMerchantsList;
            }
        }

        return String.join(",", finalMerchantsList);
    }

    public static JsonObject getMerchantByUser(String userId, String xRequestId, String partnerId) {
        JsonObject jsonReturn = null;
        String requestMethod = "GET";
        Date requestDate = new Date();
        String requestTimeOut = PropsUtil.get("onepay_ma_permisssion_timeout", "60000");

        try {
            LOGGER.log(Level.INFO, "GET TO MA-PERMISSION-SERVICE");

            StringBuilder aspOrderStateQueryURL = new StringBuilder(ONEPAY_MA_PERMISSION_SERVICE_BASE_URL)
                    .append("/partner/").append(partnerId).append("/user/").append(userId).append("/merchants");
            LOGGER.log(Level.INFO, "url: " + aspOrderStateQueryURL.toString());
            URL url = new URL(aspOrderStateQueryURL.toString());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty("s-user-id", userId);
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, requestTimeOut);

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("ma permission responseCode:" + responseCode);
            LOGGER.info("ma permission responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                JsonObject acqObject = new JsonObject(response.toString());
                if (acqObject.containsKey("map")) {
                    jsonReturn = acqObject.getJsonObject("map");
                } else {
                    jsonReturn = new JsonObject(response.toString());
                }
                LOGGER.info("acq response" + jsonReturn.toString());
            } else {
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "MA-PERMISSION DATA: {}", strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-PERMISSION  RESPONSE: {}", Utils.mask(jsonObject));
                jsonReturn = jsonObject;
                throw new ErrorException(500, jsonObject.containsKey("name") ? jsonObject.getString("name") : "",
                        jsonObject.containsKey("message") ? jsonObject.getString("message") : "",
                        jsonObject.containsKey("information_link") ? jsonObject.getString("information_link") : "",
                        jsonObject.containsKey("details") ? jsonObject.getString("details") : "");
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[MA-PERMISSION-ERROR] {}", e);
        }
        return jsonReturn;
    }

}
