package com.onepay.ma.service.handler.refund.international;

import com.onepay.ma.service.handler.refund.international.impl.InternationalRefundGetHandlerImpl;
import com.onepay.ma.service.handler.refund.international.impl.InternationalRefundPatchHandlerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;

@Component
public class InternationalRefundHandler {

    @Autowired
    InternationalRefundPatchHandlerImpl patchHandlerImpl;

    @Autowired
    InternationalRefundGetHandlerImpl getHandlerImpl;
    
    public Handler patch() {
        return patchHandlerImpl;
    }
    
    public Handler get() {
        return getHandlerImpl;
    }
}
