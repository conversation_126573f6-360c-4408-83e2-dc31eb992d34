package com.onepay.ma.service.handler.acquirer.domestic.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.acquirer.domestic.DomesticAcquirerGetHandler;
import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.service.AcquirerService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/2/16.
 */
public class DomesticAcquirerGetHandlerImpl implements DomesticAcquirerGetHandler {

    public DomesticAcquirerGetHandlerImpl(AcquirerService acquirerService, MerchantService merchantService) {
        this.acquirerService = acquirerService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        Observable<List<Acquirer>> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            return merchantService.list(connOnline, connBackUp, merchantId, userId, "domestic").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                return acquirerService.list(connReadOnly, String.join(",", merchantIdList));
                                            });
                                        });
                            });
                });
        obs.subscribe(acquirers -> {
            Map map = new HashMap();
            map.put("acquirers", acquirers);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }


    private AcquirerService acquirerService;

    private MerchantService merchantService;


}
