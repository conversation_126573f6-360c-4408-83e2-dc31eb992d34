package com.onepay.ma.service.handler.merchant.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 29-Aug-17.
 */
@Component
public class CustomerMerchantGetHandler implements Handler<RoutingContext> {
    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackup = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String userId = rc.request().getParam("userId");
        if(userId != null){

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                    .flatMap(connBankup -> {
                        return merchantService.listAll(connBankup,  userId);
                    }).subscribe(merchants -> {
                BaseList bs = new BaseList();
                List mpayMerchant = merchants.stream().filter(merchant -> merchant.getType().equals("mpay")).collect(Collectors.toList());
                bs.setList(mpayMerchant);
                bs.setTotal_items(mpayMerchant.size());
                rc.put(ParamsPool.HANDLER_DATA_RESULT, bs);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }else{
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Autowired
    private MerchantService merchantService;
}
