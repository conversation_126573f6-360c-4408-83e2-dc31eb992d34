package com.onepay.ma.service.handler.promotion.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.promotion.PromotionPutHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * Created by huynguyen on 4/2/16.
 */
public class PromotionPutHandlerImpl implements PromotionPutHandler {

    public PromotionPutHandlerImpl(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, PromotionRuleParamService promotionRuleParamService, ApprovalService approvalService) {
        this.promotionService = promotionService;
        this.promotionDiscountService = promotionDiscountService;
        this.merchantService = merchantService;
        this.promotionMerchantService = promotionMerchantService;
        this.promotionRuleService = promotionRuleService;
        this.promotionRuleParamService = promotionRuleParamService;
        this.approvalService = approvalService;
        this.initField();
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String promotionId = rc.request().getParam("promotionId");
        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        String body = rc.getBodyAsString();
        PutBodyRequest<UpdateApprovalData> mapBody = gson.fromJson(body, new TypeToken<PutBodyRequest<UpdateApprovalData>>() {
        }.getType());
        if (mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }


        if (promotionId != null) {
            List<UpdateApprovalData> updateApprovalDataList = mapBody.getItems();
            if ((updateApprovalDataList == null || updateApprovalDataList.size() <= 0) && !mapBody.getCommand().equals("send-to-approve")) {
                throw IErrors.VALIDATION_ERROR;
            }
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f -> f.dispose())
                    .flatMap(connPr -> {
                        connectionPR = connPr;
                        //get online connection
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                .flatMap(connBackUp -> {
                                    connectionBackUp = connBackUp;
                                    return connectionPR.setAutoCommitObservable(false).flatMap(aVoid -> {
                                        return promotionService.get(connectionPR, promotionId).flatMap(promotion -> {
                                            if (promotion.getStatus() == PromotionStatus.DELETED.getValue()) {
                                                throw IErrors.RESOURCE_NOT_FOUND;
                                            }
                                            if (promotion.getStatus() == PromotionStatus.CREATED.getValue()) {
                                                return updatePromotionDataBeforeApproveSend(connectionPR, promotion.getN_id(), updateApprovalDataList, 0).flatMap(updateApprovalData -> {
                                                    if (mapBody.getCommand().equals("send-to-approve")) {
                                                        return promotionService.updateStatus(connectionPR, promotion.getPromotion_id(), PromotionStatus.SEND_TO_APPROVE).flatMap(integer -> {
                                                            String newValue = "2";
                                                            Approval approval = new Approval();
                                                            approval.setUser(userId);
                                                            approval.setSchema("ONEPR");
                                                            approval.setName("PROMOTION UPDATE PROMOTION STATUS");

                                                            approval.setNew_value(newValue);
                                                            approval.setOld_value(String.valueOf(promotion.getStatus()));
                                                            approval.setField("N_STATUS");
                                                            approval.setDescription("Change status of promotion from created to approved");
                                                            ApprovalCondition approvalCondition = new ApprovalCondition();
                                                            List<ApprovalCondition> approvalConditions = new ArrayList<>();
                                                            approvalCondition.setField("PR_ID");
                                                            approvalCondition.setValue(String.valueOf(promotion.getN_id()));
                                                            approvalCondition.setName(promotionId);
                                                            approvalCondition.setType("string");
                                                            approvalConditions.add(approvalCondition);
                                                            ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                                                            approvalCondition1.setField("PR_NAME");
                                                            approvalCondition1.setValue(String.valueOf(promotion.getPromotion_name()));
                                                            approvalCondition1.setType("string");
                                                            approvalConditions.add(approvalCondition1);
                                                            String conditions = gson.toJson(approvalConditions);
                                                            approval.setConditions(conditions);
                                                            approval.setTable("TB_RULE");
                                                            approval.setType("PR_UPDATE_STATUS");

                                                            return approvalService.insert(connectionBackUp, approval).flatMap(approval1 -> {
                                                                // get promotion data final
                                                                return getPromotionData(connectionPR, promotionId);
                                                            });

                                                        });
                                                    }
                                                    // get list data
                                                    return getPromotionData(connectionPR, promotionId);
                                                });
                                            } else {
                                                return promotionMerchantService.list(connectionPR, promotion.getPromotion_id()).flatMap(merchants -> {
                                                    if (merchants.size() > 0) {
                                                        promotion.setCurrency_code(merchants.get(0).getCurrency_code());
                                                    }
                                                    return updatePromotionDataNotCreated(connectionBackUp, promotion.getN_id(), updateApprovalDataList, userId, promotion.getPromotion_id(), promotion.getCurrency_code(), promotion.getPromotion_name(), 0).flatMap(updateApprovalData -> {
                                                        // get list data
                                                        return getPromotionData(connectionPR, promotionId);
                                                    });

                                                });

                                            }

                                        });
                                    });
                                });
                    })
                    .subscribe(promotion -> {
                        if (connectionPR != null) {
                            connectionPR.commitObservable();
                        }
                        if (connectionBackUp != null) {
                            connectionBackUp.commitObservable();
                        }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, promotion);
                        rc.next();
                    }, throwable -> {
                        if (connectionPR != null) {
                            connectionPR.rollbackObservable();
                        }
                        if (connectionBackUp != null) {
                            connectionBackUp.rollbackObservable();
                        }
                        rc.fail(throwable);
                    });

        }
    }

    /**
     * update list data promotion before send to approve
     * @param connPr
     * @param promotionId
     * @param updateApprovalList
     * @param index
     * @return
     */
    private Observable<List<UpdateApprovalData>> updatePromotionDataBeforeApproveSend(SQLConnection connPr, int promotionId, List<UpdateApprovalData> updateApprovalList, int index){
        if(updateApprovalList.size() <= 0){
            return Observable.just(updateApprovalList);
        }
        final int finalIndex = index;
        UpdateApprovalData updateData = updateApprovalList.get(index);
        return Observable.just(updateData).flatMap(updateApprovalData -> {
            if(updateApprovalData.getType().toLowerCase().equals("link_merchants")){
                return  promotionMerchantService.delete(connPr, promotionId).flatMap(integer -> {
                    List<String> merchants = (List<String>) updateApprovalData.getNew_value();
                    return insertMerchantList(connPr, promotionId, merchants, 0).flatMap(strings -> {
                        if (finalIndex >= updateApprovalList.size() - 1) {
                            return Observable.just(updateApprovalList);
                        } else {
                            return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                        }
                    });
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("link_discounts")){
                List<PromotionDiscount> discounts =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionDiscount>>(){}.getType());
                // validate discount
                validateDiscountTypes(discounts);
                return  promotionDiscountService.delete(connPr, promotionId).flatMap(integer -> {
                    // insert promotion discount
                    return insertPromotionDiscountData(connPr, promotionId, discounts, 0).flatMap(strings -> {
                        if (finalIndex >= updateApprovalList.size() - 1) {
                            return Observable.just(updateApprovalList);
                        } else {
                            return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                        }
                    });
                });

            }else if(updateApprovalData.getType().toLowerCase().equals("insert_promotion_rules")){
                List<PromotionRule> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRule>>(){}.getType());
                return  insertPromotionRuleData(connPr, promotionId, rules, 0).flatMap(integer -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });

            }else if(updateApprovalData.getType().toLowerCase().equals("update_promotion_rules")){
                String field = FIELD_MAP.get(updateApprovalData.getField()) == null ? StringPool.BLANK : String.valueOf(FIELD_MAP.get(updateApprovalData.getField()));
                if(field.isEmpty()){
                    throw IErrors.VALIDATION_ERROR;
                }
                int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
                String newValue = (String) updateApprovalData.getNew_value();
                String type = "";
                if(!field.equals("N_ORDER")) {
                    return promotionRuleService.updateByField(connPr, ruleId, field, newValue).flatMap(integer -> {
                        if (finalIndex >= updateApprovalList.size() - 1) {
                            return Observable.just(updateApprovalList);
                        } else {
                            return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                        }
                    });
                }else{
                    return promotionRuleService.updateOrder(connPr, promotionId, ruleId, newValue).flatMap(integer -> {
                        if (finalIndex >= updateApprovalList.size() - 1) {
                            return Observable.just(updateApprovalList);
                        } else {
                            return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                        }
                    });
                }

            }else if(updateApprovalData.getType().toLowerCase().equals("delete_promotion_rules")){
                int ruleId = -1;
                List<OtherConditions> otherConditions = updateApprovalData.getOther_conditions();
                for (OtherConditions otherCondition : otherConditions){
                    if(otherCondition.getKey().equals("rule_id")) {
                        ruleId = Integer.valueOf(otherCondition.getValue());
                    }
                }

                return promotionRuleService.delete(connPr, promotionId, ruleId).flatMap(integer -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("update_promotion_rules_param")){
                int ruleTypeId = 0;
                int ruleParamId = 0;
                List<OtherConditions> otherConditions = updateApprovalData.getOther_conditions();
                for (OtherConditions otherCondition : otherConditions){
                    if(otherCondition.getKey().equals("rule_type_id")){
                        ruleTypeId = Integer.valueOf(otherCondition.getValue());
                    }else if(otherCondition.getKey().equals("rule_param_id")){
                        ruleParamId = Integer.valueOf(otherCondition.getValue());
                    }
                }
                List<PromotionRuleParam> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRuleParam>>(){}.getType());

                return updatePromotionRuleParam(connPr, ruleTypeId, ruleParamId, rules, 0).flatMap(promotionRuleParams -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("insert_promotion_rules_param")){
                int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
                List<PromotionRuleParam> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRuleParam>>(){}.getType());
                return insertPromotionRuleParam(connPr, ruleId, rules, 0).flatMap(promotionRuleParams -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("delete_promotion_rules_param")){
                int ruleId = 0;
                int ruleParamId = 0;
                List<OtherConditions> otherConditions = updateApprovalData.getOther_conditions();
                for (OtherConditions otherCondition : otherConditions){
                    if(otherCondition.getKey().equals("rule_id")){
                        ruleId = Integer.valueOf(otherCondition.getValue());
                    }else if(otherCondition.getKey().equals("rule_param_id")){
                        ruleParamId = Integer.valueOf(otherCondition.getValue());
                    }
                }
                return promotionRuleParamService.delete(connPr, ruleId, ruleParamId).flatMap(integer -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });
            }else{
                String field = FIELD_MAP.get(updateApprovalData.getField()) == null ? StringPool.BLANK : String.valueOf(FIELD_MAP.get(updateApprovalData.getField()));
                if(field.isEmpty()){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Can not find field" + StringPool.SPACE + updateApprovalData.getField());
                    throw IErrors.VALIDATION_ERROR;
                }
                String newValue = "";
                String sql = "UPDATE ONEPR.TB_PR"
                        + " SET "
                        + field + StringPool.EQUAL + StringPool.QUESTION
                        + " WHERE N_ID = ?";
                JsonArray params = new JsonArray();
                if(field.equals("PROMOTION_TIME")){
                    Map<String, Object> map = (Map<String, Object>) updateApprovalData.getNew_value();
                    String startTime = map.get("start_time") == null ? StringPool.BLANK : String.valueOf(map.get("start_time"));
                    String endTime = map.get("end_time") == null ? StringPool.BLANK : String.valueOf(map.get("end_time"));
                    try {
                        Date startTimeO = DATE_TIME_FORMAT.parse(startTime);
                        Date endTimeO = DATE_TIME_FORMAT.parse(endTime);
                        if(endTimeO.before(startTimeO)){
                            LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] TIME ERROR");
                            throw IErrors.VALIDATION_ERROR;
                        }
                    } catch (ParseException e) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] INVALID DATE", e);
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    sql = "UPDATE ONEPR.TB_PR"
                            + " SET "
                            + " D_START = TO_TIMESTAMP(?, 'DD/MM/YYYY HH:MI AM')"
                            + StringPool.COMMA
                            + " D_END = TO_TIMESTAMP(?, 'DD/MM/YYYY HH:MI AM') "
                            + " WHERE N_ID = ?";
                    params.add(startTime)
                            .add(endTime)
                            .add(promotionId);

                }else{
                   newValue = (String) updateApprovalData.getNew_value();
                   params.add(newValue).add(promotionId);
                }


                return  promotionService.updateByField(connPr, sql, params).flatMap(integer -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataBeforeApproveSend(connPr, promotionId, updateApprovalList, finalIndex + 1);
                    }
                });

            }

        });
    }

    /**
     * update list data promotion before send to approve
     * @param connBackUp
     * @param promotionId
     * @param updateApprovalList
     * @param index
     * @return
     */
    private Observable<List<UpdateApprovalData>> updatePromotionDataNotCreated(SQLConnection connBackUp, int promotionId, List<UpdateApprovalData> updateApprovalList, String userId, String promotionNameId, String currency, String promotionName, int index){
        if(updateApprovalList.size() <= 0){
            return Observable.just(updateApprovalList);
        }
        final int finalIndex = index;
        UpdateApprovalData updateData = updateApprovalList.get(index);


        return Observable.just(updateData).flatMap(updateApprovalData -> {
            if(updateApprovalData.getType().toLowerCase().equals("link_merchants")){
                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION LINK MERCHANT UPDATE APPROVAL");

                approval.setNew_value(gson.toJson(updateApprovalData.getNew_value()));
                approval.setOld_value(gson.toJson(updateApprovalData.getOld_value()));
                approval.setField("LINK_MERCHANT");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_PR_MERCHANT");
                approval.setType("PR_LINK_MERCHANT_APPROVAL");
                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("link_discounts")){
                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION LINK DISCOUNT UPDATE APPROVAL");

                approval.setNew_value(gson.toJson(updateApprovalData.getNew_value()));
                approval.setOld_value(gson.toJson(updateApprovalData.getOld_value()));
                approval.setField("LINK_DISCOUNT");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("PR_CURRENCY");
                approvalCondition.setValue(String.valueOf(currency));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_PR_DISCOUNT");
                approval.setType("PR_LINK_DISCOUNT_APPROVAL");
                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });

            }else if(updateApprovalData.getType().toLowerCase().equals("insert_promotion_rules")){
                List<PromotionRule> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRule>>(){}.getType());
                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION INSERT NEW PROMOTION RULES");

                approval.setNew_value(gson.toJson(rules));
                approval.setOld_value("");
                approval.setField("INSERT_PR_RULE");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("PR_CURRENCY");
                approvalCondition.setValue(currency);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition);
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_PR_RULE");
                approval.setType("PR_INSERT_RULE_APPROVAL");

                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });

            }else if(updateApprovalData.getType().toLowerCase().equals("update_promotion_rules")){
                String field = FIELD_MAP.get(updateApprovalData.getField()) == null ? StringPool.BLANK : String.valueOf(FIELD_MAP.get(updateApprovalData.getField()));
                if(field.isEmpty()){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Can not find field" + StringPool.SPACE + updateApprovalData.getField());
                    throw IErrors.VALIDATION_ERROR;
                }
                int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
                String newValue = (String) updateApprovalData.getNew_value();
                String oldValue =(String) updateApprovalData.getOld_value();
                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION UPDATE PROMOTION RULES");

                approval.setNew_value(newValue);
                approval.setOld_value(oldValue);
                approval.setField(field);
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("N_ID");
                approvalCondition.setValue(String.valueOf(ruleId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_RULE");
                approval.setType("PR_UPDATE_RULE_APPROVAL");

                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName,finalIndex + 1);
                    }
                });


            }else if(updateApprovalData.getType().toLowerCase().equals("delete_promotion_rules")){
                int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION DELETE PROMOTION RULES");

                approval.setNew_value("");
                approval.setOld_value("");
                approval.setField("DELETE_PR_RULE");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("N_ID");
                approvalCondition.setValue(String.valueOf(ruleId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_RULE");
                approval.setType("PR_DELETE_RULE_APPROVAL");
                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("update_promotion_rules_param")){
                int ruleTypeId = 0;
                int ruleParamId = 0;
                List<OtherConditions> otherConditions = updateApprovalData.getOther_conditions();
                for (OtherConditions otherCondition : otherConditions){
                    if(otherCondition.getKey().equals("rule_type_id")){
                        ruleTypeId = Integer.valueOf(otherCondition.getValue());
                    }else if(otherCondition.getKey().equals("rule_param_id")){
                        ruleParamId = Integer.valueOf(otherCondition.getValue());
                    }
                }
                List<PromotionRuleParam> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRuleParam>>(){}.getType());

                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION UPDATE PROMOTION RULES PARAM");

                approval.setNew_value(gson.toJson(rules));
                approval.setOld_value(gson.toJson(updateApprovalData.getOld_value()));
                approval.setField("UPDATE_PR_RULE_PARAM");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("RULE_TYPE_ID");
                approvalCondition.setValue(String.valueOf(ruleTypeId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("RULE_PARAM_ID");
                approvalCondition.setValue(String.valueOf(ruleParamId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("PR_CURRENCY");
                approvalCondition.setValue(String.valueOf(currency));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_RULE_PARAM");
                approval.setType("PR_UPDATE_RULE_PARAM_APPROVAL");

                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("insert_promotion_rules_param")){
                int ruleId = Integer.valueOf(updateApprovalData.getOther_conditions().get(0).getValue());
                List<PromotionRuleParam> rules =  gson.fromJson(gson.toJson(updateApprovalData.getNew_value()), new TypeToken<List<PromotionRuleParam>>(){}.getType());

                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION INSERT PROMOTION RULES PARAM");

                approval.setNew_value(gson.toJson(rules));
                approval.setOld_value("");
                approval.setField("INSERT_PR_RULE_PARAM");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("RULE_ID");
                approvalCondition.setValue(String.valueOf(ruleId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_RULE_PARAM");
                approval.setType("PR_INSERT_RULE_PARAM_APPROVAL");

                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });
            }else if(updateApprovalData.getType().toLowerCase().equals("delete_promotion_rules_param")){
                int ruleId = 0;
                int ruleParamId = 0;
                List<OtherConditions> otherConditions = updateApprovalData.getOther_conditions();
                for (OtherConditions otherCondition : otherConditions){
                    if(otherCondition.getKey().equals("rule_id")){
                        ruleId = Integer.valueOf(otherCondition.getValue());
                    }else if(otherCondition.getKey().equals("rule_param_id")){
                        ruleParamId = Integer.valueOf(otherCondition.getValue());
                    }
                }

                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION DELETE PROMOTION RULES PARAM");

                approval.setNew_value("");
                approval.setOld_value("");
                approval.setField("DELETE_PR_RULE_PARAM");
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("RULE_ID");
                approvalCondition.setValue(String.valueOf(ruleId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                approvalCondition = new ApprovalCondition();
                approvalCondition.setField("RULE_PARAM_ID");
                approvalCondition.setValue(String.valueOf(ruleParamId));
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);

                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_RULE_PARAM");
                approval.setType("PR_DELETE_RULE_PARAM_APPROVAL");

                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });
            }else{
                String field = FIELD_MAP.get(updateApprovalData.getField()) == null ? StringPool.BLANK : String.valueOf(FIELD_MAP.get(updateApprovalData.getField()));
                if(field.isEmpty()){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Can not find field" + StringPool.SPACE + updateApprovalData.getField());
                    throw IErrors.VALIDATION_ERROR;
                }

                if(field.equals("PROMOTION_TIME")){
                    Map<String, Object> map = (Map<String, Object>) updateApprovalData.getNew_value();
                    String startTime = map.get("start_time") == null ? StringPool.BLANK : String.valueOf(map.get("start_time"));
                    String endTime = map.get("end_time") == null ? StringPool.BLANK : String.valueOf(map.get("end_time"));
                    try {
                        Date startTimeO = DATE_TIME_FORMAT.parse(startTime);
                        Date endTimeO = DATE_TIME_FORMAT.parse(endTime);
                        if(endTimeO.before(startTimeO)){
                            LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] TIME ERROR");
                            throw IErrors.VALIDATION_ERROR;
                        }
                    } catch (ParseException e) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] INVALID DATE", e);
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                }

                Approval approval  = new Approval();
                approval.setUser(userId);
                approval.setSchema("ONEPR");
                approval.setName("PROMOTION UPDATE DETAIL DATA");
                String newValueGson = gson.toJson(updateApprovalData.getNew_value());
                String oldValueGson = gson.toJson(updateApprovalData.getOld_value());
                if(!oldValueGson.startsWith("{") && !oldValueGson.endsWith("}")) {
                    oldValueGson = String.valueOf(updateApprovalData.getOld_value());
                }
                if(!newValueGson.startsWith("{") && !newValueGson.endsWith("}")) {
                    newValueGson = String.valueOf(updateApprovalData.getNew_value());
                }

                approval.setNew_value(newValueGson);
                approval.setOld_value(oldValueGson);
                approval.setField(field);
                approval.setDescription(updateApprovalData.getDescription());
                ApprovalCondition approvalCondition = new ApprovalCondition();
                List<ApprovalCondition> approvalConditions = new ArrayList<>();
                approvalCondition.setField("PR_ID");
                approvalCondition.setValue(String.valueOf(promotionId));
                approvalCondition.setName(promotionNameId);
                approvalCondition.setType("string");
                approvalConditions.add(approvalCondition);
                ApprovalCondition approvalCondition1 =  new ApprovalCondition();
                approvalCondition1.setField("PR_NAME");
                approvalCondition1.setValue(String.valueOf(promotionName));
                approvalCondition1.setType("string");
                approvalConditions.add(approvalCondition1);
                String conditions = gson.toJson(approvalConditions);
                approval.setConditions(conditions);
                approval.setTable("TB_PR");
                approval.setType("PR_UPDATE_DETAIL_APPROVAL");


                return approvalService.insert(connBackUp, approval).flatMap(approval1 -> {
                    if (finalIndex >= updateApprovalList.size() - 1) {
                        return Observable.just(updateApprovalList);
                    } else {
                        return updatePromotionDataNotCreated(connBackUp, promotionId, updateApprovalList, userId, promotionNameId, currency, promotionName, finalIndex + 1);
                    }
                });


            }

        });
    }

    /**
     * insert list promotion discount data
     * @param connPr
     * @param promotionId
     * @param discounts
     * @param index
     * @return
     */
    private Observable<List<PromotionDiscount>> insertPromotionDiscountData(SQLConnection connPr, int promotionId, List<PromotionDiscount> discounts, int index){
        if(discounts.size() <= 0){
            return Observable.just(discounts);
        }
        final int finalIndex = index;
        PromotionDiscount discount = discounts.get(index);
        return Observable.just(discount).flatMap(s -> {
            return  promotionDiscountService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= discounts.size() - 1){
                    return Observable.just(discounts);
                }else{
                    return insertPromotionDiscountData(connPr, promotionId, discounts, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert list merchant data
     * @param connPr
     * @param promotionId
     * @param merchants
     * @param index
     * @return
     */
    private Observable<List<String>> insertMerchantList(SQLConnection connPr, int promotionId, List<String> merchants, int index){
        if(merchants.size() <= 0){
            return Observable.just(merchants);
        }
        final int finalIndex = index;
        String merchant = merchants.get(index);
        return Observable.just(merchant).flatMap(s -> {
            return  promotionMerchantService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= merchants.size() - 1){
                    return Observable.just(merchants);
                }else{
                    return insertMerchantList(connPr, promotionId, merchants, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert list promotion rule data
     * @param connPr
     * @param promotionId
     * @param rules
     * @param index
     * @return
     */
    private Observable<List<PromotionRule>> insertPromotionRuleData(SQLConnection connPr, int promotionId, List<PromotionRule> rules, int index){
        if(rules.size() <= 0){
            return Observable.just(rules);
        }
        final int finalIndex = index;
        PromotionRule rule = rules.get(index);
        return Observable.just(rule).flatMap(s -> {
            return  promotionRuleService.insert(connPr, promotionId, s).flatMap(integer -> {
                if(finalIndex >= rules.size() - 1){
                    return Observable.just(rules);
                }else{
                    return insertPromotionRuleData(connPr, promotionId, rules, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get promotion data
     * @param sqlConnPr
     * @param promotionId
     * @return
     */
    private Observable<Promotion> getPromotionData(SQLConnection sqlConnPr, String promotionId){
        return promotionService.get(sqlConnPr, promotionId).flatMap(promotionData -> {
            //get discount data
            return promotionDiscountService.listDiscount(sqlConnPr, StringPool.BLANK, promotionData.getPromotion_id()).flatMap(discountsData -> {
                promotionData.setDiscounts(discountsData);
                // get merchant data
                return promotionMerchantService.list(sqlConnPr, promotionData.getPromotion_id()).flatMap(merchantsData -> {
                    promotionData.setMerchants(merchantsData);
                    if(merchantsData.size() > 0) {
                        promotionData.setCurrency_code(merchantsData.get(0).getCurrency_code());
                    }
                    //get rule data
                    return promotionRuleService.list(sqlConnPr, promotionData.getPromotion_id()).map(rulesData -> {
                        promotionData.setRules(rulesData);
                        //return final promotion data
                        return promotionData;
                    });
                });
            });
        });
    }

    /**
     * insert list promotion rule param
     * @param sqlConn
     * @param promotionRuleParams
     * @param index
     * @return
     */
    private Observable<List<PromotionRuleParam>> insertPromotionRuleParam(SQLConnection sqlConn, int ruleId, List<PromotionRuleParam> promotionRuleParams, int index){
        if(promotionRuleParams.size() <= 0){
            return Observable.just(promotionRuleParams);
        }
        PromotionRuleParam promotionRuleParam = promotionRuleParams.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRuleParam).flatMap(serviceApproval -> {
            //get user data
            return promotionRuleParamService.insert(sqlConn, ruleId, promotionRuleParam).flatMap(integer -> {
                if(finalIndex >= promotionRuleParams.size() - 1){
                    return Observable.just(promotionRuleParams);
                }else{
                    return insertPromotionRuleParam(sqlConn, ruleId, promotionRuleParams, finalIndex + 1);
                }
            });
        });
    }

    /**
     * update list promotion rule param
     * @param sqlConn
     * @param promotionRuleParams
     * @param index
     * @return
     */
    private Observable<List<PromotionRuleParam>> updatePromotionRuleParam(SQLConnection sqlConn, int ruleTypeId, int ruleParamId,  List<PromotionRuleParam> promotionRuleParams, int index){
        if(promotionRuleParams.size() <= 0){
            return Observable.just(promotionRuleParams);
        }
        PromotionRuleParam promotionRuleParam = promotionRuleParams.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRuleParam).flatMap(ruleParam -> {
            if(ruleParam.getRule_param_value() != null && !ruleParam.getRule_param_value().isEmpty()) {
                //get regex
                return promotionRuleParamService.getRegexRuleTYpe(sqlConn, ruleTypeId, ruleParam.getRule_param_name()).flatMap(s -> {
                    if (s == null || s.isEmpty()) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Empty validation regex with rule type = " + ruleTypeId + " and param name = " + ruleParam.getRule_param_name());
                        throw IErrors.VALIDATION_ERROR;
                    }
                    final Pattern pattern = Pattern.compile(s);

                    if (!pattern.matcher(ruleParam.getRule_param_value()).matches()) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Invalid param value format with paramName " + ruleParam.getRule_param_name());
                        throw IErrors.VALIDATION_ERROR;
                    }
                    //update promotion rule
                    return promotionRuleParamService.update(sqlConn, ruleParamId, promotionRuleParam).flatMap(integer -> {
                        if(finalIndex >= promotionRuleParams.size() - 1){
                            return Observable.just(promotionRuleParams);
                        }else{
                            return updatePromotionRuleParam(sqlConn, ruleTypeId, ruleParamId, promotionRuleParams, finalIndex + 1);
                        }
                    });
                });
            }else{
                //update promotion rule
                return promotionRuleParamService.update(sqlConn, ruleParamId, promotionRuleParam).flatMap(integer -> {
                    if(finalIndex >= promotionRuleParams.size() - 1){
                        return Observable.just(promotionRuleParams);
                    }else{
                        return updatePromotionRuleParam(sqlConn, ruleTypeId, ruleParamId, promotionRuleParams, finalIndex + 1);
                    }
                });
            }


        });
    }

    /**
     * validate discount
     * @param promotionDiscounts
     */
    private void validateDiscountTypes(List<PromotionDiscount> promotionDiscounts) {

        int lastTypeId = 0;

        if (promotionDiscounts == null || promotionDiscounts.size() <= 0) {
            LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Invalid discounts");
            throw IErrors.VALIDATION_ERROR;
        }
        for (PromotionDiscount promotionDiscount : promotionDiscounts) {

            int discountType = promotionDiscount.getDiscount_type().getDiscount_type_id();
            if (discountType <= 0) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Invalid discount type");
                throw IErrors.VALIDATION_ERROR;
            }
            if(discountType != ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID){

                if(promotionDiscount.getDiscount_value() == null){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Invalid discount value");
                    throw IErrors.VALIDATION_ERROR;
                }
            }else{
                double discountValue = 0;
                for (Map.Entry<String,Object> map : promotionDiscount.getDiscount_value().get(0).entrySet()){
                    discountValue = Double.valueOf(map.getValue().toString());
                }
                if(discountValue < 0){
                    LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Invalid discount value > 0");
                    throw IErrors.VALIDATION_ERROR;
                }
            }
            if (lastTypeId == ParamsPool.DISCOUNT_TYPE_PERCENT_ID
                    && (discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID)) {

                LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Discount types can not contains both percent and fix type");
                throw IErrors.VALIDATION_ERROR;

            } else if (lastTypeId == ParamsPool.DISCOUNT_TYPE_FIX_ID && (discountType == ParamsPool.DISCOUNT_TYPE_FIX_ID
                    || discountType == ParamsPool.DISCOUNT_TYPE_PERCENT_ID || discountType == ParamsPool.DISCOUNT_TYPE_MAX_PER_TXN_ID)) {


                LOGGER.log(Level.SEVERE, "[ PROMOTION PUT] Discount types can not contains both percent and fix type");
                throw IErrors.VALIDATION_ERROR;

            } else {

                lastTypeId = discountType;

            }

        }


    }

    /**
     * init field
     */
    private void initField(){
        FIELD_MAP.put("promotion.promotion_name", "S_NAME");
        FIELD_MAP.put("promotion.time", "PROMOTION_TIME");
        FIELD_MAP.put("promotion.description", "S_DESC");
        FIELD_MAP.put("promotion.rules.rule_name", "S_NAME");
        FIELD_MAP.put("promotion.rules.description", "S_DESC");
        FIELD_MAP.put("promotion.rules.message.vi", "S_VI_MESSAGE");
        FIELD_MAP.put("promotion.rules.message.en", "S_EN_MESSAGE");
        FIELD_MAP.put("promotion.rules.order", "N_ORDER");
    }

    private SQLConnection connectionPR = null;

    private SQLConnection connectionBackUp = null;

    private PromotionService promotionService;

    private PromotionDiscountService promotionDiscountService;

    private MerchantService merchantService;

    private PromotionMerchantService promotionMerchantService;

    private ApprovalService approvalService;

    private PromotionRuleService promotionRuleService;

    private PromotionRuleParamService promotionRuleParamService;

    private final static Map FIELD_MAP = new HashMap<>();

    private static final SimpleDateFormat DATE_TIME_FORMAT = new SimpleDateFormat("dd/MM/yyyy hh:mm a");

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(PromotionPutHandler.class.getName());
}
