package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.notification.MpayvnNotificationMsgQuery;
import com.onepay.ma.service.service.notification.CustomerAppNotifyService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Created by anhkh on 26-Mar-18.
 */
@Component
public class CustomerNotifyMsgGetHandler implements Handler<RoutingContext> {

    @Autowired
    private CustomerAppNotifyService customerAppNotifyService;

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String msgId = rc.request().getParam(ParamsPool.ID);

        if(msgId == null) {

            String state = rc.request().getParam(ParamsPool.STATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATE));
            String keyword = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);

            MpayvnNotificationMsgQuery searchQuery = new MpayvnNotificationMsgQuery();
            searchQuery.setState(state);
            searchQuery.setFrom_date(fromDate);
            searchQuery.setTo_date(toDate);
            searchQuery.setKeywords(keyword);
            searchQuery.setPage(page);
            searchQuery.setPageSize(pageSize);

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return this.customerAppNotifyService.searchMsg(connBackup, searchQuery);
                    }).subscribe(mpayNotificationBaseList -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayNotificationBaseList);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        } else {

            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackup -> {
                        return this.customerAppNotifyService.getNotifiMsgById(connBackup, Integer.valueOf(msgId));
                    }).subscribe(mpayvnNotificationMsg -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, mpayvnNotificationMsg);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }

    }
}
