package com.onepay.ma.service.handler.permission;

import com.onepay.ma.service.handler.permission.impl.PermissionPutHandlerImpl;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface PermissionPutHandler extends Handler<RoutingContext> {
    static PermissionPutHandlerImpl create(PermissionService permissionService, CacheGuava cacheGuava){
        return new PermissionPutHandlerImpl(permissionService, cacheGuava);
    }
}
