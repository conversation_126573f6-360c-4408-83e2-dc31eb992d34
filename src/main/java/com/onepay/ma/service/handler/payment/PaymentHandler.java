package com.onepay.ma.service.handler.payment;

import com.onepay.ma.service.handler.payment.impl.PaymentGetHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by anhkh on 27-May-17.
 */
@Component
public class PaymentHandler  {

    @Autowired
    private PaymentGetHandler paymentGetHandler;

    public Handler get() {
        return paymentGetHandler;
    }
}
