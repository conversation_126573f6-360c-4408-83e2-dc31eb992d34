package com.onepay.ma.service.handler.role.impl;


import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.role.RoleGetHandler;
import com.onepay.ma.service.models.RoleData;
import com.onepay.ma.service.models.TreeRole;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by huynguyen on 3/12/16.
 */
public class RoleGetHandlerImpl implements RoleGetHandler {

    public RoleGetHandlerImpl(RoleService roleService) {
        this.roleService = roleService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String roleId = rc.request().getParam("id");
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(roleId == null) {
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return roleService.list(connBackUp, keywords);
            }).subscribe(roles -> {
                Map map = new HashMap<>();
                map.put("roles", roleToTreeData(1, roles.getRoles()));
                rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });;
        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                    .flatMap(connBackUp -> {
                        return roleService.get(connBackUp, roleId);
            }).subscribe(roleData -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, roleData);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }

    /**
     *
     * @param parentId
     * @param list
     * @return
     */
    private static List<TreeRole> roleToTreeData(int parentId, List<RoleData> list){
        TreeRole treeRole;
        List<TreeRole> returnTree = new ArrayList<>();
        if(list == null) return returnTree;

        for (RoleData roleData : list){
            int parent =  roleData.getParent_id();
            if(parent == parentId){
                treeRole = new TreeRole();
                treeRole.setRole_name(roleData.getRole_name());
                treeRole.setRole_id(roleData.getRole_id());
                treeRole.setPermissions(roleData.getPermissions());
                treeRole.setDescription(roleData.getDescription());
                treeRole.setParent_id(roleData.getParent_id());
                treeRole.setOrder(roleData.getOrder());
                List<TreeRole> roleList = roleToTreeData(roleData.getRole_id(), list);
                treeRole.setSub_roles(roleList);
                if(parent == 1){
                    treeRole.setRole_type("rootfolder");

                }else {
                    if (roleList.size() > 0) {
                        treeRole.setRole_type("folder");
                    } else {
                        treeRole.setRole_type("file");
                    }
                }
                returnTree.add(treeRole);
            }
        }

        return returnTree;
    }


    private RoleService roleService;
}
