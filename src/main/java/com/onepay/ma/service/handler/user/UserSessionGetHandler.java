package com.onepay.ma.service.handler.user;


import com.onepay.ma.service.handler.user.impl.UserSessionGetHandlerImpl;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserTerminalService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/9/16.
 */

public interface UserSessionGetHandler extends Handler<RoutingContext> {
    static UserSessionGetHandlerImpl create(OneAMConfig oneAMConfig, UserService userService, UserPermissionService userPermissionService, CacheGuava cacheGuava, MerchantService merchantService, UserTerminalService userTerminalService){
        return new UserSessionGetHandlerImpl(oneAMConfig, userService, userPermissionService, cacheGuava, merchantService, userTerminalService);
    }
}
