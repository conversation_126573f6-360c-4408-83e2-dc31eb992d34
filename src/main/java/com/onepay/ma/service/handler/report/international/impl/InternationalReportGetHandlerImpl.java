package com.onepay.ma.service.handler.report.international.impl;

import com.onepay.commons.util.Convert;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.report.domestic.DomesticReportGetHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.InternationalReportService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public class InternationalReportGetHandlerImpl implements DomesticReportGetHandler {

    public InternationalReportGetHandlerImpl(InternationalReportService internationalReportService, MerchantService merchantService) {
        this.internationalReportService = internationalReportService;
        this.merchantService = merchantService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
            oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);

        int interval = rc.request().getParam(ParamsPool.INTERVAL) == null ?   1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
//        if(interval == 1){
//            if (months > 3) {
//                throw new InvalidValueException("This search you have requested is too large for daily interval.  Please select information or narrow date range to within 3 months and retry.");
//            }
//        }else if(interval == 2){
//            if (months > 6) {
//                throw new InvalidValueException("This search you have requested is too large for weekly interval.  Please select information or narrow date range to within 6 months and retry.");
//            }
//        }else {
//            if (months > 12) {
//                throw new InvalidValueException("This search you have requested is too large .  Please select information or narrow date range to within 1 year and retry.");
//            }
//        }


        int bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? 0 :  Integer.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));

        String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

        String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));

        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

        String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

        String reportType = rc.request().getParam(ParamsPool.REPORT_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REPORT_TYPE));

        String version = rc.request().getParam(ParamsPool.VERSION) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.VERSION));

        InternationalReportParameter parameter = new InternationalReportParameter();
        parameter.setAcquirerId(bankId);
        parameter.setFromDate(fromDate);
        parameter.setToDate(toDate);
        parameter.setInterval(interval);
        parameter.setCardType(cardType);
        parameter.setKeywords(keywords);
        parameter.setVersion(version);
        Observable<Map> obs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                            //get online connection
                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                    .flatMap(connOnline -> {
                                        //get back up connection
                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                .flatMap(connBackUp -> {
                                                    //return merchant
                                                   return merchantService.list(connOnline, connBackUp, merchantId, userId, "international").flatMap(merchants -> {
                                                        Map reportData = new HashMap();
                                                       if(!reportType.equals("detail")) {
                                                           if("v2".equals(parameter.getVersion())){
                                                               //start tuydv them
                                                               String[] currencyList = {"VND", "USD", "THB", "SGD", "MYR", "IDR", "JPY", "KRW","TWD","CNY"};
                                                               if (!currency.isEmpty()) {
                                                                   currencyList = new String[1];
                                                                   currencyList[0] = currency;
                                                               }

                                                               List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                               parameter.setMerchantId(String.join(",", merchantIdList));
                                                               reportData.put("type", "currency");
                                                               return getListReportByCurrency(connReadOnly, Arrays.asList(currencyList), reportData, parameter, 0);
                                                               //end tuydv them
                                                           }else {
                                                               if (merchantId.isEmpty()) {

                                                                   String[] currencyList = {"VND", "USD", "THB", "SGD", "MYR", "IDR", "JPY", "KRW","TWD","CNY"};
                                                                   if (!currency.isEmpty()) {
                                                                       currencyList = new String[1];
                                                                       currencyList[0] = currency;
                                                                   }

                                                                   List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                                   parameter.setMerchantId(String.join(",", merchantIdList));
                                                                   reportData.put("type", "currency");
                                                                   return getListReportByCurrency(connReadOnly, Arrays.asList(currencyList), reportData, parameter, 0);
                                                               } else {
                                                                   reportData.put("type", "merchant");
                                                                   String[] merchantList = merchantId.split(",");
                                                                   parameter.setCurrency(currency);
                                                                   return getListReportByMerchant(connReadOnly, Arrays.asList(merchantList), reportData, parameter, 0);
                                                               }
                                                           }
                                                       }else{
                                                           parameter.setCurrency(currency);
                                                           return internationalReportService.listByCurrencyDetail(connReadOnly, parameter).map(internationalReports -> {
//                                                               InternationalReportConvert report = new InternationalReportConvert();
//                                                               report.setItems(internationalReports);

                                                               int totalTransCount = 0;
                                                               int totalRefundCount = 0;
                                                               double totalTransAmount = 0;
                                                               double totalRefundAmount = 0;
                                                               for (InternationalReport reportInter : internationalReports) {
                                                                   totalTransCount += reportInter.getTransaction_count();
                                                                   totalRefundCount += reportInter.getRefund_count();
                                                                   totalTransAmount += reportInter.getTransaction_total();
                                                                   totalRefundAmount += reportInter.getRefund_total();
                                                               }
//                                                               report.setTotal_transaction_count(totalTransCount);
//                                                               report.setTotal_transaction_total(totalTransAmount);
//                                                               report.setTotal_refund_count(totalRefundCount);
//                                                               report.setTotal_refund_total(totalRefundAmount);
                                                               if(internationalReports.size() > 0) {
                                                                   reportData.put("currency", internationalReports.get(0).getCurrency());
                                                               }
                                                               reportData.put("items", internationalReports);
                                                               reportData.put("total_refund_count", totalRefundCount);
                                                               reportData.put("total_refund_total", totalRefundAmount);
                                                               reportData.put("total_transaction_total", totalTransAmount);
                                                               reportData.put("total_transaction_count", totalTransCount);
                                                               return reportData;
                                                           });
                                                       }
                                                    });
                                                });
                                    });
                        });
        obs.subscribe(reports -> {
            Map returnReport = new HashMap();
            returnReport.put("reports", reports);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }

    /**
     * get list report by merchant data list
     * @param sqlConnection
     * @param listMerchant
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getListReportByMerchant(SQLConnection sqlConnection, List<String> listMerchant, Map returnMap, InternationalReportParameter parameter, int index) {
        if(listMerchant.size() <= 0){
            return Observable.just(returnMap);
        }
        String merchant = listMerchant.get(index);
        final int finalIndex = index;
        return Observable.just(merchant).flatMap(merchantData -> {
            parameter.setMerchantId(merchantData);
            //insert approval for user group
            return internationalReportService.getTotalByMerchant(sqlConnection, parameter).flatMap(integer -> {
                if(integer > 45000){
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                return internationalReportService.listByMerchant(sqlConnection, parameter).flatMap(internationalReports -> {
                    InternationalReportConvert report = new InternationalReportConvert();
                    report.setItems(internationalReports);
                    int totalTransCount = 0;
                    int totalRefundCount = 0;
                    double totalTransAmount = 0;
                    double totalRefundAmount = 0;
                    for (InternationalReport reportData : internationalReports) {
                        totalTransCount += reportData.getTransaction_count();
                        totalRefundCount += reportData.getRefund_count();
                        totalTransAmount += reportData.getTransaction_total();
                        totalRefundAmount += reportData.getRefund_total();
                    }
                    report.setTotal_transaction_count(totalTransCount);
                    report.setTotal_transaction_total(totalTransAmount);
                    report.setTotal_refund_count(totalRefundCount);
                    report.setTotal_refund_total(totalRefundAmount);
                    if(internationalReports.size() > 0) {
                        report.setCurrency(internationalReports.get(0).getCurrency());
                    }
                    returnMap.put(merchantData, report);
                    if(finalIndex >= listMerchant.size() - 1){
                        return Observable.just(returnMap);
                    }else{
                        return getListReportByMerchant(sqlConnection, listMerchant, returnMap, parameter, finalIndex + 1);
                    }
                });
            });
        });

    }

    /**
     * get list report by currency data list
     * @param sqlConnection
     * @param listCurrency
     * @param parameter
     * @param index
     * @return
     */
    private Observable<Map> getListReportByCurrency(SQLConnection sqlConnection, List<String> listCurrency, Map returnMap, InternationalReportParameter parameter, int index) {
        if (listCurrency.size() <= 0) {
            return Observable.just(returnMap);
        }
        String currency = listCurrency.get(index);
        final int finalIndex = index;
        return Observable.just(currency).flatMap(currencyData -> {
            parameter.setCurrency(currencyData);
            //insert approval for user group
            return internationalReportService.getTotalByCurrency(sqlConnection, parameter).flatMap(integer -> {
                if (integer > 45000) {
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                return internationalReportService.listByCurrency(sqlConnection, parameter).flatMap(internationalReports -> {
                    InternationalReportConvert report = new InternationalReportConvert();
                    report.setItems(internationalReports);
                    int totalTransCount = 0;
                    int totalRefundCount = 0;
                    int totalAuthorizeCount = 0;
                    int totalCaptureCount = 0;

                    double totalTransAmount = 0;
                    double totalRefundAmount = 0;
                    double totalAuthorizeAmount = 0;
                    double totalCaptureAmount = 0;

                    for (InternationalReport reportData : internationalReports) {
                        totalTransCount += reportData.getTransaction_count();
                        totalRefundCount += reportData.getRefund_count();
                        totalAuthorizeCount += reportData.getAuthorize_count();
                        totalCaptureCount += reportData.getCapture_count();
                        totalTransAmount += reportData.getTransaction_total();
                        totalRefundAmount += reportData.getRefund_total();
                        totalAuthorizeAmount += reportData.getAuthorize_total();
                        totalCaptureAmount += reportData.getCapture_total();
                    }
                    report.setTotal_transaction_count(totalTransCount);
                    report.setTotal_transaction_total(totalTransAmount);
                    report.setTotal_authorize_count(totalAuthorizeCount);
                    report.setTotal_capture_count(totalCaptureCount);
                    report.setTotal_refund_count(totalRefundCount);
                    report.setTotal_refund_total(totalRefundAmount);
                    report.setTotal_authorize_total(totalAuthorizeAmount);
                    report.setTotal_capture_total(totalCaptureAmount);
                    if (internationalReports.size() > 0) {
                        report.setCurrency(internationalReports.get(0).getCurrency());
                    }
                    returnMap.put(currency, report);
                    if (finalIndex >= listCurrency.size() - 1) {
                        return Observable.just(returnMap);
                    } else {
                        return getListReportByCurrency(sqlConnection, listCurrency, returnMap, parameter, finalIndex + 1);
                    }
                });
            });
        });
    }


    private InternationalReportService internationalReportService;

    private MerchantService merchantService;
}
