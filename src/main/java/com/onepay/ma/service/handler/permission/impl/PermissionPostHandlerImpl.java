package com.onepay.ma.service.handler.permission.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.PostPermissionParam;
import com.onepay.ma.service.handler.permission.PermissionPostHandler;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/12/16.
 */
public class PermissionPostHandlerImpl implements PermissionPostHandler {

    public PermissionPostHandlerImpl(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Override
    public void handle(RoutingContext rc) {

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        String json = rc.getBodyAsString();
        PostPermissionParam permissionParam = gson.fromJson(json, PostPermissionParam.class);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    connectionBackUp = connBackUp;
                    return connectionBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                        return permissionService.insert(connectionBackUp, permissionParam);
                    });
                }).subscribe(permission -> {
            if(connectionBackUp != null){
                connectionBackUp.commitObservable();
            }
            rc.put(ParamsPool.HANDLER_DATA_RESULT, permission);
            rc.next();

        }, throwable -> {
            if(connectionBackUp != null){
                connectionBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });


    }


    private SQLConnection connectionBackUp;

    private final Gson gson = new Gson();
    private PermissionService permissionService;
}
