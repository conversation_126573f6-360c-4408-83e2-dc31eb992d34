package com.onepay.ma.service.handler.promotion;

import com.onepay.ma.service.handler.promotion.impl.PromotionPutHandlerImpl;
import com.onepay.ma.service.service.*;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionPutHandler extends Handler<RoutingContext> {
    static PromotionPutHandlerImpl create(PromotionService promotionService, MerchantService merchantService, PromotionDiscountService promotionDiscountService, PromotionMerchantService promotionMerchantService, PromotionRuleService promotionRuleService, PromotionRuleParamService promotionRuleParamService, ApprovalService approvalService){
        return new PromotionPutHandlerImpl(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, promotionRuleParamService, approvalService);

    }
}
