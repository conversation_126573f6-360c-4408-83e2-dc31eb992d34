package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostInternationalTransactionHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/15/16.
 */
public interface FilePostInternationalTransactionHandler extends Handler<RoutingContext>  {
    static FilePostInternationalTransactionHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue){
        return new FilePostInternationalTransactionHandlerImpl(internationalTransactionService, merchantService, serverConfig, downloadFastIn, downloadFastOut, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue);

    }
}
