package com.onepay.ma.service.handler.role;

import com.onepay.ma.service.handler.role.impl.RoleDeleteHandlerImpl;
import com.onepay.ma.service.service.RoleService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface RoleDeleteHandler extends Handler<RoutingContext> {
    static RoleDeleteHandlerImpl create(RoleService roleService){
        return new RoleDeleteHandlerImpl(roleService);
    }
}
