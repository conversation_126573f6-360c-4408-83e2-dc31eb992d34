package com.onepay.ma.service.handler.role;


import com.onepay.ma.service.handler.role.impl.RolePutHandlerImpl;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/12/16.
 */
public interface RolePutHandler extends Handler<RoutingContext> {
    static RolePutHandlerImpl create(RoleService roleService, CacheGuava cacheGuava){
        return new RolePutHandlerImpl(roleService, cacheGuava);
    }
}
