package com.onepay.ma.service.handler.report.general;

import com.onepay.ma.service.handler.report.general.impl.GeneralReportDownloadHandler;
import com.onepay.ma.service.handler.report.general.impl.GeneralReportGetHandler;
import io.vertx.core.Handler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GeneralReportHandler {
    @Autowired
    private GeneralReportGetHandler generalReportGetHandler;

    @Autowired
    private GeneralReportDownloadHandler generalReportDownloadHandler;

    public Handler getGeneralReport() {
        return generalReportGetHandler;
    }

    public Handler downloadGeneralReport() {
        return generalReportDownloadHandler;
    }
}
