package com.onepay.ma.service.handler.transaction.international;

import com.onepay.ma.service.handler.transaction.international.impl.InternationalCaptureHistoryGetHandlerImpl;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalCaptureHistoryGetHandler extends Handler<RoutingContext>  {
    static InternationalCaptureHistoryGetHandlerImpl create(InternationalTransactionService internationalTransactionService, MerchantService merchantService, UserService userService){
        return new InternationalCaptureHistoryGetHandlerImpl(internationalTransactionService, merchantService, userService);

    }
}
