package com.onepay.ma.service.handler.report.statistics.impl;

import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.report.statistics.StatisticsReportDetailGetHandler;
import com.onepay.ma.service.models.StatisticsReport;
import com.onepay.ma.service.service.StatisticsReportService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 12/6/18.
 */
public class StatisticsReportDetailGetHandlerImpl implements StatisticsReportDetailGetHandler {

    public StatisticsReportDetailGetHandlerImpl(StatisticsReportService statisticsReportService) {
        this.statisticsReportService = statisticsReportService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[Statistics Detail REPORT GET] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnerecon = rc.get(ParamsPool.ONERECON_DATASOURCE_NAME);

        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
        String toDate = rc.request().getParam(ParamsPool.TO_DATE);
        try {
            df.parse(fromDate);
            df.parse(toDate);
        } catch (Exception e) {

            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        int interval = (rc.request().getParam(ParamsPool.INTERVAL) == null||rc.request().getParam(ParamsPool.INTERVAL) =="") ?   1 : Integer.valueOf(rc.request().getParam(ParamsPool.INTERVAL));
        String bankId = rc.request().getParam(ParamsPool.ACQUIRER_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.ACQUIRER_ID));
        String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));
        String merchantName = rc.request().getParam(ParamsPool.MERCHANT_NAME) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_NAME));
        String transType = rc.request().getParam(ParamsPool.TRANSACTION_TYPE) == null ? StringPool.BLANK :  String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_TYPE));


       // String reportType = rc.request().getParam(ParamsPool.REPORT_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.REPORT_TYPE));

        StatisticsReportParameter statisticsReportParameter = new StatisticsReportParameter();
        statisticsReportParameter.setAcquirerId(bankId);
        statisticsReportParameter.setFromDate(fromDate);
        statisticsReportParameter.setToDate(toDate);
        statisticsReportParameter.setMerchantId(merchantId);
        statisticsReportParameter.setMerchantName(merchantName);
        statisticsReportParameter.setInterval(interval);
        statisticsReportParameter.setTransType(transType);
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnerecon), f -> f.dispose()).flatMap(connOnerecon -> {
    //                                                        if (!reportType.equals("detail")) {
                                return statisticsReportService.listDetail(connOnerecon, statisticsReportParameter).map(statisticsReports -> {
                                    Map returnReport = new HashMap();

//                                    List<StatisticsReportConvert> statisticsReportConverts = convertReport(statisticsReports);
                                    //if (StatisticsReportConverts.size() > 0) {
                                    double totalReceiable = 0;
                                    double totalPayable = 0;
                                    double totalDiff = 0;
//                                    for (StatisticsReportConvert items : statisticsReportConverts) {
                                    for (StatisticsReport items : statisticsReports) {
                                        totalReceiable += items.getAmountIss();
                                        totalPayable += items.getAmountAcq();
                                    }
                                    totalDiff = totalReceiable-totalDiff;
                                   // returnReport.put("reports", convertReport(statisticsReports));
                                    returnReport.put("reports", statisticsReports);
                                    returnReport.put("totalReceiable", totalReceiable);
                                    returnReport.put("totalPayable", totalPayable);
                                    returnReport.put("totalDiff", totalDiff);
                                    //}
                                    return returnReport;
                                });
    //                                                        } else {
    //                                                            return StatisticsReportService.listDetail(connReadOnly, statisticsReportParameter).map(statisticsReports -> {
    //                                                                Map returnReport = new HashMap();
    //
    //                                                                List<StatisticsReportConvert> statisticsReportConverts = convertReport(statisticsReports);
    //                                                                //if (StatisticsReportConverts.size() > 0) {
    //                                                                int totalTransCount = 0;
    //                                                                int totalRefundCount = 0;
    //                                                                double totalTransAmount = 0;
    //                                                                double totalRefundAmount = 0;
    //                                                                for (StatisticsReportConvert items : statisticsReportConverts) {
    //                                                                    totalTransCount += items.getTransaction_count();
    //                                                                    totalRefundCount += items.getRefund_count();
    //                                                                    totalTransAmount += items.getTransaction_total();
    //                                                                    totalRefundAmount += items.getRefund_total();
    //                                                                }
    //                                                                returnReport.put("reports", convertReport(statisticsReports));
    //                                                                returnReport.put(ParamsPool.TOTAL_REFUND_TOTAL, totalRefundAmount);
    //                                                                returnReport.put(ParamsPool.TOTAL_TRANSACTION_TOTAL, totalTransAmount);
    //                                                                returnReport.put(ParamsPool.TOTAL_REFUND_COUNT, totalRefundCount);
    //                                                                returnReport.put(ParamsPool.TOTAL_TRANSACTION_COUNT, totalTransCount);
    //                                                                //}
    //                                                                return returnReport;
    //                                                            });
    //                                                        }
            }).subscribe(returnReport -> {
            rc.put(ParamsPool.HANDLER_DATA_RESULT, returnReport);
            rc.next();
        }, throwable -> {
            rc.fail(throwable);
        });

    }


    private StatisticsReportService statisticsReportService;

    private static final Logger LOGGER = Logger.getLogger(StatisticsReportDetailGetHandler.class.getName());
}
