package com.onepay.ma.service.handler.common.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.common.ErrorHandler;
import com.onepay.ma.service.util.ParamsPool;
import com.zaxxer.hikari.HikariDataSource;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.metadata.HikariDataSourcePoolMetadata;


import javax.sql.DataSource;
import java.sql.SQLTransientConnectionException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/6/16.
 */
public class ErrorHandlerImpl implements ErrorHandler {

//    @Autowired
//    @Qualifier(value = "backUpDataSource")
//    private DataSource backUpDataSource;

    @Override
    public void handle(RoutingContext rc) {
//        SQLConnection connOnline = rc.get(ParamsPool.CONNECTION_ONLINE);
//        if(connOnline != null){
//            connOnline.close();
//        }
//        SQLConnection connReadOnly = rc.get(ParamsPool.CONNECTION_READONLY);
//        if(connReadOnly != null){
//            connReadOnly.close();
//        }
//
//        SQLConnection connBackUp = rc.get(ParamsPool.CONNECTION_BACKUP);
//        if(connBackUp != null){
//            connBackUp.close();
//        }
//
//        SQLConnection connCDR = rc.get(ParamsPool.CONNECTION_CDR);
//        if(connCDR != null){
//            connCDR.close();
//        }
//
//        SQLConnection connPromotion = rc.get(ParamsPool.CONNECTION_PROMOTION);
//        if(connPromotion != null){
//            connPromotion.close();
//        }
//        if(rc.response().closed()) return;
        Throwable throwable = rc.failure();

        LOGGER.log(Level.SEVERE, "[ERROR SERVER] ", rc.failure());
        if((throwable instanceof SQLTransientConnectionException)) {

            LOGGER.log(Level.SEVERE, "[ERROR SQL] ", rc.failure());

            LOGGER.log(Level.SEVERE, "******************************** Connection Status ********************************");
            HikariDataSourcePoolMetadata backUpMetadata = rc.get(ParamsPool.BACK_UP_METADATA);
            HikariDataSourcePoolMetadata onlineMetadata = rc.get(ParamsPool.ONLINE_METADATA);
            HikariDataSourcePoolMetadata readOnlyMetadata = rc.get(ParamsPool.READ_ONLY_METADATA);
            HikariDataSourcePoolMetadata prPoolMetadata = rc.get(ParamsPool.PROMOTION_METADATA);
            LOGGER.log(Level.SEVERE, "[1114 Connection Status] active: "+ backUpMetadata.getActive() + ", min: "+ backUpMetadata.getMin()
                    + ", max: "+ backUpMetadata.getMax()+ ", usage: "+ backUpMetadata.getUsage()+ ", validation: "+ backUpMetadata.getValidationQuery());
            LOGGER.log(Level.SEVERE, "[1112 Connection Status] active: "+ onlineMetadata.getActive() + ", min: "+ onlineMetadata.getMin()
                    + ", max: "+ onlineMetadata.getMax()+ ", usage: "+ onlineMetadata.getUsage()+ ", validation: "+ onlineMetadata.getValidationQuery());
            LOGGER.log(Level.SEVERE, "[1113 Connection Status] active: "+ readOnlyMetadata.getActive() + ", min: "+ readOnlyMetadata.getMin()
                    + ", max: "+ readOnlyMetadata.getMax()+ ", usage: "+ readOnlyMetadata.getUsage()+ ", validation: "+ readOnlyMetadata.getValidationQuery());
            LOGGER.log(Level.SEVERE, "[PR Connection Status] active: "+ prPoolMetadata.getActive() + ", min: "+ prPoolMetadata.getMin()
                    + ", max: "+ prPoolMetadata.getMax()+ ", usage: "+ prPoolMetadata.getUsage()+ ", validation: "+ prPoolMetadata.getValidationQuery());
            LOGGER.log(Level.INFO, " ********************************  [END  Connection Status] ******************************** ");
        }else if(!(throwable instanceof ErrorException)) {
            LOGGER.log(Level.WARNING, "[ERROR SERVER] ", rc.failure());
            throwable = IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.SEVERE, "[RESPONSE] => " + ((ErrorException) throwable).getStatusCode());

        String json = ((ErrorException) throwable).toJson();
        LOGGER.log(Level.SEVERE, "[RESPONSE] => " + json);
        LOGGER.log(Level.INFO, " ********************************  [ DONE ] ******************************** ");
        rc.response().setStatusCode(((ErrorException) throwable).getStatusCode()).end(new JsonObject(json).encode());

    }

    private final static Gson gson = new Gson();
    private final static Logger LOGGER = Logger.getLogger(ErrorHandlerImpl.class.getName());


}
