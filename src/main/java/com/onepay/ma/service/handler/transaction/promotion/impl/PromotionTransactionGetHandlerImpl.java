package com.onepay.ma.service.handler.transaction.promotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.transaction.promotion.PromotionTransactionGetHandler;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.PromotionTransactionParameter;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.PromotionTransactionService;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/30/16.
 */
public class PromotionTransactionGetHandlerImpl implements PromotionTransactionGetHandler {

    public PromotionTransactionGetHandlerImpl(PromotionTransactionService promotionTransactionService, MerchantService merchantService) {
        this.promotionTransactionService = promotionTransactionService;
        this.merchantService = merchantService;
    }



    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        if(userId == null){

            LOGGER.log(Level.SEVERE, "[ PROMOTION TRANSACTION GET ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String transactionId = rc.request().getParam("id");

        JDBCClient clientPR = rc.get(ParamsPool.PROMOTION_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        if(transactionId == null) {
            DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
            String fromDate = rc.request().getParam(ParamsPool.FROM_DATE);
            String toDate = rc.request().getParam(ParamsPool.TO_DATE);

            Date oFromDate;
            Date oToDate;
            try {
                oFromDate = df.parse(rc.request().getParam(ParamsPool.FROM_DATE));
                oToDate = df.parse(rc.request().getParam(ParamsPool.TO_DATE));

            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION TRANSACTION GET ] => DATE PARSE ERROR", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int months  = FunctionUtil.monthsBetween(oFromDate, oToDate);
            if(months > 6){
                throw IErrors.SEARCH_TOO_LARGE_ERROR;
            }
            String keywords = rc.request().getParam(ParamsPool.KEY_WORDS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.KEY_WORDS));

            String merchantTransactionRef = rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_TRANSACTION_REF));

            String cardNumber = rc.request().getParam(ParamsPool.CARD_NUMBER) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_NUMBER));

            String promotionSid = rc.request().getParam(ParamsPool.PROMOTION_SID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PROMOTION_SID));
            String promotionCode = rc.request().getParam(ParamsPool.PROMOTION_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PROMOTION_CODE));


            String orderInfo = rc.request().getParam(ParamsPool.ORDER_INFO) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.ORDER_INFO));

            String cardType = rc.request().getParam(ParamsPool.CARD_TYPE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CARD_TYPE));

            String currency = rc.request().getParam(ParamsPool.CURRENCY_CODE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.CURRENCY_CODE));

            String merchantId = rc.request().getParam(ParamsPool.MERCHANT_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.MERCHANT_ID));

            String transactionIdValue = rc.request().getParam(ParamsPool.TRANSACTION_ID) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.TRANSACTION_ID));

            String status = rc.request().getParam(ParamsPool.STATUS) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.STATUS));

            String paygate = rc.request().getParam(ParamsPool.PAYGATE) == null ? StringPool.BLANK : String.valueOf(rc.request().getParam(ParamsPool.PAYGATE));

            int page = rc.request().getParam(ParamsPool.PAGE) == null ? 0 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE));
            int pageSize = rc.request().getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.valueOf(rc.request().getParam(ParamsPool.PAGE_SIZE));

            PromotionTransactionParameter parameter = new PromotionTransactionParameter();
            parameter.setPage(page);
            parameter.setPageSize(pageSize);
            parameter.setFromDate(fromDate);
            parameter.setToDate(toDate);
            parameter.setCardNumber(cardNumber);
            parameter.setOrderInfo(orderInfo);
            parameter.setTransactionId(transactionIdValue);
            parameter.setCardType(cardType);
            parameter.setCurrency(currency);
            parameter.setPromotionSid(promotionSid);
            parameter.setKeywords(keywords);
            parameter.setPromotion_code(promotionCode);
            parameter.setTransactionReference(merchantTransactionRef);
            parameter.setPaygate(paygate);
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPR -> {
                                //get online connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                        .flatMap(connOnline -> {
                                            //get back up connection
                                            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                    .flatMap(connBackUp -> {
                                                        return merchantService.list(connOnline, connBackUp, merchantId, userId, "promotion").flatMap(merchants -> {
                                                            List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                            parameter.setMerchantId(String.join(",", merchantIdList));
                                                            return promotionTransactionService.list(connPR, parameter);
                                                        });
                                                    });
                                        });
                            }).subscribe(transactions -> {
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, transactions);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });



        }else{
            Observable.using(SQLConnectionFactory::new, f -> f.create(clientPR), f-> f.dispose())
                    .flatMap(connPR -> {
                        return promotionTransactionService.get(connPR, transactionId);
                    }).subscribe(transaction -> {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, transaction);
                rc.next();
            }, throwable -> {
                rc.fail(throwable);
            });
        }
    }

    private PromotionTransactionService promotionTransactionService;

    private MerchantService merchantService;

    private static final Logger LOGGER = Logger.getLogger(PromotionTransactionGetHandler.class.getName());
}
