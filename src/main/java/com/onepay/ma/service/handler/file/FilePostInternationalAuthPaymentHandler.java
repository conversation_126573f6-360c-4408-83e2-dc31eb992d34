package com.onepay.ma.service.handler.file;

import com.onepay.ma.service.handler.file.impl.FilePostInternationalAuthPaymentHandlerImpl;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalAuthPaymentService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import io.vertx.core.Handler;
import io.vertx.rxjava.ext.web.RoutingContext;

import javax.jms.Queue;


/**
 * Created by tuydv on  5/8/18.
 */
public interface FilePostInternationalAuthPaymentHandler extends Handler<RoutingContext>  {
    static FilePostInternationalAuthPaymentHandlerImpl create(InternationalAuthPaymentService internationalAuthPaymentService, MerchantService merchantService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, CacheGuava cacheGuava, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue, InternationalTransactionService internationalTransactionService){
        return new FilePostInternationalAuthPaymentHandlerImpl(internationalAuthPaymentService, merchantService, serverConfig, downloadFastIn, downloadFastOut, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue,internationalTransactionService);

    }
}
