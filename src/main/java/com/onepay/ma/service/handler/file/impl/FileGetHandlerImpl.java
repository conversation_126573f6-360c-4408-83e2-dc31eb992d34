package com.onepay.ma.service.handler.file.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostCdrHandler;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import io.vertx.rxjava.core.file.FileSystem;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FileGetHandlerImpl implements FilePostCdrHandler {

    public FileGetHandlerImpl(ServerConfig serverConfig, FileService fileService) {
        this.serverConfig = serverConfig;
        this.fileService = fileService;
    }

    private final static Logger logger = Logger.getLogger(FileGetHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            logger.log(Level.SEVERE, "[ FILE DOWNLOAD ] => USER ID NOT FOUND");
            throw  IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();
        String fileHash = request.getParam(ParamsPool.FILE_HASH_NAME_DOWNLOAD);
        logger.info(" START GET FILE DOWNLOAD 1 " + new Date());
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return fileService.get(connBackUp, fileHash, userId).flatMap(fileDownload -> {

                        if (fileDownload == null) {
                            throw IErrors.RESOURCE_NOT_FOUND;
                        }
                        return fileService.updateExpTime(connBackUp, fileHash).map(integer -> {
                            return fileDownload;
                        });
                    });
                }).subscribe(fileDownload -> {
                    Path requestPath = FileSystems.getDefault().getPath(serverConfig.getExportLocation() + StringPool.FORWARD_SLASH + fileHash, "/").normalize();
                    logger.info("download " + requestPath);

                    FileSystem fs = rc.vertx().fileSystem();
                    fs.existsObservable(requestPath.toString()).subscribe(aBoolean -> {
                        logger.info("download1.5 " + aBoolean);
                        if (aBoolean) {
                            Map data = new HashMap();
                            data.put(ParamsPool.FILE_HASH_NAME, fileHash);
                            data.put(ParamsPool.PATH_FILE, requestPath.toString());
                            data.put(ParamsPool.FILE_NAME, fileDownload.getFile_name());
                            data.put(ParamsPool.FILE_EXT, fileDownload.getExt());
                            try {
                                data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                            } catch (IOException e) {
                                rc.fail(e);
                            }
                            rc.put(ParamsPool.HANDLER_DATA_RESULT, data);
                            rc.put(ParamsPool.HANDLER_DATA_TYPE, "file");
                            logger.info(" START GET FILE DOWNLOAD 2 " + new Date() + " FILE NAME: " + fileDownload.getFile_name() + " FILE EXT: " + fileDownload.getExt()+" FILE SIZE " +data.get(ParamsPool.FILE_SIZE));
                            rc.next();
                        } else {
                            rc.fail(IErrors.RESOURCE_NOT_FOUND);
                        }
                        
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                });
    }

    private FileService fileService;

    private ServerConfig serverConfig;
}
