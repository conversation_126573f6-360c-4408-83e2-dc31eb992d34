package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostInternationalPaymentHandler;
import com.onepay.ma.service.handler.file.FilePostInternationalTransactionHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.FunctionUtil;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 01-Jun-17.
 */
public class FilePostInternationalPaymentHandlerImpl implements FilePostInternationalPaymentHandler {

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL PAYEMNT POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);


        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        InternationalAuthPaymentQuery mapBody =  gson.fromJson(body, InternationalAuthPaymentQuery.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL PAYEMNT POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
        String finalKeyWords = FunctionUtil.isBeginIsACard(mapBody.getKeywords()) && mapBody.getKeywords().contains("*") ? mapBody.getKeywords().replaceAll("\\*","0") : mapBody.getKeywords();
        String finalCardNumber = FunctionUtil.isBeginIsACard(mapBody.getCardNumber()) && mapBody.getCardNumber().contains("*") ? mapBody.getCardNumber().replaceAll("\\*","0") : mapBody.getCardNumber();

//        if(FunctionUtil.isCardData(finalKeyWords)){
//            rc.vertx().executeBlockingObservable(objectFuture -> {
//                objectFuture.complete(FunctionUtil.oneSMHmac(finalKeyWords, serverConfig));
//            }).subscribe(o -> {
//                mapBody.setKeywords(String.valueOf(o));
//            });
//            insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
//        }else {
//            if(FunctionUtil.isCardData(finalCardNumber)){
//                rc.vertx().executeBlockingObservable(objectFuture -> {
//                    objectFuture.complete(FunctionUtil.oneSMHmac(finalCardNumber, serverConfig));
//                }).subscribe(o -> {
//                    mapBody.setCard_number(String.valueOf(o));
//                });
//                insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
//            }else {
//                insertDowload(mapBody, requestData, request, userId, clientReadOnly, clientOnline, clientBackUp, rc);
//            }
//        }
//




    }

    private void insertDowload(InternationalTxnPostFile mapBody, Map requestData, HttpServerRequest request, String userId, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp, RoutingContext rc){
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;
                                            return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "international").flatMap(merchants -> {
                                                List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                InternationalTxnParameter parameter = new InternationalTxnParameter();
                                                parameter.setAcquirerId(mapBody.getAcquirer_id());
                                                parameter.setKeywords(mapBody.getKeywords());
                                                parameter.setAuthenticationState(mapBody.getAuthentication_state());
                                                parameter.setAuthorizationCode(mapBody.getAuthorization_code());
                                                parameter.setCardNumber(mapBody.getCard_number());
                                                parameter.setTransactionReference(mapBody.getMerchant_transaction_ref());
                                                parameter.setCardType(mapBody.getCard_type());
                                                parameter.setCurrency(mapBody.getCurrency());
                                                parameter.setFromDate(mapBody.getFrom_date());
                                                parameter.setToDate(mapBody.getTo_date());
                                                parameter.setMerchantId(String.join(",", merchantIdList));
                                                parameter.setOrderInfo(mapBody.getOrder_info());
                                                parameter.setTransactionId(mapBody.getTransaction_id());
                                                parameter.setAuthenticationType(mapBody.getAuthentication_type() == null ? StringPool.BLANK : mapBody.getAuthentication_type());
                                                parameter.setTransactionType(mapBody.getTransaction_type() == null ? StringPool.BLANK : mapBody.getTransaction_type());
                                                parameter.setStatus(mapBody.getStatus());
                                                Map data = new HashMap();
                                                return internationalTransactionService.getTotalDownload(connOnline, connReadOnly, parameter).flatMap(integer -> {
                                                    if (integer == 0) {
                                                        LOGGER.log(Level.SEVERE, "[ FILE INTERNATIONAL TRANSACTION POST ] => TOTAL NOT FOUND");
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }
                                                    String fileName = "international" + StringPool.UNDERLINE + "payment" + StringPool.UNDERLINE;
                                                    long date = new java.util.Date().getTime();
                                                    fileName += date;
                                                    String fileHashName = "";
                                                    data.put("parameter", parameter);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("international_payment");
                                                    if(integer <= serverConfig.getRowLevel()){
                                                        fileDownload.setExt("xls");
                                                    }else {
                                                        fileDownload.setExt("zip");
                                                    }
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connectBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                            });
                                        });
                            });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }
            InternationalTxnParameter parameter = (InternationalTxnParameter) map.get("parameter");
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<InternationalTxnParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //  fileDownload.setExt("zip");
                Message<InternationalTxnParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });

    }

    private InternationalTransactionService internationalTransactionService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private CacheGuava cacheGuava;

    private SQLConnection connectBackUp;

    private MerchantService merchantService;

    private final static Gson gson = new Gson();



    private static final Logger LOGGER = Logger.getLogger(FilePostInternationalPaymentHandlerImpl.class.getName());
}
