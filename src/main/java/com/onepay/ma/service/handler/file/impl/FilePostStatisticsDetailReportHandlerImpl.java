package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.file.FilePostStatisticsDetailReportHandler;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.StatisticsReportService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by huynguyen on 4/2/16.
 */
public class FilePostStatisticsDetailReportHandlerImpl implements FilePostStatisticsDetailReportHandler {

    public FilePostStatisticsDetailReportHandlerImpl(StatisticsReportService statisticsReportService, ServerConfig serverConfig, Queue downloadFastIn, Queue downloadFastOut, FileService fileService, Queue downloadSlowInQueue, Queue downloadSlowOutQueue) {
        this.statisticsReportService = statisticsReportService;
        this.serverConfig = serverConfig;
        this.downloadFastInQueue = downloadFastIn;
        this.downloadFastOutQueue = downloadFastOut;
        this.downloadSlowInQueue = downloadSlowInQueue;
        this.downloadSlowOutQueue = downloadSlowOutQueue;
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            throw IErrors.VALIDATION_ERROR;
        }
        JDBCClient clientOnerecon = rc.get(ParamsPool.ONERECON_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        StatisticsReportParameter mapBody =  gson.fromJson(body, StatisticsReportParameter.class);
        if(mapBody == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnerecon), f-> f.dispose())
                .flatMap(connOnerecon -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                            connectBackUp = connBackUp;

                                                Map data = new HashMap();
                                                return statisticsReportService.getTotalReportDetail(connOnerecon, mapBody).flatMap(integer -> {
                                                    if (integer==0) {
                                                        throw IErrors.RESOURCE_NOT_FOUND;
                                                    }
                                                    String fileName = "statistics-detail" + StringPool.UNDERLINE + "report" + StringPool.UNDERLINE;
                                                    long date = new java.util.Date().getTime();
                                                    fileName += date;
                                                    String fileHashName = "";
                                                    data.put("parameter", mapBody);
                                                    data.put("file_name", fileName);
                                                    data.put("row", integer);
                                                    try {
                                                        fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                    } catch (NoSuchAlgorithmException e) {
                                                        rc.fail(e);
                                                    } catch (UnsupportedEncodingException e) {
                                                        rc.fail(e);
                                                    }
                                                    requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                    requestData.put(ParamsPool.FILE_NAME, fileName);
                                                    FileDownload fileDownload = new FileDownload();
                                                    fileDownload.setUser(userId);
                                                    fileDownload.setFile_type("statistics_report");
                                                    String ext = "XLSX";
                                                    try{
                                                        ext =mapBody.getExportType();
                                                    }catch (Exception e){

                                                    }
                                                    fileDownload.setExt(ext);
                                                    fileDownload.setFile_name(fileName);
                                                    fileDownload.setFile_hash_name(fileHashName);
                                                    fileDownload.setConditions(gson.toJson(mapBody));
                                                    return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                        return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                            return data;
                                                        });
                                                    });
                                                });
                                        });
                }).subscribe(map -> {
            if(connectBackUp != null){
                connectBackUp.commitObservable();
            }

            StatisticsReportParameter parameter = (StatisticsReportParameter) map.get("parameter");
            int row = Integer.valueOf(map.get("row").toString());
            if (row <= serverConfig.getRowLevel()) {
                //fileDownload.setExt("csv");
                Message<StatisticsReportParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                QueueProducer.sendMessage(message);
            } else {
                //fileDownload.setExt("zip");
                Message<StatisticsReportParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                QueueProducer.sendMessage(message);
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
            rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
            rc.next();
        }, throwable -> {
            if(connectBackUp != null){
                connectBackUp.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    private StatisticsReportService statisticsReportService;

    private FileService fileService;

    private Queue downloadFastInQueue;

    private Queue downloadFastOutQueue;

    private Queue downloadSlowInQueue;

    private Queue downloadSlowOutQueue;

    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    private final static Gson gson = new Gson();
}
