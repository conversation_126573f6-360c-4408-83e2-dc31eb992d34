package com.onepay.ma.service.handler.file.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.Convert;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.queue.message.Message;
import com.onepay.ma.service.queue.message.MessagePriority;
import com.onepay.ma.service.queue.producer.QueueProducer;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.domestic.SSDomesticTransactionService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.Handler;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import rx.Observable;

import javax.jms.Queue;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/2/16.
 */
@Component
public class FilePostSSDomesticTransactionHandler implements Handler<RoutingContext> {


    @Override
    public void handle(RoutingContext rc) {
        String userId = rc.get(ParamsPool.X_USER_ID);
        String ip = rc.get(ParamsPool.X_REAL_IP);
        if(userId == null){
            LOGGER.log(Level.SEVERE, "[ FILE DOMESTIC TRANSACTION POST ] => USER ID EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }

        JDBCClient clientReadOnly = rc.get(ParamsPool.READ_ONLY_DATASOURCE_NAME);
        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
        final HttpServerRequest request = rc.request();

        String body = rc.getBodyAsString();
        DomesticTxnPostFile mapBody =  gson.fromJson(body, DomesticTxnPostFile.class);
        if(mapBody == null) {
            LOGGER.log(Level.SEVERE, "[ FILE DOMESTIC TRANSACTION POST ] => BODY EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = rc.get(ParamsPool.X_REQUEST_ID);
        Map requestData = new HashMap<>();
        requestData.put(ParamsPool.X_USER_ID, userId);
        requestData.put(ParamsPool.X_REQUEST_ID, requestId);
        requestData.put(ParamsPool.X_REAL_IP, ip);
         Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                .flatMap(connReadOnly -> {
                    //get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                //get back up connection
                                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackUp -> {
                                                connectBackUp = connBackUp;
                                                return  merchantService.list(connOnline, connBackUp, mapBody.getMerchant_id(), userId, "domestic").flatMap(merchants -> {
                                                    List<String> merchantIdList = merchants.stream().map(Merchant::getMerchant_id).collect(Collectors.toList());
                                                    DomesticTxnParameter parameter = new DomesticTxnParameter();
                                                    parameter.setKeywords(mapBody.getKeywords());
                                                    parameter.setBankId(mapBody.getBank_id().replace("|",","));
                                                    parameter.setCardNumber(mapBody.getCard_number());
                                                    parameter.setFromDate(mapBody.getFrom_date());
                                                    parameter.setToDate(mapBody.getTo_date());
                                                    parameter.setMerchantId(String.join(",", merchantIdList));
                                                    parameter.setOrderInfo(mapBody.getOrder_info());
                                                    parameter.setTransactionId(mapBody.getTransaction_id());
                                                    parameter.setStatus(mapBody.getStatus());
                                                    parameter.setMerchantTransactionRef(mapBody.getMerchant_transaction_ref());
                                                    parameter.setCustomer_email(mapBody.getCustomer_email() == null ? "" : mapBody.getCustomer_email());
                                                    parameter.setCustomer_mobile(mapBody.getCustomer_mobile() == null ? "" : mapBody.getCustomer_mobile());
                                                    parameter.setMerchant_website(mapBody.getMerchant_website() == null ? "" : mapBody.getMerchant_website());
                                                    parameter.setFraud_check(mapBody.getFraud_check() == null ? "" : mapBody.getFraud_check());
                                                    parameter.setTarget(mapBody.getTarget() == null ? "" : mapBody.getTarget());
                                                    parameter.setVersion(mapBody.getVersion() == null ? "" : mapBody.getVersion());
                                                    parameter.setOrder_status(mapBody.getOrder_status() == null ? "" : mapBody.getOrder_status());

                                                    Map data = new HashMap();
                                                    return SSDomesticTransactionService.getTotalDownload(connOnline, connReadOnly, parameter).flatMap(integer -> {
                                                        if (integer == 0) {
                                                            LOGGER.log(Level.SEVERE, "[ FILE DOMESTIC TRANSACTION POST ] => TOTAL NOT FOUND");
                                                            throw IErrors.RESOURCE_NOT_FOUND;
                                                        }

                                                        if (integer > serverConfig.getRowLimit() ) {
                                                            throw IErrors.PASS_EXCEL_LIMIT;
                                                        }
                                                        String fileName = "domestic" + StringPool.UNDERLINE + "transaction" + StringPool.UNDERLINE;
                                                        long date = new java.util.Date().getTime();
                                                        fileName += date;
                                                        String fileHashName = "";
                                                        data.put("parameter", parameter);
                                                        data.put("file_name", fileName);
                                                        data.put("row", integer);
                                                        try {
                                                            fileHashName = Convert.hash(fileName + StringPool.UNDERLINE + userId + date);
                                                        } catch (NoSuchAlgorithmException e) {
                                                            rc.fail(e);
                                                        } catch (UnsupportedEncodingException e) {
                                                            rc.fail(e);
                                                        }
                                                        requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                                        requestData.put(ParamsPool.FILE_NAME, fileName);
                                                        FileDownload fileDownload = new FileDownload();
                                                        fileDownload.setUser(userId);
                                                        fileDownload.setFile_type("domestic_transaction");
                                                        if(integer <= serverConfig.getRowLevel()){
                                                            fileDownload.setExt("xls");
                                                        }else {
                                                            fileDownload.setExt("zip");
                                                        }
                                                        fileDownload.setFile_name(fileName);
                                                        fileDownload.setFile_hash_name(fileHashName);
                                                        fileDownload.setConditions(gson.toJson(mapBody));
                                                        return connectBackUp.setAutoCommitObservable(false).flatMap(aVoid -> {
                                                            return fileService.insert(connBackUp, fileDownload).map(fileDownload1 -> {
                                                                return data;
                                                            });
                                                        });
                                                    });
                                                });
                                        });
                                    });
                        }).subscribe(map -> {
                         if(connectBackUp != null){
                             connectBackUp.commitObservable();
                         }
                         DomesticTxnParameter parameter = (DomesticTxnParameter) map.get("parameter");
                         int row = Integer.valueOf(map.get("row").toString());
                         if (row <= serverConfig.getRowLevel()) {
                             //fileDownload.setExt("csv");
                             Message<DomesticTxnParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadFastOutQueue, downloadFastInQueue);
                             QueueProducer.sendMessage(message);
                         } else {
                             //  fileDownload.setExt("zip");
                             Message<DomesticTxnParameter> message = new Message<>(parameter, requestData, MessagePriority.MEDIUM_PRIORITY, request.path(), downloadSlowOutQueue, downloadSlowInQueue);
                             QueueProducer.sendMessage(message);
                         }

                         rc.put(ParamsPool.HANDLER_DATA_CODE, 202);
                         rc.put(ParamsPool.HANDLER_DATA_RESULT, map);
                         rc.next();
         }, throwable -> {
             if(connectBackUp != null){
                 connectBackUp.rollbackObservable();
             }
             rc.fail(throwable);
         });


    }

    @Autowired
    private FileService fileService;

    @Autowired
    @Qualifier(value = "downloadFastQueueIn")
    private Queue downloadFastInQueue;

    @Autowired
    @Qualifier(value = "downloadFastQueueOut")
    private Queue downloadFastOutQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueIn")
    private Queue downloadSlowInQueue;

    @Autowired
    @Qualifier(value = "downloadSlowQueueOut")
    private Queue downloadSlowOutQueue;

    @Autowired
    private ServerConfig serverConfig;

    private SQLConnection connectBackUp;

    @Autowired
    private MerchantService merchantService;

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(FilePostSSDomesticTransactionHandler.class.getName());
}
