package com.onepay.ma.service.handler.common.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.onepay.ma.service.models.installment.InstallmentBank;
import com.onepay.ma.service.models.notification.MpayNotificationGroup;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.handler.common.ResponseHandler;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.financial.FinancialTransaction;
import com.onepay.ma.service.models.financial.SamsungFinancialTransaction;
import com.onepay.ma.service.models.mpay.*;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReport;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.models.notification.MpayNotification;
import com.onepay.ma.service.models.notification.MpayvnNotificationMsg;
import com.onepay.ma.service.models.onebill.OnebillClient;
import com.onepay.ma.service.models.onebill.OnebillTransaction;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationReportConvets;
import com.onepay.ma.service.models.report.GeneralReport;
import com.onepay.ma.service.models.shop.Store;
import com.onepay.ma.service.models.user.UserProfile;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.GenericAdapter;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.file.OpenOptions;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.file.FileSystem;
import io.vertx.rxjava.core.streams.Pump;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.boot.autoconfigure.jdbc.metadata.HikariDataSourcePoolMetadata;
import rx.Observable;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/6/16.
 */

public class ResponseHandlerImpl implements ResponseHandler {

    public ResponseHandlerImpl(FileService fileService) {
        this.fileService = fileService;
    }

    @Override
    public void handle(RoutingContext rc) {
//        if(rc.response().closed()) {
//            closeConnection(rc);
//            return;
//        };
        Object dataResult = rc.get(ParamsPool.HANDLER_DATA_RESULT);
        Object dataCode = rc.get(ParamsPool.HANDLER_DATA_CODE);
        Object dataType = rc.get(ParamsPool.HANDLER_DATA_TYPE);
        if (dataType != null && dataType.equals("file")) {            
            FileSystem fs = rc.vertx().fileSystem();
            Map result = (Map) dataResult;
            Path requestPath = FileSystems.getDefault().getPath(String.valueOf(result.get(ParamsPool.PATH_FILE))).normalize();
            LOGGER.info("download 2 " + requestPath.toAbsolutePath().toString());
            fs.exists(requestPath.toString(), (isExist) -> {
                LOGGER.info("isExist " + isExist.result());
                if (isExist.result()) {
                    String extFile = result.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(result.get(ParamsPool.FILE_EXT));
                    String fileOrg = result.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(result.get(ParamsPool.FILE_NAME));
                    String contentType = "application/octet-stream";
                    if (extFile.equals("zip")) {
                        contentType = "application/zip";
                    }
                    rc.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                    rc.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);
                    rc.response().putHeader(String.valueOf(HttpHeaders.CONTENT_LENGTH), String.valueOf(result.get(ParamsPool.FILE_SIZE)));
                    rc.response().setChunked(true);


                    rc.response().sendFile(requestPath.toString(), (r) -> {
                        LOGGER.info("sendFile " + r.succeeded());
                        if (r.succeeded()) {
                            try {
                                Files.deleteIfExists(requestPath);
                            } catch (IOException e) {
                                LOGGER.log(Level.SEVERE, "Remove File Error : ", e);
                            }

                            JDBCClient jdbcClient = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);
                            Observable.using(SQLConnectionFactory::new, f -> f.create(jdbcClient), f -> f.dispose())
                                    .subscribe(connBackUp -> {
                                        fileService.updateStatus(connBackUp, "downloaded", String.valueOf(result.get(ParamsPool.FILE_HASH_NAME)), Long.valueOf(result.get(ParamsPool.FILE_SIZE).toString())).subscribe(fileDownload -> {
                                            LOGGER.log(Level.INFO, "[ DEBUG ] => update file status => delete");
                                            if (!rc.response().closed()) {
                                                rc.response().end();
                                            }
                                        }, throwable -> {
                                            LOGGER.log(Level.INFO, "[ DEBUG ] => udpate file status fail", throwable);
                                            if (!rc.response().closed()) {
                                                rc.response().end();
                                            }
                                        });
                                    }, throwable -> {
                                        LOGGER.log(Level.INFO, "[ DEBUG ] => response file fail: ", throwable);
                                        rc.fail(throwable);
                                    });
                        } else {
                                
                            LOGGER.log(Level.INFO, "[ DEBUG ] => send file fail: ", r.cause());
                            throw IErrors.RESOURCE_NOT_FOUND;
                        }
                    });
                } else {

                    throw IErrors.RESOURCE_NOT_FOUND;
                }

            });
        } else {
            closeConnection(rc);
            if (dataResult == null && dataCode == null) {
                rc.fail(IErrors.RESOURCE_NOT_FOUND);
            } else {
                String json = gson.toJson(dataResult);
                boolean showLog = (rc.get(ParamsPool.HANDLER_LOG_RESULT) == null || (boolean)rc.get(ParamsPool.HANDLER_LOG_RESULT) == false);
                if(showLog) {
                    LOGGER.log(Level.INFO, "[ RESPONSE ] =>" + StringPool.SPACE + (dataCode == null ? 200 : Integer.parseInt(dataCode.toString())));
                    if (dataResult instanceof Transactions) {
                        Transactions transactions = new Gson().fromJson(json, Transactions.class);
                        LOGGER.log(Level.INFO, "[ RESPONSE ] => Total Size :" + transactions.getTotal_items());
                    } else if (dataResult instanceof Permissions) {
                        Permissions permissions = new Gson().fromJson(json, Permissions.class);
                        LOGGER.log(Level.INFO, "[ RESPONSE ] => Total Size :" + permissions.getTotal_items());
                    } else {
                        LOGGER.log(Level.INFO, "[ RESPONSE ] => " + Utils.mask(json));
                    }
                    LOGGER.log(Level.INFO, " ********************************  [ DONE ] ******************************** ");
                }
                rc.response().setStatusCode(dataCode == null ? 200 : Integer.parseInt(dataCode.toString()))
                        .end(dataResult == null ? "" : new JsonObject(json).encode());

            }
        }

        LOGGER.log(Level.INFO, "******************************** Connection Status ********************************");
        HikariDataSourcePoolMetadata backUpMetadata = rc.get(ParamsPool.BACK_UP_METADATA);
        HikariDataSourcePoolMetadata onlineMetadata = rc.get(ParamsPool.ONLINE_METADATA);
        HikariDataSourcePoolMetadata readOnlyMetadata = rc.get(ParamsPool.READ_ONLY_METADATA);
        HikariDataSourcePoolMetadata prPoolMetadata = rc.get(ParamsPool.PROMOTION_METADATA);
        LOGGER.log(Level.INFO, "[1114 Connection Status] active: "+ backUpMetadata.getActive() + ", min: "+ backUpMetadata.getMin()
                + ", max: "+ backUpMetadata.getMax()+ ", usage: "+ backUpMetadata.getUsage()+ ", validation: "+ backUpMetadata.getValidationQuery());
        LOGGER.log(Level.INFO, "[1112 Connection Status] active: "+ onlineMetadata.getActive() + ", min: "+ onlineMetadata.getMin()
                + ", max: "+ onlineMetadata.getMax()+ ", usage: "+ onlineMetadata.getUsage()+ ", validation: "+ onlineMetadata.getValidationQuery());
        LOGGER.log(Level.INFO, "[1113 Connection Status] active: "+ readOnlyMetadata.getActive() + ", min: "+ readOnlyMetadata.getMin()
                + ", max: "+ readOnlyMetadata.getMax()+ ", usage: "+ readOnlyMetadata.getUsage()+ ", validation: "+ readOnlyMetadata.getValidationQuery());
        LOGGER.log(Level.INFO, "[PR Connection Status] active: "+ prPoolMetadata.getActive() + ", min: "+ prPoolMetadata.getMin()
                + ", max: "+ prPoolMetadata.getMax()+ ", usage: "+ prPoolMetadata.getUsage()+ ", validation: "+ prPoolMetadata.getValidationQuery());
        LOGGER.log(Level.INFO, " ********************************  [END Connection Status] ******************************** ");
    }

    /**
     * close connection
     * @param rc
     */
    private void closeConnection(RoutingContext rc){
        SQLConnection connOnline = rc.get(ParamsPool.CONNECTION_ONLINE);
        if(connOnline != null){
            connOnline.close();
        }
        SQLConnection connReadOnly = rc.get(ParamsPool.CONNECTION_READONLY);
        if(connReadOnly != null){
            connReadOnly.close();
        }

        SQLConnection connBackUp = rc.get(ParamsPool.CONNECTION_BACKUP);
        if(connBackUp != null){
            connBackUp.close();
        }

        SQLConnection connCDR = rc.get(ParamsPool.CONNECTION_CDR);
        if(connCDR != null){
            connCDR.close();
        }

        SQLConnection connPromotion = rc.get(ParamsPool.CONNECTION_PROMOTION);
        if(connPromotion != null){
            connPromotion.close();
        }

    }


    private final static Logger LOGGER = Logger.getLogger(ResponseHandlerImpl.class.getName());
    private FileService fileService;

    public final static  Gson gson = new GsonBuilder()
            .registerTypeAdapter(UserConfigDto.class, new GenericAdapter())
            .registerTypeAdapter(PaymentMpayReport.class, new GenericAdapter())
            .registerTypeAdapter(InstallmentBank.class, new GenericAdapter())
            .registerTypeAdapter(MpayNotificationGroup.class, new GenericAdapter())
            .registerTypeAdapter(SamsungFinancialTransaction.class, new GenericAdapter())
            .registerTypeAdapter(SamsungDomesticTransaction.class, new GenericAdapter())
            .registerTypeAdapter(SamsungInternationalTransaction.class, new GenericAdapter())
            .registerTypeAdapter(OnebillClient.class, new GenericAdapter())
            .registerTypeAdapter(OnebillTransactionHistory.class, new GenericAdapter())
            .registerTypeAdapter(OnebillTransaction.class, new GenericAdapter())
            .registerTypeAdapter(GeneralReport.class, new GenericAdapter())
            .registerTypeAdapter(ReconciliationReportConvets.class, new GenericAdapter())
            .registerTypeAdapter(Reconciliation.class, new GenericAdapter())
            .registerTypeAdapter(MpayRefund.class, new GenericAdapter())
            .registerTypeAdapter(MpayReportConvert.class, new GenericAdapter())
            .registerTypeAdapter(MpayReport.class, new GenericAdapter())
            .registerTypeAdapter(FinancialTransaction.class, new GenericAdapter())
            .registerTypeAdapter(UserProfile.class, new GenericAdapter())
            .registerTypeAdapter(MpayvnNotificationMsg.class, new GenericAdapter())
            .registerTypeAdapter(MpayNotification.class, new GenericAdapter())
            .registerTypeAdapter(MpayPrReport.class, new GenericAdapter())
            .registerTypeAdapter(MpayPromotionTransaction.class, new GenericAdapter())
            .registerTypeAdapter(Store.class, new GenericAdapter())
            .registerTypeAdapter(NotificationConfig.class, new GenericAdapter())
            .registerTypeAdapter(MpayOrder.class, new GenericAdapter())
            .registerTypeAdapter(MpayTransactionHistory.class, new GenericAdapter())
            .registerTypeAdapter(MpayAmount.class, new GenericAdapter())
            .registerTypeAdapter(InternationalAuthPayment.class, new GenericAdapter())
            .registerTypeAdapter(PromotionCode.class, new GenericAdapter())
            .registerTypeAdapter(Instrument.class, new GenericAdapter())
            .registerTypeAdapter(MpayTransaction.class, new GenericAdapter())
            .registerTypeAdapter(Permission.class, new GenericAdapter())
            .registerTypeAdapter(User.class, new GenericAdapter())
            .registerTypeAdapter(UserSession.class, new GenericAdapter())
            .registerTypeAdapter(Approval.class, new GenericAdapter())
            .registerTypeAdapter(DomesticTransaction.class, new GenericAdapter())
            .registerTypeAdapter(Role.class, new GenericAdapter())
            .registerTypeAdapter(Terminal.class, new GenericAdapter())
            .registerTypeAdapter(Address.class, new GenericAdapter())
            .registerTypeAdapter(Location.class, new GenericAdapter())
            .registerTypeAdapter(DomesticTransaction.class, new GenericAdapter())
            .registerTypeAdapter(DomesticCard.class, new GenericAdapter())
            .registerTypeAdapter(DomesticAmount.class, new GenericAdapter())
            .registerTypeAdapter(CardDate.class, new GenericAdapter())
            .registerTypeAdapter(ShippingAddress.class, new GenericAdapter())
            .registerTypeAdapter(InternationalTransaction.class, new GenericAdapter())
            .registerTypeAdapter(InternationalCard.class, new GenericAdapter())
            .registerTypeAdapter(InternationalAmount.class, new GenericAdapter())
            .registerTypeAdapter(AuthenticationData.class, new GenericAdapter())
            .registerTypeAdapter(AvsData.class, new GenericAdapter())
            .registerTypeAdapter(MpayAcquirer.class, new GenericAdapter())
            .registerTypeAdapter(Acquirer.class, new GenericAdapter())
            .registerTypeAdapter(DomesticReportConvert.class, new GenericAdapter())
            .registerTypeAdapter(InternationalReport.class, new GenericAdapter())
            .registerTypeAdapter(InternationalReportConvert.class, new GenericAdapter())
            .registerTypeAdapter(InternationalRefund.class, new GenericAdapter())
            .registerTypeAdapter(RefundAmount.class, new GenericAdapter())
            .registerTypeAdapter(DomesticRefund.class, new GenericAdapter())
            .registerTypeAdapter(CompareCdr.class, new GenericAdapter())
            .registerTypeAdapter(CdrTransaction.class, new GenericAdapter())
            .registerTypeAdapter(Amount.class, new GenericAdapter())
            .registerTypeAdapter(PromotionCard.class, new GenericAdapter())
            .registerTypeAdapter(PromotionAmount.class, new GenericAdapter())
            .registerTypeAdapter(PromotionCard.class, new GenericAdapter())
            .registerTypeAdapter(PromotionAddress.class, new GenericAdapter())
            .registerTypeAdapter(PromotionTransaction.class, new GenericAdapter())
            .registerTypeAdapter(Promotions.class, new GenericAdapter())
            .registerTypeAdapter(Promotion.class, new GenericAdapter())
            .registerTypeAdapter(PromotionDiscountType.class, new GenericAdapter())
            .registerTypeAdapter(PromotionDiscount.class, new GenericAdapter())
            .registerTypeAdapter(PromotionMerchant.class, new GenericAdapter())
            .registerTypeAdapter(PromotionRule.class, new GenericAdapter())
            .registerTypeAdapter(PromotionRuleType.class, new GenericAdapter())
            .registerTypeAdapter(PromotionMessage.class, new GenericAdapter())
            .registerTypeAdapter(PromotionRuleParam.class, new GenericAdapter())
            .registerTypeAdapter(LinkedHashMap.class, new GenericAdapter())
            .registerTypeAdapter(HashMap.class, new GenericAdapter())
            .registerTypeAdapter(UserData.class, new GenericAdapter())
            .registerTypeAdapter(RoleData.class, new GenericAdapter())
            .registerTypeAdapter(Users.class, new GenericAdapter())
            .registerTypeAdapter(TreeRole.class, new GenericAdapter())
            .registerTypeAdapter(TreePermission.class, new GenericAdapter())
            .registerTypeAdapter(PermissionData.class, new GenericAdapter())
            .registerTypeAdapter(PermissionRole.class, new GenericAdapter())
            .registerTypeAdapter(FileLine.class, new GenericAdapter())
            .registerTypeAdapter(CdrServiceModel.class, new GenericAdapter())
            .registerTypeAdapter(FileDownload.class, new GenericAdapter())
            .registerTypeAdapter(FileDownloads.class, new GenericAdapter())
            .registerTypeAdapter(InternationalTransactionHistory.class, new GenericAdapter())
            .registerTypeAdapter(DomesticTransactionHistory.class, new GenericAdapter())
            .registerTypeAdapter(IpAddressInfo.class, new GenericAdapter())
            .registerTypeAdapter(BinInfo.class, new GenericAdapter())
            .registerTypeAdapter(PromotionApproval.class, new GenericAdapter())
            .registerTypeAdapter(ApprovalsData.class, new GenericAdapter())
            .registerTypeAdapter(RefundApproval.class, new GenericAdapter())
            .registerTypeAdapter(UserTokenization.class, new GenericAdapter())
            .registerTypeAdapter(TokenizationTran.class, new GenericAdapter())
            .registerTypeAdapter(MocaMpayReport.class, new GenericAdapter())
            .registerTypeAdapter(MocaMpayTransaction.class, new GenericAdapter())
            .registerTypeAdapter(MocaMpayTransactionHistory.class, new GenericAdapter())
            .create();
}
