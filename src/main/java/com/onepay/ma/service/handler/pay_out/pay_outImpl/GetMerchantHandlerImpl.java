package com.onepay.ma.service.handler.pay_out.pay_outImpl;

import com.onepay.ma.service.handler.externalClient.MaPermission;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;

import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class GetMerchantHandlerImpl implements Handler<RoutingContext> {

    private static final Logger LOGGER = Logger.getLogger(GetMerchantHandlerImpl.class.getName());

    @Override
    public void handle(RoutingContext myEvent) {

        String xUserId = myEvent.get(ParamsPool.X_USER_ID);
        String xRequestId = myEvent.get(ParamsPool.X_REQUEST_ID);
        String userId = myEvent.get(ParamsPool.USER_ID);
        String partnerId = myEvent.get(ParamsPool.X_PARTNER_ID);
        LOGGER.log(Level.INFO, "xUserId: "+ xUserId);
        LOGGER.log(Level.INFO, "userId: "+ userId);
        LOGGER.log(Level.INFO, "partnerId: "+ partnerId);
        if (xUserId == null || StringUtils.isBlank(xUserId)) {
            throw IErrors.VALIDATION_ERROR;
        }

        if (partnerId == null || StringUtils.isBlank(partnerId)) {
            throw IErrors.VALIDATION_ERROR;
        }

        JsonObject merchantIds = MaPermission.getMerchantByUser(xUserId, xRequestId, partnerId);
        LOGGER.info("acq response" + merchantIds.toString());
        myEvent.put(ParamsPool.HANDLER_DATA_RESULT, merchantIds);
        myEvent.next();

    }

}
