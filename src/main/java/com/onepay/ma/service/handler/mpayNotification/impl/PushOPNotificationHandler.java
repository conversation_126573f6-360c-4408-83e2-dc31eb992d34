package com.onepay.ma.service.handler.mpayNotification.impl;

import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.models.notification.MpayNotificationPostDto;
import com.onepay.ma.service.models.notification.NotificationConfigSearchQuery;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.service.apptoken.AppTokenService;
import com.onepay.ma.service.service.mpay.MpayOrderService;
import com.onepay.ma.service.service.notification.AppNotificationConfigService;
import com.onepay.ma.service.service.notification.AppNotificationService;
import com.onepay.ma.service.util.*;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 03-Oct-17.
 */
@Component
public class PushOPNotificationHandler implements Handler<RoutingContext> {

    public static String API_KEY = PropsUtil.get("onepay_google_notifucation_api_key", "");

    public static String API_URL = PropsUtil.get("onepay_google_notifucation_api_url", "");


    @Override
    public void handle(RoutingContext rc) {


        JDBCClient clientBackUp = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        LOGGER.log(Level.INFO, "LISTEN TO OP SMS NOTIFICATION/n");
        // LOGGER.log(Level.INFO, "CONTENT: " + rc.getBodyAsString());

        JsonObject body = rc.getBodyAsJson();
        if (body == null) {
            LOGGER.log(Level.SEVERE, "mPAY NOTIFICATION ERROR: invalid content");
            throw IErrors.VALIDATION_ERROR;
        }

        String toMobile = Utils.convertMobileBumber(body.getString(ParamsPool.TO));
        String category = body.getString(ParamsPool.CATEGORY);
        String content = body.getString(ParamsPool.CONTENT);
        String title = body.getString(ParamsPool.TITLE);


        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(sqlBackUp -> {
                    this.connectionBackup = sqlBackUp;

                    return userService.findIdByMobile(connectionBackup, toMobile).flatMap(foundedUserId -> {
                        if (foundedUserId.isEmpty()) {
                            throw IErrors.RESOURCE_NOT_FOUND;
                        }

                        LOGGER.log(Level.INFO, "FOUNDED USER ID: " + foundedUserId);


                        MpayNotificationPostDto notification = new MpayNotificationPostDto();

                        notification.setContent_en(content);
                        notification.setContent_vi(content);

                        notification.setHeader_en(title ==  null ? "Unknown" : title);
                        notification.setHeader_vi(title ==  null ? "Unknown" : title);

                        notification.setCategory(category == null ? "MOBILE_NOTIFICATION" : category);
                        notification.setUserId(foundedUserId);
                        return connectionBackup.setAutoCommitObservable(false).flatMap(aVoid -> {
                            // Store notification by UserId
                            return this.appNotificationService.insert(connectionBackup, notification).flatMap(mpayNotifications -> {
                                return appTokenService.findByUserIds(connectionBackup, foundedUserId).map(foundedToken -> {

                                    foundedToken = foundedToken.stream().distinct().collect(Collectors.toList());

                                    LOGGER.log(Level.INFO, "FOUNDED TOKEN: " + foundedToken);

                                    try {
                                        String userTokenFireBase = Utils.getAccessToken();
                                        for (AppToken token : foundedToken) {

    //                                        if (eventType.equalsIgnoreCase("NOTIFICATION")) {
    //                                            messageContent = token.getLanguage().equals("vi") ? resourceContentVI : resourceContentEN;
    //                                        }

                                            // send notification
                                            this.sendNotification(content, token, userTokenFireBase);
                                        }
                                    } catch (IOException e) {
                                        LOGGER.log(Level.SEVERE,"Notify not send ", e);
                                        LOGGER.log(Level.SEVERE,"userTokenFireBase cant get, Error! ", e);
                                    }
                                    return foundedToken;
                                });
                            });
                        });
                    });

                }).subscribe(r -> {
            if (connectionBackup != null) {
                connectionBackup.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
            rc.next();
        }, throwable -> {

            if (connectionBackup != null) {
                connectionBackup.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }


    private void sendNotification(String messageContent, AppToken token, String userTokenFireBase) {

        try {

            // Prepare JSON containing the GCM message content. What to send and where to send.
            JsonObject jGcmData = new JsonObject();
            JsonObject jGcmMessNew = new JsonObject();

            if (token.getOs().equalsIgnoreCase("IOS")) {
                JsonObject jNotification = new JsonObject();
                jNotification.put("title", "mPAYvn");
                jNotification.put("body", messageContent);
                // jNotification.put("sound", "default");
                // jNotification.put("badge", 1);
                jGcmData.put("notification", jNotification);
                // jGcmData.put("priority", "high");
            }
            // jGcmData.put("to", token.getToken());
            jGcmData.put("token", token.getToken());

            JsonObject jData = new JsonObject();

            if (token.getOs().equalsIgnoreCase("ANDROID")) {
                jData.put("title", "mPAYvn");
                jData.put("body", messageContent);
                // jData.put("sound", "default");
            }
//            jData.put("target", invoice);
            // What to send in GCM message.
            jGcmData.put("data", jData);
//            jGcmData.put("invoive", invoiceId);
            // What to send in GCM message New.
            jGcmMessNew.put("message", jGcmData);

            // Create connection to send GCM Message request.
            URL url = new URL(API_URL);
//            URL url = new URL("https://fcm.googleapis.com/fcm/send");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // conn.setRequestProperty("Authorization", "key=" + API_KEY);
            conn.setRequestProperty("Authorization", "Bearer " + userTokenFireBase);

            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);


            LOGGER.log(Level.INFO, "SEND NOTIFICATION TO TOKEN: " + token.getToken());
            LOGGER.log(Level.INFO, "SEND NOTIFICATION WITH LANGUAGE: " + token.getLanguage());
            LOGGER.log(Level.INFO, "SEND NOTIFICATION WITH CONTENT: " + jGcmMessNew.toString());
            LOGGER.log(Level.INFO, "SEND NOTIFICATION WITH Authorization: " + "Bearer " + userTokenFireBase);
            // Send GCM message content.
            OutputStream outputStream = conn.getOutputStream();
            outputStream.write(jGcmMessNew.toString().getBytes());
            // Read GCM response.
            InputStream inputStream = conn.getInputStream();
            String resp = IOUtils.toString(inputStream);

            // LOGGER.log(Level.INFO, "RESPONSE: " + new JsonObject(resp).encodePrettily());
            LOGGER.log(Level.INFO, "resp: " + resp);
        } catch (IOException e) {
            // LOGGER.log(Level.WARNING, "Unable to send GCM message.");
            // LOGGER.log(Level.WARNING, "Please ensure that API_KEY has been replaced by the server " +
            //         "API key, and that the device's registration token is correct (if specified).");
            LOGGER.log(Level.WARNING, "Error ", e);
        }
    }


    @Autowired
    private UserServiceV2 userService;


    @Autowired
    private AppTokenService appTokenService;

    @Autowired
    private AppNotificationService appNotificationService;


    private SQLConnection connectionBackup;

    private static Logger LOGGER = Logger.getLogger(PushOPNotificationHandler.class.getName());
}
