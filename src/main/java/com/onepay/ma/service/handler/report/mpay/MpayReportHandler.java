package com.onepay.ma.service.handler.report.mpay;

import com.onepay.ma.service.handler.report.mpay.impl.MpayReportDownloadHandler;
import com.onepay.ma.service.handler.report.mpay.impl.MpayReportGetHandler;
import com.onepay.ma.service.handler.report.mpay.impl.MocaMpayReportDownloadHandler;
import com.onepay.ma.service.handler.report.mpay.impl.MocaMpayReportGetHandler;
import com.onepay.ma.service.handler.report.mpay.impl.PaymentMpayReportDownloadHandler;
import com.onepay.ma.service.handler.report.mpay.impl.PaymentMpayReportGetHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import io.vertx.core.Handler;

/**
 * Created by anhkh on 04-Jun-18.
 */
@Component
public class MpayReportHandler {

    @Autowired
    private MpayReportGetHandler mpayReportGetHandler;

    @Autowired
    private MocaMpayReportGetHandler mpayReportMocaGetHandler;

    @Autowired
    private PaymentMpayReportGetHandler paymentMpayReportGetHandler;

    @Autowired
    private MpayReportDownloadHandler mpayReportDownloadHandler;
    
    @Autowired
    private MocaMpayReportDownloadHandler mocaMpayReportDownloadHandler;

    @Autowired
    private PaymentMpayReportDownloadHandler paymentMpayReportDownloadHandler;

    public Handler  get() {
        return mpayReportGetHandler;
    }
    public Handler  getPayment() {
        return paymentMpayReportGetHandler;
    }



    public Handler  download() {
        return mpayReportDownloadHandler;
    }
    public Handler  downloadPayment() {
        return paymentMpayReportDownloadHandler;
    }

    public Handler getMoca() {
        return mpayReportMocaGetHandler;
    }
    public Handler  downloadMoca() {
        return mocaMpayReportDownloadHandler;
    }
}

