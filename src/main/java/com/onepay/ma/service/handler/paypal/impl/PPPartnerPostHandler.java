package com.onepay.ma.service.handler.paypal.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.handler.externalClient.PayPalClient;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.service.UserServiceV2;
import com.onepay.ma.service.service.paypal.PaypalMerchantService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.ext.web.RoutingContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class PPPartnerPostHandler implements Handler<RoutingContext> {

    @Override
    public void handle(RoutingContext rc) {
        JsonObject body = rc.getBodyAsJson();

        String userId = rc.get(ParamsPool.X_USER_ID);
        if (userId == null) {
            throw IErrors.VALIDATION_ERROR;
        }
        String merchantId = body.getString("merchantIdInPayPal");
        JsonNode accountJs = PayPalClient.getAccountStatus(merchantId);
        if (accountJs == null) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


        JDBCClient clientOnline = rc.get(ParamsPool.ONLINE_DATASOURCE_NAME);
        JDBCClient clientBackup = rc.get(ParamsPool.BACK_UP_DATASOURCE_NAME);

        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    connectionO = connOnline;
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackup), f -> f.dispose())
                            .flatMap(connBackup -> {
                                connectionB = connBackup;
                                return connectionB.setAutoCommitObservable(false).flatMap(aVoid -> {
                                    return connectionO.setAutoCommitObservable(false).flatMap(aVoid1 -> {
                                        LOGGER.log(Level.SEVERE, "PAYPAL REGISTER: INSERT INTO ONEPARTNER");
                                        return this.paypalMerchantService.insert(connectionO
                                                , accountJs.get("tracking_id").asInt()
                                                , accountJs.get("merchant_id").asText()
                                                , accountJs.get("payments_receivable").asText()
                                                , accountJs.get("primary_email_confirmed").asText());
                                    }).flatMap(aVoid1 -> {

                                        return userServiceV2.getUserDataBySId(connectionB, userId).flatMap(userData -> {
                                            return userServiceV2.getPartnerIdByUserId(connectionB, userId).flatMap(partner -> {
                                                if (partner == null) {
                                                    throw IErrors.INVALID_USER_PARTNER;
                                                }

                                                LOGGER.log(Level.SEVERE, "PAYPAL REGISTER: SEND EMAIL TO ONEPAY");
                                                return emailService.sentMailRegisterPP(userData, partner, merchantId);
                                            });
                                        });
                                    });
                                });
                            });
                }).subscribe(aVoid -> {
            if (connectionO != null) {
                connectionO.commitObservable();
            }
            if (connectionB != null) {
                connectionB.commitObservable();
            }

            rc.put(ParamsPool.HANDLER_DATA_CODE, HttpResponseStatus.OK.code());
            rc.next();
        }, throwable -> {
            if (connectionO != null) {
                connectionO.rollbackObservable();
            }
            if (connectionB != null) {
                connectionB.rollbackObservable();
            }
            rc.fail(throwable);
        });
    }

    SQLConnection connectionO;
    SQLConnection connectionB;

    @Autowired
    private PaypalMerchantService paypalMerchantService;

    @Autowired
    private EmailService emailService;


    @Autowired
    private UserServiceV2 userServiceV2;


    private static Logger LOGGER = Logger.getLogger(PPPartnerPostHandler.class.getName());

}
