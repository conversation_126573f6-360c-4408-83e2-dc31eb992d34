package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.user.UserSearchQuery;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.Encode;
import com.onepay.ma.service.models.user.UserProfile;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/7/16.
 */
@Service
public class UserServiceImpl implements UserService {


    public static String ENCODE_KEY_1 = PropsUtil.get("app.key.password.1", ""); // mOnePAY
    public static String ENCODE_KEY_2 = PropsUtil.get("app.key.password.2", ""); // mOnePAY
    public static String ENCODE_KEY_3 = PropsUtil.get("app.key.password.3", ""); // mOnePAY

    @Override
    public String encodePassword(String password, int level) {

        try {
            if (level == 1) {
                return Encode.hmacSHA256ToHex(ENCODE_KEY_1, password);
            }
            if (level == 12) {
                password = Encode.hmacSHA256ToHex(ENCODE_KEY_2, password);
            }
            if (level == 2 || level == 12) {
                return Encode.hmacSHA256ToHex(ENCODE_KEY_3, password);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Encode password: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return null;
    }

    @Override
    public Observable<List<User>> listByIds(SQLConnection connection, String ids) {
        JsonArray inParams = new JsonArray().add(ids);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_USER_BY_IDS, inParams, outParams).map(result -> {
            List<User> userList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0){
                for(JsonObject jsonObject : rs.getRows()){
                    User user = resultSetToUser(jsonObject);
                    userList.add(user);
                }
            }

            return userList;
        });
    }

    @Override
    public Observable<Users> list(SQLConnection connection, UserSearchQuery query) {
        return getListUser(connection, query).flatMap(users -> {
            //get list role
            return  getListRoleByUser(connection, users.getUsers(), 0).map(userDatas -> {
                users.setUsers(userDatas);
                return users;
            });
        }).flatMap(users -> {
            // get list mechant
            return  getListMerchantByUser(connection, users.getUsers(), 0).map(userDatas -> {
                users.setUsers(userDatas);
                return  users;
            });
        }).flatMap(users -> {
            // get list terminal for mpay merchant
            return  getListTerminalByUserMerchant(connection, users.getUsers(), 0).map(userDatas -> {
                users.setUsers(userDatas);
                return  users;
            });
        }).flatMap(users -> {
            // get list partners
            return getListPartnerByUser(connection, users.getUsers(), 0).map(userDatas -> {
                users.setUsers(userDatas);
                return users;
            });
        });
    }

    @Override
    public Observable<UserProfile> getProfileInfo(SQLConnection connection,SQLConnection connectionBackUp, String sid) {

        return getUserDataInfo(connection, sid).flatMap(userData -> {
            return this.userPermissionService.list(connection, sid).flatMap(permissions -> {
                return merchantService.listAll(connection, sid).flatMap(merchants -> {
                    String midQT = merchants.stream().filter(m-> m.getType().equalsIgnoreCase("international")).map(Merchant::getMerchant_id).collect(Collectors.joining(","));
                    return internationalTransactionService.getRiskAssessment(connectionBackUp, midQT).flatMap(risk -> {
                        return internationalTransactionService.isDigitalWallet(connectionBackUp, merchants).map(isDigitalWallet -> {
                            UserProfile profile = new UserProfile();
                            profile.setMerchants(merchants);
                            profile.setEmail(userData.getEmail());
                            profile.setS_id(userData.getS_id());
                            profile.setName(userData.getName());
                            profile.setPhone(userData.getPhone());
                            profile.setShow_risk_assetment(!midQT.equals("") && risk.equalsIgnoreCase("Y") ? true : false);
                            profile.setFunctions(permissions.getPermissions().stream().map(Permission::getPermission_name).collect(Collectors.toList()));
                            if (isDigitalWallet){
                                profile.getFunctions().add("digital_wallet_user");
                            }
                            return profile;
                        });    
                    });
                });
            });
        });
    }

    @Override
    public Observable<UserData> get(SQLConnection connection, String sid) {
        return getUserDataInfo(connection, sid).flatMap(userData -> {
            if(userData != null) {
                return roleService.listRoleByUserId(connection, userData.getS_id()).map(roles -> {
                    userData.setRoles(roles);
                    return userData;
                });
            }else{
                return Observable.just(userData);
            }
        }).flatMap(userData -> {
            if (userData == null) {
                return Observable.just(userData);
            }
            return merchantService.listAll(connection, userData.getS_id()).map(merchants -> {
                userData.setMerchants(merchants);
                return userData;
            });
        }).flatMap(userData -> {
            if (userData == null) {
                return Observable.just(userData);
            }
            return userTerminalService.getTerminalBUserId(connection, userData.getN_id()).map(terminals -> {
                this.setTerminalIntoUserData(terminals, userData);
                return userData;
            });
        }).flatMap(userData -> {
            if (userData == null) {
                return Observable.just(userData);
            }
            return userPartnerService.getPartnersByUserId(connection, userData.getS_id()).map(partners -> {
                userData.setPartners(partners.getPartners());
                return userData;
            });
        }).flatMap(userData -> {
           if (userData == null) {
               return Observable.just(userData);
           }
           return userPermissionService.list(connection, userData.getS_id()).map(permissions -> {
              userData.setPermissions(permissions.getPermissions());
              return userData;
           });
        });
    }


    @Override
    public Observable<User> getOneAm(SQLConnection connection, String sid) {
        return getUserData(connection, sid);
    }

    @Override
    public Observable<User> update(SQLConnection connection, User user) {
        return updateUser(connection, user).flatMap(integer -> {
            return getUserData(connection, user.getS_id());
        });
    }

    @Override
    public Observable<User> insert(SQLConnection connection, User user) {
        return insertUser(connection, user).flatMap(integer -> {
            return getUserData(connection, user.getS_id());
        });
    }

    /**
     * insert user data
     * @param connection
     * @param user
     * @return
     */
    private Observable<Integer> insertUser(SQLConnection connection, User user) {
        JsonArray inParams = new JsonArray()
                .add(user.getS_id())
                .addNull()
                .add(user.getName())
                .add(user.getEmail())
                .add(user.getPhone() == null ? StringPool.BLANK : user.getPhone())
                .add(user.getAddress())
                .addNull() ;
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(USER_INSERT, inParams, outParams).map(result -> {
            int returnData = 0;
            int code = result.getOutput().getInteger(7);
            if(code != 201) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert user failed" + result.getOutput().getString(9));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnData = result.getOutput().getInteger(8);
            }

            return returnData;
        });
    }

    /**
     * update user data
     * @param connection
     * @param user
     * @return
     */
    private Observable<Integer> updateUser(SQLConnection connection, User user) {

        JsonArray inParams = new JsonArray()
                .add(user.getS_id())
                .add(user.getName())
                .add(user.getEmail())
                .add(user.getPhone() == null ? StringPool.BLANK : user.getPhone())
                .add(user.getAddress())
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(USER_UPDATE, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(6);
            if(returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ USER ] Update user data failed " + result.getOutput().getString(7));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return returnValue;
        });
    }

    /**
     * GET USER DATA
     * @param sqlConnection
     * @param sid
     */
    private Observable<User> getUserData(SQLConnection sqlConnection, String sid){
        JsonArray inParams = new JsonArray().add(sid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_ONE_AM, inParams, outParams).map(result -> {
            User user = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            user = resultSetToUser(jsonObject);

            return user;
        });

    }

    /**
     * GET USER DATA
     * @param sqlConnection
     * @param sid
     */
    private Observable<UserData> getUserDataInfo(SQLConnection sqlConnection, String sid){
        JsonArray inParams = new JsonArray().add(sid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_ONE_AM, inParams, outParams).map(result -> {
            UserData user = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            user = resultSetToUserData(jsonObject);

            return user;
        });

    }

    /**
     * get list role for list user
     * @param sqlConnection
     * @param userList
     * @param index
     * @return
     */
    private Observable<List<UserData>> getListRoleByUser(SQLConnection sqlConnection, List<UserData> userList, int index){
        if(userList.size() <= 0){
            return Observable.just(userList);
        }
        UserData user = userList.get(index);
        final int finalIndex = index;
        return Observable.just(user).flatMap(userData -> {
            //insert approval for user group
            return roleService.listRoleByUserId(sqlConnection, user.getS_id()).flatMap(roles -> {
                user.setRoles(roles);
                if(finalIndex >= userList.size() - 1){
                    return Observable.just(userList);
                }else{
                    return getListRoleByUser(sqlConnection, userList, finalIndex + 1);
                }
            });
        });

    }

    /**
     * get list merchant for list user
     * @param sqlConnection
     * @param userList
     * @param index
     * @return
     */
    private Observable<List<UserData>> getListMerchantByUser(SQLConnection sqlConnection, List<UserData> userList, int index){
        if(userList.size() <= 0){
            return Observable.just(userList);
        }
        UserData user = userList.get(index);
        final int finalIndex = index;
        return Observable.just(user).flatMap(userData -> {
            //insert approval for user group
            return merchantService.listAll(sqlConnection, user.getS_id()).flatMap(merchants -> {
                user.setMerchants(merchants);
                if(finalIndex >= userList.size() - 1){
                    return Observable.just(userList);
                }else{
                    return getListMerchantByUser(sqlConnection, userList, finalIndex + 1);
                }
            });
        });

    }

    /**
     * Get list partners by user
     * @param sqlConnection
     * @param userList
     * @param index
     * @return
     */
    private Observable<List<UserData>> getListPartnerByUser(SQLConnection sqlConnection, List<UserData> userList, int index) {
        // Return if list of users is empty
        if (userList == null || userList.size() == 0){
            return Observable.just(userList);
        }

        // Get current user
        UserData user = userList.get(index);

        return Observable.just(user).flatMap(userData -> {
            // Get list partners by user id
            return userPartnerService.getPartnersByUserId(sqlConnection, user.getS_id()).flatMap(partners -> {
                user.setPartners(partners.getPartners());
                if(index == userList.size() - 1){
                    // Return if list partners of all users have retrieved.
                    return Observable.just(userList);
                }else{
                    // Continue to get list partners of next user (recursive)
                    return getListPartnerByUser(sqlConnection, userList, index + 1);
                }
            });
        });
    }

    /**
     * ...
     * @param sqlConnection
     * @param userList
     * @param index
     * @return
     */
    private Observable<List<UserData>> getListTerminalByUserMerchant(SQLConnection sqlConnection, List<UserData> userList, int index) {
        if(userList.size() <= 0){
            return Observable.just(userList);
        }
        UserData user = userList.get(index);
        final int finalIndex = index;

        return Observable.just(user).flatMap(userData -> {
            return userTerminalService.getTerminalBUserId(sqlConnection, user.getN_id()).flatMap(terminals -> {
                setTerminalIntoUserData(terminals, user);

                if(finalIndex >= userList.size() - 1){
                    return Observable.just(userList);
                }else{
                    return getListTerminalByUserMerchant(sqlConnection, userList, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get list user data
     * @param connection
     * @param query
     * @return
     */
    private Observable<Users> getListUser(SQLConnection connection, UserSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getKeywords())
                .add(query.getMerchant_id())
                .add(query.getUser_id())
                .add(query.getEmail())
                .add(query.getPhone())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(USER_SEARCH, inParams, outParams).map(result -> {
            Users users = new Users();
            List<UserData> userList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0){
                for(JsonObject jsonObject : rs.getRows()){
                    UserData user = resultSetToUserData(jsonObject);
                    userList.add(user);
                }
                users.setTotal_items(result.getOutput().getInteger(8));
            }else{
                users.setTotal_items(0);
            }

            users.setUsers(userList);
            return users;
        });
    }

    /**
     * bind data to user
     * @param rs
     * @return
     * @throws SQLException
     */
    private User resultSetToUser(JsonObject rs){
        String name = rs.getString("S_NAME");
        String sid  = rs.getString("S_ID");
        int id = rs.getInteger("N_ID");
        String address = rs.getString("S_ADDRESS");
        String phone = rs.getString("S_PHONE");
        String email = rs.getString("S_EMAIL");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");

        Date createDate = Timestamp.valueOf(rs.getString("D_CREATE"));
        Date updateDate =  Timestamp.valueOf(rs.getString("D_UPDATE"));
//        try {
//            updateDate = formatter.parse(rs.getString("D_UPDATE"));
//        } catch (ParseException e) {
//          LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
//        try {
//            createDate = formatter.parse(rs.getString("D_CREATE"));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        User user =  new User();
        user.setN_id(id);
        user.setS_id(sid);
        user.setName(name);
        user.setAddress(address);
        user.setPhone(phone);
        user.setEmail(email);
        user.setUpdate_date(updateDate);
        user.setCreate_date(createDate);

        return user;
    }

    /**
     * bind data to user data
     * @param rs
     * @return
     * @throws SQLException
     */
    private UserData resultSetToUserData(JsonObject rs)  {
        String name = rs.getString("S_NAME");
        String sid  = rs.getString("S_ID");
        int id = rs.getInteger("N_ID");
        String address = rs.getString("S_ADDRESS");
        String phone = rs.getString("S_PHONE");
        String email = rs.getString("S_EMAIL");
        String createId = rs.getString("S_CREATE_ID");
        String createName = rs.getString("S_CREATE_NAME");
        String createEmail = rs.getString("S_CREATE_EMAIL");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp createDate = rs.getString("D_CREATE") == null ? null : Timestamp.valueOf(rs.getString("D_CREATE"));
        Timestamp updateDate =  rs.getString("D_UPDATE") == null ? null :Timestamp.valueOf(rs.getString("D_UPDATE"));
        Timestamp lastLoginDate =  rs.getString("D_LAST_LOGIN") == null ? null :Timestamp.valueOf(rs.getString("D_LAST_LOGIN"));
//        try {
//            createDate = formatter.parse(rs.getString("D_CREATE"));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
//        try {
//            updateDate = formatter.parse(rs.getString("D_UPDATE"));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }



        UserData user =  new UserData();
        user.setN_id(id);
        user.setS_id(sid);
        user.setName(name);
        user.setAddress(address);
        user.setPhone(phone);
        user.setEmail(email);
        user.setUpdate_date(updateDate);
        user.setCreate_date(createDate);
        user.setLast_login_date(lastLoginDate);
        user.setCreateId(createId);
        user.setCreateName(createName);
        user.setCreateEmail(createEmail);

        return user;
    }

    private void setTerminalIntoUserData(List<Terminal> terminals, UserData userData){
        // group by Map <MerchantId, List<TerminalId>>
        Map<String, List<String>> terminalMap = terminals.stream()
                .collect(Collectors.groupingBy(Terminal::getMerchant_id,
                        Collectors.mapping(Terminal::getTerminal_id, Collectors.toList())));

        for (Merchant me: userData.getMerchants() ) {
            // Mpay merchant only
            List<String> terminalIds = terminalMap.get(me.getMerchant_id());
            if (me.getType().equalsIgnoreCase("mpay") && terminalIds != null) {
                if(terminalIds.get(0).equalsIgnoreCase("ALL")) {
                    me.setAllTerminal(true);
                }else {
                    me.setTerminalIdList(terminalIds);
                }
            }
        }
    }
    public Observable<Integer>  changePass(SQLConnection conn, String userId, String pass,String newPass) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(pass)
                .add(newPass);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(USER_CHANGE_PASS, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] Change pass Failed" + StringPool.SPACE + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnValue = result.getOutput().getInteger(3);
            }

            return returnValue;
        });
    }

    private static final String GET_ONE_AM = "{call PKG_MERCHANTPORTAL_2.USER_SEARCH_BY_SID(?,?,?,?)}";
    private static final String LIST_USER_BY_IDS = "{call PKG_MERCHANTPORTAL_2.USER_LIST_BY_IDS(?,?,?,?)}";

    private static final String USER_SEARCH = "{call PKG_MERCHANTPORTAL_2.USER_SEARCH(?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String USER_INSERT = "{call PKG_MERCHANTPORTAL_2.INSERT_USER(?,?,?,?,?,?,?,?,?,?)}";

    private static final String USER_UPDATE = "{call PKG_MERCHANTPORTAL_2.UPDATE_USER_ONEAM(?,?,?,?,?,?,?,?)}";

    private static final String USER_CHANGE_PASS = "{call PKG_ONEAM_USER.USER_CHANGE_PASS(?,?,?,?,?)}";

    @Autowired
    private RoleService roleService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private UserTerminalService userTerminalService;

    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private UserPartnerService userPartnerService;

    @Autowired
    private InternationalTransactionService internationalTransactionService;

    private static final Logger LOGGER = Logger.getLogger(UserServiceImpl.class.getName());


}
