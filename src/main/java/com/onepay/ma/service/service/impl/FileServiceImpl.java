package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.FileDownloads;
import com.onepay.ma.service.service.FileService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/16/16.
 */
@Service
public class FileServiceImpl implements FileService {

    @Override
    public Observable<FileDownload> insert(SQLConnection sqlBackUp, FileDownload fileDownload) {
        return insertFileDownload(sqlBackUp, fileDownload, fileDownload.getUser()).flatMap(integer -> {
            // return getFileDownload(sqlBackUp, fileDownload.getFile_hash_name());
            return Observable.just(fileDownload);
        });
    }

    @Override
    public Observable<FileDownloads> list(SQLConnection sqlBackUp, String keyword, String userId, int page) {
        return listFileDownload(sqlBackUp, keyword, userId, page);
    }

    @Override
    public Observable<FileDownload> get(SQLConnection sqlBackUp, String fileHashName, String userid) {
        return getFileDownload(sqlBackUp, fileHashName, userid);
    }

    @Override
    public Observable<FileDownload> updateStatus(SQLConnection sqlBackUp, String status, String fileHashName, long fileSize) {
        return updateStatusFileDownload(sqlBackUp, status, fileHashName, fileSize).flatMap(integer -> {
            return Observable.just(null);
        });
    }

    @Override
    public Observable<Integer> updateExpTime(SQLConnection sqlBackUp, String fileHashName) {
        return updateTimeExp(sqlBackUp, fileHashName);
    }

    @Override
    public Integer updateStatusDownload(Connection sqlBackUp, String status, String fileHashName, long fileSize) throws SQLException {
        return updateStatusQueueDownload(sqlBackUp, status, fileHashName, fileSize);
    }

    private Integer updateStatusQueueDownload(Connection sqlBackUp, String status, String fileHashName, long fileSize) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(fileHashName)
                .add(fileSize)
                .add(status);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        Integer rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(sqlBackUp, FILE_DOWNLOAD_UPDATE_STATUS, inParams, outParams);
            rs = callableStatement.getInt(4);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
        }
        return rs;
    }
    /**
     * get file file detail
     * @param sqlBacKUp
     * @param hashName
     * @return
     */
    private Observable<FileDownload> getFileDownload(SQLConnection sqlBacKUp, String hashName, String userId) {
        LOGGER.log(Level.INFO, "file Name:  " + hashName);
        JsonArray inParams = new JsonArray()
                .add(hashName)
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBacKUp.callWithParamsObservable2(FILE_DOWNLOAD_BY_HASH_NAME, inParams, outParams).map(result -> {
            FileDownload fileDownload = null;
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            fileDownload = bindFileDownload(jsonObject);
            return fileDownload;
        });
    }

    /**
     * list file file
     * @param sqlBacKUp
     * @param keyword
     * @param userId
     * @return
     */
    private Observable<FileDownloads> listFileDownload(SQLConnection sqlBacKUp, String keyword, String userId, int page) {
        JsonArray inParams = new JsonArray()
                .add(keyword)
                .add(userId)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBacKUp.callWithParamsObservable2(FILE_DOWNLOAD_LIST, inParams, outParams).map(result -> {
            FileDownloads fileDownloads = new FileDownloads();
            List<FileDownload> fileDownloadList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    FileDownload fileDownload = bindFileDownload(jsonObject);
                    fileDownloadList.add(fileDownload);
                }
                fileDownloads.setTotal_items(result.getOutput().getInteger(5));
            }else{
                fileDownloads.setTotal_items(0);
            }

            fileDownloads.setFiles(fileDownloadList);
            return fileDownloads;
        });
    }

    /**
     * insert file file
     * @param sqlBackUp
     * @param fileDownload
     * @param userId
     * @return
     */
    private Observable<Integer> insertFileDownload(SQLConnection sqlBackUp, FileDownload fileDownload, String userId){
        JsonArray inParams = new JsonArray()
                .add(fileDownload.getFile_hash_name())
                .add(userId)
                .add(fileDownload.getFile_name())
                .add(fileDownload.getExt())
                .add(fileDownload.getFile_type())
                .add(fileDownload.getConditions());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBackUp.callWithParamsObservable2(FILE_DOWNLOAD_INSERT, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(6);
            if(returnValue != 201){
                throw  new DatabaseException("Error : " + result.getOutput().getString(7));
            }
            return returnValue;
        });
    }

    /**
     * update status file file
     * @param sqlBackUp
     * @param status
     * @param fileHashName
     * @return
     */
    private Observable<Integer> updateStatusFileDownload(SQLConnection sqlBackUp, String status, String fileHashName, long fileSize){
        JsonArray inParams = new JsonArray()
                .add(fileHashName)
                .add(fileSize)
                .add(status);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBackUp.callWithParamsObservable2(FILE_DOWNLOAD_UPDATE_STATUS, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(3);
            if(returnValue != 201){
                throw  new DatabaseException("Error : " + result.getOutput().getString(4));

            }

            return returnValue;
        });
    }

    /**
     * update exp time  file
     * @param sqlBackUp
     * @param fileHashName
     * @return
     */
    private Observable<Integer> updateTimeExp(SQLConnection sqlBackUp,  String fileHashName){
        JsonArray inParams = new JsonArray()
                .add(fileHashName);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBackUp.callWithParamsObservable2(FILE_DOWNLOAD_UPDATE_EXP_TIME, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(1);
            if(returnValue != 200){
                throw  new DatabaseException("Error : " + result.getOutput().getString(4));

            }

            return returnValue;
        });
    }


    /**
     * bind result set to file file
     * @param rs
     * @return
     */
    private FileDownload bindFileDownload(JsonObject rs){

        String fileHashName = rs.getString("S_FILE_HASH_NAME");
        String fileName = rs.getString("S_FILE_ORG_NAME");
        String type = rs.getString("S_TYPE");
        String userId = rs.getString("S_USER_ID");
        String extension = rs.getString("S_EXTENSION");
        Timestamp createDate = Timestamp.valueOf(rs.getString("D_CREATE_DATE"));
        Timestamp updateDate = Timestamp.valueOf(rs.getString("D_UPDATED_DATE"));
        Timestamp expiredDate = null;
        if(rs.getString("D_EXP_DATE") != null){
            expiredDate = Timestamp.valueOf(rs.getString("D_EXP_DATE"));
        }
        String status = rs.getString("S_STATUS");
        String condition = rs.getString("S_CONDITIONS");
        long fileSize = 0;
        if(rs.getLong("N_FILE_SIZE") != null){
            fileSize =  rs.getLong("N_FILE_SIZE");
        }

        FileDownload fileDownload = new FileDownload();
        fileDownload.setConditions(condition);
        fileDownload.setCreate_date(createDate);
        fileDownload.setUpdate_date(updateDate);
        fileDownload.setExpired_date(expiredDate);
        fileDownload.setExt(extension);
        fileDownload.setUser(userId);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_type(type);
        fileDownload.setStatus(status);
        fileDownload.setFile_size(fileSize);

        return fileDownload;

    }

    private final static String FILE_DOWNLOAD_BY_HASH_NAME = "{call PKG_MERCHANTPORTAL_2.GET_FILE_1(?,?,?,?,?)}";

    private final static String FILE_DOWNLOAD_LIST = "{call PKG_MERCHANTPORTAL_2.LIST_FILE(?,?,?,?,?,?,?,?)}";

    private final static String FILE_DOWNLOAD_UPDATE_STATUS = "{call PKG_MERCHANTPORTAL_2.UPDATE_FILE_DOWNLOAD_STATUS(?,?,?,?,?)}";

    private final static String FILE_DOWNLOAD_INSERT = "{call PKG_MERCHANTPORTAL_2.INSERT_FILE_DOWNLOAD(?,?,?,?,?,?,?,?)}";

    private final static String FILE_DOWNLOAD_UPDATE_EXP_TIME = "{call PKG_MERCHANTPORTAL_2.UPDATE_FILE_DOWNLOAD_EXP(?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(FileServiceImpl.class.getName());
}
