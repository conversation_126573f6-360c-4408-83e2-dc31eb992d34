/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */
/**
 * Created by duongtv on 24/8/2017.
 */
package com.onepay.ma.service.service.impl;

import com.onepay.commons.util.Convert;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.TokenService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Service
public class TokenServiceImpl implements TokenService {

    @Override
    public Observable<UserTokenizations> listUserToken(SQLConnection connBackup, String cardNo, String userID, String userGroup, int page,int pagesize) {
        return getListUserPage(connBackup, cardNo, userID, userGroup, page,pagesize);
    }

    @Override
    public Observable<TokenizationTrans> listTranToken(SQLConnection connBackup, TokenizationTranParams params) {
        return getListTranPage(connBackup, params);
    }

    /**
     * get list user tokenization paging
     * @param connBackUp
     * @return
     */
    private Observable<UserTokenizations> getListUserPage(SQLConnection connBackUp, String cardNo,String userID,String userGroup, int page,int pagesize) {
        UserTokenizations users = new UserTokenizations();
        JsonArray inParams = new JsonArray()
                .add(cardNo)
                .add(userID)
                .add(userGroup)
                .add(page)
                .add(pagesize);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_USER_TOKENIZATION, inParams, outParams).map(result -> {
            List<UserTokenization> userTokenList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    UserTokenization userToken = bindUser(jsonObject);
                    userTokenList.add(userToken);
                }
            }
            users.setListUser(userTokenList);
            users.setTotal(result.getOutput().getInteger(6));
            return users;
        });
    }


    /**
     * get list tran tokenization paging
     * @param connBackUp
     * @return
     */
    private Observable<TokenizationTrans> getListTranPage(SQLConnection connBackUp, TokenizationTranParams params) {
        TokenizationTrans trans = new TokenizationTrans();
        JsonArray inParams = new JsonArray()
                .add(params.getFromDate())
                .add(params.getToDate())
                .add(params.getTranId())
                .add(params.getCardNo())
                .add(params.getUserId())
                .add(params.getUserGroup())
                .add(params.getPage())
                .add(params.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_TRAN_TOKENIZATION, inParams, outParams).map(result -> {
            List<TokenizationTran> tranTokenList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    TokenizationTran tranToken = bindTran(jsonObject);
                    tranTokenList.add(tranToken);
                }
            }
            trans.setTrans(tranTokenList);
            trans.setTotalTran(result.getOutput().getInteger(9));
            return trans;
        });
    }

    private UserTokenization bindUser(JsonObject js) {
        UserTokenization result = new UserTokenization();
        result.setUserId(js.getString("S_USER_ID"));
        result.setChannelUserId(js.getString("S_CHANNEL_USER_ID"));
        result.setUserGroupId(js.getString("S_USERGROUP_ID"));
        result.setFirstName(js.getString("S_FIRST_NAME"));
        result.setLastName(js.getString("S_LAST_NAME"));
        result.setEmail(js.getString("S_EMAIL"));
        result.setMobile(js.getString("S_MOBILE"));
        result.setStateUser(js.getString("S_USER_STATE"));
        result.setUserCreated(js.getString("D_CREATE_USER") == null ? null : Timestamp.valueOf(js.getString("D_CREATE_USER") ));
        result.setUserUpdated(js.getString("D_UPDATE_USER") == null ? null : Timestamp.valueOf(js.getString("D_UPDATE_USER") ));
        String listCard = js.getString("S_CARDS");
        String cardNos = "";
        List<CardToken> cards = new ArrayList<>();
        if(listCard!=null && listCard.length()>0){
            String[] arrCard = listCard.split(";");
            for(String strCard : arrCard){
                if(strCard!=null && strCard.length()>0){
                    String[] cellCard = strCard.split("\\|");
                    CardToken card = new CardToken();
                    card.setNameOnCard(cellCard[0]);
                    card.setCardNo(cellCard[1]);
                    card.setState(cellCard[2]);
                    card.setType(cellCard[3]);
                    cards.add(card);
                    if(cardNos.length()>0) cardNos+="; ";
                    cardNos+=cellCard[1];
                }
            }
        }
        result.setCards(cards);
        result.setStrCardNos(cardNos);
        return result;
    }

    private TokenizationTran bindTran(JsonObject js) {
        TokenizationTran result = new TokenizationTran();
        result.setTranId(js.getString("S_TRAN_ID"));
        result.setType(js.getString("S_TYPE"));
        result.setChannelID(js.getString("S_CHANNEL_ID"));
        result.setChannelPaymentID(js.getString("S_CHANNEL_PAYMENT_ID"));
        result.setGroupID(js.getString("S_GROUP_ID"));
        result.setUserID(js.getString("S_USER_ID"));
        result.setEmail(js.getString("S_EMAIL"));
        result.setFirstName(js.getString("S_FIRST_NAME"));
        result.setLastName(js.getString("S_LAST_NAME"));
        result.setMobile(js.getString("S_MOBILE"));
        result.setUserState(js.getString("S_USER_STATE"));
        result.setInstrumentID(js.getString("S_INSTRUMENT_ID"));
        result.setNameOneCard(js.getString("S_NAME_ON_CARD"));
        result.setCardNo(js.getString("S_CARD_NO"));
        result.setMerchantID(js.getString("S_MERCHANT_ID"));
        result.setTranRef(js.getString("S_MERCHANT_TXN_REF"));
        result.setAmount(js.getDouble("N_AMOUNT"));
        result.setCurrency(js.getString("S_CURRENCY"));
        result.setOrderInfo(js.getString("S_ORDER_INFO"));
        result.setReturnUrl(js.getString("S_RETURN_URL"));
        result.setCancelUrl(js.getString("S_CANCEL_URL"));
        result.setPspId(js.getString("S_PSP_ID"));
        result.setPspResponse(js.getString("D_PSP_RESPONSE") == null ? null : Timestamp.valueOf(js.getString("D_PSP_RESPONSE") ));
        result.setPspResponseCode(js.getString("S_PSP_RESPONSE_CODE"));
        result.setPspResponseData(js.getString("S_PSP_RESPONSE_DATA"));
        result.setTranState(js.getString("S_TRAN_STATE"));
        result.setCreated(js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE") ));
        result.setUpdated(js.getString("D_UPDATE") == null ? null : Timestamp.valueOf(js.getString("D_UPDATE") ));
        result.setExpired(js.getString("D_EXPIRE") == null ? null : Timestamp.valueOf(js.getString("D_EXPIRE") ));
        return result;
    }


    private static final String LIST_USER_TOKENIZATION = "{call pkg_token.search_user_card(?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRAN_TOKENIZATION = "{call pkg_token.search_tran(?,?,?,?,?,?,?,?,?,?,?,?)}";


}
