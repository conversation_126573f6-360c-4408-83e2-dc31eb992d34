package com.onepay.ma.service.service.international;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class SSInternationalTransactionService {

    public static Observable<Void> updateOrder(SQLConnection connOnline, String transactionId, String status, String description) {

        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(status)
                .add(description==null?"":description);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(UPDATE_ORDER, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(3);
            if(code != 200) {
                LOGGER.log(Level.SEVERE, "ERROR ON UPDATE ORDER APPROVAL " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;
        });
    }

    public static Observable<SamsungInternationalTransaction> get(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SAMSUNG_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            SamsungInternationalTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindSamsungTransaction(jsonObject);

            return transaction;

        });
    }

    public static Observable<SamsungInternationalTransaction> getAuthorizeTransById(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SAMSUNG_AUTHORIZE_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            SamsungInternationalTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindSamsungTransaction(jsonObject);

            return transaction;

        });
    }

    public static Observable<SamsungInternationalTransaction> getCaptureTransById(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SAMSUNG_CAPTURE_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            SamsungInternationalTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindSamsungTransaction(jsonObject);

            return transaction;

        });
    }

    /**
     * convert data from result set to samsung transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static SamsungInternationalTransaction bindSamsungTransaction(JsonObject rs) {

        String cardNumber = rs.getString("S_CARD_NO");
        String cardDate = rs.getString("S_CARD_EXP");
        String bankId = rs.getString("S_BANK_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
        String currency = rs.getString("S_CURRENCY");
        String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
        String cardType = rs.getString("S_CARD_TYPE");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
        String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
        int transactionId = rs.getInteger("EXT_ID");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE"));

//        try {
//            transactionTime = new Timestamp(formatter.parse(rs.getString("EXT_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        double total = rs.getDouble("EXT_AMOUNT");
        String transactionType = rs.getString("EXT_TYPE");
        String transactionStatus = rs.getString("EXT_STATUS");
        String transactionRefNumber = rs.getString("EXT_REF_NUMBER");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        String binCountry = rs.getString("BIN_COUNTRY");
        String avsResultCode = rs.getString("S_AVS_RESULT_CODE");
        String address = rs.getString("S_ADDRESS");
        String state = rs.getString("S_STATE_PROVINCE");
        String zipCode = rs.getString("S_ZIP_POSTAL_CODE");
        String country = rs.getString("S_COUNTRY");
        String city = rs.getString("S_CITY_TOWN");
        String verificationSecurityLevel = rs.getString("S_VERIFICATION_SECURITY_LEVEL");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String cscResult = rs.getString("S_CSCRESULT_CODE");
        String enrolled3DS = rs.getString("S_3DS_ENROLLED");
        String ip = rs.getString("S_IP");
        String ipProxy = rs.getString("S_IP_PROXY");
        String operator = rs.getString("S_OPERATOR_ID");
        double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
        boolean canVoid = rs.getInteger("CAN_VOID") != null && rs.getInteger("CAN_VOID") == 1 ? true : false;
        boolean requiredAvs = rs.getInteger("N_REQUIRED_AVS") != null && rs.getInteger("N_REQUIRED_AVS") == 1 ? true : false;
        String eci = rs.getString("S_3DS_ECI");
        String ticketNumber=rs.getString("S_TICKET_NUMBER");
        String authorizationAmount =rs.getString("S_AUTHORISED_AMOUNT");
        String commercialCardIndicator =rs.getString("S_COMMERCIAL_CARD_INDICATOR");
        String orderStatus = rs.getString("S_REVIEW_STATE");
        int nFraud = rs.getInteger("N_FRAUD") == null ? 0 : rs.getInteger("N_FRAUD");
        String orderDesc = rs.getString("S_REVIEW_DESC");


        //PP
        String status3ds = rs.getString("S_3DS_STATUS");
        String riskOverallResult = rs.getString("S_RISK_OVERALL_RESULT");
        String xid = rs.getString("S_3DS_XID");
        String cardLevelIndicator = rs.getString("S_CARD_LEVEL_INDICATOR");



        SamsungInternationalTransaction transaction = new SamsungInternationalTransaction();
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(transactionTime);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ip);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_reference(transactionRef);
        transaction.setBin_country(binCountry);
        transaction.setCsc_result_code(cscResult);
        transaction.setMerchant_id(merchantId);
        transaction.setOperator(operator);
        transaction.setEnrolled_3ds(enrolled3DS);
        transaction.setTransaction_status(transactionStatus);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_ref_number(transactionRefNumber);
        transaction.setVerification_security_level(verificationSecurityLevel);
        transaction.setResponse_code(responseCode);
        transaction.setIp_proxy(ipProxy);
        transaction.setBank_id(bankId);
        transaction.setCan_void(canVoid);
        transaction.setIs_required_avs(requiredAvs);
        transaction.setEci(eci);
        transaction.setAdvance_status(rs.getString("S_ADVANCE_STATUS"));
        transaction.setTicket_number(ticketNumber);
        transaction.setOrder_status(orderStatus);;
        transaction.setOrder_desc(orderDesc);
        transaction.setN_fraud(nFraud);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        if (acquirerId == 1) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 2) {
            acquirer.setAcquirer_short_name("VietinBank");
            acquirer.setAcquirer_name("VietinBank");
        } else if (acquirerId == 3){
            acquirer.setAcquirer_short_name("CUP");
            acquirer.setAcquirer_name("CUP");
        } else if (acquirerId == 4){
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 5){
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 6){
            acquirer.setAcquirer_short_name("Paypal");
            acquirer.setAcquirer_name("Paypal");
        } else if (acquirerId == 7){
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 8){
            acquirer.setAcquirer_short_name("BIDV");
            acquirer.setAcquirer_name("BIDV");
        }else if (acquirerId == 9){
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        }else if (acquirerId == 10){
            acquirer.setAcquirer_short_name("Techcombank");
            acquirer.setAcquirer_name("Techcombank");
        }else if (acquirerId == 11){
            acquirer.setAcquirer_short_name("VPB");
            acquirer.setAcquirer_name("VPB");
        }

        transaction.setAcquirer(acquirer);

        AvsData avsData = new AvsData();
        avsData.setAddress(address);
        avsData.setCountry(country);
        avsData.setCity(city);
        avsData.setProvince(state);
        avsData.setResult_code(avsResultCode);
        avsData.setZip_code(zipCode);

        transaction.setAvs(avsData);

        InternationalAmount amount = new InternationalAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(refundTotal);

        transaction.setAmount(amount);

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);
        authenticationData.setAuthorization_amount(authorizationAmount);

        transaction.setAuthentication(authenticationData);


        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setCommercical_card(commercialCard);
        card.setCommercial_card_indicator(commercialCardIndicator);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);

        //PP
        transaction.setXid(xid);
        transaction.setRiskOverAllResult(riskOverallResult);
        transaction.setStatus3ds(status3ds);
        transaction.setCardLevelIndicator(cardLevelIndicator);



        // SAMSUNG
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

        return transaction;
    }



    private static final String UPDATE_ORDER = "{call PKG_ONECREDIT.UPDATE_ORDER_STATUS(?,?,?,?,?)}";

    private static final String SAMSUNG_TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_SAMSUNG_TRANS_BY_ID(?,?,?,?) }";

    private static final String SAMSUNG_AUTHORIZE_TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_SS_AUTHORIZE_TXN_BY_ID(?,?,?,?) }";

    private static final String SAMSUNG_CAPTURE_TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_SS_CAPTURE_TXN_BY_ID(?,?,?,?) }";

    private static final Logger LOGGER = Logger.getLogger(SSInternationalTransactionService.class.getName());
}
