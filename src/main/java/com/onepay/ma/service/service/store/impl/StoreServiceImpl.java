package com.onepay.ma.service.service.store.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.shop.Store;
import com.onepay.ma.service.service.store.StoreService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 27-Sep-17.
 */
@Service
public class StoreServiceImpl implements StoreService {


    @Override
    public Observable<List<Store>> getStoresByIds(SQLConnection connOnline, String ids) {
        JsonArray inParams = new JsonArray()
                .add(ids);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_STORE_BY_IDS, inParams, outParams).map(result -> {
            List<Store> storeList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Store store = new Store();
                    store.setStore_id(jsonObject.getString("S_STORE_ID"));
                    store.setStore_name(jsonObject.getString("S_STORE_NAME"));
                    store.setMerchant_id(jsonObject.getString("S_MERCHANT_ID"));
                    storeList.add(store);
                }
            }

            // Return.
            return storeList;
        });
    }

    @Override
    public Observable<BaseList<Store>> searchStoresByMerchant(SQLConnection connOnline, String keywords, int page, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_STORE, inParams, outParams).map(result -> {
            List<Store> storeList = new ArrayList<>();
            BaseList<Store> stores = new BaseList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Store store = bindStore(jsonObject, merchantId);
                    storeList.add(store);
                }
                stores.setTotal_items(result.getOutput().getInteger(4));
            } else {
                stores.setTotal_items(0);
            }
            stores.setList(storeList);

            // Return.
            return stores;
        });
    }

    @Override
    public Observable<List<Store>> listStoreIdsByMerchant(SQLConnection connBackUp, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_STORE_BY_MERCHANT, inParams, outParams).map(result -> {
            List<Store> storeList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Store store = new Store();
                    store.setStore_id(jsonObject.getString("S_STORE_ID"));
                    store.setMerchant_id(jsonObject.getString("S_MERCHANT_ID"));
                    storeList.add(store);
                }
            }

            // Return.
            return storeList;
        });
    }

    private Store bindStore(JsonObject jsonObject, String merchantId) {
        Store store = new Store();
        store.setStore_id(jsonObject.getString("S_STORE_ID"));
        store.setStore_name(jsonObject.getString("S_STORE_NAME"));
        store.setMerchant_id(merchantId);

        return  store;
    }

    private static final String LIST_STORE = "{call PKG_STORE.get_list_store_mpay_page(?,?,?,?,?,?,?,?)}";

    private static final String LIST_STORE_BY_MERCHANT = "{call PKG_STORE.get_list_by_merchant(?,?,?,?)}";

    private static final String LIST_STORE_BY_IDS = "{call PKG_STORE.get_list_by_ids(?,?,?,?)}";
}
