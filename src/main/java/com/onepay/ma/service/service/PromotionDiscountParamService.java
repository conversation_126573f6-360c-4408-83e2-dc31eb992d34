package com.onepay.ma.service.service;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionDiscountParamService {
    Observable<List<Map<String,Object>>> list(SQLConnection sqlConnPr, int discountId);

    Observable<Integer> insert(SQLConnection sqlConnPr, int discountId, Map<String, Object> discountParam);
}
