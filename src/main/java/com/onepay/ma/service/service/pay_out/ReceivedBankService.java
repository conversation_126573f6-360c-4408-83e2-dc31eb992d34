package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.pay_out.ModelDto;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * Handle for ReceivedBankService
 * by Tiennv
 * 17/11/2020
 */
public interface ReceivedBankService {

    
    /**
     * Handle for search a list ReceivedBankService
     * by Tiennv
     * 17/11/2020
     */
    Observable<List<ModelDto>> getListReceivedBank(SQLConnection connection);

    List<ModelDto> getListReceivedBank(Connection backupConn) throws SQLException;
}
