package com.onepay.ma.service.service.pay_collect;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonObject;
import rx.Observable;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/7/2020
 * Time: 10:32 AM
 * To change this ma-web.
 */

public interface PartnerConfigService {
    Observable<JsonObject> getPartnerConfig(SQLConnection connection, String partner_id);
}
