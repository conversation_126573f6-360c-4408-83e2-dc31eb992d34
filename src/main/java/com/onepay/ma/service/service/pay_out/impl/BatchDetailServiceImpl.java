package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_out.BatchDetailAuthenRes;
import com.onepay.ma.service.models.pay_out.BatchDetailQueryDto;
import com.onepay.ma.service.models.pay_out.FundsTransBatchReq;
import com.onepay.ma.service.models.pay_out.FundsTransBatchRes;
import com.onepay.ma.service.service.pay_out.BatchDetailService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class BatchDetailServiceImpl implements BatchDetailService {

    private static final Logger LOGGER = Logger.getLogger(BatchDetailServiceImpl.class.getName());

    private static final String GET_LIST_FUNDS_TRANS_BATCH = "{ ? = call ONEPAYOUT.g_funds_transfer_batch(?,?,?,?,?,?,?,?) }";

    @Override
    public FundsTransBatchRes insert(Connection connection, FundsTransBatchReq fundsTransBatch) throws SQLException {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(fundsTransBatch.getType())
                .add(fundsTransBatch.getSwiftCode() == null ? "" : fundsTransBatch.getSwiftCode())
                .add(fundsTransBatch.getAccountNumber() == null ? "" : fundsTransBatch.getAccountNumber())
                .add(fundsTransBatch.getCardNumber() == null ? "" : fundsTransBatch.getCardNumber())
                .add(fundsTransBatch.getAccountName() == null ? "" : fundsTransBatch.getAccountName())
                .add(fundsTransBatch.getAmount() == null ? 0 : fundsTransBatch.getAmount())
                .add(fundsTransBatch.getCurrency() == null ? "VND" : fundsTransBatch.getCurrency())
                .add(fundsTransBatch.getFundsTransferInfo() == null ? "" : fundsTransBatch.getFundsTransferInfo())
                .add(fundsTransBatch.getFundsTransferId() == null ? "" : fundsTransBatch.getFundsTransferId())
                .add(fundsTransBatch.getRemark() == null ? "" : fundsTransBatch.getRemark())
                .add(fundsTransBatch.getState() == null ? "" : fundsTransBatch.getState())
                .add(fundsTransBatch.getReason() == null ? "" : fundsTransBatch.getReason())
                .add(fundsTransBatch.getBatchId() == null ? "" : fundsTransBatch.getBatchId())
                .add(fundsTransBatch.getMerchantId() == null ? "" : fundsTransBatch.getMerchantId())
                .add(fundsTransBatch.getMerchantAccount() == null ? "" : fundsTransBatch.getMerchantAccount());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, "{? = call ONEPAYOUT.i_funds_transfer_batch(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}", inParams, outParams);

            String error = callableStatement.getString(1);
            rs = (ResultSet) callableStatement.getObject(2);
            if (error != null && !"".equals(error)) {
                LOGGER.log(Level.SEVERE, "ERROR ON INSERT FUNDS TRANSFER");
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                FundsTransBatchRes modelDto = null;
                while (rs.next()) {
                    modelDto = new FundsTransBatchRes();
                    modelDto.setId(rs.getString("S_ID"));
                    modelDto.setType(rs.getString("S_TYPE"));
                    modelDto.setSwiftCode(rs.getString("S_SWIFT_CODE"));
                    modelDto.setAccountNumber(rs.getString("S_ACCOUNT_NUMBER"));
                    modelDto.setCardNumber(rs.getString("S_CARD_NUMBER"));
                    modelDto.setHolderName(rs.getString("S_RECEIPT_ACCOUNT_NAME"));
                    modelDto.setAmount(rs.getLong("N_AMOUNT"));
                    modelDto.setCurrency(rs.getString("S_CURRENCY"));
                    modelDto.setFundsTransferInfo(rs.getString("S_FUNDS_TRANSFER_INFO"));
                    modelDto.setFundsTransferId(rs.getString("S_FUNDS_TRANSFER_ID"));
                    modelDto.setRemark(rs.getString("S_REMARK"));
                    modelDto.setState(rs.getString("S_STATE"));
                    Timestamp createdDate = rs.getString("D_CREATED") == null ? null : Timestamp.valueOf(rs.getString("D_CREATED"));
                    modelDto.setCreatedDate(createdDate);
                    modelDto.setUpdatedDate(rs.getString("D_UPDATED"));
                    modelDto.setMerchantAccount(rs.getString("S_MERCHANT_ACCOUNT"));
                    modelDto.setMerchantId(rs.getString("S_MERCHANT_ID"));
                }
                return modelDto;
            }

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
    }

    @Override
    public Void updateMsgState(Connection backupConn, String id, String state,String message) throws SQLException {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(id)
                .add(state)
                .add(message);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull();

        CallableStatement callableStatement = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(backupConn, "{? = call ONEPAYOUT.u_funds_transfer_batch_state(?,?,?,?)}", inParams, outParams);
            String error = callableStatement.getString(1);
            ResultSet rs = (ResultSet) callableStatement.getObject(2);
            if (error != null && !"".equals(error)) {
                LOGGER.log(Level.SEVERE, "[UPDATE FUNDS TRANSFER MSG STATE  ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }

        }
        return null;
    }

    @Override
    public Observable<FundsTransBatchRes> updateMsgStateSqlConnect(SQLConnection sqlConnection, String id, String state, String message){
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(id)
                .add(state).add(message);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2("{? = call ONEPAYOUT.u_funds_transfer_batch_state(?,?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[UPDATE BATCH STATUS TOTAL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            FundsTransBatchRes fundsTransBatchList = null;
            for (JsonObject jsonObject : rs.getRows()) {
                fundsTransBatchList = bind(jsonObject);
            }
            return fundsTransBatchList;

        });
    }

    @Override
    public Observable<BaseList<FundsTransBatchRes>> search(SQLConnection sqlConnection, Map<String, String> mIn) {
        BaseList<FundsTransBatchRes> baseList = new BaseList<>();

        return this.total(sqlConnection, mIn).flatMap(total -> {
            baseList.setTotal_items(total);
            return this.searchData(sqlConnection, mIn).map(data -> {
                baseList.setList(data);
                return baseList;
            });
        });
    }

    private Observable<List<FundsTransBatchRes>> searchData(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(mIn.get("batch_id"))
                .add(mIn.get("swift_code"))
                .add(mIn.get("holder_name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_LIST_FUNDS_TRANS_BATCH, inParams, outParams).map(result -> {
            List<FundsTransBatchRes> fundsTransBatchList = new ArrayList<>();
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH FUNDS TRANSFER BATCH ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                fundsTransBatchList.add(bind(jsonObject));
            }
            return fundsTransBatchList;
        });
    }

    @Override
    public Observable<Integer> total(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(mIn.get("batch_id"))
                .add(mIn.get("swift_code"))
                .add(mIn.get("holder_name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2(GET_LIST_FUNDS_TRANS_BATCH, inParams, outParams).map(result -> {
            Integer total = 0;
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH FUNDS TRANSFER BATCH TOTAL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }

    @Override
    public Observable<List<FundsTransBatchRes>> getByBatchId(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(mIn.get("batch_id"))
                .add(mIn.getOrDefault(ParamsPool.STATE,""));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2("{ ? = call ONEPAYOUT.g_batch_detail_by_batch_id(?,?,?) }", inParams, outParams).map(result -> {
            List<FundsTransBatchRes> fundsTransBatchList = new ArrayList<>();
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[GET BATCH DETAIL BY BATCH ID ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                fundsTransBatchList.add(bind(jsonObject));
            }
            return fundsTransBatchList;
        });
    }

    @Override
    public Observable<BaseList<BatchDetailAuthenRes>> getBatchDetailAuthen(SQLConnection sqlConnection, String batchID) {
        BaseList<BatchDetailAuthenRes> baseList = new BaseList<>();
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(batchID);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull();
        return sqlConnection.callWithParamsObservable2("{ ? = call ONEPAYOUT.g_batch_detail_authen_by_batchid(?,?) }", inParams, outParams).map(result -> {
            List<BatchDetailAuthenRes> listAuthen = new ArrayList<>();
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH FUNDS TRANSFER BATCH ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            BatchDetailAuthenRes batchDetailAuthenRes;
            for (JsonObject jsonObject : rs.getRows()) {
                batchDetailAuthenRes = new BatchDetailAuthenRes();
                batchDetailAuthenRes.setBatchDetailId(jsonObject.getString("S_ID"));
                batchDetailAuthenRes.setCheckApproval(jsonObject.getInteger("N_CHECK_APPROVAL"));
                batchDetailAuthenRes.setCheckOperator(jsonObject.getInteger("N_CHECK_OPERATOR"));
                listAuthen.add(batchDetailAuthenRes);
            }
            baseList.setList(listAuthen);
            baseList.setTotal_items(listAuthen.size());
            return baseList;
        });

    }

    @Override
    public ResultSet downloadFile(Connection connection, BatchDetailQueryDto query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getBatchId())
                .add(query.getBankSender())
                .add(query.getHolderName())
                .add(query.getState())
                .add(query.getPageSize())
                .add(query.getPage());

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, GET_LIST_FUNDS_TRANS_BATCH, inParams, outParams);
            String resultCode = callableStatement.getString(1);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BATCH DETAIL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(2);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return  rs;
    }

    private FundsTransBatchRes bind(JsonObject js) {
        FundsTransBatchRes fundsTransBatch = new FundsTransBatchRes();
        try {
            fundsTransBatch.setId(js.getString("S_ID"));
            Timestamp createdDate = js.getString("D_CREATED") == null ? null : Timestamp.valueOf(js.getString("D_CREATED"));
            fundsTransBatch.setCreatedDate(createdDate);
            fundsTransBatch.setType(js.getString("S_TYPE"));
            fundsTransBatch.setSwiftCode(js.getString("S_SWIFT_CODE"));
            fundsTransBatch.setBankName(js.getString("BANKNAME"));
            fundsTransBatch.setAccountNumber(js.getString("S_ACCOUNT_NUMBER"));
            fundsTransBatch.setCardNumber(js.getString("S_CARD_NUMBER"));
            fundsTransBatch.setHolderName(js.getString("S_RECEIPT_ACCOUNT_NAME"));
            fundsTransBatch.setAmount(js.getLong("N_AMOUNT"));
            fundsTransBatch.setCurrency(js.getString("S_CURRENCY"));
            fundsTransBatch.setFundsTransferInfo(js.getString("S_FUNDS_TRANSFER_INFO"));
            fundsTransBatch.setFundsTransferId(js.getString("S_FUNDS_TRANSFER_ID"));
            fundsTransBatch.setAccountId(js.getString("S_ACCOUNT_ID"));
            fundsTransBatch.setRemark(js.getString("S_REMARK"));
            fundsTransBatch.setState(js.getString("S_STATE"));
            fundsTransBatch.setReason(js.getString("S_REASON"));
            fundsTransBatch.setBatchId(js.getString("S_BATCH_ID"));
            fundsTransBatch.setMerchantId(js.getString("S_MERCHANT_ID"));
            fundsTransBatch.setMerchantAccount(js.getString("S_MERCHANT_ACCOUNT"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fundsTransBatch;
    }
}
