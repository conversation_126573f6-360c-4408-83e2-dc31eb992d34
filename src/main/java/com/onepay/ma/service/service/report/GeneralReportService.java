package com.onepay.ma.service.service.report;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.report.GeneralReport;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

public interface GeneralReportService {
    Observable<BaseList<GeneralReport>> searchGeneralReport(SQLConnection sqlOnline, SQLConnection sqlReadonly,SQLConnection backup,GeneralReportQuery query);

    Observable<Integer> totalRecord(SQLConnection sqlOnline, SQLConnection sqlReadonly,SQLConnection backup,GeneralReportQuery query);
    ResultSet download(Connection connReadOnly, GeneralReportQuery query) throws SQLException;
    ResultSet downloadDomesticRequestRefundData(Connection connOnline, GeneralReportQuery query) throws SQLException;
    
    Observable<String> getPurchaseByRef(SQLConnection sqlOnline, String merchantId, String transactionRef);
    Observable<Integer> getPurchaseByMspId(SQLConnection sqlOnline, String transactionId);
    Observable<String> getRefundByRef(SQLConnection sqlOnline, String merchantId, String transactionRef);


    default ResultSet downloadOnline(Connection connOnline, GeneralReportQuery query) throws SQLException {
        return  this.download(connOnline, query);
    }

    ResultSet downloadBackp(Connection connBackup, GeneralReportQuery parameter) throws SQLException;

//    default Map<String, GeneralReport> mapByIdsDownload(Connection connOnline, String transIds, GeneralReportQuery parameter) throws SQLException {
//        ResultSet rs = this.downloadOnlineByIds(connOnline, transIds, parameter);
//        HashMap<Integer, DomesticTransaction> map = new HashMap();
//        while (rs.next()) {
//
//            int acquirerId = rs.getInt("N_ACQUIRER_ID");
//            String acquirerName = rs.getString("S_ACQUIRER_NAME");
//            String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");
//            String orderInfo = rs.getString("S_ORDER_INFO");
//            double amount = rs.getDouble("N_AMOUNT");
//            String currency = rs.getString("S_CURRENCY_CODE");
//            Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
//
//            DomesticTransaction transaction = new DomesticTransaction();
//
//            Acquirer acquirer = new Acquirer();
//            acquirer.setAcquirer_id(acquirerId);
//            acquirer.setAcquirer_name(acquirerName);
//            acquirer.setAcquirer_short_name(acquirerShortName);
//
//            DomesticAmount amountData = new DomesticAmount();
//            amountData.setCurrency(currency);
//            amountData.setTotal(amount);
//
//            transaction.setAcquirer(acquirer);
//            transaction.setOrder_info(orderInfo);
//            transaction.setAmount(amountData);
//            transaction.setTransaction_time(merchantTransactionDate);
//
//            map.put( rs.getInt("N_TRANSACTION_ID"), transaction);
//        }
//        return map;
//
//    }
}
