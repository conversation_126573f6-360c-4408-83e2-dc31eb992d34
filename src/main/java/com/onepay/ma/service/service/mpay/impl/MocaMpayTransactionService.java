package com.onepay.ma.service.service.mpay.impl;

import com.google.gson.Gson;
import com.ibm.icu.text.DateFormat;
import com.ibm.icu.text.SimpleDateFormat;
import com.onepay.ma.service.models.ManualRefundDescription;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.models.mpay.*;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.Instrument;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.sql.UpdateResult;
import oracle.jdbc.OracleTypes;
import org.apache.logging.log4j.util.Strings;
import org.bouncycastle.jcajce.provider.asymmetric.ec.SignatureSpi.ecCVCDSA224;
import org.springframework.stereotype.Service;

import groovy.json.JsonException;
import rx.Observable;
import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 7/5/2016.
 */
@Service
public class MocaMpayTransactionService {

    private static final String LIST_TRANSACTION_ONLINE = "{call PKG_MPAY.PAYMENT_LIST_DATA_111(?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_READONLY = "{call PKG_MPAY.PAYMENT_LIST_DATA_113(?,?,?,?,?,?,?)}";
    private static final String SEARCH_TRANSACTION = "{call PKG_MPAY.PAYMENT_SEARCH(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String MOCA_TRANSACTION_GET_BY_ID = "{call PKG_MPAY.MOCA_PAYMENT_GET_BY_ID(?,?,?,?)}"; //purchase detail
    private static final String TRANSACTION_GET_BY_ID_V2 = "{call PKG_MPAY.PAYMENT_GET_BY_ID_V2(?,?,?,?,?)}";
    private static final String TRANSACTION_GET_BY_ORDER_ID = "{call PKG_MPAY.PAYMENT_GET_BY_ORDER_ID(?,?,?,?)}";
    private static final String LIST_TRANSACTION_HISTORY = "{call PKG_MPAY.SEARCH_TRANSACTION_HIS(?,?,?,?,?)}";
    private static final String UPDATE_QR_TRANSACTION_STATUS_BY_ID = "{call PKG_MPAY.UPDATE_PURCHASE_STATUS_BY_ID(?,?,?,?)}";

    private static final String LIST_TRANSACTION_ONLINE_BY_IDS = "{call PKG_MPAY.payment_get_by_ids_2(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String MOCA_LIST_TRANSACTION_ONLINE_BY_IDS_V3 = "{call PKG_MPAY.moca_payment_get_by_ids(?,?,?,?,?,?,?,?,?,?,?,?)}"; //download

    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_REFUND_APPROVE.get_approval_history_2(?,?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(MocaMpayTransactionService.class.getName());
    // private static final String SEARCH_TRANSACTION_2 = "{call PKG_MPAY.PAYMENT_SEARCH_V5(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String MOCA_SEARCH_TRANSACTION_2 = "{call PKG_MPAY.MOCA_PAYMENT_SEARCH(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_REFUND_APPROVE.get_refund_total_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DOWNLOAD_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_download_2(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
   

    private final static Gson gson = new Gson();

    public static Observable<NewBaseList<MocaMpayTransaction>> list(SQLConnection connReadOnly, SQLConnection connOnline, MocaMpayTransactionQuery query) {

        NewBaseList<MocaMpayTransaction> transactions = new NewBaseList<>();
        int basePageSize = query.getPageSize();

        // Get online transactions
        return listTransactionOnline(connOnline, query).flatMap(onlineTrans -> {
            int pageSize = query.getPageSize() - onlineTrans.size();
            query.setPageSize(pageSize);

            // Get readonly transaction
            return listTransactionReadonly(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {

                onlineTrans.addAll(readOnlyTrans);
                // Mark number index
                int index = 1;
                for (MocaMpayTransaction transaction : onlineTrans) {
                    transaction.setRowNum(index + (query.getPage() * basePageSize));
                    index++;
                }

                // set & Return.
                transactions.setList(onlineTrans);

                return Observable.just(transactions);
            });
        });
    }

    public static Observable<NewBaseList<MocaMpayTransaction>> search(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, MocaMpayTransactionQuery query) {

        // LOGGER.log(Level.INFO, "query condition: " + gson.toJson(query));
        NewBaseList<MocaMpayTransaction> transactions = new NewBaseList<>();
        int basePageSize = query.getPageSize();
        return getListTotalTransRefundBackup(connBackup, query).flatMap(totalApproval -> {
            return MocaMpayTransactionService.transMapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), query).flatMap(totalPurchaseMap -> {
                return getListTotalTransaction(connOnline, query, -1).flatMap(total1 -> {
                    return getListTotalTransaction(connReadOnly, query, 1).flatMap(total2 -> {
                        Integer totalBackup = countFilterById(totalPurchaseMap, totalApproval);
                        transactions.setTotalItems(total1 + total2 + totalBackup);
                        LOGGER.log(Level.INFO, ">> TOTAL_MPAY_BACKUP: " + totalBackup);
                        LOGGER.log(Level.INFO, ">> TOTAL_MPAY_ONLINE: " + total1);
                        LOGGER.log(Level.INFO, ">> TOTAL_MPAY_READONLY: " + total2);

                        // Get online transactions
                        return getListTransactionBackup(connBackup, query).flatMap(refundApproval -> {
                            // JOIN purchase INFO -------> filter != null
                            List<MocaMpayTransaction> refundApproval2 = refundApproval
                                    .stream()
                                    .map(approval -> {
                                        return joinApproveWithPurchase(approval, totalPurchaseMap.get(approval.getTransactionId()));
                                    })
                                    .filter(domesticRefund -> domesticRefund != null)
                                    .collect(Collectors.toList());
                            query.setOffset(totalBackup);
                            return searchTransaction(connOnline, query, -1).flatMap(onlineTrans -> {
                                // Get readonly transaction
                                query.setOffset(totalBackup + total1);
                                return searchTransaction(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {
                                    List<MocaMpayTransaction> l = new ArrayList<>();
                                    l.addAll(refundApproval2);
                                    l.addAll(onlineTrans);
                                    l.addAll(readOnlyTrans);

                                    l.sort((o1, o2) -> {
                                        return o2.getCreateTime().compareTo(o1.getCreateTime());
                                    });
                                    LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_MPAY_BACKUP: " + refundApproval2.size());
                                    LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_MPAY_ONLINE: " + onlineTrans.size());
                                    LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_MPAY_READONLY: " + readOnlyTrans.size());

                                    // Mark number index
                                    int index = 1;
                                    for (MocaMpayTransaction transaction : l) {
                                        transaction.setRowNum(index + (query.getPage() * basePageSize));
                                        index++;
                                    }
                                    // set & Return.
                                    transactions.setList(l);
                                    return Observable.just(transactions);
                                });
                            });
                        });
                    });
                });
            });
        });
    }

    private static Integer countFilterById(Map<String, MocaMpayTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(String.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    public static MocaMpayTransaction joinApproveWithPurchase(MocaMpayTransaction approve, MocaMpayTransaction purchase) {
        if (purchase == null) {
            return null;
        }

        MocaMpayTransaction result = approve;
        result.setOrderInfo(purchase.getOrderInfo());
        // result.setA(purchase.getAcquirer());
        result.setBankTransId(purchase.getBankTransId());
        result.setMerchantName(purchase.getMerchantName());
        result.setCustomerTransId(purchase.getCustomerTransId());
        result.setMasking(purchase.getMasking());
        result.setAppName(purchase.getAppName());
        result.setOrderInfo(purchase.getOrderInfo());
        result.setAcqCode(purchase.getAcqCode());
        result.setClient(purchase.getClient());
        result.setQrId(purchase.getQrId());
        result.setAppName(purchase.getAppName());
        result.setInstrument(purchase.getInstrument());
        result.setCreateTime(purchase.getCreateTime());
        // result.setAmount(purchase.getAmount());
        result.setAcquirer(purchase.getAcquirer());
        // result.setPaymentAmount(purchase.getPaymentAmount());

        //caculate payment Amount request refund
        MpayAmount amount = new MpayAmount();
        amount.setCurrency(purchase.getAmount().getCurrency());
        amount.setTotal(approve.getAmount().getTotal());
        result.setAmount(amount);
        double paymentAmount = approve.getAmount().getTotal(); //if not moca case  = request refund amount
        double inputAmount = approve.getAmount().getTotal();
        double originalAmount = purchase.getAmount().getTotal();
        // double refundedAmount = purchase.getAmount().getRefundTotal() != null ? purchase.getAmount().getRefundTotal() : 0;
        if (!"".equals(purchase.getOfferCode())){
            if(purchase.getOfferDiscountAmount() != null || purchase.getOfferDiscountAmount() != 0 
                || purchase.getPaymentAmount() != null || purchase.getPaymentAmount() != 0
                // || purchase.getPartnerDiscountRate() != null || purchase.getPartnerDiscountRate() != 0 || 
                // purchase.getMerchantDiscountRate() != null || purchase.getMerchantDiscountRate() != 0
                ){
                    // double partnerDiscountRate = purchase.getPartnerDiscountRate();
                    // double merchantDiscountRate = purchase.getMerchantDiscountRate();
                    double offerDiscountAmount = purchase.getOfferDiscountAmount();
                    double paymentAmountPurchase = purchase.getPaymentAmount();
                    // paymentAmount = caculateActualAmount(inputAmount, originalAmount, partnerDiscountRate, merchantDiscountRate, offerDiscountAmount );
                    paymentAmount = caculateActualAmount(inputAmount, paymentAmountPurchase, offerDiscountAmount );
            }
        }
        result.setPaymentAmount(paymentAmount);
        return result;
    }

    public static Observable<List<String>> getListTotalTransRefundBackup(SQLConnection connBackup, MocaMpayTransactionQuery parameter) {
        String status = "2";
        if (parameter.getStatus() == null || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("401".equalsIgnoreCase(parameter.getStatus()) || "405".equalsIgnoreCase(parameter.getStatus())) {
            status = parameter.getStatus();
        }

        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(status)
                .add(parameter.getCurrency())
                .add(null != parameter.getTransType() ? parameter.getTransType() : "")
                .add(RefundData.Type.QR.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_TOTAL_B, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(12);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL MPAY REFUND ERROR : " + resultSet.getOutput().getString(13));
                throw IErrors.DOMESTIC_SERVER_ERROR;
            }

            List<String> result = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(11).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result.add(jsonObject.getString("N_ORIGINAL_ID"));
            }
            return result;
        });
    }

    /**
     * get transaction data online database
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    public static Observable<List<MocaMpayTransaction>> getListTransactionBackup(SQLConnection connBackup, MocaMpayTransactionQuery parameter) {
        String status = "2";
        if (parameter.getStatus() == null || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("401".equalsIgnoreCase(parameter.getStatus()) || "405".equalsIgnoreCase(parameter.getStatus())) {
            status = parameter.getStatus();
        }
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(status)
                .add(parameter.getCurrency())
                .add(null != parameter.getTransType() ? parameter.getTransType() : "")
                .add(RefundData.Type.QR.getValue())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(result -> {
            List<MocaMpayTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(13).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MocaMpayTransaction transaction = bindApproval(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    private static MocaMpayTransaction bindApproval(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        String originalId = rs.getString("N_ORIGINAL_ID");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInteger("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");

        String subData = rs.getString("S_DATA");
        String note = null;
        JsonObject jsonObject = null;
        try {
            jsonObject = new JsonObject(subData);
        } catch (JsonException err) {
            Logger.getLogger("Error", err.toString());
        }

        try {
            note = jsonObject.getString("note");
        } catch (JsonException err) {
            Logger.getLogger("Error", err.toString());
        }
        MpayAmount amount = new MpayAmount();
        amount.setTotal(total);

        MocaMpayTransaction transaction = new MocaMpayTransaction();
        transaction.setAmount(amount);
        transaction.setMerchantId(merchantId);
        transaction.setOrderInfo(orderInfo);
        transaction.setStatus(String.valueOf(status));
        transaction.setMerchantTxnRef(transactionRef);
        transaction.setTransactionType(transactionType);
        transaction.setId(String.valueOf(transactionId));
        transaction.setTransactionId(originalId);
        transaction.setNote(note);
        return transaction;
    }


    public static Observable<MocaMpayTransaction> get(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(MOCA_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY TRANSACTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MocaMpayTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);
            // LOGGER.log(Level.INFO, "QR GRAP MOCA TRANS 112 "+transaction.toString());
            return transaction;

        });
    }

    public static Observable<Void> updateStatus(SQLConnection connOnline, String transactionId, String status) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(status);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(UPDATE_QR_TRANSACTION_STATUS_BY_ID, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(1);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "ERROR ON UPDATE QR TRANSACTION STATUS " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;
        });
    }

    public static Observable<List<MocaMpayTransaction>> getByOrderId(SQLConnection connOnline, String orderId) {

        JsonArray inParams = new JsonArray()
                .add(orderId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(TRANSACTION_GET_BY_ORDER_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY TRANSACTION BY ORDER ID ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransaction> transactions = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactions.add(bindTransaction(jsonObject));
                }
            }

            return transactions;
        });
    }

    public static Observable<Map<String, MocaMpayTransaction>> mapByIds(SQLConnection connOnline, String transIds, MpayRefundParameter parameter) {
        return listByIds(connOnline, transIds, parameter).map(mpaTrs -> {
            Map<String, MocaMpayTransaction> map = new HashMap<>();
            for (MocaMpayTransaction dt : mpaTrs) {
                map.put(dt.getTransactionId(), dt);
            }
            return map;
        });
    }

    public static Observable<List<MocaMpayTransaction>> listByIds(SQLConnection connOnline, String transIds, MpayRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(transIds)
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getOrderInfo())
                .add(parameter.getMasking())
                .add(parameter.getAppName())
                .add(parameter.getCardType())
                .add(parameter.getQrId())
                .add(parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(MOCA_LIST_TRANSACTION_ONLINE_BY_IDS_V3, inParams, outParams).map(resultSet -> {
            List<MocaMpayTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(10) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS MPAY TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MocaMpayTransaction transaction = bindTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }

    public static Observable<Map<String, MocaMpayTransaction>> transMapByIds(SQLConnection connOnline, String transIds, MocaMpayTransactionQuery parameter) {
        return listApprovalByIds(connOnline, transIds, parameter).map(mpaTrs -> {
            Map<String, MocaMpayTransaction> map = new HashMap<>();
            for (MocaMpayTransaction dt : mpaTrs) {
                map.put(dt.getTransactionId(), dt);
            }
            return map;
        });
    }

    public static Observable<List<MocaMpayTransaction>> listApprovalByIds(SQLConnection connOnline, String transIds, MocaMpayTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(transIds)
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getOrderInfo())
                .add(null != parameter.getMasking()? parameter.getMasking():"")
                .add(parameter.getAppName())
                .add("")
                .add(parameter.getQrId())
                .add(parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(MOCA_LIST_TRANSACTION_ONLINE_BY_IDS_V3, inParams, outParams).map(resultSet -> {
            List<MocaMpayTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(10) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS MPAY TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MocaMpayTransaction transaction = bindTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }

    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection connReadBackup, MocaMpayTransactionQuery query) {
        return getListTotalTransRefundBackup(connReadBackup, query).flatMap(totalApproval -> {
            return MocaMpayTransactionService.transMapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), query).flatMap(totalPurchaseMap -> {
                return getListTotalTransaction(connOnline, query, -1).flatMap(total1 -> {
                    return getListTotalTransaction(connReadOnly, query, 1).flatMap(total2 -> {
                        return Observable.just(total1 + total2 + countFilterById(totalPurchaseMap, totalApproval));
                    });
                });
            });
        });
    }

    public static ResultSet downloadBackup(Connection connBackup, MocaMpayTransactionQuery query) throws SQLException {
        return downloadBackupData(connBackup, query);
    }

    public static ResultSet downloadOnline(Connection connOnline, MocaMpayTransactionQuery query) throws SQLException {
        return getDonwloadData(connOnline, query, -1);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, MocaMpayTransactionQuery query) throws SQLException {
        return getDonwloadData(connReadOnly, query, 1);
    }

    public static Observable<List<MocaMpayTransactionHistory>> listHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackUp, String transactionId) {
        return getListTransactionHistory(connOnline, transactionId, -1).flatMap(transactionOnline -> {
            // transactions.setTotal_items(domesticTransactions.size());
            return getListTransactionHistory(connReadOnly, transactionId, 1).flatMap(transactionReadOnly -> {

                return getListApprovalHistoryBackup(connBackUp, transactionId).flatMap(approvalHistories -> {
                    List<MocaMpayTransactionHistory> transactionsFinal = new ArrayList<>();
                    transactionsFinal.addAll(transactionOnline);
                    transactionsFinal.addAll(transactionReadOnly);
                    transactionsFinal.addAll(approvalHistories);


                    transactionsFinal
                            .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                    // transactions.setTransactions(transactionsFinal);
                    return Observable.just(transactionsFinal);
                });
            });
        });
    }

    private static Observable<List<MocaMpayTransaction>> listTransactionOnline(SQLConnection sqlConnection, MocaMpayTransactionQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getMerchantId())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(LIST_TRANSACTION_ONLINE, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(4);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[LIST mPAY TRANSACTION ONLINE ERROR]: " + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    MocaMpayTransaction transaction = bindTransaction(jsonObject);
                    transactionList.add(transaction);
                }
            }
            return transactionList;
        });
    }



    private static Observable<List<MocaMpayTransaction>> listTransactionReadonly(SQLConnection sqlConnection, MocaMpayTransactionQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(query.getMerchantId())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(LIST_TRANSACTION_READONLY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(5);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[LIST mPAY TRANSACTION READONLY ERROR]: " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    MocaMpayTransaction transaction = bindTransaction(jsonObject);
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadBackupData(Connection connection, MocaMpayTransactionQuery parameter) throws SQLException {
        String status = "2";
        if (parameter.getStatus() == null || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("401".equalsIgnoreCase(parameter.getStatus()) || "405".equalsIgnoreCase(parameter.getStatus())) {
            status = parameter.getStatus();
        }

        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(status)
                .add(RefundData.Type.QR.getValue())
                .add(parameter.getTransType()==null?"":parameter.getTransType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL ]: PKG_REFUND_APPROVE.get_refund_download, IN: " + inParams + ", OUT: " + outParams);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, DOWNLOAD_TRANSACTION_BACKUP, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(11);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }


    public static Map<String, MocaMpayTransaction> transMapByIdsDownload(Connection connOnline, String transIds, MocaMpayTransactionQuery parameter) {
        List<MocaMpayTransaction> MocaMpayTransactions = transListByIdsDownload(connOnline, transIds, parameter);
        Map<String, MocaMpayTransaction> map = new HashMap<>();
        for (MocaMpayTransaction dt : MocaMpayTransactions) {
            map.put(dt.getTransactionId(), dt);
        }
        return map;
    }

    public static List<MocaMpayTransaction> transListByIdsDownload(Connection connOnline, String transIds, MocaMpayTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(transIds)
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getOrderInfo())
                .add(null != parameter.getMasking() ? parameter.getMasking(): "")
                .add(parameter.getAppName())
                .add("")
                .add(parameter.getQrId())
                .add(parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL ]: PKG_PAYMENT2.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);

        ResultSet rs;
        CallableStatement callableStatement = null;
        List<MocaMpayTransaction> result = new ArrayList<>();
        try {

            callableStatement = ExportDatasourceUtil.execute(connOnline, MOCA_LIST_TRANSACTION_ONLINE_BY_IDS_V3, inParams, outParams);
            if (callableStatement.getInt(11) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS DOMESTIC TRANSACTION ONLINE ERROR]: " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(10);
            while (rs.next()) {
                String id = rs.getString("S_ID");
                double total = rs.getDouble("N_AMOUNT");
                String currency = rs.getString("S_CURRENCY");
                String status = rs.getString("S_STATE");
                if ("Waiting for OnePAY's Approval".equalsIgnoreCase(status))
                    status = "Waiting for OnePay's Approval";
                String instrumentName = rs.getString("S_INSTRUMENT_NAME");
                String instrumentNumber = rs.getString("S_INSTRUMENT_NUMBER");
                String instrumentType = rs.getString("S_INSTRUMENT_TYPE");
                String instrumentBrandId = rs.getString("S_INSTRUMENT_BRAND_ID");
                String inputType = rs.getString("S_INPUT_TYPE");
                String terminalId = rs.getString("S_TERMINAL_ID");
                String merchantId = rs.getString("S_MERCHANT_ID");
                String merchantName = rs.getString("S_MERCHANT_NAME");
                String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT");
                String customerTransactionId = rs.getString("S_CUSTOMER_TRANS_ID");
                String orderInfo = rs.getString("S_ORDER_INFO");
                String merchantOrderRef = rs.getString("S_MERCH_ORDER_REF");
                String merchantTxnRef = rs.getString("S_MERCH_TXN_REF");
                String client = rs.getString("S_CLIENT_ID");
                Timestamp createTime = Timestamp.valueOf(rs.getString("D_CREATE"));
                String bankTransRef = rs.getString("S_BANK_TRANS_ID");
                String acqCode = rs.getString("S_MSP_ID");
                String clientData = rs.getString("S_DATA");
                JsonObject jClientData = null;
                Double paymentAmount = rs.getDouble("N_SETTLEMENT_AMOUNT");

                try {
                    if (Objects.nonNull(clientData) && !Strings.isBlank(clientData) && !Strings.isEmpty(clientData)) {
                        jClientData = new JsonObject(clientData);
                    }
                } catch (Exception e) {
                    Logger.getLogger("Client data parse error", e.toString());
                }

                MpayAcquirer acquirer = new MpayAcquirer();
                acquirer.setAcquirerName(rs.getString("S_INSTRUMENT_TYPE"));
                String txtExpireTime = rs.getString("D_EXPIRE");
                Timestamp expireTime = null;
                if (txtExpireTime != null) {
                    expireTime = Timestamp.valueOf(rs.getString("D_EXPIRE"));
                }
                String instrumentId = rs.getString("S_INSTRUMENT_ID");
                // Instrument
                Instrument instrument = new Instrument();
                instrument.setName(instrumentName);
                instrument.setNumber(instrumentNumber);
                instrument.setType(instrumentType);
                instrument.setId(instrumentId);
                instrument.setBrandId(instrumentBrandId);

                // Amount
                MpayAmount amount = new MpayAmount();
                amount.setCurrency(currency);
                amount.setTotal(total);

                MocaMpayTransaction transaction = new MocaMpayTransaction();
                transaction.setTransactionId(id);
                transaction.setBankTransId(bankTransRef);
                transaction.setCustomerTransId(customerTransactionId);
                transaction.setClientRef(customerTransactionId);
                transaction.setMerchantId(merchantId);
                transaction.setMerchantName(merchantName);
                transaction.setOnecomMerchantId(onecomMerchantId);
                transaction.setCreateTime(createTime);
                transaction.setExpireTime(expireTime);
                transaction.setStatus(status);
                transaction.setInstrument(instrument);
                transaction.setAmount(amount);
                transaction.setOrderInfo(orderInfo);
                transaction.setInputType(inputType);
                transaction.setTerminalId(terminalId);
                transaction.setMerchantOrderRef(merchantOrderRef);
                transaction.setMerchantTxnRef(merchantTxnRef);
                transaction.setAcquirer(acquirer);
                transaction.setClient(client);
                transaction.setAcqCode(acqCode);

                transaction.setAppName(rs.getString("S_APP_NAME"));
                transaction.setInvoiceId(rs.getString("S_INVOICE_ID"));
                transaction.setMasking(rs.getString("S_MASKING"));
                transaction.setQrId(rs.getString("S_QR_ID"));
                transaction.setClientId(rs.getString("S_CLIENT_ID"));
                transaction.setPaymentAmount(rs.getDouble("N_SETTLEMENT_AMOUNT"));

                //MOCA
                Map<String, Object> mocaPromotion =  getMocaPromotion(clientData);
                transaction.setPaymentAmount(paymentAmount);
                transaction.setPartnerDiscountRate((Double) mocaPromotion.get("partnerDiscountRate"));
                transaction.setMerchantDiscountRate((Double) mocaPromotion.get("merchantDiscountRate"));
                transaction.setOfferCode((String) mocaPromotion.get("offerCode"));
                transaction.setOfferName((String) mocaPromotion.get("offerName"));
                transaction.setOfferDiscountAmount((Double) mocaPromotion.get("offerDiscountAmount"));

                // if (jClientData != null && jClientData.containsKey("promotion")) {
                //     JsonObject promotion = jClientData.containsKey("promotion") ? jClientData.getJsonObject("promotion") : new JsonObject();
                //     if (promotion != null) {
                //         JsonObject program = promotion.containsKey("program") ? promotion.getJsonObject("program") : new JsonObject();
                //         double partnerDiscountPercent = program.containsKey("partner_discount_percent") ? program.getDouble("partner_discount_percent") : 0;
                //         double merchantDiscountPercent = program.containsKey("merchant_discount_percent") ? program.getDouble("merchant_discount_percent") : 0;

                //         transaction.setPartnerDiscountRate(setRate(partnerDiscountPercent));
                //         transaction.setMerchantDiscountRate(setRate(merchantDiscountPercent));
                //         transaction.setOfferCode(program.containsKey("offer_code") ? program.getString("offer_code") : "");
                //         transaction.setOfferName(program.containsKey("offer_name") ? program.getString("offer_name") : "");
                //         JsonObject resultJson = promotion.containsKey("result") ? promotion.getJsonObject("result") : new JsonObject();
                //         String offerDiscountAmount = resultJson.containsKey("offer_discount_amount") ? resultJson.getString("offer_discount_amount") : "";
                //         transaction.setOfferDiscountAmount(offerDiscountAmount != null || offerDiscountAmount != "" ? Double.parseDouble(offerDiscountAmount) : 0);
                //     // } else {
                //     //     transaction.setPartnerDiscountRate(0d);
                //     //     transaction.setMerchantDiscountRate(0d);
                //     //     transaction.setOfferCode("");
                //     //     transaction.setOfferName("");
                //     //     transaction.setOfferDiscountAmount(0d);

                //     }
                // }
                result.add(transaction);
            }
            return result;

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


    }

    private static ResultSet getDonwloadData(Connection connection, MocaMpayTransactionQuery query, int rows) throws SQLException {
            JsonArray inParams = new JsonArray()
            .add(rows)
            .add(QueryMethod.DOWNLOAD.toString())
            .add(query.getFromDate())
            .add(query.getToDate())
            .add(query.getKeywords())
            .add(query.getMerchantId())
            .add(query.getMerchantName())
            .add(query.getAcqCode())
            .add(query.getMerchantTransactionRef())
            .add(query.getTerminalId())
            .add(query.getInstrumentNumber())
            .add(query.getOrderInfo())
            .add(query.getTransactionId())
            .add(query.getCustomerTransactionId())
            .add(query.getBankTransactionId())
            .add(query.getBankId())
            .add(query.getCurrency())
            .add(query.getStatus())
            .add(query.getMasking() == null ? "" : query.getMasking())
            .add(query.getAppName() == null ? "" : query.getAppName())
            .add(query.getQrId() == null ? "" : query.getQrId())
            .add(query.getInvoiceId() == null ? "" : query.getInvoiceId())
            .add(query.getClientId() == null ? "" : query.getClientId())
            .add(query.getVersion() == null ? "" : query.getVersion())
            .add(query.getTransType() == null ? "" : query.getTransType())
            .addNull()
            .addNull()
            .addNull()
            .add(query.getBankTerminalId() == null ? "" : query.getBankTerminalId())
            .add(query.getBankMerchantId() == null ? "" : query.getBankMerchantId())
            ;
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, MOCA_SEARCH_TRANSACTION_2, inParams, outParams);
            Integer reusltCode = callableStatement.getInt(32);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD mPAY TRANSACTION ONLINE ERROR]: " + callableStatement.getString(33));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(31);

        } catch (SQLException ex) {

            LOGGER.log(Level.SEVERE, "[SEARCH mPAY TRANSACTION ERROR]: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }

    private static Observable<List<MocaMpayTransaction>> searchTransaction(SQLConnection sqlConnection, MocaMpayTransactionQuery query, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.SELECT.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getKeywords())
                .add(query.getMerchantId())
                .add(query.getMerchantName())
                .add(query.getAcqCode())
                .add(query.getMerchantTransactionRef())
                .add(query.getTerminalId())
                .add(query.getInstrumentNumber())
                .add(query.getOrderInfo())
                .add(query.getTransactionId())
                .add(query.getCustomerTransactionId())
                .add(query.getBankTransactionId())
                .add(query.getBankId())
                .add(query.getCurrency())
                .add(query.getStatus())
                .add(query.getMasking())
                .add(query.getAppName())
                .add(query.getQrId())
                .add(query.getInvoiceId())
                .add(query.getClientId() == null ? "" : query.getClientId())
                .add(query.getVersion() == null ? "" : query.getVersion())
                .add(query.getTransType() == null ? "" : query.getTransType())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset())
                .add(query.getBankTerminalId() == null ? "" : query.getBankTerminalId())
                .add(query.getBankMerchantId() == null ? "" : query.getBankMerchantId())
                ;
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(MOCA_SEARCH_TRANSACTION_2, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(31);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY TRANSACTION ERROR]: " + result.getOutput().getString(32));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(30).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    MocaMpayTransaction transaction = bindTransaction(jsonObject);
                    transaction.setId(jsonObject.getString("S_ORIGINAL_ID"));
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }

    private static Observable<Integer> getListTotalTransaction(SQLConnection conn, MocaMpayTransactionQuery query, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getKeywords())
                .add(query.getMerchantId())
                .add(query.getMerchantName())
                .add(query.getAcqCode())
                .add(query.getMerchantTransactionRef())
                .add(query.getTerminalId())
                .add(query.getInstrumentNumber())
                .add(query.getOrderInfo())
                .add(query.getTransactionId())
                .add(query.getCustomerTransactionId())
                .add(query.getBankTransactionId())
                .add(query.getBankId())
                .add(query.getCurrency())
                .add(query.getStatus())
                .add(query.getMasking() == null ? "" : query.getMasking())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getQrId() == null ? "" : query.getQrId())
                .add(query.getInvoiceId() == null ? "" : query.getInvoiceId())
                .add(query.getClientId() == null ? "" : query.getClientId())
                .add(query.getVersion() == null ? "" : query.getVersion())
                .add(query.getTransType() == null ? "" : query.getTransType())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getBankTerminalId() == null ? "" : query.getBankTerminalId())
                .add(query.getBankMerchantId() == null ? "" : query.getBankMerchantId())
                ;
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(MOCA_SEARCH_TRANSACTION_2, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(31);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(32));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = 0;

            Map map = result.getOutput().getJsonObject(30).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });

    }

    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connOnline
     * @param transactionId
     * @return
     */
    private static Observable<List<MocaMpayTransactionHistory>> getListTransactionHistory(SQLConnection connOnline, String transactionId, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(3);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MocaMpayTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }


    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connBackup
     * @param transactionId
     * @return
     */
    private static Observable<List<MocaMpayTransactionHistory>> getListApprovalHistoryBackup(SQLConnection connBackup, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(2);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackup.callWithParamsObservable2(LIST_REFUND_APPROVAL_HISTORY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST DOMESTIC APPROVAL HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MocaMpayTransactionHistory> childList = new ArrayList<>();
            List<MocaMpayTransactionHistory> parentList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MocaMpayTransactionHistory transaction = bindApprovalHistory(jsonObject);
                parentList.add(transaction);
                if (transaction.getParent_id() != null) {
                    childList.add(transaction);
                }
            }

            Map<String, List<MocaMpayTransactionHistory>> childMap = childList
                    .stream()
                    .collect(Collectors.groupingBy(MocaMpayTransactionHistory::getParent_id));

            return parentList.stream().map(parent -> {
                MocaMpayTransactionHistory re = new MocaMpayTransactionHistory(parent);

                if (childMap.get(parent.getTransaction_id()) != null) {
                    re.setSubHistories(childMap.get(parent.getTransaction_id()));
                } else {
                    re.setSubHistories(new ArrayList<>());
                }
                re.getSubHistories().add(parent);
                re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                return re;
            })
                    .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                    .collect(Collectors.toList());
        });
    }


    private static MocaMpayTransactionHistory bindTransactionHistory(JsonObject js) {
        MocaMpayTransactionHistory result = new MocaMpayTransactionHistory();

        Amount amount = new Amount();
        amount.setTotal(js.getDouble("N_AMOUNT"));
        amount.setCurrency(js.getString("S_CURRENCY"));

        result.setTransaction_id(js.getString("S_ID"));
        result.setOriginal_id(js.getString("S_ORIGINAL_ID"));
        result.setAmount(amount);
        result.setTransaction_type(js.getString("S_TRANSACTION_TYPE"));
        result.setOperator_id(js.getString("S_OPERATOR_ID"));
        result.setStatus(js.getString("S_STATE"));
        result.setAdvanced_status(js.getString("S_ADVANCE_STATUS"));
        result.setOrder_info(js.getString("S_INFO"));
        result.setMerchant_transaction_ref(js.getString("S_MERCH_TXN_REF"));
        result.setTransaction_time(js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE")));

        String desc = js.getString("S_DESCRIPTION");
        ManualRefundDescription description = new ManualRefundDescription();
        if (desc != null && !desc.isEmpty() && desc.contains("type")) {
            description = gson.fromJson(desc, ManualRefundDescription.class);
            result.setDescription(description.getType());
        }

        return result;
    }


    /**
     * convert data from result set to transaction history
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static MocaMpayTransactionHistory bindApprovalHistory(JsonObject rs) {

        Integer transactionId = rs.getInteger("N_ID");
        String originalId = rs.getString("S_TRANS_REF_ID");
        String currency = rs.getString("S_CURRENCY");
        String status = String.valueOf(rs.getInteger("N_STATUS"));
        String operator = rs.getString("S_OPERATOR_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANS_REF");

        Integer parent_id = rs.getInteger("N_PARENT_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(rs.getString("D_CREATE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String subData = rs.getString("S_DATA");
        String note = null;
        JsonObject jsonObject = null;
        try {
            jsonObject = new JsonObject(subData);
        } catch (JsonException err) {
            Logger.getLogger("Error", err.toString());
        }

        try {
            note = jsonObject.getString("note");
        } catch (JsonException err) {
            Logger.getLogger("Error", err.toString());
        }

        MocaMpayTransactionHistory MocaMpayTransactionHistory = new MocaMpayTransactionHistory();
        MocaMpayTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        MocaMpayTransactionHistory.setStatus(status);
        MocaMpayTransactionHistory.setNote(note);
        MocaMpayTransactionHistory.setOperator_id(operator);
        MocaMpayTransactionHistory.setTransaction_id(transactionId.toString());
        MocaMpayTransactionHistory.setTransaction_type(transactionType);
        MocaMpayTransactionHistory.setTransaction_time(date);
        MocaMpayTransactionHistory.setOriginal_id(originalId);
        MocaMpayTransactionHistory.setParent_id(parent_id == null ? null : parent_id.toString());
        MocaMpayTransactionHistory.setAdvanced_status(advanceStatus != null ? advanceStatus : StringPool.BLANK);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        MocaMpayTransactionHistory.setAmount(amountData);

        return MocaMpayTransactionHistory;
    }


    private static MocaMpayTransaction bindTransaction(JsonObject rs) {
        String id = rs.getString("S_ID");
        double total = rs.getDouble("N_AMOUNT");
        Double refund_total = rs.getDouble("N_REFUND_AMOUNT");
        String currency = rs.getString("S_CURRENCY");
        String status = rs.getString("S_STATE");
        String instrumentName = rs.getString("S_INSTRUMENT_NAME");
        String instrumentNumber = rs.getString("S_INSTRUMENT_NUMBER");
        String instrumentType = rs.getString("S_INSTRUMENT_TYPE");
        String instrumentBrandId = rs.getString("S_INSTRUMENT_BRAND_ID");
        String inputType = rs.getString("S_INPUT_TYPE");
        String terminalId = rs.getString("S_TERMINAL_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT");
        String customerTransactionId = rs.getString("S_CUSTOMER_TRANS_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        String merchantOrderRef = rs.getString("S_MERCH_ORDER_REF");
        String merchantTxnRef = rs.getString("S_MERCH_TXN_REF");
        String client = rs.getString("S_CLIENT_ID");
        Timestamp createTime = Timestamp.valueOf(rs.getString("D_CREATE"));
        String bankTransRef = rs.getString("S_BANK_TRANS_ID");
        String acqCode = rs.getString("S_MSP_ID");
        String clientData = rs.getString("S_DATA");
        JsonObject jClientData = null;
        Double paymentAmount = rs.getDouble("N_SETTLEMENT_AMOUNT");

        try {
            if (Objects.nonNull(clientData) && !Strings.isBlank(clientData) && !Strings.isEmpty(clientData)) {
                jClientData = new JsonObject(clientData);
            }
        } catch (Exception e) {
            Logger.getLogger("Client data parse error", e.toString());
        }

        // DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        // Timestamp createTime = null;
        //
        // try {
        // createTime = new Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
        // } catch (ParseException e) {
        // LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
        // }

        MpayAcquirer acquirer = new MpayAcquirer();
        // acquirer.setCard_type(rs.getInteger("S_ACQUIRER_ID"));
        acquirer.setAcquirerName(rs.getString("S_INSTRUMENT_TYPE"));

        String txtUpdateTime = rs.getString("D_UPDATE");
        Timestamp updateTime = null;
        if (txtUpdateTime != null) {
            updateTime = Timestamp.valueOf(txtUpdateTime);
        }
        String txtExpireTime = rs.getString("D_EXPIRE");
        Timestamp expireTime = null;

        if (txtExpireTime != null) {
            expireTime = Timestamp.valueOf(rs.getString("D_EXPIRE"));
        }
        String instrumentId = rs.getString("S_INSTRUMENT_ID");

        // Instrument
        Instrument instrument = new Instrument();
        instrument.setName(instrumentName);
        instrument.setNumber(instrumentNumber);
        instrument.setType(instrumentType);


        instrument.setId(instrumentId);
        instrument.setBrandId(instrumentBrandId);

        // Amount
        MpayAmount amount = new MpayAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefundTotal(refund_total); // TODO: fill correct one.


        MocaMpayTransaction transaction = new MocaMpayTransaction();
        transaction.setTransactionId(id);
        transaction.setBankTransId(bankTransRef);
        transaction.setCustomerTransId(customerTransactionId);
        transaction.setClientRef(customerTransactionId);
        transaction.setMerchantId(merchantId);
        transaction.setMerchantName(merchantName);
        transaction.setOnecomMerchantId(onecomMerchantId);
        transaction.setCreateTime(createTime);
        transaction.setUpdateTime(updateTime);
        transaction.setExpireTime(expireTime);
        transaction.setStatus(status);
        transaction.setInstrument(instrument);
        transaction.setAmount(amount);
        transaction.setOrderInfo(orderInfo);
        transaction.setInputType(inputType);
        transaction.setTerminalId(terminalId);
        transaction.setMerchantOrderRef(merchantOrderRef);
        transaction.setMerchantTxnRef(merchantTxnRef);
        transaction.setAcquirer(acquirer);
        transaction.setClient(client);
        transaction.setAcqCode(acqCode);
        transaction.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));

        transaction.setAppName(rs.getString("S_APP_NAME"));
        transaction.setInvoiceId(rs.getString("S_INVOICE_ID"));
        transaction.setMasking(rs.getString("S_MASKING"));
        transaction.setQrId(rs.getString("S_QR_ID"));
        transaction.setClientId(rs.getString("S_CLIENT_ID"));

        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        if (jClientData != null) {
            JsonObject jMsb = jClientData.containsKey("msb_qr") ? jClientData.getJsonObject("msb_qr") : new JsonObject();
            transaction.setBankMerchantId(jMsb.containsKey("bank_merchant_id") ? jMsb.getString("bank_merchant_id") : "");
            transaction.setBankTerminalId(jMsb.containsKey("bank_terminal_id") ? jMsb.getString("bank_terminal_id") : "");
        }
        // QUICK LINK
        String rawData = rs.getString("S_RAW_DATA");
        if (rawData != null) {
            JsonObject rawDataJ = new JsonObject(new String(Base64.getDecoder().decode(rawData)));
            try {
                rawDataJ = new JsonObject(new String(Base64.getDecoder().decode(rawData), "UTF-8"));
                transaction.setCustomer_address(rawDataJ.getString("vpc_SHIP_Street01"));
            } catch (UnsupportedEncodingException e) {
                LOGGER.log(Level.SEVERE, "error");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }
            //MOCA
            Map<String, Object> mocaPromotion =  getMocaPromotion(clientData);
            transaction.setPaymentAmount(paymentAmount);
            transaction.setPartnerDiscountRate((Double) mocaPromotion.get("partnerDiscountRate"));
            transaction.setMerchantDiscountRate((Double) mocaPromotion.get("merchantDiscountRate"));
            transaction.setOfferCode((String) mocaPromotion.get("offerCode"));
            transaction.setOfferName((String) mocaPromotion.get("offerName"));
            if (!"".equals(mocaPromotion.get("offerCode"))){
                transaction.setOfferDiscountAmount((Double) mocaPromotion.get("offerDiscountAmount"));
            }

        // if (jClientData != null && jClientData.containsKey("promotion")) {
        //     JsonObject promotion = jClientData.containsKey("promotion") ? jClientData.getJsonObject("promotion") : new JsonObject();
        //     if (promotion != null) {
        //         JsonObject program = promotion.containsKey("program") ? promotion.getJsonObject("program") : new JsonObject();
        //         double partnerDiscountPercent = program.containsKey("partner_discount_percent") ? program.getDouble("partner_discount_percent") : 0;
        //         double merchantDiscountPercent = program.containsKey("merchant_discount_percent") ? program.getDouble("merchant_discount_percent") : 0;

        //         transaction.setPartnerDiscountRate(setRate(partnerDiscountPercent));
        //         transaction.setMerchantDiscountRate(setRate(merchantDiscountPercent));
        //         transaction.setOfferCode(program.containsKey("offer_code") ? program.getString("offer_code") : "");
        //         transaction.setOfferName(program.containsKey("offer_name") ? program.getString("offer_name") : "");
        //         JsonObject resultJson = promotion.containsKey("result") ? promotion.getJsonObject("result") : new JsonObject();
        //         double offerDiscountAmount = resultJson.containsKey("offer_discount_amount") ? resultJson.getDouble("offer_discount_amount") : 0;
        //         transaction.setOfferDiscountAmount(offerDiscountAmount);
        //     // } else {
        //     //     transaction.setPartnerDiscountRate(0d);
        //     //     transaction.setMerchantDiscountRate(0d);
        //     //     transaction.setOfferCode("");
        //     //     transaction.setOfferName("");
        //     //     transaction.setOfferDiscountAmount(0d);

        //     }
        // }
        // // Return.
        return transaction;
    }

    private static Double setRate (double rate){
        Double result = 0d;
        if (rate != 0){
            result = rate/100;
        }
        return result;
    }

    //caclulate actualAmount(paymentAmount in request refund) moca
    // public static double caculateActualAmount(double inputAmount, double originalAmount, 
    //     double partnerDiscountRate, double merchantDiscountRate, double offerDiscountAmount ){
    public static double caculateActualAmount(double inputAmount, double paymentAmount, double offerDiscountAmount ){
            double actutalAmount = inputAmount;
            if (inputAmount != 0 || paymentAmount != 0 || offerDiscountAmount != 0
            // || partnerDiscountRate != 0 || merchantDiscountRate != 0
            ){
                // double merchantDiscount = Math.round((inputAmount/originalAmount)*offerDiscountAmount*merchantDiscountRate);
                // double partnerDiscount = Math.round((inputAmount/originalAmount)*offerDiscountAmount*partnerDiscountRate);
                // actutalAmount = inputAmount - merchantDiscount - partnerDiscount;
                actutalAmount = Math.round(inputAmount/(1+(offerDiscountAmount/paymentAmount))); //update formular

            }
            return actutalAmount;
        }
    //get promotion Moca from s_data\
    public static Map<String, Object> getMocaPromotion (String sData){
        Map<String, Object> promotionData = new HashMap();
        JsonObject jClientData = null;
        Double partnerDiscountRate = 0d;
        Double merchantDiscountRate = 0d;
        String offerCode = "";
        String offerName = "";
        Double offerDiscountAmount = 0d;
        Double paymentAmountPurchase = 0d;
        try {
            if (Objects.nonNull(sData) && !Strings.isBlank(sData) && !Strings.isEmpty(sData)) {
            jClientData = new JsonObject(sData);
            }
        } catch (Exception e) {
            Logger.getLogger("Client data parse error", e.toString());
        }
        if (jClientData != null && jClientData.containsKey("promotion")) {
            JsonObject promotion = jClientData.containsKey("promotion") ? jClientData.getJsonObject("promotion") : new JsonObject();
            if (promotion != null) {
                JsonObject program = promotion.containsKey("program") ? promotion.getJsonObject("program") : new JsonObject();
                double partnerDiscountPercent = program.containsKey("partner_discount_percent") ? program.getDouble("partner_discount_percent") : 0;
                double merchantDiscountPercent = program.containsKey("merchant_discount_percent") ? program.getDouble("merchant_discount_percent") : 0;

                partnerDiscountRate = setRate(partnerDiscountPercent);
                merchantDiscountRate = setRate(merchantDiscountPercent);
                offerCode = program.containsKey("offer_code") ? program.getString("offer_code") : "";
                offerName = program.containsKey("offer_name") ? program.getString("offer_name") : "";
                JsonObject resultJson = promotion.containsKey("result") ? promotion.getJsonObject("result") : new JsonObject();
                offerDiscountAmount = resultJson.containsKey("offer_discount_amount") ? resultJson.getDouble("offer_discount_amount") : 0;
                paymentAmountPurchase = resultJson.containsKey("settlement_amount") ? resultJson.getDouble("settlement_amount") : 0;
            }
        }
        promotionData.put("partnerDiscountRate", partnerDiscountRate);
        promotionData.put("merchantDiscountRate", merchantDiscountRate);
        promotionData.put("offerCode", offerCode);
        promotionData.put("offerName", offerName);
        promotionData.put("offerDiscountAmount", offerDiscountAmount);
        promotionData.put("paymentAmountPurchase", paymentAmountPurchase);

        return promotionData;
    }


}
