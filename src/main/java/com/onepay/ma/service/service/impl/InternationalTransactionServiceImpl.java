package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.util.PropsUtil;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;
import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/6/16.
 */
@Service
public class InternationalTransactionServiceImpl implements InternationalTransactionService {


    @Override
    public Observable<Transactions<InternationalTransaction>> list(SQLConnection connReadOnly, SQLConnection connOnline, InternationalTxnParameter internationalTxnParameter) {
        Transactions<InternationalTransaction> transactions = new Transactions();
        return getListTotalTransaction(connOnline, internationalTxnParameter, -1).flatMap(totalItem1 -> {
            return getListTotalTransaction(connReadOnly, internationalTxnParameter, totalItem1).flatMap(totalItem2 -> {
                transactions.setTotal_items(totalItem1 + totalItem2);
                internationalTxnParameter.setOffset(0);
                return getListTransaction(connOnline, internationalTxnParameter, -1).flatMap(transactionsListOnline -> {
                    // offset
                    internationalTxnParameter.setOffset(totalItem1);
                    return getListTransaction(connReadOnly, internationalTxnParameter, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                        List<InternationalTransaction> transactionsFinal = new ArrayList<>();
                        transactionsFinal.addAll(transactionsListOnline);
                        transactionsFinal.addAll(transactionsListReadOnly);
                        int index = 1;
                        for (InternationalTransaction internationalTransaction : transactionsFinal) {
                            internationalTransaction.setRow_num(index + (internationalTxnParameter.getPage() * internationalTxnParameter.getPageSize()));
                            index++;
                        }
                        transactions.setTransactions(transactionsFinal);
                        return Observable.just(transactions);
                    });

                });
            });

        });
    }

    @Override
    public Observable<InternationalTransaction> get(SQLConnection sqlConnection, String transactionId) {
        return getTransaction(sqlConnection, transactionId);
    }

    @Override
    public Observable<BrandConfig> getBrandConfig(SQLConnection sqlConnection, String brandId) {
        JsonArray inParams = new JsonArray()
                .add(brandId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(BRAND_CONFIG_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET BRAND CONFIG ERROR ]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            String sbrandId = jsonObject.getString("S_BRAND_ID");
            String state = jsonObject.getString("S_STATE");
            String sdata = jsonObject.getString("S_DATA");
            String sdesc = jsonObject.getString("S_DESC");
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
            Timestamp created = Timestamp.valueOf(jsonObject.getString("D_CREATE"));
            Timestamp updated = Timestamp.valueOf(jsonObject.getString("D_UPDATE"));
            BrandConfig brankConfig = new BrandConfig();
            brankConfig.setBranchId(sbrandId);
            brankConfig.setState(state);
            brankConfig.setData(sdata);
            brankConfig.setDesc(sdesc);
            brankConfig.setCreate(created);
            brankConfig.setUpdate(updated);
            return brankConfig;
        });
    }

    @Override
    /**
     * <AUTHOR>
     * @param sqlConnection
     * @param txnRef
     */
    public Observable<InternationalTransaction> getByRef(SQLConnection sqlConnection, String txnRef) {
        return getTransactionByRef(sqlConnection, txnRef);
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, InternationalTxnParameter parameter) {
        return getListTotalTransaction(connOnline, parameter, -1).flatMap(totalItem1 -> {
            return getListTotalTransaction(connReadOnly, parameter, totalItem1).map(totalItem2 -> {
                return totalItem1 + totalItem2;
            });
        });
    }

    @Override
    public Observable<InternationalTransactionHistory> getHistoryByMerchantRef(SQLConnection sqlOnline, String transactionId, String merchantTransactionRef, String merchantId) {
        return getHistoryByMerchantRefData(sqlOnline, transactionId, merchantId, merchantTransactionRef);
    }

    @Override
    public Observable<String> getContarctTypeMsp(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(GET_CONTRACT_TYPE_MSP, inParams, outParams).map(result -> {
            String r = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            r = result.getOutput().getString(1) != null ? new JsonObject(result.getOutput().getString(1)).getString("contract_type") : null;

            return r;

        });
    }

    @Override
    public ResultSet download(Connection connOnline, InternationalTxnParameter parameter, int row) throws SQLException {
        return downloadData(connOnline, parameter, row);
    }

    @Override
    public Observable<List<InternationalTransactionHistory>> listHistory(SQLConnection connOnline, SQLConnection connBackup, String transactionId) {

        return getListTransactionHistory(connOnline, transactionId, -1).flatMap(internationalTransactions -> {
            // return getListTransactionHistory(connOnline,
            // transactionId,internationalTransactions.size()).flatMap(internationalTransactionsReadOnly -> {

            List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
            return getListApprovalHistoryBackup(connBackup, transactionId).flatMap(approvalHistories -> {
                transactionsFinal.addAll(internationalTransactions);
                transactionsFinal.addAll(approvalHistories);
                Optional<InternationalTransactionHistory> authHis = transactionsFinal.stream()
                        .filter(trans -> "Authorize".equalsIgnoreCase(trans.getTransaction_type()))
                        .findFirst();
                if (authHis.isPresent()) {
                    approvalHistories.stream().forEach(a -> {
                        a.setOriginal_transaction_id(authHis.get().getTransaction_id());
                    });
                }


                // sort by trans time.
                transactionsFinal
                        .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

                return Observable.just(transactionsFinal);

            });
            // });

        });
    }

    // @Override
    // public Observable<List<InternationalTransactionHistory>> listTransAuthorizeHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId) {

    //     return getListTransactionAuthorizeHistory(connOnline, transactionId, -1).flatMap(authorizedTransactions -> {
    //         return getListTransactionAuthorizeHistory(connReadOnly, transactionId, authorizedTransactions.size()).flatMap(authorizedTransactionsReadOnly -> {
    //             List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
    //             transactionsFinal.addAll(authorizedTransactions);
    //             transactionsFinal.addAll(authorizedTransactionsReadOnly);
    //             String captureTranIds = transactionsFinal.stream().filter(trans -> "Capture".equals(trans.getTransaction_type()))
    //                     .map(item -> String.valueOf(item.getTransaction_id())).collect(Collectors.joining(","));
    //             return getListCaptureRefundHistoryBackup(connBackup, captureTranIds).flatMap(refundCapture -> {
    //                 transactionsFinal.addAll(refundCapture);
    //                 // sort by trans time.
    //                 transactionsFinal
    //                         .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
    //                 return Observable.just(transactionsFinal);
    //             });
    //         });
    //     });
    // }

    // @Override
    // public Observable<List<InternationalTransactionHistory>> listTransCaptureHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId) {

    //     return getListTransactionCaptureHistory(connOnline, transactionId, -1).flatMap(capturedTransactions -> {
    //         return getListTransactionCaptureHistory(connReadOnly, transactionId, capturedTransactions.size()).flatMap(capturedTransactionsReadOnly -> {
    //             List<InternationalTransactionHistory> captureFinal = new ArrayList<>();
    //             captureFinal.addAll(capturedTransactions);
    //             captureFinal.addAll(capturedTransactionsReadOnly);
    //             String captureTranIds = captureFinal.stream().filter(trans -> "Capture".equals(trans.getTransaction_type()))
    //                     .map(item -> String.valueOf(item.getTransaction_id())).collect(Collectors.joining(","));
    //             return getListCaptureRefundHistoryBackup(connBackup, captureTranIds).flatMap(captureRefundHistories -> {
    //                 List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
    //                 transactionsFinal.addAll(capturedTransactions);
    //                 transactionsFinal.addAll(capturedTransactionsReadOnly);
    //                 transactionsFinal.addAll(captureRefundHistories);

    //                 // sort by trans time.
    //                 transactionsFinal
    //                         .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

    //                 return Observable.just(transactionsFinal);
    //             });
    //         });
    //     });
    // }

    // @Override
    // public Observable<List<InternationalTransactionHistory>> listTransRefundCaptureHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId) {

    //     return getListTransactionRefundCaptureHistory(connOnline, transactionId, -1).flatMap(capturedTransactions -> {
    //         return getListTransactionRefundCaptureHistory(connReadOnly, transactionId, capturedTransactions.size()).flatMap(capturedTransactionsReadOnly -> {
    //             List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
    //             // transactionsFinal.addAll(capturedTransactions);
    //             // transactionsFinal.addAll(capturedTransactionsReadOnly);
    //             // Get capture transaction history
    //             String originalTransactionId = "";
    //             if (capturedTransactions.size() > 0)
    //                 originalTransactionId = String.valueOf(capturedTransactions.get(0).getOriginal_transaction_id());
    //             else if (capturedTransactionsReadOnly.size() > 0)
    //                 originalTransactionId = String.valueOf(capturedTransactionsReadOnly.get(0).getOriginal_transaction_id());

    //             return listTransCaptureHistory(connReadOnly, connOnline, connBackup, originalTransactionId).flatMap(authorizeTransaction -> {
    //                 transactionsFinal.addAll(authorizeTransaction);
    //                 // .stream()
    //                 // .filter(internationalTransactionHistory ->
    //                 // "Capture".equals(internationalTransactionHistory.getTransaction_type()))
    //                 // .collect(Collectors.toList())
    //                 //

    //                 // sort by trans time.
    //                 transactionsFinal
    //                         .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

    //                 return Observable.just(transactionsFinal);
    //             });
    //         });
    //     });
    // }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection connection, InternationalTxnParameter parameter, int row) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .add(parameter.getAuthenticationType())
                .add(parameter.getAuthenticationState())
                .add(parameter.getAuthorizationCode())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getIs_show_3d());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        Long start = System.currentTimeMillis();
        LOGGER.log(Level.INFO, "[DOWNLOAD INTERNATIONAL TRANSACTION START]: ");

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(23);


            Long end = System.currentTimeMillis();

            LOGGER.log(Level.INFO, "[DOWNLOAD INTERNATIONAL TRANSACTION DONE]: " + (end - start));
            if (reusltCode != 200) {
                LOGGER.log(Level.WARNING, "[DOWNLOAD INTERNATIONAL TRANSACTION ERROR]: " + callableStatement.getString(24));
            }
            rs = (ResultSet) callableStatement.getObject(22);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }


    private Observable<List<InternationalTransaction>> getListTransaction(SQLConnection connOnline, InternationalTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .add(parameter.getAuthenticationType())
                .add(parameter.getAuthenticationState())
                .add(parameter.getAuthorizationCode())
                .add(parameter.getPageSize())
                .add(parameter.getPage())
                .add(parameter.getOffset())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getIs_show_3d());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            List<InternationalTransaction> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(22);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ONLINE ERROR]: " + result.getOutput().getString(23));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(21).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransaction transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }


            return transactionList;

        });
    }



    private Observable<Integer> getListTotalTransaction(SQLConnection connOnline, InternationalTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .add(parameter.getAuthenticationType())
                .add(parameter.getAuthenticationState())
                .add(parameter.getAuthorizationCode())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getIs_show_3d());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            Integer total = 0;

            Integer reusltCode = result.getOutput().getInteger(22);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(23));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(21).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }


            return total;

        });
    }

    private Observable<Integer> getListTotalSamsungTransaction(SQLConnection connOnline, InternationalTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .add(parameter.getAuthenticationType())
                .add(parameter.getAuthenticationState())
                .add(parameter.getAuthorizationCode())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getIs_show_3d())
                .add(parameter.getOrderStatus());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(LIST_SAMSUNG_TRANSACTION, inParams, outParams).map(result -> {

            Integer total = 0;

            Integer reusltCode = result.getOutput().getInteger(23);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(24));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(22).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }


            return total;

        });
    }



    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private Observable<InternationalTransaction> getTransaction(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            InternationalTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);
            transaction.setType(jsonObject.getString("S_TYPE"));
            return transaction;

        });
    }

    /**
     * get transaction data
     * 
     * <AUTHOR>
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private Observable<InternationalTransaction> getTransactionByRef(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_REF, inParams, outParams).map(result -> {
            InternationalTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            return transaction;

        });
    }

    /**
     * get history by merchant ref
     *
     * @param sqlOnline
     * @param transactionId
     * @return
     */
    private Observable<InternationalTransactionHistory> getHistoryByMerchantRefData(SQLConnection sqlOnline, String transactionId, String merchantId, String transactionReference) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(transactionReference)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlOnline.callWithParamsObservable2(TRANSACTION_BY_MERCHANT_REF, inParams, outParams).map(result -> {
            InternationalTransactionHistory internationalTransactionHistory = null;

            Integer reusltCode = result.getOutput().getInteger(4);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            internationalTransactionHistory = bindTransactionHistory(jsonObject);
            return internationalTransactionHistory;


        });
    }

    /**
     * get history by merchant ref
     *
     * @param sqlOnline
     * @param originalId
     * @param merchantId
     * @param transaction ref
     * @return
     */
    @Override
    public Observable<InternationalTransactionHistory> getCaptureTransByRef(SQLConnection sqlOnline, String merchantId, String transactionReference) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(transactionReference)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull();
        LOGGER.log(Level.INFO, "inParams CAPTURE "+ inParams);
        return sqlOnline.callWithParamsObservable2(CAPTURE_BY_MERCHANT_REF, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);
           
            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET CAPTURE TRANSACTION BY REF ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            InternationalTransactionHistory interTrans = new InternationalTransactionHistory();
            interTrans.setTransaction_id(jsonObject.getInteger("N_ID"));
            interTrans.setMerchant_id(jsonObject.getString("s_merchant_id"));
            interTrans.setMerchant_transaction_ref(jsonObject.getString("s_transaction_reference"));
            LOGGER.log(Level.INFO, "interTrans  "+ interTrans.toString());
            return interTrans;
        });
    }


    /**
     * get listByMerchant transaction history data
     *
     * @param connReadOnly
     * @param transactionId
     * @param row <0:online;row>=0 offline
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListTransactionHistory(SQLConnection connReadOnly, String transactionId, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(3);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<InternationalTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;

        });
    }

    /**
     * get listByMerchant transaction history data
     *
     * @param connReadOnly
     * @param transactionId
     * @param row <0:online;row>=0 offline
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListTransactionAuthorizeHistory(SQLConnection connReadOnly, String transactionId, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(3);
            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<InternationalTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }
            return transactionList;
        });
    }

    /**
     * get listByMerchant transaction history data
     *
     * @param connReadOnly
     * @param transactionId
     * @param row <0:online;row>=0 offline
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListTransactionCaptureHistory(SQLConnection connReadOnly, String transactionId, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(3);
            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<InternationalTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }
            return transactionList;
        });
    }

    /**
     * get listByMerchant transaction history data
     *
     * @param connReadOnly
     * @param transactionId
     * @param row <0:online;row>=0 offline
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListTransactionRefundCaptureHistory(SQLConnection connReadOnly, String transactionId, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(3);
            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<InternationalTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }
            return transactionList;
        });
    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalTransaction bindTransaction(JsonObject rs) {
        String cardNumber = rs.getString("S_CARD_NO");
        String nameOnCard = rs.getString("S_CARD_HOLDER") != null ? rs.getString("S_CARD_HOLDER") : rs.getString("S_NAME_ON_CARD");
        String bankId = rs.getString("S_BANK_ID");
        String cardDate = rs.getString("S_CARD_EXP");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
        String currency = rs.getString("S_CURRENCY");
        String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
        String cardType = rs.getString("S_CARD_TYPE");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
        String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
        int transactionId = rs.getInteger("EXT_ID");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE"));
        double total = rs.getDouble("EXT_AMOUNT");
        String transactionType = rs.getString("EXT_TYPE");
        String transactionStatus = rs.getString("EXT_STATUS");
        String transactionRefNumber = rs.getString("EXT_REF_NUMBER");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        String binCountry = rs.getString("BIN_COUNTRY");
        String avsResultCode = rs.getString("S_AVS_RESULT_CODE");
        String address = rs.getString("S_ADDRESS");
        String state = rs.getString("S_STATE_PROVINCE");
        String zipCode = rs.getString("S_ZIP_POSTAL_CODE");
        String country = rs.getString("S_COUNTRY");
        String city = rs.getString("S_CITY_TOWN");
        String verificationSecurityLevel = rs.getString("S_VERIFICATION_SECURITY_LEVEL");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String cscResult = rs.getString("S_CSCRESULT_CODE");
        String enrolled3DS = rs.getString("S_3DS_ENROLLED");
        String ip = rs.getString("S_IP");
        String ipProxy = rs.getString("S_IP_PROXY");
        String operator = rs.getString("S_OPERATOR_ID");
        double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
        boolean canVoid = rs.getInteger("CAN_VOID") != null && rs.getInteger("CAN_VOID") == 1 ? true : false;
        boolean requiredAvs = rs.getInteger("N_REQUIRED_AVS") != null && rs.getInteger("N_REQUIRED_AVS") == 1 ? true : false;
        String eci = rs.getString("S_3DS_ECI");
        String ticketNumber = rs.getString("S_TICKET_NUMBER");
        String authorizationAmount = rs.getString("S_AUTHORISED_AMOUNT");
        String commercialCardIndicator = rs.getString("S_COMMERCIAL_CARD_INDICATOR");
        String risk_assesment = rs.getString("S_FRAUD_INFO_FOR_MER_ALERT");


        // PP
        String status3ds = rs.getString("S_3DS_STATUS");
        String riskOverallResult = rs.getString("S_RISK_OVERALL_RESULT");
        String xid = rs.getString("S_3DS_XID");
        String cardLevelIndicator = rs.getString("S_CARD_LEVEL_INDICATOR");



        InternationalTransaction transaction = new InternationalTransaction();
        if (risk_assesment != null) {
            transaction.setRisk_assesment("Review Required");
        } else {
            transaction.setRisk_assesment("Not Assessed");
        }
        transaction.setBank_id(bankId);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(transactionTime);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ip);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_reference(transactionRef);
        transaction.setOcMerTxnRef(rs.getString("S_OC_MER_TXN_REF"));
        transaction.setBin_country(binCountry);
        transaction.setCsc_result_code(cscResult);
        transaction.setMerchant_id(merchantId);
        transaction.setOperator(operator);
        transaction.setEnrolled_3ds(enrolled3DS);
        transaction.setTransaction_status(transactionStatus);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_ref_number(transactionRefNumber);
        transaction.setVerification_security_level(verificationSecurityLevel);
        transaction.setResponse_code(responseCode);
        transaction.setIp_proxy(ipProxy);
        transaction.setCan_void(canVoid);
        transaction.setIs_required_avs(requiredAvs);
        transaction.setEci(eci);
        transaction.setAdvance_status(rs.getString("S_ADVANCE_STATUS"));
        transaction.setTicket_number(ticketNumber);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_short_name(rs.getString("S_ACQUIRER_NAME"));
        acquirer.setAcquirer_name(rs.getString("S_ACQUIRER_NAME"));
        transaction.setAcquirer(acquirer);

        AvsData avsData = new AvsData();
        avsData.setAddress(address);
        avsData.setCountry(country);
        avsData.setCity(city);
        avsData.setProvince(state);
        avsData.setResult_code(avsResultCode);
        avsData.setZip_code(zipCode);

        transaction.setAvs(avsData);

        InternationalAmount amount = new InternationalAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(refundTotal);
        // tong so tien refund capture man hinh detail giao dich authorize
        if (rs.containsKey("EXT_AUTH_REFUND_CAP_AMOUNT") && rs.getDouble("EXT_AUTH_REFUND_CAP_AMOUNT") > 0) {
            double refundCaptureTotal = rs.getDouble("EXT_AUTH_REFUND_CAP_AMOUNT");
            amount.setRefund_capture_total(refundCaptureTotal);
        }
        // tong so tien refund capture man hinh detail giao dich capture
        if (rs.containsKey("EXT_CAP_REFUND_CAP_AMOUNT") && rs.getDouble("EXT_CAP_REFUND_CAP_AMOUNT") > 0) {
            double refundCaptureTotal = rs.getDouble("EXT_CAP_REFUND_CAP_AMOUNT");
            amount.setRefund_capture_total(refundCaptureTotal);
        }

        transaction.setAmount(amount);

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);
        authenticationData.setAuthorization_amount(authorizationAmount);

        transaction.setAuthentication(authenticationData);


        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setName_on_card(nameOnCard);
        card.setCommercical_card(commercialCard);
        card.setCommercial_card_indicator(commercialCardIndicator);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);

        // PP
        transaction.setXid(xid);
        transaction.setRiskOverAllResult(riskOverallResult);
        transaction.setStatus3ds(status3ds);
        transaction.setCardLevelIndicator(cardLevelIndicator);



        // SAMSUNG
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

        // ADAYROI
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));

        // Installment
        transaction.setInstallment_bank(rs.getString("S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(rs.getString("S_INSTALLMENT_STATE"));
        transaction.setInstallment_cus_email(rs.getString("S_INSTALLMENT_CUST_EMAIL"));
        transaction.setInstallment_cus_phone(rs.getString("S_INSTALLMENT_CUST_PHONE"));
        transaction.setInstallment_time(rs.getString("S_INSTALLMENT_TIME"));
        transaction.setInstallment_cancel_days(rs.getInteger("N_INSTALLMENT_CANCEL_DAYS"));
        transaction.setInstallment_monthly_amount(rs.getDouble("N_INSTALLMENT_MONTHLY_AMOUNT"));
        transaction.setInstallment_fee(rs.getDouble("N_INSTALLMENT_FEE"));
        transaction.setCard_holder_name(rs.getString("S_CARD_HOLDER"));

        //QUICK LINK
        String rawData = rs.getString("S_RAW_DATA");
        if(rawData != null) {
            JsonObject rawDataJ = new JsonObject(new String(Base64.getDecoder().decode(rawData)));
            try {
                rawDataJ = new JsonObject(new String(Base64.getDecoder().decode(rawData),"UTF-8"));
                transaction.setCustomer_address(rawDataJ.getString("vpc_SHIP_Street01"));
            } catch (UnsupportedEncodingException e) {
                LOGGER.log(Level.SEVERE, "error");
               throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }

        return transaction;
    }



    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connOnline
     * @param transactionId
     * @return
     */
    private Observable<List<InternationalTransactionHistory>> getListApprovalHistoryBackup(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(1);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_REFUND_APPROVAL_HISTORY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST International APPROVAL HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            List<InternationalTransactionHistory> childList = new ArrayList<>();
            List<InternationalTransactionHistory> parentList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindApprovalHistory(jsonObject);

                if (transaction.getParent_id() == null) {
                    parentList.add(transaction);
                } else {
                    childList.add(transaction);
                }
            }

            Map<Integer, List<InternationalTransactionHistory>> childMap = childList.stream()
                    .collect(Collectors.groupingBy(InternationalTransactionHistory::getParent_id));

            return parentList.stream().map(parent -> {
                InternationalTransactionHistory re = new InternationalTransactionHistory(parent);

                if (childMap.get(parent.getTransaction_id()) != null) {
                    re.setSubHistories(childMap.get(parent.getTransaction_id()));
                } else {
                    re.setSubHistories(new ArrayList<>());
                }
                re.getSubHistories().add(parent);
                re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                return re;
            })
                    .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                    .collect(Collectors.toList());
        });
    }

    @Override
    public Observable<List<InternationalTransaction>> listByIds(SQLConnection connOnline, String transIds, InternationalRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getAcquirerId())
                // .add("")
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_ONLINE_BY_IDS, inParams, outParams).map(resultSet -> {
            List<InternationalTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(6) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS INTERNATIONAL TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransaction transaction = bindTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }

    @Override
    /**
     * Authorize capture
     * Search capture trans by id
     * 
     * <AUTHOR>
     * @param connOnline
     * @param transIds
     * @param parameter
     */
    public Observable<List<InternationalTransaction>> listCaptureByIds(SQLConnection connOnline, String transIds, InternationalRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getAcquirerId())
                // .add("")
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_CAPTURE_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_CAPTURE_TRANSACTION_ONLINE_BY_IDS, inParams, outParams).map(resultSet -> {
            List<InternationalTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(6) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS INTERNATIONAL TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransaction transaction = bindTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }

    @Override
    public ResultSet downloadOnlineByIds(Connection connOnline, String transIds, InternationalRefundParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getAcquirerId())
                // .add("")
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        LOGGER.log(Level.INFO, "[CALL FOR DOWNLOAD]: PKG_ONECREDIT.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connOnline, LIST_TRANSACTION_ONLINE_BY_IDS, inParams, outParams);

            if (callableStatement.getInt(7) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS INTERNATIONAL TRANSACTION ONLINE ERROR]: " + callableStatement.getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(6);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


        return rs;
    }

    @Override
    public Observable<Integer> insertOnePAYManual(SQLConnection connOnline, Integer originalId, Double amount, String transReference, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(originalId)
                .add(amount)
                .add(transReference)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.INSERT_TRANS_ONEPAY_MANUAL, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(INSERT_TRANS_ONEPAY_MANUAL, inParams, outParams).map(resultSet -> {
            int returnId = resultSet.getOutput().getInteger(4);
            if (returnId == 0) {
                LOGGER.log(Level.SEVERE, "[INSERT TRANS ONEPAY MANUAL ERROR]: " + resultSet.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return returnId;
        });
    }

    @Override
    public Observable<Integer> insertReverseDue(SQLConnection connOnline, Integer originalId, Double amount, String transReference, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(originalId)
                .add(amount)
                .add(transReference)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.USP_INSERT_TRANS_REVERSE_DUE, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(INSERT_TRANS_REVERSE_DUE, inParams, outParams).map(resultSet -> {
            int returnId = resultSet.getOutput().getInteger(4);
            if (returnId == 0) {
                LOGGER.log(Level.SEVERE, "[INSERT TRANS REVERSE DUE ERROR]: " + resultSet.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return returnId;
        });
    }

    @Override
    public Observable<String> getRiskAssessment(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull();
        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_RISK_ASSESSMENT, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(GET_RISK_ASSESSMENT, inParams, outParams).map(resultSet -> {
            if (resultSet.getOutput().getInteger(1) != 200) {
                LOGGER.log(Level.SEVERE, "[GET_RISK_ASSESSMENT ERROR]: " + resultSet.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = resultSet.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String result = null;
            for (JsonObject jsonObject : rs.getRows()) {
                result = jsonObject.getString("N_SHOW_ON_MA");
            }
            return result;
        });
    }

    @Override
    public Observable<Boolean> isApplePay(SQLConnection connOnline, List<Merchant> merchants) {
        boolean result = false ;
        List<String> listMerchant = new ArrayList<>();
        
        if(Objects.isNull(merchants)){
            return Observable.just(result);
        }

        for (Merchant mer : merchants) {
            if (!"".equals(mer.getMerchant_id()) || !"".equals(mer.getType())){
                if("international".equals(mer.getType())) {
                    listMerchant.add(mer.getMerchant_id());
                }
            }
        }
        
        if(!Objects.isNull(listMerchant) && !listMerchant.isEmpty()){
            String listString = String.join(",", listMerchant);
            JsonArray inParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .addNull()
                    .add(listString);
            JsonArray outParams = new JsonArray()
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.VARCHAR)
                    .addNull();
            // LOGGER.log(Level.WARNING, "[CALL ]: PKG_ONECREDIT.GET_RISK_ASSESSMENT, IN: " + inParams + ", OUT: " + outParams);
            return connOnline.callWithParamsObservable2(CHECK_IS_APPLEPAY, inParams, outParams).map(resultSet -> {
                if (resultSet.getOutput().getInteger(1) != 200) {
                    LOGGER.log(Level.SEVERE, "[GET APPLEPAY ERROR]: " + resultSet.getOutput().getString(2));
                    return result;
                }
                if (resultSet.getOutput().getInteger(0) > 0){
                    return true;
                }
                return result;
            });
        }
        return Observable.just(result);
    }
    
    @Override
    public Observable<Boolean> isDigitalWallet(SQLConnection connOnline, List<Merchant> merchants) {
        boolean result = false ;
        List<String> listMerchant = new ArrayList<>();
        
        if(Objects.isNull(merchants)){
            return Observable.just(result);
        }

        for (Merchant mer : merchants) {
            if (!"".equals(mer.getMerchant_id()) || !"".equals(mer.getType())){
                if("international".equals(mer.getType())
                    || "domestic".equals(mer.getType())) {
                    listMerchant.add(mer.getMerchant_id());
                }
            }
        }
        
        if(Objects.isNull(listMerchant) || listMerchant.isEmpty())
            return Observable.just(false);

        // Chia list thành nhiều list nhỏ có độ dài < 4000 để gọi DB
        List<String> listStrMerchant = new ArrayList<>();
        while (!listMerchant.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            while (sb.length() < 3800 && !listMerchant.isEmpty()) {
                sb.append(listMerchant.remove(0)).append(",");
            }
            sb.setLength(sb.length() - 1);
            listStrMerchant.add(sb.toString());
        }

        return Observable.from(listStrMerchant).flatMap(strMerchant -> {
            JsonArray inParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .addNull()
                    .add(strMerchant);
            JsonArray outParams = new JsonArray()
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.VARCHAR)
                    .addNull();
            return connOnline.callWithParamsObservable2(CHECK_IS_DIGITAL_WALLET, inParams, outParams).map(resultSet -> {
                if (resultSet.getOutput().getInteger(1) != 200) {
                    LOGGER.log(Level.WARNING, "[CHECK_IS_DIGITAL_WALLET ERROR]: " + resultSet.getOutput().getString(2));
                    return false;
                }
                if (resultSet.getOutput().getInteger(0) > 0){
                    return true;
                }
                return false;
            });
        }).exists(subResult -> subResult); // Nếu có 1 trong các list nhỏ có kết quả là true thì trả về true
    }

    /**
     * convert data from result set to transaction history
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalTransactionHistory bindTransactionHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_ID");
        String currency = rs.getString("S_CURRENCY");
        String status = rs.getString("S_STATUS");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        String operator = rs.getString("S_OPERATOR_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String financialTransactionId = rs.getString("S_FINANCIAL_TRANSACTION_ID");
        String referenceNumber = rs.getString("S_REFERENCE_NUMBER");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = java.sql.Timestamp.valueOf(rs.getString("D_DATE"));

        InternationalTransactionHistory transactionHistory = new InternationalTransactionHistory();
        transactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        transactionHistory.setStatus(status);
        transactionHistory.setOperator_id(operator);
        transactionHistory.setOriginal_transaction_id(originalId);
        transactionHistory.setMerchant_id(merchantId);
        transactionHistory.setResponse_code(responseCode);
        transactionHistory.setTransaction_id(transactionId);
        transactionHistory.setTransaction_type(transactionType);
        transactionHistory.setTransaction_time(date);
        transactionHistory.setAdvance_status(advanceStatus);
        transactionHistory.setReference_number(referenceNumber);
        transactionHistory.setFinancial_transaction_id(financialTransactionId);
        transactionHistory.setParent_id(rs.getInteger("N_PARENT_ID"));
        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        transactionHistory.setAmount(amountData);

        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        String disputeReason = rs.getString("S_DISPUTE_REASON") == null ? "" : rs.getString("S_DISPUTE_REASON");
        String note = transactionType.equals("Refund Dispute") ? disputeReason : data.getString("note");
        transactionHistory.setNote(note);

        return transactionHistory;
    }

    private InternationalTransactionHistory bindApprovalHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_ID");
        int originalId = Integer.valueOf(rs.getString("N_TRANS_REF_ID"));
        String currency = rs.getString("S_CURRENCY");
        Integer status = rs.getInteger("N_STATUS");
        String operator = rs.getString("S_OPERATOR_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANS_REF");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(rs.getString("D_CREATE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");

        InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
        internationalTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        internationalTransactionHistory.setOriginal_transaction_id(originalId);
        internationalTransactionHistory.setStatus(status.toString());
        internationalTransactionHistory.setOperator_id(operator);
        internationalTransactionHistory.setTransaction_id(transactionId);
        internationalTransactionHistory.setTransaction_type(transactionType);
        internationalTransactionHistory.setTransaction_time(date);
        internationalTransactionHistory.setMerchant_id(merchantId);
        internationalTransactionHistory.setAdvance_status(advanceStatus != null ? advanceStatus : StringPool.BLANK);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        internationalTransactionHistory.setAmount(amountData);
        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        String disputeReason = rs.getString("S_DISPUTE_REASON") == null ? "" : rs.getString("S_DISPUTE_REASON");
        String note = transactionType.equals("Refund Dispute") ? disputeReason : data.getString("note");
        internationalTransactionHistory.setNote(note);

        return internationalTransactionHistory;
    }


    private Observable<List<InternationalTransactionHistory>> getListCaptureRefundHistoryBackup(SQLConnection connBackUp, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(3);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_CAPTURE_REFUND_HISTORY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST International REFUND CAPTURE HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            List<InternationalTransactionHistory> childList = new ArrayList<>();
            List<InternationalTransactionHistory> parentList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindApprovalHistory(jsonObject);

                if (transaction.getParent_id() == null) {
                    parentList.add(transaction);
                } else {
                    childList.add(transaction);
                }
            }

            Map<Integer, List<InternationalTransactionHistory>> childMap = childList.stream()
                    .collect(Collectors.groupingBy(InternationalTransactionHistory::getParent_id));

            return parentList.stream().map(parent -> {
                InternationalTransactionHistory re = new InternationalTransactionHistory(parent);

                if (childMap.get(parent.getTransaction_id()) != null) {
                    re.setSubHistories(childMap.get(parent.getTransaction_id()));
                } else {
                    re.setSubHistories(new ArrayList<>());
                }
                re.getSubHistories().add(parent);
                re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                return re;
            })
                    .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                    .collect(Collectors.toList());
        });
    }


    private static final String LIST_TRANSACTION = "{call PKG_ONECREDIT.SEARCH_TRANSACTION(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_SAMSUNG_TRANSACTION = "{call PKG_ONECREDIT.SEARCH_SAMSUNG_TRANSACTION(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_VOID_TXN.SEARCH_TRANSACTION_BY_ID_5(?,?,?,?) }";

    private static final String GET_CONTRACT_TYPE_MSP = "{call PKG_ONECREDIT.get_transaction_contract_type(?,?,?,?) }";

    private static final String BRAND_CONFIG_GET_BY_ID = "{call PKG_VOID_TXN.get_brand_config(?,?,?,?) }";

    private static final String TRANSACTION_GET_BY_REF = "{call PKG_ONECREDIT.SEARCH_TXN_BY_REF(?,?,?,?) }";// DuongPXT void transaction

    private static final String LIST_TRANSACTION_HISTORY = "{call PKG_ONECREDIT.search_txn_his(?,?,?,?,?)}";

    private static final String TRANSACTION_BY_MERCHANT_REF = "{call SEARCH_REFUND_BY_REF(?,?,?,?,?,?) }";

    private static final String CAPTURE_BY_MERCHANT_REF = "{call SEARCH_CAPTURE_BY_REF(?,?,?,?,?) }";

    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_REFUND_APPROVE.get_approval_history_3(?,?,?,?,?)}";// DuongPXT authorize Capture : add type of refund capture

    private static final String LIST_TRANSACTION_ONLINE_BY_IDS = "{call PKG_ONECREDIT.GET_TRANSACTION_BY_IDS2(?,?,?,?,?,?,?,?)}";

    private static final String LIST_CAPTURE_TRANSACTION_ONLINE_BY_IDS = "{call PKG_ONECREDIT.GET_CAPTURE_TXN_BY_IDS(?,?,?,?,?,?,?,?)}"; // DuongPXT authorize Capture : add get capture his by id

    private static final String INSERT_TRANS_ONEPAY_MANUAL = "{call PKG_ONECREDIT.INSERT_TRANS_ONEPAY_MANUAL(?,?,?,?,?,?)}";

    private static final String INSERT_TRANS_REVERSE_DUE = "{call PKG_ONECREDIT.USP_INSERT_TRANS_REVERSE_DUE(?,?,?,?,?,?)}";

    private static final String GET_RISK_ASSESSMENT = "{call PKG_ONECREDIT.get_risk_assessment_2(?,?,?,?)}";

    private static final String LIST_CAPTURE_REFUND_HISTORY = "{call PKG_REFUND_APPROVE.get_refund_capture_history(?,?,?,?,?)}";

    private static final String CHECK_IS_APPLEPAY = "{call PKG_MERCHANT.check_applepay_user(?,?,?,?)}";

    private static final String CHECK_IS_DIGITAL_WALLET = "{call PKG_MERCHANT.check_merchant_digital_wallet(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(InternationalTransactionServiceImpl.class.getName());


}
