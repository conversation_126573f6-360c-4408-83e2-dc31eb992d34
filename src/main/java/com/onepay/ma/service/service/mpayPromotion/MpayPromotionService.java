package com.onepay.ma.service.service.mpayPromotion;

import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by anhkh on 26-Jan-18.
 */
public interface MpayPromotionService {

    Observable<Transactions<MpayPromotionTransaction>> search(SQLConnection connOnline, SQLConnection connReadOnly , MpayPromotionQuery query);

    Observable<MpayPromotionTransaction> get(SQLConnection connOnline, String id);


    Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, MpayPromotionQuery query);

    ResultSet downloadOnline(Connection connOnline, MpayPromotionQuery query) throws SQLException;

    ResultSet downloadReadonly(Connection connReadOnly, MpayPromotionQuery query) throws SQLException;

}
