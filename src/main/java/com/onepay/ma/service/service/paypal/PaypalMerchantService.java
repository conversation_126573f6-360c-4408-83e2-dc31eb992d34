package com.onepay.ma.service.service.paypal;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

public interface PaypalMerchantService  {

    Observable<Void> insert(SQLConnection sqlConnectionO, Integer partnerId, String ppMerchantId, String paymentReceivable, String emailConfirmed);

    Observable<String> getppMerchantIdByTransNo(SQLConnection connection, String transNo);
}
