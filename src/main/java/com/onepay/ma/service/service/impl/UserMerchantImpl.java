package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.service.UserMerchantService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
@Service
public class UserMerchantImpl implements UserMerchantService {

    @Override
    public Observable<BaseList<User>> findUserByMerchant(SQLConnection connection, String merchantId, String keyword) {
        return findUserByMerchantId(connection, merchantId, keyword);
    }

    @Override
    public Observable<Integer> deleteByUserId(SQLConnection connection, int userId, String type) {
        return deleteMerchantByUser(connection, userId, type);
    }

    @Override
    public Observable<Integer> insert(SQLConnection connection, Merchant merchant, int userId) {
        return this.insertMerchantByUser(connection, merchant, userId);
    }
    @Override
    public Observable<Integer> deleteByUserIdV2(SQLConnection connection, int userId) {
        return deleteMerchantByUserV2(connection, userId);
    }
    @Override
    public Observable<Integer> deleteByUserIdV3(SQLConnection connection, int userId, List<Merchant> merchants) {
        return deleteMerchantByUserV3(connection, userId, merchants);
    }

    @Override
    public Observable<List<String>> listUserIdByMerchant(SQLConnection connection, String merchantId, String type) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(GET_USERS_BY_MERCHANT_ID, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(3);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] FIND USER BY MERCHANT ERROR:" + StringPool.SPACE + result.getOutput().getString(4));
                throw  IErrors.VALIDATION_ERROR;
            }

            List<String> listUserId = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    listUserId.add(jsonObject.getString("S_USER_ID"));
                }
            }
            return listUserId;
        });                
    }

    private Observable<BaseList<User>> findUserByMerchantId(SQLConnection connection, String merchantId, String keyword ){
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(keyword)
                .add(0)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(SELECT_BY_MERCHANT, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(5);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] FIND USER BY MERCHANT ERROR:" + StringPool.SPACE + result.getOutput().getString(6));
                throw  IErrors.VALIDATION_ERROR;
            }

            BaseList<User> baseList = new BaseList<>();
            List<User> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    transactionList.add(this.bindUser(jsonObject));
                }
            } else {
            }
            baseList.setList(transactionList);
            return baseList;
        });
    }



    /**
     * delete merchant by user and type
     * @param connection
     * @param userId
     * @param type
     * @return
     */
    private Observable<Integer> deleteMerchantByUser(SQLConnection connection, int userId, String type){
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_MERCHANT_USER_ID, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(2);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] Delete Merchant Failed" + StringPool.SPACE + result.getOutput().getString(2));
                throw  IErrors.VALIDATION_ERROR;
            }

            return returnValue;
        });
    }

    /**
     * insert merchant by user and type
     * @param connection
     * @param userId
     * @param merchant
     * @return
     */
    private Observable<Integer> insertMerchantByUser(SQLConnection connection, Merchant merchant, int userId){
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(merchant.getMerchant_id())
                .add(merchant.getMerchant_name() != null ? merchant.getMerchant_name() : StringPool.BLANK)
                .add(merchant.getCurrency_code()!= null ? merchant.getCurrency_code() : "VND")
                .add(merchant.getType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(INSERT_MERCHANT_USER_ID, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(5);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] Insert Merchant Failed" + StringPool.SPACE +  result.getOutput().getString(6));
                throw  IErrors.VALIDATION_ERROR;
            }

            return returnValue;
        });
    }
    /**
     * delete merchant by user and type
     * @param connection
     * @param userId
     * @return
     */
    private Observable<Integer> deleteMerchantByUserV2(SQLConnection connection, int userId){
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_MERCHANT_USER_ID_V2, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(1);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] Delete Merchant Failed" + StringPool.SPACE + result.getOutput().getString(2));
                throw  IErrors.VALIDATION_ERROR;
            }

            return returnValue;
        });
    }

    /**
     * delete merchant by user and type
     * 
     * @param connection
     * @param userId
     * @param List<Merchants>
     * @return
     */
    private Observable<Integer> deleteMerchantByUserV3(SQLConnection connection, int userId, List<Merchant> merchants) {
        // Lấy danh sách type theo cấu hình merchant
        String merchantPermitConfig = merchants.stream()
                .map(Merchant::getType)  // Lấy danh sách type
                .distinct()           // Loại bỏ trùng lặp
                .collect(Collectors.joining(",")); // Nối thành chuỗi

        //xoa merchant permit theo list type
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(merchantPermitConfig);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_MERCHANT_USER_ID_V3, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(2);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE,
                        "[ MERCHANT ] Delete Merchant Failed" + StringPool.SPACE + result.getOutput().getString(3));
                throw IErrors.VALIDATION_ERROR;
            }

            return returnValue;
        });


        // return null;
    }

    private User bindUser(JsonObject rs) {
        String sid  = rs.getString("S_USER_ID");
        String phone = rs.getString("S_PHONE");
        String email = rs.getString("S_EMAIL");
//        try {
//            updateDate = formatter.parse(rs.getString("D_UPDATE"));
//        } catch (ParseException e) {
//          LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
//        try {
//            createDate = formatter.parse(rs.getString("D_CREATE"));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        User user =  new User();
        user.setS_id(sid);
        user.setPhone(phone);
        user.setEmail(email);

        return user;
    }


    public static final String SELECT_BY_MERCHANT = "{call PKG_MERCHANT_114.find_user_by_merchant(?,?,?,?,?,?,?)}";

    public static final String DELETE_MERCHANT_USER_ID = "{call PKG_MERCHANTPORTAL_2.DELETE_MERCHANT_USER_ID(?,?,?,?)}";

    public static final String INSERT_MERCHANT_USER_ID = "{call PKG_MERCHANTPORTAL_2.INSERT_MERCHANT_USER(?,?,?,?,?,?,?)}";

    public static final String DELETE_MERCHANT_USER_ID_V2 = "{call PKG_USER_V2.DELETE_MERCHANT_USER_ID(?,?,?)}";

    public static final String DELETE_MERCHANT_USER_ID_V3 = "{call PKG_USER_V2.DELETE_MERCHANT_USER_ID_V2(?,?,?,?)}";

    public static final String INSERT_MERCHANT_USER_ID_V2 = "{call PKG_USER_V2.INSERT_MERCHANT_USER(?,?,?,?,?,?,?)}";

    public static final String GET_USERS_BY_MERCHANT_ID = "{call PKG_MERCHANT_114.list_user_by_merchant(?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(UserMerchantImpl.class.getName());
}
