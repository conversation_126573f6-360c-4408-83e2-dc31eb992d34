package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.ManualRefundParameter;
import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.mpay.impl.MocaMpayTransactionService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.util.PropsUtil;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import javax.sql.DataSource;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 8/8/2016.
 */
@Service
public class RefundApprovalServiceImpl implements RefundApprovalService {

    @Override
    public Observable<RefundApproval> insert(SQLConnection backupConn, String userId, String merchantId, String transactionId,
                                             Double amount, String currency, String type, String transactionReference, Integer transactionType) {
        return this.insertApproval(backupConn, userId, merchantId, transactionId, amount, currency, type, transactionReference, transactionType).flatMap(id -> {
            return this.getById(backupConn, id);
        });
    }

    @Override
    public Observable<RefundApproval> insert2(SQLConnection backupConn, String userId, String merchantId, String transactionId,
                                              Double amount, String currency, String type, Integer parentId, String merchantTransRef, Integer status, Integer transactionType) {
        return this.insertApproval2(backupConn, userId, merchantId, transactionId, amount, currency, type, parentId, merchantTransRef, status, transactionType).flatMap(id -> {
            // call ep dong bo qr
            // String jobId = PropsUtil.get("onesched-service-qr_synx_id", "");
            // try {
            //     OneSchedClient.synchronize(jobId, "0s");
            // } catch (Exception e) {
            //     LOGGER.log(Level.INFO, "qr call ep dong bo error " + e.getMessage());
            // }
            return this.getById(backupConn, id);
        });
    }

    @Override
    public Observable<RefundApproval> insert3(SQLConnection backupConn, String userId, String merchantId, String transactionId
            , Double amount, String currency, String type, Integer parentId, String merchantTransRef
            , Integer status, Integer transactionType, String data) {
        return this.insertApproval3(backupConn, userId, merchantId, transactionId, amount, currency, type, parentId, merchantTransRef, status, transactionType, data).flatMap(id -> {
            // call ep dong bo qr
            // String jobId = PropsUtil.get("onesched-service-qr_synx_id", "");
            // try {
            //     OneSchedClient.synchronize(jobId, "0s");
            // } catch (Exception e) {
            //     LOGGER.log(Level.INFO, "qr call ep dong bo error " + e.getMessage());
            // }

            // call ep dong bo international
            // try {
            //         OneSchedClient.synchronizeInternationalFullSync();
            // } catch (Exception ex) {
            //         LOGGER.log(Level.INFO, "international call ep dong bo error " + ex.getMessage());
            // }
            return this.getById(backupConn, id);
        });
    }

    @Override
    public Observable<RefundApproval> insert3Mpay(SQLConnection backupConn, String userId, String merchantId, String transactionId
            , Double amount, String currency, String type, Integer parentId, String merchantTransRef
            , Integer status, Integer transactionType, String data) {
        return this.insertApproval3(backupConn, userId, merchantId, transactionId, amount, currency, type, parentId, merchantTransRef, status, transactionType, data).flatMap(id -> {
            // call ep dong bo qr
            // String jobId = PropsUtil.get("onesched-service-qr_synx_id", "");
            // try {
            //     OneSchedClient.synchronize(jobId, "0s");
            // } catch (Exception e) {
            //     LOGGER.log(Level.INFO, "qr call ep dong bo error " + e.getMessage());
            // }
            return this.getByIdMpay(backupConn, id);
        });
    }

    @Override
    public Observable<RefundApproval> get(SQLConnection backupConn, Integer id) {
        return this.getById(backupConn, id);
    }
    @Override
    public Observable<RefundApproval> getMpay(SQLConnection backupConn, Integer id) {
        return this.getByIdMpay(backupConn, id);
    }
    @Override
    public Observable update(SQLConnection backupConn, Integer id, Integer newStatus, String merchantTransRef) {
        JsonArray inParams = new JsonArray()
                .add(id)
                .add(merchantTransRef)
                .add(newStatus);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(UPDATE_APPROVAL_STATUS, inParams, outParams).map(rs -> {
            int resultCode = rs.getOutput().getInteger(3);

            if (resultCode != 200) {
                throw new DatabaseException(rs.getOutput().getString(4));
            }
            
            return resultCode;
        });
    }

    private Observable<Integer> insertApproval(SQLConnection backupConn, String userId, String merchantId, String transactionId,
                                               Double amount, String currency, String type, String transactionReference
            , Integer transactionType) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(merchantId)
                .add(transactionId)
                .add(amount)
                .add(currency)
                .add(type)
                .add(transactionReference)
                .add(transactionType);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(INSERT_APPROVAL, inParams, outParams).map(rs -> {
            Integer returnId = 0;
            int resultCode = rs.getOutput().getInteger(9);

            if (resultCode != 200) {
                throw new DatabaseException(rs.getOutput().getString(10));
            } else {
                returnId = rs.getOutput().getInteger(8);
            }
            return returnId;
        });
    }

    private Observable<Integer> insertApproval3(SQLConnection backupConn, String userId, String merchantId, String transactionId,
                                                Double amount, String currency, String type, Integer parentId, String merchantTransRef,
                                                Integer status, Integer transactionType, String data) {
        JsonArray inParams = new JsonArray()
                .add(parentId == null ? "" : parentId)
                .add(userId)
                .add(merchantId)
                .add(transactionId)
                .add(merchantTransRef)
                .add(amount)
                .add(currency)
                .add(type)
                .add(status)
                .add(data)
                .add(transactionType);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(INSERT_APPROVAL_3, inParams, outParams).map(rs -> {
            Integer returnId = 0;
            int resultCode = rs.getOutput().getInteger(12);

            if (resultCode != 200) {
                throw new DatabaseException(rs.getOutput().getString(13));
            } else {
                returnId = rs.getOutput().getInteger(11);
            }
            return returnId;
        });
    }

    private Observable<Integer> insertApproval2(SQLConnection backupConn, String userId, String merchantId, String transactionId,
                                                Double amount, String currency, String type, Integer parentId, String merchantTransRef, Integer status, Integer transactionType) {
        JsonArray inParams = new JsonArray()
                .add(parentId == null ? "" : parentId)
                .add(userId)
                .add(merchantId)
                .add(transactionId)
                .add(merchantTransRef)
                .add(amount)
                .add(currency)
                .add(type)
                .add(status)
                .add(transactionType);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(INSERT_APPROVAL_2, inParams, outParams).map(rs -> {
            Integer returnId = 0;
            int resultCode = rs.getOutput().getInteger(11);

            if (resultCode != 200) {
                throw new DatabaseException(rs.getOutput().getString(12));
            } else {
                returnId = rs.getOutput().getInteger(10);
            }
            return returnId;
        });
    }

    @Override
    public Observable<RefundApproval> getById(SQLConnection backupConn, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(GET_APPROVAL, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(2);

            if (resultCode != 200) {
                throw new DatabaseException(result.getOutput().getString(3));
            }
            RefundApproval approval = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            approval = bind(jsonObject);
            return approval;
        });
    }

    private RefundApproval bind(JsonObject rs) {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        RefundApproval result = new RefundApproval();

        Amount amount = new Amount();
        amount.setCurrency(rs.getString("S_CURRENCY"));
        amount.setTotal(rs.getDouble("N_AMOUNT"));

        result.setAmount(amount);
        result.setTransaction_id(rs.getInteger("N_ID"));
        result.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        result.setOriginal_transaction_id(rs.getString("N_TRANS_REF_ID"));
        result.setMerchant_transaction_ref(rs.getString("S_MERCHANT_TRANS_REF"));
        result.setTransaction_type(rs.getString("S_TYPE"));
        result.setData(rs.getString("S_DATA"));
        result.setParent_id(rs.getInteger("N_PARENT_ID"));
        result.setStatus(rs.getInteger("N_STATUS"));
        result.setOperator_id(rs.getString("S_APPROVE_USER"));
        result.setN_type(rs.getInteger("N_TYPE"));
        result.setNote(rs.getString("S_NOTE"));
        Timestamp createDate = Timestamp.valueOf(rs.getString("D_CREATE"));
//        try {
//            createDate = new Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        result.setTransaction_time(createDate);

        return result;
    }

    @Override
    public Observable<RefundApproval> getByIdMpay(SQLConnection backupConn, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(GET_APPROVAL, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(2);

            if (resultCode != 200) {
                throw new DatabaseException(result.getOutput().getString(3));
            }
            RefundApproval approval = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            approval = bindMpayMoca(jsonObject);
            return approval;
        });
    }

    private RefundApproval bindMpayMoca(JsonObject rs) {
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        RefundApproval result = new RefundApproval();

        Amount amount = new Amount();
        amount.setCurrency(rs.getString("S_CURRENCY"));
        amount.setTotal(rs.getDouble("N_AMOUNT"));

        result.setAmount(amount);
        result.setTransaction_id(rs.getInteger("N_ID"));
        result.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        result.setOriginal_transaction_id(rs.getString("N_TRANS_REF_ID"));
        result.setMerchant_transaction_ref(rs.getString("S_MERCHANT_TRANS_REF"));
        result.setTransaction_type(rs.getString("S_TYPE"));
        result.setData(rs.getString("S_DATA"));
        result.setParent_id(rs.getInteger("N_PARENT_ID"));
        result.setStatus(rs.getInteger("N_STATUS"));
        result.setOperator_id(rs.getString("S_APPROVE_USER"));
        result.setN_type(rs.getInteger("N_TYPE"));
        result.setBrandId(rs.getString("S_INS_BRAND_ID"));
        result.setDataPromotion(rs.getString("S_DATA_PROMOTION"));
        result.setSTransRefId(rs.getString("S_TRANS_REF_ID"));
        Timestamp createDate = Timestamp.valueOf(rs.getString("D_CREATE"));
//        try {
//            createDate = new Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        result.setTransaction_time(createDate);

        // Map<String, Object> promotionData = MocaMpayTransactionService.getMocaPromotion(rs.getString("S_DATA_PROMOTION"));
            //MOCA
            Double originalAmount = rs.getDouble("N_ORIGINAL_AMOUNT");
            Double inputAmount = rs.getDouble("N_AMOUNT");

            String clientData = rs.getString("S_DATA_PROMOTION");
            Map<String, Object> mocaPromotion =  MocaMpayTransactionService.getMocaPromotion(clientData);
            Double actutalAmount = 0d;
            // Double partnerDiscountRate =  (Double) mocaPromotion.get("partnerDiscountRate");
            // Double merchantDiscountRate = (Double) mocaPromotion.get("merchantDiscountRate");
            Double offerDiscountAmount = (Double) mocaPromotion.get("offerDiscountAmount");
            Double paymentAmount = (Double) mocaPromotion.get("paymentAmountPurchase");
            if (offerDiscountAmount != null || paymentAmount != null){
                actutalAmount =  MocaMpayTransactionService.caculateActualAmount(inputAmount, paymentAmount, offerDiscountAmount );
            }
            // Double actutalAmount =  MocaMpayTransactionService.caculateActualAmount(inputAmount,originalAmount,partnerDiscountRate,merchantDiscountRate,offerDiscountAmount );

        result.setPaymentAmount(actutalAmount == null ? 0 : actutalAmount);
        
            return result;
    }


    @Override
    public Observable<String> insertTblRefund(SQLConnection connOnline, ManualRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(parameter.getId())
                .add(parameter.getPaymentId())
                .add(parameter.getMerchantId())
                .add(parameter.getAmount())
                .add(parameter.getCurrency())
                .add(parameter.getClientId())
                .add(parameter.getClientRef())
                .add(parameter.getClientMerchantId())
                .add(parameter.getServerId())
                .add(parameter.getServerRef())
                .add(parameter.getServerMerchantId())
                .add(parameter.getDescription());
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return connOnline.callWithParamsObservable2(INSERT_REFUND, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            return jsonObject.getString("S_ID");
        });
    }

   
    @Override
    public Observable<String> updateRefundStatus(SQLConnection connOnline, String refundId) {

        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(refundId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .addNull();

        return connOnline.callWithParamsObservable2(UPDATE_REFUND_STATUS, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            return jsonObject.getString("S_ID");
        });
    }

    @Override
    public Observable updateErrorRefundStatus(SQLConnection backupConn, Integer approvalId, Integer errorStatus, String errorMessage) {
        LOGGER.info("-------------------------------");
        JsonArray inParams = new JsonArray()
                .add(approvalId)
                .add(errorStatus)
                .add(errorMessage);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(UPDATE_ERROR_REFUND_STATUS, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(3);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "UPDATE_ERROR_REFUND_STATUS " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return resultCode;
        });
    }

    @Override
    public Observable<String> updateRefundQrStatus(SQLConnection connOnline, String refundId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(refundId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .addNull();
        return connOnline.callWithParamsObservable2(UPDATE_REFUND_QR_STATUS, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            return jsonObject.getString("S_ID");
        });
    }

    @Override
    public Observable<String> updateDomesticRefundQrStatus(SQLConnection connOnline, Long refundTransactionId) {
        LOGGER.info("updateDomesticRefundQrStatus with refundTransactionId " + refundTransactionId);
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(refundTransactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .addNull();
        return connOnline.callWithParamsObservable2(UPDATE_REFUND_DOMESTIC_STATUS, inParams, outParams).map(result -> {
            LOGGER.info("UPDATE_REFUND_DOMESTIC_STATUS " + refundTransactionId + " result " + result.getOutput());
            if (null == result || result.getOutput().isEmpty() || null == result.getOutput().getJsonObject(1))
                return null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            return jsonObject.getString("S_ID");
        });
    }
    @Override
    public Observable<List<JsonObject>> getGetByTransRef(SQLConnection backupConn, String transRef) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(transRef);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .addNull();
//        List<JsonObject> out = new ArrayList<>();
        return  backupConn.callWithParamsObservable2(GET_APPROVAL_BY_TRANS_REF, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().isEmpty()) return null;
            return rs.getRows();
        });
    }

    private static final String GET_APPROVAL_BY_TRANS_REF = "{? = call GET_REQUEST_REFUND_BY_REF(?,?)}";

    private static final String INSERT_APPROVAL = "{call PKG_REFUND_APPROVE.refund_approval_insert(?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String INSERT_APPROVAL_2 = "{call PKG_REFUND_APPROVE.refund_approval_insert_2(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String INSERT_APPROVAL_3 = "{call PKG_REFUND_APPROVE.refund_approval_insert_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String UPDATE_APPROVAL_STATUS = "{call PKG_REFUND_APPROVE.refund_approval_update_status(?,?,?,?,?)}";

    private static final String GET_APPROVAL = "{call PKG_REFUND_APPROVE.refund_approval_get2(?,?,?,?)}";

    private static final String SEARCH_APPROVAL = "{call PKG_REFUND_APPROVE.refund_approval_search(?,?,?,?,?,?,?,?,?,?)}";

    private static final String INSERT_REFUND = "{? = call MSP.CREATE_REFUND(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String UPDATE_ITA_STATE = "{? = call MSP.UPDATE_ITA_STATE(?,?,?)}";

    private static final String UPDATE_REFUND_STATUS = "{? = call MSP.REFUND_UPDATE_APPROVED(?,?)}";

    private static final String UPDATE_ERROR_REFUND_STATUS = "{call PKG_REFUND_APPROVE.update_error_status(?,?,?,?,?)}";

    private static final String UPDATE_REFUND_QR_STATUS = "{? = call MSP.REFUND_QR_UPDATE_FAIL(?,?)}";

    private static final String UPDATE_REFUND_DOMESTIC_STATUS = "{? = call merchantportal.u_domestic_by_refund_txnid(?,?)}";

    private static final Logger LOGGER = Logger.getLogger(RefundApprovalService.class.getName());

}
