package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.models.Permission;
import com.onepay.ma.service.models.Permissions;
import com.onepay.ma.service.service.UserPermissionService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
@Service
public class UserPermissionImpl implements UserPermissionService {

    @Override
    public Observable<Permissions> list(SQLConnection connection, String sid) {
        return getPermissionList(connection, sid);
    }

    /**
     * get permission list
     * @param sqlConnection
     * @param sid
     */
    private Observable<Permissions> getPermissionList(SQLConnection sqlConnection, String sid){
        JsonArray inParams = new JsonArray().add(sid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_ONE_AM_USER_PERMISSION, inParams, outParams).map(result -> {
            Permissions permissions = new Permissions();
            List<Permission> permissionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0){
                for (JsonObject jsonObject : rs.getRows()) {
                    Permission permission = resultSetToPermission(jsonObject);
                    permissionList.add(permission);
                }
                permissions.setTotal_items(permissionList.size());
            }else{
                permissions.setTotal_items(0);
            }

            permissions.setPermissions(permissionList);
            return permissions;
        });

    }

    /**
     * get result set from permission
     * @param rs
     * @return
     * @throws SQLException
     */
    private Permission resultSetToPermission(JsonObject rs) {
        int id = rs.getInteger("FUNC_ID");
        int parentId = rs.getInteger("FUNC_PARENT_ID");
        String name = rs.getString("FUNC_NAME");
        String moduleUrl = rs.getString("S_MODULE_URL");
        String icon = rs.getString("FUNC_ICON");
        String state = rs.getString("FUNC_STATE");
        boolean isAbstract = rs.getInteger("N_ABSTRACT") != null &&  rs.getInteger("N_ABSTRACT") == 1 ? true : false;
        String moduleName = rs.getString("S_MODULE_NAME");
        String controller = rs.getString("S_CONTROLLER_URL");
        //  String factoryUrl = rs.getString("S_FACTORY_URL");
        String directiveUrl = rs.getString("S_DIRECTIVE_URL");
        boolean isRootPermission = rs.getInteger("N_ROOT_PERMISSION") != null &&  rs.getInteger("N_ROOT_PERMISSION") == 1 ? true : false;
        String item_name = rs.getString("S_ITEM_NAME");
        //String abstractStateUrl = rs.getString("S_ABSTRACT_STATE_URL");
        String path = rs.getString("S_PATH_REGEX");
        //String method = rs.getString("S_METHOD");
        int order = rs.getInteger("N_ORDER");

        Permission permission = new Permission();
        permission.setPermission_id(id);
        permission.setParent_id(parentId);
        permission.setIs_abstract(isAbstract);
        permission.setPermission_name(name);
        permission.setIcon(icon);
        permission.setState(state);
        permission.setModule_url(moduleUrl);
        permission.setModule_name(moduleName);
        permission.setController_url(controller);
        permission.setIs_root_permission(isRootPermission);
        // permission.setFactory_url(factoryUrl);
        permission.setDirective_url(directiveUrl);
        permission.setItem_name(item_name);
        // permission.setTemplate_module_url(templateUrl);
        // permission.setAbstract_state_url(abstractStateUrl);

        permission.setPath_regex(path);
        permission.setOrder(order);

        return permission;
    }

    public static final String GET_ONE_AM_USER_PERMISSION = "{call PKG_MERCHANTPORTAL_2.GET_LIST_USER_FUNC(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(UserPermissionImpl.class.getName());



}
