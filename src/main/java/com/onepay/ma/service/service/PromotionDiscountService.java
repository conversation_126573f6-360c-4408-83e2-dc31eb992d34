package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionDiscount;
import com.onepay.ma.service.models.PromotionDiscountType;
import com.onepay.ma.service.models.PromotionDiscountTypeList;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionDiscountService {

    Observable<PromotionDiscountTypeList<PromotionDiscountType>> listDiscountType(SQLConnection sqlConnPr);

    Observable<PromotionDiscountType> getDiscountType(SQLConnection sqlConnPr, int discountTypeId);

    Observable<List<PromotionDiscount>> listDiscount(SQLConnection sqlConnPr, String promotionId, String promotionSid);

    Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, PromotionDiscount promotionDiscount);

    Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId);
}
