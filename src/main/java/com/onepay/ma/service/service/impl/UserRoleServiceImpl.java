package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.service.UserRoleService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
@Service
public class UserRoleServiceImpl implements UserRoleService {
    @Override
    public Observable<Integer> insert(SQLConnection connection, int userId, String roleId) {
        return insertRoleUser(connection, userId, roleId);
    }

    @Override
    public Observable<Integer> delete(SQLConnection connection, int userId) {
        return deleteRoleUser(connection, userId);
    }
    @Override
    public Observable<Integer> insertList(SQLConnection connection, int userId, String listRoleId){
        return insertListRoleUser(connection, userId, listRoleId);
    }

    /**
     * insert role user
     * @param connection
     * @param userId
     * @param roleId
     * @return
     */
    private Observable<Integer> insertRoleUser(SQLConnection connection, int userId, String roleId){
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(roleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(INSERT_USER_ROLE, inParams, outParams).map(result -> {
            int returnId = result.getOutput().getInteger(2);
            if(returnId == 0){
                throw  new DatabaseException("insert user role failed");
            }

            return returnId;
        });
    }

    /**
     * delete role user
     * @param connection
     * @param userId
     * @return
     */
    private Observable<Integer> deleteRoleUser(SQLConnection connection, int userId){
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_USER_ROLE, inParams, outParams).map(result -> {
            int returnId = result.getOutput().getInteger(1);
            if(returnId == 0){
                throw  new DatabaseException("Delete user role failed");
            }

            return returnId;
        });
    }
    /**
     * insert list role user
     * @param connection
     * @param userId
     * @param listRoleId
     * @return
     */
    private Observable<Integer> insertListRoleUser(SQLConnection connection, int userId, String listRoleId){
        if(!"".equals(listRoleId)) {
            JsonArray inParams = new JsonArray()
                    .add(userId)
                    .add(listRoleId);
            JsonArray outParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.VARCHAR);
            return connection.callWithParamsObservable2(INSERT_LIST_USER_ROLE, inParams, outParams).map(result -> {
                int returnId = result.getOutput().getInteger(2);
                if (returnId != 201) {
                    LOGGER.log(Level.SEVERE, "[INSERT USER ROLE ERROR]: " + result.getOutput().getString(3));
                    throw new DatabaseException("insert list user role failed");
                }

                return returnId;
            });
        }
        return Observable.just(1);
    }

    private static final String INSERT_USER_ROLE = "{call PKG_MERCHANTPORTAL_2.ROLE_USER_INSERT(?,?,?,?)}";

    private static final String DELETE_USER_ROLE = "{call PKG_MERCHANTPORTAL_2.ROLE_USER_DELETE(?,?,?)}";

    private static final String INSERT_LIST_USER_ROLE = "{call PKG_USER_V2.ROLE_USER_INSERT(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(UserRoleServiceImpl.class.getName());
}
