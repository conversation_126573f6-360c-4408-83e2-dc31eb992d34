package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.pay_out.ModelDto;
import com.onepay.ma.service.service.pay_out.MerchantAccountService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Handle service for MerchantAccountServiceImpl
 * by Tiennv
 * 17/11/2020
 */

@Service
public class MerchantAccountServiceImpl implements MerchantAccountService {

    public static final String GET_MERCHANT_ACCOUNT = "{? = call ONEPAYOUT.G_MERCHANT_ACCOUNT(?,?,?,?) }";

    /**
     * Handle for search a list MerchantAccountService
     * by Tiennv
     * 17/11/2020
     */
    public Observable<List<ModelDto>> getListMerchantAccount(SQLConnection connection, String merchantId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull();

        return connection.callWithParamsObservable2(GET_MERCHANT_ACCOUNT, inParams, outParams).map(result -> {
            List<ModelDto> modelDtos = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            ModelDto modelDto;
            for (JsonObject jsonObject : rs.getRows()) {
                modelDto = new ModelDto(jsonObject.getString("S_ID"), jsonObject.getString("S_ID"), jsonObject.getLong("N_BALANCE"));
                modelDtos.add(modelDto);
            }
            return modelDtos;
        });
    }

    ;


}
