package com.onepay.ma.service.service.pay_collect.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_collect.Bank;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.models.pay_collect.UserQueryPayCollectDto;
import com.onepay.ma.service.service.pay_collect.PartnerConfigService;
import com.onepay.ma.service.service.pay_collect.UserConfigService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/7/2020
 * Time: 10:34 AM
 * To change this ma-web.
 */
@Service
public class PayCollectPartnerConfigServiceImpl implements PartnerConfigService {
    public static final String G_PARTNER_CONFIG = "{call PKG_MA_PAYCOLLECT.G_PARTNER_CONFIG(?,?,?,?) }";
    private static final Logger LOGGER = Logger.getLogger(PayCollectPartnerConfigServiceImpl.class.getName());
    @Override
    public Observable<JsonObject> getPartnerConfig(SQLConnection connection, String partner_id) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(partner_id)
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                ;
        return connection.callWithParamsObservable2(G_PARTNER_CONFIG, inParams, outParams).map(result -> {
            if (result.getOutput().getInteger(1) != 200) {
                LOGGER.log(Level.SEVERE, "[GET ALL PAYCOLLECT BANK ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                return jsonObject;
            }
            return null;
        });
    }
}
