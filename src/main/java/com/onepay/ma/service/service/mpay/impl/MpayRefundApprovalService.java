package com.onepay.ma.service.service.mpay.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.models.mpay.MpayRefund;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;

import groovy.json.JsonException;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;

public class MpayRefundApprovalService {
    

    private static final Logger LOGGER = Logger.getLogger(MpayRefundApprovalService.class.getName());

    private static final String TRANSACTION_APPROVAL_GET_BY_ID = "{call PKG_REFUND_APPROVE.get_refund_by_id(?,?,?,?)}";

    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_REFUND_APPROVE.get_refund_total(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    
    private static final String LIST_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    public static Observable<NewBaseList<MpayRefund>> list(SQLConnection sqlOnline, SQLConnection sqlBackup, MpayRefundParameter parameter) {

        NewBaseList<MpayRefund> transactions = new NewBaseList<>();
        return getListTotalTransactionBackup(sqlBackup, parameter).flatMap(totalApproval -> {
            
            return MpayTransactionService.mapByIds(sqlOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(totalPurchaseMap -> {

                Integer totalBackup = countFilterById(totalPurchaseMap, totalApproval);
                transactions.setTotalItems(totalBackup);
                return getListTransactionBackup(sqlBackup, parameter).flatMap(refundApproval -> {

                    // JOIN purchase INFO ------->  filter != null
                    List<MpayRefund> refundApproval2 = refundApproval
                    .stream()
                    .map(approval -> {
                        return joinApproveWithPurchase(approval, totalPurchaseMap.get(approval.getTransactionId()));
                    })
                    .filter(domesticRefund -> domesticRefund != null)
                    .collect(Collectors.toList());
                    transactions.setList(refundApproval2);
                    return Observable.just(transactions);
                });
            });
        });
    }

    public static Observable<MpayRefund> getById(SQLConnection sqlBackup, String transactionId) {
        JsonArray inParams = new JsonArray()
            .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBackup.callWithParamsObservable2(TRANSACTION_APPROVAL_GET_BY_ID, inParams, outParams).map(result -> {
            MpayRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindApproval(jsonObject);


            return transaction;
        });

    }

    

    public static MpayRefund joinApproveWithPurchase(MpayRefund approve, MpayTransaction purchase) {


        if (purchase == null) {
            return null;
        }

        MpayRefund result = approve;
        result.setPurchaseDate(purchase.getCreateTime());
        result.setOrderInfo(purchase.getOrderInfo());
        //        result.setA(purchase.getAcquirer());
        result.setBankTransId(purchase.getBankTransId());
        result.setMerchantName(purchase.getMerchantName());
        result.setCustomerTransId(purchase.getCustomerTransId());
        result.setCardNumber(purchase.getInstrument().getNumber());
        result.setCardHolderName(purchase.getInstrument().getName());
        result.setInstrumentName(purchase.getInstrument().getName());
        result.setInstrumentBrand(purchase.getInstrument().getBrandId());
        result.setInstrumentType(purchase.getInstrument().getType());
        result.setPurchaseAmount(purchase.getAmount().getTotal());
        result.setMasking(purchase.getMasking());
        result.setAppName(purchase.getAppName());
        result.setOrderInfo(purchase.getOrderInfo());
        result.setAcqCode(purchase.getAcqCode());
        result.setChannel(handleChannel(purchase.getClientId()));
        result.setPaymentAmount(purchase.getPaymentAmount());
        result.setOfferDiscountAmount(purchase.getOfferDiscountAmount());
        return result;
    }
    private static Integer countFilterById(Map<String, MpayTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(String.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }
    /**
     * get  transaction data total
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    public static Observable<List<String>> getListTotalTransactionBackup(SQLConnection connBackup, MpayRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getStatus())
                .addNull()
                .add(RefundData.Type.QR.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_TOTAL_B, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(11);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL DOMESTIC REFUND ERROR : " + resultSet.getOutput().getString(12));
                throw IErrors.DOMESTIC_SERVER_ERROR;
            }

            List<String> result = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(10).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result.add(jsonObject.getString("N_ORIGINAL_ID"));
            }

            return result;
        });
    }

    
    /**
     * get  transaction data online database
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    private static Observable<List<MpayRefund>> getListTransactionBackup(SQLConnection connBackup, MpayRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getStatus())
                .addNull()
                .add(RefundData.Type.QR.getValue())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(result -> {
            List<MpayRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(12).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MpayRefund transaction = bindApproval(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    private static MpayRefund bindApproval(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        String originalId = rs.getString("N_ORIGINAL_ID");
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInteger("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");
        
        String subData = rs.getString("S_DATA");
        String note = null;
        JsonObject jsonObject = null;
        try {
            jsonObject = new JsonObject(subData);
        }catch (JsonException err){
            Logger.getLogger("Error", err.toString());
        }

        try {
            note = jsonObject.getString("note");
        }catch (JsonException err){
            Logger.getLogger("Error", err.toString());
        }


        MpayRefund transaction = new MpayRefund();
        transaction.setMerchantId(merchantId);
        transaction.setOrderInfo(orderInfo);
        transaction.setStatus(String.valueOf(status));
        transaction.setRefundDate(date);
        transaction.setMerchantTxnRef(transactionRef);
        transaction.setId(String.valueOf(transactionId));
        transaction.setTransactionType(transactionType);
        transaction.setTransactionId(originalId);
        transaction.setRefundAmount(total);
        transaction.setCurrency(currency);
        transaction.setNote(note);

        return transaction;
    }

    private static String handleChannel(String inputData) {
        String outputData = null;
        if (inputData.equals("DSP")) {
            outputData = "mPayVN";
        } else if (inputData.equals("VIETINQR")) {
            outputData = "Vietin";
        } else if (inputData.equals("DSP_VRBANK")) {
            outputData = "VRB";
        } else if (inputData.equals("MSBQR")) {
            outputData = "MSB";
        } else {
            outputData = inputData;
        }
        return outputData;
    }


}
