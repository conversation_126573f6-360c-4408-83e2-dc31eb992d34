package com.onepay.ma.service.service.financial;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.financial.FinancialTransaction;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.models.financial.SamsungFinancialTransaction;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by tuydv on 09-5-18.
 */
public class SSFinancialTransactionService {


    private static final String SEARCH_SAMSUNG_TRANSACTION = "{call PKG_ONECREDIT.search_samsung_tran_migs_4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String SAMSUNG_TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_SAMSUNG_TRAN_MIGS_BY_ID(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(SSFinancialTransactionService.class.getName());

    private final static Gson gson = new Gson();


    public  static Observable<Transactions<SamsungFinancialTransaction>> search(SQLConnection connReadOnly, SQLConnection connOnline, FinancialTransactionQuery query) {

        // LOGGER.log(Level.INFO, "query condition: " + gson.toJson(query));

        Transactions<SamsungFinancialTransaction> transactions = new Transactions<>();

        return getListTotalTransaction(connOnline, query,-1).flatMap(total1 -> {
            return getListTotalTransaction(connReadOnly, query,1).flatMap(total2 -> {
                transactions.setTotal_items(total1 + total2);
                int basePageSize = query.getPageSize();
                // Get online transactions
                query.setOffset(0);
                return searchTransaction(connOnline, query,-1).flatMap(onlineTrans -> {
                    // offset
                    query.setOffset(total1);
                    // Get readonly transaction
                    return searchTransaction(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {

                        onlineTrans.addAll(readOnlyTrans);
                        // Mark number index
                        int index = 1;
                        for (SamsungFinancialTransaction transaction: onlineTrans) {
                            transaction.setRow_num(index + (query.getPage() * basePageSize));
                            index++;
                        }

                        // set & Return.
                        transactions.setTransactions(onlineTrans);

                        return Observable.just(transactions);
                    });
                });
            });
        });
    }


    public static Observable<SamsungFinancialTransaction> get(SQLConnection connOnline, String Id) {
        JsonArray inParams = new JsonArray()
                .add(Id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(SAMSUNG_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET FINANCIAL TRANSACTION DETAIL ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            SamsungFinancialTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindSamsungTransaction(jsonObject);

            return transaction;
        });
    }

    private static Observable<Integer> getListTotalTransaction(SQLConnection connOnline, FinancialTransactionQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(query.getStatus())
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile()== null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email()== null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website()== null ? "" : query.getMerchant_website())
                .add(query.getFraud_check()== null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code() == null ? "" : query.getAuthorisation_code())
                .add(query.getOrder_status() == null ? "" : query.getOrder_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(SEARCH_SAMSUNG_TRANSACTION, inParams, outParams).map(result -> {
            Integer total = 0;
            Integer reusltCode = result.getOutput().getInteger(25);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH SAMSUNG Financial TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(26));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(27).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });
    }



    private  static Observable<List<SamsungFinancialTransaction>> searchTransaction(SQLConnection sqlConnection, FinancialTransactionQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(query.getStatus())
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getOffset())
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile())
                .add(query.getCustomer_email())
                .add(query.getMerchant_website())
                .add(query.getFraud_check())
                .add(query.getAuthorisation_code())
                .add(query.getOrder_status())
                .add(query.getRisk_assessment())
                .add(query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return sqlConnection.callWithParamsObservable2(SEARCH_SAMSUNG_TRANSACTION, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(25);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH Samsung Financial TRANSACTION ERROR]: " + result.getOutput().getString(26));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<SamsungFinancialTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(27).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    SamsungFinancialTransaction transaction = bindSamsungTransaction(jsonObject);
                    transaction.setAdvance_status(jsonObject.getString("S_ADVANCE_STATUS"));
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }


    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, FinancialTransactionQuery query) {
        return getListTotalTransaction(connOnline, query,-1).flatMap(totalItem1 -> {
            return getListTotalTransaction(connReadOnly, query,1).map(totalItem2 -> {
                return totalItem1 + totalItem2;
            });
        });
    }

    public static ResultSet downloadOnline(Connection connOnline, FinancialTransactionQuery parameter) throws SQLException {
        return downloadData(connOnline, parameter,-1);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, FinancialTransactionQuery parameter) throws SQLException {
        return downloadData(connReadOnly, parameter,1);
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param query
     * @return
     */
    private static ResultSet downloadData(Connection connection, FinancialTransactionQuery query, int row) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(query.getStatus())
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile()== null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email()== null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website()== null ? "" : query.getMerchant_website())
                .add(query.getFraud_check()== null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code()== null ? "" : query.getAuthorisation_code())
                .add(query.getOrder_status() == null ? "" : query.getOrder_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);


        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_SAMSUNG_TRANSACTION, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(26);

            if (reusltCode != 200) {
                LOGGER.log(Level.WARNING, "[DOWNLOAD SS FINANCIAL TRANSACTION ONLINE ERROR]: " + callableStatement.getString(27));
            }

            rs = (ResultSet) callableStatement.getObject(28);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private  static SamsungFinancialTransaction bindSamsungTransaction(JsonObject rs){
        Integer id = rs.getInteger("N_ID");
        String transaction_no = rs.getString("S_TRANSACTION_NO");
        Timestamp date = rs.getString("D_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_DATE"));
        String merchantId = rs.getString("S_MERCHANTID");
        String acquirerid = rs.getInteger("N_ACQUIRERID").toString();
        String trans_ref = rs.getString("S_MERCHANTTRANSACTIONREFEREN");
        String trans_type = rs.getString("S_TRANSACTIONTYPE");
        double total = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCY");
        String response = rs.getString("S_RESPONSECODE");
        String order_ref = rs.getString("S_ORDERREFERENCE");
        String card_number = rs.getString("S_CARDNO");
        String auth_code = rs.getString("S_AUTHORISATIONCODE");
        String payment_auth_id = rs.getString("S_PAYMENT_AUTHENTICATION_ID");
        String batch_number = rs.getString("S_BATCHNUMBER");
        Integer transactionId = rs.getInteger("ONECREDIT_TRANS_ID");
        String cardType = rs.getString("S_CARDTYPE");
        String base_status = rs.getString("S_STATUS");
        String order_status = rs.getString("S_REVIEW_STATE");
        String order_desc = rs.getString("S_REVIEW_DESC");
        String s_date = rs.getString("S_DATA");
        int n_fraud = rs.getInteger("N_FRAUD") == null ? 0 : rs.getInteger("N_FRAUD");
        String risk_assesment = null;
        try {
            risk_assesment = rs.getString("S_FRAUD_INFO_FOR_MER_ALERT");
        }catch (Exception e){

        }
       /* Acquirer acquirer = new Acquirer();
//        acquirer.setCard_type(rs.getInteger("S_ACQUIRER_ID"));
        acquirer.setAcquirer_name(rs.getString("S_INSTRUMENT_TYPE"));*/

        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(0.0); //TODO: fill correct one.



        SamsungFinancialTransaction transaction = new SamsungFinancialTransaction();
        if(trans_type.equals("Purchase") ){
            if(risk_assesment != null){
                transaction.setRisk_assesment("Review Required");
            } else{
                transaction.setRisk_assesment("Not Assessed");
            }
        }
        transaction.setId(id);
        transaction.setTrans_no(transaction_no);
        transaction.setDate(date);
        transaction.setMerchant_id(merchantId);
        transaction.setAcquirer(acquirerid);
        transaction.setTrans_ref(trans_ref);
        transaction.setTrans_type(trans_type);
        transaction.setCurrency(currency);
        transaction.setCard_type(cardType);
        transaction.setResponse(response);
        transaction.setOrder_ref(order_ref);
        transaction.setCard_number(card_number);
        transaction.setAuth_code(auth_code);
        transaction.setAmount(amount);
        transaction.setPayment_auth_id(payment_auth_id);
        transaction.setBatch_number(batch_number);
        transaction.setTranaction_id(transactionId==null?"":transactionId.toString());
        transaction.setBase_status(base_status);

        // SAMSUNG
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));
        transaction.setOrder_status(order_status);
        transaction.setOrder_desc(order_desc);
        transaction.setN_fraud(n_fraud);
        // Return.
        return transaction;
    }



}
