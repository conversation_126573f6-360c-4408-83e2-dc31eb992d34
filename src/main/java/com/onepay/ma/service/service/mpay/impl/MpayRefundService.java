package com.onepay.ma.service.service.mpay.impl;

import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.mpay.MpayRefund;
import com.onepay.ma.service.models.mpay.MpayRefundParameter;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
@Service
public class MpayRefundService {

    private static final String UPDATE_QR_TRANSACTION_STATUS_BY_ID = "{call PKG_MPAY.UPDATE_QR_REFUND_STATUS_BY_ID(?,?,?,?)}";

    public static ResultSet downloadOnline(Connection connOnline, MpayRefundParameter parameter) throws SQLException {
        return download(connOnline, parameter, -1);
    }
    public static ResultSet downloadReadonly(Connection connReadOnly, MpayRefundParameter parameter, int rows) throws SQLException {
        return download(connReadOnly, parameter, rows);
    }

    public static Observable<NewBaseList<MpayRefund>> list(SQLConnection sqlReadOnly, SQLConnection sqlOnline, MpayRefundParameter parameter) {
        NewBaseList<MpayRefund> transactions = new NewBaseList<>();
        int basePageSize = parameter.getPageSize();

        LOGGER.log(Level.INFO, "------------------START GET TOTAL Mpay Refund Online----------------- ");
        return getListTotalTransaction(sqlOnline, parameter, -1).flatMap(totalItem1 -> {
            LOGGER.log(Level.INFO, "------------------END  GET TOTAL Mpay Refund Online " + totalItem1 + "----------------- ");

            LOGGER.log(Level.INFO, "------------------START GET TOTAL Mpay Refund READONLY----------------- ");
            return getListTotalTransaction(sqlReadOnly, parameter, totalItem1).flatMap(totalItem2 -> {
                LOGGER.log(Level.INFO, "------------------END  GET TOTAL Mpay Refund READONLY " + totalItem2 + "----------------- ");
                transactions.setTotalItems(totalItem1 + totalItem2 );
                parameter.setOffset(0);

                LOGGER.log(Level.INFO, "------------------START GET Mpay refund ONLINE----------------- ");
                return getListTransaction(sqlOnline, parameter, -1).flatMap(transactionsListOnline -> {
                    LOGGER.log(Level.INFO, "------------------END GET Mpay refund ONLINE----------------- ");
                    LOGGER.log(Level.INFO, "ONLINE : " + transactionsListOnline.size());
                        LOGGER.log(Level.INFO, "------------------ CASE ONLINE + APPROVAL < PAGESIZE----------------- ");
                        parameter.setOffset(totalItem1);
                        LOGGER.log(Level.INFO, "------------------START GET Mpay refund READONLY----------------- ");
                        return getListTransaction(sqlReadOnly, parameter, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                            LOGGER.log(Level.INFO, "------------------END GET Mpay refund READONLY----------------- ");
                            List<MpayRefund> transactionsFinal = new ArrayList<>();
                            transactionsFinal.addAll(transactionsListOnline);
                            transactionsFinal.addAll(transactionsListReadOnly);
                            int index = 1;
                            for (MpayRefund transaction : transactionsFinal) {
                                transaction.setRow_num(index + (parameter.getPage() * basePageSize));
                                index++;
                            }
                            transactions.setList(transactionsFinal);
                            return Observable.just(transactions);
                        });
                });
            });
        });
    }


    public static Observable<MpayRefund> get(SQLConnection sqlOnline, String transactionId) {
        return getTransaction(sqlOnline, transactionId).flatMap(mpayRefund -> {
            return Observable.just(mpayRefund);
        });
    }
    

    public static Observable<Void> updateStatus(SQLConnection connOnline, String transactionId, String status) {
        JsonArray inParams = new JsonArray()
        .add(transactionId)
        .add(status);
        JsonArray outParams = new JsonArray()
        .addNull()
        .add(OracleTypes.INTEGER)
        .add(OracleTypes.VARCHAR)
           .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(UPDATE_QR_TRANSACTION_STATUS_BY_ID, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(1);
            if(code != 200) {
                LOGGER.log(Level.SEVERE, "ERROR ON UPDATE QR TRANSACTION STATUS " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;
        });
    }

    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection readOnlyConn, MpayRefundParameter parameter) {
                // TOTAL ONLINE
                return getListTotalTransaction(connOnline, parameter, -1).flatMap(totalItem1 -> {
                    // TOTAL READONLY
                    return getListTotalTransaction(readOnlyConn, parameter, totalItem1).map(totalItem2 -> {
                        return totalItem1 + totalItem2 ;
                    });
                });
    }

    public static ResultSet download(Connection conn, MpayRefundParameter parameter, int rows) throws SQLException {
        return downloadData(conn, parameter, rows);
    }


    private static Observable<List<MpayRefund>> getListTransaction(SQLConnection connection, MpayRefundParameter parameter, int rows) {

        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getMasking() == null ? "" : parameter.getMasking())
                .add(parameter.getAppName() == null ? "" : parameter.getAppName())
                .add(parameter.getQrId() == null ? "" : parameter.getQrId())
                .add(parameter.getClientId())
                .add(parameter.getVersion())
                .add(parameter.getPage())
                .add(parameter.getPageSize())
                .add(parameter.getOffset())
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_TRANSACTION_V2, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(23) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH MPAY REFUND ERROR]: " + result.getOutput().getString(24));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(22).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                MpayRefund transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    private static Observable<Integer> getListTotalTransaction(SQLConnection connection, MpayRefundParameter parameter, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getMerchantTransactionReference() == null ? "" : parameter.getMerchantTransactionReference())
                .add(parameter.getMasking() == null ? "" : parameter.getMasking())
                .add(parameter.getAppName() == null ? "" : parameter.getAppName())
                .add(parameter.getQrId() == null ? "" : parameter.getQrId())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getVersion())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_TRANSACTION_V2, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(23) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH MPAY REFUND TOTAL ERROR]: " + result.getOutput().getString(24));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            Map map = result.getOutput().getJsonObject(22).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadData(Connection connection, MpayRefundParameter parameter, int rows) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getAcqCode())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getMasking()  == null ? "" : parameter.getMasking())
                .add(parameter.getAppName()  == null ? "" : parameter.getAppName())
                .add(parameter.getQrId() == null ? "" : parameter.getQrId())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getVersion())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION_V2, inParams, outParams);
            Integer reusltCode = callableStatement.getInt(24);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD MPAY REFUND ERROR]: " + callableStatement.getString(25));
            }
            rs = (ResultSet) callableStatement.getObject(23);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private static Observable<MpayRefund> getTransaction(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            MpayRefund transaction = null;


            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[GET MPAY REFUND DETAIL ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }
    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static MpayRefund bindTransaction(JsonObject rs) {
        String id = rs.getString("S_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT");
        String instrumentType = rs.getString("S_INSTRUMENT_TYPE");
        String instrumentName = rs.getString("S_INSTRUMENT_NAME");
        String instrumentBrand = rs.getString("S_INS_BRAND_ID");
        String transactionId = rs.getString("S_PAYMENT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        Timestamp purchaseDate = Timestamp.valueOf(rs.getString("D_PURCHASE_DATE"));
        double purchaseAmount = rs.getDouble("N_PURCHASE_AMOUNT");
        Timestamp refundDate = Timestamp.valueOf(rs.getString("D_REFUND_DATE"));
        double refundAmount = rs.getDouble("N_REFUND_AMOUNT");
        String description = rs.getString("S_DESCRIPTION");
        String clientData = rs.getString("S_DATA");
        Double paymentAmount = rs.getDouble("N_SETTLEMENT_AMOUNT");
        JsonObject jClientData = null;
        try {
            if (Objects.nonNull(clientData) && !Strings.isBlank(clientData) && !Strings.isEmpty(clientData)) {
                jClientData = new JsonObject(clientData);
            }
        } catch (Exception e) {
            Logger.getLogger("Client data parse error", e.toString());
        }

//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
//        try {
//            purchaseDate = new Timestamp(formatter.parse(rs.getString("D_PURCHASE_DATE")).getTime());
//            date = new Timestamp(formatter.parse(rs.getString("D_MERCHANT_TRANSACTION_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        String status = rs.getString("S_STATE");

        String bankTransId = rs.getString("S_BANK_TRANS_ID");
        String customerTransId = rs.getString("S_CUSTOMER_TRANS_ID");
        String acqCode = rs.getString("ACQ_CODE");
        String cardNumber = rs.getString("S_INSTRUMENT_NUMBER");
        String currency = rs.getString("S_CURRENCY");
        String merchantTxnRef = rs.getString("S_MERCH_TXN_REF");

        MpayRefund transaction = new MpayRefund();
        transaction.setId(id);
        transaction.setMerchantId(merchantId);
        transaction.setMerchantName(merchantName);
        transaction.setOnecomMerchantId(onecomMerchantId);
        transaction.setInstrumentType(instrumentType);
        transaction.setInstrumentBrand(instrumentBrand);
        transaction.setInstrumentName(instrumentName);
        transaction.setTransactionId(transactionId);
        transaction.setOrderInfo(orderInfo);
        transaction.setPurchaseDate(purchaseDate);
        transaction.setPurchaseAmount(purchaseAmount);
        transaction.setRefundDate(refundDate);
        transaction.setRefundAmount(refundAmount);
        transaction.setStatus(status);
        transaction.setBankTransId(bankTransId);
        transaction.setCustomerTransId(customerTransId);
        transaction.setAcqCode(acqCode);
        transaction.setCardNumber(cardNumber);
        transaction.setCurrency(currency);
        transaction.setMerchantTxnRef(merchantTxnRef);
        transaction.setNote(description);

        transaction.setChannel(rs.getString("S_CLIENT_ID") == null ? "" : rs.getString("S_CLIENT_ID"));
        transaction.setAppName(rs.getString("S_APP_NAME"));
        transaction.setInvoiceId(rs.getString("S_INVOICE_ID"));
        transaction.setMasking(rs.getString("S_MASKING"));
        transaction.setQrId(rs.getString("S_QR_ID"));
        transaction.setCardHolderName(rs.getString("S_INSTRUMENT_NAME"));
        if (jClientData != null) {
            JsonObject jMsb = jClientData.containsKey("msb_qr") ? jClientData.getJsonObject("msb_qr") : new JsonObject();
            transaction.setBankMerchantId(jMsb.containsKey("bank_merchant_id") ? jMsb.getString("bank_merchant_id") : "");
            transaction.setBankTerminalId(jMsb.containsKey("bank_terminal_id") ? jMsb.getString("bank_terminal_id") : "");
        }
        Map<String, Object> mocaMpayData = MocaMpayTransactionService.getMocaPromotion(clientData);
        if(!"".equals(mocaMpayData.get("offerCode"))){
            transaction.setOfferDiscountAmount((Double)mocaMpayData.get("offerDiscountAmount"));
        } else {
            transaction.setOfferDiscountAmount(null);
        }
        transaction.setPaymentAmount(paymentAmount);
        return transaction;
    }


    public Observable<MpayRefund> getRefundByRef(SQLConnection sqlConnection,String ref, String merchantId) {

        JsonArray inParams = new JsonArray()
                .add(ref)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()   
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(GET_REFUND_BY_REF, inParams, outParams).map(result -> {
            MpayRefund transaction = null;

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[GET MPAY REFUND DETAIL ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            return transaction;
        });
    }

    public Observable<MpayRefund> updateRefundByRef(SQLConnection sqlConnection,String id, String newRef, String des) {

        JsonArray inParams = new JsonArray()
                .add(id)
                .add(newRef)
                .add(des);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()   
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(UPDATE_REFUND_BY_ID, inParams, outParams).map(result -> {
            MpayRefund transaction = null;

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[GET MPAY REFUND DETAIL ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            // Map map = result.getOutput().getJsonObject(1).getMap();
            // io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // if (rs.getRows().size() <= 0) return null;
            // JsonObject jsonObject = rs.getRows().get(0);
            // transaction = bindTransaction(jsonObject);

            return transaction;
        });
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param paymentId
     * @return
     */
    public static Observable<List<JsonObject>> getRefundByPaymentId(SQLConnection sqlConnection, String paymentId) {
        JsonArray inParams = new JsonArray()
                .add(paymentId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_REFUND_BY_PAYMENT_ID, inParams, outParams).map(result -> {
            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[GET MPAY REFUND DETAIL ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().isEmpty()) return null;
            return rs.getRows();
        });
    }

    private static final String LIST_TRANSACTION = "{call PKG_MPAY.SEARCH_REFUND(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_V2 = "{call PKG_MPAY.SEARCH_REFUND_V3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_MPAY.get_refund_by_id(?,?,?,?)}";
    private static final String GET_REFUND_BY_PAYMENT_ID = "{call search_refund_by_payment_id(?,?,?,?)}";
    private static final String GET_REFUND_BY_REF = "{call PKG_MPAY.get_refund_by_ref(?,?,?,?,?)}";
    private static final String UPDATE_REFUND_BY_ID = "{call PKG_MPAY.update_refund_by_id(?,?,?,?,?)}";



    private static final Logger LOGGER = Logger.getLogger(MpayRefundService.class.getName());

}
