package com.onepay.ma.service.service;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public interface UserRoleService {
    Observable<Integer> insert(SQLConnection connection, int userId, String roleId);
    Observable<Integer> delete(SQLConnection connection, int userId);
    Observable<Integer> insertList(SQLConnection connection, int userId, String listRoleId);
}
