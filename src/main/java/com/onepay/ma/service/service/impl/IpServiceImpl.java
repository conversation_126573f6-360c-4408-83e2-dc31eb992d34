package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.IpAddressInfo;
import com.onepay.ma.service.service.IpService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.sql.ResultSet;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/31/16.
 */
@Service
public class IpServiceImpl implements IpService {
    @Override
    public Observable<IpAddressInfo> get(SQLConnection connReadOnly, String ip) {
        return getIpInformation(connReadOnly, ip);
    }

    /**
     * get ip information
     * @param connReadOnly
     * @param ip
     * @return
     */
    private Observable<IpAddressInfo> getIpInformation(SQLConnection connReadOnly, String ip){
        String[] ipSplit = ip.split("\\.");
        JsonArray inParams = new JsonArray()
                .add(ipSplit[0])
                .add(ipSplit[1])
                .add(ipSplit[2])
                .add(ipSplit[3]);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.NUMBER);
        return connReadOnly.callWithParamsObservable2(IP_ADDRESS_INFO, inParams, outParams).map(result -> {
            IpAddressInfo ipAddressInfo = null;
            Map map = result.getOutput().getJsonObject(4).getMap();
            ResultSet rs = (ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0 ) return null;
            ipAddressInfo = bindIpInfo(rs.getRows().get(0));
            ipAddressInfo.setIp_address(ip);
           // ipAddressInfo = bindIpInfo(resultSet.getResults());
          //  ipAddressInfo.setIp_address(ip);

            return ipAddressInfo;
        });
    }

    /**
     * Convert resultset to ip data
     * @param rs
     * @return
     */
    private IpAddressInfo bindIpInfo(JsonObject rs) {
        String city = rs.getString("CITY");
        String countryCode = rs.getString("COUNTRY_CODE");
        String countryName = rs.getString("COUNTRY_NAME");
        String ispName = rs.getValue("ISP_NAME") == null ||  rs.getValue("ISP_NAME").equals("null") ? "-" : rs.getString("ISP_NAME");

        String region = rs.getString("REGION");

        IpAddressInfo ipAddressInfo = new IpAddressInfo();
        ipAddressInfo.setCity(city);
        ipAddressInfo.setCountry_code(countryCode);
        ipAddressInfo.setCountry_name(countryName);
        ipAddressInfo.setIsp_name(ispName);
        ipAddressInfo.setRegion(region);
        return ipAddressInfo;
    }

    private final static String IP_ADDRESS_INFO = "{ call PKG_ONEFRAUD.GET_IP2_LOCATION(?,?,?,?,?,?,?) }";

    private static final Logger LOGGER = Logger.getLogger(IpServiceImpl.class.getName());
}
