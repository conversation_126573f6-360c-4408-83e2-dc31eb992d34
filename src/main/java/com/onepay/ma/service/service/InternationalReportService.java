package com.onepay.ma.service.service;

import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public interface InternationalReportService {
    Observable<List<InternationalReport>> listByMerchant(SQLConnection connection, InternationalReportParameter parameter);
    Observable<List<InternationalReport>> listByCurrency(SQLConnection connection, InternationalReportParameter parameter);
    Observable<List<InternationalReport>> listByCurrencyDetail(SQLConnection connection, InternationalReportParameter parameter);
    Observable<Integer> getTotalByMerchant(SQLConnection connection,  InternationalReportParameter parameter);
    Observable<Integer> getTotalByCurrency(SQLConnection connection,  InternationalReportParameter parameter);
    List<InternationalReport>  downloadByMerchant(Connection connection, InternationalReportParameter parameter) throws SQLException;
    List<InternationalReport>  downloadByCurrency(Connection connection, InternationalReportParameter parameter) throws SQLException;
    ResultSet downloadDetail(Connection connection, InternationalReportParameter parameter) throws SQLException;

}
