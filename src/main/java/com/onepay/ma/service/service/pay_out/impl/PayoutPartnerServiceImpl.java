package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.pay_out.PartnerDTO;
import com.onepay.ma.service.service.pay_out.PayOutPartnerService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;
import java.sql.Timestamp;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class PayoutPartnerServiceImpl implements PayOutPartnerService {

    private static final Logger LOGGER = Logger.getLogger(PayoutPartnerServiceImpl.class.getName());

    @Override
    public Observable<PartnerDTO> getByPartnerId(SQLConnection connection, String partnerId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(partnerId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.g_partner_by_id(?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH OPERATOR ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(null != rs && !rs.getRows().isEmpty()) {
                return bind(rs.getRows().get(0));
            }
            return null;
        });
    }

    private PartnerDTO bind(JsonObject js) {
        PartnerDTO partnerDTO = new PartnerDTO();
        try {
            partnerDTO.setId(js.getString("S_ID"));
            partnerDTO.setName(js.getString("S_NAME"));
            partnerDTO.setState(js.getString("S_STATE"));
            partnerDTO.setDesc(js.getString("S_DESC"));
            partnerDTO.setNotifyUrl(js.getString("S_NOTIFY_URL"));
            partnerDTO.setCheckApproval(js.getInteger("N_CHECK_APPROVAL"));
            partnerDTO.setCheckOperator(js.getInteger("N_CHECK_OPERATOR"));
            Timestamp createdDate = js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE"));
            partnerDTO.setCreatedDate(createdDate);
            Timestamp updatedDate = js.getString("D_UPDATE") == null ? null : Timestamp.valueOf(js.getString("D_UPDATE"));
            partnerDTO.setUpdatedDate(updatedDate);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return partnerDTO;
    }
}
