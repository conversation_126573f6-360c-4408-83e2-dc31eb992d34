package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.service.AcquirerService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
@Service
public class AcquirerServiceImpl implements AcquirerService {

    @Override
    public Observable<List<Acquirer>> list(SQLConnection sqlConnOnline, String merchantId) {
        return listAcquirer(sqlConnOnline, merchantId);
    }

    /**
     * get list acquirer data
     * @param conn
     * @param mechantId
     * @return
     */
    private Observable<List<Acquirer>> listAcquirer(SQLConnection conn, String mechantId){
        JsonArray inParams = new JsonArray()
                .add(mechantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(LIST_ACQUIRER, inParams, outParams).map(result -> {
            List<Acquirer> acquirerList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                Acquirer acquirer = bindAcquirer(jsonObject);
                acquirerList.add(acquirer);
            }

            return acquirerList;
        });
    }
    /**
     * bind resultset data to acquirer
     * @param rs
     * @return
     * @throws SQLException
     */
    private Acquirer bindAcquirer(JsonObject rs) {
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_SHORT_NAME");

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_short_name(acquirerName);
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);

        return acquirer;
    }

    private static final String LIST_ACQUIRER = "{call PKG_PAYMENT2.SEARCH_ACQ(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(AcquirerServiceImpl.class.getName());
}
