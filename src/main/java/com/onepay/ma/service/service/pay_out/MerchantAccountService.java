package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.pay_out.ModelDto;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Handle for MerchantAccountService
 * by Tiennv
 * 17/11/2020
 */
public interface MerchantAccountService {

    /**
     * Handle for search a list MerchantAccountService
     * by Tiennv
     * 17/11/2020
     */
    Observable<List<ModelDto>> getListMerchantAccount(SQLConnection connection, String merchantId);
}
