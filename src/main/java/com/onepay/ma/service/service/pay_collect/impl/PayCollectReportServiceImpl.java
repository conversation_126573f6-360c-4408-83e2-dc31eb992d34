package com.onepay.ma.service.service.pay_collect.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_collect.ReportDto;
import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.service.pay_collect.ReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

@Service
public class PayCollectReportServiceImpl implements ReportService {
    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger
            .getLogger(PayCollectReportServiceImpl.class.getName());
    private static final String GET_LIST_REPORT = "{ call PKG_MA_PAYCOLLECT.get_paycollect_report_v2(?,?,?,?,?,?,?,?,?,?,?,?,?) }";

    @Override
    public Observable<BaseList<ReportDto>> search(SQLConnection sqlConnection, ReportSearchReq searchRequest) {
        BaseList<ReportDto> baseList = new BaseList<>();

        return this.total(sqlConnection, searchRequest).flatMap(total -> {
            baseList.setTotal_items(total);
            return this.searchData(sqlConnection, searchRequest).map(payCollectTransactionsOnline -> {
                baseList.setList(payCollectTransactionsOnline);
                return baseList;
            });
        });
    }

    @Override
    public Observable<Integer> total(SQLConnection sqlConnection, ReportSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getVirtualId())
                // .add(query.getBankTxnRef())
                // .add(query.getSenderBankList())
                .add(query.getReceivedBankList())
                .add(query.getInterval())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId())
                .add(query.getGroupBy());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                // .addNull()
                // .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_LIST_REPORT, inParams, outParams).map(result -> {
            Integer total = 0;
            Integer resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH REPORT TOTAL ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });

    }

    private Observable<List<ReportDto>> searchData(SQLConnection sqlConnection, ReportSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getVirtualId())
                // .add(query.getBankTxnRef())
                // .add(query.getSenderBankList())
                .add(query.getReceivedBankList())
                .add(query.getInterval())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId())
                .add(query.getGroupBy());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                // .addNull()
                // .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_LIST_REPORT, inParams, outParams).map(result -> {
            List<ReportDto> reportList = new ArrayList<>();
            Integer resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH ONEBILL TRANSACTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                reportList.add(bindReportData(jsonObject));
            }
            return reportList;
        });
    }

    @Override
    public ResultSet download(Connection connection, ReportSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getVirtualId())
                // .add(query.getBankTxnRef())
                // .add(query.getSenderBankList())
                .add(query.getReceivedBankList())
                .add(query.getInterval())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId())
                .add(query.getGroupBy());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                // .addNull()
                // .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, GET_LIST_REPORT, inParams, outParams);

            int code = callableStatement.getInt(2);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD REPORT ERROR]: " + callableStatement.getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(1);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private static ReportDto bindReportData(JsonObject rs) {
        ReportDto reportDto = new ReportDto();
        try {
            reportDto.setDataType(rs.getString("DATA_TYPE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("DATA_TYPE")));
            reportDto.setMerchantId(rs.getString("S_MERCHANT_ID"));
            reportDto.setVirtualId(rs.getString("S_BANK_ACCOUNT"));
            reportDto.setSenderName(rs.getString("S_FULL_NAME"));
            // reportDto.setSenderBank(rs.getString("SENDER_BANK"));
            // reportDto.setReceivedName(rs.getString("S_RECEIPT_ACC_NAME"));
            reportDto.setReceivedBank(rs.getString("RECEIPT_BANK"));
            reportDto.setNoTrans(rs.getInteger("NO_TRANS"));
            reportDto.setTotalTrans(rs.getDouble("TOTAL_TRANS"));
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return reportDto;
    }

}
