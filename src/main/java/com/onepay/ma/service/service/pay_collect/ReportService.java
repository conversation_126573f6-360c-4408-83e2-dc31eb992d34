package com.onepay.ma.service.service.pay_collect;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_collect.ReportDto;
import com.onepay.ma.service.models.pay_collect.ReportSearchReq;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;

public interface ReportService {
    Observable<BaseList<ReportDto>> search(SQLConnection sqlConnection, ReportSearchReq searchRequest);
    Observable<Integer> total(SQLConnection onlineConn, ReportSearchReq searchRequest);
    ResultSet download(Connection connection, ReportSearchReq query);

}
