package com.onepay.ma.service.service.paypal.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.paypal.PaypalNotify;
import com.onepay.ma.service.service.paypal.PaypalNotifyService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.logging.Level;

@Service
public class PaypalNotifyServiceImpl implements PaypalNotifyService {

    @Override
    public Observable<Void> insert(SQLConnection sqlConnection, PaypalNotify model) {
        JsonArray inParams = new JsonArray()
                .add(model.getId())
                .add(model.getEventType())
                .add(model.getSummary())
                .add(model.getResourceType())
                .add(model.getResource())
                .add(model.getEventVersion())
                .add(model.getLinks());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR insertNotify]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnection.callWithParamsObservable2(INSERT_NOTIFY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(7) != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT PAYPAL NOTIFY ERROR] => " + result.getOutput().getString(8));

                if(result.getOutput().getInteger(7) == 409) {
                    throw IErrors.DUPLICATE_NOTIFY;
                }

                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            return null;

        });
    }
    private static final String INSERT_NOTIFY = "{call PKG_PAYPAL.insert_notification(?,?,?,?,?,?,?,?,?)}";
    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PaypalNotifyServiceImpl.class.getName());
}
