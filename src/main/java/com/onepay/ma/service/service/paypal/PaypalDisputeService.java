package com.onepay.ma.service.service.paypal;

import com.onepay.ma.service.models.paypal.PaypalDispute;
import com.onepay.ma.service.models.paypal.PaypalEvidence;
import com.onepay.ma.service.models.paypal.PaypalMessage;
import com.onepay.ma.service.models.paypal.PaypalTransaction;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

public interface PaypalDisputeService {

    Observable<Integer> insertDispute(SQLConnection sqlConnectionB, PaypalDispute model);

    Observable<Void> insertOrUpdateDisputeTransaction(SQLConnection sqlConnectionB, PaypalTransaction model, String disputeId);

    Observable<Void> insertPaypalEvidence(SQLConnection sqlConnectionB, PaypalEvidence evidence);

    Observable<List<PaypalTransaction>> getPaypalTransaction(SQLConnection sqlConnectionB, PaypalTransaction model);

    Observable<Void> insertListPaypalMessage(SQLConnection sqlConnectionB, List<PaypalMessage> listPaypalMessage,String disputeId,Integer dispute_Id);

}
