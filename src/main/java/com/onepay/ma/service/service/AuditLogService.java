package com.onepay.ma.service.service;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import oracle.jdbc.OracleTypes;
import rx.Observable;

import java.util.logging.Logger;

public class AuditLogService {

    private static final Logger LOGGER = Logger.getLogger(AuditLogService.class.getName());
    private static final String INSERT_AUDIT_LOG = "{call oneam.INSERT_AUDIT_LOG(?,?,?,?,?,?,?)}";

    public static Observable<Boolean> auditLog(SQLConnection conn,
                                               String userId,
                                               String action,
                                               String status,
                                               String description,
                                               String platform) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(userId)
                .add(action)
                .add(status)
                .add(description)
                .add(platform);

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return conn.callWithParamsObservable2(INSERT_AUDIT_LOG, inParams, outParams)
                .map(result -> {
                    JsonArray output = result.getOutput();
                    int resultCode = output.getInteger(0);
                    String msg = output.getString(1);
                    LOGGER.info("resultCode: " + resultCode);
                    if (resultCode != 200) {
                        LOGGER.warning("[AUDIT_LOG] Ghi log lỗi: " + msg);
                        return false;
                    }
                    return true;
                })
                .onErrorResumeNext(err -> {
                    LOGGER.warning("[AUDIT_LOG] Exception: " + err.getMessage());
                    return Observable.just(false);
                });
    }
}
