package com.onepay.ma.service.service.mpay;

import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpay.MpayOrder;
import com.onepay.ma.service.models.mpay.MpayOrderQuery;
import com.onepay.ma.service.models.mpay.MpayTransactionHistory;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 07-Aug-17.
 */
public interface MpayOrderService {


    Observable<Transactions<MpayOrder>> list(SQLConnection connReadOnly, SQLConnection connOnline, MpayOrderQuery query);

    Observable<MpayOrder> get(SQLConnection connOnline, String transactionId);

//    Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, MpayOrderQuery query);

    //    Observable<InternationalTransactionHistory> getHistoryByMerchantRef(SQLConnection sqlOnline, String transactionId, String merchantTransactionRef, String merchantId);
//    ResultSet downloadOnline(Connection connOnline, MpayTransactionQuery query) throws SQLException;
//
//    ResultSet downloadReadonly(Connection connReadOnly, MpayTransactionQuery query) throws SQLException;

    Observable<List<MpayTransactionHistory>> listHistory(SQLConnection connReadOnly, SQLConnection connOnline, String transactionId);

    Observable<Map<String,Object>> getTransactionByInvoice(SQLConnection connOnline, String invoiceId);
}
