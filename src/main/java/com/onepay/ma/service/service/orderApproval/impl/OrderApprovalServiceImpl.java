package com.onepay.ma.service.service.orderApproval.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.orderApproval.OrderApproval;
import com.onepay.ma.service.service.impl.ApprovalServiceImpl;
import com.onepay.ma.service.service.orderApproval.OrderApprovalService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class OrderApprovalServiceImpl implements OrderApprovalService {

    private static final String INSERT_APPROVAL = "{call PKG_ORDER_APPROVAL.approval_insert(?,?,?,?,?,?,?,?)}";


    private static final String GET_APPROVAL_BY_TRANS= "{call PKG_ORDER_APPROVAL.approval_get(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(ApprovalServiceImpl.class.getName());

    @Override
    public Observable<OrderApproval> getByTransactionId(SQLConnection connection, String transactionId) {

        JsonArray inParams = new JsonArray().add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(GET_APPROVAL_BY_TRANS, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(2);
            if(code != 200) {
                LOGGER.log(Level.SEVERE, "ERROR ON GET ORDER APPROVAL " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            OrderApproval approval = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0 ) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            approval = bindDataToApproval(jsonObject);

            return  approval;
        });
    }

    @Override
    public Observable<OrderApproval> insert(SQLConnection connection, OrderApproval model) {

        JsonArray inParams = new JsonArray()
                .add(model.getTransaction_id())
                .add(model.getType())
                .add(model.getDescription() == null ? "" :model.getDescription() )
                .add(model.getUser_confirm())
                .add(model.getStatus());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.NUMBER);

        return connection.callWithParamsObservable2(INSERT_APPROVAL, inParams, outParams).flatMap(result -> {
            Integer code = result.getOutput().getInteger(5);
            if(code != 201) {
                LOGGER.log(Level.SEVERE, "ERROR ON INSERT ORDER APPROVAL " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return this.getByTransactionId(connection, model.getTransaction_id());
        });

    }

    private OrderApproval bindDataToApproval(JsonObject rs) {
        OrderApproval approval =  new OrderApproval();
        approval.setId(rs.getInteger("N_ID"));
        approval.setDescription(rs.getString("S_DESCRIPTION"));
        approval.setTransaction_id(rs.getString("S_TRANSACTION_ID"));
        approval.setUser_confirm(rs.getString("S_USER_EMAIL"));
        approval.setStatus(rs.getString("S_STATUS"));
        approval.setType(rs.getString("S_TYPE"));
        approval.setCreate_time(rs.getString("D_CREATE") == null ? null : Timestamp.valueOf(rs.getString("D_CREATE")));

        return approval;
    }

}
