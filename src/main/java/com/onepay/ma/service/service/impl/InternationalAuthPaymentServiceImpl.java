package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.InternationalAuthPaymentService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 18-May-17.
 */
@Service
public class InternationalAuthPaymentServiceImpl implements InternationalAuthPaymentService {

    @Override
    public Observable<Transactions<InternationalAuthPayment>> list(SQLConnection connReadOnly, SQLConnection connOnline, InternationalAuthPaymentQuery query) {
        Transactions<InternationalAuthPayment> transactions = new Transactions();
        return totalAuthPayment(connOnline, query,-1).flatMap(total1 -> {
            return totalAuthPayment(connReadOnly, query,1).flatMap(total2 -> {
                transactions.setTotal_items(total1 + total2);

                query.setOffset(0);
                return this.listAuthPayment(connOnline, query,-1).flatMap(transOnline -> {

                    // offset
                    query.setOffset(total1);
                    return this.listAuthPayment(connReadOnly, query,1).flatMap(transReadOnly -> {
                        List<InternationalAuthPayment> transList = new ArrayList<>();
                        transList.addAll(transOnline);
                        transList.addAll(transReadOnly);
                        int index = 1;
                        for (InternationalAuthPayment internationalAuthPayment : transList) {
                            internationalAuthPayment.setRow_num(index + (query.getPage() * query.getPageSize()));
                            index++;
                        }
                        transactions.setTransactions(transList);
                        return Observable.just(transactions);
                    });
                });
            });
        });
    }

    @Override
    public Observable<InternationalAuthPayment> get(SQLConnection connOnline, SQLConnection backup, String transactionId) {
        return this.getById(connOnline, transactionId).flatMap(internationalAuthPayment -> {
            if(internationalAuthPayment.getFinancial_number() != null) return getMoreInfo(backup, internationalAuthPayment);
            return Observable.just(internationalAuthPayment);
        });
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, InternationalAuthPaymentQuery query) {
        return totalAuthPayment(connOnline, query,-1).flatMap(total1 -> {
            return totalAuthPayment(connReadOnly, query,1).map(total2 -> {
                return total1 + total2;
            });
        });
    }@Override
    public ResultSet downloadOnline(Connection connOnline, InternationalAuthPaymentQuery parameter) throws SQLException {
        return downloadData(connOnline, parameter,-1);
    }

    @Override
    public ResultSet downloadReadonly(Connection connReadOnly, InternationalAuthPaymentQuery parameter) throws SQLException {
        return downloadData(connReadOnly, parameter,1);
    }

    /**
     * file data
     *
     * @param connection
     * @param query
     * @return
     */
    private ResultSet downloadData(Connection connection, InternationalAuthPaymentQuery query,int row) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getKeywords())
                .add(query.getMerchantId())
                .add(query.getTransactionId())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getCardType())
                .add(query.getCurrency())
                .add(query.getAuthenticationState())
                .add(query.getAuthenticationCode())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getSource());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(20);

            if (reusltCode != 200) {
                LOGGER.log(Level.WARNING, "[DOWNLOAD INTERNATIONAL AUTH PAYMENT TRANSACTION READONLY ERROR]: " + callableStatement.getString(21));
            }
            rs = (ResultSet) callableStatement.getObject(19);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }
    private Observable<List<InternationalAuthPayment>> listAuthPayment(SQLConnection connOnline, InternationalAuthPaymentQuery query,int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(query.getKeywords())
                .add(query.getMerchantId())
                .add(query.getTransactionId())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getCardType())
                .add(query.getCurrency())
                .add(query.getAuthenticationState())
                .add(query.getAuthenticationCode())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset())
                .add(query.getSource());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

      /*  LOGGER.log(Level.WARNING, "[GET INTERNATIONAL PAYMENT Param]: " );
        LOGGER.log(Level.WARNING, "From_date: " +query.getFrom_date());
        LOGGER.log(Level.WARNING, "To_date: " +query.getTo_date());
        LOGGER.log(Level.WARNING, "AuthenticationState: " +query.getAuthenticationState());
        LOGGER.log(Level.WARNING, "AuthenticationCode: " +query.getAuthenticationCode());*/
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            List<InternationalAuthPayment> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(19);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL PAYMENT ERROR]: " + result.getOutput().getString(20));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(18).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                InternationalAuthPayment transaction = bind(jsonObject);
                transactionList.add(transaction);
            }


            return transactionList;

        });
    }

    private Observable<InternationalAuthPayment> getById(SQLConnection connOnline, String id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL PAYMENT BY ID ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();

            InternationalAuthPayment internationalAuthPayment=  null;
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                internationalAuthPayment = bind(jsonObject);
            }


            return internationalAuthPayment;

        });
    }

    private Observable<InternationalAuthPayment> getMoreInfo(SQLConnection connOnline, final InternationalAuthPayment internationalAuthPayment) {
        JsonArray inParams = new JsonArray()
                .add(internationalAuthPayment.getMerchant_id())
                .add(internationalAuthPayment.getMerchant_txn_ref())
                .add(internationalAuthPayment.getFinancial_number());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(TRANSACTION_GET_MOR_INFO, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(4);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET INTERNATIONAL PAYMENT MORE INFO ERROR]: " + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(3).getMap();

            InternationalAuthPayment authResult=  internationalAuthPayment;
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                authResult.setVerification_token(jsonObject.getString("S_VPC_VER_TOKEN"));
                authResult.setPares_status(jsonObject.getString("S_VPC_3DS_STATUS"));
                authResult.setEnrolled_3ds(jsonObject.getString("S_VPC_3DS_ENROLLED"));
                //TODO: 3-D Secure VERes.enrolled  + Verification Token ( CAVV)
            }


            return authResult;

        });
    }
    private Observable<Integer> totalAuthPayment(SQLConnection connOnline, InternationalAuthPaymentQuery query,int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getKeywords())
                .add(query.getMerchantId())
                .add(query.getTransactionId())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getCardType())
                .add(query.getCurrency())
                .add(query.getAuthenticationState())
                .add(query.getAuthenticationCode())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getSource());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            Integer total = 0;
            Integer reusltCode = result.getOutput().getInteger(19);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET AUTH PAYMENT TOTAL ERROR]: " + result.getOutput().getString(19));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(18).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;

        });
    }


    private InternationalAuthPayment bind(JsonObject js) {
        InternationalAuthPayment rs = new InternationalAuthPayment();

        Amount amount = new Amount();
        amount.setCurrency(js.getString("S_CURRENCY"));
        amount.setTotal(Double.valueOf(js.getString("S_AMOUNT")));

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(js.getString("S_AUTHENTICATION_STATE"));
        authenticationData.setAuthentication_type(js.getString("S_MESSAGE_TYPE"));
        authenticationData.setAuthorization_code(js.getString("S_AUTHENTICATION_CODE"));

        InternationalCard internationalCard = new InternationalCard();
        internationalCard.setCard_number(js.getString("S_CARD_NUMBER_MASK"));
        internationalCard.setCard_type(js.getString("S_CARD_TYPE"));

        rs.setTransaction_id(js.getString("S_AUTHENTICATION_ID"));
        rs.setMerchant_txn_ref(js.getString("S_TRANSACTION_REFERENCE"));
        rs.setMerchant_id(js.getString("S_MERCHANTID"));
        rs.setOrder_info(js.getString("S_ORDERREFERENCE"));
        rs.setEci(js.getString("S_3D_SECURE_ECI"));
        rs.setEnrolled_3ds(js.getString("S_3D_SECURE_VERES_ENROLLED"));
        rs.setXid(js.getString("S_3D_SECURE_XID"));
        rs.setPares_status(js.getString("S_3D_SECURE_PARES_STATUS"));
        rs.setVerification_token(js.getString("S_VERIFICATION_TOKEN"));
        rs.setVerification_security_level(js.getString("S_VERIFICATION_SECURITY_LEVEL"));
        rs.setFinancial_number(js.getString("S_FINANCIAL_TRANSACTION_NUMBER"));
        rs.setTime_taken(js.getString("S_TIME_TAKEN"));
        rs.setTransaction_time(js.getString("D_DATE") != null ? Timestamp.valueOf(js.getString("D_DATE")) : null);
        rs.setAmount(amount);
        rs.setAuthentication(authenticationData);
        rs.setCard(internationalCard);
        Integer onecreditTransactionId = js.getInteger("ONECREDIT_TRANS_ID");
        rs.setOnecredit_transaction_id(onecreditTransactionId==null?"":onecreditTransactionId.toString());
        rs.setSource(js.getString("S_SOURCE"));

        return rs;
    }

    private static final String LIST_TRANSACTION = "{call PKG_ONECREDIT.get_transaction_auth_v4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.get_transaction_auth_by_id3(?,?,?,?) }";

    private static final String TRANSACTION_GET_MOR_INFO = "{call PKG_ONECREDIT_1114.get_authentication_detail(?,?,?,?,?,?) }";

    private static final Logger LOGGER = Logger.getLogger(InternationalAuthPaymentServiceImpl.class.getName());
}
