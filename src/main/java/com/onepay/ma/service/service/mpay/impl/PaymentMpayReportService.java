package com.onepay.ma.service.service.mpay.impl;

import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.models.mpay.PaymentMpayReport;
import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/2/16.
 */
@Service
public class PaymentMpayReportService {

    public static Observable<List<PaymentMpayReport>> list(SQLConnection connection, MpayReportQuery parameter) {
        return listReport(connection, parameter);
    }

    public static Observable<Integer> getTotalReport(SQLConnection connReadOnly, MpayReportQuery parameter) {
        return getListTotalReport(connReadOnly, parameter);
    }

    public static List<PaymentMpayReport> download(Connection connReadOnly, MpayReportQuery parameter) throws SQLException {
        return downloadData(connReadOnly, parameter);
    }

    /**
     * file data from readonly database
     * 
     * @param connection
     * @param parameter
     * @return
     */
    private static List<PaymentMpayReport> downloadData(Connection connection, MpayReportQuery parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAcqCode())
                .add(parameter.getAppName())
                .add(parameter.getMasking())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // String callName = REPORT_DOMESTIC_DOWNLOAD;
        // if("v2".equals(parameter.getVersion())) {
        // callName = REPORT_DOMESTIC_V2;
        // }
        CallableStatement callableStatement = null;
        ResultSet rs = null;

        List<PaymentMpayReport> reports = new ArrayList<>();
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_MPAY, inParams, outParams);

            int code = callableStatement.getInt(15);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[DOWNLOAD MPAY REPORT ERROR]: " + callableStatement.getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(14);
            boolean recordsAvailable = rs.next();

            // Map map = result.getOutput().getJsonObject(5).getMap();
            // io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String merchantId = "";
            String merchantName = "";
            String appName = "";
            String masking = "";
            String acqCode = "";
            String channel = "";
            String data = "";
            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = rs.getString("S_INS_TYPE");
                    String appNameData = rs.getString("S_APP_NAME") == null ? "" : rs.getString("S_APP_NAME");
                    String maskingData = rs.getString("S_MASKING") == null ? "" : rs.getString("S_MASKING");
                    String acqCodeData = rs.getString("S_ACQ_CODE");
                    String channelData = rs.getString("S_CLIENT_ID");
                    String midData = rs.getString("S_MERCHANT_ID");
                    String dataTemp = rs.getString("S_DATA");
                    if (!rs.getString("D_DATE").equals(date) || !cardTypeData.equals(cardType) || !midData.equals(merchantId)
                            || !appNameData.equals(appName) || !maskingData.equals(masking)
                            || !acqCodeData.equals(acqCode)
                            || !channelData.equals(channel)
                            || !dataTemp.equals(data)
                            ) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MERCHANT_NAME", merchantName);
                        objectData.put("D_DATE", date);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_APP_NAME", appName);
                        objectData.put("S_CLIENT_ID", channel);
                        objectData.put("S_MASKING", masking);
                        objectData.put("S_DATA", data);
                        objectData.put("S_ACQ_CODE", acqCode);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        reports.add(bindReport(objectData));
                        objectData = new JsonObject();
                    }
                }

                date = rs.getString("D_DATE");
                cardType = rs.getString("S_INS_TYPE");
                appName = rs.getString("S_APP_NAME");
                masking = rs.getString("S_MASKING");
                channel = rs.getString("S_CLIENT_ID");
                acqCode = rs.getString("S_ACQ_CODE");
                merchantId = rs.getString("S_MERCHANT_ID");
                merchantName = rs.getString("S_MERCHANT_NAME");
                data = rs.getString("S_DATA");
                if (rs.getString("S_TRANSACTION_TYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", rs.getInt("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", rs.getDouble("N_AMOUNT"));
                }
                if (!rs.next()) {
                    recordsAvailable = false;
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MERCHANT_NAME", merchantName);
                    objectData.put("D_DATE", date);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_APP_NAME", appName);
                    objectData.put("S_MASKING", masking);
                    objectData.put("S_CLIENT_ID", channel);
                    objectData.put("S_DATA", data);
                    objectData.put("S_ACQ_CODE", acqCode);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    reports.add(bindReport(objectData));
                    objectData = new JsonObject();
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error Sql", e);
            throw new DatabaseException("Error Sql", e);
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error close result set", e);
                }
            }
            if (callableStatement != null) {
                try {
                    callableStatement.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error close statement", e);
                }

            }
        }

        return reports;
    }

    private static Observable<List<PaymentMpayReport>> listReport(SQLConnection connection, MpayReportQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAcqCode())
                .add(parameter.getAppName())
                .add(parameter.getMasking())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // String callName =REPORT_DOMESTIC;
        // if("v2".equals(parameter.getVersion())) {
        // callName = REPORT_DOMESTIC_V2;
        // }
        return connection.callWithParamsObservable2(REPORT_MPAY, inParams, outParams).map(result -> {
            int code = result.getOutput().getInteger(14);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[SEARCH MPAY REPORT ERROR]: " + result.getOutput().getString(15));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<PaymentMpayReport> reports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(13).getMap();
            io.vertx.ext.sql.ResultSet resultSet = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String merchantId = "";
            String merchantName = "";
            String channel = "";
            String appName = "";
            String masking = "";
            String acqCode = "";
            String data = "";
            int index = 0;
            for (JsonObject rs : resultSet.getRows()) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = rs.getString("S_INS_TYPE");
                    String appNameData = rs.getString("S_APP_NAME") == null ? "" : rs.getString("S_APP_NAME");
                    String maskingData = rs.getString("S_MASKING") == null ? "" : rs.getString("S_MASKING");
                    String acqCodeData = rs.getString("S_ACQ_CODE");
                    String channelData = rs.getString("S_CLIENT_ID");
                    String midData = rs.getString("S_MERCHANT_ID");
                    String dataTemp = rs.getString("S_DATA");
                    LOGGER.info("S_DATA:" + dataTemp + " appNameData:" + appNameData);
                    if (!rs.getString("D_DATE").equals(date) || !cardTypeData.equals(cardType) || !midData.equals(merchantId)
                            || !appName.equals(appNameData) || !masking.equals(maskingData)
                            || !acqCodeData.equals(acqCode) || !channelData.equals(channel) || !dataTemp.equals(data)) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MERCHANT_NAME", merchantName);
                        objectData.put("D_DATE", date);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_APP_NAME", appName);
                        objectData.put("S_MASKING", masking);
                        objectData.put("S_ACQ_CODE", acqCode);
                        objectData.put("S_DATA", data);
                        objectData.put("S_CLIENT_ID", channel);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        reports.add(bindReport(objectData));
                        objectData = new JsonObject();
                    }
                }

                date = rs.getString("D_DATE");
                cardType = rs.getString("S_INS_TYPE");
                appName = rs.getString("S_APP_NAME");
                masking = rs.getString("S_MASKING");
                acqCode = rs.getString("S_ACQ_CODE");
                merchantId = rs.getString("S_MERCHANT_ID");
                merchantName = rs.getString("S_MERCHANT_NAME");
                channel = rs.getString("S_CLIENT_ID");
                data = rs.getString("S_DATA");
                if (rs.getString("S_TRANSACTION_TYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", rs.getInteger("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", rs.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", rs.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= resultSet.getRows().size()) {
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MERCHANT_NAME", merchantName);
                    objectData.put("D_DATE", date);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_APP_NAME", appName);
                    objectData.put("S_MASKING", masking);
                    objectData.put("S_DATA", data);
                    objectData.put("S_CLIENT_ID", channel);
                    objectData.put("S_ACQ_CODE", acqCode);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    reports.add(bindReport(objectData));
                    objectData = new JsonObject();
                }
            }


            return reports;
        });
    }

    /**
     * get report data total
     * 
     * @param connection
     * @param parameter
     * @return
     */
    private static Observable<Integer> getListTotalReport(SQLConnection connection, MpayReportQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAcqCode())
                .add(parameter.getAppName())
                .add(parameter.getMasking())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getBankTerminalId() == null ? "" : parameter.getBankTerminalId())
                .add(parameter.getBankMerchantId() == null ? "" : parameter.getBankMerchantId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);


        return connection.callWithParamsObservable2(REPORT_MPAY, inParams, outParams).map(result -> {
            int code = result.getOutput().getInteger(14);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[SEARCH MPAY REPORT ERROR]: " + result.getOutput().getString(15));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = 0;
            Map map = result.getOutput().getJsonObject(13).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }


    private static PaymentMpayReport bindReport(JsonObject rs) {
        LOGGER.info("rs.getString(S_DATA): " + rs.getString("S_DATA"));
        PaymentMpayReport report = new PaymentMpayReport();
        report.setCardType(rs.getString("S_CARD_TYPE"));

        report.setAppName(rs.getString("S_APP_NAME"));
        report.setMasking(rs.getString("S_MASKING"));
        report.setAcqCode(rs.getString("S_ACQ_CODE"));
        report.setChannel(rs.getString("S_CLIENT_ID"));

        report.setReportDate(Timestamp.valueOf(rs.getString("D_DATE")));
        report.setMerchantId(rs.getString("S_MERCHANT_ID"));
        report.setMerchantName(rs.getString("S_MERCHANT_NAME"));
        report.setRefundCount(rs.getInteger("REFUND_COUNT"));
        report.setTransactionCount(rs.getInteger("N_COUNT_TRAN"));
        report.setTransactionTotal(rs.getDouble("N_TOTAL_AMOUNT"));
        report.setRefundTotal(rs.getDouble("REFUND_AMOUNT"));
        String clientData = rs.getString("S_DATA");
        JsonObject jClientData = null;
        try {
            if (Objects.nonNull(clientData) && !Strings.isBlank(clientData) && !Strings.isEmpty(clientData)) {
                jClientData = new JsonObject(clientData);
            }
        } catch (Exception e) {
            Logger.getLogger("Client data parse error", e.toString());
        }
        if (jClientData != null) {
            JsonObject jMsb = jClientData.containsKey("msb_qr") ? jClientData.getJsonObject("msb_qr") : new JsonObject();
            report.setBankMerchantId(jMsb.containsKey("bank_merchant_id") ? jMsb.getString("bank_merchant_id") : "");
            report.setBankTerminalId(jMsb.containsKey("bank_terminal_id") ? jMsb.getString("bank_terminal_id") : "");
        }
        return report;
    }

    private final static String REPORT_MPAY = "{call PKG_MPAY.search_reports_payment_v2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PaymentMpayReportService.class.getName());


}
