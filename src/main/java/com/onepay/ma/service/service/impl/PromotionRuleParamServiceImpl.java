package com.onepay.ma.service.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionRuleParam;
import com.onepay.ma.service.service.PromotionRuleParamService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.lang.reflect.Type;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/10/16.
 */
@Service
public class PromotionRuleParamServiceImpl  implements PromotionRuleParamService{
    @Override
    public Observable<List<PromotionRuleParam>> list(SQLConnection sqlConnPr, int ruleId) {
        return listPromotionRuleParam(sqlConnPr, ruleId);
    }

    @Override
    public Observable<Integer> insert(SQLConnection sqlConnPr, int ruleId, PromotionRuleParam promotionRuleParam) {
        return insertPromotionRuleParam(sqlConnPr, ruleId, promotionRuleParam);
    }

    @Override
    public Observable<Integer> update(SQLConnection sqlConnPr, int ruleParamId, PromotionRuleParam promotionRuleParam) {
        return updatePromotionRuleParam(sqlConnPr, ruleParamId, promotionRuleParam);
    }

    @Override
    public Observable<Integer> delete(SQLConnection sqlConnPr, int ruleId, int ruleParamId) {
        return deletePromotionRuleParam(sqlConnPr, ruleId, ruleParamId);
    }

    @Override
    public Observable<String> getRegexRuleTYpe(SQLConnection sqlConnPr, int ruleId, String paramName) {
        return regexRuleTypeParam(sqlConnPr, ruleId, paramName);
    }

    /**
     * insert promotion rule param
     * @param conn
     * @param ruleId
     * @param promotionRuleParam
     * @return
     */
    private Observable<Integer> insertPromotionRuleParam(SQLConnection conn, int ruleId, PromotionRuleParam promotionRuleParam) {
        String value = promotionRuleParam.getRule_param_value();
        if(value == null)  return Observable.just(ruleId);
        if(value.length() > 4000){
            List<String> strings = new ArrayList<>();
            int index = 0;
            while (index < value.length()) {
                strings.add(value.substring(index, Math.min(index + 4000, value.length())));
                index += 4000;
            }
            return  insertPromotionRuleParamListData(conn, ruleId, promotionRuleParam.getRule_param_name(), strings, promotionRuleParam.getOrder(),0).map(strings1 -> {
                //return rule id
                return ruleId;
            });
        }else{
           return insertPartPromotionRuleParam(conn, ruleId, promotionRuleParam.getRule_param_name(), value, promotionRuleParam.getOrder()).map(integer -> {
               //return rule id
               return ruleId;
           });
        }
    }

    /**
     * insert promotion rule param
     * @param conn
     * @param ruleParamId
     * @param promotionRuleParam
     * @return
     */
    private Observable<Integer>  updatePromotionRuleParam(SQLConnection conn, int ruleParamId, PromotionRuleParam promotionRuleParam) {
        String value = promotionRuleParam.getRule_param_value();
        if(value.length() > 4000){
            List<String> strings = new ArrayList<>();
            int index = 0;
            while (index < value.length()) {
                strings.add(value.substring(index, Math.min(index + 4000, value.length())));
                index += 4000;
            }
            return  updatePromotionRuleParamListData(conn, ruleParamId, promotionRuleParam.getRule_param_name(), strings, promotionRuleParam.getOrder(),0).map(strings1 -> {
                return ruleParamId;
            });
        }else{
            return updatePartPromotionRuleParam(conn, ruleParamId, promotionRuleParam.getRule_param_name(), value, promotionRuleParam.getOrder()).map(integer -> {
                return ruleParamId;
            });
        }
    }

    /**
     * insert promotion rule param list
     * @param conn
     * @param ruleId
     * @param name
     * @param value
     * @param order
     * @param index
     * @return
     */
    private Observable<List<String>> insertPromotionRuleParamListData(SQLConnection conn, int ruleId, String name, List<String> value, int order, int index){
        if(value.size() <= 0){
            return Observable.just(value);
        }
        final int finalIndex = index;
        String data = value.get(index);
        return Observable.just(data).flatMap(s -> {
            return insertPartPromotionRuleParam(conn, ruleId, name, s, order).flatMap(integer -> {
                if(finalIndex >= value.size() - 1){
                    return Observable.just(value);
                }else{
                    return insertPromotionRuleParamListData(conn, ruleId, name, value,  order + 1, finalIndex + 1);
                }
            });
        });
    }

    /**
     * inser part data
     * @param conn
     * @param ruleId
     * @param name
     * @param value
     * @param order
     * @return
     */
    private Observable<Integer> insertPartPromotionRuleParam(SQLConnection conn, int ruleId, String name, String value, int order){
        JsonArray inParams = new JsonArray()
                .add(ruleId)
                .add(name)
                .add(value)
                .add(order);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return conn.callWithParamsObservable2(RULE_PARAM_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(4);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ RULE PROMOTION ] Insert Rule Param Promotion Failed" + StringPool.SPACE + result.getOutput().getString(5));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(6);
            }

            return returnValue;
        });
    }

    /**
     * update promotion param > 4000
     * @param conn
     * @param ruleParamId
     * @param name
     * @param value
     * @param order
     * @param index
     * @return
     */
    private Observable<List<String>> updatePromotionRuleParamListData(SQLConnection conn, int ruleParamId, String name, List<String> value, int order, int index){
        if(value.size() <= 0){
            return Observable.just(value);
        }
        final int finalIndex = index;
        String data = value.get(index);
        return Observable.just(data).flatMap(s -> {
            return updatePartPromotionRuleParam(conn, ruleParamId, name, s, order).flatMap(integer -> {
                if(finalIndex >= value.size() - 1){
                    return Observable.just(value);
                }else{
                    return updatePromotionRuleParamListData(conn, ruleParamId, name, value,  order + 1, finalIndex + 1);
                }
            });
        });
    }

    /**
     * update part data
     * @param conn
     * @param ruleParamId
     * @param name
     * @param value
     * @param order
     * @return
     */
    private Observable<Integer> updatePartPromotionRuleParam(SQLConnection conn, int ruleParamId, String name, String value, int order){
        JsonArray inParams = new JsonArray()
                .add(ruleParamId)
                .add(name)
                .add(value)
                .add(order);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return conn.callWithParamsObservable2(RULE_PARAM_UPDATE, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(4);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ RULE PROMOTION ] Update Rule Param Promotion Failed" + StringPool.SPACE + result.getOutput().getString(5));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(6);
            }


            return returnValue;
        });
    }

    /**
     * get regex rule type param
     * @param conn
     * @param ruleTypeId
     * @param paramName
     * @return
     */
    private Observable<String> regexRuleTypeParam(SQLConnection conn, int ruleTypeId, String paramName){
        JsonArray inParams = new JsonArray()
                .add(ruleTypeId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(PROMOTION_RULE_TYPE_PARAMS, inParams, outParams).map(result -> {
            String validationRegex = "";

                int resultCode = result.getOutput().getInteger(1);
                if (resultCode != 200) {
                    LOGGER.log(Level.SEVERE, "[ RULE PROMOTION ] Get Rule Param Regex Failed" + StringPool.SPACE + result.getOutput().getString(2));
                    throw  IErrors.INTERNAL_SERVER_ERROR;
                }else{
                    Gson gson = new Gson();

                    Type type = new TypeToken<Map<String, Object>>() {}.getType();

                    String returnValue = result.getOutput().getString(3);


                    Map<String, Object> ruleTypeParamMap = gson.fromJson(returnValue, type);

                    if (!ruleTypeParamMap.isEmpty() && ruleTypeParamMap.get(paramName.toLowerCase()) != null) {

                        Map paramInfoMap = (Map) ruleTypeParamMap.get(paramName.toLowerCase());

                        if (!paramInfoMap.isEmpty() && paramInfoMap.get("validate") != null) {

                            if (paramInfoMap.get("validate") instanceof String) {

                                validationRegex = paramInfoMap.get("validate").toString();

                            } else if (paramInfoMap.get("validate") instanceof Map) {

                                validationRegex = ((Map) paramInfoMap.get("validate")).get("multiple").toString();
                            }
                        }
                    }
                }

            return validationRegex;
        });
    }

    /**
     * delete promotion rule param
     * @param conn
     * @param ruleParamId
     * @return
     */
    private Observable<Integer> deletePromotionRuleParam(SQLConnection conn, int ruleId , int ruleParamId) {
        JsonArray inParams = new JsonArray()
                .add(ruleParamId)
                .add(ruleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return conn.callWithParamsObservable2(RULE_PARAM_DELETE, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ RULE PROMOTION ] Insert Rule Param Promotion Failed" + StringPool.SPACE + result.getOutput().getString(3));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(4);
            }

            return returnValue;
        });
    }

    /**
     * get list rule param by rule id
     * @param sqlConn
     * @param ruleId
     * @return
     */
    private Observable<List<PromotionRuleParam>> listPromotionRuleParam(SQLConnection sqlConn, int ruleId){
        JsonArray inParams = new JsonArray()
                .add(ruleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR) ;
        return sqlConn.callWithParamsObservable2(LIST_RULE_PARAM, inParams, outParams).map(result -> {

            List<PromotionRuleParam> promotionRuleParams = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PromotionRuleParam promotionRuleParam = bindParam(jsonObject);
                promotionRuleParams.add(promotionRuleParam);
            }

            return promotionRuleParams;
        });
    }

    /**
     * bind result set to rule param
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionRuleParam bindParam(JsonObject rs) {

        int nid = rs.getInteger("N_ID");
        String name = rs.getString("S_NAME");
        String value = rs.getString("S_VALUE");
        int order = rs.getInteger("N_ORDER");

        PromotionRuleParam promotionRuleParam  = new PromotionRuleParam();
        promotionRuleParam.setOrder(order);
        promotionRuleParam.setRule_param_id(nid);
        promotionRuleParam.setRule_param_name(name);
        promotionRuleParam.setRule_param_value(value);

        return promotionRuleParam;

    }

    private final static String LIST_RULE_PARAM = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_PARAMS(?,?,?,?)}";

    private final static String RULE_PARAM_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_PARAM_INSERT(?,?,?,?,?,?,?)}";

    private final static String RULE_PARAM_DELETE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_PARAM_DELETE(?,?,?,?,?)}";

    private static final String RULE_PARAM_UPDATE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_PARAM_UPDATE(?,?,?,?,?,?,?)}";

    private static final String PROMOTION_RULE_TYPE_PARAMS = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_TYPE_PARAMS(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PromotionRuleParamServiceImpl.class.getName());


}
