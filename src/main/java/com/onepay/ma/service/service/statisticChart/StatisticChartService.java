package com.onepay.ma.service.service.statisticChart;

import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.chartStatistics.ChartStatistics;
import com.onepay.ma.service.models.user.ChartDomesticParmester;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:43 PM
 * To change this ma-service.
 */
public interface StatisticChartService {
    Observable<ChartStatistics> list(SQLConnection connReadOnly, Map<String,String > mIn,String datef,String datet,String datename, String paygate,String target);
    Observable<List<ChartStatistics>> listAll(SQLConnection connReadOnly, Map<String,String > mIn, Object datef, Object datet, String paygate,List arr,List arrCard,List arrBank,String target);
    Observable<List<Map>> listfail(SQLConnection connReadOnly,String merchantId,String cardType,String datef, String datet, String paygate,String target);
    ResultSet dowloadfile(Connection connReadOnly, ChartStatisticQuery param, String paygate) throws SQLException;
    ResultSet dowloadFileTotal(Connection connReadOnly, ChartStatisticQuery query);
}
