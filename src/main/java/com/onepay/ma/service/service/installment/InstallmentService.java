package com.onepay.ma.service.service.installment;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.installment.InstallmentBank;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InstallmentService {

    private static final String GET_ALL_BANK = "{call PKG_INSTALLMENT.get_installment_bank(?,?,?)}";
    private static final String UPDATE_STATE  = "{call PKG_INSTALLMENT.update_installment_state(?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(InstallmentService.class.getName());

    public static Observable<BaseList<InstallmentBank>> getAllBank(SQLConnection sqlConnection) {
        JsonArray inParams = new JsonArray();
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_ALL_BANK, inParams, outParams).map(result -> {
            BaseList<InstallmentBank> bankBaseList = new BaseList<>();
            List<InstallmentBank> list = new ArrayList<>();

            if (result.getOutput().getInteger(1) != 200) {
                LOGGER.log(Level.SEVERE, "[GET ALL INSTALLMENT BANK ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InstallmentBank bank = new InstallmentBank();
                bank.setName(jsonObject.getString("S_BANK"));
                list.add(bank);
            }
            bankBaseList.setList(list);


            return bankBaseList;
        });
    }

    public static Observable<Void> rejectInstallment(SQLConnection sqlConnection, String id) {
        return updateState(sqlConnection, id, "void");
    }

    private static Observable<Void> updateState(SQLConnection sqlConnection, String id, String state) {
        JsonArray inParams = new JsonArray()
                .add(id)
                .add(state);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(UPDATE_STATE, inParams, outParams).map(result -> {
            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[UPDATE INSTALLMENT STATUS ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return null;
        });
    }
}
