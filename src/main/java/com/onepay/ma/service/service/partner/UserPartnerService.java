package com.onepay.ma.service.service.partner;

import com.onepay.ma.service.models.partner.Partners;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

public interface UserPartnerService {
    Observable<Integer> insert(SQLConnection sqlConnection, int userId, String partnerId);

    Observable<Integer> deleteByUserId(SQLConnection sqlConnection, int userId);

    Observable<Partners> getPartnersByUserId(SQLConnection sqlConnection, String userId);
}
