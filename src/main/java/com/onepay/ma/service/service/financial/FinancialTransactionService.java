package com.onepay.ma.service.service.financial;

import com.google.gson.Gson;
import com.ibm.icu.text.DateFormat;
import com.ibm.icu.text.SimpleDateFormat;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.AuthenticationData;
import com.onepay.ma.service.models.AvsData;
import com.onepay.ma.service.models.CardDate;
import com.onepay.ma.service.models.InternationalAmount;
import com.onepay.ma.service.models.InternationalCard;
import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.financial.FinancialTransaction;
import com.onepay.ma.service.models.financial.SamsungFinancialTransaction;
import com.onepay.ma.service.models.financial.FinancialTransactionQuery;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Component;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by tuydv on 09-5-18.
 */
@Component
public class FinancialTransactionService {

    private static final String SEARCH_TRANSACTION_V2 = "{call PKG_ONECREDIT.SEARCH_TRAN_MIGS_6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_CAPTURE_TRANSACTION_ONLINE_BY_IDS_V4 = "{call PKG_ONECREDIT.get_capture_txn_by_ids_v4(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_ONLINE_BY_IDS_V3 = "{call PKG_ONECREDIT.get_transaction_by_ids_v3(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.search_tran_qt(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_TRAN_MIGS_BY_ID_4(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(FinancialTransactionService.class.getName());

    private final static Gson gson = new Gson();

    public static Observable<Transactions<FinancialTransaction>> search(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, FinancialTransactionQuery query) {

        // LOGGER.log(Level.INFO, "query condition: " + gson.toJson(query));

        LOGGER.log(Level.INFO, "[SEARCH FINANCIAL INTERNATIONAL TRANSACTION DATA START]: ");
        Long start = System.currentTimeMillis();

        Transactions<FinancialTransaction> transactions = new Transactions<>();

        return getListTotalTransactionBackup(connBackup, query).flatMap(totalApproval -> {

            return getListTotalTransaction(connOnline, query, -1).flatMap(total1 -> {

                return getListTotalTransaction(connReadOnly, query, 1).flatMap(total2 -> {

                    transactions.setTotal_items(total1 + total2 + totalApproval);

            //         // Get online transactions

                    return getListTransactionBackup(connBackup, query).flatMap(refundApproval -> {


                        query.setOffset(totalApproval);
                        return searchTransaction(connOnline, query, -1).flatMap(onlineTrans -> {
                            // offset
                            query.setOffset(total1 + totalApproval);
                            // Get readonly transaction
                            return searchTransaction(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {
                                List<FinancialTransaction> l = new ArrayList();
                                l.addAll(refundApproval);
                                l.addAll(onlineTrans);
                                l.addAll(readOnlyTrans);
                                l.sort((o1, o2) -> {
                                    return o2.getDate().compareTo(o1.getDate());
                                });
                                transactions.setTransactions(l);

                                return Observable.just(transactions);
                            });
                        });
                    });

                });

            });
        });

    }

    public Observable<FinancialTransaction> get(SQLConnection connOnline, String Id) {
        JsonArray inParams = new JsonArray()
                .add(Id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET FINANCIAL TRANSACTION DETAIL ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            FinancialTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            return transaction;
        });
    }


    private static Observable<Integer> getListTotalTransaction(SQLConnection connOnline, FinancialTransactionQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(query.getStatus())
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile() == null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email() == null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website() == null ? "" : query.getMerchant_website())
                .add(query.getFraud_check() == null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code() == null ? "" : query.getAuthorisation_code())
                .add(query.getInstallment_bank() == null ? "" : query.getInstallment_bank())
                .add(query.getInstallment_status() == null ? "" : query.getInstallment_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(SEARCH_TRANSACTION_V2, inParams, outParams).map(result -> {
            Integer total = 0;
            Integer reusltCode = result.getOutput().getInteger(26);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH Financial TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(27));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(28).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });
    }


    private static Observable<List<FinancialTransaction>> searchTransaction(SQLConnection sqlConnection, FinancialTransactionQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(query.getStatus())
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getOffset())
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile())
                .add(query.getCustomer_email())
                .add(query.getMerchant_website())
                .add(query.getFraud_check())
                .add(query.getAuthorisation_code())
                .add(query.getInstallment_bank())
                .add(query.getInstallment_status())
                .add(query.getRisk_assessment())
                .add(query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return sqlConnection.callWithParamsObservable2(SEARCH_TRANSACTION_V2, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(26);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH Financial TRANSACTION ERROR]: " + result.getOutput().getString(27));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<FinancialTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(28).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    FinancialTransaction transaction = bindTransaction(jsonObject);
                    transaction.setAdvance_status(jsonObject.getString("S_ADVANCE_STATUS"));
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }

    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection connReadBackup, FinancialTransactionQuery query) {
        return getListTotalTransactionBackup(connReadBackup, query).flatMap(totalApproval -> {
            // return mapByIds(connOnline, String.join(StringPool.COMMA, totalApproval),
            // query).flatMap(totalPurchaseMap -> {
            return getListTotalTransaction(connOnline, query, -1).flatMap(totalItem1 -> {
                return getListTotalTransaction(connReadOnly, query, 1).map(totalItem2 -> {
                    return totalItem1 + totalItem2 + totalApproval;
                });
            });
            // });
        });
    }

    public static ResultSet downloadBackup(Connection connBackup, FinancialTransactionQuery parameter) throws SQLException {
        return downloadBackupData(connBackup, parameter);
    }

    public static ResultSet downloadOnline(Connection connOnline, FinancialTransactionQuery parameter) throws SQLException {
        return downloadData(connOnline, parameter, -1);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, FinancialTransactionQuery parameter) throws SQLException {
        return downloadData(connReadOnly, parameter, 1);
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadBackupData(Connection connection, FinancialTransactionQuery query) throws SQLException {
        String status = "0";
        if (null == query.getStatus() || query.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(query.getStatus())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(query.getStatus())
                || "Waiting for onepays approval".equalsIgnoreCase(query.getStatus())) {
            status = "405";
        }

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(status)
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile() == null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email() == null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website() == null ? "" : query.getMerchant_website())
                .add(query.getFraud_check() == null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code() == null ? "" : query.getAuthorisation_code())
                .add(query.getInstallment_bank() == null ? "" : query.getInstallment_bank())
                .add(query.getInstallment_status() == null ? "" : query.getInstallment_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);

                CallableStatement callableStatement = null;
                ResultSet rs = null;
                try {
                    callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION_BACKUP, inParams, outParams);
        
                    Integer reusltCode = callableStatement.getInt(26);
        
                    if (reusltCode != 200) {
                        LOGGER.log(Level.WARNING, "[DOWNLOAD FINANCIAL TRANSACTION BACKUP ERROR]: " + callableStatement.getString(27));
                    }
        
                    rs = (ResultSet) callableStatement.getObject(28);
        
                } catch (SQLException ex) {
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                return rs;
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param query
     * @return
     */
    private static ResultSet downloadData(Connection connection, FinancialTransactionQuery query, int row) throws SQLException {
        JsonArray inParams = new JsonArray()
        .add(row)
        .add(QueryMethod.DOWNLOAD.toString())
        .add(query.getMerchant_id())
        .add(query.getFrom_date())
        .add(query.getTo_date())
        .add(query.getOrder_info())
        .add(query.getStatus())
        .add(query.getCard_number())
        .add(query.getCard_type())
        .add(query.getMerchant_transaction_ref())
        .addNull()
        .addNull()
        .addNull()
        .add(query.getCurrency())
        .add(query.getTransaction_type())
        .add(query.getAcquirer_id())
        .add(query.getTransaction_id())
        .add(query.getCustomer_mobile())
        .add(query.getCustomer_email())
        .add(query.getMerchant_website())
        .add(query.getFraud_check())
        .add(query.getAuthorisation_code())
        .add(query.getInstallment_bank())
        .add(query.getInstallment_status())
        .add(query.getRisk_assessment())
        .add(query.getHidden_column());
JsonArray outParams = new JsonArray()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .addNull()
        .add(OracleTypes.INTEGER)
        .add(OracleTypes.VARCHAR)
        .add(OracleTypes.CURSOR);


        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_TRANSACTION_V2, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(27);

            if (reusltCode != 200) {
                LOGGER.log(Level.WARNING, "[DOWNLOAD FINANCIAL TRANSACTION ONLINE ERROR]: " + callableStatement.getString(28));
            }

            rs = (ResultSet) callableStatement.getObject(29);

        } catch (SQLException ex) {
            LOGGER.log(Level.SEVERE, "[DOWNLOAD FINANCIAL TRANSACTION ONLINE ERROR]: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }


    private static Observable<List<FinancialTransaction>> getListTransactionBackup(SQLConnection connBackup, FinancialTransactionQuery query) {
        String status = "0";
        if (null == query.getStatus() || query.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(query.getStatus())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(query.getStatus())
        || "Waiting for onepays approval".equalsIgnoreCase(query.getStatus())) {
            status = "405";
        }
        
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(status)
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(0)
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile() == null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email() == null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website() == null ? "" : query.getMerchant_website())
                .add(query.getFraud_check() == null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code() == null ? "" : query.getAuthorisation_code())
                .add(query.getInstallment_bank() == null ? "" : query.getInstallment_bank())
                .add(query.getInstallment_status() == null ? "" : query.getInstallment_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(25);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET QT REQUEST REFUND  ERROR : " + resultSet.getOutput().getString(26));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<FinancialTransaction> list = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(27).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                FinancialTransaction transaction = bindTransaction(jsonObject);
                transaction.setAdvance_status(jsonObject.getString("S_ADVANCE_STATUS"));
                list.add(transaction);
            }
            return list;
        });
    }


    private static Observable<Integer> getListTotalTransactionBackup(SQLConnection connBackup, FinancialTransactionQuery query) {
        String status = "0";
        if (null == query.getStatus() || query.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(query.getStatus())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(query.getStatus())
                || "Waiting for onepays approval".equalsIgnoreCase(query.getStatus())) {
            status = "405";
        }

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getOrder_info())
                .add(status)
                .add(query.getCard_number())
                .add(query.getCard_type())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .addNull()
                .addNull()
                .add(query.getCurrency())
                .add(query.getTransaction_type())
                .add(query.getAcquirer_id())
                .add(query.getTransaction_id())
                .add(query.getCustomer_mobile() == null ? "" : query.getCustomer_mobile())
                .add(query.getCustomer_email() == null ? "" : query.getCustomer_email())
                .add(query.getMerchant_website() == null ? "" : query.getMerchant_website())
                .add(query.getFraud_check() == null ? "" : query.getFraud_check())
                .add(query.getAuthorisation_code() == null ? "" : query.getAuthorisation_code())
                .add(query.getInstallment_bank() == null ? "" : query.getInstallment_bank())
                .add(query.getInstallment_status() == null ? "" : query.getInstallment_status())
                .add(query.getRisk_assessment() == null ? "" : query.getRisk_assessment())
                .add(query.getHidden_column() == null ? "" : query.getHidden_column());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(25);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL MPAY REFUND ERROR : " + resultSet.getOutput().getString(26));
                throw IErrors.DOMESTIC_SERVER_ERROR;
            }

            Integer result = 0;
            Map map = resultSet.getOutput().getJsonObject(27).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result = jsonObject.getInteger("N_TOTAL");
            }
            return result;
        });
    }


    // add by danhnt
    // private static Observable<Map<String, InternationalTransaction>> mapByIds(SQLConnection
    // connOnline, String transIds, FinancialTransactionQuery parameter) {
    // return listByIds(connOnline, transIds, parameter).flatMap(internationalTransactions -> {
    // return listCaptureByIds(connOnline, transIds, parameter).map(captureTransactions -> {
    // Map<String, InternationalTransaction> map = new HashMap<>();
    // internationalTransactions.addAll(captureTransactions);
    // for (InternationalTransaction dt : internationalTransactions) {
    // map.put(String.valueOf(dt.getTransaction_id()), dt);
    // }
    // return map;
    // });

    // });
    // }

    // public static Map<Integer, InternationalTransaction> transMapByIdsDownload(Connection connOnline,
    // String transIds, FinancialTransactionQuery parameter) {
    // List<InternationalTransaction> internationalTransactions = listByIdsDownload(connOnline,
    // transIds, parameter);
    // List<InternationalTransaction> captureTransactions = listCaptureByIdsDownload(connOnline,
    // transIds, parameter);
    // Map<Integer, InternationalTransaction> map = new HashMap<>();
    // internationalTransactions.addAll(captureTransactions);
    // for (InternationalTransaction dt : internationalTransactions) {
    // map.put(dt.getTransaction_id(), dt);
    // }
    // return map;
    // }

    private static Observable<List<InternationalTransaction>> listByIds(SQLConnection connOnline, String transIds, FinancialTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrder_info())
                .add(parameter.getAcquirer_id())
                .add(parameter.getCard_number())
                .add(parameter.getInstallment_bank())
                .add(parameter.getInstallment_status())
                .add(parameter.getRisk_assessment())
                .add(parameter.getAuthorisation_code());

        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_TRANSACTION_BY_IDS_V3, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_ONLINE_BY_IDS_V3, inParams, outParams).map(resultSet -> {
            List<InternationalTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(10) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS INTERNATIONAL TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = resultSet.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jSon : rs.getRows()) {
                InternationalTransaction transaction = bindInterTransaction(jSon);
                result.add(transaction);
            }
            return result;
        });
    }

    private static List<InternationalTransaction> listByIdsDownload(Connection connOnline, String transIds, FinancialTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrder_info())
                .add(parameter.getAcquirer_id())
                // .add("")
                .add(parameter.getCard_number())
                .add("")
                .add("")
                .add("")
                .add(parameter.getAuthorisation_code());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_TRANSACTION_BY_IDS_V3, IN: " + inParams + ", OUT: " + outParams);
        ResultSet rs;
        CallableStatement callableStatement = null;
        List<InternationalTransaction> result = new ArrayList<>();
        try {

            callableStatement = ExportDatasourceUtil.execute(connOnline, LIST_TRANSACTION_ONLINE_BY_IDS_V3, inParams, outParams);
            if (callableStatement.getInt(11) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS TRANSACTION ONLINE ERROR]: " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(10);
            while (rs.next()) {
                InternationalTransaction transaction = bindInterTransactionDownload(rs);
                result.add(transaction);
            }
            return result;
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
    }

    /**
     * Authorize capture
     * Search capture trans by id
     * 
     * @param connOnline
     * @param transIds
     * @param parameter
     */
    private static Observable<List<InternationalTransaction>> listCaptureByIds(SQLConnection connOnline, String transIds, FinancialTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrder_info())
                .add(parameter.getAcquirer_id())
                // .add("")
                .add(parameter.getCard_number())
                .add(parameter.getInstallment_bank())
                .add(parameter.getInstallment_status())
                .add(parameter.getRisk_assessment())
                .add(parameter.getAuthorisation_code());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                // .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_CAPTURE_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_CAPTURE_TRANSACTION_ONLINE_BY_IDS_V4, inParams, outParams).map(resultSet -> {
            List<InternationalTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(10) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS INTERNATIONAL TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = resultSet.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransaction transaction = bindInterTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }

    /**
     * Authorize capture
     * Search capture trans by id
     * 
     * @param connOnline
     * @param transIds
     * @param parameter
     */
    private static List<InternationalTransaction> listCaptureByIdsDownload(Connection connOnline, String transIds, FinancialTransactionQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrder_info())
                .add(parameter.getAcquirer_id())
                // .add("")
                .add(parameter.getCard_number())
                .add("")
                .add("")
                .add("")
                .add(parameter.getAuthorisation_code());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.INFO, "[CALL ]: PKG_ONECREDIT.GET_CAPTURE_TRANSACTION_BY_IDS_V3, IN: " + inParams + ", OUT: " + outParams);
        ResultSet rs;
        CallableStatement callableStatement = null;
        List<InternationalTransaction> result = new ArrayList<>();
        try {

            callableStatement = ExportDatasourceUtil.execute(connOnline, LIST_CAPTURE_TRANSACTION_ONLINE_BY_IDS_V4, inParams, outParams);
            if (callableStatement.getInt(11) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS TRANSACTION ONLINE ERROR]: " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(10);
            while (rs.next()) {
                InternationalTransaction transaction = bindInterTransactionDownload(rs);
                result.add(transaction);
            }
            return result;
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

    }


    private static Integer countFilterById(Map<String, InternationalTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(transactionId) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    private static FinancialTransaction joinApproveWithPurchase(FinancialTransaction approve, InternationalTransaction purchase) {

        if (purchase == null) {
            return null;
        }

        FinancialTransaction result = approve;

        result.setOrder_ref(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer().getAcquirer_name());
        // result.setAdvance_status(purchase.getAdvance_status());
        result.setCard_number(purchase.getCard().getCard_number());
        // result.getAmount().setTotal(purchase.getAmount().getTotal());
        result.setResponse(purchase.getResponse_code());
        result.setInstallment_bank(result.getInstallment_bank());
        result.setInstallment_status(result.getInstallment_status());
        result.setRisk_assesment(purchase.getRisk_assesment());
        // result.setIp_address(purchase.getIp_address());
        result.setTranaction_id(purchase.getOriginal_transaction_id() + "");
        return result;
    }

    /**
     * convert data from result set to Inter transaction
     *
     * @param rs
     * @return InternationalTransaction
     * @throws SQLException
     */
    public static InternationalTransaction bindInterTransaction(JsonObject rs) {
        String cardNumber = rs.getString("S_CARD_NO");
        String nameOnCard = rs.getString("S_CARD_HOLDER") != null ? rs.getString("S_CARD_HOLDER") : rs.getString("S_NAME_ON_CARD");
        String bankId = rs.getString("S_BANK_ID");
        String cardDate = rs.getString("S_CARD_EXP");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
        String currency = rs.getString("S_CURRENCY");
        String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
        String cardType = rs.getString("S_CARD_TYPE");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
        String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
        int transactionId = rs.getInteger("EXT_ID");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE"));

        // try {
        // transactionTime = new Timestamp(formatter.parse(rs.getString("EXT_DATE")).getTime());
        // } catch (ParseException e) {
        // LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
        // }
        double total = rs.getDouble("EXT_AMOUNT");
        String transactionType = rs.getString("EXT_TYPE");
        String transactionStatus = rs.getString("EXT_STATUS");
        String transactionRefNumber = rs.getString("EXT_REF_NUMBER");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        String binCountry = rs.getString("S_BIN_COUNTRY");
        String avsResultCode = rs.getString("S_AVS_RESULT_CODE");
        String address = rs.getString("S_ADDRESS");
        String state = rs.getString("S_STATE_PROVINCE");
        String zipCode = rs.getString("S_ZIP_POSTAL_CODE");
        String country = rs.getString("S_COUNTRY");
        String city = rs.getString("S_CITY_TOWN");
        String verificationSecurityLevel = rs.getString("S_VERIFICATION_SECURITY_LEVEL");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String cscResult = rs.getString("S_CSCRESULT_CODE");
        String enrolled3DS = rs.getString("S_3DS_ENROLLED");
        String ip = rs.getString("S_IP");
        String ipProxy = rs.getString("S_IP_PROXY");
        String operator = rs.getString("S_OPERATOR_ID");
        double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
        boolean canVoid = rs.getInteger("CAN_VOID") != null && rs.getInteger("CAN_VOID") == 1 ? true : false;
        boolean requiredAvs = rs.getInteger("N_REQUIRED_AVS") != null && rs.getInteger("N_REQUIRED_AVS") == 1 ? true : false;
        String eci = rs.getString("S_3DS_ECI");
        String ticketNumber = rs.getString("S_TICKET_NUMBER");
        String authorizationAmount = rs.getString("S_AUTHORISED_AMOUNT");
        String commercialCardIndicator = rs.getString("S_COMMERCIAL_CARD_INDICATOR");
        String risk_assesment = rs.getString("S_FRAUD_INFO_FOR_MER_ALERT");


        // PP
        String status3ds = rs.getString("S_3DS_STATUS");
        String riskOverallResult = rs.getString("S_RISK_OVERALL_RESULT");
        String xid = rs.getString("S_3DS_XID");
        String cardLevelIndicator = rs.getString("S_CARD_LEVEL_INDICATOR");



        InternationalTransaction transaction = new InternationalTransaction();
        if (risk_assesment != null) {
            transaction.setRisk_assesment("Review Required");
        } else {
            transaction.setRisk_assesment("Not Assessed");
        }
        transaction.setBank_id(bankId);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(transactionTime);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ip);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_reference(transactionRef);
        transaction.setBin_country(binCountry);
        transaction.setCsc_result_code(cscResult);
        transaction.setMerchant_id(merchantId);
        transaction.setOperator(operator);
        transaction.setEnrolled_3ds(enrolled3DS);
        transaction.setTransaction_status(transactionStatus);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_ref_number(transactionRefNumber);
        transaction.setVerification_security_level(verificationSecurityLevel);
        transaction.setResponse_code(responseCode);
        transaction.setIp_proxy(ipProxy);
        transaction.setCan_void(canVoid);
        transaction.setIs_required_avs(requiredAvs);
        transaction.setEci(eci);
        transaction.setAdvance_status(rs.getString("S_ADVANCE_STATUS"));
        transaction.setTicket_number(ticketNumber);

        transaction.setInstallment_bank(rs.getString("S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(rs.getString("S_INSTALLMENT_STATE"));

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        if (acquirerId == 1) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 2) {
            acquirer.setAcquirer_short_name("VietinBank");
            acquirer.setAcquirer_name("VietinBank");
        } else if (acquirerId == 3) {
            acquirer.setAcquirer_short_name("CUP");
            acquirer.setAcquirer_name("CUP");
        } else if (acquirerId == 4) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 5) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 6) {
            acquirer.setAcquirer_short_name("Paypal");
            acquirer.setAcquirer_name("Paypal");
        } else if (acquirerId == 7) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 8) {
            acquirer.setAcquirer_short_name("BIDV");
            acquirer.setAcquirer_name("BIDV");
        } else if (acquirerId == 9) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 10) {
            acquirer.setAcquirer_short_name("Techcombank");
            acquirer.setAcquirer_name("Techcombank");
        } else if (acquirerId == 11) {
            acquirer.setAcquirer_short_name("VPB");
            acquirer.setAcquirer_name("VPB");
        }

        transaction.setAcquirer(acquirer);

        AvsData avsData = new AvsData();
        avsData.setAddress(address);
        avsData.setCountry(country);
        avsData.setCity(city);
        avsData.setProvince(state);
        avsData.setResult_code(avsResultCode);
        avsData.setZip_code(zipCode);

        transaction.setAvs(avsData);

        InternationalAmount amount = new InternationalAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(refundTotal);
        // tong so tien refund capture man hinh detail giao dich authorize
        if (rs.containsKey("EXT_AUTH_REFUND_CAP_AMOUNT") && rs.getDouble("EXT_AUTH_REFUND_CAP_AMOUNT") > 0) {
            double refundCaptureTotal = rs.getDouble("EXT_AUTH_REFUND_CAP_AMOUNT");
            amount.setRefund_capture_total(refundCaptureTotal);
        }
        // tong so tien refund capture man hinh detail giao dich capture
        if (rs.containsKey("EXT_CAP_REFUND_CAP_AMOUNT") && rs.getDouble("EXT_CAP_REFUND_CAP_AMOUNT") > 0) {
            double refundCaptureTotal = rs.getDouble("EXT_CAP_REFUND_CAP_AMOUNT");
            amount.setRefund_capture_total(refundCaptureTotal);
        }

        transaction.setAmount(amount);

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);
        authenticationData.setAuthorization_amount(authorizationAmount);

        transaction.setAuthentication(authenticationData);


        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setName_on_card(nameOnCard);
        card.setCommercical_card(commercialCard);
        card.setCommercial_card_indicator(commercialCardIndicator);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);

        // PP
        transaction.setXid(xid);
        transaction.setRiskOverAllResult(riskOverallResult);
        transaction.setStatus3ds(status3ds);
        transaction.setCardLevelIndicator(cardLevelIndicator);



        // SAMSUNG
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

        // ADAYROI
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));

        // Installment
        transaction.setInstallment_bank(rs.getString("S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(rs.getString("S_INSTALLMENT_STATE"));
        transaction.setInstallment_cus_email(rs.getString("S_INSTALLMENT_CUST_EMAIL"));
        transaction.setInstallment_cus_phone(rs.getString("S_INSTALLMENT_CUST_PHONE"));
        transaction.setInstallment_time(rs.getString("S_INSTALLMENT_TIME"));
        transaction.setInstallment_cancel_days(rs.getInteger("N_INSTALLMENT_CANCEL_DAYS"));
        transaction.setInstallment_monthly_amount(rs.getDouble("N_INSTALLMENT_MONTHLY_AMOUNT"));
        transaction.setInstallment_fee(rs.getDouble("N_INSTALLMENT_FEE"));
        transaction.setCard_holder_name(rs.getString("S_CARD_HOLDER"));

        return transaction;
    }

    /**
     * convert data from result set to Inter transaction
     *
     * @param rs
     * @return InternationalTransaction
     * @throws SQLException
     */
    public static InternationalTransaction bindInterTransactionDownload(ResultSet rs) {
        InternationalTransaction transaction = new InternationalTransaction();
        try {
            String cardNumber = rs.getString("S_CARD_NO");
            String cardDate = rs.getString("S_CARD_EXP");
            String orderInfo = rs.getString("S_ORDER_INFO");
            String currency = rs.getString("S_CURRENCY");
            String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
            String cardType = rs.getString("S_CARD_TYPE");
            String merchantId = rs.getString("S_MERCHANT_ID");
            String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
            String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
            int transactionId = rs.getInt("EXT_ID");
            Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE"));

            double total = rs.getDouble("EXT_AMOUNT");
            String responseCode = rs.getString("S_RESPONSE_CODE");
            String commercialCard = rs.getString("S_COMMERCIAL_CARD");
            double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");

            transaction.setTransaction_id(transactionId);
            transaction.setTransaction_time(transactionTime);
            transaction.setOrder_info(orderInfo);
            transaction.setMerchant_id(merchantId);
            transaction.setResponse_code(responseCode);
            transaction.setBank_id(rs.getString("S_BANK_ID"));

            InternationalAmount amount = new InternationalAmount();
            amount.setCurrency(currency);
            amount.setTotal(total);
            amount.setRefund_total(refundTotal);
            transaction.setAmount(amount);

            AuthenticationData authenticationData = new AuthenticationData();
            authenticationData.setAuthentication_state(authenticationState);
            authenticationData.setAuthentication_type(authenticationType);
            authenticationData.setAuthorization_code(authorizationCode);
            transaction.setAuthentication(authenticationData);
            transaction.setBin_country(rs.getString("s_bin_country"));
            InternationalCard card = new InternationalCard();
            card.setCard_number(cardNumber);
            card.setCard_type(cardType);
            card.setCommercical_card(commercialCard);

            CardDate cardDateData = new CardDate();
            if (cardDate != null && cardDate.length() == 4) {
                cardDateData.setYear(cardDate.substring(0, 2));
                cardDateData.setMonth(cardDate.substring(2, 4));
            }

            card.setCard_date(cardDateData);

            transaction.setCard(card);
        } catch (Exception ex) {
            LOGGER.warning("BIN DATA DOWNLOAD EXCEPTION " + ex.getMessage());
        }
        return transaction;
    }

    // end add by danhnt
    private static FinancialTransaction bindTransaction(JsonObject rs) {
        Integer id = rs.getInteger("N_ID");
        String transaction_no = rs.getString("S_TRANSACTION_NO");
        Timestamp date = rs.getString("D_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_DATE"));
        String merchantId = rs.getString("S_MERCHANTID");
        String acquirerid = rs.getInteger("N_ACQUIRERID") == null ? "" : rs.getInteger("N_ACQUIRERID").toString();
        String trans_ref = rs.getString("S_MERCHANTTRANSACTIONREFEREN");
        String trans_type = rs.getString("S_TRANSACTIONTYPE");
        double total = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCY");
        String response = rs.getString("S_RESPONSECODE");
        String order_ref = rs.getString("S_ORDERREFERENCE");
        String card_number = rs.getString("S_CARDNO");
        String name_on_card = rs.getString("S_NAME_ON_CARD");
        String auth_code = rs.getString("S_AUTHORISATIONCODE");
        String payment_auth_id = rs.getString("S_PAYMENT_AUTHENTICATION_ID");
        String batch_number = rs.getString("S_BATCHNUMBER");
        Integer transactionId = rs.getInteger("ONECREDIT_TRANS_ID");
        String cardType = rs.getString("S_CARDTYPE");
        String base_status = rs.getString("S_STATUS");
        String bank_id = rs.getString("S_BANK_ID");
        String risk_assesment = null;
        try {
            risk_assesment = rs.getString("S_FRAUD_INFO_FOR_MER_ALERT");
        } catch (Exception e) {

        }

        /* Acquirer acquirer = new Acquirer();
        //        acquirer.setCard_type(rs.getInteger("S_ACQUIRER_ID"));
        acquirer.setAcquirer_name(rs.getString("S_INSTRUMENT_TYPE"));*/

        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(0.0); // TODO: fill correct one.


        FinancialTransaction transaction = new FinancialTransaction();
        if (trans_type.equals("Purchase")) {
            if (risk_assesment != null) {
                transaction.setRisk_assesment("Review Required");
            } else {
                transaction.setRisk_assesment("Not Assessed");
            }
        }
        transaction.setId(id);
        transaction.setTrans_no(transaction_no);
        transaction.setDate(date);
        transaction.setMerchant_id(merchantId);
        transaction.setAcquirer(acquirerid);
        transaction.setTrans_ref(trans_ref);
        transaction.setTrans_type(trans_type);
        transaction.setCurrency(currency);
        transaction.setCard_type(cardType);
        transaction.setResponse(response);
        transaction.setOrder_ref(order_ref);
        transaction.setCard_number(card_number);
        transaction.setName_on_card(name_on_card);
        transaction.setAuth_code(auth_code);
        transaction.setAmount(amount);
        transaction.setPayment_auth_id(payment_auth_id);
        transaction.setBatch_number(batch_number);
        transaction.setTranaction_id(transactionId == null ? "" : transactionId.toString());
        transaction.setBase_status(base_status);
        transaction.setBank_id(bank_id);
        // INSTALLMENT
        transaction.setInstallment_bank(rs.getString("S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(rs.getString("S_INSTALLMENT_STATE"));

        // SAMSUNG
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

        // ADAYROI
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));

        // Return.
        return transaction;
    }

    private static SamsungFinancialTransaction bindSamsungTransaction(JsonObject rs) {
        Integer id = rs.getInteger("N_ID");
        String transaction_no = rs.getString("S_TRANSACTION_NO");
        Timestamp date = rs.getString("D_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_DATE"));
        String merchantId = rs.getString("S_MERCHANTID");
        String acquirerid = rs.getInteger("N_ACQUIRERID").toString();
        String trans_ref = rs.getString("S_MERCHANTTRANSACTIONREFEREN");
        String trans_type = rs.getString("S_TRANSACTIONTYPE");
        double total = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCY");
        String response = rs.getString("S_RESPONSECODE");
        String order_ref = rs.getString("S_ORDERREFERENCE");
        String card_number = rs.getString("S_CARDNO");
        String auth_code = rs.getString("S_AUTHORISATIONCODE");
        String payment_auth_id = rs.getString("S_PAYMENT_AUTHENTICATION_ID");
        String batch_number = rs.getString("S_BATCHNUMBER");
        Integer transactionId = rs.getInteger("ONECREDIT_TRANS_ID");
        String cardType = rs.getString("S_CARDTYPE");
        String base_status = rs.getString("S_STATUS");
        String order_status = rs.getString("S_REVIEW_STATE");
        String order_desc = rs.getString("S_REVIEW_DESC");
        int n_fraud = rs.getInteger("N_FRAUD") == null ? 0 : rs.getInteger("N_FRAUD");

        /* Acquirer acquirer = new Acquirer();
        //        acquirer.setCard_type(rs.getInteger("S_ACQUIRER_ID"));
        acquirer.setAcquirer_name(rs.getString("S_INSTRUMENT_TYPE"));*/

        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(0.0); // TODO: fill correct one.



        SamsungFinancialTransaction transaction = new SamsungFinancialTransaction();
        transaction.setId(id);
        transaction.setTrans_no(transaction_no);
        transaction.setDate(date);
        transaction.setMerchant_id(merchantId);
        transaction.setAcquirer(acquirerid);
        transaction.setTrans_ref(trans_ref);
        transaction.setTrans_type(trans_type);
        transaction.setCurrency(currency);
        transaction.setCard_type(cardType);
        transaction.setResponse(response);
        transaction.setOrder_ref(order_ref);
        transaction.setCard_number(card_number);
        transaction.setAuth_code(auth_code);
        transaction.setAmount(amount);
        transaction.setPayment_auth_id(payment_auth_id);
        transaction.setBatch_number(batch_number);
        transaction.setTranaction_id(transactionId == null ? "" : transactionId.toString());
        transaction.setBase_status(base_status);

        // SAMSUNG
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));
        transaction.setOrder_status(order_status);
        transaction.setOrder_desc(order_desc);
        transaction.setN_fraud(n_fraud);
        // Return.
        return transaction;
    }
}
