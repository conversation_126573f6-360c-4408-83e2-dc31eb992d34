package com.onepay.ma.service.service;


import com.onepay.ma.service.models.RoleData;
import com.onepay.ma.service.models.RoleParam;
import com.onepay.ma.service.models.Roles;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public interface RoleService {
    Observable<List<RoleData>> listRoleByUserId(SQLConnection connection, String userId);
    Observable<List<RoleData>> listRoleByPermissionId(SQLConnection connection, int permissionId);
    Observable<Roles> list(SQLConnection connection, String keywords);
    Observable<RoleData> get(SQLConnection connection, String roleId);
    Observable<RoleData> insert(SQLConnection connection, RoleParam parameter);
    Observable<Integer> delete(SQLConnection connection, String roleId);
    Observable<RoleData> update(SQLConnection connection, RoleParam parameter, String roleId);
    Observable<RoleData> updateParentOrder(SQLConnection connection, String parentId, String orderId, String roleId);
    Observable<List<RoleData>> listRoleByUserIdV2(SQLConnection connection, String user_id);
}
