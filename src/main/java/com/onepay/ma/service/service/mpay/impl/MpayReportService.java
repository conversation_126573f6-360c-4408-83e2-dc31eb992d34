package com.onepay.ma.service.service.mpay.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.mpay.MpayReport;
import com.onepay.ma.service.models.mpay.MpayReportQuery;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by anhkh on 04-Jun-18.
 */
@Service
public class MpayReportService {

    private final static String REPORT_MPAY_DOWNLOAD = "{call PKG_MPAY.SEARCH_REPORTS_2(?,?,?,?,?,?,?,?,?,?,?)}";
    private final static String REPORT_MPAY_DOWNLOAD_V2 = "{call PKG_MPAY.SEARCH_REPORTS_3(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private final static String REPORT_MPAY_TOTAL = "{call PKG_MPAY.SEARCH_REPORTS_TOTAL(?,?,?,?,?,?,?,?,?,?,?)}";
    private final static String REPORT_MPAY_TOTAL_V2 = "{call PKG_MPAY.SEARCH_REPORTS_TOTAL_2(?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(MpayReportService.class.getName());

    public static  Observable<List<MpayReport>> list(SQLConnection connection, MpayReportQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAppName() == null ? "" : parameter.getAppName())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getVersion() == null ? "" : parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_MPAY_DOWNLOAD_V2, inParams, outParams).map(result -> {
            int code = result.getOutput().getInteger(10);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[SEARCH MPAY REPORT ERROR]: " + result.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<MpayReport> reports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String merchantId = "";
            String merchantName = "";
            String appName = "";
            String client = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_INS_TYPE") == null ? "" : jsonObject.getString("S_INS_TYPE");
                    String mid = jsonObject.getString("S_MERCHANT_ID");
                    String appNameData = jsonObject.getString("S_APP_NAME") == null ? "" : jsonObject.getString("S_APP_NAME");
                    if (!jsonObject.getString("D_DATE").equals(date)  || !mid.equals(merchantId) || !cardTypeData.equals(cardType) || !appNameData.equals(appName)) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MERCHANT_NAME", merchantName);
                        objectData.put("D_DATE", date);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_APP_NAME", appName);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("S_CLIENT_ID", client);
                        reports.add(bindReport(objectData));
                        objectData = new JsonObject();
                    }
                }

                date = jsonObject.getString("D_DATE");
                String cardTypeData = jsonObject.getString("S_INS_TYPE");
                cardType = cardTypeData;
                merchantId = jsonObject.getString("S_MERCHANT_ID");
                merchantName = jsonObject.getString("S_MERCHANT_NAME");
                appName = jsonObject.getString("S_APP_NAME");
                client = jsonObject.getString("S_CLIENT_ID");
                if (jsonObject.getString("S_TRANSACTION_TYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", jsonObject.getInteger("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MERCHANT_NAME", merchantName);
                    objectData.put("D_DATE", date);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_APP_NAME", appName);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("S_CLIENT_ID", client);
                    reports.add(bindReport(objectData));
                    objectData = new JsonObject();
                }
            }


            return reports;
        });
    }

    public static  Observable<Integer> getTotalReport(SQLConnection connReadOnly, MpayReportQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAppName() == null ? "" : parameter.getAppName())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getVersion() == null ? "" : parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(REPORT_MPAY_TOTAL_V2, inParams, outParams).map(result -> {
            int code = result.getOutput().getInteger(10);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[MPAY REPORT TOTAL ERROR]: " + result.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return result.getOutput().getInteger(9);
        });
    }

    public static  List<MpayReport> download(Connection connReadOnly, MpayReportQuery parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getBankId())
                .add(parameter.getAppName() == null ? "" : parameter.getAppName())
                .add(parameter.getClientId() == null ? "" : parameter.getClientId())
                .add(parameter.getVersion() == null ? "" : parameter.getVersion());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        List<MpayReport> reports = new ArrayList<>();
        try {
            callableStatement = ExportDatasourceUtil.execute(connReadOnly, REPORT_MPAY_DOWNLOAD_V2, inParams, outParams);

            int code = callableStatement.getInt(11);

            if (code != 200) {
                LOGGER.log(java.util.logging.Level.SEVERE, "[DOWNLOAD MPAY REPORT ERROR]: " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(10);
            boolean recordsAvailable = rs.next();

//        Map map = result.getOutput().getJsonObject(5).getMap();
//        io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String appName = "";
            String merchantId = "";
            String merchantName = "";
            String clientId = "";
            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = rs.getString("S_INS_TYPE")  == null ? "" : rs.getString("S_INS_TYPE");
                    String appData = rs.getString("S_APP_NAME") == null ? "" : rs.getString("S_APP_NAME");
                    String mid = rs.getString("S_MERCHANT_ID");
                    if (!rs.getString("D_DATE").equals(date) || !cardTypeData.equals(cardType) || !mid.equals(merchantId) || !appData.equals(appName)) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MERCHANT_NAME", merchantName);
                        objectData.put("D_DATE", date);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_APP_NAME", appName);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("S_CLIENT_ID", clientId);
                        reports.add(bindReport(objectData));
                        objectData = new JsonObject();
                    }
                }

                date = rs.getString("D_DATE");
                String cardTypeData = rs.getString("S_INS_TYPE");
                cardType = cardTypeData;
                merchantId = rs.getString("S_MERCHANT_ID");
                merchantName = rs.getString("S_MERCHANT_NAME");
                appName = rs.getString("S_APP_NAME");
                clientId = rs.getString("S_CLIENT_ID");
                if (rs.getString("S_TRANSACTION_TYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", rs.getInt("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", rs.getDouble("N_AMOUNT"));
                }
                if (!rs.next()) {
                    recordsAvailable = false;
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MERCHANT_NAME", merchantName);
                    objectData.put("D_DATE", date);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_APP_NAME", appName);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("S_CLIENT_ID", clientId);
                    reports.add(bindReport(objectData));
                    objectData = new JsonObject();
                }
            }
        }catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (rs != null) {
                rs.close();
            }
        }

        return reports;
    }

    private static MpayReport bindReport(JsonObject rs) {
        MpayReport report = new MpayReport();
        report.setCardType(rs.getString("S_CARD_TYPE"));

        report.setAppName(rs.getString("S_APP_NAME"));

        report.setReportDate(Timestamp.valueOf(rs.getString("D_DATE")));
//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
//
//        try {
//            domesticReport.setReport_date( formatter.parse(rs.getString("TXN_DATE")));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        report.setMerchantId(rs.getString("S_MERCHANT_ID"));
        report.setMerchantName(rs.getString("S_MERCHANT_NAME"));
        report.setRefundCount(rs.getInteger("REFUND_COUNT"));
        report.setTransactionCount(rs.getInteger("N_COUNT_TRAN"));
        report.setTransactionTotal(rs.getDouble("N_TOTAL_AMOUNT"));
        report.setRefundTotal(rs.getDouble("REFUND_AMOUNT"));
        report.setClient(rs.getString("S_CLIENT_ID"));
        return report;
    }

}
