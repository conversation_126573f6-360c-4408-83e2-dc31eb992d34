package com.onepay.ma.service.service.refundCapture;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.google.gson.Gson;
import com.onepay.ma.service.handler.externalClient.MSPClient;
import com.onepay.ma.service.handler.externalClient.OneSchedClient;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.models.InternationalTransactionHistory;
import com.onepay.ma.service.models.MerchantData;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.RefundConfig;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.base.PatchRequest;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.RefundApprovalService;
import com.onepay.ma.service.service.UserService;
import com.onepay.ma.service.service.impl.MerchantApprovalService;
import com.onepay.ma.service.service.lock.LockService;
import com.onepay.ma.service.util.ErrorException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.SQLConnectionFactory;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.util.Utils;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.rxjava.core.http.HttpClient;
import io.vertx.rxjava.ext.web.RoutingContext;
import oracle.jdbc.OracleTypes;
import rx.Observable;

public class RefundCaptureService {

    private static final Logger LOGGER = Logger.getLogger(RefundCaptureService.class.getName());

    private UserService userService;

    private LockService lockService;

    private RefundConfig refundConfig;

    private InternationalTransactionService internationalTransactionService;

    private MerchantService merchantService;

    private MerchantApprovalService merchantApproval;

    private RefundApprovalService refundApprovalService;

    private InternationalRefundService internationalRefundService;

    private SQLConnection onlineCon;

    private SQLConnection backUpCon;

    private final static Gson gson = new Gson();
    private static final String X_SECURE_HASH = "X-Secure-Hash";
    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_REFUND_APPROVE.get_approval_history(?,?,?,?,?)}";
    private static final String LIST_CAPTURE_TRANSACTION_HISTORY = "{call PKG_ONECREDIT.SEARCH_CAPTURE_TXN_HIS_2(?,?,?,?,?)}";
    private static final String CAPTURE_TRANSACTION_BY_MERCHANT_REF = "{call PKG_ONECREDIT.search_refund_capture_by_ref(?,?,?,?,?,?)}";
    private static final String SAMSUNG_MERCHANT_APPROVALS = PropsUtil.get("samsung.merchant.approvals", "");

    public RefundCaptureService(MerchantApprovalService merchantApproval,
            RefundApprovalService refundApprovalService, UserService userService, RefundConfig refundConfig,
            InternationalTransactionService internationalTransactionService,
            InternationalRefundService internationalRefundService, MerchantService merchantService) {
        this.refundConfig = refundConfig;
        this.internationalTransactionService = internationalTransactionService;
        this.merchantService = merchantService;
        this.userService = userService;
        this.merchantApproval = merchantApproval;
        this.refundApprovalService = refundApprovalService;
        this.internationalRefundService = internationalRefundService;

    }

    private Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> getMerchantApproval(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                return this.internationalTransactionService.get(connOnline, transactionId).flatMap(interTransaction -> {
                                    return this.internationalTransactionService.getContarctTypeMsp(connOnline, transactionId).flatMap(contractType -> {
                                        return this.merchantApproval.getMerchantApproval(connBackUp, interTransaction.getMerchant_id()).map(merchantId -> {
                                            RefundData.NUMBER_OF_STEP_CONFIRMATION result = RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            // 0 : chi merchant duyet, 1: merchant + OP duyet, 2 OP duyet
                                            if (Utils.isBigMerchant(interTransaction.getMerchant_id())) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            }
                                            if (null != contractType && "3B".equalsIgnoreCase(contractType)) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                            } else if (null != contractType && contractType.equalsIgnoreCase("2B")) {
                                                if (null != merchantId && !merchantId.isEmpty()) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE;
                                                } else  {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                }
                                            }
                                           
                                            int acquirerId = interTransaction.getAcquirer().getAcquirer_id();
                                            if (8 == acquirerId) {
                                                // hotfix special case for samsung
                                                List<String> samsungMerchantApprovals = Arrays.asList(SAMSUNG_MERCHANT_APPROVALS.split(","));
                                                if (samsungMerchantApprovals.contains(interTransaction.getMerchant_id())) {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                                } else {
                                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                                }
                                            } else if (11 == acquirerId || 7 == acquirerId || 9 == acquirerId || 3 == acquirerId) {
                                                // acq =Sacombank. vpb onepay phai duyet
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            if (interTransaction.getMerchant_id().startsWith("OP_") && !interTransaction.getMerchant_id().equals("OP_PREPAID")) {
                                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                            }

                                            return result;
                                        });
                                    });
                                });
                            });
                });
    }

    public void refundCapture(RoutingContext rc, Observable<Object> checkFlag, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, PatchRequest<RefundData> refundData,
            String userId, Boolean skipCallSynchronize) {

        checkFlag.subscribe(internationalRefund -> {
            if (internationalRefund != null) {
                rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalRefund);
                rc.next();

            } else {

                Observable<Double> obsAmountRefund = getAmountRefundCapture(transactionId, clientReadOnly,
                        clientOnline, clientBackUp, refundData);
                // begin refund data
                obsAmountRefund.subscribe(amount -> {
                    Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> flagInsert = getMerchantApproval(clientOnline, clientBackUp, transactionId);
                    flagInsert.subscribe(flag -> {
                        /**
                         * flag = 0: duyet luon flag = 1: duyet lan 1 boi merchant admin flag = 2: duyet
                         * lan 2 onepay admin
                         */
                        if (flag == RefundData.NUMBER_OF_STEP_CONFIRMATION.ONE) {
                            LOGGER.log(Level.INFO, "****** INSERT REFUND APPROVAL *****");
                            Observable
                                    .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                            return this.refundApprovalService.insertRequest3(connBackUp, userId,
                                                    refundData.getValue().getMerchant_id(), transactionId,
                                                    refundData.getValue().getAmount(),
                                                    refundData.getValue().getCurrency(), refundData.getValue().getRefund_reference(),
                                                    RefundData.Type.REFUND_CAPTURE.getValue(),
                                                    refundData.getValue().getNote()).flatMap(approval -> {
                                                        return Observable.just(approval);
                                                    });
                                        });
                                    }).subscribe(stringObjectMap -> {
                                        // call ep dong bo international
                                        if (skipCallSynchronize == false) {
                                            try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                            } catch (Exception e) {
                                                LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                                            }
                                        }
                                        unlock(transactionId, clientBackUp);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                        } else if (flag == RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO) {
                            LOGGER.log(Level.INFO, "****** INSERT REFUND ONEPAY APPROVAL *****");
                            Observable
                                    .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                    .flatMap(connBackUp -> {
                                        return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                            return this.refundApprovalService.insertRequestOnepay3(connBackUp,
                                                    userId, refundData.getValue().getMerchant_id(), transactionId,
                                                    refundData.getValue().getAmount(),
                                                    refundData.getValue().getCurrency(), null, refundData.getValue().getRefund_reference(),
                                                    RefundApproval.Status.REQUEST_ONEPAY.code,
                                                    RefundData.Type.REFUND_CAPTURE.getValue(),
                                                    refundData.getValue().getNote()).flatMap(res -> {
                                                        return Observable.just(res);
                                                    });
                                        });
                                    }).subscribe(stringObjectMap -> {
                                        // call ep dong bo international
                                        if (skipCallSynchronize == false) {
                                            try {
                                                OneSchedClient.synchronizeInterRefundViewSync();
                                            } catch (Exception e) {
                                                    LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                                            }
                                        }
                                        unlock(transactionId, clientBackUp);
                                        rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                        rc.next();
                                    }, throwable -> {
                                        rc.fail(throwable);
                                    });
                        } else {
                            Observable<MerchantData> merchantObs = getMerchantObs(transactionId, clientReadOnly,
                                    clientOnline, clientBackUp, refundData);

                            merchantObs.subscribe(merchantData -> {
                                Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline),
                                        f -> f.dispose()).subscribe(connOnline -> {
                                            if (merchantData == null) {
                                                LOGGER.log(Level.SEVERE,
                                                        "[ INTERNATIONAL REFUND TRANSACTION PATCH] => INVALID MERCHANT ");
                                                throw IErrors.VALIDATION_ERROR;
                                            }
                                            this.internationalTransactionService.get(connOnline, transactionId)
                                                    .subscribe(captureTransaction -> {
                                                        JsonObject res = null;
                                                        try {
                                                            res = MSPClient.refundCapture(refundData.getValue().getMerchant_id(),
                                                                    captureTransaction.getTransaction_reference(), refundData.getValue().getRefund_reference(),
                                                                    refundData.getValue().getAmount(), merchantData.getAccessCode(), merchantData.getHashCode(),
                                                                    userId, refundData.getValue().getNote());
                                                                    
                                                            if (skipCallSynchronize == false) {
                                                                OneSchedClient.synchronizeInterRefundViewSync();
                                                            }
                                                        } catch (Exception e) {
                                                            rc.fail(e);
                                                        }
                                                        clientRequestToObs(rc, transactionId, clientOnline,
                                                                clientBackUp, res, refundData);

                                                    });
                                        });

                            }, throwable -> {
                                rc.fail(throwable);
                            });
                        }
                    });
                }, throwable -> {
                    rc.fail(throwable);
                });

            }
        }, throwable -> {
            rc.fail(throwable);
        });
    }

    /**
     * get amount refund capture
     * 
     * <AUTHOR>
     * @param transactionId
     * @param clientReadOnly
     * @param clientOnline
     * @param clientBackUp
     * @param refundData
     */
    private Observable<Double> getAmountRefundCapture(String transactionId, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable
                                        .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return listHistory(connReadOnly, connOnline, connBackup, transactionId)
                                                    .map(internationalTransactionHistories -> {
                                                        double remainAmt = 0;
                                                        double refundAmt = refundData.getValue().getAmount();
                                                        for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                            // Case purchase ->
                                                            if (his.getTransaction_type().toUpperCase()
                                                                    .equals("CAPTURE")) {
                                                                remainAmt += his.getAmount().getTotal();
                                                            } else if ((his.getTransaction_type().equalsIgnoreCase(
                                                                    RefundApproval.Status.REQUEST.content) &&
                                                                    (Integer.valueOf(his
                                                                            .getStatus()) != RefundApproval.Status.REQUEST.code ||
                                                                            Integer.valueOf(his
                                                                                    .getStatus()) != RefundApproval.Status.REQUEST_ONEPAY.code))
                                                                    ||
                                                                    (his.getAdvance_status() != null && (his
                                                                            .getAdvance_status()
                                                                            .equalsIgnoreCase("Rejected") ||
                                                                            his.getAdvance_status().equalsIgnoreCase(
                                                                                    "Merchant Rejected")
                                                                            ||
                                                                            his.getAdvance_status().equalsIgnoreCase(
                                                                                    "OnePAY Rejected")))
                                                                    ||
                                                                    (his.getTransaction_type().equalsIgnoreCase(
                                                                            RefundApproval.Status.APPROVED.content))) {
                                                                // Do nothing
                                                            } else {
                                                                if (his.getAdvance_status()
                                                                        .equalsIgnoreCase("Successful"))
                                                                    remainAmt -= his.getAmount().getTotal();
                                                            }
                                                        }
                                                        String str = String.format("%1.2f", remainAmt);
                                                        remainAmt = Double.valueOf(str);
                                                        if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                            return refundAmt;
                                                        } else {
                                                            LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " +
                                                                    remainAmt + " DESCRIPTION : " + refundAmt);
                                                            throw IErrors.AMOUNT_REFUND_ERROR;
                                                        }
                                                    });
                                        });
                            });
                });
    }

    private Observable<Double> getAmountRefundApproval(String transactionId, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, RefundApproval refundApproval) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                .flatMap(connReadOnly -> {
                    // get online connection
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                            .flatMap(connOnline -> {
                                // get back up connection
                                return Observable
                                        .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return listHistory(connReadOnly, connOnline, connBackup, transactionId)
                                                    .map(internationalTransactionHistories -> {
                                                        double remainAmt = 0;
                                                        double refundAmt = refundApproval.getAmount().getTotal();
                                                        for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                            // Case purchase ->
                                                            if (his.getTransaction_type().toUpperCase()
                                                                    .equals("CAPTURE")) {
                                                                remainAmt += his.getAmount().getTotal();
                                                            } else if ((his.getTransaction_type().equalsIgnoreCase(
                                                                    RefundApproval.Status.REQUEST.content) &&
                                                                    Integer.valueOf(his
                                                                            .getStatus()) != RefundApproval.Status.REQUEST.code)
                                                                    ||
                                                                    (his.getAdvance_status() != null && (his
                                                                            .getAdvance_status()
                                                                            .equalsIgnoreCase("Rejected") ||
                                                                            his.getAdvance_status().equalsIgnoreCase(
                                                                                    "Merchant Rejected")
                                                                            ||
                                                                            his.getAdvance_status().equalsIgnoreCase(
                                                                                    "OnePAY Rejected")))
                                                                    ||
                                                                    (his.getTransaction_type().equalsIgnoreCase(
                                                                            RefundApproval.Status.APPROVED.content))) {
                                                                // Do nothing
                                                            } else {
                                                                if (his.getAdvance_status()
                                                                        .equalsIgnoreCase("Successful"))
                                                                    remainAmt -= his.getAmount().getTotal();
                                                            }
                                                        }
                                                        String str = String.format("%1.2f", remainAmt);
                                                        remainAmt = Double.valueOf(str);
                                                        if (refundAmt <= remainAmt && remainAmt > 0 && refundAmt > 0) {
                                                            return refundAmt;
                                                        } else {
                                                            LOGGER.log(Level.SEVERE, "[ AMOUNT REFUND  ] =>  REMAIN : " +
                                                                    remainAmt + " DESCRIPTION : " + refundAmt);
                                                            throw IErrors.AMOUNT_REFUND_ERROR;
                                                        }
                                                    });
                                        });
                            });
                });
    }

    public Observable<MerchantData> getMerchantObs(String transactionId, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, PatchRequest<RefundData> refundData) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(connOnline -> {
                    return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose())
                            .flatMap(connReadOnly -> {
                                return Observable
                                        .using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                        .flatMap(connBackup -> {
                                            return listHistory(connReadOnly, connOnline, connBackup, transactionId)
                                                    .flatMap(internationalTransactionHistories -> {
                                                        double remainAmt = 0;
                                                        double refundAmt = refundData.getValue().getAmount();
                                                        for (InternationalTransactionHistory his : internationalTransactionHistories) {
                                                            if (his.getTransaction_type().toUpperCase()
                                                                    .equals("CAPTURE")) {
                                                                remainAmt = his.getAmount().getTotal();
                                                            } else {
                                                                remainAmt -= his.getAmount().getTotal();
                                                            }
                                                        }
                                                        if (refundAmt <= remainAmt && remainAmt > 0) {
                                                            return merchantService.getData(connOnline,
                                                                    refundData.getValue().getMerchant_id());
                                                        } else {
                                                            LOGGER.log(Level.SEVERE,
                                                                    "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REMAIN : " +
                                                                            remainAmt + " REFUND : " + refundAmt);
                                                            throw IErrors.AMOUNT_REFUND_ERROR;
                                                        }
                                                    });
                                        });
                            });
                });
    }

    public Observable<MerchantData> getMerchantObsApproval(JDBCClient clientReadOnly, JDBCClient clientOnline,
            JDBCClient clientBackUp, RefundApproval approval) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f -> f.dispose()).flatMap(connReadOnly -> {
                // get back up connection
                return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackup -> {
                    return listHistory(connReadOnly, connOnline, connBackup, approval.getOriginal_transaction_id().toString()).flatMap(internationalTransactionHistories -> {
                        double remainAmt = 0;
                        double refundAmt = approval.getAmount().getTotal();
                        for (InternationalTransactionHistory his : internationalTransactionHistories) {
                            if (his.getTransaction_type().toUpperCase()
                                    .equals("CAPTURE")) {
                                remainAmt = his.getAmount().getTotal();
                            } else {
                                remainAmt -= his.getAmount().getTotal();
                            }
                        }
                        if (refundAmt <= remainAmt && remainAmt > 0) {
                            return merchantService.getData(connOnline, approval.getMerchant_id());
                        } else {

                            LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REMAIN : " + remainAmt + " REFUND : " + refundAmt);
                            this.refundApprovalService.updateErrorRefundStatus(connBackup, approval.getTransaction_id(), 400, "Amount Refund Error");
                            throw new ErrorException(500, "Amount Refund Error", 400 + "|" + "Amount Refund Error", "Refund Failed", "Refund Failed");

                        }
                    });
                });
            });
        });
    }

    public void clientRequestToObs(RoutingContext rc, String transactionId, JDBCClient clientOnline,
            JDBCClient clientBackUp, JsonObject dataResp, PatchRequest<RefundData> refundData) {
        try {
            if (dataResp != null) {
                // Map dataResp = (Map) mapVpc.get("vpc_response");
                if (dataResp.getString("vpc_TxnResponseCode") != null && "0".equals(dataResp.getString("vpc_TxnResponseCode") + "")) {
                    Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                            return getHistoryByMerchantRef(connOnline, transactionId, refundData.getValue().getMerchant_id(), dataResp.getString("vpc_MerchTxnRef").toString()).flatMap(internationalTransactionHistory -> {
                                return userService.get(connBackUp1, internationalTransactionHistory.getOperator_id()).map(userData -> {
                                    if (userData != null) {
                                        internationalTransactionHistory.setOperator_id(userData.getEmail());
                                    }
                                    return internationalTransactionHistory;
                                });

                            });
                        });
                    });

                    returnObs.subscribe(internationalTransactionHistory -> {
                        unlock(transactionId, clientBackUp);
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                        rc.next();
                    }, rc::fail);
                } else {
                    LOGGER.log(Level.SEVERE, "Refund transaction failed ", Utils.mask(dataResp) );
                    LOGGER.log(Level.INFO,
                            "===================================== END REFUND ==================================" +
                                    StringPool.NEW_LINE);
                    if (dataResp.getString("vpc_TxnResponseCode") != null) {
                        LOGGER.log(Level.SEVERE, "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED ");
                        throw IErrors.REFUND_FAILED;
                    } else {
                        LOGGER.log(Level.SEVERE,
                                "[ INTERNATIONAL REFUND CAPTURE PATCH] => REFUND FAILED (ONECREDIT STATUS EMPTY)");
                        // LOGGER.log(Level.SEVERE, dataResp.encodePrettily());
                        throw IErrors.REFUND_FAILED;
                    }
                }

            } else {
                LOGGER.log(Level.SEVERE,
                        "[ INTERNATIONAL REFUND CAPTURE PATCH] => REFUND FAILED " + Utils.mask(dataResp));
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception ex) {
            rc.fail(ex);
        }
    }

    public static Observable<List<InternationalTransactionHistory>> listHistory(SQLConnection connReadOnly,
            SQLConnection connOnline, SQLConnection connBackup, String transactionId) {

        return getListCaptureTransactionHistory(connOnline, transactionId, -1).flatMap(internationalTransactions -> {
            return getListCaptureTransactionHistory(connReadOnly, transactionId, internationalTransactions.size())
                    .flatMap(internationalTransactionsReadOnly -> {

                        return getListApprovalHistoryBackup(connBackup, transactionId).flatMap(approvalHistories -> {

                            List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
                            transactionsFinal.addAll(internationalTransactions);
                            transactionsFinal.addAll(internationalTransactionsReadOnly);
                            transactionsFinal.addAll(approvalHistories);

                            // sort by trans time.
                            transactionsFinal
                                    .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

                            return Observable.just(transactionsFinal);

                        });
                    });

        });
    }

    private static Observable<List<InternationalTransactionHistory>> getListApprovalHistoryBackup(
            SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray().add(transactionId).add(1);
        JsonArray outParams = new JsonArray().addNull().addNull().add(OracleTypes.CURSOR).add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_REFUND_APPROVAL_HISTORY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE,
                        "[LIST International APPROVAL HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<InternationalTransactionHistory> childList = new ArrayList<>();
            List<InternationalTransactionHistory> parentList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalTransactionHistory transaction = bindApprovalHistory(jsonObject);

                if (transaction.getParent_id() == null) {
                    parentList.add(transaction);
                } else {
                    childList.add(transaction);
                }
            }

            Map<Integer, List<InternationalTransactionHistory>> childMap = childList.stream()
                    .collect(Collectors.groupingBy(InternationalTransactionHistory::getParent_id));

            return parentList.stream().map(parent -> {
                InternationalTransactionHistory re = new InternationalTransactionHistory(parent);

                if (childMap.get(parent.getTransaction_id()) != null) {
                    re.setSubHistories(childMap.get(parent.getTransaction_id()));
                } else {
                    re.setSubHistories(new ArrayList<>());
                }
                re.getSubHistories().add(parent);
                re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                return re;
            }).sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                    .collect(Collectors.toList());
        });
    }

    public static Observable<List<InternationalTransactionHistory>> getListCaptureTransactionHistory(
            SQLConnection connReadOnly, String transactionId, int row) {
        JsonArray inParams = new JsonArray().add(row).add(transactionId);
        JsonArray outParams = new JsonArray().addNull().addNull().add(OracleTypes.CURSOR).add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_CAPTURE_TRANSACTION_HISTORY, inParams, outParams)
                .map(result -> {
                    Integer reusltCode = result.getOutput().getInteger(3);

                    if (reusltCode != 200) {
                        LOGGER.log(Level.SEVERE,
                                "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(4));
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    List<InternationalTransactionHistory> transactionList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(2).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    for (JsonObject jsonObject : rs.getRows()) {
                        InternationalTransactionHistory transaction = bindTransactionHistory(jsonObject);
                        transactionList.add(transaction);
                    }

                    return transactionList;

                });
    }

    public static InternationalTransactionHistory bindApprovalHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_ID");
        int originalId = Integer.valueOf(rs.getString("N_TRANS_REF_ID"));
        String currency = rs.getString("S_CURRENCY");
        Integer status = rs.getInteger("N_STATUS");
        String operator = rs.getString("S_OPERATOR_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANS_REF");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(rs.getString("D_CREATE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");

        InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
        internationalTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        internationalTransactionHistory.setOriginal_transaction_id(originalId);
        internationalTransactionHistory.setStatus(status.toString());
        internationalTransactionHistory.setOperator_id(operator);
        internationalTransactionHistory.setTransaction_id(transactionId);
        internationalTransactionHistory.setTransaction_type(transactionType);
        internationalTransactionHistory.setTransaction_time(date);
        internationalTransactionHistory.setMerchant_id(merchantId);
        internationalTransactionHistory.setAdvance_status(advanceStatus != null ? advanceStatus : StringPool.BLANK);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        internationalTransactionHistory.setAmount(amountData);
        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        internationalTransactionHistory.setNote(data.getString("note"));

        return internationalTransactionHistory;
    }

    public static InternationalTransactionHistory bindTransactionHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_ID");
        String currency = rs.getString("S_CURRENCY");
        String status = rs.getString("S_STATUS");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        String operator = rs.getString("S_OPERATOR_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String financialTransactionId = rs.getString("S_FINANCIAL_TRANSACTION_ID");
        String referenceNumber = rs.getString("S_REFERENCE_NUMBER");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = java.sql.Timestamp.valueOf(rs.getString("D_DATE"));

        InternationalTransactionHistory transactionHistory = new InternationalTransactionHistory();
        transactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        transactionHistory.setStatus(status);
        transactionHistory.setOperator_id(operator);
        transactionHistory.setOriginal_transaction_id(originalId);
        transactionHistory.setMerchant_id(merchantId);
        transactionHistory.setResponse_code(responseCode);
        transactionHistory.setTransaction_id(transactionId);
        transactionHistory.setTransaction_type(transactionType);
        transactionHistory.setTransaction_time(date);
        transactionHistory.setAdvance_status(advanceStatus);
        transactionHistory.setReference_number(referenceNumber);
        transactionHistory.setFinancial_transaction_id(financialTransactionId);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        transactionHistory.setAmount(amountData);

        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        transactionHistory.setNote(data.getString("note"));

        return transactionHistory;
    }

    public void refundCaptureOnePAYManual(RoutingContext rc, String body, JDBCClient clientReadOnly,
            JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, String xRequestId, String userId,
            HttpClient httpClient, Observable<RefundApproval> obsBackUp) {
        obsBackUp.subscribe(refundApproval -> {
            Observable<Double> obsAmountRefund = getAmountRefundApproval(refundApproval.getOriginal_transaction_id().toString(), clientReadOnly, clientOnline, clientBackUp, refundApproval);
            // begin refund data
            obsAmountRefund.subscribe(amount -> {
                Observable<MerchantData> merchantObs = getMerchantObsApproval(clientReadOnly, clientOnline, clientBackUp, refundApproval);

                merchantObs.subscribe(merchantData -> {
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                        backUpCon = connBackUp1;
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
                            onlineCon = connOnline;
                            return onlineCon.setAutoCommitObservable(false).flatMap(aVoid -> {

                                return this.internationalTransactionService.get(onlineCon, refundApproval.getOriginal_transaction_id().toString()).flatMap(internationalTransaction -> {
                                    String requestId = xRequestId;
                                    String refundReference = refundApproval.getMerchant_id() +
                                            "_" + requestId;
                                    if (refundReference.length() > 32) {
                                        int largerInt = refundReference.length() - 32 + 1;
                                        requestId = requestId.substring(0,
                                                requestId.length() - largerInt);
                                        refundReference = refundApproval.getMerchant_id() + "_" +
                                                requestId;
                                    }
                                    final String transRef = refundReference;
                                    return this.internationalTransactionService.insertOnePAYManual(onlineCon, Integer.valueOf(refundApproval.getOriginal_transaction_id()), amount, refundReference, internationalTransaction.getMerchant_id()).flatMap(integer -> {

                                        return getHistoryByMerchantRef(onlineCon, refundApproval.getOriginal_transaction_id().toString(), internationalTransaction.getMerchant_id(), transRef).flatMap(internationalTransactionHistory -> {
                                            return userService.get(backUpCon, internationalTransactionHistory.getOperator_id()).flatMap(userData -> {
                                                return this.refundApprovalService.insertApprove(connBackUp1, userId, refundApproval.getMerchant_id(), refundApproval.getOriginal_transaction_id(),
                                                        refundApproval.getAmount().getTotal(), refundApproval.getAmount().getCurrency(), Integer.valueOf(transactionId),
                                                        refundApproval.getMerchant_transaction_ref(), 400, RefundData.Type.REFUND_CAPTURE.getValue()).flatMap(approval1 -> {
                                                            return this.internationalRefundService.get(connOnline, backUpCon, refundApproval.getTransaction_id().toString()).map(InternationalRefund -> {

                                                                if (userData != null) {
                                                                    InternationalRefund.setOperator(userData.getEmail());
                                                                }
                                                                return internationalTransactionHistory;
                                                            });
                                                        });
                                            });
                                        });
                                    });
                                });
                            });
                        });
                    }).subscribe(internationalTransactionHistory -> {
                        if (onlineCon != null) {
                            onlineCon.commitObservable();
                        }
                        if (backUpCon != null) {
                            backUpCon.commitObservable();
                        }
                        try {
                            OneSchedClient.synchronizeInterRefundViewSync();
                        } catch (Exception e) {
                            LOGGER.log(Level.INFO, "international call ep dong bo error " + e.getMessage());
                        }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                        rc.next();
                    }, throwable -> {
                        if (onlineCon != null) {
                            onlineCon.rollbackObservable();
                        }
                        if (backUpCon != null) {
                            backUpCon.rollbackObservable();
                        }
                        rc.fail(throwable);
                    });

                }, throwable -> {
                    rc.fail(throwable);
                });
            }, throwable -> {
                rc.fail(throwable);
            });
        });

    }

    /**
     * get history by merchant ref
     *
     * @param sqlOnline
     * @param transactionId
     * @return
     */
    private Observable<InternationalTransactionHistory> getHistoryByMerchantRef(SQLConnection sqlOnline,
            String transactionId, String merchantId, String transactionReference) {
        JsonArray inParams = new JsonArray().add(transactionId).add(transactionReference).add(merchantId);
        JsonArray outParams = new JsonArray().addNull().addNull().addNull().add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER).add(OracleTypes.VARCHAR);
        return sqlOnline.callWithParamsObservable2(CAPTURE_TRANSACTION_BY_MERCHANT_REF, inParams, outParams)
                .map(result -> {
                    InternationalTransactionHistory internationalTransactionHistory = null;

                    Integer reusltCode = result.getOutput().getInteger(4);

                    if (reusltCode != 200) {
                        LOGGER.log(Level.SEVERE,
                                "[GET INTERNATIONAL TRANSACTION HISTORY ERROR]: " + result.getOutput().getString(5));
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    Map map = result.getOutput().getJsonObject(3).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() <= 0) {
                        LOGGER.info("RefundCaptureService.getHistoryByMerchantRef rs.getRows().size(): " +
                                rs.getRows().size());
                        return null;
                    }
                    JsonObject jsonObject = rs.getRows().get(0);
                    internationalTransactionHistory = bindTransactionHistory(jsonObject);
                    return internationalTransactionHistory;

                });
    }


    private Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> getConfirmStep(JDBCClient clientOnline, JDBCClient clientBackUp, String transactionId, RefundApproval approval) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline -> {
            return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp -> {
                return this.internationalTransactionService.get(connOnline, transactionId).flatMap(interTransaction -> {
                    return this.internationalTransactionService.getContarctTypeMsp(connOnline, transactionId).map(contractType -> {

                            // RefundData.NUMBER_OF_STEP_CONFIRMATION result = RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                            if (approval.getStatus().equals(RefundApproval.Status.REQUEST_ONEPAY.code)) {
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                            }

                            if (Utils.isBigMerchant(interTransaction.getMerchant_id())) {
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                            }
                            
                            if (contractType != null && contractType.equalsIgnoreCase("2B")) {
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                            }
                            if (contractType != null && contractType.equalsIgnoreCase("3B")) {
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                            }
                            int acquirerId = interTransaction.getAcquirer().getAcquirer_id();
                            if (8 == acquirerId) {
                                // hotfix special case for samsung
                                List<String> samsungMerchantApprovals = Arrays.asList(SAMSUNG_MERCHANT_APPROVALS.split(","));
                                if (samsungMerchantApprovals.contains(interTransaction.getMerchant_id())) {
                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                                } else {
                                    return RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO;
                                }
                            }
                            if ((11 == acquirerId || 7 == acquirerId || 9 == acquirerId || 3 == acquirerId)) {
                                // acq =Sacombank. vpb onepay phai duyet
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                            }

                            if (interTransaction.getMerchant_id().startsWith("OP_") && !interTransaction.getMerchant_id().equals("OP_PREPAID")) {
                                return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                            }

                            return RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO;
                    });
                });
            });
        });
    }

    public void approve(RoutingContext rc, JDBCClient clientReadOnly, JDBCClient clientOnline, JDBCClient clientBackUp,
            String transactionId, String xRequestId, String userId, HttpClient httpClient,
            Observable<RefundApproval> obsBackUp, Boolean skipCallSynchronize) {
        obsBackUp.subscribe(approval -> {
            Observable<RefundData.NUMBER_OF_STEP_CONFIRMATION> doubleConfirmationObs = getConfirmStep(clientOnline,clientBackUp, approval.getOriginal_transaction_id(), approval);
            doubleConfirmationObs.subscribe(isDoubleConfirmation -> {
                if (isDoubleConfirmation == RefundData.NUMBER_OF_STEP_CONFIRMATION.TWO) {
                    // Insert request to onepay
                    LOGGER.log(Level.INFO, "****** INSERT REFUND REQUEST TO ONEPAY *****");
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return connBackUp.setAutoCommitObservable(true).flatMap(aVoid -> {
                                    // Insert new request refund for OnePAY Approval
                                    String note = new JsonObject(approval.getData()).getString("note");
                                    return this.refundApprovalService.insertRequestOnepay3(connBackUp, userId,
                                            approval.getMerchant_id(), approval.getOriginal_transaction_id(),
                                            approval.getAmount().getTotal(), approval.getAmount().getCurrency(),
                                            approval.getTransaction_id(), approval.getMerchant_transaction_ref(),
                                            RefundApproval.Status.REQUEST_ONEPAY.code, approval.getN_type(), note)
                                            .flatMap(res -> {
                                                return Observable.just(res);
                                            });
                                });
                            }).subscribe(stringObjectMap -> {
                                
                                // call ep dong bo international
                                if (skipCallSynchronize == false) {
                                    // call ep dong bo international
                                    try {
                                            OneSchedClient.synchronizeInterRefundViewSync();
                                    } catch (Exception e) {
                                            LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                    }
                            }
                                rc.put(ParamsPool.HANDLER_DATA_RESULT, stringObjectMap);
                                rc.next();
                            }, throwable -> {
                                rc.fail(throwable);
                            });
                } else if (isDoubleConfirmation == RefundData.NUMBER_OF_STEP_CONFIRMATION.ZERO) {
                    Observable<MerchantData> merchantObs = getMerchantObsApproval(clientReadOnly, clientOnline,
                            clientBackUp, approval);

                    merchantObs.subscribe(merchantData -> {
                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                                .flatMap(connOnline -> {
                                    if (merchantData == null) {
                                        LOGGER.log(Level.SEVERE,
                                                "[ INTERNATIONAL REFUND TRANSACTION PATCH] => INVALID MERCHANT ");
                                        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp),
                                                f -> f.dispose()).flatMap(connBackUp -> {
                                                    return this.refundApprovalService.updateErrorRefundStatus(
                                                            connBackUp, Integer.parseInt(transactionId), 500,
                                                            "There was a validation issue with your request");
                                                }).subscribe();
                                        throw IErrors.VALIDATION_ERROR;
                                    }
                                    // if exist fail record -> up date ref
                                    return updateTXNExtend(connOnline, approval, xRequestId);
                                }).subscribe(internationalTransaction -> {

                                    // get origin transaction info
                                    getTransactionByID(clientOnline, approval.getOriginal_transaction_id().toString(),
                                            this.internationalTransactionService).subscribe(captureTransaction -> {
                                                JsonObject res = null;
                                                try {
                                                    res = MSPClient.refundCapture(approval.getMerchant_id(),
                                                            captureTransaction.getOcMerTxnRef(), approval.getMerchant_transaction_ref(),
                                                            approval.getAmount().getTotal(), merchantData.getAccessCode(), merchantData.getHashCode(),
                                                            userId, "");
                                                } catch (Exception e) {
                                                    rc.fail(e);
                                                }
                                                clientApproveRequestToObs(rc, userId, transactionId, clientOnline,
                                                        clientBackUp, res, approval, skipCallSynchronize);
                                            });
                                });
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    LOGGER.log(Level.SEVERE, "[ International REFUND  APPROVAL] => REFUND APPROVAL DONE ALREADY");
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                        Integer.parseInt(transactionId), 500,
                                        "This approval has already approved|rejected");
                            }).subscribe();
                    throw new ErrorException(500, "DUPLICATE_REFUND_ERROR",
                            500 + "|" + "This approval has already approved|rejected", "",
                            "This approval has already approved|rejected");
                }
            });

        });

    }

    private void clientApproveRequestToObs(RoutingContext rc, String userId, String transactionId,
            JDBCClient clientOnline, JDBCClient clientBackUp, JsonObject dataResp,
            RefundApproval approval, Boolean skipCallSynchronize) {
        try {
            if (dataResp != null) {
                // Map dataResp = (Map) mapVpc.getString("vpc_response");
                if (dataResp.getString("vpc_TxnResponseCode") != null &&
                        "0".equals(dataResp.getString("vpc_TxnResponseCode") + "")) {
                    Observable<InternationalTransactionHistory> returnObs = Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose()).flatMap(connOnline1 -> {
                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose()).flatMap(connBackUp1 -> {
                            return getHistoryByMerchantRef(connOnline1, approval.getOriginal_transaction_id().toString(), approval.getMerchant_id(), dataResp.getString("vpc_MerchTxnRef").toString())
                                    .flatMap(internationalTransactionHistory -> {
                                        return this.refundApprovalService.insertApprove(connBackUp1, userId, approval.getMerchant_id(), approval.getOriginal_transaction_id(), approval.getAmount().getTotal(),
                                                approval.getAmount().getCurrency(), Integer.valueOf(transactionId), approval.getMerchant_transaction_ref(),
                                                400, RefundData.Type.REFUND_CAPTURE.getValue())
                                                .flatMap(approval1 -> {
                                                    if (approval1.getStatus() != 400 && approval1.getStatus() != 300) {
                                                        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                                                                .flatMap(
                                                                        connBackUp -> {
                                                                            return this.refundApprovalService.updateErrorRefundStatus(connBackUp, Integer.parseInt(transactionId), 500, "").flatMap(a -> {
                                                                                throw IErrors.REFUND_FAILED;
                                                                            });
                                                                        });
                                                    }
                                                    LOGGER.info(
                                                            "DuongPXT LOG RefundCaptureService clientApproveRequestToObs: ");
                                                    // LOGGER.info("DuongPXT
                                                    // LOG
                                                    // RefundCaptureService
                                                    // clientApproveRequestToObs:
                                                    // "+internationalTransactionHistory.getOperator_id());
                                                    LOGGER.info(
                                                            "DuongPXT LOG RefundCaptureService clientApproveRequestToObs: " +
                                                                    connBackUp1);
                                                    // if
                                                    // (Objects.isNull(internationalTransactionHistory))
                                                    // return
                                                    // internationalTransactionHistory;
                                                    return userService
                                                            .get(connBackUp1,
                                                                    internationalTransactionHistory
                                                                            .getOperator_id())
                                                            .map(userData -> {
                                                                if (userData != null) {
                                                                    internationalTransactionHistory
                                                                            .setOperator_id(
                                                                                    userData.getEmail());
                                                                }
                                                                return internationalTransactionHistory;
                                                            });
                                                });
                                    });
                            // });

                        });
                    });
                    returnObs.subscribe(internationalTransactionHistory -> {
                        // call ep dong bo international
                        if (skipCallSynchronize == false) {
                            // call ep dong bo international
                            try {
                                    OneSchedClient.synchronizeInterRefundViewSync();
                            } catch (Exception e) {
                                    LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                            }
                    }
                        rc.put(ParamsPool.HANDLER_DATA_RESULT, internationalTransactionHistory);
                        rc.next();
                    }, throwable -> {
                        rc.fail(throwable);
                    });
                } else {
                    LOGGER.log(Level.SEVERE, "Refund transaction failed ", dataResp == null ? "" : Utils.mask(dataResp));
                    LOGGER.log(Level.INFO,
                            "===================================== END REFUND ==================================" +
                                    StringPool.NEW_LINE);
                    Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                            .flatMap(connBackUp -> {
                                return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                        Integer.parseInt(transactionId), 500, "Error");
                            }).subscribe(a -> {
                                
                                // call ep dong bo international
                                
                                if (skipCallSynchronize == false) {
                                    // call ep dong bo international
                                    try {
                                            OneSchedClient.synchronizeInterRefundViewSync();
                                    } catch (Exception e) {
                                            LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                    }
                            }
                                rc.fail(IErrors.REFUND_FAILED);
                            });
                }

            } else {
                LOGGER.log(Level.SEVERE,
                        "[ INTERNATIONAL REFUND TRANSACTION PATCH] => REFUND FAILED " + Utils.mask(dataResp));
                Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                        .flatMap(connBackUp -> {
                            return this.refundApprovalService.updateErrorRefundStatus(connBackUp,
                                    Integer.parseInt(transactionId), 500, "Error");
                        }).subscribe(a -> {
                            
                            if (skipCallSynchronize == false) {
                                // call ep dong bo international
                                try {
                                        OneSchedClient.synchronizeInterRefundViewSync();
                                } catch (Exception e) {
                                        LOGGER.log(Level.INFO, "international call ep dong bo refund error " + e.getMessage());
                                }
                        }
                            rc.fail(IErrors.REFUND_FAILED);
                        });

                throw new ErrorException(500, "REFUND_FAILED", "500 | Refund Capture Fail", "Refund Failed",
                        "Refund Failed");
            }
        } catch (Exception ex) {
            rc.fail(ex);
        }

    }

    private Observable<InternationalTransaction> getTransactionByID(JDBCClient clientOnline, String transactionId,
            InternationalTransactionService internationalTransactionService) {
        return Observable.using(SQLConnectionFactory::new, f -> f.create(clientOnline), f -> f.dispose())
                .flatMap(conn -> {
                    return internationalTransactionService.get(conn, transactionId);
                });

    }

    private Observable<InternationalTransaction> updateTXNExtend(SQLConnection connOnline, RefundApproval approval, String xRequestId) {
        return this.internationalTransactionService.get(connOnline, approval.getOriginal_transaction_id().toString()).flatMap(internationalTransaction -> {

            // TODO: find and update refund record with refund Merchant_transaction_ref + refund merchant id =
            // approval Merchant_transaction_ref + approval merchant ID
            return this.internationalRefundService.getByRef(connOnline, approval.getMerchant_transaction_ref(), approval.getMerchant_id()).flatMap(internationalRefund -> {
                // case refund is pending (response code is null) -> khong update ban ghi nay
                if (internationalRefund != null && internationalRefund.getResponse_code() != null) {
                    LOGGER.info("=========Start get Refund================");
                    SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
                    JsonObject jData = new JsonObject(internationalRefund.getData());
                    jData.put("history", new JsonObject()
                            .put("merchant_transaction_ref", approval.getMerchant_transaction_ref())
                            .put("last_approved", sdf.format(new Date(internationalRefund.getLastUpdate().getTime()))));

                    String req = xRequestId;
                    String newRef = approval.getMerchant_id() + "_" + req;
                    if (newRef.length() > 32) {
                        int largerInt = newRef.length() - 32 + 1;
                        req = req.substring(0, req.length() - largerInt);
                        newRef = approval.getMerchant_id() + "_" + req;
                    }
                    return this.internationalRefundService.updateTransExt(connOnline, newRef, approval.getMerchant_transaction_ref(), approval.getMerchant_id(), jData.toString().replace("\\", "")).flatMap(internationalRefundUpdate -> {
                        LOGGER.info("=========Start update Refund================");
                        // Map r = new HashMap();
                        // r.put("purchase", internationalTransaction);
                        // r.put("approval", approval);

                        return Observable.just(internationalTransaction);

                    });
                } else {

                    return Observable.just(internationalTransaction);
                }

            });
        });
    }

    public void unlock(String key, JDBCClient clientBackUp) {
        Observable.using(SQLConnectionFactory::new, f -> f.create(clientBackUp), f -> f.dispose())
                .flatMap(connBackUp -> {
                    return this.lockService.unlock(connBackUp, key, RefundData.Type.INTERNATIONAL.getValue()).flatMap(lock -> {
                        return Observable.just(lock);
                    });
                }).subscribe();
    }

    /**
     * @return UserService return the userService
     */
    public UserService getUserService() {
        return userService;
    }

    /**
     * @param userService the userService to set
     */
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    /**
     * @return RefundConfig return the refundConfig
     */
    public RefundConfig getRefundConfig() {
        return refundConfig;
    }

    /**
     * @param refundConfig the refundConfig to set
     */
    public void setRefundConfig(RefundConfig refundConfig) {
        this.refundConfig = refundConfig;
    }

    /**
     * @return InternationalTransactionService return the
     *         internationalTransactionService
     */
    public InternationalTransactionService getInternationalTransactionService() {
        return internationalTransactionService;
    }

    /**
     * @param internationalTransactionService the internationalTransactionService to
     *        set
     */
    public void setInternationalTransactionService(InternationalTransactionService internationalTransactionService) {
        this.internationalTransactionService = internationalTransactionService;
    }

    /**
     * @return MerchantService return the merchantService
     */
    public MerchantService getMerchantService() {
        return merchantService;
    }

    /**
     * @param merchantService the merchantService to set
     */
    public void setMerchantService(MerchantService merchantService) {
        this.merchantService = merchantService;
    }

    /**
     * @return RefundApprovalService return the refundApprovalService
     */
    public RefundApprovalService getRefundApprovalService() {
        return refundApprovalService;
    }

    /**
     * @param refundApprovalService the refundApprovalService to set
     */
    public void setRefundApprovalService(RefundApprovalService refundApprovalService) {
        this.refundApprovalService = refundApprovalService;
    }

    /**
     * @return InternationalRefundService return the internationalRefundService
     */
    public InternationalRefundService getInternationalRefundService() {
        return internationalRefundService;
    }

    /**
     * @param internationalRefundService the internationalRefundService to set
     */
    public void setInternationalRefundService(InternationalRefundService internationalRefundService) {
        this.internationalRefundService = internationalRefundService;
    }

    /**
     * @return SQLConnection return the onlineCon
     */
    public SQLConnection getOnlineCon() {
        return onlineCon;
    }

    /**
     * @param onlineCon the onlineCon to set
     */
    public void setOnlineCon(SQLConnection onlineCon) {
        this.onlineCon = onlineCon;
    }

    /**
     * @return SQLConnection return the backUpCon
     */
    public SQLConnection getBackUpCon() {
        return backUpCon;
    }

    /**
     * @param backUpCon the backUpCon to set
     */
    public void setBackUpCon(SQLConnection backUpCon) {
        this.backUpCon = backUpCon;
    }
}
