package com.onepay.ma.service.service;

import com.onepay.ma.service.models.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 7/12/2016.
 */
public interface TerminalService {
    Observable<Terminals> getTerminalsByMerchant(SQLConnection connOnline, String keywords, int page, String merchantId);


    Observable<List<Terminal>> getTerminalsByIds(SQLConnection connOnline, String ids);

    Observable<List<Terminal>> listTerminalIdsByMerchant(SQLConnection connBaclUp, String merchantIds);

    default Observable<Map<String, List<String>>> mapTerminalIdsByMerchant(SQLConnection connBaclUp, String merchantIds) {
        return this.listTerminalIdsByMerchant(connBaclUp, merchantIds).flatMap(terminals -> {

            Map<String, List<String>> result  = terminals.stream().collect(Collectors.groupingBy(Terminal::getMerchant_id,
                    Collectors.mapping(Terminal::getTerminal_id, Collectors.toList())));
            return Observable.just(result);
        });
    }
}
