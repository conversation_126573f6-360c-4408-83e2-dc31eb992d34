package com.onepay.ma.service.service;

import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.paypal.Partner;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/3/16.
 */
public interface UserServiceV2 {

    Observable<Integer> changePass(SQLConnection connection, String userId, String pass, String newPass);
    String encodePassword(String password, int level);
    Observable<UserData> insert(SQLConnection connection, UserData user);
    Observable<UserData> getUserExisted(SQLConnection connection,String email, String phone);
    Observable<List<UserData>> listByCreateId(SQLConnection connection, String createId);
    Observable<UserData> update(SQLConnection connection, UserData user,String sId);
    Observable<UserData> getUserDataBySId(SQLConnection connection, String sId);
    Observable<UserData> resetPass(SQLConnection connection, String userId,String newPass, String platform);
    Observable<Partner> getPartnerIdByUserId(SQLConnection connection, String sId);
    Observable<List<UserData>> listUserByPartnerIds(SQLConnection connection, String userId, String partnerIds);
    Observable<List<UserData>> listUserByMerchantIds(SQLConnection connection, String userId, String partnerIds);
    Observable<List<String>> listRecentPasswords(SQLConnection connection, String userId);
    Observable<Integer> changePassV2(SQLConnection connection, String userId, String pass, String newPass, String platform);

    Observable<String> findIdByMobile(SQLConnection connection, String mobile);
}
