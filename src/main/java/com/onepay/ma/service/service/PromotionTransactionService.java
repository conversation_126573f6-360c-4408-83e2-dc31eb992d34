package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionTransaction;
import com.onepay.ma.service.models.PromotionTransactionParameter;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/4/16.
 */
public interface PromotionTransactionService {
    Observable<Transactions<PromotionTransaction>> list(SQLConnection sqlConnPr, PromotionTransactionParameter promotionTransactionParameter);
    Observable<PromotionTransaction> get(SQLConnection sqlConnPr, String transactionId);
    Observable<Integer> revert(SQLConnection sqlConnPr, String transactionId);
    Observable<Integer> getTotalDownload(SQLConnection sqlConnPr, PromotionTransactionParameter parameter);
    ResultSet download(Connection connPr, PromotionTransactionParameter parameter) throws SQLException;
}
