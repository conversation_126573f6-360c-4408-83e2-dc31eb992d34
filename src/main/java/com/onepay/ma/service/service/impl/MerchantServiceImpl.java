package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.MerchantData;
import com.onepay.ma.service.models.Merchants;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.mpay.MpayMerchant;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/6/16.
 */
@Service
public class MerchantServiceImpl implements MerchantService {

    public static final String ALL_MERCHANT_ID_BY_USER = "{call PKG_MERCHANTPORTAL_2.GET_ALL_MERCHANTS_BY_USER_ID(?,?,?,?)}";
    public static final String GET_MERCHANT_MPAY = "{call PKG_MERCHANT.GET_MERCHANT_MPAY(?,?,?,?)}";
    public static final String GET_MERCHANT_DATA = "{call PKG_ONECREDIT.GET_MERCHANT_DATA(?,?,?,?)}";
    public static final String GET_MERCHANT_DOMESTIC_DATA = "{call PKG_PAYMENT2.GET_MERCHANT_DATA(?,?,?,?)}";
    public static final String GET_CURRENCIES_BY_MERCHANT_ID = "{call PKG_MPAY.get_currency_by_id(?,?,?,?)}";
    private static final String LIST_MERCHANT_MA = "{call PKG_MERCHANTPORTAL_2.GET_LIST_MERCHANTS_MA_2(?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_ONECREDIT = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_ONECREDIT(?,?,?,?)}";
    private static final String LIST_MERCHANT_ONEBILL = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_ONEBILL(?,?,?,?)}";
    private static final String LIST_MERCHANT_PAYCOLLECT = "{call PKG_MERCHANT.get_list_merchants_paycollect(?,?,?,?)}";
    private static final String MERCHANT_PAYCOLLECT_NEW = "{call pkg_ma_paycollect.get_list_merchants_paycollect(?,?,?,?)}";
    private static final String LIST_MERCHANT_PAYOUT = "{call PKG_MERCHANT.get_list_merchants_payout(?,?,?,?)}";
    private static final String LIST_MERCHANT_PAYMENT2 = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_PAYMENT2(?,?,?,?)}";
    private static final String LIST_MERCHANT_PROMOTION = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_PROMOTION(?,?,?,?)}";
    private static final String LIST_MERCHANT_MPAY = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_MPAY(?,?,?,?)}";
    private static final String LIST_MERCHANT_MPAY_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_MPAY_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_QUICKLINK_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_QUICKLINK_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_PAYMENT2_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_DOMESTIC_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_ONECREDIT_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_INTER_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_BILLING_PAGE = "{call PKG_MERCHANT.get_list_mer_bill_page(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_PROMOTION_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_PROMOTION_PAGE(?,?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_ID_BY_USER_PARTNER = "{call PKG_PARTNER.GET_LIST_ID_BY_USER_PARTNER(?,?,?,?)}";
    public static final String ALL_MERCHANT_ID_BY_USER_V2 = "{call PKG_USER_V2.GET_ALL_MERCHANTS_BY_USER_ID(?,?,?,?)}";
    public static final String LIST_MERCHANT_DOMESTIC_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_DOMES_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_INTER_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_INTER_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_PAYOUT_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_PO_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_QUICKLINK_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_QL_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_PAYCOLLECT_BY_LIST_ID = "{call PKG_MA_PAYCOLLECT.GET_LIST_MER_PC_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_BILLING_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_BILL_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_PROMOTION_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_PROMO_BY_LIST_ID(?,?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_MPAY_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_MPAY_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String LIST_MERCHANT_BNPL_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_BNPL_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_QUICKLINK = "{call PKG_MERCHANT.get_list_merchants_quicklink(?,?,?,?)}";
    private static final String LIST_MERCHANT_BNPL = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_BNPL(?,?,?,?)}";
    private static final String LIST_MERCHANT_VIETQR_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_VIETQR_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_VIETQR = "{call PKG_MERCHANT.get_list_merchants_vietqr(?,?,?,?)}";
    public static final String LIST_MERCHANT_VIETQR_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_VIETQR_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    public static final String GET_ALL_MERCHANT_PAYCOLLECT = "{call PKG_MA_PAYCOLLECT.GET_ALL_MERCHANT_PAYCOLLECT(?,?,?,?)}";
    //direct debit
    private static final String LIST_MERCHANT_DIRECT_DEBIT = "{call PKG_MERCHANT.GET_LIST_MERCHANTS_DD(?,?,?,?)}";
    private static final String LIST_MERCHANT_DIRECT_DEBIT_PAGE = "{call PKG_MERCHANT.GET_LIST_MER_DD_PAGE(?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT_DIRECT_DEBIT_BY_LIST_ID = "{call PKG_MERCHANT.GET_LIST_MER_DD_BY_LIST_ID(?,?,?,?,?,?,?,?)}";
    
    public static final String LIST_MERCHANT_PAYMENT_BY_LIST_ID = "{call PKG_MERCHANT.GET_MERCHANTS_PAYMENT_BY_IDS(?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(MerchantServiceImpl.class.getName());

    @Override
    public Observable<List<Merchant>> list(SQLConnection connOnline, SQLConnection connBackUp, String merchantId,
            String userId, String type) {
        return getListMerchant(connBackUp, merchantId, userId, type).flatMap(merchants -> {
            if (type.equalsIgnoreCase("bnpl")) {
                LOGGER.info("Type BNPL");
            }
            List<Merchant> listMerchant = new ArrayList<>();
            if (merchants.size() == 1) {
                Merchant merchant = merchants.get(0);
                if (merchant.getMerchant_id().equals("ALL")) {
                    if (!merchantId.isEmpty()) {
                        if (type.equals("international")) {
                            return getListMerchantOneCredit(connOnline, merchantId);
                        } else if (type.equals("domestic")) {
                            return getListMerchantPayment(connOnline, merchantId);
                        } else if (type.equals("promotion")) {
                            return getListMerchantPromotion(connOnline, merchantId);
                        } else if (type.equals("mpay")) {
                            return listMerchantMpayByIds(connOnline, merchantId);
                        } else if (type.equals("billing")) {
                            return getListMerchantBilling(connOnline, merchantId);
                        } else if (type.equals("payout")) {
                            return getListMerchantPayout(connOnline, merchantId);
                        } else if (type.equals("paycollect")) {
                            return getListMerchantPayCollect(connOnline, merchantId);
                        } else if (type.equals("quicklink")) {
                            return getListMerchantQuicklink(connOnline, merchantId);
                        } else if (type.equals("bnpl")) {
                            return getListMerchantBnpl(connOnline, merchantId);
                        } else if (type.equals("vietqr")) {
                            return listMerchantVietQrByIds(connOnline, merchantId);
                        } else if (type.equals("direct-debit")) {
                            return getListMerchantDirectDebit(connOnline, merchantId);
                        }
                        listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                        return Observable.just(listMerchant);
                    }
                    return Observable.just(merchants);
                } else {
                    if (!merchantId.isEmpty()) {
                        listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                        return Observable.just(listMerchant);
                    } else {
                        return Observable.just(merchants);
                    }
                }
            } else {
                if (!merchantId.isEmpty()) {
                    listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                    return Observable.just(listMerchant);
                } else {
                    return Observable.just(merchants);
                }
            }
        });
    }

    @Override
    public Observable<List<Merchant>> listOnline(SQLConnection connOnline, String merchantId, String userId,
            String type, List<Merchant> merchantBackUp) {
        if (type.equals("international")) {
            return getListMerchantOneCredit(connOnline, merchantId);
        } else if (type.equals("domestic")) {
            return getListMerchantPayment(connOnline, merchantId);
        } else if (type.equals("promotion")) {
            return getListMerchantPromotion(connOnline, merchantId);
        }
        List<Merchant> listMerchant = getListMerchantWithMerchantId(merchantBackUp, merchantId);
        return Observable.just(listMerchant);
    }

    @Override
    public Observable<List<Merchant>> listBackUp(SQLConnection connBackUp, String merchantId, String userId,
            String type) {
        return getListMerchant(connBackUp, merchantId, userId, type);
    }

    @Override
    public Observable<Merchants> listMerchantData(SQLConnection connOnline, String keywords, int page, String type,
            String currency) {
        if (type.equals("domestic")) {
            return getListMerchantPaymentPage(connOnline, keywords, page, currency);
        } else if (type.equals("international")) {
            return getListMerchantOneCreditPage(connOnline, keywords, page, currency);
        } else if (type.equals("billing")) {
            return getListMerchantBillingPage(connOnline, keywords, page, currency);
        } else if (type.equals("promotion")) {
            return getListMerchantPromotionPage(connOnline, keywords, page, currency);
        } else if (type.equals("mpay")) {
            return getListMerchantMpayPage(connOnline, keywords, page);
        } else if (type.equals("quicklink")) {
            return getListMerchantQuickLinkPage(connOnline, keywords, page);
        } else if (type.equals("vietqr")) {
            return getListMerchantVietQrPage(connOnline, keywords, page);
        } else if (type.equals("direct-debit")) {
            return getListMerchantDirectDebitPage(connOnline, keywords, page);
        } else {
            LOGGER.log(Level.SEVERE, "[ MERCHANT ] INVALID TYPE MERCHANT");
            throw IErrors.VALIDATION_ERROR;
        }
    }

    @Override
    public Observable<List<Merchant>> listAll(SQLConnection connBackUp, String userId) {
        return getListMerchantAll(connBackUp, userId);
    }

    @Override
    public Observable<Merchant> get(SQLConnection connBackUp, String merchantId) {
        return null;
    }

    @Override
    public Observable<MpayMerchant> getMpayMerchant(SQLConnection readonlyConn, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return readonlyConn.callWithParamsObservable2(GET_MERCHANT_MPAY, inParams, outParams).map(result -> {
            MpayMerchant merchant = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            merchant = new MpayMerchant();
            merchant.setId(jsonObject.getString("S_ID"));
            merchant.setName(jsonObject.getString("S_NAME"));
            merchant.setCity(jsonObject.getString("S_CITY"));
            merchant.setCategoryCode(jsonObject.getString("S_CATEGORY_CODE"));
            merchant.setCountry(jsonObject.getString("S_COUNTRY_CODE"));

            return merchant;
        });
    }

    @Override
    public Observable<MerchantData> getData(SQLConnection connOnline, String merchantId) {
        return getMerchantData(connOnline, merchantId);
    }

    @Override
    public Observable<MerchantData> getDomesticData(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(GET_MERCHANT_DOMESTIC_DATA, inParams, outParams).map(result -> {
            MerchantData merchantData = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            merchantData = bindMerchantData(jsonObject);

            return merchantData;
        });
    }

    @Override
    public Observable<List<Merchant>> listAllByUserId(SQLConnection connBackUp, String userId) {
        return getListMerchantV2(connBackUp, userId);
    }

    @Override
    public Observable<Merchants> listMerchantByListMerchantId(SQLConnection connOnline, SQLConnection connBackUp,
            SQLConnection connPaycollect, String userId, String keywords, int page, String type, String currency) {
        LOGGER.log(Level.WARNING, "------------------START GET LIST MERCHANT ID BY USER PARTNER----------------- ");
        LOGGER.info("WITH USERID: " + userId);
        return listMerchantIdByUserPartner(connBackUp, userId).flatMap(listIds -> {
            LOGGER.log(Level.INFO, "------------------END GET LIST MERCHANT ID BY USER PARTNER----------------- ");
            if (listIds != null && listIds.size() > 0 && listIds.get(0) != null) {
                List<String> listIdsNoDuplicate = new ArrayList<>(new HashSet<>(listIds));
                // List of merchant id => string separate by comma
                String strListIds = String.join(",", listIdsNoDuplicate);
                LOGGER.info(keywords + ", " + page + ", " + ", " + strListIds);
                if (type.equals("domestic")) {
                    return getListMerchantDomesticByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("international")) {
                    return getListMerchantOneCreditByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("billing")) {
                    return getListMerchantBillingByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("promotion")) {
                    return getListMerchantPromotionByListIds(connOnline, keywords, page, currency, strListIds);
                } else if (type.equals("mpay")) {
                    return getListMerchantMpayByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("payout")) {
                    return getListMerchantPayoutByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("paycollect")) {
                    return getListMerchantPayCollectByListIds(connPaycollect, keywords, page, strListIds);
                } else if (type.equals("quicklink")) {
                    return getListMerchantQuickLinkByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("bnpl")) {
                    return getListMerchantBNPLByListIds(connOnline, keywords, page, strListIds); // merchant QR = merchant bnpl
                } else if (type.equals("vietqr")) {
                    return getListMerchantVietQrByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("direct-debit")) {
                    return getListMerchantDirectDebitByListIds(connOnline, keywords, page, strListIds);
                } else if (type.equals("payment")) {
                    return getListMerchantPaymentByListIds(connOnline, keywords, page, strListIds);
                } else {
                    LOGGER.log(Level.SEVERE, "[ MERCHANT ] INVALID TYPE MERCHANT");
                    throw IErrors.VALIDATION_ERROR;
                }
            } else {
                // listIds null mean there is no merchant avaible for this user. so return null;
                Merchants merchants = new Merchants();
                merchants.setTotal_items(0);
                merchants.setMerchants(new ArrayList<>());
                return Observable.just(merchants);
            }
        });
    }

    /**
     * Get list of merchant id by partner (granted by user)
     * 
     * @param connBackUp
     * @param userId
     * @return
     */
    private Observable<List<String>> listMerchantIdByUserPartner(SQLConnection connBackUp, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_MERCHANT_ID_BY_USER_PARTNER, inParams, outParams)
                .map(result -> {
                    Map map = result.getOutput().getJsonObject(1).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

                    List<String> listIds = new ArrayList<>();
                    if (rs.getRows().size() == 0)
                        return null;

                    for (JsonObject json : rs.getRows()) {
                        listIds.add(json.getString("S_MERCHANT_ID"));
                    }

                    return listIds;
                });
    }

    /**
     * get list merchant with merchant id
     * 
     * @param list
     * @param merchantId
     * @return
     */
    private List<Merchant> getListMerchantWithMerchantId(List<Merchant> list, String merchantId) {
        List<Merchant> merchants = new ArrayList<>();
        String[] listMerchantId = merchantId.split(",");
        for (String merchant : listMerchantId) {
            for (Merchant merchantData : list) {
                if (merchantData.getMerchant_id().equals(merchant)) {
                    merchants.add(merchantData);
                }
            }
        }
        return merchants;
    }

    /**
     * get list merchant data one credit
     * 
     * @param connOnline
     * @param merchantId
     * @return
     */
    private Observable<List<Merchant>> getListMerchantOneCredit(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_ONECREDIT, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantBilling(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_ONEBILL, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantPayout(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYOUT, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantPayCollect(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYCOLLECT, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantQuicklink(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_QUICKLINK, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantBnpl(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_BNPL, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getListMerchantDirectDebit(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_DIRECT_DEBIT, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            // JsonObject jsonObject = rs.getRows().get(0);
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<List<Merchant>> getMerchantPayCollect(SQLConnection connPaycollect, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connPaycollect.callWithParamsObservable2(MERCHANT_PAYCOLLECT_NEW, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get list merchant data payment paging
     * 
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantPromotionPage(SQLConnection connOnline, String keywords, int page,
            String currency) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(currency)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PROMOTION_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging by list of merchant id (promotion)
     * 
     * @param connOnline
     * @param keywords
     * @param page
     * @param currency
     * @param listIds
     * @return
     */
    private Observable<Merchants> getListMerchantPromotionByListIds(SQLConnection connOnline, String keywords, int page,
            String currency, String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(currency)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PROMOTION_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(6).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(5));
                    } else {
                        merchants.setTotal_items(0);
                    }

                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    /**
     * get list merchant data payment paging
     * 
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantPaymentPage(SQLConnection connOnline, String keywords, int page,
            String currency) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYMENT2_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging by list of merchant id (domestic)
     * 
     * @param connOnline
     * @param keywords
     * @param page
     * @param listIds
     * @return
     */
    private Observable<Merchants> getListMerchantDomesticByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_DOMESTIC_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }
                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    /**
     * get list merchant data quiclink paging
     * 
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantQuickLinkPage(SQLConnection connOnline, String keywords, int page) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_QUICKLINK_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchantMpay(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data direct debit paging
     *
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantDirectDebitPage(SQLConnection connOnline, String keywords, int page) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_DIRECT_DEBIT_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchantMpay(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging
     * 
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantMpayPage(SQLConnection connOnline, String keywords, int page) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_MPAY_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchantMpay(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging by list of merchant id (mpay)
     * 
     * @param connOnline
     * @param keywords
     * @param page
     * @param listIds
     * @return
     */
    private Observable<Merchants> getListMerchantMpayByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_MPAY_BY_LIST_ID, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchantMpay(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging
     * 
     * @param connOnline
     * @param keywords
     * @return
     */
    private Observable<Merchants> getListMerchantOneCreditPage(SQLConnection connOnline, String keywords, int page,
            String currency) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_ONECREDIT_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data payment paging by list of merchant id (international)
     * 
     * @param connOnline
     * @param keywords
     * @param page
     * @param listIds
     * @return
     */
    private Observable<Merchants> getListMerchantOneCreditByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_INTER_BY_LIST_ID, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    private Observable<Merchants> getListMerchantBillingPage(SQLConnection connOnline, String keywords, int page,
            String currency) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_BILLING_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * Get list merchant billing by list of merchant id
     * 
     * @param connOnline
     * @param keywords
     * @param page
     * @param listIds
     * @return
     */
    private Observable<Merchants> getListMerchantBillingByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_BILLING_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }

                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    private Observable<Merchants> getListMerchantPayoutByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYOUT_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }

                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    private Observable<Merchants> getListMerchantPayCollectByListIds(SQLConnection connOnline, String keywords,
            int page, String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYCOLLECT_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }

                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    private Observable<Merchants> getListMerchantQuickLinkByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_QUICKLINK_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }

                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    private Observable<Merchants> getListMerchantBNPLByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_BNPL_BY_LIST_ID, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    private Observable<Merchants> getListMerchantDirectDebitByListIds(SQLConnection connOnline, String keywords, int page, String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_DIRECT_DEBIT_BY_LIST_ID, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    /**
     * get list merchant data one credit
     * 
     * @param connOnline
     * @param merchantId
     * @return
     */
    private Observable<List<Merchant>> getListMerchantPayment(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYMENT2, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get list merchant data Promotion
     * 
     * @param connOnline
     * @param merchantId
     * @return
     */
    private Observable<List<Merchant>> getListMerchantPromotion(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PROMOTION, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get list merchant data Promotion
     * 
     * @param connOnline
     * @param merchantId
     * @return
     */
    @Override
    public Observable<List<Merchant>> listMerchantMpayByIds(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_MPAY, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchantMpay(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get listByMerchant merchant data
     * 
     * @param connOnline
     * @param merchantId
     * @param userId
     * @param type
     * @return
     */
    private Observable<List<Merchant>> getListMerchant(SQLConnection connOnline, String merchantId, String userId,
            String type) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(userId)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_MA, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get listByMerchant merchant data
     * 
     * @param connBackUp
     * @param userId
     * @return
     */
    private Observable<List<Merchant>> getListMerchantAll(SQLConnection connBackUp, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(ALL_MERCHANT_ID_BY_USER, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchant(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * get merchant data information
     * 
     * @param connOnline
     * @param merchantId
     * @return
     */
    private Observable<MerchantData> getMerchantData(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(GET_MERCHANT_DATA, inParams, outParams).map(result -> {
            MerchantData merchantData = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            merchantData = bindMerchantData(jsonObject);

            return merchantData;
        });
    }

    @Override
    public Observable<BaseList<String>> getCurrenciesByMerchantId(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(GET_CURRENCIES_BY_MERCHANT_ID, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET CURRENCIES BY MERCHANT  ]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BaseList<String> baseList = new BaseList();
            List<String> currencies = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                currencies.add(jsonObject.getString("S_CURRENCY"));
            }
            baseList.setList(currencies);
            baseList.setTotal_items(currencies.size());
            return baseList;
        });
    }

    @Override
    public Observable<List<Merchant>> merchantPaycollects(SQLConnection connCollect, SQLConnection connBackUp,
            String merchantId, String userId, String type) {
        return getListMerchant(connBackUp, merchantId, userId, type).flatMap(merchants -> {
            List<Merchant> listMerchant = new ArrayList<>();
            if (merchants.size() == 1) {
                Merchant merchant = merchants.get(0);
                if (merchant.getMerchant_id().equals("ALL")) {
                    if (!merchantId.isEmpty()) {
                        return getMerchantPayCollect(connCollect, merchantId);
                        // listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                        // return Observable.just(listMerchant);
                    } else {
                        return Observable.just(merchants);
                    }
                } else {
                    if (!merchantId.isEmpty()) {
                        listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                        return Observable.just(listMerchant);
                    } else {
                        return Observable.just(merchants);
                    }
                }

            } else {
                if (!merchantId.isEmpty()) {
                    listMerchant = getListMerchantWithMerchantId(merchants, merchantId);
                    return Observable.just(listMerchant);
                } else {
                    return Observable.just(merchants);
                }
            }
        });
    }

    /**
     * convert data from result set to merchant
     * 
     * @param rs
     * @return
     * @throws SQLException
     */
    private Merchant bindMerchant(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String currency = rs.getString("S_CURRENCY_CODE");
        String type = rs.getString("S_TYPE");
        String state = "ALL".equals(merchantId) ? "enabled" : rs.getString("S_STATE");
        Merchant merchant = new Merchant();
        merchant.setMerchant_id(merchantId);
        merchant.setMerchant_name(merchantName);
        merchant.setCurrency_code(currency);
        merchant.setType(type);
        merchant.setState(state);

        return merchant;
    }

    /**
     * convert data from result set to merchant
     * 
     * @param rs
     * @return
     * @throws SQLException
     */
    private Merchant bindMerchantMpay(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String currency = rs.getString("S_CURRENCY_CODE");
        String type = rs.getString("S_TYPE");
        Merchant merchant = new Merchant();
        merchant.setMerchant_id(merchantId);
        merchant.setMerchant_name(merchantName);
        merchant.setCurrency_code(currency);
        merchant.setType(type);
        return merchant;
    }

    /**
     * convert data from result set to merchant
     * 
     * @param rs
     * @return
     * @throws SQLException
     */
    private MerchantData bindMerchantData(JsonObject rs) {

        String accessCode = rs.getString("S_ACCESS_CODE");
        String hashCode = rs.getString("S_HASH_CODE");
        MerchantData merchantData = new MerchantData();
        merchantData.setAccessCode(accessCode);
        merchantData.setHashCode(hashCode);
        return merchantData;
    }

    /**
     * get listByMerchant merchant data
     * 
     * @param connBackUp
     * @param userId
     * @return
     */
    private Observable<List<Merchant>> getListMerchantV2(SQLConnection connBackUp, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(ALL_MERCHANT_ID_BY_USER_V2, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchantV2(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    /**
     * convert data from result set to merchant
     * 
     * @param rs
     * @return
     * @throws SQLException
     */
    private Merchant bindMerchantV2(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        // String merchantName = rs.getString("S_MERCHANT_NAME");
        // String currency = rs.getString("S_CURRENCY_CODE");
        String type = rs.getString("S_TYPE");
        Merchant merchant = new Merchant();
        merchant.setMerchant_id(merchantId);
        // merchant.setMerchant_name(type.equals("mpay")?merchantName:merchantId);
        // merchant.setCurrency_code(currency);
        merchant.setType(type);

        return merchant;
    }

    private Observable<Merchants> getListMerchantVietQrPage(SQLConnection connOnline, String keywords, int page) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_VIETQR_PAGE, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchantMpay(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(3));
            } else {
                merchants.setTotal_items(0);
            }
            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

    @Override
    public Observable<List<Merchant>> listMerchantVietQrByIds(SQLConnection connOnline, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_VIETQR, inParams, outParams).map(result -> {
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                Merchant merchant = bindMerchantMpay(jsonObject);
                merchantList.add(merchant);
            }

            return merchantList;
        });
    }

    private Observable<Merchants> getListMerchantVietQrByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_VIETQR_BY_LIST_ID, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(5).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchantMpay(jsonObject);
                            merchantList.add(merchant);
                        }
                        merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }
                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    @Override
    public Observable<Merchants> listAllMerchantPaycollect(SQLConnection connBackUp, SQLConnection connPaycollect,
        String type, String userId) {
        return listMerchantIdByUserPartner(connBackUp, userId).flatMap(listIds -> {
            if (listIds != null && listIds.size() > 0 && listIds.get(0) != null) {
                if (type.equals("paycollect")) {
                    return listAllMerchantPaycollect(connPaycollect, type);
                } else {
                    LOGGER.log(Level.SEVERE, "[ MERCHANT ] INVALID TYPE MERCHANT");
                    throw IErrors.VALIDATION_ERROR;
                }
            } else {
                // listIds null mean there is no merchant avaible for this user. so return null;
                Merchants merchants = new Merchants();
                merchants.setTotal_items(0);
                merchants.setMerchants(new ArrayList<>());
                return Observable.just(merchants);
            }
        });
    }

    private Observable<Merchants> listAllMerchantPaycollect(SQLConnection connPaycollect, String type) {
        JsonArray inParams = new JsonArray()
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connPaycollect.callWithParamsObservable2(GET_ALL_MERCHANT_PAYCOLLECT, inParams, outParams)
                .map(result -> {
                    Merchants merchants = new Merchants();
                    List<Merchant> merchantList = new ArrayList<>();
                    Map map = result.getOutput().getJsonObject(1).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() > 0) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            Merchant merchant = bindMerchant(jsonObject);
                            merchantList.add(merchant);
                        }
                        // merchants.setTotal_items(result.getOutput().getInteger(4));
                    } else {
                        merchants.setTotal_items(0);
                    }
                    merchants.setMerchants(merchantList);
                    return merchants;
                });
    }

    private Observable<Merchants> getListMerchantPaymentByListIds(SQLConnection connOnline, String keywords, int page,
            String listIds) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(page)
                .add(50)
                .add(listIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_MERCHANT_PAYMENT_BY_LIST_ID, inParams, outParams).map(result -> {
            Merchants merchants = new Merchants();
            List<Merchant> merchantList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Merchant merchant = bindMerchant(jsonObject);
                    merchantList.add(merchant);
                }
                merchants.setTotal_items(result.getOutput().getInteger(4));
            } else {
                merchants.setTotal_items(0);
            }

            merchants.setMerchants(merchantList);
            return merchants;
        });
    }

}
