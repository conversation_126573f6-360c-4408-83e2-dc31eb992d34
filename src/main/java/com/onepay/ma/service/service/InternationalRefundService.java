package com.onepay.ma.service.service;

import com.onepay.ma.service.models.InternationalRefund;
import com.onepay.ma.service.models.InternationalRefundParameter;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface InternationalRefundService {
    Observable<Transactions<InternationalRefund>> list(SQLConnection sqlReadOnly, SQLConnection sqlOnline,SQLConnection sqlBackup, InternationalRefundParameter parameter);
    Observable<InternationalRefund> get(SQLConnection sqlOnline, SQLConnection sqlBackup, String transactionId);

    Observable<InternationalRefund> getByRef(SQLConnection sqlOnline,  String ref, String merchantId);
    Observable<InternationalRefund> updateTransExt(SQLConnection sqlOnline, String newFef, String ref, String merchantId, String data);

    Observable<RefundApproval> getApprovalByRef(SQLConnection sqlBackup, Integer transactionId, String ref, String merchantId);

    Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection connBackUp, InternationalRefundParameter parameter);
    ResultSet downloadOnline(Connection connOnline, InternationalRefundParameter parameter) throws SQLException;
    ResultSet downloadReadonly(Connection connReadOnly, InternationalRefundParameter parameter) throws SQLException;
    ResultSet downloadBackp(Connection connBackup, InternationalRefundParameter parameter) throws SQLException;

}
