package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_out.BatchReq;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

public interface BatchService {
    Observable<BatchReq> insert(SQLConnection connection, Map<String, String> param);
    void updateMsgState(Connection backupConn, String id, String state) throws SQLException;
    Observable<BatchReq> updateMsgStateSqlConnect(SQLConnection backupConn, Map<String, String> param);
    Observable<BaseList<BatchReq>> search(SQLConnection sqlConnection, Map<String, String> mIn);
    Observable<Integer> total(SQLConnection onlineConn, Map<String, String> mIn);
}
