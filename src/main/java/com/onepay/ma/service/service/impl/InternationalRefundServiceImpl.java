package com.onepay.ma.service.service.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.InternationalRefundService;
import com.onepay.ma.service.service.InternationalTransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 4/3/16.
 */
@Service
public class InternationalRefundServiceImpl implements InternationalRefundService {
    @Autowired
    private InternationalTransactionService internationalTransactionService;

    @Override
    public Observable<Transactions<InternationalRefund>> list(SQLConnection sqlReadOnly, SQLConnection sqlOnline, SQLConnection sqlBackup, InternationalRefundParameter parameter) {
        Transactions<InternationalRefund> transactions = new Transactions();
        int basePageSize = parameter.getPageSize();

        // filter by approval

        LOGGER.log(Level.INFO, "------------------START GET TOTAL International refund Approval List----------------- ");

        return getListTotalTransactionBackup(sqlBackup, parameter).flatMap(totalApproval -> {
            // LOGGER.log(Level.INFO, "GET TOTAL International refund Approval: " + gson.toJson(totalApproval) + " IDS : " + String.join(StringPool.COMMA, totalApproval));
            LOGGER.log(Level.INFO, "------------------END GET TOTAL International refund Approval List----------------- ");
            // filter by ACQUIRER_ID , card number, order info with original transaction

            LOGGER.log(Level.INFO, "------------------START GET TOTAL International Transaction List By Ids----------------- ");
            return this.internationalTransactionService.mapByIds(sqlOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(totalPurchaseMap -> {
                LOGGER.log(Level.INFO, "DATA GET TOTAL International TRansaction List By Ids: " + totalPurchaseMap.size());

                LOGGER.log(Level.INFO, "------------------END  GET TOTAL International Tansaction List By Ids----------------- ");

                Integer totalBackup = countFilterById(totalPurchaseMap, totalApproval);

                transactions.setTotal_items(totalBackup);

                LOGGER.log(Level.INFO, "------------------START GET International refund Approval List----------------- ");
                return getListTransactionBackup(sqlBackup, parameter).flatMap(refundApproval -> {

                    LOGGER.log(Level.INFO, "------------------END GET International refund Approval List----------------- ");
                    // LOGGER.log(Level.INFO, "International refund Approval List: " + gson.toJson(refundApproval));
                    // List of Purchase id
                    List<String> ids = refundApproval.stream()
                            .map(a -> a.getOriginal_transaction_id() + StringPool.BLANK)
                            .collect(Collectors.toList());
                    String purchaseIds = String.join(StringPool.COMMA, ids);
                    LOGGER.log(Level.INFO, "International refund Approval  Original Ids: " + ids);

                    // Get map trans info By ids
                    return this.internationalTransactionService.mapByIds(sqlOnline, purchaseIds, parameter).flatMap(purchaseMap -> {
                        // LOGGER.log(Level.INFO, "International Transaction List By Ids: " + gson.toJson(purchaseMap));

                        LOGGER.log(Level.INFO, "----------------------START JOIN ---------------------------");
                        // JOIN purchase INFO -------> filter != null
                        List<InternationalRefund> refundApproval2 = refundApproval
                                .stream()
                                .map(approval -> {
                                    return this.joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_transaction_id()));
                                })
                                .filter(InternationalRefund -> InternationalRefund != null)
                                .collect(Collectors.toList());


                        LOGGER.log(Level.INFO, "----------------------END JOIN ---------------------------");
                        LOGGER.log(Level.INFO, "International refund after join List: " + gson.toJson(refundApproval2));

                        parameter.setOffset(totalBackup);
                        LOGGER.log(Level.INFO, "------------------END GET International refund READONLY----------------- ");
                        List<InternationalRefund> transactionsFinal = new ArrayList<>();
                        transactionsFinal.addAll(refundApproval2);
                        transactions.setTransactions(transactionsFinal);
                        return Observable.just(transactions);
                    });
                });

            });
        });

    }

    @Override
    public Observable<InternationalRefund> get(SQLConnection sqlOnline, SQLConnection sqlBackup, String transactionId) {
        // return getTransaction(sqlOnline, transactionId).flatMap(internationalRefund -> {
            // if (internationalRefund == null) {
                return getTransactionApproval(sqlBackup, Integer.valueOf(transactionId)).flatMap(approval -> {
                    // if (String.valueOf(approval.getOriginal_transaction_id()) != null) {
                        return this.internationalTransactionService.get(sqlOnline, String.valueOf(approval.getOriginal_transaction_id())).map(purchase -> {
                            this.joinApproveWithPurchase(approval, purchase);
                            return approval;
                        });
                    // } else {
                    //     return Observable.just(internationalRefund);
                    // }
                });
            // }
        //     return Observable.just(internationalRefund);
        // });
    }

    @Override
    public Observable<InternationalRefund> getByRef(SQLConnection sqlOnline, String ref, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(ref)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlOnline.callWithParamsObservable2(TRANSACTION_REFUND_GET_BY_REF, inParams, outParams).map(result -> {
            InternationalRefund transaction = null;
            Integer resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET International REFUND ERROR : " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            return transaction;
        });
    }

    @Override
    public Observable<InternationalRefund> updateTransExt(SQLConnection sqlOnline, String newRef, String ref, String merchantId, String data) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(newRef)
                .add(ref)
                .add(data);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlOnline.callWithParamsObservable2(TRANSACTION_REFUND_UPDATE_BY_ID, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(4);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] UPDATE TRANS EXT ERROR : " + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                LOGGER.log(Level.INFO, "RESONPSE " + result.getOutput().getInteger(4));
            }
            return null;
        });
    }

    @Override
    public Observable<RefundApproval> getApprovalByRef(SQLConnection sqlBackup, Integer transactionId, String ref, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(ref)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlBackup.callWithParamsObservable2(TRANSACTION_APPROVAL_GET_BY_REF, inParams, outParams).map(result -> {
            RefundApproval transaction = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindApproval2(jsonObject);


            return transaction;
        });
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection sqlBackup, InternationalRefundParameter parameter) {
        return getListTotalTransactionBackup(sqlBackup, parameter).flatMap(totalApproval -> {
            return this.internationalTransactionService.mapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(totalPurchaseMap -> {
                return getTotalTransactionRefund(connOnline, parameter, -1).flatMap(totalItems1 -> {
                    return getTotalTransactionRefund(connReadOnly, parameter, 1).map(totalItems2 -> {
                        return totalItems1 + totalItems2 + countFilterById(totalPurchaseMap, totalApproval);
                    });
                });
            });
        });
    }

    @Override
    public ResultSet downloadOnline(Connection connOnline, InternationalRefundParameter parameter) throws SQLException {
        return downloadData(connOnline, parameter, -1);
    }

    @Override
    public ResultSet downloadReadonly(Connection connReadOnly, InternationalRefundParameter parameter) throws SQLException {
        return downloadData(connReadOnly, parameter, 1);
    }

    @Override
    public ResultSet downloadBackp(Connection connBackup, InternationalRefundParameter parameter) throws SQLException {
        return downloadBackupData(connBackup, parameter);
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private Observable<InternationalRefund> getTransaction(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            InternationalRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            boolean canVoid = jsonObject.getInteger("CAN_VOID") != null && jsonObject.getInteger("CAN_VOID") == 1 ? true : false;
            transaction.setCan_void(canVoid);

            return transaction;
        });
    }


    /**
     * get transaction data
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<List<InternationalRefund>> getListTransaction(SQLConnection connection, InternationalRefundParameter parameter, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .add(parameter.getPageSize())
                .add(parameter.getPage())
                .add(parameter.getOffset())
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connection.callWithParamsObservable2(LIST_TRANSACTION_REFUND, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(18);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET International REFUND ERROR : " + result.getOutput().getString(19));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<InternationalRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(17).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalRefund transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;

        });
    }

    /**
     * get transaction total
     *
     * @param connOnline
     * @param parameter
     * @return
     */
    private Observable<Integer> getTotalTransactionRefund(SQLConnection connOnline, InternationalRefundParameter parameter, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_REFUND, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(18);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET International ONELINE REFUND ERROR : " + result.getOutput().getString(19));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            int total = 0;
            Map map = result.getOutput().getJsonObject(17).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;

        });
    }

    /**
     * file data from database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection connection, InternationalRefundParameter parameter, int rows) throws SQLException {

        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getTransactionReference())
                .add(parameter.getTransactionType())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getTransactionId())
                .add(parameter.getCurrency())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);


        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION_REFUND, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(19);
            rs = (ResultSet) callableStatement.getObject(18);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalRefund bindTransaction(JsonObject rs) {

        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String currency = rs.getString("S_CURRENCY");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        int transactionId = rs.getInteger("EXT_ID");
        Timestamp date = Timestamp.valueOf(rs.getString("EXT_DATE"));
        String transactionType = rs.getString("EXT_TYPE");
        String status = rs.getString("EXT_STATUS");
        String status2 = rs.getString("EXT_STATUS_2");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
        String operator = rs.getString("S_OPERATOR_ID");
        double total = rs.getDouble("EXT_AMOUNT");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String cscResult = rs.getString("S_CSCRESULT_CODE");
        String cardType = rs.getString("S_CARD_TYPE");
        String cardNumber = rs.getString("S_CARD_NO");
        String cardDate = rs.getString("S_CARD_EXP");
        String data = rs.getString("S_DATA");
        Timestamp lastUpdate = rs.getString("D_STATUS") == null ? null : Timestamp.valueOf((rs.getString("D_STATUS")));
        String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
        String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
        String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
        double purchaseTotal = rs.getDouble("EXT_PURCHASE_AMOUNT");
        Timestamp purchaseDate = rs.getString("EXT_PURCHASE_DATE") == null ? null : Timestamp.valueOf(rs.getString("EXT_PURCHASE_DATE"));
        InternationalRefund transaction = new InternationalRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOperator(operator);
        transaction.setOrder_info(orderInfo);
        transaction.setResponse_code(status);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_time(date);
        transaction.setTransaction_reference(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setCsc_result_code(cscResult);
        transaction.setTransaction_purchase_time(purchaseDate);
        //////
        transaction.setData(data);
        transaction.setLastUpdate(lastUpdate);
        //////
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        refundAmount.setPurchase_total(purchaseTotal);
        transaction.setTransaction_status_2(status2.trim());
        transaction.setAmount(refundAmount);
        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        if (acquirerId == 1) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 2) {
            acquirer.setAcquirer_short_name("VietinBank");
            acquirer.setAcquirer_name("VietinBank");
        } else if (acquirerId == 3) {
            acquirer.setAcquirer_short_name("CUP");
            acquirer.setAcquirer_name("CUP");
        } else if (acquirerId == 4) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 5) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 6) {
            acquirer.setAcquirer_short_name("Paypal");
            acquirer.setAcquirer_name("Paypal");
        } else if (acquirerId == 7) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 8) {
            acquirer.setAcquirer_short_name("BIDV");
            acquirer.setAcquirer_name("BIDV");
        } else if (acquirerId == 9) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 10) {
            acquirer.setAcquirer_short_name("Techcombank");
            acquirer.setAcquirer_name("Techcombank");
        } else if (acquirerId == 11) {
            acquirer.setAcquirer_short_name("VPB");
            acquirer.setAcquirer_name("VPB");
        }
        transaction.setAcquirer(acquirer);
        transaction.setTransaction_status(status);
        if (acquirer.getAcquirer_short_name().equals("CUP") && status == null && status2.equals("WAIT_FOR_APPROVE")) {
            transaction.setTransaction_status(status2);
        }
        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);
        transaction.setAuthentication(authenticationData);
        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setCommercical_card(commercialCard);
        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }
        card.setCard_date(cardDateData);
        transaction.setCard(card);
        return transaction;
    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalRefund bindApproval(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        String currency = rs.getString("S_CURRENCY_CODE");
        Integer transactionId = rs.getInteger("N_TRANSACTION_ID");
        Integer originalId = Integer.parseInt(rs.getString("N_ORIGINAL_ID"));
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String status = rs.getInteger("N_TRANSACTION_STATUS").toString();
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");
        InternationalRefund transaction = new InternationalRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_status(status);
        transaction.setTransaction_time(date);
        transaction.setTransaction_reference(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setOperator(operatorId);
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        transaction.setAmount(refundAmount);
        transaction.setRefund_type(rs.getInteger("N_TYPE").toString());
        return transaction;
    }

    private RefundApproval bindApproval2(JsonObject rs) {
        RefundApproval result = new RefundApproval();

        Amount amount = new Amount();
        amount.setCurrency(rs.getString("S_CURRENCY"));
        amount.setTotal(rs.getDouble("N_AMOUNT"));

        result.setAmount(amount);
        result.setTransaction_id(rs.getInteger("N_ID"));
        result.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        result.setOriginal_transaction_id(rs.getString("N_TRANS_REF_ID"));
        result.setMerchant_transaction_ref(rs.getString("S_MERCHANT_TRANS_REF"));
        result.setTransaction_type(rs.getString("S_TRANSACTION_TYPE"));
        result.setData(rs.getString("S_DATA"));
        result.setParent_id(rs.getInteger("N_PARENT_ID"));
        result.setStatus(rs.getInteger("N_STATUS"));
        result.setOperator_id(rs.getString("S_OPERATOR_ID"));

        Timestamp createDate = Timestamp.valueOf(rs.getString("D_CREATE"));
        // try {
        // createDate = new Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
        // } catch (ParseException e) {
        // LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
        // }
        result.setTransaction_time(createDate);

        return result;
    }

    /**
     * get transaction data total
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    private Observable<List<String>> getListTotalTransactionBackup(SQLConnection connBackup, InternationalRefundParameter parameter) {

        // TODO: Cheat here, need to change later
        String status = parameter.getStatus();
        if (StringUtils.isEmpty(status)) {
            status = "0";
        } else if (status.equals("0")) {
            status = "400";
        }
        int refundType = (parameter.getRefundType() == null ? 13 :parameter.getRefundType());
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .addNull()
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionReference())
                .add(status)
                .add(parameter.getCurrency())
                .add(refundType);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_TOTAL_B, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(11);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL International REFUND ERROR : " + resultSet.getOutput().getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<String> result = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(10).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result.add(jsonObject.getString("N_ORIGINAL_ID"));
            }

            return result;
        });
    }

    /**
     * get transaction data online database
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    private Observable<List<InternationalRefund>> getListTransactionBackup(SQLConnection connBackup, InternationalRefundParameter parameter) {
        // TODO: Cheat here, need to change later
        String status = parameter.getStatus();
        if (StringUtils.isEmpty(status)) {
            status = "0";
        } else if (status.equals("0")) {
            status = "400";
        }
        int refundType = (parameter.getRefundType() == null ? 13 :parameter.getRefundType());
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .addNull()
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionReference())
                .add(status)
                .add(parameter.getCurrency())
                .add(refundType)
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(result -> {
            List<InternationalRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(12).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalRefund transaction = bindApproval(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    private InternationalRefund joinApproveWithPurchase(InternationalRefund approve, InternationalTransaction purchase) {


        if (purchase == null) {
            return null;
        }

        InternationalRefund result = approve;

        result.setTransaction_purchase_time(purchase.getTransaction_time());
        result.setOrder_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());

        result.setCard(purchase.getCard());
        result.getAmount().setPurchase_total(purchase.getAmount().getTotal());
        // result.setIp_address(purchase.getIp_address());

        return result;
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    public Observable<InternationalRefund> getTransactionApproval(SQLConnection sqlConnection, Integer transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_APPROVAL_GET_BY_ID, inParams, outParams).map(result -> {
            InternationalRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindApproval(jsonObject);


            return transaction;
        });
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadBackupData(Connection connection, InternationalRefundParameter parameter) throws SQLException {
        String status = parameter.getStatus();
        if (status == null || StringUtils.isEmpty(status)) {
            status = "0";
        } else if (status.equals("0")) {
            status = "400";
        }
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                // .add(parameter.getAcquirerId())
                .addNull()
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionReference())
                .add(status)
                .add(RefundData.Type.INTERNATIONAL.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.INFO, "[CALL ]: PKG_REFUND_APPROVE.get_refund_download, IN: " + inParams + ", OUT: " + outParams);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, DOWNLOAD_TRANSACTION_BACKUP, inParams, outParams);


            Integer resultCode = callableStatement.getInt(11);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] DOWNLOAD  International REFUND BACKUP ERROR : " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(10);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return rs;
    }

    private Integer countFilterById(Map<Integer, InternationalTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }


    private static final String LIST_TRANSACTION_REFUND = "{call PKG_ONECREDIT.SEARCH_REFUND(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_REFUND_APPROVE.get_refund_total_2(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_ONECREDIT.SEARCH_TRANSACTION_BY_ID_3(?,?,?,?) }";


    private static final String LIST_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}"; // DuongPXT authorize capture: add refund n_type

    private static final String TRANSACTION_APPROVAL_GET_BY_ID = "{call PKG_REFUND_APPROVE.get_refund_by_id(?,?,?,?)}";
    private static final String TRANSACTION_APPROVAL_GET_BY_REF = "{call PKG_REFUND_APPROVE.get_refund_by_ref(?,?,?,?,?,?)}";

    private static final String TRANSACTION_REFUND_GET_BY_REF = "{call PKG_ONECREDIT.get_refund_by_ref(?,?,?,?,?)}";
    private static final String TRANSACTION_REFUND_UPDATE_BY_ID = "{call PKG_ONECREDIT.update_refund_by_ref(?,?,?,?,?,?)}";

    private static final String DOWNLOAD_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_download(?,?,?,?,?,?,?,?,?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(InternationalRefundServiceImpl.class.getName());
    private Gson gson = new Gson();

}
