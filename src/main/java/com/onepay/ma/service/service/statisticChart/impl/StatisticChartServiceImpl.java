package com.onepay.ma.service.service.statisticChart.impl;
import com.onepay.ma.service.models.chartStatistics.ChartStatisticQuery;
import com.onepay.ma.service.models.chartStatistics.ChartStatistics;
import com.onepay.ma.service.models.user.ChartDomesticParmester;
import com.onepay.ma.service.service.statisticChart.StatisticChartService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 8/13/2020
 * Time: 3:44 PM
 * To change this ma-service.
 */
@Service
public class StatisticChartServiceImpl implements StatisticChartService {
    private static final String GET_STATUS_TRANSACTION = "{call PKG_CHART_STATISTIC.GET_STATUS_TRANSACTION(?,?,?,?,?,?,?,?,?)}";
    private static final String GET_STATUS_TRANSACTION_DETAIL = "{call PKG_CHART_STATISTIC.GET_STATUS_TRANSACTION_DETAIL(?,?,?,?,?,?,?,?,?)}";
    private static final String GET_STATUS_DOWLOAD_TOTAL = "{call PKG_CHART_STATISTIC.DOWNLOAD_TRANSACTION_TOTAL(?,?,?,?,?,?,?)}";
    private static final String GET_STATUS_DOWLOAD = "{call PKG_CHART_STATISTIC.DOWNLOAD_TRANSACTION(?,?,?,?,?,?,?,?,?)}";
    private static final String GET_ALL_STATUS_TRANSACTION = "{call PKG_CHART_STATISTIC.GET_ALL_TRANSACTION(?,?,?,?,?,?,?,?,?,?)}";
    private static Logger LOGGER = Logger.getLogger(StatisticChartServiceImpl.class.getName());
    @Override
    public Observable<ChartStatistics> list(SQLConnection connection, Map<String, String > mIn,String datef,String datet,String datename, String paygate,String target) {
        JsonArray inParams = new JsonArray()
                .add(paygate)
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.CARD_TYPE))
                .add(datef)
                .add(datet)
                .add(target);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connection.callWithParamsObservable2(GET_STATUS_TRANSACTION, inParams, outParams).map(result -> {
            if(result.getOutput().getInteger(6) != 200){
                LOGGER.log(Level.SEVERE, "[Chart] GET ERROR" + StringPool.SPACE + result.getOutput().getString(7));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            ChartStatistics chartInternational = new ChartStatistics();
            JsonObject js = rs.getRows().get(0);
            chartInternational.setCountS(js.getInteger("SUCCESS_TRANSACTION") == null ? 0 : js.getInteger("SUCCESS_TRANSACTION"));
            chartInternational.setTotalS(js.getDouble("TOTAL_SUCCESS") == null ? 0.0 : js.getDouble("TOTAL_SUCCESS"));
            chartInternational.setCountF(js.getInteger("FAILED_TRANSACTION") == null ? 0 : js.getInteger("FAILED_TRANSACTION"));
            chartInternational.setTotalF(js.getDouble("TOTAL_FAILED") == null ? 0.0 : js.getDouble("TOTAL_FAILED"));
            chartInternational.setMerchantId(mIn.get("merchant_id"));
            chartInternational.setPaygate(paygate);
            chartInternational.setDate_name(datename);
            return chartInternational;
        });
    }
    @Override
    public Observable<List<ChartStatistics>> listAll(SQLConnection connection, Map<String, String> mIn, Object datef, Object datet, String paygate,List arr,List arrCard,List arrBank,String target) {
        JsonArray inParams = new JsonArray()
                .add(paygate)
                .add(mIn.get(ParamsPool.MERCHANT_ND))
                .add(mIn.get(ParamsPool.MERCHANT_QT))
                .add(mIn.get(ParamsPool.CARD_TYPE))
                .add(datef)
                .add(datet)
                .add(target);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connection.callWithParamsObservable2(GET_ALL_STATUS_TRANSACTION, inParams, outParams).map(result -> {
            if(result.getOutput().getInteger(7) != 200){
                LOGGER.log(Level.SEVERE, "[Chart] GET ERROR" + StringPool.SPACE + result.getOutput().getString(8));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }
            List<ChartStatistics> chartInternational = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject js : rs.getRows()) {
                ChartStatistics chartsv = new ChartStatistics();
                chartsv.setCountS(js.getInteger("SUCCESS_TRANSACTION"));
                chartsv.setTotalS(js.getDouble("N_AMOUNT_SUCCESS"));
                chartsv.setCountF(js.getInteger("FAILED_TRANSACTION"));
                chartsv.setTotalF(js.getDouble("N_AMOUNT_FAIL"));
                chartsv.setPaygate(js.getString("PAYGATE"));
                chartsv.setCardType(js.getString("S_CARD_TYPE"));
                chartInternational.add(chartsv);
            }
                        for (Object card : arrCard) {
                            ChartStatistics chartsv = new ChartStatistics();
                            int count = 0;
                            for (ChartStatistics chr : chartInternational) {

                                if (card.equals(chr.getCardType())) {
                                    count++;
                                }
                            }
                                if (count == 0) {
                                    chartsv.setCountS(0);
                                    chartsv.setTotalS(0.0);
                                    chartsv.setCountF(0);
                                    chartsv.setTotalF(0.0);
                                    chartsv.setPaygate("QT");
                                    chartsv.setCardType(card.toString());
                                    chartInternational.add(chartsv);
                                }

                            }
                        for (Object bank : arrBank) {
                            ChartStatistics chartsv = new ChartStatistics();
                            int count = 0;
                            for (ChartStatistics chr : chartInternational) {
                                if (bank.equals(chr.getCardType())) {
                                    count++;
                                }
                                }
                                if (count == 0) {
                                    chartsv.setCountS(0);
                                    chartsv.setTotalS(0.0);
                                    chartsv.setCountF(0);
                                    chartsv.setTotalF(0.0);
                                    chartsv.setPaygate("ND");
                                    chartsv.setCardType(bank.toString());
                                    chartInternational.add(chartsv);
                                }
                        }
                    return chartInternational;
        });
    }
    @Override
    public Observable<List<Map>> listfail(SQLConnection connection, String merchantId,String cardType,String datef, String datet, String paygate,String target) {
        JsonArray inParams = new JsonArray()
                .add(paygate)
                .add(merchantId)
                .add(cardType)
                .add(datef)
                .add(datet)
                .add(target);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connection.callWithParamsObservable2(GET_STATUS_TRANSACTION_DETAIL, inParams, outParams).map(result -> {
            if(result.getOutput().getInteger(6) != 200){
                LOGGER.log(Level.SEVERE, "[Chart] GET ERROR" + StringPool.SPACE + result.getOutput().getString(7));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }
            List<Map> chartInternational = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject js : rs.getRows()) {
                Map chartsv = new HashMap();
                chartsv.put("resCode",js.getString("RESPONSE_CODE"));
                chartsv.put("countF",js.getInteger("FAILED_TRANSACTION"));
                chartsv.put("responseCode",js.getString("S_ADV_RESPONSE_CODE"));
                chartInternational.add(chartsv);
            }
            return chartInternational;
        });
    }
    @Override
    public ResultSet dowloadfile(Connection connection, ChartStatisticQuery query, String paygate) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(paygate)
                .add(query.getMerchantId())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getTarget())
                .add(query.getCardType() == null ? "" : query.getCardType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, GET_STATUS_DOWLOAD, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(8);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD "+paygate+" ERROR]: " + callableStatement.getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(7);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return  rs;
    }
    @Override
    public ResultSet dowloadFileTotal(Connection connection, ChartStatisticQuery query) {
        JsonArray inParams = new JsonArray()
                .add("")
                .add(query.getMerchantNd())
                .add(query.getMerchantQt())
                .add(query.getCardType() == null ? "" : query.getCardType())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getTarget());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, GET_ALL_STATUS_TRANSACTION, inParams, outParams);
            Integer reusltCode = callableStatement.getInt(8);
            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD "+query.getPaygate()+" ERROR]: " + callableStatement.getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(10);
        } catch (SQLException ex) {
            LOGGER.log(Level.SEVERE,""+ ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return  rs;
    }
}
