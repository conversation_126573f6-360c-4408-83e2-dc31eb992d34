package com.onepay.ma.service.service;

import com.onepay.ma.service.models.Promotion;
import com.onepay.ma.service.models.PromotionParam;
import com.onepay.ma.service.models.PromotionStatus;
import com.onepay.ma.service.models.Promotions;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionService {
    Observable<Promotions> list(SQLConnection sqlConnPr, String keyword, String merchantId, int page, int status);
    Observable<Promotion> get(SQLConnection sqlConnPr, String promotionId);
    Observable<Promotion> insert(SQLConnection sqlConnPr, PromotionParam promotionParam);
    Observable<Integer> updateStatus(SQLConnection sqlConnPr, String promotionId , PromotionStatus status);
    Observable<Integer> updateApproveDate(SQLConnection sqlConnPr, int promotionId);
    Observable<Integer> updateByField(SQLConnection sqlConnPr, String sql, JsonArray params);
}
