package com.onepay.ma.service.service.notification;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.BaseSearchCondition;
import com.onepay.ma.service.models.notification.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 26-Mar-18.
 */
public interface CustomerAppNotifyService {
    List<Map<String, String>> getAppTokenByMobileDSP(Connection readonlyConn, String mobile)  throws SQLException;

    Integer insertMsg(Connection backupConn, MpayvnNotificationMsg msg)  throws SQLException;

    Void updateMsgState(Connection backupConn, Integer id, String state)  throws SQLException;

    default Void failMsgState(Connection backupConn, Integer id) throws SQLException{
        return updateMsgState(backupConn, id, "failed");
    }
    default Void sendMsgState(Connection backupConn, Integer id) throws SQLException{
        return updateMsgState(backupConn, id, "sent");
    }

    Integer insertNotify(Connection backupConn, MpayNotificationPostDto notification)  throws SQLException;

    Observable<BaseList<MpayvnNotificationMsg>> searchMsg(SQLConnection backupConn, MpayvnNotificationMsgQuery condition);

    Observable<MpayNotifications> getNotifyByDspId(SQLConnection backupConn, NotificationSearchQuery dsp_user_id);

    Observable<MpayvnNotificationMsg> getNotifiMsgById(SQLConnection backupConn, Integer id);

    Observable<Void> see(SQLConnection backupConn, Integer notifyId);
}
