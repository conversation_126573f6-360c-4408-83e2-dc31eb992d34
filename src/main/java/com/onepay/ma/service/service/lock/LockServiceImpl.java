package com.onepay.ma.service.service.lock;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

@Service
public class LockServiceImpl implements LockService {

    @Override
    public Observable<Boolean> lock(SQLConnection backupConn, String key, long timeoutMillis, int type) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(key)
                .add(timeoutMillis)
                .add(type);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.NUMBER)
                .addNull()
                .addNull()
                .addNull();
        return backupConn.callWithParamsObservable2("{? = call pkg_lock.create_lock(?, ?, ?)}", inParams, outParams).map(rs -> {
            return rs.getOutput().getInteger(0).equals(0);
        });
    }

    @Override
    public Observable unlock(SQLConnection backupConn, String key, int type) {
        JsonArray inParams = new JsonArray()
                .add(key)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull();
        return backupConn.callWithParamsObservable2("{call pkg_lock.delete_lock(?, ?)}", inParams, outParams).map(rs -> {
            return null;
        });
    }

}
