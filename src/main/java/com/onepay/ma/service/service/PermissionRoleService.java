package com.onepay.ma.service.service;

import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public interface PermissionRoleService {
    Observable<Integer> insert(SQLConnection connection, int permissionId, int roleId);
    Observable<Integer> delete(SQLConnection connection, int permissionId);
    Observable<Integer> deleteByRoleId(SQLConnection connection, int roleId);
}
