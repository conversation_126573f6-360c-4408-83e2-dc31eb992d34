package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_out.BatchReq;
import com.onepay.ma.service.service.pay_out.BatchService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class BatchServiceImpl implements BatchService {

    private static final Logger LOGGER = Logger.getLogger(BatchServiceImpl.class.getName());

    private static final String GET_LIST_BATCH = "{ ? = call ONEPAYOUT.g_batch(?,?,?,?,?,?,?,?) }";

    @Override
    public Observable<BatchReq> insert(SQLConnection connection, Map<String, String> param) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(param.getOrDefault("ACCOUNT_ID",""))
                .add(param.getOrDefault("ACCOUNT_LOGIN",""))
                .add(param.getOrDefault("STATE",""))
                .add(param.getOrDefault("DESC",""))
                .add(param.getOrDefault("NAME",""))
                .add(param.getOrDefault("FILE_NAME",""))
                .add(param.getOrDefault("HEADER",""))
                .add(param.getOrDefault("PATH",""));
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.i_batch(?,?,?,?,?,?,?,?,?)}", inParams, outParams).map(result -> {
            String error = result.getOutput().getString(0);
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (error != null && !"".equals(error)) {
                LOGGER.log(Level.SEVERE, "ERROR ON INSERT BATCH");
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                if (rs.getRows().size() <= 0) return null;
                JsonObject jsonObject = rs.getRows().get(0);
                return bind(jsonObject);
            }
        });

    }

    @Override
    public void updateMsgState(Connection backupConn, String id, String state) throws SQLException {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(id)
                .add(state)
                .addNull();
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull();

        CallableStatement callableStatement = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(backupConn, "{? = call ONEPAYOUT.u_batch_state(?,?,?,?)}", inParams, outParams);
            String error = callableStatement.getString(1);
            ResultSet rs = (ResultSet) callableStatement.getObject(2);
            if (error != null && !"".equals(error)) {
                LOGGER.log(Level.SEVERE, "[UPDATE FUNDS TRANSFER MSG STATE  ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }

        }
    }

    @Override
    public Observable<BatchReq> updateMsgStateSqlConnect(SQLConnection sqlConnection, Map<String, String> param){
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(param.getOrDefault("batch_id",""))
                .add(param.getOrDefault(ParamsPool.STATE,""))
                .add(param.getOrDefault("user_name",""));
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2("{? = call ONEPAYOUT.u_batch_state(?,?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH FUNDS TRANSFER BATCH TOTAL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            BatchReq batch = null;
            for (JsonObject jsonObject : rs.getRows()) {
                batch = bind(jsonObject);
            }
            return batch;

        });
    }

    @Override
    public Observable<BaseList<BatchReq>> search(SQLConnection sqlConnection, Map<String, String> mIn) {
        BaseList<BatchReq> baseList = new BaseList<>();

        return this.total(sqlConnection, mIn).flatMap(total -> {
            baseList.setTotal_items(total);
            return this.searchData(sqlConnection, mIn).map(data -> {
                baseList.setList(data);
                return baseList;
            });
        });
    }

    private Observable<List<BatchReq>> searchData(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get("name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_LIST_BATCH, inParams, outParams).map(result -> {
            List<BatchReq> batchs = new ArrayList<>();
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH BATCH ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                BatchReq batch = bind(jsonObject);
                batch.setCountT(jsonObject.getInteger("COUNT_T"));
                batch.setTotal(jsonObject.getInteger("TOTAL"));
                batchs.add(batch);
            }
            return batchs;
        });
    }

    @Override
    public Observable<Integer> total(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get("name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2(GET_LIST_BATCH, inParams, outParams).map(result -> {
            Integer total = 0;
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH FUNDS TRANSFER BATCH TOTAL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }

    private BatchReq bind(JsonObject js) {
        BatchReq batch = new BatchReq();
        try {
            batch.setId(js.getString("S_ID"));
            batch.setUserName(js.getString("S_USER_NAME"));
            batch.setState(js.getString("S_STATE"));
            batch.setOriginalName(js.getString("S_ORIGINAL_NAME"));
            Timestamp createdDate = js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE"));
            batch.setCreatedDate(createdDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return batch;
    }
}
