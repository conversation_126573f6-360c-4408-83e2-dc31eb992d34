package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.service.pay_out.OperatorService;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class OperatorServiceImpl implements OperatorService {

    private static final Logger LOGGER = Logger.getLogger(OperatorServiceImpl.class.getName());

    private static final String GET_LIST_OPERATOR = "{ ? = call ONEPAYOUT.G_OPERATOR(?,?,?,?,?,?,?,?,?,?) }";

    @Override
    public Observable<BaseList<OperatorDTO>> search(SQLConnection sqlConnection, Map<String, String> mIn) {
        BaseList<OperatorDTO> baseList = new BaseList<>();
        return this.total(sqlConnection, mIn).flatMap(total -> {
            baseList.setTotal_items(total);
            return this.searchData(sqlConnection, mIn).map(data -> {
                baseList.setList(data);
                return baseList;
            });
        });
    }

    private Observable<List<OperatorDTO>> searchData(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get(ParamsPool.EMAIL))
                .add(mIn.get("name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2(GET_LIST_OPERATOR, inParams, outParams).map(result -> {
            List<OperatorDTO> fundsTransBatchList = new ArrayList<>();
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH OPERATOR ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                fundsTransBatchList.add(bind(jsonObject));
            }
            return fundsTransBatchList;
        });
    }

    @Override
    public Observable<Integer> total(SQLConnection sqlConnection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get(ParamsPool.EMAIL))
                .add(mIn.get("name"))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.PAGESIZE))
                .add(mIn.get(ParamsPool.PAGE));

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2(GET_LIST_OPERATOR, inParams, outParams).map(result -> {
            Integer total = 0;
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH OPERATOR TOTAL ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }


    @Override
    public Observable<OperatorDTO> insert(SQLConnection connection, OperatorDTO param) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(param.getPartnerId())
                .add(param.getEmail())
                .add(param.getUserName())
                .add(param.getPassword())
                .add(param.getUserCreate());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.i_operator(?,?,?,?,?,?)}", inParams, outParams).map(result -> {
            String error = result.getOutput().getString(0);
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (error != null && !"".equals(error)) {
                LOGGER.log(Level.SEVERE, "ERROR ON INSERT OPERATOR");
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                if (rs.getRows().size() <= 0) return null;
                JsonObject jsonObject = rs.getRows().get(0);
                OperatorDTO modelDto = bind(jsonObject);
                return modelDto;
            }
        });

    }

    @Override
    public Observable<OperatorDTO> update(SQLConnection sqlConnection, OperatorDTO param){
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(param.getOperatorId())
                .add(param.getEmail())
                .add(param.getUserName());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2("{? = call ONEPAYOUT.u_operator(?,?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[UPDATE OPERATOR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            OperatorDTO modelDto = bind(jsonObject);
            return modelDto;

        });
    }

    @Override
    public Observable<OperatorDTO> updateState(SQLConnection sqlConnection,  JsonObject param){
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(param.getString("operatorId",""))
                .add(param.getString(ParamsPool.STATE,""));;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2("{? = call ONEPAYOUT.u_operator_state(?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[UPDATE OPERATOR STATE ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            OperatorDTO operator = null;
            for (JsonObject jsonObject : rs.getRows()) {
                operator = bind(jsonObject);
                operator.setPassword(jsonObject.getString("S_OPERATOR_PASSWORD"));
            }
            return operator;

        });
    }

    @Override
    public Observable<OperatorDTO> getOperatorById(SQLConnection connection,  String id) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(id);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.g_operator_by_id(?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH OPERATOR ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(null != rs && !rs.getRows().isEmpty()) {
                OperatorDTO bind = bind(rs.getRows().get(0));
                bind.setPassword(rs.getRows().get(0).getString("S_OPERATOR_PASSWORD"));
                return bind;
            }
            return null;
        });
    }

    @Override
    public Observable<OperatorDTO> resetPass(SQLConnection connection, String operatorId, String newPass) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(operatorId)
                .add(newPass);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull() ;
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.U_OPERATOR_RESET_PASS(?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.WARNING, "[RESET PASS ERROR]: ");
                return null;
            } else {
                Map map = result.getOutput().getJsonObject(1).getMap();
                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                if (rs.getRows().size() <= 0) return null;
                JsonObject jsonObject = rs.getRows().get(0);
                return  bind(jsonObject);
            }
        });
    }

    @Override
    public Observable<OperatorDTO> getOperatorExisted(SQLConnection connection, String partnerId, String userName) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(partnerId)
                .add(userName);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.g_check_exits_operator(?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[SEARCH OPERATOR ERROR]: ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(null != rs && !rs.getRows().isEmpty()) {
                return bind(rs.getRows().get(0));
            }
            return null;
        });
    }

    @Override
    public Observable<BaseList<OperatorDTO>> getByUserAndPass(SQLConnection connection, String partnerId, String userName, String pass) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .add(partnerId)
                .add(userName)
                .add(pass);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull();
        BaseList<OperatorDTO> baseList = new BaseList<>();
        List<OperatorDTO> operators = new ArrayList<>();
        return connection.callWithParamsObservable2("{? = call ONEPAYOUT.g_operator_by_user_and_pass(?,?,?,?)}", inParams, outParams).map(result -> {
            String resultCode = result.getOutput().getString(0);
            if (resultCode != null) {
                LOGGER.log(Level.SEVERE, "[GET OPERATOR BY EMAIL AND PASS ERROR]: ");
                throw IErrors.USER_NAME_OR_PASS_NOT_EXISTED;
            }
            if (result.getOutput() == null || result.getOutput().isEmpty() || result.getOutput().hasNull(1)) {
                LOGGER.log(Level.SEVERE, "[GET OPERATOR BY EMAIL AND PASS ERROR]: ");
                throw IErrors.USER_NAME_OR_PASS_NOT_EXISTED;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(null != rs && !rs.getRows().isEmpty()) {
                OperatorDTO operator = bind(rs.getRows().get(0));
                if(!operator.getState().isEmpty() && "active".equalsIgnoreCase(operator.getState())){
                    operators.add(operator);
                }

            }
            baseList.setTotal_items(operators.size());
            baseList.setList(operators);
            return baseList;
        });
    }

    private OperatorDTO bind(JsonObject js) {
        OperatorDTO operatorDTO = new OperatorDTO();
        try {
            operatorDTO.setOperatorId(js.getString("S_OPERATOR_ID"));
            operatorDTO.setPartnerId(js.getString("S_PARTNER_ID"));
            operatorDTO.setState(js.getString("S_STATE"));
            operatorDTO.setEmail(js.getString("S_EMAIL"));
            operatorDTO.setUserName(js.getString("S_OPERATOR_USER_NAME"));
            operatorDTO.setUserCreate(js.getString("S_USER_CREATE"));
            Timestamp createdDate = js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE"));
            operatorDTO.setCreatedDate(createdDate);
            Timestamp updatedDate = js.getString("D_UPDATE") == null ? null : Timestamp.valueOf(js.getString("D_UPDATE"));
            operatorDTO.setUpdatedDate(updatedDate);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return operatorDTO;
    }
}
