package com.onepay.ma.service.service;

import com.onepay.ma.service.models.DomesticReport;
import com.onepay.ma.service.models.DomesticReportParameter;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
public interface DomesticReportService {
    Observable<List<DomesticReport>> list(SQLConnection connection, DomesticReportParameter parameter);
    Observable<List<DomesticReport>> listDetail(SQLConnection connection, DomesticReportParameter parameter);
    Observable<Integer>  getTotalReportDetail(SQLConnection connReadOnly, DomesticReportParameter parameter);
    Observable<Integer>  getTotalReport(SQLConnection connReadOnly, DomesticReportParameter parameter);
    List<DomesticReport> download(Connection connReadOnly, DomesticReportParameter parameter) throws SQLException;

}
