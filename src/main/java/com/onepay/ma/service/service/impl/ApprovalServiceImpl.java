package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Approval;
import com.onepay.ma.service.models.ApprovalType;
import com.onepay.ma.service.models.Approvals;
import com.onepay.ma.service.service.ApprovalService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 3/6/16.
 */

@Service
public class ApprovalServiceImpl implements ApprovalService {

    @Override
    public Observable<Approvals> list(SQLConnection connection, ApprovalType approvalType, String keyword, int page, String status) {
        return getListApprovals(connection, approvalType, keyword, page, status);
    }

    @Override
    public Observable<Approval> get(SQLConnection connection, int nid) {
        return getApproval(connection, nid);
    }

    @Override
    public Observable<Approval> update(SQLConnection connection, int id, String status, String userId) {
        return getApproval(connection, id).flatMap(approval -> {
            if(approval == null || !approval.getStatus().toLowerCase().equals("pending")){
               throw  IErrors.RESOURCE_NOT_FOUND;
            }
            return updateApproval(connection,id, approval, status, userId).flatMap(integer -> getApproval(connection, id));
        });

    }

    @Override
    public Observable<Approval> insert(SQLConnection connection, Approval approval) {
        return  insertApproval(connection, approval);
    }

    /**
     * get list approval by type
     * @param sqlConnection
     * @param approvalType
     * @param keyword
     * @param page
     * @param status
     */
    private Observable<Approvals> getListApprovals(SQLConnection sqlConnection, ApprovalType approvalType, String keyword, int page, String status){

            JsonArray inParams = new JsonArray()
                    .add(keyword)
                    .add(status)
                    .add(50)
                    .add(page);
            JsonArray outParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .add(OracleTypes.CURSOR)
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.INTEGER)
                    .add(OracleTypes.VARCHAR);

            String procedure = StringPool.BLANK;

            switch (approvalType) {
                case PROMOTION:
                    procedure = LIST_PROMOTION_APPROVAL;
                    break;

            }

            if(procedure.isEmpty()){
                throw IErrors.VALIDATION_ERROR;
            }
            return sqlConnection.callWithParamsObservable2(procedure, inParams, outParams).map(result -> {
                Approvals approvals = new Approvals();
                List<Approval> approvalList = new ArrayList<>();
                Map map = result.getOutput().getJsonObject(4).getMap();
                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                //JsonObject jsonObject = rs.getRows().get(0);
                if(rs.getRows().size() > 0){
                    for(JsonObject jsonObject : rs.getRows()){
                        Approval approval =  bindDataToApproval(jsonObject);
                        approvalList.add(approval);
                    }
                    approvals.setTotal_items(result.getOutput().getInteger(5));
                }else{
                    approvals.setTotal_items(0);
                }

                approvals.setApprovals(approvalList);
                return  approvals;
            });

    }

    /**
     * get a approval
     * @param sqlConnection
     * @param nid
     */
    private Observable<Approval> getApproval(SQLConnection sqlConnection, int nid){

        JsonArray inParams = new JsonArray().add(nid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(APPROVAL_BY_NID, inParams, outParams).map(result -> {
            Approval approval = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0 ) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            approval = bindDataToApproval(jsonObject);
            approval.setSchema(jsonObject.getString("S_SCHEMA"));
            approval.setTable(jsonObject.getString("S_TABLE"));

            return  approval;
        });


    }

    /**
     * insert approval data
     * @param sqlConnection
     * @param approval
     */
    private Observable<Approval> insertApproval(SQLConnection sqlConnection, Approval approval){

            JsonArray inParams = new JsonArray()
                    .add(approval.getName())
                    .add(approval.getType())
                    .add(approval.getSchema())
                    .add(approval.getTable())
                    .add(approval.getField())
                    .add(approval.getOld_value())
                    .add(approval.getNew_value())
                    .add(approval.getConditions())
                    .add(approval.getDescription())
                    .add(approval.getUser());
            JsonArray outParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .addNull()
                    .add(OracleTypes.NUMBER)
                    .add(OracleTypes.VARCHAR)
                    .add(OracleTypes.NUMBER);

            return sqlConnection.callWithParamsObservable2(INSERT_APPROVAL, inParams, outParams).map(result -> {
                int returnId = 0;
                int nid = result.getOutput().getInteger(10);
                if(nid != 201){
                    throw  new DatabaseException(result.getOutput().getString(11));
                }else{
                    returnId = result.getOutput().getInteger(12);
                }

                return returnId;
            }).flatMap(integer -> getApproval(sqlConnection, integer)).map(approval1 -> approval1);


    }

    /**
     * update approval data
     * @param connection
     * @param id
     * @param approval
     * @param status
     * @param userId
     */
    private Observable<Integer> updateApproval(SQLConnection connection, int id, Approval approval, String status, String userId){

            JsonArray inParams = new JsonArray()
                    .add(id)
                    .add(userId)
                    .add(status);
            JsonArray outParams = new JsonArray()
                    .addNull()
                    .addNull()
                    .addNull()
                    .add(OracleTypes.NUMBER)
                    .add(OracleTypes.VARCHAR)
                    .add(OracleTypes.NUMBER);
            return connection.callWithParamsObservable2(UPDATE_APPROVAL_BY_ID, inParams, outParams).map(result -> {
                int nid = result.getOutput().getInteger(3);
                if(nid != 200){
                    throw  new DatabaseException("Update Approval Failed.");
                }

                return nid;

            });

    }

    /**
     * Bind data to approval
     * @param rs
     * @return
     * @throws SQLException
     */
    private Approval bindDataToApproval(JsonObject rs) {
        String approval_name = rs.getString("S_NAME");
        String oldValue = rs.getString("S_OLD_VALUE");
        String newValue = rs.getString("S_NEW_VALUE");
        String type = rs.getString("S_TYPE");
        String field = rs.getString("S_FIELD");
        String userId = rs.getString("S_USER_ID");
        String userConfirm = rs.getString("S_USER_CONFIRM_ID");
        String description = rs.getString("S_DESCRIPTION");
        String conditions = rs.getString("S_CONDITIONS");
        String status = rs.getString("S_STATUS");
        Timestamp requestTime = rs.getString("D_CREATE") == null ? null : Timestamp.valueOf(rs.getString("D_CREATE"));
        int id = rs.getInteger("N_ID");
        Approval approval =  new Approval();
        approval.setId(id);
        approval.setName(approval_name);
        approval.setOld_value(oldValue);
        approval.setDescription(description);
        approval.setField(field);
        approval.setNew_value(newValue);
        approval.setUser(userId);
        approval.setUser_confirm(userConfirm);
        approval.setConditions(conditions);
        approval.setStatus(status);
        approval.setType(type);
        approval.setRequest_time(requestTime);

        return approval;
    }

    private static final String LIST_PROMOTION_APPROVAL = "{call PKG_APPROVE.PR_APPROVAL_SEARCH(?,?,?,?,?,?,?,?)}";

    private static final String APPROVAL_BY_NID = "{call PKG_APPROVE.APPROVAL_GET(?,?,?,?)}";

    private static final String UPDATE_APPROVAL_BY_ID = "{call PKG_APPROVE.APPROVAL_UPDATE(?,?,?,?,?, ?)}";

    private static final String INSERT_APPROVAL= "{call PKG_APPROVE.APPROVAL_INSERT(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(ApprovalServiceImpl.class.getName());

}
