package com.onepay.ma.service.service.store;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.shop.Store;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 27-Sep-17.
 */
public interface StoreService {
    Observable<BaseList<Store>> searchStoresByMerchant(SQLConnection connOnline, String keywords, int page, String merchantId);


    Observable<List<Store>> getStoresByIds(SQLConnection connOnline, String ids);

    Observable<List<Store>> listStoreIdsByMerchant(SQLConnection connBaclUp, String merchantIds);

    default Observable<Map<String, List<String>>> mapStoreIdsByMerchant(SQLConnection connBaclUp, String merchantIds) {
        return this.listStoreIdsByMerchant(connBaclUp, merchantIds).flatMap(terminals -> {

            Map<String, List<String>> result  = terminals.stream().collect(Collectors.groupingBy(Store::getMerchant_id,
                    Collectors.mapping(Store::getStore_id, Collectors.toList())));
            return Observable.just(result);
        });
    }
}
