package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.StatisticsReportDt;
import com.onepay.ma.service.models.StatisticsReport;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.StatisticsReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by tuydv on 12/6/18.
 */
@Service
public class StatisticsReportServiceImpl implements StatisticsReportService {
    @Override
    public Observable<List<StatisticsReport>> list(SQLConnection connection, StatisticsReportParameter parameter) {
        return listReport(connection, parameter);
    }

    @Override
    public Observable<List<StatisticsReport>> listDetail(SQLConnection connection, StatisticsReportParameter parameter) {
        return  listReportDetail(connection, parameter);
    }
    @Override
    public Observable<List<StatisticsReportDt>> listDetailDt(SQLConnection connection, StatisticsReportParameter parameter) {
        return  listReportDetailDt(connection, parameter);
    }

    @Override
    public Observable<Integer> getTotalReportDetail(SQLConnection connReadOnly, StatisticsReportParameter parameter) {
        return getListTotalReportDetail(connReadOnly, parameter);
    }

    @Override
    public Observable<Integer> getTotalReport(SQLConnection connReadOnly, StatisticsReportParameter parameter){
        return getListTotalReport(connReadOnly, parameter);
    }

    @Override
    public ResultSet download(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        return downloadData(connection, parameter);
    }
    @Override
    public ResultSet downloadDetail(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        return downloadDataDetail(connection, parameter);
    }
    @Override
    public Observable<Integer> getTotalReportDetailDt(SQLConnection connReadOnly, StatisticsReportParameter parameter) {
        return getListTotalReportDetailDt(connReadOnly, parameter);
    }
    @Override
    public ResultSet downloadDetailDt(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        return downloadDataDetailDt(connection, parameter);
    }

    private Observable<List<StatisticsReport>> listReport(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(STATISTICS_REPORT, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(5);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS  ERROR : " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<StatisticsReport> statisticsReports = new ArrayList<StatisticsReport>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                StatisticsReport statisticsReport = bindReport(jsonObject);
                statisticsReports.add(statisticsReport);
            }

            return statisticsReports;
        });
    }

    /**
     *  get report data total
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<Integer> getListTotalReport(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(STATISTICS_REPORT, inParams, outParams).map(result -> {

            Integer resultCode = result.getOutput().getInteger(5);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS TOTAL ERROR : " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total=0;
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;

        });
    }


    /**
     * file data from onerecon database
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = ExportDatasourceUtil.execute(connection, STATISTICS_REPORT, inParams, outParams);
        Integer reusltCode = callableStatement.getInt(6);

        if (reusltCode != 200) {
            LOGGER.log(Level.SEVERE, "[DOWNLOAD STATISTICS ERROR]: " + callableStatement.getString(7));
        }

        ResultSet rs = (ResultSet) callableStatement.getObject(5);
        return rs;
    }

    private Observable<List<StatisticsReport>> listReportDetail(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getInterval())
                .add(parameter.getTransType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_STATISTICS_DETAIL, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(9);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS DETAIL ERROR : " + result.getOutput().getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<StatisticsReport> statisticsReports = new ArrayList<StatisticsReport>();
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                StatisticsReport statisticsReport = bindReportDetail(jsonObject);
                statisticsReports.add(statisticsReport);
            }

            return statisticsReports;
        });
    }
    private Observable<Integer> getListTotalReportDetail(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getInterval())
                .add(parameter.getTransType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_STATISTICS_DETAIL, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(9);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS DETAIL TOTAL ERROR : " + result.getOutput().getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total=0;
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }
    /**
     * file data from onerecon database
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadDataDetail(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getMerchantId())
                .add(parameter.getMerchantName())
                .add(parameter.getInterval())
                .add(parameter.getTransType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = ExportDatasourceUtil.execute(connection, REPORT_STATISTICS_DETAIL, inParams, outParams);
        Integer reusltCode = callableStatement.getInt(10);

        if (reusltCode != 200) {
            LOGGER.log(Level.SEVERE, "[DOWNLOAD STATISTICS_DETAIL ERROR]: " + callableStatement.getString(11));
        }

        ResultSet rs = (ResultSet) callableStatement.getObject(9);
        return rs;
    }

    private Observable<List<StatisticsReportDt>> listReportDetailDt(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getTransDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getInterval())
                .add(parameter.getTransType())
                .add(parameter.getAcqType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_STATISTICS_DETAIL_DT, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(7);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS DETAIL DT ERROR : " + result.getOutput().getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<StatisticsReportDt> statisticsDtReports = new ArrayList<StatisticsReportDt>();
            Map map = result.getOutput().getJsonObject(6).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                StatisticsReportDt statisticsReport = bindReportDetailDt(jsonObject);
                statisticsDtReports.add(statisticsReport);
            }

            return statisticsDtReports;
        });
    }

    private Observable<Integer> getListTotalReportDetailDt(SQLConnection connection, StatisticsReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getTransDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getInterval())
                .add(parameter.getTransType())
                .add(parameter.getAcqType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_STATISTICS_DETAIL_DT, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(7);
            if(resultCode != 200) {
                LOGGER.log(Level.SEVERE , "[ERROR] GET STATISTICS DETAIL TOTAL ERROR : " + result.getOutput().getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = 0;

            Map map = result.getOutput().getJsonObject(6).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }


    /**
     * file data from onerecon database
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadDataDetailDt(Connection connection, StatisticsReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getTransDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getInterval())
                .add(parameter.getTransType())
                .add(parameter.getAcqType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_STATISTICS_DETAIL_DT, inParams, outParams);
            Integer reusltCode = callableStatement.getInt(8);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD STATISTICS_DETAIL_DT ERROR]: " + callableStatement.getString(9));
            }

            rs =  (ResultSet) callableStatement.getObject(7);

        }catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }


    private StatisticsReport bindReport(JsonObject rs) {
        StatisticsReport statisticsReport = new StatisticsReport();
        String stmDate = rs.getString("D_SETTLEMENT");
        String cardType= rs.getString("S_CARD_TYPE");
       // String acqCode=rs.getString("S_ACQ_CODE");
        Double amountIss=rs.getDouble("N_AMOUNT_ISS");
        Double amountAcq=rs.getDouble("N_AMOUNT_ACQ");
        Double issFee = rs.getDouble("N_ISS_FEE");
        Double acqFee=rs.getDouble("N_ACQ_FEE");
        Double amountRecon=amountIss-amountAcq;
        Double feeRecon =issFee -acqFee;
        statisticsReport.setSettlementDate(stmDate);
        statisticsReport.setCardType(cardType);
       // statisticsReport.setAcqCode(acqCode);
        statisticsReport.setAmountIss(amountIss);
        statisticsReport.setAmountAcq(amountAcq);
        statisticsReport.setIssFee(issFee);
        statisticsReport.setAcqFee(acqFee);
        statisticsReport.setAmountRecon(amountRecon);
        statisticsReport.setFeeRecon(feeRecon);

        return statisticsReport;
    }

    private StatisticsReport bindReportDetail(JsonObject rs) {
        StatisticsReport statisticsReport = new StatisticsReport();
        String transDate = rs.getString("D_TRANS_DATE");
        String cardType= rs.getString("S_CARD_TYPE");
        Integer countIss =rs.getInteger("COUNT_ISS");
        Integer countAcq= rs.getInteger("COUNT_ACQ");
        Double amountIss=rs.getDouble("N_AMOUNT_ISS");
        Double amountAcq=rs.getDouble("N_AMOUNT_ACQ");
        Double issFee = rs.getDouble("N_ISS_FEE");
        Double acqFee=rs.getDouble("N_ACQ_FEE");
        Double amountRecon=amountIss-amountAcq;
        Double feeRecon =issFee -acqFee;
        String transType = rs.getString("S_TRANS_TYPE");
        statisticsReport.setTransType(transType);
        statisticsReport.setTransDate(transDate);
        statisticsReport.setCardType(cardType);
        statisticsReport.setAmountIss(amountIss);
        statisticsReport.setAmountAcq(amountAcq);
        statisticsReport.setIssFee(issFee);
        statisticsReport.setAcqFee(acqFee);
        statisticsReport.setAmountRecon(amountRecon);
        statisticsReport.setFeeRecon(feeRecon);
        statisticsReport.setCountIss(countIss);
        statisticsReport.setCountAcq(countAcq);
        return statisticsReport;
    }
    private StatisticsReportDt bindReportDetailDt(JsonObject rs) {
        StatisticsReportDt statisticsReport = new StatisticsReportDt();
        // merchant Id
        String merchantId= rs.getString("S_MERCHANT_ID");
        // merchant Name
        String merchantName= rs.getString("S_MERCHANT_NAME");
        // Acq
        String acqCode= rs.getString("S_ACQ_CODE");
        // S_CARD_TYPE
        String  cardType=rs.getString("S_CARD_TYPE");
        // D_TRANS_DATE
        String transDate = rs.getString("D_TRANS_DATE");
        // Transaction Source
        String transactionSource= rs.getString("S_CLIENT_CODE");
        // S_TRANS_TYPE
        String transType= rs.getString("S_TRANS_TYPE");
        // Bank Transaction ID
        String bankTransId= rs.getString("S_BANK_TRANS_ID");
        // Transaction ID (Khach hang - DSP)
        String cusTransId= rs.getString("S_CUSTOMER_TRANS_ID");
        // Merchant transaction ID (merchant - MSP)
        String merchantRef= rs.getString("S_MERCHANT_REF");
        String onepayTransId= rs.getString("S_ONEPAY_TRANS_ID");
        // S_CARD_NUMBER
        String cardNumber= rs.getString("S_CARD_NUMBER");
        // S_CURRENCY_CODE
        String currency= rs.getString("S_CURRENCY_CODE");
        // N_AMOUNT_ISS
        Double amountIss=rs.getDouble("N_AMOUNT_ISS");
        Double amountAcq=rs.getDouble("N_AMOUNT_ACQ");
        Double issFee = rs.getDouble("N_ISS_FEE");
        Double acqFee=rs.getDouble("N_ACQ_FEE");
        Double percentFeeIss = rs.getDouble("N_MSO_PERCENT_ISS");
        Double percentFeeAcq=rs.getDouble("N_MSO_PERCENT_ACQ");

        statisticsReport.setMerchantId(merchantId);
        statisticsReport.setMerchantName(merchantName);
        statisticsReport.setAcqCode(acqCode);
        statisticsReport.setCardType(cardType);
        statisticsReport.setTransDate(transDate);
        statisticsReport.setTransactionSource(transactionSource);
        statisticsReport.setTransType(transType);
        statisticsReport.setBankTransId(bankTransId);
        statisticsReport.setCusTransId(cusTransId);
        statisticsReport.setMerchantRef(merchantRef);
        statisticsReport.setOnepayTransId(onepayTransId);
        statisticsReport.setCardNumber(cardNumber);
        statisticsReport.setCurrency(currency);
        statisticsReport.setAmountIss(amountIss);
        statisticsReport.setAmountAcq(amountAcq);
        statisticsReport.setIssFee(issFee);
        statisticsReport.setAcqFee(acqFee);
        statisticsReport.setPercentFeeIss(percentFeeIss);
        statisticsReport.setPercentFeeAcq(percentFeeAcq);
        return statisticsReport;

    }

    private final static String STATISTICS_REPORT = "{call PKG_FINANCE.get_trans_financial(?,?,?,?,?,?,?)}";

    private final static String REPORT_STATISTICS_DETAIL = "{call PKG_FINANCE.get_financial_groupbankid(?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_STATISTICS_DETAIL_DT = "{call PKG_FINANCE.get_financial_detail_dt(?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(StatisticsReportServiceImpl.class.getName());


}
