package com.onepay.ma.service.service;

import com.onepay.ma.service.models.StatisticsReportDt;
import com.onepay.ma.service.models.StatisticsReport;
import com.onepay.ma.service.models.StatisticsReportParameter;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by tuydv on 12/6/18.
 */
public interface StatisticsReportService {
    Observable<List<StatisticsReport>> list(SQLConnection connection, StatisticsReportParameter parameter);
    Observable<List<StatisticsReport>> listDetail(SQLConnection connection, StatisticsReportParameter parameter);
    Observable<List<StatisticsReportDt>> listDetailDt(SQLConnection connection, StatisticsReportParameter parameter);
    Observable<Integer>  getTotalReport(SQLConnection connection, StatisticsReportParameter parameter);
    ResultSet download(Connection connection, StatisticsReportParameter parameter) throws SQLException;
    Observable<Integer>  getTotalReportDetail(SQLConnection connection, StatisticsReportParameter parameter);
    ResultSet downloadDetail(Connection connection, StatisticsReportParameter parameter) throws SQLException;
    Observable<Integer>  getTotalReportDetailDt(SQLConnection connection, StatisticsReportParameter parameter);
    ResultSet downloadDetailDt(Connection connection, StatisticsReportParameter parameter) throws SQLException;

}
