package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import java.util.Map;
import org.springframework.stereotype.Service;
import rx.Observable;

@Service
public class MerchantApprovalServiceImpl implements MerchantApprovalService {

    private static final String GET_MERCHANT_APPROVAL = "{call PKG_MERCHANT_APPROVAL.GET_MERCHANT_APPROVAL(?,?,?,?)}";

    @Override
    public Observable<String> getMerchantApproval(SQLConnection backupConn, String merchantId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull();

        return backupConn.callWithParamsObservable2(GET_MERCHANT_APPROVAL, inParams, outParams).map(resultSet -> {

            String merchantApproval = null;
            int resultCode = resultSet.getOutput().getInteger(1);

            if (resultCode != 200) {
                throw new DatabaseException(resultSet.getOutput().getString(2));
            } else {
                Map map = resultSet.getOutput().getJsonObject(0).getMap();
                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                for (JsonObject jsonObject : rs.getRows()) {
                    merchantApproval = jsonObject.getString("S_MERCHANT_ID");
                    break;
                }
            }
            return merchantApproval;
        });
    }
}
