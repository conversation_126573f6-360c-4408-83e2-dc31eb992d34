package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Terminal;
import com.onepay.ma.service.service.UserTerminalService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 7/11/2016.
 */
@Service
public class UserTerminalServiceImpl  implements UserTerminalService{


    @Override
    public Observable<List<Terminal>> getTerminalBUserId(SQLConnection connOnline, Integer userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return  connOnline.callWithParamsObservable2(GET_TERMINAL_USER_ID, inParams, outParams).map(resultSet -> {
            List<Terminal> terminalList = new ArrayList<>();

            Map map = resultSet.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Terminal terminal = bindTerminal(jsonObject);
                    terminalList.add(terminal);
                }
            }

            return terminalList;
        });
    }

    private Terminal bindTerminal(JsonObject jsonObject) {
        Terminal terminal = new Terminal();
        terminal.setTerminal_id(jsonObject.getString("S_TERMINAL_ID"));
        terminal.setMerchant_id(jsonObject.getString("S_MERCHANT_ID"));
        return  terminal;
    }


    @Override
    public Observable<Integer> insert(SQLConnection connection, int userId, String merchantId, String terminalId) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(merchantId)
                .add(terminalId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(INSERT_TERMINAL_USER_ID, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(3);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert Terminal Failed" + StringPool.SPACE +  result.getOutput().getString(5));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return returnValue;
        });
    }

    @Override
    public Observable<Integer> deleteByUserId(SQLConnection connection, int userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);


        return connection.callWithParamsObservable2(DELETE_TERMINAL_BY_USER_ID, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(1);
            if (returnValue != 200) {
                LOGGER.log(Level.SEVERE, "[ USER ] Remove Terminal Failed" + StringPool.SPACE +  result.getOutput().getString(2));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return returnValue;
        });
    }

    public static final String DELETE_TERMINAL_BY_MER_ID = "{call PKG_TERMINAL_114.DELETE_MERCHANT_USER_ID(?,?,?,?)}";

    public static final String DELETE_TERMINAL_BY_TER_ID = "{call PKG_TERMINAL_114.DELETE_MERCHANT_USER_ID(?,?,?,?)}";

    public static final String DELETE_TERMINAL_BY_USER_ID = "{call PKG_TERMINAL_114.delete_terminal_by_user_id(?,?,?)}";

    public static final String INSERT_TERMINAL_USER_ID = "{call PKG_TERMINAL_114.INSERT_TERMINAL_USER(?,?,?,?,?)}";

    public static final String GET_TERMINAL_USER_ID = "{call PKG_TERMINAL_114.get_terminal_by_user_id(?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(UserTerminalService.class.getName());

}
