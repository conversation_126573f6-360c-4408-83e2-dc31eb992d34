package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.RoleData;
import com.onepay.ma.service.models.RoleParam;
import com.onepay.ma.service.models.Roles;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Override
    public Observable<List<RoleData>> listRoleByUserId(SQLConnection connection, String userId) {
        return getRoleByUserId(connection, userId);
    }

    @Override
    public Observable<List<RoleData>> listRoleByPermissionId(SQLConnection connection, int permissionId) {
        return getRoleByPermissionId(connection, permissionId);
    }

    @Override
    public Observable<Roles> list(SQLConnection connection, String keywords) {
        return getListRole(connection, keywords).flatMap(roles -> {
            return getListPermissionRole(connection, roles.getRoles(), 0).map(roleDatas -> {
                roles.setRoles(roleDatas);
                return roles;
            });
        });
    }

    @Override
    public Observable<RoleData> get(SQLConnection connection, String roleId) {
        return getRole(connection, roleId).flatMap(roleData -> {
            return permissionService.listByRoleId(connection, roleId).map(permissionDatas -> {
               roleData.setPermissions(permissionDatas);
                return roleData;
            });
        });
    }

    @Override
    public Observable<RoleData> insert(SQLConnection connection, RoleParam parameter) {
        return insertPermission(connection, parameter);
    }

    @Override
    public Observable<Integer> delete(SQLConnection connection, String roleId) {
        return deleteRole(connection, roleId);
    }

    @Override
    public Observable<List<RoleData>> listRoleByUserIdV2(SQLConnection connection, String userId) {
        return getRoleByUserIdV2(connection, userId);
    }

    /**
     * delete role data
     * @param connection
     * @param id
     * @return
     */
    private Observable<Integer> deleteRole(SQLConnection connection , String id){

        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_DELETE, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if(resultCode != 200){
                LOGGER.log(Level.SEVERE, "[ ROLE ] Delete Role Failed" + StringPool.SPACE + result.getOutput().getString(2));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    @Override
    public Observable<RoleData> update(SQLConnection connection, RoleParam parameter, String roleId) {
        return updateRole(connection, parameter, roleId);
    }

    @Override
    public Observable<RoleData> updateParentOrder(SQLConnection connection, String parentId, String orderId, String roleId) {
        return updateRoleParentOrder(connection, parentId, orderId, roleId);
    }

    /**
     * insert permission data
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<RoleData> insertPermission(SQLConnection connection , RoleParam parameter){

        JsonArray inParams = new JsonArray()
                .add(parameter.getParent_id())
                .add(parameter.getRole_name())
                .add(parameter.getDescription());

        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_INSERT, inParams, outParams).flatMap(result -> {
            int resultCode = 0;
            int returnValue = 0;
            resultCode  = result.getOutput().getInteger(4);
            if(resultCode != 201){
                LOGGER.log(Level.SEVERE, "[ ROLE ] Insert Permission Failed" + StringPool.SPACE + result.getOutput().getString(5));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(3);
            }

            return getRole(connection, String.valueOf(returnValue));
        });
    }

    /**
     * insert permission data
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<RoleData> updateRole(SQLConnection connection , RoleParam parameter, String roleId){

        JsonArray inParams = new JsonArray()
                .add(roleId)
                .add(parameter.getRole_name())
                .add(parameter.getDescription());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable(ROLE_UPDATE, inParams, outParams).flatMap(callableStatement -> {
            try {
                int resultCode  = callableStatement.getInt(4);
                if(resultCode != 200){
                    LOGGER.log(Level.SEVERE, "[ ROLE ] Update Role Failed" + StringPool.SPACE + callableStatement.getString(5));
                    throw  IErrors.INTERNAL_SERVER_ERROR;
                }

            } catch (SQLException e) {
                throw new DatabaseException("Error Sql", e);
            }  finally {
                if(callableStatement != null){
                    try {
                        callableStatement.close();
                    } catch (SQLException e) {
                        LOGGER.log(Level.SEVERE, "Error close statement", e);
                    }

                }
            }
            return getRole(connection, roleId);
        });
    }

    /**
     * update role parent and order
     * @param connection
     * @param parentId
     * @param order
     * @param roleId
     * @return
     */
    private Observable<RoleData> updateRoleParentOrder(SQLConnection connection , String parentId, String order, String roleId){

        JsonArray inParams = new JsonArray()
                .add(roleId)
                .add(parentId)
                .add(order);

        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable(ROLE_UPDATE_PARENT_ORDER, inParams, outParams).flatMap(callableStatement -> {
            try {
                int resultCode  = callableStatement.getInt(4);
                if(resultCode != 200){
                    LOGGER.log(Level.SEVERE, "[ ROLE ] Update Parent Role Failed" + StringPool.SPACE + callableStatement.getString(5));
                    throw  IErrors.INTERNAL_SERVER_ERROR;
                }

            } catch (SQLException e) {
                throw new DatabaseException("Error Sql", e);
            }  finally {
                if(callableStatement != null){
                    try {
                        callableStatement.close();
                    } catch (SQLException e) {
                        LOGGER.log(Level.SEVERE, "Error close statement", e);
                    }

                }
            }
            return getRole(connection, roleId);
        });
    }

    /**
     * get list role by s_user_id
     * @param connection
     * @param userId
     * @return
     */
    private Observable<List<RoleData>> getRoleByUserId(SQLConnection connection, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_USERS, inParams, outParams).map(result -> {
            List<RoleData> roleDataList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                RoleData role = bindRole(jsonObject);
                roleDataList.add(role);
            }

            return roleDataList;
        });
    }

    /**
     * get list role by permission_id
     * @param connection
     * @param permissionId
     * @return
     */
    private Observable<List<RoleData>> getRoleByPermissionId(SQLConnection connection, int permissionId) {
        JsonArray inParams = new JsonArray()
                .add(permissionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_PERMISSION, inParams, outParams).map(result -> {
            List<RoleData> roleDataList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                RoleData role = bindRole(jsonObject);
                roleDataList.add(role);
            }

            return roleDataList;
        });
    }

    /**
     * get list role
     * @param connection
     * @param keywords
     * @return
     */
    private Observable<Roles> getListRole(SQLConnection connection, String keywords){
        JsonArray inParams = new JsonArray()
                .add(keywords);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_SEARCH, inParams, outParams).map(result -> {
            Roles roles = new Roles();
            List<RoleData> roleDataList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0){
                for (JsonObject jsonObject : rs.getRows()) {
                    RoleData role = bindRole(jsonObject);
                    roleDataList.add(role);
                }
                roles.setTotal_items(roleDataList.size());
            }else{
                roles.setTotal_items(0);
            }

            roles.setRoles(roleDataList);
            return roles;
        });
    }

    /**
     * get role data
     * @param connection
     * @param roleId
     * @return
     */
    private Observable<RoleData> getRole(SQLConnection connection, String roleId){
        JsonArray inParams = new JsonArray()
                .add(roleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_BY_ID, inParams, outParams).map(result -> {
            RoleData roleData = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            roleData = bindRole(jsonObject);

            return roleData;
        });
    }

    /**
     * bind role
     * @param rs
     * @return
     * @throws SQLException
     */
    private RoleData bindRole(JsonObject rs) {
        RoleData roleData = new RoleData();
        int roleId = rs.getInteger("N_ROLE_ID");
        int parentRoleId = rs.getInteger("N_PARENT_ID");
        String roleName = rs.getString("S_ROLE_NAME");
        String desc = rs.getString("S_DESC");
        int order = rs.getInteger("N_ORDER");
        roleData.setRole_id(roleId);
        roleData.setDescription(desc);
        roleData.setRole_name(roleName);
        roleData.setOrder(order);
        roleData.setParent_id(parentRoleId);
        return roleData;
    }

    /**
     * get list permission role
     * @param sqlConnection
     * @param roleList
     */
    private Observable<List<RoleData>> getListPermissionRole(SQLConnection sqlConnection, List<RoleData> roleList, int index){
        if(roleList.size() <=0){
            return Observable.just(roleList);
        }
        RoleData role = roleList.get(index);
        final int finalIndex = index;
        return Observable.just(role).flatMap(approvalData -> {
            //insert approval for user group
            return permissionService.listByRoleId(sqlConnection, String.valueOf(role.getRole_id())).flatMap(permissionDatas -> {
                role.setPermissions(permissionDatas);
                if(finalIndex >= roleList.size() - 1){
                    return Observable.just(roleList);
                }else{
                    return getListPermissionRole(sqlConnection, roleList, finalIndex + 1);
                }
            });
        });
    }
    /**
     * get list role by n_id
     * @param connection
     * @param userId
     * @return
     */
    private Observable<List<RoleData>> getRoleByUserIdV2(SQLConnection connection, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(ROLE_USERS_V2, inParams, outParams).map(result -> {
            List<RoleData> roleDataList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                RoleData role = bindRoleV2(jsonObject);
                roleDataList.add(role);
            }

            return roleDataList;
        });
    }
    /**
     * bind role
     * @param rs
     * @return
     * @throws SQLException
     */
    private RoleData bindRoleV2(JsonObject rs) {
        RoleData roleData = new RoleData();
        int roleId = rs.getInteger("N_ROLE_ID");
        //int parentRoleId = rs.getInteger("N_PARENT_ID");
       // String roleName = rs.getString("S_ROLE_NAME");
        //String desc = rs.getString("S_DESC");
       // int order = rs.getInteger("N_ORDER");
        roleData.setRole_id(roleId);
//        roleData.setDescription(desc);
//        roleData.setRole_name(roleName);
//        roleData.setOrder(order);
//        roleData.setParent_id(parentRoleId);
        return roleData;
    }
    @Autowired
    private PermissionService permissionService;

    private static final String ROLE_USERS =  "{call PKG_MERCHANTPORTAL_2.USER_ROLES(?,?,?,?)}";

    private static final String ROLE_INSERT =  "{call PKG_MERCHANTPORTAL_2.ROLE_INSERT(?,?,?,?,?,?)}";

    private static final String ROLE_UPDATE =  "{call PKG_MERCHANTPORTAL_2.ROLE_UPDATE(?,?,?,?,?)}";

    private static final String ROLE_PERMISSION =  "{call PKG_MERCHANTPORTAL_2.PERMISSION_ROLES(?,?,?,?)}";

    private static final String ROLE_UPDATE_PARENT_ORDER =  "{call PKG_MERCHANTPORTAL_2.ROLE_UPDATE_PARENT_ORDER(?,?,?,?,?)}";

    private static final String ROLE_SEARCH =  "{call PKG_MERCHANTPORTAL_2.ROLE_SEARCH(?,?,?,?)}";

    private static final String ROLE_BY_ID =  "{call PKG_MERCHANTPORTAL_2.ROLE_BY_ID(?,?,?,?)}";

    private static final String ROLE_DELETE =  "{call PKG_MERCHANTPORTAL_2.ROLE_DELETE(?,?,?)}";

    private static final String ROLE_USERS_V2 =  "{call PKG_USER_V2.USER_ROLES(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(RoleServiceImpl.class.getName());

}
