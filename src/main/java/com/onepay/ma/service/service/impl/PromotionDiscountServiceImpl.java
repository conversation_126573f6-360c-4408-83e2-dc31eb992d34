package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionDiscount;
import com.onepay.ma.service.models.PromotionDiscountType;
import com.onepay.ma.service.models.PromotionDiscountTypeList;
import com.onepay.ma.service.service.PromotionDiscountParamService;
import com.onepay.ma.service.service.PromotionDiscountService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
@Service
public class PromotionDiscountServiceImpl implements PromotionDiscountService {
    @Override
    public Observable<PromotionDiscountTypeList<PromotionDiscountType>> listDiscountType(SQLConnection sqlConnPr) {
        return getListDiscountType(sqlConnPr);
    }

    @Override
    public Observable<PromotionDiscountType> getDiscountType(SQLConnection sqlConnPr, int discountTypeId) {
        return getPromotionDiscountType(sqlConnPr, discountTypeId);
    }

    @Override
    public Observable<List<PromotionDiscount>> listDiscount(SQLConnection sqlConnPr, String promotionId, String promotionSid) {
        return getListPromotionDiscount(sqlConnPr, promotionId, promotionSid).flatMap(promotionDiscountList -> {
            // get list discount param for range discount
            return getListPromotionDiscountParam(sqlConnPr, promotionDiscountList, 0);
        });
    }

    @Override
    public Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, PromotionDiscount promotionDiscount) {
        int discountTypeId = promotionDiscount.getDiscount_type().getDiscount_type_id();
        return insertPromotionDiscount(sqlConnPr, promotionId, promotionDiscount).flatMap(integer -> {
            if(discountTypeId == ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID){
                return insertListPromotionDiscountParam(sqlConnPr, integer, promotionDiscount.getDiscount_value(), 0).flatMap(maps -> Observable.just(integer));
            }else{
                return Observable.just(integer);
            }
        });
    }

    @Override
    public Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId) {
        return deletePromotionDiscount(sqlConnPr, promotionId);
    }

    /**
     * insert promotion discount
     * @param sqlConnPr
     * @param promotionId
     * @param promotionDiscount
     * @return
     */
    private Observable<Integer> insertPromotionDiscount(SQLConnection sqlConnPr, int promotionId, PromotionDiscount promotionDiscount) {
        int discountTypeId = promotionDiscount.getDiscount_type().getDiscount_type_id();
        Map paramData = new HashMap<>();
        for (Map.Entry entry : promotionDiscount.getDiscount_value().get(0).entrySet()){
            if(!entry.getKey().equals("order")) {
                paramData.put(ParamsPool.PROMOTION_PARAM_NAME, entry.getKey());
                paramData.put(ParamsPool.PROMOTION_PARAM_VALUE, entry.getValue());
            }
        }

        String discountValue = (discountTypeId == ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID) ? StringPool.BLANK : String.valueOf(paramData.get(ParamsPool.PROMOTION_PARAM_VALUE));
        JsonArray inParams = new JsonArray()
                .add(promotionId)
                .add(discountTypeId)
                .add(discountValue);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER) ;
        return sqlConnPr.callWithParamsObservable2(DISCOUNT_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ DISCOUNT PROMOTION ] =>Insert Discount Promotion Failed" + StringPool.SPACE + result.getOutput().getString(4));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(5);
            }

            return returnValue;
        });
    }

    /**
     * insert list promotion discount parameter
     * @param sqlCOnnPr
     * @param discountId
     * @param listDiscountParam
     * @param index
     * @return
     */
    private Observable<List<Map<String,Object>>> insertListPromotionDiscountParam(SQLConnection sqlCOnnPr, int discountId, List<Map<String,Object>> listDiscountParam, int index){
        if(listDiscountParam.size() <= 0){
            return Observable.just(listDiscountParam);
        }
        Map<String, Object> map = listDiscountParam.get(index);
        final int finalIndex = index;
        return Observable.just(map).flatMap(serviceApproval -> {
            //get user data
            return promotionDiscountParamService.insert(sqlCOnnPr, discountId, map).flatMap(integer -> {
                if(finalIndex >= listDiscountParam.size() - 1){
                    return Observable.just(listDiscountParam);
                }else{
                    return insertListPromotionDiscountParam(sqlCOnnPr, discountId, listDiscountParam, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get list promotion discount
     * @param sqlConnPr
     * @param promotionId
     * @param promotionSid
     * @return
     */
    private Observable<List<PromotionDiscount>> getListPromotionDiscount(SQLConnection sqlConnPr, String promotionId, String promotionSid) {
        JsonArray inParams = new JsonArray()
                .add(promotionId)
                .add(promotionSid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR) ;
        return sqlConnPr.callWithParamsObservable2(LIST_DISCOUNT, inParams, outParams).map(result -> {

            List<PromotionDiscount> promotionDiscountTypeList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PromotionDiscount promotionDiscount = bindDiscountData(jsonObject);
                promotionDiscountTypeList.add(promotionDiscount);
            }

            return promotionDiscountTypeList;
        });
    }

    /**
     * get promotion discount type
     * @param sqlConnPr
     * @param discountTypeId
     * @return
     */
    private Observable<PromotionDiscountType> getPromotionDiscountType(SQLConnection sqlConnPr, int discountTypeId) {
        JsonArray inParams = new JsonArray()
                .add(discountTypeId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return sqlConnPr.callWithParamsObservable2(DISCOUNT_TYPE_BY_ID, inParams, outParams).map(result -> {

            PromotionDiscountType promotionDiscountType = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            promotionDiscountType = bindDiscountType(jsonObject);

            return promotionDiscountType;
        });
    }

    /**
     * get list discount type
     * @param sqlConn
     * @return
     */
    private Observable<PromotionDiscountTypeList<PromotionDiscountType>> getListDiscountType(SQLConnection sqlConn) {
        JsonArray inParams = new JsonArray()  ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER) ;
        return sqlConn.callWithParamsObservable2(LIST_DISCOUNT_TYPE, inParams, outParams).map(result -> {
            PromotionDiscountTypeList<PromotionDiscountType> promotionDiscountTypes = new PromotionDiscountTypeList();

            List<PromotionDiscountType> promotionDiscountTypeList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0){
                for (JsonObject jsonObject : rs.getRows()){
                    PromotionDiscountType discountType = bindDiscountType(jsonObject);
                    promotionDiscountTypeList.add(discountType);
                }
                promotionDiscountTypes.setTotal_items( result.getOutput().getInteger(3));
            }else{
                promotionDiscountTypes.setTotal_items(0);
            }

            promotionDiscountTypes.setDiscount_types(promotionDiscountTypeList);
            return promotionDiscountTypes;
        });
    }


    /**
     * delete promotion merchant
     * @param sqlConn
     * @param promotionId
     * @return
     */
    private Observable<Integer> deletePromotionDiscount(SQLConnection sqlConn, int promotionId){

        JsonArray inParams = new JsonArray()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConn.callWithParamsObservable2(DISCOUNT_DELETE, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ DISCOUNT PROMOTION ] Delete Discount Promotion Failed" + StringPool.SPACE + result.getOutput().getString(2));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    /**
     * get list promotion discount param
     * @param sqlConn
     * @param promotionDiscountList
     * @param index
     * @return
     */
    private Observable<List<PromotionDiscount>> getListPromotionDiscountParam(SQLConnection sqlConn, List<PromotionDiscount> promotionDiscountList, int index){
        if(promotionDiscountList.size() <= 0){
            return Observable.just(promotionDiscountList);
        }
        PromotionDiscount promotionDiscount = promotionDiscountList.get(index);
        final int finalIndex = index;
        return Observable.just(promotionDiscount).flatMap(promotionDiscountData -> {
            if(promotionDiscountData.getDiscount_type().getDiscount_type_id() == ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID) {
                //get user data
                return promotionDiscountParamService.list(sqlConn, promotionDiscount.getDiscount_id()).flatMap(promotionDiscountsList -> {
                    promotionDiscount.setDiscount_value(promotionDiscountsList);
                    if (finalIndex >= promotionDiscountList.size() - 1) {
                        return Observable.just(promotionDiscountList);
                    } else {
                        return getListPromotionDiscountParam(sqlConn, promotionDiscountList, finalIndex + 1);
                    }

                });
            }else {
                if (finalIndex >= promotionDiscountList.size() - 1) {
                    return Observable.just(promotionDiscountList);
                } else {
                    return getListPromotionDiscountParam(sqlConn, promotionDiscountList, finalIndex + 1);
                }
            }
        });
    }


    /**
     * bind result set to discount type
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionDiscountType bindDiscountType(JsonObject rs){
        String name = rs.getString("S_NAME");
        int id = rs.getInteger("N_ID");
        String desc = rs.getString("S_DESC");

        PromotionDiscountType discountType = new PromotionDiscountType();
        discountType.setDescription(desc);
        discountType.setDiscount_type_id(id);
        discountType.setDiscount_type_name(name);

        return discountType;
    }

    /**
     * bind promotion discount
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionDiscount bindDiscountData(JsonObject rs){
        int id = rs.getInteger("N_ID");
        int discountTypeId = rs.getInteger("N_TYPE_ID");
        String discountName = rs.getString("S_NAME");
        String discountTypeDesc = rs.getString("S_DESC");
        double discountValue = rs.getDouble("N_VALUE") != null ? rs.getDouble("N_VALUE") : 0;
        PromotionDiscount promotionDiscount = new PromotionDiscount();
        promotionDiscount.setDiscount_id(id);
        if(discountTypeId != ParamsPool.DISCOUNT_TYPE_FIX_BY_AMOUNT_RANGE_ID){

            Map<String, Object> discountMap = new LinkedHashMap<>();

            discountMap.put(discountName.toLowerCase(), discountValue);
            discountMap.put(ParamsPool.PROMOTION_PARAM_ORDER, 1);
            // Get discount range params
            List<Map<String, Object>> discountParamList = new ArrayList<>();
            discountParamList.add(discountMap);
            promotionDiscount.setDiscount_value(discountParamList);

        }else {
            promotionDiscount.setDiscount_value(new ArrayList<>());
        }


        PromotionDiscountType discountType = new PromotionDiscountType();
        discountType.setDiscount_type_id(discountTypeId);
        discountType.setDiscount_type_name(discountName);
        discountType.setDescription(discountTypeDesc);
        promotionDiscount.setDiscount_type(discountType);

        return promotionDiscount;
    }


    private final static String LIST_DISCOUNT_TYPE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_DISCOUNT_TYPE_LIST(?,?,?,?)}";

    private final static String DISCOUNT_TYPE_BY_ID = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_DISCOUNT_TYPE_ID(?,?,?,?)}";

    private final static String LIST_DISCOUNT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_DISCOUNTS(?,?,?,?,?)}";

    private static final String DISCOUNT_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_DISCOUNT_INSERT(?,?,?,?,?,?)}";

    private static final String DISCOUNT_DELETE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_DISCOUNT_DELETE(?,?,?)}";

    @Autowired
    private PromotionDiscountParamService promotionDiscountParamService;

    private static final Logger LOGGER = Logger.getLogger(PromotionDiscountServiceImpl.class.getName());
}
