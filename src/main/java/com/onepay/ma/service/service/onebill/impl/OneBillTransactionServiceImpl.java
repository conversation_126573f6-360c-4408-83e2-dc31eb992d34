package com.onepay.ma.service.service.onebill.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.onebill.OnebillClient;
import com.onepay.ma.service.models.onebill.OnebillTransaction;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.service.onebill.OneBillTransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class OneBillTransactionServiceImpl implements OneBillTransactionService {

    private final static String ONEBILL_GET = "{call PKG_ONEBILL.get_transaction(?,?,?,?)}";

    private final static String REFUND_TRANSACTION = "{call PKG_ONEBILL.insert_refund(?,?,?,?,?)}";
    private final static String LIST_TRANSACTION_HISTORY = "{call PKG_ONEBILL.get_history(?,?,?,?)}";

    private final static String ONEBILL_SEARCH = "{call PKG_ONEBILL.search_transaction(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String ONEBILL_CLIENT_LIST = "{call PKG_ONEBILL.get_list_client(?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(OneBillTransactionServiceImpl.class.getName());

    @Override
    public Observable<List<OnebillTransactionHistory>> listHistory(SQLConnection connOnebill, Integer transactionId) {
        return getListTransactionHistory(connOnebill, transactionId).flatMap(transactions -> {
            return Observable.just(transactions);
        });
    }

    Observable<List<OnebillTransactionHistory>> getListTransactionHistory(SQLConnection sqlConnection, Integer transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(LIST_TRANSACTION_HISTORY, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET BILLING TRANSACION HISTORY ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<OnebillTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                OnebillTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }
            return transactionList;
        });

    }

    @Override
    public Observable<Void> refund(SQLConnection connOnebill, Integer transactionId, Double amount, String operator) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(amount)
                .add(operator);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnebill.callWithParamsObservable2(REFUND_TRANSACTION, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(3);

            if (reusltCode != 201) {
                LOGGER.log(Level.SEVERE, "[REFUND BILLING TRANSACTION ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return null;
        });

    }

    @Override
    public Observable<BaseList<OnebillTransaction>> search(SQLConnection connOnebill, OnebillTransactionQuery query) {
        BaseList<OnebillTransaction> baseList = new BaseList<>();

        return this.total(connOnebill, query).flatMap(total -> {
            baseList.setTotal_items(total);
            query.setOffset(0);
            return this.searchData(connOnebill, query).map(onebillTransactions -> {
                baseList.setList(onebillTransactions);
                return baseList;
            });

        });
    }

    @Override
    public Observable<Integer> total(SQLConnection connOnebill, OnebillTransactionQuery query) {
        return this.searchTotal(connOnebill, query).flatMap(totalTrans -> {
            return Observable.just(totalTrans);
        });
    }

    @Override
    public Observable<OnebillTransaction> getById(SQLConnection sqlConnection, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(ONEBILL_GET, inParams, outParams).map(result -> {

            OnebillTransaction transaction = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET ONEBILL TRANSACTION BY ID ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                transaction = bindTransaction(jsonObject);
            }

            return transaction;

        });
    }

    @Override
    public ResultSet download(Connection connection, OnebillTransactionQuery query) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getProviderId())
                .add(query.getOrderInfo())
                .add(query.getBankId())
                .add(query.getChannel())
                .add(query.getTransactionType())
                .add(query.getStatus())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, ONEBILL_SEARCH, inParams, outParams);

            int code = callableStatement.getInt(14);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD ONEBILL TRANSACTION ERROR]: " + callableStatement.getString(15));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(13);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private Observable<List<OnebillTransaction>> searchData(SQLConnection sqlConnection, OnebillTransactionQuery query) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(query.getProviderId())
                .add(query.getOrderInfo())
                .add(query.getBankId())
                .add(query.getChannel())
                .add(query.getTransactionType())
                .add(query.getStatus())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(ONEBILL_SEARCH, inParams, outParams).map(result -> {

            List<OnebillTransaction> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(13);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH ONEBILL TRANSACTION ERROR]: " + result.getOutput().getString(14));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(12).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                transactionList.add(bindTransaction(jsonObject));
            }
            return transactionList;
        });
    }

    private Observable<Integer> searchTotal(SQLConnection sqlConnection, OnebillTransactionQuery query) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(query.getProviderId())
                .add(query.getOrderInfo())
                .add(query.getBankId())
                .add(query.getChannel())
                .add(query.getTransactionType())
                .add(query.getStatus())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(ONEBILL_SEARCH, inParams, outParams).map(result -> {

            Integer total = 0;

            Integer reusltCode = result.getOutput().getInteger(13);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH ONEBILL TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(14));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(12).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });

    }

    @Override
    public Observable<BaseList<OnebillClient>> listClient(SQLConnection onlineConn) {
        JsonArray inParams = new JsonArray();
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return onlineConn.callWithParamsObservable2(ONEBILL_CLIENT_LIST, inParams, outParams).map(result -> {
            BaseList<OnebillClient> onebillClientBaseList = new BaseList<>();
            List<OnebillClient> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(1);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[LIST ONEBILL CLIENT ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                OnebillClient client = new OnebillClient();
                client.setId(jsonObject.getInteger("N_ID"));
                client.setName(jsonObject.getString("S_CLIENT_NAME"));
                transactionList.add(client);
            }

            onebillClientBaseList.setList(transactionList);
            return onebillClientBaseList;

        });
    }


    private OnebillTransactionHistory bindTransactionHistory(JsonObject rs) {
        OnebillTransactionHistory transaction = new OnebillTransactionHistory();

        Amount amount = new Amount();
        amount.setTotal(rs.getDouble("N_AMOUNT"));
        amount.setRefund_total(rs.getDouble("N_REFUND_AMOUNT"));
        amount.setCurrency(rs.getString("S_CURRENCY"));

        transaction.setOperator(rs.getString("S_OPERATOR"));
        transaction.setStatus(rs.getString("S_STATE"));
        transaction.setOrderReference(rs.getString("S_ORDER_INFO"));
        transaction.setId(rs.getInteger("N_TRANS_ID"));
        transaction.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        transaction.setTransactionDate(rs.getString("D_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_DATE")));
        transaction.setAmount(amount);

        return transaction;
    }

    private OnebillTransaction bindTransaction(JsonObject rs) {
        OnebillTransaction transaction = new OnebillTransaction();

        Amount amount = new Amount();
        amount.setTotal(rs.getDouble("N_AMOUNT"));
        amount.setRefund_total(rs.getDouble("N_REFUND_AMOUNT"));
        amount.setCurrency(rs.getString("S_CURRENCY"));

        transaction.setBankId(rs.getString("S_BANK_ID"));
        transaction.setChannel(rs.getString("S_CHANNEL_ID"));
        transaction.setProvider(rs.getString("S_PROVIDER_ID"));
        transaction.setStatus(rs.getString("S_STATE"));
        transaction.setOrderReference(rs.getString("S_ORDER_INFO"));
        transaction.setId(rs.getInteger("N_TRANS_ID"));
        transaction.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        transaction.setDescription(rs.getString("S_DESCRIPTION"));
        transaction.setTransactionDate(rs.getString("D_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_DATE")));
        transaction.setAmount(amount);
        transaction.setOriginalId(rs.getInteger("N_ORGINAL_ID"));

        return transaction;
    }
}
