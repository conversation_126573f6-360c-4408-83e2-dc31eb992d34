package com.onepay.ma.service.service.quicklink;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.installment.InstallmentBank;
import com.onepay.ma.service.models.quick_link.UploadFileDTO;
import com.onepay.ma.service.models.quick_link.UploadFileDTO;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class QuickLinkService {

    private static final String INSERT_UPLOAD = "{call PKG_UPLOAD.insert_upload(?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(QuickLinkService.class.getName());

    public static Observable<UploadFileDTO> insertUploadFile(SQLConnection sqlConnection, UploadFileDTO upload, String userId) {
        JsonArray inParams = new JsonArray()
                .add(upload.getName())
                .add(upload.getOriginalName())
                .add(upload.getExt())
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(INSERT_UPLOAD, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(5) != 200) {
                LOGGER.log(Level.SEVERE, "[GET ALL Me ERROR]: " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            UploadFileDTO bank = new UploadFileDTO();
            for (JsonObject jsonObject : rs.getRows()) {
                bank = bindingData(jsonObject, upload);
            }
            return bank;
        });
    }

    private static UploadFileDTO bindingData(JsonObject data, UploadFileDTO upload) {
        UploadFileDTO uploadDto = new UploadFileDTO();
        try {
            uploadDto.setId(data.getInteger("N_ID"));
            uploadDto.setName(data.getString("S_NAME"));
            uploadDto.setOriginalName(data.getString("S_ORIGINAL_NAME"));
            uploadDto.setExt(data.getString("S_EXT"));
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            uploadDto.setCreatedDate(sdf.parse(data.getString("S_CREATE_DATE")));
            uploadDto.setUserId(data.getString("S_CREATE"));
            uploadDto.setType(upload.getType());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR BINDING] ", e);
        }
        return uploadDto;
    }
}
