package com.onepay.ma.service.service;

import com.onepay.ma.service.models.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by anhkh on 18-May-17.
 */
public interface InternationalAuthPaymentService {
    Observable<Transactions<InternationalAuthPayment>> list(SQLConnection connReadOnly, SQLConnection connOnline, InternationalAuthPaymentQuery parameter);
    Observable<InternationalAuthPayment> get(SQLConnection connOnline, SQLConnection backup,String transactionId);
    Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, InternationalAuthPaymentQuery parameter);
    ResultSet downloadOnline(Connection connOnline, InternationalAuthPaymentQuery parameter) throws SQLException;
    ResultSet downloadReadonly(Connection connReadOnly, InternationalAuthPaymentQuery parameter) throws SQLException;
}
