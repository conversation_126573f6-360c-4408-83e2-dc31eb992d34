package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionRule;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionRuleService {
    Observable<List<PromotionRule>> list(SQLConnection sqlConnPr, String promotionId);

    Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, PromotionRule promotionRuleDataParam);

    Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId, int ruleId);

    Observable<Integer> updateByField(SQLConnection sqlConnPr, int ruleId, String field, String value);

    Observable<Integer> updateOrder(SQLConnection sqlConnPr, int promotionId, int ruleId, String value);
}
