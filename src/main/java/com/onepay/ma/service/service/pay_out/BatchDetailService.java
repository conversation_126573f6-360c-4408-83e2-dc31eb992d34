package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_out.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface BatchDetailService {
    FundsTransBatchRes insert(Connection connection, FundsTransBatchReq fundsTransBatch) throws SQLException;
    Void updateMsgState(Connection backupConn, String id, String state, String message) throws SQLException;
    Observable<FundsTransBatchRes> updateMsgStateSqlConnect(SQLConnection backupConn, String id, String state, String message) throws SQLException;
    Observable<BaseList<FundsTransBatchRes>> search(SQLConnection sqlConnection, Map<String, String> mIn);
    Observable<Integer> total(SQLConnection onlineConn, Map<String, String> mIn);
    Observable<List<FundsTransBatchRes>> getByBatchId(SQLConnection sqlConnection, Map<String, String> mIn);
    Observable<BaseList<BatchDetailAuthenRes>> getBatchDetailAuthen(SQLConnection sqlConnection, String batchID);
    ResultSet downloadFile(Connection connection, BatchDetailQueryDto query);
}
