package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionCode;
import com.onepay.ma.service.models.PromotionCodeSearchQuery;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by anhkh on 04-Jan-17.
 */
public interface PromotionCodeService {

    Observable<BaseList<PromotionCode>> search(SQLConnection sqlConnection, PromotionCodeSearchQuery query);

    Observable<PromotionCode> get(SQLConnection sqlConnection, String id);

    default Observable<PromotionCode> useCode(SQLConnection sqlConnection, String id) {
        return this.updateCouponCodeStatus(sqlConnection, id, "USED");
    }

    Observable<PromotionCode> updateCouponCodeStatus(SQLConnection sqlConnection, String id, String state);
}
