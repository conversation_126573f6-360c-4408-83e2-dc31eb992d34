package com.onepay.ma.service.service.report.impl;

import com.google.gson.Gson;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.SortUtils;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.mpay.MpayTransaction;
import com.onepay.ma.service.models.report.GeneralReport;
import com.onepay.ma.service.models.report.GeneralReportQuery;
import com.onepay.ma.service.service.report.GeneralReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class GeneralReportServiceImpl implements GeneralReportService {

    private final static String REPORT_GENERAL_SEARCH_V8 = "{call PKG_REPORT.search_general_report_v9(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String SEARCH_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_general_6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_PURCHASE_BY_MSP_ID = "{call PKG_REPORT.get_purchase_by_msp_id(?,?,?,?)}";
    private static final String GET_PURCHASE_BY_REF = "{call PKG_REPORT.get_purchase_by_ref(?,?,?,?,?)}";
    private static final String GET_REFUND_BY_REF = "{call PKG_REPORT.get_purchase_by_ref(?,?,?,?,?)}";
    private final static String DOMESTIC_REQUEST_REFUND_SEARCH = "{call PKG_REPORT.search_nd_request_refund(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(GeneralReportServiceImpl.class.getName());

    @Override
    public Observable<BaseList<GeneralReport>> searchGeneralReport(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection sqlBackup, GeneralReportQuery query) {
        BaseList<GeneralReport> transactions = new BaseList<>();

        // GET TOTAL BACKUP
        return totalTransBackup(sqlBackup, query).flatMap(totalBackup -> {

            return searchDomesticRequestRefundTotal(connOnline, query).flatMap(totalDomesticRefundOP -> {

                return searchGeneralReportTotal(connOnline, query).flatMap(totalItem1 -> {
                    // return searchGeneralReportTotal(connReadOnly, query, 1).flatMap(totalItem2 -> {

                        transactions.setTotal_items(totalItem1 +  totalBackup + totalDomesticRefundOP);
                        LOGGER.log(Level.INFO, "------------------START GET  refund Approval List----------------- ");
                        return getListTransactionBackup(sqlBackup, query).flatMap(refundApproval -> {
                            query.setOffset(totalBackup);

                            return searchDomesticRequestRefundData(connOnline, query).flatMap(domesticRefundOP -> {

                                query.setOffset(totalBackup + totalDomesticRefundOP);
                                return searchGeneralReportData(connOnline, query).flatMap(transactionsListOnline -> {
                                    // offset
                                    query.setOffset(totalBackup + totalDomesticRefundOP + totalItem1);
                                    // return searchGeneralReportData(connReadOnly, query, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                                        List<GeneralReport> transactionsFinal = new ArrayList<>();
                                        // transactionsFinal.addAll(refundApprovalInter);
                                        // transactionsFinal.addAll(refundApproDomestic);
                                        // transactionsFinal.addAll(refundApprovalQR);
                                        transactionsFinal.addAll(refundApproval);
                                        Collections.sort(transactionsFinal, Comparator.comparing(GeneralReport::getState)
                                                .thenComparing((o1, o2) -> SortUtils.compareInputType(o1.getInputType(), o2.getInputType()))
                                                .thenComparing(Comparator.comparing(GeneralReport::getTransactionDate).reversed()));

                                        transactionsFinal.addAll(domesticRefundOP);
                                        // transactionsFinal.sort((o1, o2) -> o2.getTransactionDate().compareTo(o1.getTransactionDate()));
                                        transactionsFinal.addAll(transactionsListOnline);
                                        // transactionsFinal.addAll(transactionsListReadOnly);
                                        // transactionsFinal.sort((o1, o2) -> o2.getTransactionDate().compareTo(o1.getTransactionDate()));
                                        transactions.setList(transactionsFinal);
                                        return Observable.just(transactions);
                                    });
                                });
                            });
                        // });

                    // });
                });
            });
        });

    }

    @Override
    public Observable<Integer> totalRecord(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection sqlBackup, GeneralReportQuery query) {
        return totalTransBackup(sqlBackup, query).flatMap(totalApproval -> {

            return searchDomesticRequestRefundTotal(connOnline, query).flatMap(totalDomesticRefundOP -> {

                return searchGeneralReportTotal(connOnline, query).map(totalItem1 -> {

                        Integer totalBackup = totalDomesticRefundOP + totalItem1  + totalApproval;
                        return totalBackup;
                });
            });
        });
    }

    public Observable<List<GeneralReport>> searchGeneralReportData(SQLConnection sqlConnection, GeneralReportQuery query) {
        JsonArray inParams = new JsonArray()
                .add(0)
                .add(QueryMethod.SELECT.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdQt())
                .add(query.getMerchantIdNd())
                .add(query.getMerchantIdQr())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getVersion())
                .add(query.getTarget())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(REPORT_GENERAL_SEARCH_V8, inParams, outParams).map(result -> {

            List<GeneralReport> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(21);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                GeneralReport transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }


            return transactionList;

        });
    }

    private Observable<Integer> searchGeneralReportTotal(SQLConnection sqlConnection, GeneralReportQuery query) {
        JsonArray inParams = new JsonArray()
                .add(0)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdQt())
                .add(query.getMerchantIdNd())
                .add(query.getMerchantIdQr())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getVersion())
                .add(query.getTarget())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(REPORT_GENERAL_SEARCH_V8, inParams, outParams).map(result -> {

            Integer total = 0;

            Integer reusltCode = result.getOutput().getInteger(21);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT TOTAL ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }


            return total;

        });
    }

    private Observable<Integer> searchDomesticRequestRefundTotal(SQLConnection sqlConnection, GeneralReportQuery query) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdNd())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(DOMESTIC_REQUEST_REFUND_SEARCH, inParams, outParams).map(result -> {

            Integer total = 0;

            Integer reusltCode = result.getOutput().getInteger(15);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT TOTAL ERROR]: " + result.getOutput().getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }


            return total;

        });
    }

    public Observable<List<GeneralReport>> searchDomesticRequestRefundData(SQLConnection sqlConnection, GeneralReportQuery query) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdNd())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(DOMESTIC_REQUEST_REFUND_SEARCH, inParams, outParams).map(result -> {

            List<GeneralReport> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(15);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT ERROR]: " + result.getOutput().getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                GeneralReport transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }


            return transactionList;

        });
    }


    @Override
    public ResultSet downloadDomesticRequestRefundData(Connection connection, GeneralReportQuery query) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdNd())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, DOMESTIC_REQUEST_REFUND_SEARCH, inParams, outParams);


            int code = callableStatement.getInt(16);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD GENERAL REPORT LIST ERROR]: " + callableStatement.getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(15);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }


    private Observable<Integer> totalTransBackup(SQLConnection connBackup, GeneralReportQuery parameter) {
        String status = "0";
        if (null == parameter.getTransactionState() || parameter.getTransactionState().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "405";
        }
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantIdQt())
                .add(parameter.getMerchantIdNd())
                .add(parameter.getMerchantIdQr())
                .add(parameter.getTransactionId())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getTransactionType())
                .add(status)
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getVersion())
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(SEARCH_TRANSACTION_BACKUP, inParams, outParams).map(result -> {

            Integer total = 0;
            Integer reusltCode = result.getOutput().getInteger(16);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT TOTAL BACKUP ERROR]: " + result.getOutput().getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(15).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }

    private Observable<List<GeneralReport>> getListTransactionBackup(SQLConnection connBackup, GeneralReportQuery parameter) {

        String status = "0";
        if (null == parameter.getTransactionState() || parameter.getTransactionState().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "405";
        }

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantIdQt())
                .add(parameter.getMerchantIdNd())
                .add(parameter.getMerchantIdQr())
                .add(parameter.getTransactionId())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getTransactionType())
                .add(status)
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getVersion())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(SEARCH_TRANSACTION_BACKUP, inParams, outParams).map(result -> {
            List<GeneralReport> transactionList = new ArrayList<>();

            Integer reusltCode = result.getOutput().getInteger(16);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET GENERAL REPORT LIST BACKUP ERROR]: " + result.getOutput().getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(15).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                GeneralReport transaction = bindTransactionRequestRefund(jsonObject);
                transactionList.add(transaction);
            }
            return transactionList;
        });
    }

    @Override
    public ResultSet download(Connection connection, GeneralReportQuery query) throws SQLException {


        JsonArray inParams = new JsonArray()
                .add(0)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getTransactionId())
                .add(query.getMerchantIdQt())
                .add(query.getMerchantIdNd())
                .add(query.getMerchantIdQr())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getCardType())
                .add(query.getOrderInfo())
                .add(query.getMerchantTransactionRef())
                .add(query.getCardNumber())
                .add(query.getTransactionType())
                .add(query.getTransactionState())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getVersion())
                .add(query.getTarget())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_GENERAL_SEARCH_V8, inParams, outParams);


            int code = callableStatement.getInt(22);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD GENERAL REPORT LIST ERROR]: " + callableStatement.getString(23));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(21);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    @Override
    public ResultSet downloadBackp(Connection connection, GeneralReportQuery parameter) throws SQLException {

        String status = "0";
        if (null == parameter.getTransactionState() || parameter.getTransactionState().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "401";
        } else if ("Waiting for onepay's approval".equalsIgnoreCase(parameter.getTransactionState())) {
            status = "405";
        }

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantIdQt())
                .add(parameter.getMerchantIdNd())
                .add(parameter.getMerchantIdQr())
                .add(parameter.getTransactionId())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getTransactionType())
                .add(status)
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getVersion())
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_TRANSACTION_BACKUP, inParams, outParams);

            int code = callableStatement.getInt(17);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD GENERAL REPORT LIST DOWNLOAD ERROR]: " + callableStatement.getString(18));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(16);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private GeneralReport bindTransaction(JsonObject rs) {
        GeneralReport transaction = new GeneralReport();

        Amount amount = new Amount();
        amount.setTotal(rs.getDouble("N_AMOUNT"));
        amount.setCurrency(rs.getString("S_CURRENCY"));

        transaction.setCardNumber(rs.getString("S_CARD_NUMBER"));
        transaction.setMerchantId(rs.getString("S_MERCHANT_ID"));
        transaction.setId(rs.getString("S_TRANSACTION_ID"));
        transaction.setOriginalId(rs.getString("N_ORIGINAL_ID"));
        transaction.setOrderInfo(rs.getString("S_ORDER_INFO"));
        transaction.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
        transaction.setState(rs.getString("S_TRANSACTION_STATE") == null ? "" : rs.getString("S_TRANSACTION_STATE"));
        transaction.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        transaction.setInputType(rs.getString("S_INPUT_TYPE"));
        transaction.setResponseCode(rs.getString("S_RESPONSE_CODE") == null ? "" : rs.getString("S_RESPONSE_CODE"));
        transaction.setTransactionDate(rs.getString("D_TRANSACTION_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_TRANSACTION_DATE")));
        transaction.setAmount(amount);
        if (Objects.equals(rs.getString("PAYMENT_CHANNEL"), "QR"))
            transaction.setCardType(rs.getString("S_APP_NAME") == null ? "" : rs.getString("S_APP_NAME"));
        else
            transaction.setCardType(rs.getString("S_CARD_TYPE"));

        return transaction;
    }

    private GeneralReport bindTransactionRequestRefund(JsonObject rs) {
        GeneralReport transaction = new GeneralReport();

        Amount amount = new Amount();
        String originalId = rs.getString("N_ORIGINAL_ID");

        amount.setTotal(rs.getDouble("N_AMOUNT"));
        amount.setCurrency(rs.getString("S_CURRENCY"));
        transaction.setCardNumber(rs.getString("S_CARD_NUMBER"));
        transaction.setMerchantId(rs.getString("S_MERCHANT_ID"));
        transaction.setId(rs.getString("S_TRANSACTION_ID"));
        transaction.setOrderInfo(rs.getString("S_ORDER_INFO"));
        transaction.setMerchantTransactionRef(rs.getString("S_MERCHANT_TRANS_REF"));
        transaction.setState(rs.getString("S_TRANSACTION_STATE"));
        transaction.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        transaction.setInputType(rs.getString("S_INPUT_TYPE"));
        transaction.setTransactionDate(rs.getString("D_TRANSACTION_DATE") == null ? null : Timestamp.valueOf(rs.getString("D_TRANSACTION_DATE")));
        transaction.setAmount(amount);
        transaction.setOriginalId(originalId);

        transaction.setAppName(rs.getString("S_APP_NAME") == null ? "" : rs.getString("S_APP_NAME"));
        transaction.setCardType(rs.getString("S_CARD_TYPE"));
        transaction.setCardNumber(rs.getString("S_CARD_NUMBER"));
        return transaction;
    }



    private Integer countFilterById(Map<Integer, InternationalTransaction> totalPurchaseMap, Map<Integer, DomesticTransaction> totalPurchaseDomesticMap, Map<String, MpayTransaction> totalPurchaseMpayMap, List<String> totalApproval, List<String> totalApprovalND, List<String> totalApprQR) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        if (!totalApprovalND.isEmpty()) {
            for (String transactionId : totalApprovalND) {
                if (totalPurchaseDomesticMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        if (null != totalApprQR && !totalApprQR.isEmpty()) {
            for (String transactionId : totalApprQR) {
                if (totalPurchaseMpayMap.get(transactionId) != null) {
                    result++;
                }
            }
        }
        return result;
    }


    private GeneralReport joinApproveInterWithPurchase(GeneralReport approve, InternationalTransaction purchase) {


        if (purchase == null) {
            return null;
        }

        GeneralReport result = approve;
        // if (0 != purchase.getOriginal_transaction_id()) {
        // result.setId(String.valueOf(purchase.getOriginal_transaction_id()));
        // }
        result.setOrderInfo(purchase.getOrder_info());
        result.setCardNumber(purchase.getCard().getCard_number());
        result.setCardType(purchase.getCard().getCard_type());
        return result;
    }

    private GeneralReport joinApproveDoWithPurchase(GeneralReport approve, DomesticTransaction purchase) {


        if (purchase == null) {
            return null;
        }

        GeneralReport result = approve;

        result.setOrderInfo(purchase.getOrder_info());
        result.setCardNumber(purchase.getCard().getCard_number());
        result.setCardType(String.valueOf(purchase.getAcquirer().getAcquirer_id()));
        return result;
    }

    private GeneralReport joinApproveDoWithMpay(GeneralReport approve, MpayTransaction purchase) {

        if (purchase == null) {
            return null;
        }

        GeneralReport result = approve;

        result.setOrderInfo(purchase.getOrderInfo());
        result.setCardNumber(purchase.getInstrument().getNumber());
        result.setCardType(purchase.getAppName());
        return result;
    }

    private Gson gson = new Gson();



    @Override
    public Observable<Integer> getPurchaseByMspId(SQLConnection sqlOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlOnline.callWithParamsObservable2(GET_PURCHASE_BY_MSP_ID, inParams, outParams).map(result -> {
            Integer resultS = null;

            Integer reusltCode = result.getOutput().getInteger(2);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[get_purchase_by_ref]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                resultS = jsonObject.getInteger("N_TRANSACTION_ID");
            }
            return resultS;
        });
    }

    @Override
    public Observable<String> getPurchaseByRef(SQLConnection sqlOnline, String merchantId, String transactionRef) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(transactionRef);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlOnline.callWithParamsObservable2(GET_PURCHASE_BY_REF, inParams, outParams).map(result -> {
            String resultS = null;

            Integer reusltCode = result.getOutput().getInteger(3);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[get_purchase_by_ref]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                resultS = jsonObject.getString("S_ID");
            }
            return resultS;
        });
    }

    @Override
    public Observable<String> getRefundByRef(SQLConnection sqlOnline, String merchantId, String transactionRef) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(transactionRef);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlOnline.callWithParamsObservable2(GET_REFUND_BY_REF, inParams, outParams).map(result -> {
            String resultS = null;

            Integer reusltCode = result.getOutput().getInteger(3);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[get_purchase_by_ref]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                resultS = jsonObject.getString("S_ID");
            }
            return resultS;
        });
    }



}
