package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.service.PromotionDiscountParamService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/10/16.
 */
@Service
public class PromotionDiscountParamServiceImpl implements PromotionDiscountParamService {

    @Override
    public Observable<List<Map<String, Object>>> list(SQLConnection sqlConnPr, int discountId) {
        return getListPromotionDiscountParam(sqlConnPr, discountId);
    }

    @Override
    public Observable<Integer> insert(SQLConnection sqlConnPr, int discountId, Map<String, Object> discountParam) {
        return insertPromotionDiscountParam(sqlConnPr, discountId, discountParam);
    }

    /**
     *
     * @param discountParam
     * @return
     */
    private Observable<Integer> insertPromotionDiscountParam(SQLConnection connection, int discountId, Map<String, Object> discountParam){
        String paramOrder = discountParam.get(ParamsPool.PROMOTION_PARAM_ORDER) != null ? discountParam.get(ParamsPool.PROMOTION_PARAM_ORDER).toString() : StringPool.BLANK;
        List<Map<String, Object>> listDataParam = new ArrayList<>();
        for (Map.Entry<String, Object> mapEntry : discountParam.entrySet()) {
            Map map = new HashMap<>();
            map.put(ParamsPool.PROMOTION_PARAM_ORDER, paramOrder);
            map.put(ParamsPool.PROMOTION_PARAM_NAME, mapEntry.getKey().toUpperCase());
            map.put(ParamsPool.PROMOTION_PARAM_VALUE, mapEntry.getValue());
            listDataParam.add(map);
        }

        return insertListPromotionParamData(connection, discountId, listDataParam, 0).map(maps -> {
            return discountId;
        });

    }

    /**
     * insert list data async
     * @param conn
     * @param discountId
     * @param listParamData
     * @param index
     * @return
     */
    private Observable<List<Map<String,Object>>> insertListPromotionParamData(SQLConnection conn, int discountId, List<Map<String,Object>> listParamData, int index){
        if(listParamData.size() <= 0){
            return Observable.just(listParamData);
        }
        Map<String, Object> map = listParamData.get(index);
        final int finalIndex = index;
        return Observable.just(map).flatMap(serviceApproval -> {
            //insert promotion data param
            return insertPromotionParamDiscountData(conn, discountId, map).flatMap(integer -> {
                if(finalIndex >= listParamData.size() - 1){
                    return Observable.just(listParamData);
                }else{
                    return insertListPromotionParamData(conn, discountId, listParamData, finalIndex + 1);
                }
            });
        });
    }

    /**
     * insert promotion param discount
     * @param conn
     * @param discountId
     * @param map
     * @return
     */
    private Observable<Integer> insertPromotionParamDiscountData(SQLConnection conn, int discountId, Map<String, Object> map){
        JsonArray inParams = new JsonArray()
                .add(discountId)
                .add(map.get(ParamsPool.PROMOTION_PARAM_NAME))
                .add(map.get(ParamsPool.PROMOTION_PARAM_VALUE))
                .add(map.get(ParamsPool.PROMOTION_PARAM_ORDER));
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return conn.callWithParamsObservable2(DISCOUNT_PARAM_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(4);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[PROMOTION DISCOUNT PARAM] => Insert Discount Param Promotion Failed" + StringPool.SPACE + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(6);
            }

            return returnValue;
        });
    }

    /**
     * get list promotion discount param
     * @param sqlConnPr
     * @param discountId
     * @return
     */
    private Observable<List<Map<String, Object>>> getListPromotionDiscountParam(SQLConnection sqlConnPr, int discountId){
        JsonArray inParams = new JsonArray()
                .add(discountId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR) ;
        return sqlConnPr.callWithParamsObservable2(DISCOUNT_PARAM_LIST, inParams, outParams).map(result -> {

            List<Map<String, Object>> promotionDiscountParamList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            int index = 0;
            Map paramInfoMap = new HashMap<>();
            int index2 = 0;
            for (JsonObject jsonObject : rs.getRows()){
                String name = jsonObject.getString("S_NAME");
                Double value = jsonObject.getString("S_VALUE") != null ? Double.valueOf(jsonObject.getString("S_VALUE")): null;
                int order = jsonObject.getInteger("N_ORDER");
                if(order != index){
                    if(!paramInfoMap.isEmpty()){
                        promotionDiscountParamList.add(paramInfoMap);
                    }

                    index = order;
                    paramInfoMap = new HashMap<>();
                    paramInfoMap.put(ParamsPool.PROMOTION_PARAM_ORDER, index);
                    paramInfoMap.put(name.toLowerCase(), value);
                }else{
                    paramInfoMap.put(name.toLowerCase(), value);
                }
                index2++;
                if(index2 > rs.getRows().size() - 1){
                    promotionDiscountParamList.add(paramInfoMap);
                }

            }

            return promotionDiscountParamList;
        });
    }


    private static final String DISCOUNT_PARAM_LIST = "{call ONEPR.PKG_ONEPR_ADMIN.DISCOUNT_PARAM_GET(?,?,?,?)}";

    private static final String DISCOUNT_PARAM_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.DISCOUNT_PARAM_INSERT(?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PromotionDiscountParamServiceImpl.class.getName());


}
