package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.Terminal;
import com.onepay.ma.service.models.Terminals;
import com.onepay.ma.service.service.TerminalService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by anhkh on 7/12/2016.
 */
@Service
public class TerminalServiceImpl implements TerminalService {


    @Override
    public Observable<List<Terminal>> getTerminalsByIds(SQLConnection connOnline, String ids) {
        JsonArray inParams = new JsonArray()
                .add(ids);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TERMINAL_BY_IDS, inParams, outParams).map(result -> {
            List<Terminal> terminalList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Terminal terminal = new Terminal();
                    terminal.setTerminal_id(jsonObject.getString("S_TERMINAL_ID"));
                    terminal.setTerminal_name(jsonObject.getString("S_TERMINAL_NAME"));
                    terminal.setMerchant_id(jsonObject.getString("S_MERCHANT_ID"));
                    terminalList.add(terminal);
                }
            }

            // Return.
            return terminalList;
        });
    }

    /** {@inheritDoc} */
    @Override
    public Observable<Terminals> getTerminalsByMerchant(SQLConnection connBackUp, String keywords, int page, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(keywords)
                .add(page)
                .add(50);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_TERMINAL, inParams, outParams).map(result -> {
            List<Terminal> terminalList = new ArrayList<>();
            Terminals terminals = new Terminals();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Terminal terminal = bindTerminal(jsonObject, merchantId);
                    terminalList.add(terminal);
                }
                terminals.setTotal_items(result.getOutput().getInteger(4));
            } else {
                terminals.setTotal_items(0);
            }
            terminals.setTerminals(terminalList);

            // Return.
            return terminals;
        });
    }

    @Override
    public Observable<List<Terminal>> listTerminalIdsByMerchant(SQLConnection connBackUp, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackUp.callWithParamsObservable2(LIST_TERMINAL_BY_MERCHANT, inParams, outParams).map(result -> {
            List<Terminal> terminalList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Terminal terminal = new Terminal();
                    terminal.setTerminal_id(jsonObject.getString("S_TERMINAL_ID"));
                    terminal.setMerchant_id(jsonObject.getString("S_MERCHANT_ID"));
                    terminalList.add(terminal);
                }
            }

            // Return.
            return terminalList;
        });
    }

    private Terminal bindTerminal(JsonObject jsonObject, String merchantId) {
        Terminal terminal = new Terminal();
        terminal.setTerminal_id(jsonObject.getString("S_TERMINAL_ID"));
        terminal.setTerminal_name(jsonObject.getString("S_TERMINAL_NAME"));
        terminal.setMerchant_id(merchantId);

        return  terminal;
    }

    private static final String LIST_TERMINAL = "{call PKG_TERMINAL.get_list_ter_mpay_page(?,?,?,?,?,?,?,?)}";

    private static final String LIST_TERMINAL_BY_MERCHANT = "{call PKG_TERMINAL.get_list_by_merchant(?,?,?,?)}";

    private static final String LIST_TERMINAL_BY_IDS = "{call PKG_TERMINAL.get_list_by_ids(?,?,?,?)}";
}
