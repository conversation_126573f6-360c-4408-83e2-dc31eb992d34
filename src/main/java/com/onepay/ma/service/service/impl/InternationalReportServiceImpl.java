package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.InternationalReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON>uy<PERSON> on 4/2/16.
 */
@Service
public class InternationalReportServiceImpl implements InternationalReportService {
    @Override
    public Observable<List<InternationalReport>> listByMerchant(SQLConnection connection, InternationalReportParameter parameter) {
        return listReportByMerchant(connection, parameter);
    }

    @Override
    public Observable<List<InternationalReport>> listByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        return listReportByCurrency(connection, parameter);
    }

    @Override
    public Observable<List<InternationalReport>> listByCurrencyDetail(SQLConnection connection, InternationalReportParameter parameter) {
        return listReportByCurrencyDetail(connection, parameter);
    }

    @Override
    public Observable<Integer> getTotalByMerchant(SQLConnection connection, InternationalReportParameter parameter) {
        return getTotalDataByMerchant(connection, parameter);
    }

    @Override
    public Observable<Integer> getTotalByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        return getTotalDataByCurrency(connection, parameter);
    }

    @Override
    public List<InternationalReport> downloadByMerchant(Connection connection, InternationalReportParameter parameter) throws SQLException {
        return downloadDataByMerchant(connection, parameter);
    }

    @Override
    public List<InternationalReport> downloadByCurrency(Connection connection, InternationalReportParameter parameter) throws SQLException {
        return downloadDataByCurrency(connection, parameter);
    }

    @Override
    public ResultSet downloadDetail(Connection connection, InternationalReportParameter parameter) throws SQLException {
        return this.downloadDataDetail(connection, parameter);
    }

    /**
     * get total data total
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<Integer> getTotalDataByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        String version = parameter.getVersion() == null ? "v1" : parameter.getVersion();
        JsonArray inParams = new JsonArray()
                .add(version)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_INTERNATIONAL_CURRENCY_2, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(10);
            if(resultCode != 200) {
                LOGGER.severe("REPORT TOTAL CURRENCY ERROR: " + result.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            Map map = result.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private List<InternationalReport> downloadDataByMerchant(Connection connection, InternationalReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        List<InternationalReport> internationalReports = new ArrayList<>();
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_INTERNATIONAL_MERCHANT, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(9);
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String currency = "";
            String merchantId = "";
            boolean recordsAvailable = rs.next();

            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = rs.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    }
                    if (!rs.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        InternationalReport internationalReport = bindReport(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }

                date = rs.getString("D_DATE");

                String cardTypeData = rs.getString("S_CARDTYPE").toLowerCase();

                if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                    cardTypeData = "Amex";
                } else if (cardTypeData.equals("mastercard")) {
                    cardTypeData = "MasterCard";
                } else if (cardTypeData.equals("visa")) {
                    cardTypeData = "Visa";
                } else if (cardTypeData.equals("jcb")) {
                    cardTypeData = "JCB";
                }
                cardType = cardTypeData;
                currency = rs.getString("S_CURRENCY");
                merchantId = rs.getString("S_MERCHANTID");
                if (rs.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", rs.getInt("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", rs.getDouble("N_AMOUNT"));
                }
                if (!rs.next()) {
                    recordsAvailable = false;
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    InternationalReport internationalReport = bindReport(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (rs != null) {
                rs.close();
            }
        }


        return internationalReports;

    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadDataDetail(Connection connection, InternationalReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_INTERNATIONAL_DETAIL, inParams, outParams);

            int code = callableStatement.getInt(9);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "REPORT DETAIL ERROR: " + callableStatement.getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(8);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return rs;
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private List<InternationalReport> downloadDataByCurrency(Connection connection, InternationalReportParameter parameter) throws SQLException {
        String version = parameter.getVersion() == null ? "v1" : parameter.getVersion();
        JsonArray inParams = new JsonArray()
                .add(version)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;

        List<InternationalReport> internationalReports = new ArrayList<>();
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_INTERNATIONAL_CURRENCY_2, inParams, outParams);

            rs = (ResultSet) callableStatement.getObject(10);
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String currency = "";
            String merchantId = "";
            boolean recordsAvailable = rs.next();

            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = rs.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    }
                    if ("v2".equals(version)) {
                        String mid = rs.getString("S_MERCHANTID");
                        if (!rs.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase()) || !mid.equals(merchantId)) {
                            objectData.put("S_MERCHANT_ID", merchantId);
                            objectData.put("S_CARD_TYPE", cardType);
                            objectData.put("S_CURRENCY", currency);
                            objectData.put("D_DATE", date);
                            objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                            objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                            objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                            objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                            objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                            objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                            objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                            objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                            objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                            objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                            InternationalReport internationalReport = bindReport(objectData);
                            internationalReports.add(internationalReport);
                            objectData = new JsonObject();
                        }

                    } else {
                        if (!rs.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())) {
                            objectData.put("S_MERCHANT_ID", merchantId);
                            objectData.put("S_CARD_TYPE", cardType);
                            objectData.put("S_CURRENCY", currency);
                            objectData.put("D_DATE", date);
                            objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                            objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                            objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                            objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                            objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                            objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                            objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                            objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                            objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                            objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                            InternationalReport internationalReport = bindReport(objectData);
                            internationalReports.add(internationalReport);
                            objectData = new JsonObject();
                        }
                    }
                }

                date = rs.getString("D_DATE");

                String cardTypeData = rs.getString("S_CARDTYPE").toLowerCase();

                if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                    cardTypeData = "Amex";
                } else if (cardTypeData.equals("mastercard")) {
                    cardTypeData = "MasterCard";
                } else if (cardTypeData.equals("visa")) {
                    cardTypeData = "Visa";
                } else if (cardTypeData.equals("jcb")) {
                    cardTypeData = "JCB";
                }
                cardType = cardTypeData;
                currency = rs.getString("S_CURRENCY");
                merchantId = rs.getString("S_MERCHANTID");
                if ("Purchase".equalsIgnoreCase(rs.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("N_COUNT_TRAN", rs.getInt("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else if("REFUND".equalsIgnoreCase(rs.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("REFUND_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else if("Authorize".equalsIgnoreCase(rs.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("AUTHORIZE_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("AUTHORIZE_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else if("CAPTURE".equalsIgnoreCase(rs.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("CAPTURE_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", rs.getDouble("N_AMOUNT"));
                } else if("REFUND_CAPTURE".equalsIgnoreCase(rs.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("REFUND_CAPTURE_COUNT", rs.getInt("N_COUNT"));
                    objectData.put("REFUND_CAPTURE_AMOUNT", rs.getDouble("N_AMOUNT"));
                }
                if (!rs.next()) {
                    recordsAvailable = false;
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                    objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                    objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                    InternationalReport internationalReport = bindReport(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }


        } catch (Exception ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
        return internationalReports;
    }

    /**
     * get total data total
     *
     * @param connOnline
     * @param parameter
     * @return
     */
    private Observable<Integer> getTotalDataByMerchant(SQLConnection connOnline, InternationalReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(REPORT_INTERNATIONAL_MERCHANT_TOTAL, inParams, outParams).map(result -> {
            int total = result.getOutput().getInteger(8);

            return total;

        });
    }

    /**
     * list report by merchant
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<List<InternationalReport>> listReportByMerchant(SQLConnection connection, InternationalReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connection.callWithParamsObservable2(REPORT_INTERNATIONAL_MERCHANT, inParams, outParams).map(result -> {
            List<InternationalReport> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String currency = "";
            String merchantId = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    }
                    if (!jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        InternationalReport internationalReport = bindReport(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }

                date = jsonObject.getString("D_DATE");

                String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                    cardTypeData = "Amex";
                } else if (cardTypeData.equals("mastercard")) {
                    cardTypeData = "MasterCard";
                } else if (cardTypeData.equals("visa")) {
                    cardTypeData = "Visa";
                } else if (cardTypeData.equals("jcb")) {
                    cardTypeData = "JCB";
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                merchantId = jsonObject.getString("S_MERCHANTID");
                if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("N_COUNT_TRAN", jsonObject.getInteger("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else {
                    objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    InternationalReport internationalReport = bindReport(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }


            return internationalReports;
        });
    }

    /**
     * list report by merchant
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<List<InternationalReport>> listReportByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        String version = parameter.getVersion() == null ? "v1" : parameter.getVersion();
        JsonArray inParams = new JsonArray()
                .add(version)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

//        if("v2".equals(version)){
        return connection.callWithParamsObservable2(REPORT_INTERNATIONAL_CURRENCY_2, inParams, outParams).map(result -> {
            List<InternationalReport> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            String date = "";
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String merchantId = "";
            String currency = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {

                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = "";
                    if (jsonObject.getString("S_CARDTYPE") != null) {
                        cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                        if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                            cardTypeData = "Amex";
                        } else if (cardTypeData.equals("mastercard")) {
                            cardTypeData = "MasterCard";
                        } else if (cardTypeData.equals("visa")) {
                            cardTypeData = "Visa";
                        } else if (cardTypeData.equals("jcb")) {
                            cardTypeData = "JCB";
                        }
                    }
                    if ("v2".equals(version)) {
                        String mid = jsonObject.getString("S_MERCHANTID");
                        if (!jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase()) || !mid.equals(merchantId)) {
                            objectData.put("S_MERCHANT_ID", merchantId);
                            objectData.put("S_CARD_TYPE", cardType);
                            objectData.put("S_CURRENCY", currency);
                            objectData.put("D_DATE", date);
                            objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                            objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                            objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                            objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                            objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                            objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                            objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                            objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                            objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                            objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                            InternationalReport internationalReport = bindReport(objectData);
                            internationalReports.add(internationalReport);
                            objectData = new JsonObject();
                        }

                    } else {
                        if (!jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())) {
                            objectData.put("S_MERCHANT_ID", merchantId);
                            objectData.put("S_CARD_TYPE", cardType);
                            objectData.put("S_CURRENCY", currency);
                            objectData.put("D_DATE", date);
                            objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                            objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                            objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                            objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                            objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                            objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                            objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                            objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                            objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                            objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                            InternationalReport internationalReport = bindReport(objectData);
                            internationalReports.add(internationalReport);
                            objectData = new JsonObject();
                        }
                    }
                }

                date = jsonObject.getString("D_DATE");

                String cardTypeData = "";
                if (jsonObject.getString("S_CARDTYPE") != null) {
                    cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    }
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                merchantId = jsonObject.getString("S_MERCHANTID");
                if ("Purchase".equalsIgnoreCase(jsonObject.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("N_COUNT_TRAN", jsonObject.getInteger("N_COUNT"));
                    objectData.put("N_TOTAL_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if("REFUND".equalsIgnoreCase(jsonObject.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if("Authorize".equalsIgnoreCase(jsonObject.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("AUTHORIZE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("AUTHORIZE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if("CAPTURE".equalsIgnoreCase(jsonObject.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("CAPTURE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if("REFUND_CAPTURE".equalsIgnoreCase(jsonObject.getString("S_TRANSACTIONTYPE"))) {
                    objectData.put("REFUND_CAPTURE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_CAPTURE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("AUTHORIZE_COUNT", objectData.getInteger("AUTHORIZE_COUNT") == null ? 0 : objectData.getInteger("AUTHORIZE_COUNT"));
                    objectData.put("AUTHORIZE_AMOUNT", objectData.getDouble("AUTHORIZE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORIZE_AMOUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("REFUND_CAPTURE_COUNT", objectData.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : objectData.getInteger("REFUND_CAPTURE_COUNT"));
                    objectData.put("REFUND_CAPTURE_AMOUNT", objectData.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_CAPTURE_AMOUNT"));
                    InternationalReport internationalReport = bindReport(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }

            return internationalReports;
        });
//        }else {
//            return connection.callWithParamsObservable2(REPORT_INTERNATIONAL_CURRENCY, inParams, outParams).map(result -> {
//                List<InternationalReport> internationalReports = new ArrayList<>();
//                Map map = result.getOutput().getJsonObject(9).getMap();
//                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
//                String date = "";
//                JsonObject objectData = new JsonObject();
//                String cardType = "";
//                String currency = "";
//                int index = 0;
//                for (JsonObject jsonObject : rs.getRows()){
//
//                    if(date.isEmpty()){
//                        objectData = new JsonObject();
//                    }else{
//                        String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();
//
//                        if(cardTypeData.equals("american express") || cardTypeData.equals("amex")){
//                            cardTypeData = "Amex";
//                        }else if(cardTypeData.equals("mastercard")){
//                            cardTypeData = "MasterCard";
//                        }else if(cardTypeData.equals("visa")){
//                            cardTypeData = "Visa";
//                        }else if(cardTypeData.equals("jcb")){
//                            cardTypeData = "JCB";
//                        }
//                        if(!jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())){
//                            objectData.put("S_MERCHANT_ID", "");
//                            objectData.put("S_CARD_TYPE", cardType);
//                            objectData.put("S_CURRENCY", currency);
//                            objectData.put("D_DATE", date);
//                            objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 :  objectData.getInteger("N_COUNT_TRAN") );
//                            objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 :  objectData.getDouble("N_TOTAL_AMOUNT") );
//                            objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 :  objectData.getInteger("REFUND_COUNT") );
//                            objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 :  objectData.getDouble("REFUND_AMOUNT") );
//                            InternationalReport internationalReport = bindReport(objectData);
//                            internationalReports.add(internationalReport);
//                            objectData = new JsonObject();
//                        }
//                    }
//
//                    date = jsonObject.getString("D_DATE");
//
//                    String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();
//
//                    if(cardTypeData.equals("american express") || cardTypeData.equals("amex")){
//                        cardTypeData = "Amex";
//                    }else if(cardTypeData.equals("mastercard")){
//                        cardTypeData = "MasterCard";
//                    }else if(cardTypeData.equals("visa")){
//                        cardTypeData = "Visa";
//                    }else if(cardTypeData.equals("jcb")){
//                        cardTypeData = "JCB";
//                    }
//                    cardType = cardTypeData;
//                    currency = jsonObject.getString("S_CURRENCY");
//                    if(jsonObject.getString("S_TRANSACTIONTYPE").equals("Purchase")){
//                        objectData.put("N_COUNT_TRAN", jsonObject.getInteger("N_COUNT"));
//                        objectData.put("N_TOTAL_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
//                    }else{
//                        objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
//                        objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
//                    }
//                    index++;
//                    if( index >= rs.getRows().size()){
//                        objectData.put("S_MERCHANT_ID", "");
//                        objectData.put("S_CARD_TYPE", cardType);
//                        objectData.put("S_CURRENCY", currency);
//                        objectData.put("D_DATE", date);
//                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 :  objectData.getInteger("N_COUNT_TRAN") );
//                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 :  objectData.getDouble("N_TOTAL_AMOUNT") );
//                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 :  objectData.getInteger("REFUND_COUNT") );
//                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 :  objectData.getDouble("REFUND_AMOUNT") );
//                        InternationalReport internationalReport = bindReport(objectData);
//                        internationalReports.add(internationalReport);
//                        objectData = new JsonObject();
//                    }
//                }
//
//                return internationalReports;
//            });
//        }
    }

    /**
     * list report by merchant
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<List<InternationalReport>> listReportByCurrencyDetail(SQLConnection connection, InternationalReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_INTERNATIONAL_CURRENCY_DETAIL, inParams, outParams).map(result -> {
            List<InternationalReport> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(6).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                InternationalReport internationalReport = bindReport(jsonObject);
                internationalReports.add(internationalReport);
            }

            return internationalReports;
        });
    }

    /**
     * bind result set to report
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalReport bindReport(JsonObject rs) {
        InternationalReport report = new InternationalReport();
        report.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        report.setCard_type(rs.getString("S_CARD_TYPE"));
        report.setCurrency(rs.getString("S_CURRENCY"));
        report.setRefund_count((rs.getInteger("REFUND_COUNT") == null ? 0 : rs.getInteger("REFUND_COUNT")) + (rs.getInteger("REFUND_CAPTURE_COUNT") == null ? 0 : rs.getInteger("REFUND_CAPTURE_COUNT")));
        report.setRefund_total((rs.getDouble("REFUND_AMOUNT") == null ? 0 : rs.getDouble("REFUND_AMOUNT")) + (rs.getDouble("REFUND_CAPTURE_AMOUNT") == null ? 0 : rs.getDouble("REFUND_CAPTURE_AMOUNT")));
        report.setTransaction_count(rs.getInteger("N_COUNT_TRAN"));
        report.setTransaction_total(rs.getDouble("N_TOTAL_AMOUNT"));
        if (rs.containsKey("AUTHORIZE_COUNT"))
            report.setAuthorize_count(rs.getInteger("AUTHORIZE_COUNT"));
        if (rs.containsKey("AUTHORIZE_AMOUNT"))
            report.setAuthorize_total(rs.getDouble("AUTHORIZE_AMOUNT"));
        if (rs.containsKey("CAPTURE_COUNT"))
            report.setCapture_count(rs.getInteger("CAPTURE_COUNT"));
        if (rs.containsKey("CAPTURE_AMOUNT"))
            report.setCapture_total(rs.getDouble("CAPTURE_AMOUNT"));

        // DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        report.setReport_date(java.sql.Timestamp.valueOf(rs.getString("D_DATE")));
//        try {
//            report.setReport_date( formatter.parse(rs.getString("D_DATE")));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        return report;
    }

    private final static String REPORT_INTERNATIONAL_MERCHANT = "{call PKG_ONECREDIT.SEARCH_REPORTS_MERCHANT_2(?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_INTERNATIONAL_DETAIL = "{call PKG_ONECREDIT.SEARCH_REPORTS_DETAIL(?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_INTERNATIONAL_CURRENCY = "{call PKG_ONECREDIT.search_reports_currency(?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_INTERNATIONAL_CURRENCY_2 = "{call PKG_ONECREDIT.search_reports_currency_2(?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_INTERNATIONAL_MERCHANT_TOTAL = "{call PKG_ONECREDIT.SEARCH_REPORTS_MERCHANT_TOTAL(?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_INTERNATIONAL_CURRENCY_DETAIL = "{call PKG_ONECREDIT.SEARCH_REPORTS_CURRENCY_DETAIL(?,?,?,?,?,?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(InternationalReportServiceImpl.class.getName());


}
