package com.onepay.ma.service.service.partner;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.models.partner.Partners;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

public interface PartnerService {
    Observable<Partners> list(SQLConnection sqlConnection, int page, int pageSize, String keyword);

    Observable<Partner> getPartnerById(SQLConnection sqlConnectionB, Integer id);
}
