package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.InternationalTransaction;
import com.onepay.ma.service.service.MidService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.Map;

/**
 * Created by anhkh on 01-Apr-17.
 */
@Service
public class MidServiceImpl implements MidService {
    @Override
    public Observable<String> getByMerchantId(SQLConnection sqlConnection, String merchantId) {
        JsonArray inParams = new JsonArray()
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_BY_MERCHANT_ID, inParams, outParams).map(result -> {
            InternationalTransaction transaction = null;
            String midNo = result.getOutput().getString(1);


            return midNo;

        });
    }


    private static final String GET_BY_MERCHANT_ID = "{call PKG_ONEPARTNER.get_mid_by_merchant_id(?,?,?,?) }";
}
