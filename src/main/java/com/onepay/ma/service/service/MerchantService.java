package com.onepay.ma.service.service;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.MerchantData;
import com.onepay.ma.service.models.Merchants;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.mpay.MpayMerchant;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface MerchantService {
    Observable<List<Merchant>> list(SQLConnection connOnline, SQLConnection connBackUp, String merchantId, String userId, String type);
    Observable<List<Merchant>> listOnline(SQLConnection connOnline, String merchantId, String userId, String type, List<Merchant> merchantBackUp);
    Observable<List<Merchant>> listBackUp(SQLConnection connBackUp, String merchantId, String userId, String type);
    Observable<Merchants> listMerchantData(SQLConnection connOnline, String keywords, int page, String type, String currency);
    Observable<List<Merchant>> listAll(SQLConnection connBackUp, String userId);
    Observable<Merchant> get(SQLConnection connBackUp, String merchantId);
    Observable<MpayMerchant> getMpayMerchant(SQLConnection readonlyConn, String merchantId);
    Observable<MerchantData> getData(SQLConnection connOnline, String merchantId);
    Observable<MerchantData> getDomesticData(SQLConnection connOnline, String merchantId);
    Observable<BaseList<String>> getCurrenciesByMerchantId(SQLConnection connOnline, String merchantId);
    Observable<List<Merchant>> listMerchantMpayByIds(SQLConnection connOnline, String merchantId);
    Observable<List<Merchant>> listAllByUserId(SQLConnection connBackUp, String userId);
    Observable<Merchants> listMerchantByListMerchantId(SQLConnection connOnline, SQLConnection connBackUp,SQLConnection connPaycollect, String userId, String keywords, int page,  String type, String currency);
    Observable<List<Merchant>> merchantPaycollects(SQLConnection connPaycollect, SQLConnection connBackUp, String merchantId, String userId, String type);
    Observable<List<Merchant>> listMerchantVietQrByIds(SQLConnection connOnline, String merchantId);
    Observable<Merchants> listAllMerchantPaycollect(SQLConnection connBackUp, SQLConnection connPaycollect, String type, String userId);
}
