package com.onepay.ma.service.service.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.google.gson.Gson;
import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.models.CardDate;
import com.onepay.ma.service.models.DomesticCard;
import com.onepay.ma.service.models.DomesticRefund;
import com.onepay.ma.service.models.DomesticRefundParameter;
import com.onepay.ma.service.models.DomesticTransaction;
import com.onepay.ma.service.models.RefundAmount;
import com.onepay.ma.service.models.RefundData;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.DomesticRefundService;
import com.onepay.ma.service.service.domestic.DomesticTransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import org.springframework.stereotype.Service;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;

/**
 * Created by huynguyen on 4/3/16.
 */
@Service
public class DomesticRefundServiceImpl implements DomesticRefundService {


    @Override
    public Observable<Transactions<DomesticRefund>> list(SQLConnection sqlReadOnly, SQLConnection sqlOnline, SQLConnection sqlBackup, DomesticRefundParameter parameter) {
        Transactions<DomesticRefund> transactions = new Transactions();

        int basePageSize = parameter.getPageSize();

        // filter by approval

        LOGGER.log(Level.INFO, "------------------START GET TOTAL Domestic refund Approval List----------------- ");

        return getListTotalTransactionBackup(sqlBackup, parameter).flatMap(totalApproval -> {
            LOGGER.log(Level.INFO, "GET TOTAL Domestic refund Approval: " + gson.toJson(totalApproval) + " IDS : " + String.join(StringPool.COMMA, totalApproval));
            LOGGER.log(Level.INFO, "------------------END GET TOTAL Domestic refund Approval List----------------- ");
            // filter by ACQUIRER_ID , card number, order info with original transaction

            LOGGER.log(Level.INFO, "------------------START GET TOTAL Domestic TRansaction List By Ids----------------- ");
            return DomesticTransactionService.mapByIds(sqlOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(totalPurchaseMap -> {
                LOGGER.log(Level.INFO, "DATA GET TOTAL Domestic Transaction List By Ids: " + totalPurchaseMap.size());

                LOGGER.log(Level.INFO, "------------------END  GET TOTAL Domestic Transaction List By Ids----------------- ");


                LOGGER.log(Level.INFO, "------------------START GET TOTAL Domestic Refund Online----------------- ");
                return getListTotalTransaction(sqlOnline, parameter, -1).flatMap(totalItem1 -> {
                    LOGGER.log(Level.INFO, "------------------END  GET TOTAL Domestic Refund Online " + totalItem1 + "----------------- ");

                    LOGGER.log(Level.INFO, "------------------START GET TOTAL Domestic Refund READONLY----------------- ");
                    return getListTotalTransaction(sqlReadOnly, parameter, totalItem1).flatMap(totalItem2 -> {
                        LOGGER.log(Level.INFO, "------------------END  GET TOTAL Domestic Refund READONLY " + totalItem2 + "----------------- ");

                        Integer totalBackup = countFilterById(totalPurchaseMap, totalApproval);

                        transactions.setTotal_items(totalItem1 + totalItem2 + totalBackup);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_REFUND_ONLINE: " + totalItem1);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_REFUND_READONLY: " + totalItem2);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_REFUND_BACKUP: " + totalBackup);

                        LOGGER.log(Level.INFO, "------------------START GET Domestic refund Approval List----------------- ");
                        return getListTransactionBackup(sqlBackup, parameter).flatMap(refundApproval -> {

                            LOGGER.log(Level.INFO, "------------------END GET Domestic refund Approval List----------------- ");
                            // LOGGER.log(Level.INFO, "Domestic refund Approval List: " + gson.toJson(refundApproval));
                            // List of Purchase id
//                            List<String> ids = refundApproval.stream()
//                                    .map(a -> a.getOriginal_id() + StringPool.BLANK)
//                                    .collect(Collectors.toList());
//                            String purchaseIds = String.join(StringPool.COMMA, ids);
//                            LOGGER.log(Level.WARNING, "Domestic refund Approval  Original Ids: " + ids);

                            // Get map trans info By ids
                            return DomesticTransactionService.mapByIds(sqlOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(purchaseMap -> {
                                // LOGGER.log(Level.INFO, "Domestic Transaction List By Ids: " + gson.toJson(purchaseMap));

                                LOGGER.log(Level.INFO, "----------------------START JOIN ---------------------------");
                                // JOIN purchase INFO ------->  filter != null
                                List<DomesticRefund> refundApproval2 = refundApproval
                                        .stream()
                                        .map(approval -> {
                                            return this.joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_id()));
                                        })
                                        .filter(domesticRefund -> domesticRefund != null)
                                        .collect(Collectors.toList());


                                LOGGER.log(Level.INFO, "----------------------END JOIN ---------------------------");
                                // LOGGER.log(Level.INFO, "Domestic refund after join List: " + gson.toJson(refundApproval2));
                                parameter.setOffset(totalBackup);
                                LOGGER.log(Level.INFO, ">> OFFSET_DOMESTIC_REFUND_ONLINE: " + totalBackup);

                                LOGGER.log(Level.INFO, "------------------START GET Domestic refund ONLINE----------------- ");
                                return getListTransaction(sqlOnline, parameter, -1).flatMap(transactionsListOnline -> {

                                    LOGGER.log(Level.INFO, "------------------END GET Domestic refund ONLINE----------------- ");

                                    LOGGER.log(Level.INFO, "ONLINE : " + transactionsListOnline.size() + " APPROVAL : " + refundApproval2.size());

                                        LOGGER.log(Level.INFO, "------------------ CASE ONLINE + APPROVAL < PAGESIZE----------------- ");

                                        parameter.setOffset(totalBackup + totalItem1);
                                        LOGGER.log(Level.INFO, ">> OFFSET_DOMESTIC_REFUND_READONLY: " + (totalBackup + totalItem1));
                                        LOGGER.log(Level.INFO, "------------------START GET Domestic refund READONLY----------------- ");
                                        return getListTransaction(sqlReadOnly, parameter, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                                            LOGGER.log(Level.INFO, "------------------END GET Domestic refund READONLY----------------- ");
                                            List<DomesticRefund> transactionsFinal = new ArrayList<>();
                                            transactionsFinal.addAll(refundApproval2);
                                            transactionsFinal.addAll(transactionsListOnline);
                                            transactionsFinal.addAll(transactionsListReadOnly);
                                            LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_REFUND_BACKUP: " + refundApproval2.size());
                                            LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_REFUND_ONLINE: " + transactionsListOnline.size());
                                            LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_REFUND_READONLY: " + transactionsListReadOnly.size());
                                            int index = 1;
                                            for (DomesticRefund transaction : transactionsFinal) {
                                                transaction.setRow_num(index + (parameter.getPage() * basePageSize));
                                                index++;
                                            }
                                            transactions.setTransactions(transactionsFinal);
                                            return Observable.just(transactions);
                                        });
                                });
                            });
                        });
                    });
                });

            });
        });
    }

    @Override
    public Observable<DomesticRefund> get(SQLConnection sqlOnline, SQLConnection sqlBackup, String transactionId) {
        return getTransaction(sqlOnline, transactionId).flatMap(domesticRefund -> {
            if (domesticRefund == null) {
                return getTransactionApproval(sqlBackup, Integer.valueOf(transactionId)).flatMap(approval -> {
                    if (approval != null && approval.getOriginal_id()!= null) {
                        return DomesticTransactionService.get(sqlOnline, approval.getOriginal_id().toString()).map(purchase -> {
                            this.joinApproveWithPurchase(approval, purchase);
                            return approval;
                        });
                    } else {
                        return Observable.just(domesticRefund);
                    }
                });
            }
            return Observable.just(domesticRefund);
        });
    }

    
    @Override
    public Observable<DomesticRefund> getByRef(SQLConnection sqlOnline, String ref) {
        JsonArray inParams = new JsonArray()
                .add(ref);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlOnline.callWithParamsObservable2(TRANSACTION_GET_BY_REF, inParams, outParams).map(result -> {
            DomesticRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection readOnlyConn, SQLConnection connBackup, DomesticRefundParameter parameter) {
        // filter by approval
        return getListTotalTransactionBackup(connBackup, parameter).flatMap(totalApproval -> {
            // filter by transaction
            return DomesticTransactionService.mapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(totalPurchaseMap -> {
//            return this.domesticTransactionService.listTotalByIds(connOnline, String.join(StringPool.COMMA, totalApproval), parameter).flatMap(backupTotal -> {

                // TOTAL ONLINE
                return getListTotalTransaction(connOnline, parameter, -1).flatMap(totalItem1 -> {
                    // TOTAL READONLY
                    return getListTotalTransaction(readOnlyConn, parameter, totalItem1).map(totalItem2 -> {

                        return totalItem1 + totalItem2 + countFilterById(totalPurchaseMap, totalApproval);
                    });
                });
            });
        });
    }

    @Override
    public ResultSet download(Connection connOnline, DomesticRefundParameter parameter, int rows) throws SQLException {
        return downloadData(connOnline, parameter, rows);
    }


    @Override
    public ResultSet downloadBackp(Connection connBackup, DomesticRefundParameter parameter) throws SQLException {
        return downloadBackupData(connBackup, parameter);
    }


    @Override
    public Observable<List<DomesticRefund>> getListTransaction(SQLConnection connReadOnly, DomesticRefundParameter parameter, int rows) {

        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .add(parameter.getPage())
                .add(parameter.getPageSize())
                .add(parameter.getOffset())
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(15) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH DOMESTIC REFUND ERROR]: " + result.getOutput().getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<DomesticRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticRefund transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * get  transaction data online database
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    @Override
    public Observable<List<DomesticRefund>> getListTransactionBackup(SQLConnection connBackup, DomesticRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getStatus())
                .add(parameter.getCurrency())
                .add(RefundData.Type.DOMESTIC.getValue())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_BACKUP, inParams, outParams).map(result -> {
            List<DomesticRefund> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(12).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticRefund transaction = bindApproval(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    private Observable<Integer> getListTotalTransaction(SQLConnection connection, DomesticRefundParameter parameter, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(15) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH DOMESTIC REFUND ERROR]: " + result.getOutput().getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }


    /**
     * get  transaction data total
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    private Observable<List<String>> getListTotalTransactionBackup(SQLConnection connBackup, DomesticRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getStatus())
                .add(parameter.getCurrency() == null ? "" : parameter.getCurrency())
                .add(RefundData.Type.DOMESTIC.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LIST_TRANSACTION_TOTAL_B, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(11);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL DOMESTIC REFUND ERROR : " + resultSet.getOutput().getString(12));
                throw IErrors.DOMESTIC_SERVER_ERROR;
            }

            List<String> result = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(10).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result.add(jsonObject.getString("N_ORIGINAL_ID"));
            }

            return result;
        });
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private Observable<DomesticRefund> getTransaction(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            DomesticRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private Observable<DomesticRefund> getTransactionApproval(SQLConnection sqlConnection, Integer transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_APPROVAL_GET_BY_ID, inParams, outParams).map(result -> {
            DomesticRefund transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindApproval(jsonObject);


            return transaction;
        });
    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private DomesticRefund bindTransaction(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_NAME");
        String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        Timestamp purchaseDate = Timestamp.valueOf(rs.getString("D_PURCHASE_DATE"));

//        try {
//            purchaseDate = new Timestamp(formatter.parse(rs.getString("D_PURCHASE_DATE")).getTime());
//            date = new Timestamp(formatter.parse(rs.getString("D_MERCHANT_TRANSACTION_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        //Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInteger("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String ip = rs.getString("S_IP");
        double total = rs.getDouble("N_AMOUNT");
        double purchaseTotal = rs.getDouble("N_PURCHASE_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");

        String cardDate = rs.getString("S_CARD_DATE");
        String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
        String cardNumber = rs.getString("S_CARD_NUMBER");
        String cardVerificationCode = String.valueOf(rs.getInteger("N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");

        /** Comment round 3 */
        String transactionInfo = rs.getString("S_TRANSACTION_INFO");
        String authenticationUrl = rs.getString("S_RETURN_AUTHENTICATION_URL");


        DomesticRefund transaction = new DomesticRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(status);
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setIp_address(ip);
        transaction.setOperator_id(operatorId);
        transaction.setTransaction_purchase_time(purchaseDate);
        transaction.setAuthentication_url(authenticationUrl);
        transaction.setTransaction_info(transactionInfo);

        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        refundAmount.setPurchase_total(purchaseTotal);

        transaction.setAmount(refundAmount);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);


        transaction.setAcquirer(acquirer);

        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }
        card.setCard_date(cardDateData);
        transaction.setCard(card);

        
        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        transaction.setNote(data.getString("note"));

        return transaction;
    }


    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private DomesticRefund bindApproval(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        int originalId = Integer.parseInt(rs.getString("N_ORIGINAL_ID"));
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInteger("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");


        DomesticRefund transaction = new DomesticRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(status);
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setOperator_id(operatorId);
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);

        transaction.setAmount(refundAmount);


        return transaction;
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection connection, DomesticRefundParameter parameter, int rows) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getOrderInfo())
                .add(parameter.getStatus())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getCardNumber());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(15);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadBackupData(Connection connection, DomesticRefundParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionReference())
                .add(parameter.getStatus())
                .add(RefundData.Type.DOMESTIC.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.INFO, "[CALL ]: PKG_REFUND_APPROVE.get_refund_download, IN: " + inParams + ", OUT: " + outParams);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, DOWNLOAD_TRANSACTION_BACKUP, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(10);


        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private DomesticRefund joinApproveWithPurchase(DomesticRefund approve, DomesticTransaction purchase) {


        if (purchase == null) {
            return null;
        }

        DomesticRefund result = approve;

        result.setTransaction_purchase_time(purchase.getTransaction_time());
//        approve.setAuthentication_url(purchase.getA);
        result.setOrder_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());

        result.setCard(purchase.getCard());
        result.getAmount().setPurchase_total(purchase.getAmount().getTotal());
        result.setIp_address(purchase.getIp_address());

        return result;
    }

    private Integer countFilterById(Map<Integer, DomesticTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    private static final String LIST_TRANSACTION = "{call PKG_PAYMENT2.SEARCH_REFUND(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String DOWNLOAD_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_download(?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_REFUND_APPROVE.get_refund_total(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_PAYMENT2.GET_REFUND(?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_REF = "{call PKG_PAYMENT2.GET_REFUND_BY_REF(?,?,?,?)}";

    private static final String TRANSACTION_APPROVAL_GET_BY_ID = "{call PKG_REFUND_APPROVE.get_refund_by_id(?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(DomesticRefundServiceImpl.class.getName());

    private Gson gson = new Gson();
}
