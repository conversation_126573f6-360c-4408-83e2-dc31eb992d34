package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.BinInfo;
import com.onepay.ma.service.service.BinService;
import com.onepay.ma.service.util.CardType;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.sql.ResultSet;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/31/16.
 */
@Service
public class BinServiceImpl implements BinService {
    @Override
    public Observable<BinInfo> get(SQLConnection connReadOnly, String bin) {
        return getBinInformation(connReadOnly, bin);
    }

    /**
     * get bin information
     * @param connReadOnly
     * @param bin
     * @return
     */
    private Observable<BinInfo> getBinInformation(SQLConnection connReadOnly, String bin){

        CardType cardType = CardType.detect(bin);
        String procedure = StringPool.BLANK;
        switch (cardType){
            case AMERICAN_EXPRESS:
                procedure = AMEX_INFO;
                break;
            case MASTERCARD:
                procedure = MASTER_INFO;
                break;
            case VISA:
                procedure = VISA_INFO;
                break;
            default:
                procedure = StringPool.BLANK;
                break;
        }
        if(procedure.isEmpty()){
            throw IErrors.VALIDATION_ERROR;
        }
        JsonArray inParams = new JsonArray()
                .add(bin);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.NUMBER);
        return connReadOnly.callWithParamsObservable2(procedure, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "GET MASTER BIN: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BinInfo binInfo = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            ResultSet rs = (ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            binInfo = bindBinInfo(rs.getRows().get(0), cardType);
            binInfo.setBin_number(bin);


            return binInfo;
        });
    }

    /**
     * Convert resultset to bin data
     * @param rs
     * @return
     */
    private BinInfo bindBinInfo(JsonObject rs, CardType cardType){
        BinInfo binInfo  = new BinInfo();
        String bankName = StringPool.MINUS;
        String state = StringPool.MINUS;
        String country = StringPool.MINUS;
        String phone = StringPool.MINUS;
        String street = StringPool.MINUS;
        String city = StringPool.MINUS;
        switch (cardType){
            case AMERICAN_EXPRESS:
                bankName = rs.getString("S_BANK_NAME");
                country = rs.getString("S_COUNTRY_NAME");
                phone = rs.getString("S_BANK_PHONE");

                break;
            case MASTERCARD:
                bankName = rs.getString("S_BANKNAME");
                country = rs.getString("S_COUNTRY");
                city = rs.getString("S_CITY");
                state = rs.getString("S_STATE");
                street = rs.getString("S_STREETADDR");
                phone = rs.getString("S_TELECOMNUM");
                break;
            case VISA:
                bankName = rs.getString("S_NAME");
                country = rs.getString("S_COUNTRY");
                city = rs.getString("S_COPY_ORIGINAL_CITY");
                state = rs.getString("S_COPY_ORIGINAL_STATE");
                street = rs.getString("S_COPY_ORIGINAL_STREET_1") + ((rs.getString("S_COPY_ORIGINAL_STREET_2") == null) ? StringPool.BLANK : rs.getString("S_COPY_ORIGINAL_STREET_2"));
                phone = rs.getString("S_ARB_COMPLIANCE_DIRECT");
                break;
            default:
                break;
        }

        binInfo.setCity(city);
        binInfo.setCountry(country);
        binInfo.setPhone(phone);
        binInfo.setBank_name(bankName);
        binInfo.setState(state);
        binInfo.setStreet(street == null ? "-" : street);

        return binInfo;
    }


    public static final String AMEX_INFO = "{ call PKG_ONEFRAUD.GET_AMEX_BIN(?,?,?,?) }";

    public static final String MASTER_INFO= "{ call PKG_ONEFRAUD.GET_MASTER_BIN(?,?,?,?) }";

    public static final String VISA_INFO = "{ call PKG_ONEFRAUD.GET_VISA_BIN(?,?,?,?) }";

    private static final Logger LOGGER = Logger.getLogger(BinServiceImpl.class.getName());

}
