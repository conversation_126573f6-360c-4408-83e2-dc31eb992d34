/*
 * Copyright (c) 2017. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */
/**
 * Created by duongtv on 24/8/2017.
 */
package com.onepay.ma.service.service;

import com.onepay.ma.service.models.TokenizationTranParams;
import com.onepay.ma.service.models.TokenizationTrans;
import com.onepay.ma.service.models.UserTokenizations;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

public interface TokenService {
    public Observable<UserTokenizations> listUserToken(SQLConnection connBackup, String cardNo, String userID, String userGroup, int page,int pagesize);

    public Observable<TokenizationTrans> listTranToken(SQLConnection connBackup, TokenizationTranParams params);
}
