package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.models.MerchantRefundPermit;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.Map;

/**
 * Created by anhkh on 8/9/2016.
 */
@Service
public class MerchantPermitServiceImpl implements  MerchantPermitService{

    @Override
    public Observable<Integer> countMerchantUserApprovalById(SQLConnection backupConn, String merchantId, Integer type) {

        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return backupConn.callWithParamsObservable2(IS_MERCHANT_PERMITTED, inParams, outParams).map(resultSet -> {

            Integer count = 0;
            int resultCode = resultSet.getOutput().getInteger(3);

            if(resultCode != 200) {
                throw  new DatabaseException(resultSet.getOutput().getString(4));
            } else {
                count = resultSet.getOutput().getInteger(2);
            }
            return count;
        });
    }

    @Override
    public Observable<MerchantRefundPermit> getMerchantRefundPermit(SQLConnection backupConn, String merchantId, Integer type) {
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(type);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return backupConn.callWithParamsObservable2(GET_MERCHANT_PERMITTED, inParams, outParams).map(resultSet -> {

            MerchantRefundPermit result = null;
            int resultCode = resultSet.getOutput().getInteger(3);

            if(resultCode != 200) {
                throw  new DatabaseException(resultSet.getOutput().getString(4));
            } else {
                Map map = resultSet.getOutput().getJsonObject(2).getMap();
                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                for (JsonObject jsonObject : rs.getRows()) {
                    result = new MerchantRefundPermit();
                    result.setId(jsonObject.getInteger("N_ID"));
                    result.setMerchantId(jsonObject.getString("S_MERCHANT_ID"));
                    result.setType(jsonObject.getInteger("N_TYPE"));
                    result.setDoubleConfirmation(jsonObject.getInteger("N_DOUBLE_CONFIRMATION"));
                    break;
                }
            }
            return result;
        });
    }


    private static final String IS_MERCHANT_PERMITTED = "{call PKG_REFUND_APPROVE.count_merchant_permited(?,?,?,?,?)}";
    private static final String GET_MERCHANT_PERMITTED = "{call PKG_REFUND_APPROVE.get_merchant_permited(?,?,?,?,?)}";
}
