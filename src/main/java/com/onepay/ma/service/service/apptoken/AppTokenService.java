package com.onepay.ma.service.service.apptoken;

import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;


/**
 * Created by anhkh on 22-Sep-17.
 */
public interface AppTokenService {

    Observable<Void> insert(SQLConnection sqlConnection, AppToken appToken);

    Observable<List<AppToken>> findByUserIds(SQLConnection sqlConnection, String userIds);

    Observable<Void> removeToken(SQLConnection sqlConnection, String token);
}
