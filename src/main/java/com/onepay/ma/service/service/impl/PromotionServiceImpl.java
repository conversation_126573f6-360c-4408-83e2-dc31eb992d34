package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Promotion;
import com.onepay.ma.service.models.PromotionParam;
import com.onepay.ma.service.models.PromotionStatus;
import com.onepay.ma.service.models.Promotions;
import com.onepay.ma.service.service.PromotionService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
@Service
public class PromotionServiceImpl implements PromotionService {
    @Override
    public Observable<Promotions> list(SQLConnection sqlConnPr, String keyword, String merchantId, int page, int status) {
        return getListPromotionList(sqlConnPr, keyword, merchantId, page, status);
    }

    @Override
    public Observable<Promotion> get(SQLConnection sqlConnPr, String promotionId) {
        return getPromotion(sqlConnPr, promotionId);
    }

    @Override
    public Observable<Promotion> insert(SQLConnection sqlConnPr, PromotionParam promotionParam) {
        return insertPromotionData(sqlConnPr, promotionParam);
    }

    @Override
    public Observable<Integer> updateStatus(SQLConnection sqlConnPr, String promotionId, PromotionStatus status) {
        return updateStatusPromotion(sqlConnPr, promotionId, status);
    }

    @Override
    public Observable<Integer> updateApproveDate(SQLConnection sqlConnPr, int promotionId) {
        return updateDateApprove(sqlConnPr, promotionId);
    }

    @Override
    public Observable<Integer> updateByField(SQLConnection sqlConnPr, String sql, JsonArray param) {
        return updatePromotionByField(sqlConnPr, sql, param);
    }

    /**
     * update status promotion
     * @param sqlConnPr
     * @return
     */
    private Observable<Integer> updatePromotionByField(SQLConnection sqlConnPr, String sql, JsonArray params){

        return  sqlConnPr.updateWithParamsObservable(sql, params).flatMap(updateResult -> {
            // update data
            return Observable.just(updateResult.getUpdated());
        });
    }

    /**
     * update status promotion
     * @param sqlConnPr
     * @param promotionId
     * @param status
     * @return
     */
    private Observable<Integer> updateStatusPromotion(SQLConnection sqlConnPr, String promotionId, PromotionStatus status){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(promotionId)
                .add(status.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnPr.callWithParamsObservable2(UPDATE_STATUS, inParams, outParams).map(result -> {
            //int returnValue = 0;
            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION ] Update Status Promotion Failed" + StringPool.SPACE + result.getOutput().getString(4));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }


            return resultCode;
        });
    }

    /**
     * insert promotion data
     * @param sqlConn
     * @param promotionParam
     * @return
     */
    private Observable<Promotion> insertPromotionData(SQLConnection sqlConn, PromotionParam promotionParam){

        JsonArray inParams = new JsonArray()
                .add(promotionParam.getPromotion_name())
                .add(promotionParam.getDescription())
                .add(promotionParam.getStart_time())
                .add(promotionParam.getEnd_time())
                .add(promotionParam.getUser_id());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return sqlConn.callWithParamsObservable2(PROMOTION_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(5);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION ] Insert Data Failed" + StringPool.SPACE + result.getOutput().getString(6));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(7);
            }

            return returnValue;
        }).flatMap(integer -> {
            // get promotion by nid
            return getPromotionByNid(sqlConn, integer);
        });
    }

    /**
     * insert promotion data
     * @param sqlConn
     * @param promotionId
     * @return
     */
    private Observable<Integer> updateDateApprove(SQLConnection sqlConn, int promotionId){

        JsonArray inParams = new JsonArray()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConn.callWithParamsObservable2(UPDATE_DATE_APPROVE, inParams, outParams).map(result -> {

            int resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION ] Update Data Failed" + StringPool.SPACE + result.getOutput().getString(6));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    /**
     * get list promotion list
     * @param sqlConnPr
     * @param keyword
     * @param merchantId
     * @return
     */
    private Observable<Promotions> getListPromotionList(SQLConnection sqlConnPr, String keyword, String merchantId, int page, int status){
        JsonArray inParams = new JsonArray()
                .add(merchantId)
                .add(keyword)
                .add(status)
                .add(50)
                .add(page);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER);
        return sqlConnPr.callWithParamsObservable2(LIST_PROMOTION, inParams, outParams).map(result -> {
            Promotions promotions = new Promotions();
            List<Promotion> promotionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    Promotion promotion = bindPromotion(jsonObject);
                    promotionList.add(promotion);
                }
                promotions.setTotal_items(result.getOutput().getInteger(8));
            }else{
                promotions.setTotal_items(0);
            }

            promotions.setPromotions(promotionList);
            return promotions;
        });
    }

    /**
     * get  promotion
     * @param sqlConnPr
     * @param promotionId
     * @return
     */
    private Observable<Promotion> getPromotion(SQLConnection sqlConnPr, String promotionId){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return sqlConnPr.callWithParamsObservable2(PROMOTION_BY_ID, inParams, outParams).map(result -> {
            Promotion promotion = null;
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            promotion = bindPromotion(jsonObject);


            return promotion;
        });
    }

    /**
     * get promotion by nid
     * @param sqlConnPr
     * @param promotionId
     * @return
     */
    private Observable<Promotion> getPromotionByNid(SQLConnection sqlConnPr, int promotionId){
        JsonArray inParams = new JsonArray()
                .add(promotionId)
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return sqlConnPr.callWithParamsObservable2(PROMOTION_BY_ID, inParams, outParams).map(result -> {
            Promotion promotion = null;
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            promotion = bindPromotion(jsonObject);

            return promotion;
        });
    }

    /**
     * bind promotion
     * @param rs
     * @return
     * @throws SQLException
     */
    private Promotion bindPromotion(JsonObject rs) {
        int id = rs.getInteger("N_ID");
        String promotionId = rs.getString("S_ID");
        String promotionName = rs.getString("S_NAME");
        String desc = rs.getString("S_DESC");
        String createUser = rs.getString("S_CREATE_USER");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");

        Timestamp startDate = java.sql.Timestamp.valueOf(rs.getString("D_START"));
        Timestamp endDate = java.sql.Timestamp.valueOf(rs.getString("D_END"));
        Timestamp createDate = java.sql.Timestamp.valueOf(rs.getString("D_CREATE"));;
        Timestamp approveDate = null;


        if(rs.getString("D_APPROVE") != null){
            approveDate = java.sql.Timestamp.valueOf(rs.getString("D_APPROVE"));
        }

        int status = rs.getInteger("N_STATUS");
        boolean expired = rs.getInteger("N_EXPIRED") != null && rs.getInteger("N_EXPIRED") == 1 ? true : false ;

        Promotion promotion = new Promotion();
        promotion.setApprove_time(approveDate);
        promotion.setPromotion_id(promotionId);
        promotion.setDescription(desc);
        promotion.setPromotion_name(promotionName);
        promotion.setStart_time(startDate);
        promotion.setEnd_time(endDate);
        promotion.setCreate_time(createDate);
        promotion.setStatus(status);
        promotion.setCreate_user(createUser);
        promotion.setN_id(id);
        promotion.setExpired(expired);
        return  promotion;
    }

    private final static String LIST_PROMOTION = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_SEARCH(?,?,?,?,?,?,?,?,?)}";

    private final static String PROMOTION_BY_ID = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_GET(?,?,?,?,?)}";

    private static final String PROMOTION_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_INSERT_2(?,?,?,?,?,?,?,?)}";

    private static final String UPDATE_STATUS = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_STATUS(?,?,?,?,?)}";

    private static final String UPDATE_DATE_APPROVE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_APPROVE_UPDATE(?,?,?)}";

    private static final String SCHEMA_DATA = "ONEPR";

    private static final Logger LOGGER = Logger.getLogger(PromotionServiceImpl.class.getName());

}
