package com.onepay.ma.service.service.paypal.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.DateTimeUtil;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.paypal.*;
import com.onepay.ma.service.service.paypal.PaypalDisputeService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

@Service
public class PaypalDisputeServiceImpl implements PaypalDisputeService {


    public Observable<Integer> insertDispute(SQLConnection sqlConnection, PaypalDispute model) {
        JsonArray inParams = new JsonArray();
        if(model.getId()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getId());
        }
        if(model.getReason()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getReason());
        }
        if(model.getStatus()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getStatus());
        }
        if(model.getAmount().getCurrency()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getAmount().getCurrency());
        }
        if(model.getAmount().getTotal()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getAmount().getTotal());
        }
        if(model.getOutcomeCode()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getOutcomeCode());
        }
        if(model.getRefundAmount() !=null){
            if(model.getRefundAmount().getCurrency() ==null){
                inParams.addNull();
            }else {
                inParams.add(model.getRefundAmount().getCurrency());
            }
            if(model.getRefundAmount().getTotal() ==null){
                inParams.addNull();
            }else {
                inParams.add(model.getRefundAmount().getTotal());
            }
        }else{
            inParams.addNull()
                    .addNull();
        }
        if(model.getSellerResponseDueDate()==null){
            inParams.addNull();
        }else {
            inParams.add(DateTimeUtil.convertDatetoString(model.getSellerResponseDueDate(), DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss));
        }
        if(model.getCreateDate()==null){
            inParams.addNull();
        }else {
            inParams.add(DateTimeUtil.convertDatetoString(model.getCreateDate(), DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss));
        }
        if(model.getUpdateDate()==null){
            inParams.addNull();
        }else {
            inParams.add(DateTimeUtil.convertDatetoString(model.getUpdateDate(), DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss));
        }
        if(model.getDisputeLifeCycleStage()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getDisputeLifeCycleStage());
        }
        if(model.getDisputeChannel()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getDisputeChannel());
        }
        if(model.getExtensions()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getExtensions());
        }
        inParams.add(model.getContent());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        LOGGER.log(Level.WARNING, "[CALL FOR insertDispute]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnection.callWithParamsObservable2(INSERT_DISPUTE, inParams, outParams).map(result -> {
            Integer dispute_Id = null;
            if (result.getOutput().getInteger(16) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT OR UPDATE PAYPAL DISPUTE ERROR] => " + result.getOutput().getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }else{
                dispute_Id =result.getOutput().getInteger(15);
            }
            return dispute_Id;

        });
    }
    @Override
    public Observable<Void> insertOrUpdateDisputeTransaction(SQLConnection sqlConnectionB, PaypalTransaction model, String disputeId) {
        JsonArray inParams = new JsonArray()
                .add(disputeId);
        if(model.getBuyerTransactionId()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getBuyerTransactionId());
        }
        if(model.getSellerTransactionId()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getSellerTransactionId());
        }
        if(model.getTransactionStatus()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getTransactionStatus());
        }
        if(model.getGrossAmount().getCurrency()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getGrossAmount().getCurrency());
        }
        if(model.getGrossAmount().getTotal()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getGrossAmount().getTotal());
        }
        if(model.getInvoiceNumber()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getInvoiceNumber());
        }
        if(model.getCustom()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getCustom());
        }
        if(model.getBuyerEmail()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getBuyerEmail());
        }
        if(model.getBuyerPayerId()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getBuyerPayerId());
        }
        if(model.getBuyerName()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getBuyerName());
        }
        if(model.getSellerEmail()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getSellerEmail());
        }
        if(model.getSellerMerchantId()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getSellerMerchantId());
        }
        if(model.getSellerName()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getSellerName());
        }
        if(model.getCreatDate()==null){
            inParams.addNull();
        }else {
            inParams.add(DateTimeUtil.convertDatetoString(model.getCreatDate(), DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss));
        }
        if(model.getSellerProtection()==null){
            inParams.addNull();
        }else {
            inParams.add(model.getSellerProtection());
        }
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR insertDisputeTransaction]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnectionB.callWithParamsObservable2(INSERT_DISPUTE_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(16) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT PAYPAL DISPUTE TRANSACTION ERROR] => " + result.getOutput().getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            return null;

        });
    }

    @Override
    public Observable<Void> insertPaypalEvidence(SQLConnection sqlConnectionB, PaypalEvidence model){
        return insertEvidence(sqlConnectionB, model).flatMap(evidenceId -> {
            return insertListTrackingInfor(sqlConnectionB, model.getTrackingInfo(),evidenceId, 0).map(list -> {
                return null;
            });
        });
    }

    @Override
    public Observable<List<PaypalTransaction>> getPaypalTransaction(SQLConnection sqlConnectionB, PaypalTransaction model){
        JsonArray inParams = new JsonArray()
                .add(model.getDisputeId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnectionB.callWithParamsObservable2(LIST_DISPUTE_TRANS, inParams, outParams).flatMap(result -> {

            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[ GET PAYPAL TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<PaypalTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                PaypalTransaction transaction = bindDisputeTrans(jsonObject);
                transactionList.add(transaction);
            }

            return Observable.just(transactionList);
        });
    }


    public Observable<Integer> insertEvidence(SQLConnection sqlConnectionB, PaypalEvidence model){
        JsonArray inParams = new JsonArray()
                .add(model.getDisputeId())
                .add(model.getType())
                .add(model.getSource())
                .add(model.getnDisputeId());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR insertEvidence]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnectionB.callWithParamsObservable2(INSERT_EVIDENCE, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(5) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT EVIDENCE ERROR] => " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }else{
                return result.getOutput().getInteger(4);
            }

        });
    }
    private Observable<List<PaypalTrackingInfo>> insertListTrackingInfor(SQLConnection sqlConn, List<PaypalTrackingInfo> listTrackingInfor,int evidenceId, int index){
        if(listTrackingInfor.size() <= 0){
            return Observable.just(listTrackingInfor);
        }
        PaypalTrackingInfo paypalTrackingInfo = listTrackingInfor.get(index);
        final int finalIndex = index;
        return insertPaypalTrackingInfo(sqlConn, paypalTrackingInfo,evidenceId).flatMap(void1 -> {
            return Observable.just(paypalTrackingInfo).flatMap(promotionDiscountData -> {
                    if (finalIndex >= listTrackingInfor.size() - 1) {
                        return Observable.just(listTrackingInfor);
                    } else {
                        return insertListTrackingInfor(sqlConn, listTrackingInfor, evidenceId, finalIndex + 1);
                    }
            });
        });
    }
    public Observable<Void> insertPaypalTrackingInfo(SQLConnection sqlConnectionB, PaypalTrackingInfo model,int evidenceId){
        JsonArray inParams = new JsonArray()
                .add(model.getNumber())
                .add(model.getCarrierName())
                .add(evidenceId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR insertPaypalTrackingInfo]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnectionB.callWithParamsObservable2(INSERT_TRACKING_INFO, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT PAYPAL TRACKING INFO ERROR] => " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            return null;

        });
    }
    @Override
    public Observable<Void> insertListPaypalMessage(SQLConnection sqlConnectionB, List<PaypalMessage> listPaypalMessage, String disputeId,Integer dispute_Id){
        return insertPaypalMessage(sqlConnectionB, listPaypalMessage,disputeId,dispute_Id, 0).map(list -> {
            return null;
        });
    }
    private Observable<List<PaypalMessage>> insertPaypalMessage(SQLConnection sqlConn, List<PaypalMessage> listPaypalMessage,String disputeId,Integer dispute_Id, int index){
        if(listPaypalMessage.size() <= 0){
            return Observable.just(listPaypalMessage);
        }
        PaypalMessage paypalMessage = listPaypalMessage.get(index);
        final int finalIndex = index;
        return insertPaypalMessage(sqlConn, paypalMessage,disputeId,dispute_Id).flatMap(void1 -> {
            return Observable.just(paypalMessage).flatMap(promotionDiscountData -> {
                if (finalIndex >= listPaypalMessage.size() - 1) {
                    return Observable.just(listPaypalMessage);
                } else {
                    return insertPaypalMessage(sqlConn, listPaypalMessage, disputeId,dispute_Id, finalIndex + 1);
                }
            });
        });
    }
    public Observable<Void> insertPaypalMessage(SQLConnection sqlConnectionB, PaypalMessage model,String disputeId,Integer dispute_Id){
        JsonArray inParams = new JsonArray()
                .add(disputeId)
                .add(dispute_Id)
                .add(model.getPosted_by());
        if(model.getTimePosted()==null){
            inParams.addNull();
        }else {
            inParams.add(DateTimeUtil.convertDatetoString(model.getTimePosted(), DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss));
        }
            inParams.add(model.getContent());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR insertPaypalMessage]:  IN: " + inParams + ", OUT: " + outParams);
        return sqlConnectionB.callWithParamsObservable2(INSERT_MESSAGE, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(6) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT PAYPAL MESSAGE ERROR] => " + result.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            return null;

        });
    }
    private PaypalTransaction bindDisputeTrans(JsonObject rs) {

        String disputeId = rs.getString("S_DISPUTE_ID");
        String buyerTransactionId = rs.getString("S_BUYER_TRANS_ID");
        String sellerTransactionId = rs.getString("S_SELLER_TRANS_ID");
        String transactionStatus = rs.getString("S_TRANSACTION_STATUS");
        String currency = rs.getString("S_CURRENCY");
        double amount = rs.getDouble("N_AMOUNT");
        String invoiceNumber = rs.getString("S_INVOICE_NUMBER");
        String custom = rs.getString("S_CUSTOM");
        String buyerEmail = rs.getString("S_BUYER_EMAIL");
        String buyerPayerId = rs.getString("S_BUYER_PAYER_ID");
        String buyerName = rs.getString("S_BUYER_NAME");
        String sellerEmail = rs.getString("S_SELLER_EMAIL");
        String sellerMerchantId = rs.getString("S_SELLER_MERCHANT_ID");
        String sellerName = rs.getString("S_SELLER_NAME");

        PaypalTransaction transaction = new PaypalTransaction();
        transaction.setDisputeId(disputeId);
        transaction.setBuyerTransactionId(buyerTransactionId);
        transaction.setSellerTransactionId(sellerTransactionId);
        transaction.setTransactionStatus(transactionStatus);
        transaction.setInvoiceNumber(invoiceNumber);
        transaction.setCustom(custom);
        transaction.setBuyerEmail(buyerEmail);
        transaction.setBuyerPayerId(buyerPayerId);
        transaction.setBuyerName(buyerName);
        transaction.setSellerEmail(sellerEmail);
        transaction.setSellerMerchantId(sellerMerchantId);
        transaction.setSellerName(sellerName);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        transaction.setGrossAmount(amountData);

        return transaction;
    }
    private static final String INSERT_DISPUTE = "{call PKG_PAYPAL.insert_dispute(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String INSERT_DISPUTE_TRANSACTION = "{call PKG_PAYPAL.insert_or_update_dis_trans(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String INSERT_EVIDENCE = "{call PKG_PAYPAL.insert_evidence(?,?,?,?,?,?)}";
    private static final String INSERT_TRACKING_INFO = "{call PKG_PAYPAL.insert_tracking_info(?,?,?,?,?)}";
    private static final String INSERT_MESSAGE = "{call PKG_PAYPAL.insert_message(?,?,?,?,?,?,?,?)}";
    private static final String LIST_DISPUTE_TRANS = "{call PKG_PAYPAL.GET_DISPUTE_TRANS(?,?,?,?)}";

    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PaypalNotifyServiceImpl.class.getName());
}
