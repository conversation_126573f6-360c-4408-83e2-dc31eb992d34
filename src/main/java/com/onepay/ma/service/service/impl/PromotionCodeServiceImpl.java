package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionCode;
import com.onepay.ma.service.models.PromotionCodeSearchQuery;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.service.PromotionCodeService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 04-Jan-17.
 */
@Service
public class PromotionCodeServiceImpl implements PromotionCodeService {

    @Override
    public Observable<PromotionCode> updateCouponCodeStatus(SQLConnection sqlConnection, String id, String state) {
        JsonArray inParams = new JsonArray()
                .add(id)
                .add(state);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(UPDATE_COUPON_STATE, inParams, outParams).flatMap(result -> {

            if(result.getOutput().getInteger(2) != 201){
//                LOGGER.log(Level.SEVERE, "[ PROMOTION CODE ] UPDATE STATE ERROR" + StringPool.SPACE + result.getOutput().getString(3));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }
            return this.get(sqlConnection, id);
        });
    }

    @Override
    public Observable<BaseList<PromotionCode>> search(SQLConnection sqlConnection, PromotionCodeSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getCustomer())
                .add(query.getMerchantId())
                .add(query.getTransactionId())
                .add(query.getCouponCode())
                .add(query.getTransRef())
                .add(query.getOrderRef())
                .add(query.getSmsState())
                .add(query.getCouponState())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SEARCH_PROMO, inParams, outParams).map(result -> {
            List<PromotionCode> codeList = new ArrayList<>();
            BaseList<PromotionCode> codes = new BaseList<>();

            if(result.getOutput().getInteger(15) != 200){
//                LOGGER.log(Level.SEVERE, "[ PROMOTION CODE ] SEARCH ERROR" + StringPool.SPACE + result.getOutput().getString(16));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }

            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            if (rs.getRows().size() > 0) {
                for (JsonObject jsonObject : rs.getRows()) {
                    PromotionCode promo = bind(jsonObject);
                    codeList.add(promo);
                }
                codes.setTotal_items(result.getOutput().getInteger(13));
            } else {
                codes.setTotal_items(0);
            }
            codes.setList(codeList);

            // Return.
            return codes;
        });
    }

    @Override
    public Observable<PromotionCode> get(SQLConnection sqlConnection, String id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_PROMO_BY_ID, inParams, outParams).map(result -> {
            PromotionCode PromotionCode = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            PromotionCode = bind(jsonObject);


            return PromotionCode;
        });
    }

    private PromotionCode bind(JsonObject jo) {
        PromotionCode code = new PromotionCode();
        code.setId(jo.getInteger("N_ID"));
        code.setMerchant_id(jo.getString("S_MERCHANT_ID"));
        code.setTransaction_id(jo.getInteger("N_TRAN_ID"));
        code.setCustomer_email(jo.getString("S_CUSTOMER_EMAIL"));
        code.setCustomer_name(jo.getString("S_CUSTOMER_NAME"));
        code.setCustomer_phone(jo.getString("S_CUSTOMER_PHONE"));
        code.setCoupon_code(jo.getString("S_COUPON_CODE"));
        code.setCoupon_state(jo.getString("S_COUPON_STATE"));
        code.setOrder_ref(jo.getString("S_ORDER_REF"));
        code.setTrans_ref(jo.getString("S_TRAN_REF"));
        code.setSms_state(jo.getString("S_SMS_STATE"));
        code.setSend_sms_date(jo.getString("D_SEND_SMS") == null ? null : Timestamp.valueOf(jo.getString("D_SEND_SMS")));
        code.setUsed_date(jo.getString("D_USE_COUPON") == null ? null : Timestamp.valueOf(jo.getString("D_USE_COUPON")));
        return code;
    }

    private static final String SEARCH_PROMO = "{call PKG_PROMOTION.search_promo_code(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String GET_PROMO_BY_ID = "{call PKG_PROMOTION.get_promo_code(?,?,?,?)}";

    private static final String UPDATE_COUPON_STATE = "{call PKG_PROMOTION.UPDATE_PROMO_STATUS(?,?,?,?)}";


//    private static final Logger LOGGER = Logger.getLogger(PromotionCodeServiceImpl.class.getName());
}
