package com.onepay.ma.service.service;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.Terminal;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 7/11/2016.
 */
public interface UserTerminalService {

    Observable<Integer> insert(SQLConnection connection, int userId, String merchantId, String terminalId);


    Observable<List<Terminal>> getTerminalBUserId(SQLConnection connOnline, Integer userId);

    default Observable<Map<String, List<String>>> getTerminalMapByUserId(SQLConnection connOnline, Integer userId) {
        return this.getTerminalBUserId(connOnline, userId).flatMap(terminals -> {
            Map<String, List<String>> result  = terminals.stream().collect(Collectors.groupingBy(Terminal::getMerchant_id,
                    Collectors.mapping(Terminal::getTerminal_id, Collectors.toList())));
            return Observable.just(result);
        });
    }

    Observable<Integer> deleteByUserId(SQLConnection connection, int userId);
}
