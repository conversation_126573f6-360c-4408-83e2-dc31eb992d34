package com.onepay.ma.service.service.mpayPromotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReport;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReportQuery;
import com.onepay.ma.service.service.mpayPromotion.MpayPrReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 01-Feb-18.
 */
@Service
public class MpayPrReportServiceImpl implements MpayPrReportService {

    @Override
    public Observable<BaseList<MpayPrReport>> list(SQLConnection connection, MpayPrReportQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(SEARCH_REPORT, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(6);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY PR REPORT ONLINE ERROR]: " + result.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BaseList<MpayPrReport> baseList =  new BaseList<>();
            List<MpayPrReport> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(bind(jsonObject));
                }
            }
            baseList.setTotal_items(transactionList.size());
            baseList.setList(transactionList);
            return baseList;
        });
    }


    @Override
    public ResultSet download(Connection connReadOnly, MpayPrReportQuery query) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(query.getMerchant_id())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connReadOnly, SEARCH_REPORT, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(7);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD mPAY REPORT ONLINE ERROR]: " + callableStatement.getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(6);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return  rs;
    }

    private MpayPrReport bind(JsonObject jo) {
        MpayPrReport result = new MpayPrReport();

        result.setDate(jo.getString("D_DATE")== null? null : Timestamp.valueOf(jo.getString("D_DATE")));
        result.setMerchant_id(jo.getString("S_MERCHANT_ID"));
        result.setSum_discount(jo.getDouble("N_SUM_DISCOUNT"));
        result.setSum_original(jo.getDouble("N_SUM_ORIGINAL"));
        result.setSum_payment(jo.getDouble("N_SUM_PAYMENT"));
        result.setTrans_count(jo.getInteger("N_TRANS_COUNT"));

        return result;
    }

    private static final String SEARCH_REPORT = "{call PKG_MPAY_PROMOTION.search_reports(?,?,?,?,?,?,?,?)}";

    private Logger LOGGER = Logger.getLogger(MpayPrReportServiceImpl.class.getName());
}
