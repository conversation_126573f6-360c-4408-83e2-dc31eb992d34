package com.onepay.ma.service.service.pay_collect.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.service.pay_collect.UserProfileConfigService;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/7/2020
 * Time: 10:34 AM
 * To change this ma-web.
 */
@Service
public class PayCollectUserProfileConfigServiceImpl implements UserProfileConfigService {
    public static final String SEARCH_USER = "{call PKG_MA_PAYCOLLECT.S_USER(?,?,?,?,?,?,?,?,?,?,?) }";
    public static final String GET_ALL_USER = "{call PKG_MA_PAYCOLLECT.G_ALL_USER(?,?,?,?) }";
    private static final Logger LOGGER = Logger.getLogger(PayCollectUserProfileConfigServiceImpl.class.getName());
    @Override
    public Observable<List<UserConfigDto>> all(SQLConnection connection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                ;
        return connection.callWithParamsObservable2(GET_ALL_USER, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            List<UserConfigDto> dtos = new ArrayList<>();
            for (JsonObject jsonObject : rs.getRows()) {
                UserConfigDto dto = new UserConfigDto();
                dto.setAddress(jsonObject.getString("S_ADDRESS"));
                dto.setCreated_date(jsonObject.getString("D_CREATE"));

                dto.setDate_of_birth(java.sql.Timestamp.valueOf(jsonObject.getString("D_DATE_OF_BIRTH")));
//                dto.setAddress(jsonObject.getString("S_ADDRESS"));
                dto.setIssue_date(java.sql.Timestamp.valueOf(jsonObject.getString("S_ISSUE_DATE")));

//                dto.setDate_of_birth(jsonObject.getString("D_DATE_OF_BIRTH"));
//                dto.setIssue_date(jsonObject.getString("S_ISSUE_DATE"));
                dto.setDescription(jsonObject.getString("S_DESC"));
                dto.setEmail(jsonObject.getString("S_EMAIL"));
                dto.setGender(jsonObject.getString("S_GENDER"));
                dto.setId(jsonObject.getString("S_ID"));
                dto.setId_card(jsonObject.getString("S_ID_CARD"));
                dto.setIssue_by(jsonObject.getString("S_ISSUE_BY"));
//                dto.setIssue_date(jsonObject.getString("S_ISSUE_DATE"));
                dto.setMobile(jsonObject.getString("S_MOBILE"));
                dto.setPartner_id(jsonObject.getString("S_PARTNER_ID"));
                dto.setPartner_name(jsonObject.getString("S_PARTNER_NAME"));
                dto.setReference_id(jsonObject.getString("S_REFERENCE_ID"));
                dto.setState(jsonObject.getString("S_STATE"));
                dto.setName(jsonObject.getString("S_FULL_NAME"));
                dtos.add(dto);
            }
            return dtos;
        });
    }

    @Override
    public Observable<List<UserConfigDto>> search(SQLConnection connection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.USER_ID))
                .add(mIn.get(ParamsPool.USER_NAME))
                .add(mIn.get(ParamsPool.REFERENCE))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGE))
                .add(mIn.get(ParamsPool.PAGE_SIZE))
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                ;
        return connection.callWithParamsObservable2(SEARCH_USER, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            List<UserConfigDto> dtos = new ArrayList<>();
            for (JsonObject jsonObject : rs.getRows()) {
                UserConfigDto dto = new UserConfigDto();
                dto.setName(jsonObject.getString("S_FULL_NAME"));
                dto.setAddress(jsonObject.getString("S_ADDRESS"));
                dto.setCreated_date(jsonObject.getString("D_CREATE"));
                dto.setDate_of_birth(java.sql.Timestamp.valueOf(jsonObject.getString("D_DATE_OF_BIRTH")));
//                dto.setAddress(jsonObject.getString("S_ADDRESS"));
                dto.setIssue_date(java.sql.Timestamp.valueOf(jsonObject.getString("S_ISSUE_DATE")));
//                dto.setDate_of_birth(jsonObject.getString("D_DATE_OF_BIRTH"));
//                dto.setIssue_date(jsonObject.getString("S_ISSUE_DATE"));
                dto.setDescription(jsonObject.getString("S_DESC"));
                dto.setEmail(jsonObject.getString("S_EMAIL"));
                dto.setGender(jsonObject.getString("S_GENDER"));
                dto.setId(jsonObject.getString("S_ID"));
                dto.setId_card(jsonObject.getString("S_ID_CARD"));
                dto.setIssue_by(jsonObject.getString("S_ISSUE_BY"));
//                dto.setIssue_date(jsonObject.getString("S_ISSUE_DATE"));
                dto.setMobile(jsonObject.getString("S_MOBILE"));
                dto.setPartner_id(jsonObject.getString("S_PARTNER_ID"));
                dto.setPartner_name(jsonObject.getString("S_PARTNER_NAME"));
                dto.setReference_id(jsonObject.getString("S_REFERENCE_ID"));
                dto.setState(jsonObject.getString("S_STATE"));
                dtos.add(dto);
            }
            return dtos;
        });
    }
    @Override
    public Observable<BaseList<UserConfigDto>> total(SQLConnection connection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.USER_ID))
                .add(mIn.get(ParamsPool.USER_NAME))
                .add(mIn.get(ParamsPool.REFERENCE))
                .add(mIn.get(ParamsPool.STATE))
                .add(mIn.get(ParamsPool.PAGE))
                .add(mIn.get(ParamsPool.PAGE_SIZE))
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                ;
        return connection.callWithParamsObservable2(SEARCH_USER, inParams, outParams).map(result -> {
            BaseList dto = new BaseList();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            JsonObject jsonObject = rs.getRows().get(0);
            BaseList baseList = new BaseList();
            baseList.setTotal_items(jsonObject.getInteger("N_TOTAL")!= null ?jsonObject.getInteger("N_TOTAL") : 0);
            return dto;
        });
    }
//    @Override
//    public ResultSet dowloadfile(Connection connection, SummaryQueryDto query) {
//        JsonArray inParams = new JsonArray()
//                .addNull()
//                .addNull()
//                .addNull()
//                .add(QueryMethod.SELECT.toString())
//                .add(query.getMerchantId())
//                .add(query.getMerchantName())
//                .add(query.getFromDate())
//                .add(query.getToDate())
//                .add(query.getMerchantAccount())
//                .add(query.getBankSender())
//                .add(query.getBankReceipt())
//                .add(query.getTimeInterval())
//                ;
//        JsonArray outParams = new JsonArray()
//                .add(OracleTypes.CURSOR)
//                .add(OracleTypes.INTEGER)
//                .add(OracleTypes.VARCHAR)
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                .addNull()
//                ;
//        CallableStatement callableStatement = null;
//        ResultSet rs = null;
//        try {
//            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_USER, inParams, outParams);
//
//            Integer reusltCode = callableStatement.getInt(2);
//
//            if(reusltCode != 200) {
//                LOGGER.log(Level.WARNING, "[DOWNLOAD SUMMARY_FUNDTRANSFER ERROR]: " + callableStatement.getString(3));
//                throw IErrors.INTERNAL_SERVER_ERROR;
//            }
//            rs = (ResultSet) callableStatement.getObject(1);
//        } catch (SQLException ex) {
//            throw IErrors.INTERNAL_SERVER_ERROR;
//        }
//
//        return  rs;
//    }

}
