package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_out.ListSummaryFundsTransDto;
import com.onepay.ma.service.models.pay_out.SummaryFundsTransDto;
import com.onepay.ma.service.models.pay_out.SummaryQueryDto;
import com.onepay.ma.service.service.pay_out.SummaryFundsTransService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.math.BigInteger;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 4:26 PM
 * To change this ma-web.
 */
@Service
public class SummaryFundsTransServiceImpl implements SummaryFundsTransService {
    public static final String SUMMARY_FUNDTRANSFER = "{call pkg_onepayout.SUMMARY_FUNDTRANSFER(?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final Logger LOGGER = Logger.getLogger(SummaryFundsTransServiceImpl.class.getName());
    @Override
    public Observable<List<SummaryFundsTransDto>> getSummaryFundsTrans(SQLConnection connection, Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.MERCHANT_NAME))
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get(ParamsPool.ACCOUNT_ID))
                .add(mIn.get(ParamsPool.BANK_SENDER_SWIFTCODE))
                .add(mIn.get(ParamsPool.BANK_RECEIPT_SWIFTCODE))
                .add(mIn.get(ParamsPool.TIME_INTERVAL))
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                ;
        return connection.callWithParamsObservable2(SUMMARY_FUNDTRANSFER, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            List<SummaryFundsTransDto> transDtos = new ArrayList<>();
            for (JsonObject jsonObject : rs.getRows()) {
                SummaryFundsTransDto transDto = new SummaryFundsTransDto();
                transDto.setAccountId(jsonObject.getString("S_ACCOUNT_ID"));
                transDto.setMerchantId(jsonObject.getString("S_PARTNER_ID"));
                transDto.setMerchantName(jsonObject.getString("S_NAME"));
                transDto.setSenderSwiftCode(jsonObject.getString("S_SENDER_SWIFT_CODE"));
                transDto.setReceiptSwiftCode(jsonObject.getString("S_RECEIPT_SWIFT_CODE"));
                transDto.setCount(BigInteger.valueOf(jsonObject.getLong("N_COUNT")));
                transDto.setAmount(BigInteger.valueOf(jsonObject.getLong("N_AMOUNT")));
                transDto.setDateType(jsonObject.getString("S_DATE_TYPE"));
                transDtos.add(transDto);
            }
            return transDtos;
        });
    }
    @Override
    public Observable<ListSummaryFundsTransDto> getTotalSummaryFundsTrans(SQLConnection connection,Map<String, String> mIn) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(mIn.get(ParamsPool.MERCHANT_ID))
                .add(mIn.get(ParamsPool.MERCHANT_NAME))
                .add(mIn.get(ParamsPool.FROM_DATE))
                .add(mIn.get(ParamsPool.TO_DATE))
                .add(mIn.get(ParamsPool.ACCOUNT_ID))
                .add(mIn.get(ParamsPool.BANK_SENDER_SWIFTCODE))
                .add(mIn.get(ParamsPool.BANK_RECEIPT_SWIFTCODE))
                .add(mIn.get(ParamsPool.TIME_INTERVAL))
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                ;
        return connection.callWithParamsObservable2(SUMMARY_FUNDTRANSFER, inParams, outParams).map(result -> {
            ListSummaryFundsTransDto dto = new ListSummaryFundsTransDto();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            JsonObject jsonObject = rs.getRows().get(0);
            dto.setTotalAmount(BigInteger.valueOf(jsonObject.getLong("N_TOTAL_AMOUNT")));
            dto.setTotalCount(BigInteger.valueOf(jsonObject.getLong("N_TOTAL")));
            return dto;
        });
    }
    @Override
    public ResultSet dowloadfile(Connection connection, SummaryQueryDto query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getMerchantId())
                .add(query.getMerchantName())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getMerchantAccount())
                .add(query.getBankSender())
                .add(query.getBankReceipt())
                .add(query.getTimeInterval())
                ;
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                ;
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SUMMARY_FUNDTRANSFER, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD SUMMARY_FUNDTRANSFER ERROR]: " + callableStatement.getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(1);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return  rs;
    }
}
