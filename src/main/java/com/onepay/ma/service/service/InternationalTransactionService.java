package com.onepay.ma.service.service;

import com.onepay.ma.service.models.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/30/16.
 */
public interface InternationalTransactionService {

    Observable<Transactions<InternationalTransaction>> list(SQLConnection connReadOnly, SQLConnection connOnline, InternationalTxnParameter internationalTxnParameter);
    Observable<InternationalTransaction> get(SQLConnection connOnline, String transactionId);
    Observable<BrandConfig> getBrandConfig(SQLConnection connOnline, String brandId);
    Observable<InternationalTransaction> getByRef(SQLConnection connOnline, String txnRef);//DuongPXT get transaction byref
    Observable<String> getContarctTypeMsp(SQLConnection connOnline, String transactionId);
    Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, InternationalTxnParameter parameter);
    Observable<InternationalTransactionHistory> getHistoryByMerchantRef(SQLConnection sqlOnline, String transactionId, String merchantTransactionRef, String merchantId);
    Observable<InternationalTransactionHistory> getCaptureTransByRef(SQLConnection sqlOnline, String merchantId, String transactionReference);
    ResultSet download(Connection connReadOnly, InternationalTxnParameter parameter, int row) throws SQLException;
    Observable<List<InternationalTransactionHistory>> listHistory(SQLConnection connOnline, SQLConnection connBackup, String transactionId);
    // Observable<List<InternationalTransactionHistory>> listTransAuthorizeHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId);
    // Observable<List<InternationalTransactionHistory>> listTransCaptureHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId);
    // Observable<List<InternationalTransactionHistory>> listTransRefundCaptureHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId);
    Observable<List<InternationalTransaction>> listByIds(SQLConnection connOnline, String transIds, InternationalRefundParameter parameter);
    Observable<List<InternationalTransaction>> listCaptureByIds(SQLConnection connOnline, String transIds, InternationalRefundParameter parameter);// DuongPXT authorize capture : add get capture transaction by id


    ResultSet downloadOnlineByIds(Connection connOnline, String ids, InternationalRefundParameter parameter) throws SQLException;

    Observable<Integer> insertOnePAYManual(SQLConnection connOnline, Integer originalId,Double amount, String transReference,String merchantId );

    Observable<Integer> insertReverseDue(SQLConnection connOnline, Integer originalId,Double amount, String transReference,String merchantId );

    Observable<String> getRiskAssessment(SQLConnection connOnline,String merchantId );

    Observable<Boolean> isApplePay(SQLConnection connOnline, List<Merchant> merchants);

    Observable<Boolean> isDigitalWallet(SQLConnection connOnline, List<Merchant> merchants);

    default  ResultSet downloadOnline(Connection connOnline, InternationalTxnParameter parameter) throws SQLException {
        return  this.download(connOnline, parameter, -1);
    }
    default  ResultSet downloadReadonly(Connection connReadOnly, InternationalTxnParameter parameter, int row) throws SQLException {
        return this.download(connReadOnly, parameter, row);
    }
    default Observable<Map<Integer, InternationalTransaction>> mapByIds(SQLConnection connOnline, String transIds, InternationalRefundParameter parameter) {
        // Duongpxt authorize capture: add search capture trans 
        return this.listByIds(connOnline, transIds, parameter).flatMap(internationalTransactions -> {
            return this.listCaptureByIds(connOnline, transIds, parameter).map(captureTransactions -> {
                Map<Integer, InternationalTransaction> map = new HashMap<>();
                internationalTransactions.addAll(captureTransactions);
                for (InternationalTransaction dt: internationalTransactions) {
                    map.put(dt.getTransaction_id(), dt);
                }
                return map;
            });
           
        });
    }


    default Map<Integer, InternationalTransaction> mapByIdsDownload(Connection connOnline, String transIds, InternationalRefundParameter parameter) throws SQLException {

        ResultSet rs = this.downloadOnlineByIds(connOnline, transIds, parameter);
        HashMap<Integer, InternationalTransaction> map = new HashMap();
        while (rs.next()) {
            String cardNumber = rs.getString("S_CARD_NO");
            String cardDate = rs.getString("S_CARD_EXP");
            String orderInfo = rs.getString("S_ORDER_INFO");
            int acquirerId = rs.getInt("N_ACQUIRER_ID");
            String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
            String currency = rs.getString("S_CURRENCY");
            String authorizationCode = rs.getString("S_AUTHORISATION_CODE");
            String cardType = rs.getString("S_CARD_TYPE");
            String merchantId = rs.getString("S_MERCHANT_ID");
            String authenticationType = rs.getString("S_AUTHENTICATION_TYPE");
            String authenticationState = rs.getString("S_AUTHENTICATION_STATE");
            int transactionId = rs.getInt("EXT_ID");
            int originalId = Integer.valueOf(rs.getString("N_ORIGINAL_ID"));
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
            Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE"));

//        try {
//            transactionTime = new Timestamp(formatter.parse(rs.getString("EXT_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
            double total = rs.getDouble("EXT_AMOUNT");
            String transactionType = rs.getString("EXT_TYPE");
            String transactionStatus = rs.getString("EXT_STATUS");
            String transactionRefNumber = rs.getString("EXT_REF_NUMBER");
            String responseCode = rs.getString("S_RESPONSE_CODE");
//            String binCountry = rs.getString("s_bin_country");
            String avsResultCode = rs.getString("S_AVS_RESULT_CODE");
            String address = rs.getString("S_ADDRESS");
            String state = rs.getString("S_STATE_PROVINCE");
            String zipCode = rs.getString("S_ZIP_POSTAL_CODE");
            String country = rs.getString("S_COUNTRY");
            String city = rs.getString("S_CITY_TOWN");
            String verificationSecurityLevel = rs.getString("S_VERIFICATION_SECURITY_LEVEL");
            String commercialCard = rs.getString("S_COMMERCIAL_CARD");
            String cscResult = rs.getString("S_CSCRESULT_CODE");
            String enrolled3DS = rs.getString("S_3DS_ENROLLED");
            String ip = rs.getString("S_IP");
//            String ipProxy = rs.getString("S_IP_PROXY");
            String operator = rs.getString("S_OPERATOR_ID");
            double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
            //boolean canVoid = rs.getInt("CAN_VOID") != null && rs.getInt("CAN_VOID") == 1 ? true : false;
           // boolean requiredAvs = rs.getInt("N_REQUIRED_AVS") != null && rs.getInt("N_REQUIRED_AVS") == 1 ? true : false;
           // String eci = rs.getString("S_3DS_ECI");


            InternationalTransaction transaction = new InternationalTransaction();
            transaction.setTransaction_id(transactionId);
            transaction.setTransaction_time(transactionTime);
            transaction.setTransaction_type(transactionType);
            transaction.setIp_address(ip);
            transaction.setOrder_info(orderInfo);
            transaction.setTransaction_reference(transactionRef);
//            transaction.setBin_country(binCountry);
            transaction.setCsc_result_code(cscResult);
            transaction.setMerchant_id(merchantId);
            transaction.setOperator(operator);
            transaction.setEnrolled_3ds(enrolled3DS);
            transaction.setTransaction_status(transactionStatus);
            transaction.setOriginal_transaction_id(originalId);
            transaction.setTransaction_ref_number(transactionRefNumber);
            transaction.setVerification_security_level(verificationSecurityLevel);
            transaction.setResponse_code(responseCode);
//            transaction.setIp_proxy(ipProxy);
           // transaction.setCan_void(canVoid);
           // transaction.setIs_required_avs(requiredAvs);
           // transaction.setEci(eci);
            transaction.setAdvance_status(rs.getString("S_ADVANCE_STATUS"));

            Acquirer acquirer = new Acquirer();
            acquirer.setAcquirer_id(acquirerId);
            if (acquirerId == 1) {
                acquirer.setAcquirer_short_name("VietcomBank");
                acquirer.setAcquirer_name("VietcomBank");
            } else if (acquirerId == 2) {
                acquirer.setAcquirer_short_name("VietinBank");
                acquirer.setAcquirer_name("VietinBank");
            } else if (acquirerId == 3){
                acquirer.setAcquirer_short_name("CUP");
                acquirer.setAcquirer_name("CUP");
            } else if (acquirerId == 4){
                acquirer.setAcquirer_short_name("VietcomBank");
                acquirer.setAcquirer_name("VietcomBank");
            } else if (acquirerId == 5){
                acquirer.setAcquirer_short_name("VietcomBank");
                acquirer.setAcquirer_name("VietcomBank");
            } else if (acquirerId == 6){
                acquirer.setAcquirer_short_name("Paypal");
                acquirer.setAcquirer_name("Paypal");
            } else if (acquirerId == 7){
                acquirer.setAcquirer_short_name("Sacombank");
                acquirer.setAcquirer_name("Sacombank");
            } else if (acquirerId == 8){
                acquirer.setAcquirer_short_name("BIDV");
                acquirer.setAcquirer_name("BIDV");
            }else if (acquirerId == 9){
                acquirer.setAcquirer_short_name("Sacombank");
                acquirer.setAcquirer_name("Sacombank");
            }else if (acquirerId == 10){
                acquirer.setAcquirer_short_name("Techcombank");
                acquirer.setAcquirer_name("Techcombank");
            }else if (acquirerId == 11){
                acquirer.setAcquirer_short_name("VPB");
                acquirer.setAcquirer_name("VPB");
            }


            transaction.setAcquirer(acquirer);

            AvsData avsData = new AvsData();
            avsData.setAddress(address);
            avsData.setCountry(country);
            avsData.setCity(city);
            avsData.setProvince(state);
            avsData.setResult_code(avsResultCode);
            avsData.setZip_code(zipCode);

            transaction.setAvs(avsData);

            InternationalAmount amount = new InternationalAmount();
            amount.setCurrency(currency);
            amount.setTotal(total);
            amount.setRefund_total(refundTotal);
//            amount.setRefund_capture_total(refundCaptureTotal);

            transaction.setAmount(amount);

            AuthenticationData authenticationData = new AuthenticationData();
            authenticationData.setAuthentication_state(authenticationState);
            authenticationData.setAuthentication_type(authenticationType);
            authenticationData.setAuthorization_code(authorizationCode);

            transaction.setAuthentication(authenticationData);


            InternationalCard card = new InternationalCard();
            card.setCard_number(cardNumber);
            card.setCard_type(cardType);
            card.setCommercical_card(commercialCard);

            CardDate cardDateData = new CardDate();
            if (cardDate != null && cardDate.length() == 4) {
                cardDateData.setYear(cardDate.substring(0, 2));
                cardDateData.setMonth(cardDate.substring(2, 4));
            }

            card.setCard_date(cardDateData);

            transaction.setCard(card);
            map.put( transaction.getTransaction_id(), transaction);
        }
        return map;

    }

}
