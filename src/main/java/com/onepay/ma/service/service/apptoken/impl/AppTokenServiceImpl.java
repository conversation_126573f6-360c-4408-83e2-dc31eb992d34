package com.onepay.ma.service.service.apptoken.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.appToken.AppToken;
import com.onepay.ma.service.service.apptoken.AppTokenService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 22-Sep-17.
 */
@Service
public class AppTokenServiceImpl implements AppTokenService {

    @Override
    public Observable<Void> insert(SQLConnection connBackup, AppToken appToken) {
        JsonArray inParams = new JsonArray()
                .add(appToken.getUserId())
                .add(appToken.getToken())
                .add(appToken.getLanguage())
                .add(appToken.getOs())
                .add(appToken.getOs_version());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connBackup.callWithParamsObservable2(INSERT_APP_TOKEN, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(5);

            if(reusltCode != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT APP TOKEN ERROR]: " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;
        });
    }

    @Override
    public Observable<List<AppToken>> findByUserIds(SQLConnection sqlConnection, String userIds) {
        JsonArray inParams = new JsonArray()
                .add(userIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(FIND_TOKEN_BY_USERIDS, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[FIND APP TOKEN ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            List<AppToken> list = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    AppToken appToken = new AppToken();
                    appToken.setUserId(jsonObject.getString("S_USER_ID"));
                    appToken.setToken(jsonObject.getString("S_TOKEN"));
                    appToken.setLanguage(jsonObject.getString("S_LANGUAGE"));
                    appToken.setOs(jsonObject.getString("S_OS"));
                    list.add(appToken);
                }
            }

            return list;
        });
    }


    @Override
    public Observable<Void> removeToken(SQLConnection sqlConnection, String token) {
        JsonArray inParams = new JsonArray()
                .add(token);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(REMOVE_TOKEN, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(1);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[REMOVE APP TOKEN ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return null;
        });
    }

    private static final String REMOVE_TOKEN = "{call PKG_APP_TOKEN.remove_token(?,?,?)}";
    private static final String FIND_TOKEN_BY_USERIDS = "{call PKG_APP_TOKEN.find_token_by_users_2(?,?,?,?)}";
    private static final String INSERT_APP_TOKEN = "{call PKG_APP_TOKEN.insert_app_token(?,?,?,?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(AppTokenServiceImpl.class.getName());

}
