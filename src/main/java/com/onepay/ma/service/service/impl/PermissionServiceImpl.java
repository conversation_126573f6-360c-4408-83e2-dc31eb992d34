package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PermissionData;
import com.onepay.ma.service.models.PermissionRole;
import com.onepay.ma.service.models.PostPermissionParam;
import com.onepay.ma.service.models.PutPermissionParam;
import com.onepay.ma.service.service.PermissionService;
import com.onepay.ma.service.service.RoleService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by h<PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
@Service
public class PermissionServiceImpl implements PermissionService {


    @Override
    public Observable<List<PermissionData>> list(SQLConnection connection, String keywords) {
        return getListPermission(connection, keywords).flatMap(permissionDatas -> {
            // get role list
            return getListRoleByPermission(connection, permissionDatas, 0);
        });
    }

    @Override
    public Observable<List<PermissionRole>> listByRoleId(SQLConnection connection, String roleId) {
        return getPermissionRoleId(connection, roleId);
    }

    @Override
    public Observable<PermissionData> get(SQLConnection connection, int id) {
        return getPermission(connection, id).flatMap(permissionData -> {
            // get role list
            return roleService.listRoleByPermissionId(connection, id).map(roleDatas -> {
                permissionData.setRoles(roleDatas);
                return permissionData;
            });
        });
    }

    @Override
    public Observable<PermissionData> updateParentOrder(SQLConnection connection, String parentId, String orderId, String permissionId) {
        return updatePermissionParentOrder(connection, parentId, orderId, permissionId);
    }

    @Override
    public Observable<PermissionData> update(SQLConnection connection, PutPermissionParam parameter) {
        return updatePermission(connection, parameter);
    }

    @Override
    public Observable<PermissionData> insert(SQLConnection connection, PostPermissionParam parameter) {
        return insertPermission(connection, parameter);
    }

    @Override
    public Observable<Integer> delete(SQLConnection connection, int id) {
        return deletePermission(connection, id);
    }

    /**
     * get permission by role id
     * @param connection
     * @param roleId
     * @return
     */
    private Observable<List<PermissionRole>> getPermissionRoleId(SQLConnection connection, String roleId) {
        JsonArray inParams = new JsonArray()
                .add(roleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_ROLES, inParams, outParams).map(result -> {
            List<PermissionRole> permissionDatas = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PermissionRole permissionData = bindPermissionRole(jsonObject);
                permissionDatas.add(permissionData);
            }

            return permissionDatas;
        });
    }

    /**
     * delete permission data
     * @param connection
     * @param id
     * @return
     */
    private Observable<Integer> deletePermission(SQLConnection connection , int id){

        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_DELETE, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if(resultCode != 200){
                LOGGER.log(Level.SEVERE, "[ PERMISSION ] Delete Permission Failed" + StringPool.SPACE + result.getOutput().getString(2));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }

            return resultCode;
        });
    }

    /**
     * insert permission data
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<PermissionData> insertPermission(SQLConnection connection , PostPermissionParam parameter){

        JsonArray inParams = new JsonArray()
                .add(parameter.getParent_id())
                .add(parameter.getPermission_name())
                .add(parameter.getIcon())
                .add(parameter.is_root_permission())
                .add(parameter.is_abstract())
                .add(parameter.getModule_name())
                .add(parameter.getModule_url())
                .add(parameter.getItem_name())
                .add(parameter.getDirective_url())
                .add(parameter.getController_url())
                .add(parameter.getDescription())
                .add(parameter.getState())
                .add(parameter.getPath_regex());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_INSERT, inParams, outParams).flatMap(result -> {
            int resultCode = 0;
            int returnValue = 0;
            resultCode = result.getOutput().getInteger(14);
            if(resultCode != 201){
                LOGGER.log(Level.SEVERE, "[ PERMISSION ] Delete Permission Failed" + StringPool.SPACE + result.getOutput().getString(15));
                throw  IErrors.INTERNAL_SERVER_ERROR;

            }else{
                returnValue = result.getOutput().getInteger(13);
            }

            return getPermission(connection, returnValue);
        });
    }

    /**
     * update permission data
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<PermissionData> updatePermission(SQLConnection connection , PutPermissionParam parameter){

        JsonArray inParams = new JsonArray()
                .add(parameter.getPermission_id())
                .add(parameter.getPermission_name())
                .add(parameter.getIcon())
                .add(parameter.is_root_permission())
                .add(parameter.is_abstract())
                .add(parameter.getModule_name())
                .add(parameter.getModule_url())
                .add(parameter.getItem_name())
                .add(parameter.getDirective_url())
                .add(parameter.getController_url())
                .add(parameter.getDescription())
                .add(parameter.getState())
                .add(parameter.getPath_regex());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_UPDATE, inParams, outParams).flatMap(result -> {
            int resultCode = result.getOutput().getInteger(13);
            if(resultCode != 200){
                LOGGER.log(Level.SEVERE, "[ PERMISSION ] Update Permission Failed" + StringPool.SPACE + result.getOutput().getString(14));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return getPermission(connection, parameter.getPermission_id());
        });
    }

    /**
     * get permission data
     * @param connection
     * @param id
     * @return
     */
    private Observable<PermissionData> getPermission(SQLConnection connection , int id){
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_BY_ID, inParams, outParams).map(result -> {
            PermissionData permissionData = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            permissionData = bindPermission(jsonObject);

            return permissionData;
        });
    }

    /**
     * get list permission data
     * @param connection
     * @param keywords
     * @return
     */
    private Observable<List<PermissionData>> getListPermission(SQLConnection connection, String keywords){
        JsonArray inParams = new JsonArray()
                .add(keywords);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_SEARCH, inParams, outParams).map(result -> {
            List<PermissionData> permissionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PermissionData permission = bindPermission(jsonObject);
                permissionList.add(permission);
            }

            return permissionList;
        });
    }

    /**
     * get list merchant for list channel
     * @param sqlConnection
     * @param permissionList
     * @param index
     * @return
     */
    private Observable<List<PermissionData>> getListRoleByPermission(SQLConnection sqlConnection, List<PermissionData> permissionList, int index){
        if(permissionList.size() <= 0){
            return Observable.just(permissionList);
        }
        PermissionData permission = permissionList.get(index);
        final int finalIndex = index;
        return Observable.just(permission).flatMap(userData -> {
            //insert approval for user group
            return roleService.listRoleByPermissionId(sqlConnection, permission.getPermission_id()).flatMap(roles -> {
                permission.setRoles(roles);
                if(finalIndex >= permissionList.size() - 1){
                    return Observable.just(permissionList);
                }else{
                    return getListRoleByPermission(sqlConnection, permissionList, finalIndex + 1);
                }
            });
        });

    }
    /**
     * update permission parent and order
     * @param connection
     * @param parentId
     * @param order
     * @param permissionId
     * @return
     */
    private Observable<PermissionData> updatePermissionParentOrder(SQLConnection connection , String parentId, String order, String permissionId){

        JsonArray inParams = new JsonArray()
                .add(permissionId)
                .add(parentId)
                .add(order);

        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(PERMISSION_UPDATE_PARENT_ORDER, inParams, outParams).flatMap(result -> {
            int resultCode  = result.getOutput().getInteger(3);
            if(resultCode != 200){
                LOGGER.log(Level.SEVERE, "[ PERMISSION ] Update Permission Failed" + StringPool.SPACE + result.getOutput().getString(4));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return getPermission(connection, Integer.valueOf(permissionId));
        });
    }
    /**
     * bind result set to permission
     * @param rs
     * @return
     * @throws SQLException
     */
    private PermissionData bindPermission(JsonObject rs) {
        int id = rs.getInteger("FUNC_ID");
        int parentId = rs.getInteger("FUNC_PARENT_ID");
        String name = rs.getString("FUNC_NAME");
        String moduleUrl = rs.getString("S_MODULE_URL");
        String icon = rs.getString("FUNC_ICON");
        String state = rs.getString("FUNC_STATE");
        boolean isAbstract = rs.getInteger("N_ABSTRACT") != null && rs.getInteger("N_ABSTRACT") == 1 ? true : false ;
        String moduleName = rs.getString("S_MODULE_NAME");
        String controller = rs.getString("S_CONTROLLER_URL");
        String desc = rs.getString("S_DESC");
        //  String factoryUrl = rs.getString("S_FACTORY_URL");
        String directiveUrl = rs.getString("S_DIRECTIVE_URL");
        String item_name = rs.getString("S_ITEM_NAME");
        boolean isRootPermission = rs.getInteger("N_ROOT_PERMISSION") != null && rs.getInteger("N_ROOT_PERMISSION") == 1 ? true : false ;
        //String abstractStateUrl = rs.getString("S_ABSTRACT_STATE_URL");
        String path = rs.getString("S_PATH_REGEX");
        //String method = rs.getString("S_METHOD");
        int order = rs.getInteger("N_ORDER");

        PermissionData permission = new PermissionData();
        permission.setPermission_id(id);
        permission.setParent_id(parentId);
        permission.setIs_abstract(isAbstract);
        permission.setPermission_name(name);
        permission.setIcon(icon);
        permission.setState(state);
        permission.setDescription(desc);
        permission.setModule_url(moduleUrl);
        permission.setModule_name(moduleName);
        permission.setController_url(controller);
        permission.setIs_root_permission(isRootPermission);
        // permission.setFactory_url(factoryUrl);
        permission.setDirective_url(directiveUrl);
        permission.setItem_name(item_name);
        // permission.setTemplate_module_url(templateUrl);
        // permission.setAbstract_state_url(abstractStateUrl);

        permission.setPath_regex(path);
        permission.setOrder(order);

        return permission;
    }

    /**
     * bind result set to permission
     * @param rs
     * @return
     * @throws SQLException
     */
    private PermissionRole bindPermissionRole(JsonObject rs){
        int id = rs.getInteger("N_FUNC_ID");
        String name = rs.getString("S_FUNC_NAME");
        int order = rs.getInteger("N_ORDER");

        PermissionRole permission = new PermissionRole();
        permission.setPermission_id(id);
        permission.setPermission_name(name);
        permission.setOrder(order);


        return permission;
    }

    @Autowired
    private RoleService roleService;


    private static final String PERMISSION_BY_ID =  "{call PKG_MERCHANTPORTAL_2.FUNCTION_BY_ID(?,?,?,?)}";

    private static final String PERMISSION_UPDATE =  "{call PKG_MERCHANTPORTAL_2.FUNCTION_UPDATE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String PERMISSION_INSERT =  "{call PKG_MERCHANTPORTAL_2.FUNCTION_INSERT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String PERMISSION_DELETE =  "{call PKG_MERCHANTPORTAL_2.FUNCTION_DELETE(?,?,?)}";

    private static final String PERMISSION_SEARCH =  "{call PKG_MERCHANTPORTAL_2.FUNCTION_SEARCH(?,?,?,?)}";

    private static final String PERMISSION_ROLES = "{call PKG_MERCHANTPORTAL_2.ROLES_PERMISSION(?,?,?,?)}";

    private static final String PERMISSION_UPDATE_PARENT_ORDER =  "{call PKG_MERCHANTPORTAL_2.PERMISSION_UPDATE_PARENT_ORDER(?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PermissionServiceImpl.class.getName());

}
