package com.onepay.ma.service.service.notification.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.models.notification.NotificationConfigSearchQuery;
import com.onepay.ma.service.service.notification.AppNotificationConfigService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 25-Sep-17.
 */
@Service
public class AppNotificationConfigServiceImpl implements AppNotificationConfigService {

    @Override
    public Observable<Void> delete(SQLConnection connectionB, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(DELETE_CONFIG, inParams, outParams).flatMap(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DELETE mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return Observable.just(null);
        });
    }

    @Override
    public Observable<MpayNotificationConfig> get(SQLConnection connectionB, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(GET_CONFIG, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayNotificationConfig mpayNotificationConfig = null;

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    mpayNotificationConfig = this.bind(jsonObject);
                }
            }

            return mpayNotificationConfig;
        });
    }

    @Override
    public Observable<BaseList<MpayNotificationConfig>> search(SQLConnection connectionB, NotificationConfigSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getMerchantId())
                .add(query.getUserId())
                .add(query.getShopId())
                .add(query.getTerminalId())
                .add(query.getType())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEARCH_CONFIG, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(9);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BaseList<MpayNotificationConfig> baseList = new BaseList<>();
            List<MpayNotificationConfig> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    transactionList.add(this.bind(jsonObject));
                }
                baseList.setTotal_items(result.getOutput().getInteger(8));
            } else {
                baseList.setTotal_items(0);
            }
            baseList.setList(transactionList);
            return baseList;
        });
    }

    @Override
    public Observable<List<String>> findUserBy(SQLConnection connectionB, NotificationConfigSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getMerchantId())
                .add(query.getShopId())
                .add(query.getTerminalId())
                .add(query.getType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEARCH_USER_BY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(5);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[Find USER ID BY mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<String> idList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    idList.add(jsonObject.getString("S_USER_ID"));
                }
            }
            return idList;
        });
    }

    @Override
    public Observable<MpayNotificationConfig> insert(SQLConnection connectionB, MpayNotificationConfig config) {
        JsonArray inParams = new JsonArray()
                .add(config.getMerchantId())
                .add(config.getUserId())
                .add(config.getStoreId())
                .add(config.getTerminalId())
                .add(config.getType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(INSERT_CONFIG, inParams, outParams).flatMap(result -> {
            Integer reusltCode = result.getOutput().getInteger(6);

            if(reusltCode != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return this.get(connectionB, result.getOutput().getInteger(5));
        });
    }

    @Override
    public Observable<MpayNotificationConfig> update(SQLConnection connectionB, MpayNotificationConfig config) {
        JsonArray inParams = new JsonArray()
                .add(config.getId())
                .add(config.getMerchantId())
                .add(config.getUserId())
                .add(config.getStoreId())
                .add(config.getTerminalId())
                .add(config.getType());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(UPDATE_CONFIG, inParams, outParams).flatMap(result -> {
            Integer reusltCode = result.getOutput().getInteger(6);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[UPDATE mPAY NOTIFICATION CONFIG ERROR]: " + result.getOutput().getString(7));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return get(connectionB, config.getId());
        });
    }

    private  MpayNotificationConfig bind(JsonObject js) {
        MpayNotificationConfig result = new MpayNotificationConfig();

        String merchantId =js.getString("S_MERCHANT_ID");
        String shopId =js.getString("S_STORE_ID");
        String type =js.getString("S_TYPE");
        String userId =js.getString("S_USER_ID");
        String terminalId = js.getString("S_TERMINAL_ID");
        Integer id = js.getInteger("N_ID");

        result.setMerchantId(merchantId);
        result.setTerminalId(terminalId);
        result.setStoreId(shopId);
        result.setType(type);
        result.setUserId(userId);
        result.setCreated_date(Timestamp.valueOf(js.getString("D_CREATE")));
        result.setId(id);

        result.setStoreIds(shopId == null ? new ArrayList<>() : Arrays.asList(shopId.split(StringPool.COMMA)));
        result.setTerminalIds(terminalId == null ? new ArrayList<>()  : Arrays.asList(terminalId.split(StringPool.COMMA)));
        result.setUserIds(userId == null ? new ArrayList<>()  : Arrays.asList(userId.split(StringPool.COMMA)));
        result.setTypes(type == null ? new ArrayList<>()  : Arrays.asList(type.split(StringPool.COMMA)));

        return result;
    }

    private static final String INSERT_CONFIG= "{call PKG_MPAY_NOTIFY.insert_config(?,?,?,?,?,?,?,?)}";
    private static final String UPDATE_CONFIG= "{call PKG_MPAY_NOTIFY.update_config(?,?,?,?,?,?,?,?)}";

    private static final String GET_CONFIG = "{call PKG_MPAY_NOTIFY.get_config(?,?,?,?)}";

    private static final String DELETE_CONFIG = "{call PKG_MPAY_NOTIFY.remove_config(?,?,?)}";
    private static final String SEARCH_CONFIG= "{call PKG_MPAY_NOTIFY.search_config(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String SEARCH_USER_BY= "{call PKG_MPAY_NOTIFY.filter_user(?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(AppNotificationConfigServiceImpl.class.getName());
}
