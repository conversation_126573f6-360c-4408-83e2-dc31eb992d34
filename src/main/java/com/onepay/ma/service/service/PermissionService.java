package com.onepay.ma.service.service;


import com.onepay.ma.service.models.PermissionData;
import com.onepay.ma.service.models.PermissionRole;
import com.onepay.ma.service.models.PostPermissionParam;
import com.onepay.ma.service.models.PutPermissionParam;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/4/16.
 */
public interface PermissionService {
    Observable<List<PermissionData>> list(SQLConnection connection, String keywords);
    Observable<List<PermissionRole>> listByRoleId(SQLConnection connection, String roleId);
    Observable<PermissionData> get(SQLConnection connection, int id);
    Observable<PermissionData> updateParentOrder(SQLConnection connection, String parentId, String orderId, String permissionId);
    Observable<PermissionData> update(SQLConnection connection, PutPermissionParam parameter);
    Observable<PermissionData> insert(SQLConnection connection, PostPermissionParam parameter);
    Observable<Integer> delete(SQLConnection connection, int id);
}
