package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionMerchant;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionMerchantService {
    Observable<List<PromotionMerchant>> list(SQLConnection sqlConnPr, String promotionId);
    Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, String merchantId);
    Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId);
}
