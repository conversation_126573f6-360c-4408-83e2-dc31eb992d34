package com.onepay.ma.service.service;

import com.onepay.ma.service.models.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface CdrService {
    Observable<Transactions<CompareCdr>> list(SQLConnection sqlConCdr, CdrParameter parameter);
    Observable<Integer> getTotal(SQLConnection sqlConCdr, CdrParameterPost parameter);
    ResultSet download(Connection sqlConCdr, CdrParameterPost parameter) throws SQLException;
    Observable<FileLine> getFileLine(SQLConnection sqlConCdr, String fileLineId);
    Observable<Integer> update(SQLConnection sqlConCdr, String id, String transId, String resultCode,  String description);
    Observable<List<CdrServiceModel>> listService(SQLConnection sqlConCdr, String source, String service);
}
