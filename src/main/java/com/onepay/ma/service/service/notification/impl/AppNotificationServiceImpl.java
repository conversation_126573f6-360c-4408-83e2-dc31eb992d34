package com.onepay.ma.service.service.notification.impl;

import com.onepay.ma.service.models.notification.*;
import com.onepay.ma.service.util.DateTimeUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.service.notification.AppNotificationService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 09-Oct-17.
 */
@Service
public class AppNotificationServiceImpl implements AppNotificationService {

    @Override
    public Observable<MpayNotifications> search(SQLConnection connectionB, NotificationSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getUserId())
                .add(query.getLanguage())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEARCH_NOTIFY, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(7);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY NOTIFICATION ERROR]: " + result.getOutput().getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayNotifications baseList = new MpayNotifications();
            List<MpayNotification> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(this.bind(jsonObject));
                }
                baseList.setList(transactionList);
                baseList.setTotal_items(result.getOutput().getInteger(6));
            } else {

                baseList.setTotal_items(0);
            }
            baseList.setTotal_unseen(result.getOutput().getInteger(5));
            return baseList;
        });
    }

    @Override
    public Observable<Integer> insert(SQLConnection connectionB, MpayNotificationPostDto notify) {
        JsonArray inParams = new JsonArray()
                .add(notify.getHeader_en())
                .add(notify.getHeader_vi())
                .add(notify.getContent_en())
                .add(notify.getContent_vi())
                .add(notify.getUserId())
                .add(notify.getTarget() == null ? "" : notify.getTarget())
                .add(notify.getCategory());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(INSERT_NOTIFY, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(8);

            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT mPAY NOTIFICATION ERROR]: " + result.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return result.getOutput().getInteger(7);
        });
    }

    @Override
    public Observable<Void> see(SQLConnection connectionB, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEE_NOTIFY, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(1);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEE mPAY NOTIFICATION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return null;
        });
    }

    @Override
    public Observable<MpayNotifications> searchMobileNotification(SQLConnection connectionB, NotificationSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getKeywords())
                .add(query.getUserId())
                .add(query.getGroup())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEARCH_MOBILE_NOTIFY, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(8);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH MOBILE NOTIFICATION ERROR]: " + result.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayNotifications baseList = new MpayNotifications();
            List<MpayNotification> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(this.bind(jsonObject));
                }
                baseList.setList(transactionList);
                baseList.setTotal_items(result.getOutput().getInteger(7));
            } else {

                baseList.setTotal_items(0);
            }
            baseList.setTotal_unseen(result.getOutput().getInteger(6));
            return baseList;
        });
    }

    @Override
    public Observable<BaseList<MpayNotificationGroup>> searchMobileNotificationGroup(SQLConnection connectionB, String keywords, String userId) {
        JsonArray inParams = new JsonArray()
                .add(keywords)
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connectionB.callWithParamsObservable2(SEARCH_MOBILE_NOTIFY_GROUP, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(3);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY NOTIFICATION ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BaseList<MpayNotificationGroup> baseList = new BaseList<>();
            List<MpayNotificationGroup> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(this.bindGroup(jsonObject));
                }
                baseList.setList(transactionList);
            }
            return baseList;
        });
    }

    private MpayNotificationGroup bindGroup(JsonObject js) {
        MpayNotificationGroup result = new MpayNotificationGroup();

        String content = js.getString("S_CONTENT");
        String header = js.getString("S_HEADER");
        String lastDate = js.getString("D_LAST");
        Integer count = js.getInteger("N_COUNT");

        result.setLast_content(content);
        result.setHeader(header);
        result.setCount_item(count);
        result.setLast_date(lastDate == null ? null : Timestamp.valueOf(lastDate));


        return result;
    }

    private MpayNotification bind(JsonObject js) {
        MpayNotification result = new MpayNotification();

        String content = js.getString("S_CONTENT");
        String header = js.getString("S_HEADER");
        String target = js.getString("S_TARGET");
        String category = js.getString("S_CATEGORY");
        String userId = js.getString("S_USER_ID");
        String state = js.getString("S_STATE");
        String createDate = js.getString("D_CREATE");
        Integer id = js.getInteger("N_ID");

        result.setContent(content);
        result.setHeader(header);
        result.setTarget(target);
        result.setCategory(category);
        result.setUserId(userId);
        result.setState(state);
        result.setId(id);
        result.setCreate_date(createDate == null ? null : Timestamp.valueOf(createDate));
        result.setS_create_date(createDate == null ? null : DateTimeUtil.iso8601(DateTimeUtil.convertStringtoDate(createDate, DateTimeUtil.DateTemplate.YYYY_MM_DD_HHmmss)));


        return result;
    }


    private static final String INSERT_NOTIFY = "{call PKG_MPAY_NOTIFY.insert_notify(?,?,?,?,?,?,?,?,?,?)}";
    private static final String SEE_NOTIFY = "{call PKG_MPAY_NOTIFY.see_notify(?,?,?)}";


    private static final String SEARCH_NOTIFY = "{call PKG_MPAY_NOTIFY.search_notify(?,?,?,?,?,?,?,?,?)}";

    private static final String SEARCH_MOBILE_NOTIFY_GROUP = "{call PKG_MPAY_NOTIFY.get_mobile_notify_group(?,?,?,?,?)}";

    private static final String SEARCH_MOBILE_NOTIFY = "{call PKG_MPAY_NOTIFY.get_mobile_notify(?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(AppNotificationService.class.getName());
}
