package com.onepay.ma.service.service.notification.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.DateTimeUtil;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.notification.*;
import com.onepay.ma.service.service.notification.CustomerAppNotifyService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 26-Mar-18.
 */
@Service
public class CustomerAppNotifyServiceImpl implements CustomerAppNotifyService {

    @Override
    public List<Map<String, String>> getAppTokenByMobileDSP(Connection readonlyConn, String mobile) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(mobile);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        List<Map<String, String>> idList = new ArrayList<>();
        ResultSet rs = null;

        try {
            callableStatement = ExportDatasourceUtil.execute(readonlyConn, SEARCH_USER_BY, inParams, outParams);
            Integer reusltCode = callableStatement.getInt(3);
            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[Find USER ID BY mPAY NOTIFICATION CONFIG ERROR]: " + callableStatement.getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(2);
            if (rs != null) {
                while (rs.next()) {
                    HashMap hmap = new HashMap();
                    hmap.put("userId", rs.getString("S_USER_ID"));
                    hmap.put("token", rs.getString("S_NOTIFICATION_TOKEN"));
                    hmap.put("os", rs.getString("S_OS_NAME"));
                    idList.add(hmap);
                }
            }
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (rs != null) {
                rs.close();
            }
        }

        return idList;
    }

    @Override
    public Integer insertMsg(Connection backupConn, MpayvnNotificationMsg msg) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(msg.getHeader_en())
                .add(msg.getHeader_vi())
                .add(msg.getContent_en())
                .add(msg.getContent_vi())
                .add(msg.getSender())
                .add(msg.getTarget())
                .add(msg.getCategory())
                .add(msg.getDestination().isEmpty() ? "ALL" : msg.getDestination())
                .add(msg.getHtml_content_en())
                .add(msg.getHtml_content_vi());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        Integer result = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(backupConn, INSERT_NOTIFY_MSG, inParams, outParams);
            Integer resultCode = callableStatement.getInt(12);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT mPAYvn CUSTOMER NOTIFICATION MSG ERROR]: " + callableStatement.getString(13));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            result = callableStatement.getInt(11);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
        }
        return result;
    }

    @Override
    public Integer insertNotify(Connection backupConn, MpayNotificationPostDto notification) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(notification.getMsgId())
                .add(notification.getUserId())
                .add(notification.getToken());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        Integer result = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(backupConn, INSERT_NOTIFY, inParams, outParams);
            Integer resultCode = callableStatement.getInt(5);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[INSERT mPAYvn CUSTOMER NOTIFICATION  ERROR]: " + callableStatement.getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            result = callableStatement.getInt(4);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
        }

        return result;
    }

    @Override
    public Observable<BaseList<MpayvnNotificationMsg>> searchMsg(SQLConnection backupConn, MpayvnNotificationMsgQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getState())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(SEARCH_MSG, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(8);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAYvn CUSTOMER NOTIFICATION MSG ERROR]: " + result.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            BaseList<MpayvnNotificationMsg> baseList = new BaseList<>();
            List<MpayvnNotificationMsg> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(6).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(this.bindMsg(jsonObject));
                }
                baseList.setTotal_items(result.getOutput().getInteger(7));
            } else {
                baseList.setTotal_items(0);
            }
            baseList.setList(transactionList);
            return baseList;
        });
    }

    @Override
    public Observable<MpayNotifications> getNotifyByDspId(SQLConnection backupConn, NotificationSearchQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getUserId())
                .add(query.getLanguage())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(SEARCH_NOTIFY_BY_DSP_USER_ID, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(7);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY CUSTOMER NOTIFICATION By USER ID ERROR]: " + result.getOutput().getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayNotifications baseList = new MpayNotifications();
            List<MpayNotification> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(this.bindNotify(jsonObject));
                }
                baseList.setList(transactionList);
                baseList.setTotal_items(result.getOutput().getInteger(6));
            } else {

                baseList.setTotal_items(0);
            }
            baseList.setTotal_unseen(result.getOutput().getInteger(5));
            return baseList;
        });
    }

    @Override
    public Observable<Void> see(SQLConnection backupConn, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(SEE_NOTIFY, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(1);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEE mPAY CUSTOMER NOTIFICATION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return null;
        });
    }

    @Override
    public Void updateMsgState(Connection backupConn, Integer id, String state) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(id)
                .add(state);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);


        CallableStatement callableStatement = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(backupConn, UPDATE_NOTIFY_MSG_STATE, inParams, outParams);
            Integer resultCode = callableStatement.getInt(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[UPDATE mPAYvn CUSTOMER NOTIFICATION MSG STATE  ERROR]: " + callableStatement.getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
        }
        return null;
    }

    @Override
    public Observable<MpayvnNotificationMsg> getNotifiMsgById(SQLConnection backupConn, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return backupConn.callWithParamsObservable2(GET_MSG, inParams, outParams).map(result -> {
            Integer resultCode = result.getOutput().getInteger(2);

            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY CUSTOMER NOTIFICATION By USER ID ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayvnNotificationMsg msg = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    msg = this.bindMsg(jsonObject);
                    //doi html<-->content;
                    msg.setContent_en(msg.getHtml_content_en());
                    msg.setContent_vi(msg.getHtml_content_vi());
                    msg.setHtml_content_en(msg.getHtml_content_en());
                    msg.setHtml_content_vi(msg.getHtml_content_vi());

                }
            }
            return msg;
        });
    }

    private MpayvnNotificationMsg bindMsg(JsonObject jsonObject) {
        MpayvnNotificationMsg msg = new MpayvnNotificationMsg();
        msg.setId(jsonObject.getInteger("N_ID"));
        msg.setCategory(jsonObject.getString("S_CATEGORY"));
        msg.setHeader_en(jsonObject.getString("S_HEADER_EN"));
        msg.setHeader_vi(jsonObject.getString("S_HEADER_VI"));
        msg.setContent_en(jsonObject.getString("S_CONTENT_EN"));
        msg.setContent_vi(jsonObject.getString("S_CONTENT_VI"));
        msg.setTarget(jsonObject.getString("S_TARGET"));
        msg.setDestination(jsonObject.getString("S_DESTINATION"));
        msg.setState(jsonObject.getString("S_STATE"));
        msg.setSender(jsonObject.getString("S_SENDER_EMAIL"));
        msg.setSeen_total(jsonObject.getInteger("N_SEEN_TOTAL"));
        msg.setSend_total(jsonObject.getInteger("N_SEND_TOTAL"));
        msg.setCreate_date(jsonObject.getString("D_CREATE") == null ? null : Timestamp.valueOf(jsonObject.getString("D_CREATE")));

        msg.setHtml_content_en(jsonObject.getString("S_HTML_CONTENT_EN"));
        msg.setHtml_content_vi(jsonObject.getString("S_HTML_CONTENT_VI"));
        ;

        return msg;
    }


    private MpayNotification bindNotify(JsonObject jsonObject) {
        MpayNotification notification = new MpayNotification();
        notification.setId(jsonObject.getInteger("N_ID"));
        notification.setCategory(jsonObject.getString("S_CATEGORY"));
        notification.setHeader(jsonObject.getString("S_HEADER"));
        notification.setContent(jsonObject.getString("S_CONTENT"));
        notification.setTarget(jsonObject.getString("S_TARGET"));
        notification.setUserId(jsonObject.getString("S_DSP_USER_ID"));
        notification.setState(jsonObject.getString("S_STATE"));
        notification.setMsg_id(jsonObject.getInteger("N_MSG_ID"));
        notification.setCreate_date(jsonObject.getString("D_CREATE") == null ? null : Timestamp.valueOf(jsonObject.getString("D_CREATE")));
        notification.setS_create_date(jsonObject.getString("D_CREATE") == null ? null : DateTimeUtil.iso8601(DateTimeUtil.convertStringtoDate(jsonObject.getString("D_CREATE"), DateTimeUtil.DateTemplate.YYYY_MM_DD_HHmmss)));

        return notification;
    }


    private static final String INSERT_NOTIFY = "{call PKG_MPAY_NOTIFY.insert_user_notify(?,?,?,?,?,?)}";
    private static final String INSERT_NOTIFY_MSG = "{call PKG_MPAY_NOTIFY.insert_customer_notify_msg(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String SEARCH_USER_BY = "{call PKG_DSP_NOTIFY.filter_user_mpayvn(?,?,?,?)}";

    private static final String SEARCH_NOTIFY_BY_DSP_USER_ID = "{call PKG_MPAY_NOTIFY.get_user_notify_by_id(?,?,?,?,?,?,?,?,?)}";
    private static final String SEARCH_MSG = "{call PKG_MPAY_NOTIFY.search_user_notify_msg(?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_MSG = "{call PKG_MPAY_NOTIFY.get_user_notify_msg(?,?,?,?)}";


    private static final String SEE_NOTIFY = "{call PKG_MPAY_NOTIFY.see_user_notify_by_id(?,?,?)}";
    private static final String UPDATE_NOTIFY_MSG_STATE = "{call PKG_MPAY_NOTIFY.update_user_notify_msg_state(?,?,?,?)}";

    private static Logger LOGGER = Logger.getLogger(CustomerAppNotifyServiceImpl.class.getName());
}
