package com.onepay.ma.service.service.notification;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.notification.*;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by anhkh on 09-Oct-17.
 */
public interface AppNotificationService {


    Observable<MpayNotifications> search(SQLConnection connectionB, NotificationSearchQuery query);

    Observable<Integer> insert(SQLConnection connectionB, MpayNotificationPostDto notify);

    Observable<Void> see(SQLConnection connectionB, Integer id);

    Observable<MpayNotifications> searchMobileNotification(SQLConnection connectionB, NotificationSearchQuery query);

    Observable<BaseList<MpayNotificationGroup>> searchMobileNotificationGroup(SQLConnection connectionB, String keywords, String userId);

}
