package com.onepay.ma.service.service.reconcile.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationDetailQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.service.reconcile.ReconcileService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by  tuydv on 19/09/18.
 */
@Service
public class ReconcileServiceImpl implements ReconcileService {

    @Override
    public Observable<List<Reconciliation>> listPayment(SQLConnection connection, ReconciliationQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchant_id())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date())
                .add(parameter.getInterval())
                .add(parameter.getAcquirer())
                .add(parameter.getCard_type())
                .add(parameter.getCurrency())
                .add(parameter.getTransaction_type());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(RECONCILE_PAYMENT, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(10);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "PAYMENT RECONCILE REPORT ERROR: " + result.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<Reconciliation> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(9).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            JsonObject objectData = new JsonObject();
            String date = "";
            String cardType = "";
            String currency = "";
            String acquirerId = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                    if (!jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase()) || !jsonObject.getString("S_ACQUIRERID").equals(acquirerId)) {
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_ACQUIRER_ID", acquirerId);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);

                        objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                        objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                        objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                        objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                        objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                        objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                        objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));
                        Reconciliation internationalReport = bindReconciliation(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }


                date = jsonObject.getString("D_DATE");

                String cardTypeData = "";
                if (jsonObject.getString("S_CARDTYPE") != null) {
                    cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();
                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                acquirerId = jsonObject.getString("S_ACQUIRERID");
//                merchantId =jsonObject.getString("S_MERCHANTID");

                int count = jsonObject.getInteger("N_COUNT");
                if (!objectData.isEmpty()) {
                    count += objectData.getInteger("TRANS_COUNT");
                }
                objectData.put("TRANS_COUNT", count);

                if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                    objectData.put("PURCHASE_COUNT", jsonObject.getInteger("N_COUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Refund")) {
                    objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Void Purchase")) {
                    objectData.put("VOID_PURCHASE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("VOID_PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Capture")) {
                    objectData.put("CAPTURE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Authorisation")) {
                    objectData.put("AUTHORISE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("AUTHORISE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_ACQUIRER_ID", acquirerId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);

                    objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                    objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                    objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                    objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));

                    Reconciliation internationalReport = bindReconciliation(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }

            return internationalReports;
        });
    }

    @Override
    public List<Reconciliation> downloadPayment(Connection connection, ReconciliationQuery parameter) throws SQLException {

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchant_id())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date())
                .add(parameter.getInterval())
                .add(parameter.getAcquirer())
                .add(parameter.getCard_type())
                .add(parameter.getCurrency())
                .add(parameter.getTransaction_type());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet resultSet = null;
        List<Reconciliation> internationalReports = new ArrayList<>();
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, RECONCILE_PAYMENT, inParams, outParams);
            int code = callableStatement.getInt(11);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "PAYMENT RECONCILE DOWNLOAD  ERROR: " + callableStatement.getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            resultSet = (ResultSet) callableStatement.getObject(10);

            boolean recordsAvailable = resultSet.next();

            JsonObject objectData = new JsonObject();
            String date = "";
            String cardType = "";
            String currency = "";
            String acquirerId = "";

            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = resultSet.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                    if (!resultSet.getString("D_DATE").equals(date)
                            || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())
                            || !resultSet.getString("S_ACQUIRERID").equals(acquirerId)) {
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_ACQUIRER_ID", acquirerId);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);

                        objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                        objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                        objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                        objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                        objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                        objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                        objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));
                        Reconciliation internationalReport = bindReconciliation(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }


                date = resultSet.getString("D_DATE");

                String cardTypeData = "";
                if (resultSet.getString("S_CARDTYPE") != null) {
                    cardTypeData = resultSet.getString("S_CARDTYPE").toLowerCase();
                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                }
                cardType = cardTypeData;
                currency = resultSet.getString("S_CURRENCY");
                acquirerId = resultSet.getString("S_ACQUIRERID");
//                merchantId =jsonObject.getString("S_MERCHANTID");

                int count = resultSet.getInt("N_COUNT");
                if (!objectData.isEmpty()) {
                    count += objectData.getInteger("TRANS_COUNT");
                }
                objectData.put("TRANS_COUNT", count);

                if (resultSet.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("PURCHASE_AMOUNT", resultSet.getDouble("N_AMOUNT"));
                    objectData.put("PURCHASE_COUNT", resultSet.getInt("N_COUNT"));
                } else if (resultSet.getString("S_TRANSACTIONTYPE").equals("Refund")) {
                    objectData.put("REFUND_COUNT", resultSet.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", resultSet.getDouble("N_AMOUNT"));
                } else if (resultSet.getString("S_TRANSACTIONTYPE").equals("Void Purchase")) {
                    objectData.put("VOID_PURCHASE_COUNT", resultSet.getInt("N_COUNT"));
                    objectData.put("VOID_PURCHASE_AMOUNT", resultSet.getDouble("N_AMOUNT"));
                } else if (resultSet.getString("S_TRANSACTIONTYPE").equals("Capture")) {
                    objectData.put("CAPTURE_COUNT", resultSet.getInt("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", resultSet.getDouble("N_AMOUNT"));
                } else if (resultSet.getString("S_TRANSACTIONTYPE").equals("Authorisation")) {
                    objectData.put("AUTHORISE_COUNT", resultSet.getInt("N_COUNT"));
                    objectData.put("AUTHORISE_AMOUNT", resultSet.getDouble("N_AMOUNT"));
                }
                if (!resultSet.next()) {
                    recordsAvailable = false;
                    objectData.put("S_ACQUIRER_ID", acquirerId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);

                    objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                    objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                    objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                    objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));

                    Reconciliation internationalReport = bindReconciliation(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (resultSet != null) {
                resultSet.close();
            }
        }


        return internationalReports;
    }

    @Override
    public List<Reconciliation> downloadDetailPayment(Connection connection, ReconciliationDetailQuery parameter) throws SQLException {

        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchant_id())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date())
                .add(parameter.getBase_date())
                .add(parameter.getInterval())
                .add(parameter.getAcquirer())
                .add(parameter.getCard_type())
                .add(parameter.getCurrency())
                .add(parameter.getTransaction_type());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet jsonObject = null;

        List<Reconciliation> internationalReports = new ArrayList<>();
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, RECONCILE_PAYMENT_DETAIL, inParams, outParams);
            int code = callableStatement.getInt(12);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "PAYMENT RECONCILE DOWNLOAD  ERROR: " + callableStatement.getString(13));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            jsonObject = (ResultSet) callableStatement.getObject(11);
            boolean recordsAvailable = jsonObject.next();

            JsonObject objectData = new JsonObject();
            String date = "";
            String cardType = "";
            String currency = "";
            String acquirerId = "";
            String merchantId = "";
            String midNo = "";

            while (recordsAvailable) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                    if (!merchantId.equals(jsonObject.getString("S_MERCHANTID"))
                            || !jsonObject.getString("D_DATE").equals(date)
                            || !cardTypeData.toLowerCase().equals(cardType.toLowerCase())
                            || !jsonObject.getString("S_ACQUIRERID").equals(acquirerId)) {
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_ACQUIRER_ID", acquirerId);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MID_NO", midNo);

                        objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                        objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                        objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                        objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                        objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                        objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                        objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));
                        Reconciliation internationalReport = bindReconciliation(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }


                date = jsonObject.getString("D_DATE");

                String cardTypeData = "";
                if (jsonObject.getString("S_CARDTYPE") != null) {
                    cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();
                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                acquirerId = jsonObject.getString("S_ACQUIRERID");
                merchantId = jsonObject.getString("S_MERCHANTID");
                midNo = jsonObject.getString("S_MID_NO");

                int count = jsonObject.getInt("N_COUNT");
                if (!objectData.isEmpty()) {
                    count += objectData.getInteger("TRANS_COUNT");
                }
                objectData.put("TRANS_COUNT", count);

                if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                    objectData.put("PURCHASE_COUNT", jsonObject.getInt("N_COUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Refund")) {
                    objectData.put("REFUND_COUNT", jsonObject.getInt("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Void Purchase")) {
                    objectData.put("VOID_PURCHASE_COUNT", jsonObject.getInt("N_COUNT"));
                    objectData.put("VOID_PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Capture")) {
                    objectData.put("CAPTURE_COUNT", jsonObject.getInt("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Authorisation")) {
                    objectData.put("AUTHORISE_COUNT", jsonObject.getInt("N_COUNT"));
                    objectData.put("AUTHORISE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                if (!jsonObject.next()) {
                    recordsAvailable = false;
                    objectData.put("S_ACQUIRER_ID", acquirerId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MID_NO", midNo);

                    objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                    objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                    objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                    objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));

                    Reconciliation internationalReport = bindReconciliation(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }
            if (jsonObject != null) {
                jsonObject.close();
            }
        }


        return internationalReports;
    }

    @Override
    public Observable<List<Reconciliation>> listDetailPayment(SQLConnection connection, ReconciliationDetailQuery parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchant_id())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date())
                .add(parameter.getBase_date())
                .add(parameter.getInterval())
                .add(parameter.getAcquirer())
                .add(parameter.getCard_type())
                .add(parameter.getCurrency())
                .add(parameter.getTransaction_type());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(RECONCILE_PAYMENT_DETAIL, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(11);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "PAYMENT RECONCILE DETAIL REPORT ERROR: " + result.getOutput().getString(12));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<Reconciliation> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(10).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            JsonObject objectData = new JsonObject();
            String date = "";
            String cardType = "";
            String currency = "";
            String acquirerId = "";
            String merchantId = "";
            String midNo = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {
                if (date.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                    if (!merchantId.equals(jsonObject.getString("S_MERCHANTID")) || !jsonObject.getString("D_DATE").equals(date) || !cardTypeData.toLowerCase().equals(cardType.toLowerCase()) || !jsonObject.getString("S_ACQUIRERID").equals(acquirerId)) {
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_ACQUIRER_ID", acquirerId);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("D_DATE", date);
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_MID_NO", midNo);

                        objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                        objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                        objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                        objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                        objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                        objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                        objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));
                        Reconciliation internationalReport = bindReconciliation(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }


                date = jsonObject.getString("D_DATE");

                String cardTypeData = "";
                if (jsonObject.getString("S_CARDTYPE") != null) {
                    cardTypeData = jsonObject.getString("S_CARDTYPE").toLowerCase();
                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    } else if (cardTypeData.equals("pp")) {
                        cardTypeData = "Paypal";
                    } else if (cardTypeData.equals("cup")) {
                        cardTypeData = "CUP";
                    }
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                acquirerId = jsonObject.getString("S_ACQUIRERID");
                merchantId = jsonObject.getString("S_MERCHANTID");
                midNo = jsonObject.getString("S_MID_NO");

                int count = jsonObject.getInteger("N_COUNT");
                if (!objectData.isEmpty()) {
                    count += objectData.getInteger("TRANS_COUNT");
                }
                objectData.put("TRANS_COUNT", count);

                if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Purchase")) {
                    objectData.put("PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                    objectData.put("PURCHASE_COUNT", jsonObject.getInteger("N_COUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Refund")) {
                    objectData.put("REFUND_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Void Purchase")) {
                    objectData.put("VOID_PURCHASE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("VOID_PURCHASE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Capture")) {
                    objectData.put("CAPTURE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("CAPTURE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTIONTYPE").equals("Authorisation")) {
                    objectData.put("AUTHORISE_COUNT", jsonObject.getInteger("N_COUNT"));
                    objectData.put("AUTHORISE_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_ACQUIRER_ID", acquirerId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("D_DATE", date);
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_MID_NO", midNo);

                    objectData.put("TRANS_COUNT", objectData.getInteger("TRANS_COUNT") == null ? 0 : objectData.getInteger("TRANS_COUNT"));

                    objectData.put("PURCHASE_COUNT", objectData.getInteger("PURCHASE_COUNT") == null ? 0 : objectData.getInteger("PURCHASE_COUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("AUTHORISE_COUNT", objectData.getInteger("AUTHORISE_COUNT") == null ? 0 : objectData.getInteger("AUTHORISE_COUNT"));
                    objectData.put("CAPTURE_COUNT", objectData.getInteger("CAPTURE_COUNT") == null ? 0 : objectData.getInteger("CAPTURE_COUNT"));
                    objectData.put("VOID_PURCHASE_COUNT", objectData.getInteger("VOID_PURCHASE_COUNT") == null ? 0 : objectData.getInteger("VOID_PURCHASE_COUNT"));

                    objectData.put("VOID_PURCHASE_AMOUNT", objectData.getDouble("VOID_PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("VOID_PURCHASE_AMOUNT"));
                    objectData.put("PURCHASE_AMOUNT", objectData.getDouble("PURCHASE_AMOUNT") == null ? 0 : objectData.getDouble("PURCHASE_AMOUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("CAPTURE_AMOUNT", objectData.getDouble("CAPTURE_AMOUNT") == null ? 0 : objectData.getDouble("CAPTURE_AMOUNT"));
                    objectData.put("AUTHORISE_AMOUNT", objectData.getDouble("AUTHORISE_AMOUNT") == null ? 0 : objectData.getDouble("AUTHORISE_AMOUNT"));

                    Reconciliation internationalReport = bindReconciliation(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }

            return internationalReports;
        });
    }

    @Override
    public Observable<List<InternationalReport>> listByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        return listReportByCurrency(connection, parameter);
    }

    @Override
    public ResultSet downloadDetail(Connection connection, InternationalReportParameter parameter) throws SQLException {
        return this.downloadDataDetail(connection, parameter);
    }

    /**
     * list report by merchant
     *
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<List<InternationalReport>> listReportByCurrency(SQLConnection connection, InternationalReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(RECONCILE_CURRENCY, inParams, outParams).map(result -> {
            List<InternationalReport> internationalReports = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(8).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            JsonObject objectData = new JsonObject();
            String cardType = "";
            String merchantId = "";
            String currency = "";
            String transactionType = "";
            int index = 0;
            for (JsonObject jsonObject : rs.getRows()) {
                if (merchantId.isEmpty()) {
                    objectData = new JsonObject();
                } else {
                    String cardTypeData = jsonObject.getString("S_CARD_TYPE").toLowerCase();

                    if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                        cardTypeData = "Amex";
                    } else if (cardTypeData.equals("mastercard")) {
                        cardTypeData = "MasterCard";
                    } else if (cardTypeData.equals("visa")) {
                        cardTypeData = "Visa";
                    } else if (cardTypeData.equals("jcb")) {
                        cardTypeData = "JCB";
                    }
                    String mid = jsonObject.getString("S_MERCHANT_ID");
                    if (!cardTypeData.toLowerCase().equals(cardType.toLowerCase()) || !mid.equals(merchantId)) {
                        objectData.put("S_MERCHANT_ID", merchantId);
                        objectData.put("S_CARD_TYPE", cardType);
                        objectData.put("S_CURRENCY", currency);
                        objectData.put("S_TRANSACTION_TYPE", transactionType);
                        objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                        objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                        objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                        objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                        objectData.put("N_VOID_TRAN_AMOUNT", objectData.getDouble("N_VOID_TRAN_AMOUNT") == null ? 0 : objectData.getDouble("N_VOID_TRAN_AMOUNT"));
                        objectData.put("N_VOID_TRAN_COUNT", objectData.getDouble("N_VOID_TRAN_COUNT") == null ? 0 : objectData.getDouble("N_VOID_TRAN_COUNT"));
                        InternationalReport internationalReport = bindReport(objectData);
                        internationalReports.add(internationalReport);
                        objectData = new JsonObject();
                    }
                }

                String cardTypeData = jsonObject.getString("S_CARD_TYPE").toLowerCase();

                if (cardTypeData.equals("american express") || cardTypeData.equals("amex")) {
                    cardTypeData = "Amex";
                } else if (cardTypeData.equals("mastercard")) {
                    cardTypeData = "MasterCard";
                } else if (cardTypeData.equals("visa")) {
                    cardTypeData = "Visa";
                } else if (cardTypeData.equals("jcb")) {
                    cardTypeData = "JCB";
                }
                cardType = cardTypeData;
                currency = jsonObject.getString("S_CURRENCY");
                merchantId = jsonObject.getString("S_MERCHANT_ID");
                transactionType = jsonObject.getString("S_TRANSACTION_TYPE");
                int count = jsonObject.getInteger("N_COUNT");
                if (!objectData.isEmpty()) {
                    count += objectData.getInteger("N_COUNT_TRAN");
                }
                objectData.put("N_COUNT_TRAN", count);
                if (jsonObject.getString("S_TRANSACTION_TYPE").equals("Purchase")) {
                    objectData.put("N_TOTAL_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTION_TYPE").equals("Void Purchase")) {
                    objectData.put("N_VOID_TRAN_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                } else if (jsonObject.getString("S_TRANSACTION_TYPE").equals("Refund")) {
                    objectData.put("REFUND_AMOUNT", jsonObject.getDouble("N_AMOUNT"));
                }
                index++;
                if (index >= rs.getRows().size()) {
                    objectData.put("S_MERCHANT_ID", merchantId);
                    objectData.put("S_CARD_TYPE", cardType);
                    objectData.put("S_CURRENCY", currency);
                    objectData.put("S_TRANSACTION_TYPE", transactionType);
                    objectData.put("N_COUNT_TRAN", objectData.getInteger("N_COUNT_TRAN") == null ? 0 : objectData.getInteger("N_COUNT_TRAN"));
                    objectData.put("N_TOTAL_AMOUNT", objectData.getDouble("N_TOTAL_AMOUNT") == null ? 0 : objectData.getDouble("N_TOTAL_AMOUNT"));
                    objectData.put("REFUND_COUNT", objectData.getInteger("REFUND_COUNT") == null ? 0 : objectData.getInteger("REFUND_COUNT"));
                    objectData.put("REFUND_AMOUNT", objectData.getDouble("REFUND_AMOUNT") == null ? 0 : objectData.getDouble("REFUND_AMOUNT"));
                    objectData.put("N_VOID_TRAN_AMOUNT", objectData.getDouble("N_VOID_TRAN_AMOUNT") == null ? 0 : objectData.getDouble("N_VOID_TRAN_AMOUNT"));
                    objectData.put("N_VOID_TRAN_COUNT", objectData.getDouble("N_VOID_TRAN_COUNT") == null ? 0 : objectData.getDouble("N_VOID_TRAN_COUNT"));
                    InternationalReport internationalReport = bindReport(objectData);
                    internationalReports.add(internationalReport);
                    objectData = new JsonObject();
                }
            }

            return internationalReports;
        });
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadDataDetail(Connection connection, InternationalReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getMerchantId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardType())
                .add(parameter.getCurrency());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, REPORT_RECONCILE_DETAIL, inParams, outParams);
            int code = callableStatement.getInt(8);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "REPORT DETAIL ERROR: " + callableStatement.getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(7);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    private Reconciliation bindReconciliation(JsonObject rs) {
        Reconciliation result = new Reconciliation();

        result.setAcquirer(rs.getString("S_ACQUIRER_ID"));
        result.setCard_type(rs.getString("S_CARD_TYPE"));
        result.setCurrency(rs.getString("S_CURRENCY"));
        result.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        result.setMid_no(rs.getString("S_MID_NO"));
        result.setCount_transaction(rs.getInteger("TRANS_COUNT"));
        result.setCount_purchase(rs.getInteger("PURCHASE_COUNT"));
        result.setCount_refund(rs.getInteger("REFUND_COUNT"));
        result.setCount_void(rs.getInteger("VOID_PURCHASE_COUNT"));
        result.setCount_authorise(rs.getInteger("AUTHORISE_COUNT"));
        result.setCount_capture(rs.getInteger("CAPTURE_COUNT"));
        result.setTotal_refund(rs.getDouble("REFUND_AMOUNT"));
        result.setTotal_void(rs.getDouble("VOID_PURCHASE_AMOUNT"));
        result.setTotal_purchase(rs.getDouble("PURCHASE_AMOUNT"));
        result.setTotal_capture(rs.getDouble("CAPTURE_AMOUNT"));
        result.setTotal_authorise(rs.getDouble("AUTHORISE_AMOUNT"));
        result.setSettlement_date(java.sql.Timestamp.valueOf(rs.getString("D_DATE")));

        return result;
    }

    /**
     * bind result set to report
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private InternationalReport bindReport(JsonObject rs) {
        InternationalReport report = new InternationalReport();
        report.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        report.setCard_type(rs.getString("S_CARD_TYPE"));
        report.setCurrency(rs.getString("S_CURRENCY"));
        report.setRefund_count(rs.getInteger("REFUND_COUNT"));
        report.setRefund_total(rs.getDouble("REFUND_AMOUNT"));
        report.setTransaction_count(rs.getInteger("N_COUNT_TRAN"));
        report.setTransaction_total(rs.getDouble("N_TOTAL_AMOUNT"));
        report.setVoid_transaction_count(rs.getInteger("N_VOID_TRAN_COUNT"));
        report.setVoid_transaction_total(rs.getDouble("N_VOID_TRAN_AMOUNT"));
        report.setTransaction_type(rs.getString("S_TRANSACTION_TYPE"));

        // DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        // report.setReport_date(java.sql.Timestamp.valueOf(rs.getString("D_DATE")));
//        try {
//            report.setReport_date( formatter.parse(rs.getString("D_DATE")));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        return report;
    }


    private final static String REPORT_RECONCILE_DETAIL = "{call PKG_ONEFRAUD.SEARCH_REPORTS_DETAIL_2(?,?,?,?,?,?,?,?,?)}";

    private final static String RECONCILE_CURRENCY = "{call PKG_ONEFRAUD.SEARCH_REPORTS_CURRENCY_v2(?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String RECONCILE_PAYMENT = "{call PKG_ONEFRAUD.PAYMENT_RECONCILE_REPORTS(?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String RECONCILE_PAYMENT_DETAIL = "{call PKG_ONEFRAUD.PAYMENT_RECONCILE_DETAIL(?,?,?,?,?,?,?,?,?,?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(ReconcileServiceImpl.class.getName());
}
