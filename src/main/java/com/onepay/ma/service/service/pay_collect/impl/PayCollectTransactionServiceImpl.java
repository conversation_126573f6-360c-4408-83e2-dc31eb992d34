package com.onepay.ma.service.service.pay_collect.impl;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.pay_collect.Bank;
import com.onepay.ma.service.models.pay_collect.Transaction;
import com.onepay.ma.service.models.pay_collect.TransactionSearchReq;
import com.onepay.ma.service.service.pay_collect.TransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

@Service
public class PayCollectTransactionServiceImpl implements TransactionService {

    private static final String GET_LIST_TRANSACTIONS = "{ call PKG_MA_PAYCOLLECT.search_trans_collect(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String GET_TRANSACTION_BY_ID = "{ call PKG_MA_PAYCOLLECT.get_trans_collect_id(?,?,?,?) }";
    private static final String GET_ALL_BANK = "{call PKG_MA_PAYCOLLECT.get_all_bank(?,?,?)}";


    @Override
    public Observable<BaseList<Transaction>> search(SQLConnection sqlConnection, TransactionSearchReq searchRequest) {
        BaseList<Transaction> baseList = new BaseList<>();

        return this.total(sqlConnection, searchRequest).flatMap(total -> {
            baseList.setTotal_items(total);
            return this.searchData(sqlConnection, searchRequest).map(payCollectTransactionsOnline -> {
                baseList.setList(payCollectTransactionsOnline);
                return baseList;
            });
        });
    }

    @Override
    public  Observable<Integer> total(SQLConnection sqlConnection, TransactionSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getTransactionId())
                .add(query.getVirtualAccId())
                .add(query.getAccountName())
                .add(query.getReference())
                .add(query.getBankTxnRef())
                .add(query.getState())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId());

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConnection.callWithParamsObservable2(GET_LIST_TRANSACTIONS, inParams, outParams).map(result -> {
            Integer total = 0;
            Integer resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });

    }

    private  Observable<List<Transaction>> searchData(SQLConnection sqlConnection, TransactionSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getTransactionId())
                .add(query.getVirtualAccId())
                .add(query.getAccountName())
                .add(query.getReference())
                .add(query.getBankTxnRef())
                .add(query.getState())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId());

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_LIST_TRANSACTIONS, inParams, outParams).map(result -> {
            List<Transaction> transactionList = new ArrayList<>();
            Integer resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH TRANSACTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                transactionList.add(bind(jsonObject,"LIST"));
            }
            return transactionList;
        });
    }

    @Override
    public ResultSet download(Connection connection, TransactionSearchReq query) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(query.getFromDate())
                .add(query.getToDate())
                .add(query.getTransactionId())
                .add(query.getVirtualAccId())
                .add(query.getAccountName())
                .add(query.getReference())
                .add(query.getBankTxnRef())
                .add(query.getState())
                .add(query.getPageSize())
                .add(query.getPage())
                .add(query.getMerchantId());

        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, GET_LIST_TRANSACTIONS, inParams, outParams);

            int code = callableStatement.getInt(2);
            if(code != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD TRANSACTION ERROR]: " + callableStatement.getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(1);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }


    @Override
    public Observable<Transaction> getTransactionById(SQLConnection sqlConnection, String id) {

        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(id);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull();
        return sqlConnection.callWithParamsObservable2(GET_TRANSACTION_BY_ID, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET PARTNER BY ID ] Failed " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Transaction transaction = null;
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = this.bind(jsonObject,"DETAIL");

            return transaction;
        });
    }

    @Override
    public Observable<BaseList<Bank>> getAllBank(SQLConnection sqlConnection) {
        JsonArray inParams = new JsonArray();
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_ALL_BANK, inParams, outParams).map(result -> {
            BaseList<Bank> bankBaseList = new BaseList<>();
            List<Bank> list = new ArrayList<>();
            if (result.getOutput().getInteger(1) != 200) {
                LOGGER.log(Level.SEVERE, "[GET ALL PAYCOLLECT BANK ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            Bank bank;
            for (JsonObject jsonObject : rs.getRows()) {
                bank = new Bank();
                bank.setId(jsonObject.getString("N_ID"));
                bank.setBankName(jsonObject.getString("S_BANK_NAME"));
                bank.setSwiftCode(jsonObject.getString("S_SWIFT_CODE"));
                bank.setState(jsonObject.getString("S_STATE"));
                bank.setInternerCost(jsonObject.getLong("N_INTERNER_COST"));
                bank.setExternerCost(jsonObject.getLong("N_EXTERNER_COST"));
                bank.setDesc(jsonObject.getString("S_DESC"));
                bank.setCreatedDate(jsonObject.getString("D_CREATE"));
                bank.setUpdatedDate(jsonObject.getString("D_UPDATE"));
                list.add(bank);
            }
            bankBaseList.setList(list);
            bankBaseList.setTotal_items(list.size());
            return bankBaseList;
        });
    }


    private Transaction bind(JsonObject js , String typeBind) {
        Transaction transaction = new Transaction();
        try {
            transaction.setId(js.getString("S_ID"));
            Timestamp createdDate = js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE"));
            Timestamp bankDate = js.getString("D_BANK_DATE") == null ? null : Timestamp.valueOf(js.getString("D_BANK_DATE"));
            transaction.setCreatedDate(createdDate);
            transaction.setBankTxnRef(js.getString("S_BANK_TXN_REF"));
            transaction.setReceiptAccName(js.getString("S_RECEIPT_ACC_NAME"));
            transaction.setVirtualAccId(js.getString("S_BANK_ACCOUNT"));
            transaction.setReceivedBank(js.getString("RECEIPT_BANK"));
            transaction.setAmount(js.getDouble("N_AMOUNT"));
            transaction.setCurrency(js.getString("S_CURRENCY"));
            transaction.setState(js.getString("S_STATE"));
            transaction.setBankMessage(js.getString("S_REMARK"));
            transaction.setReference(js.getString("S_REFERENCE_ID"));
            transaction.setPartnerId(js.getString("S_PARTNER_ID"));
            transaction.setBankDate(bankDate);
            if (typeBind.equals("DETAIL")) {
                transaction.setBranchName(js.getString("S_BRANCH_NAME"));
                transaction.setAddress(js.getString("S_ADDRESS"));
                transaction.setEmail(js.getString("S_EMAIL"));
                transaction.setMobile(js.getString("S_MOBILE"));
                transaction.setClientId(js.getString("S_CLIENT_ID"));
                transaction.setSenderAccount(js.getString("S_SENDER_ACC_ID"));
                transaction.setSenderAccName(js.getString("S_SENDER_ACC_NAME"));
                transaction.setSenderSwiftCode(js.getString("S_SENDER_SWIFT_CODE"));
                transaction.setReceiptAccName(js.getString("S_RECEIPT_ACC_NAME"));
                transaction.setReceiptSwiftCode(js.getString("S_RECEIPT_SWIFT_CODE"));
                transaction.setBalanceBefore(js.getDouble("N_ACC_BALANCE_BEFORE"));
                transaction.setBalanceAfter(js.getDouble("N_ACC_BALANCE_AFTER"));
                transaction.setBankResCode(js.getString("S_BANK_RES_CODE"));
                transaction.setSenderAddress(js.getString("S_SENDER_ADDRESS"));
                transaction.setSenderCity(js.getString("S_SENDER_CITY"));
                transaction.setSenderCountry(js.getString("S_SENDER_COUNTRY"));
                transaction.setUserId(js.getString("S_USER_ID"));
//            transaction.setReceivedAccount();
                transaction.setReceivedAddress(js.getString("S_RECEIPT_ACC_ADDR"));
                transaction.setReceivedCity(js.getString("S_RECEIPT_ACC_CITY"));
                transaction.setReceivedCountry(js.getString("S_RECEIPT_ACC_COUNTRY"));
                transaction.setFeeAmount(js.getDouble("N_BANK_FEES_AMOUNT"));
                transaction.setVatAmount(js.getDouble("N_BANK_VAT_AMOUNT"));
                transaction.setAccountId(js.getString("S_USER_ACCOUNT_ID"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return transaction;
    }



    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PayCollectTransactionServiceImpl.class.getName());
}
