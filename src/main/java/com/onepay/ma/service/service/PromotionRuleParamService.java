package com.onepay.ma.service.service;

import com.onepay.ma.service.models.PromotionRuleParam;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/10/16.
 */
public interface PromotionRuleParamService {
    Observable<List<PromotionRuleParam>> list(SQLConnection sqlConnPr, int ruleId);

    Observable<Integer> insert(SQLConnection sqlConnPr, int ruleId, PromotionRuleParam promotionRuleParam);

    Observable<Integer> update(SQLConnection sqlConnPr, int ruleParamId, PromotionRuleParam promotionRuleParam);

    Observable<Integer> delete(SQLConnection sqlConnPr, int ruleId, int ruleParamId);

    Observable<String> getRegexRuleTYpe(SQLConnection sqlConnPr, int ruleId, String paramName);
}
