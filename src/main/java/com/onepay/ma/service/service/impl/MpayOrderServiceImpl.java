package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.mpay.MpayOrder;
import com.onepay.ma.service.models.mpay.MpayOrderPostModel;
import com.onepay.ma.service.models.mpay.MpayOrderQuery;
import com.onepay.ma.service.models.mpay.MpayTransactionHistory;
import com.onepay.ma.service.service.MerchantService;
import com.onepay.ma.service.service.mpay.MpayOrderService;
import com.onepay.ma.service.util.QrUtils;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 07-Aug-17.
 */
@Service
public class MpayOrderServiceImpl implements MpayOrderService {


    @Override
    public Observable<List<MpayTransactionHistory>> listHistory(SQLConnection connReadOnly, SQLConnection connOnline, String transactionId) {
        return getListTransactionHistoryOnline(connOnline, transactionId).flatMap(transactionOnline -> {
            //transactions.setTotal_items(domesticTransactions.size());
            return  getListTransactionHistoryReadOnly(connReadOnly, transactionId).flatMap(transactionReadOnly -> {
                List<MpayTransactionHistory> transactionsFinal = new ArrayList<>();
                transactionsFinal.addAll(transactionOnline);
                transactionsFinal.addAll(transactionReadOnly);
                //transactions.setTransactions(transactionsFinal);
                return Observable.just(transactionsFinal);
            });

        });
    }

    @Override
    public Observable<Transactions<MpayOrder>> list(SQLConnection connReadOnly, SQLConnection connOnline, MpayOrderQuery query) {

        Transactions<MpayOrder> transactions = new Transactions<>();

        return this.getListTotalTransactionOnline(connOnline, query).flatMap(total1 -> {
            return this.getListTotalTransactionReadonly(connReadOnly, query).flatMap(total2 -> {
                transactions.setTotal_items(total1 + total2);
                int basePageSize = query.getPageSize();
                // Get online transactions
                return this.getListTransactionOnline(connOnline, query).flatMap( onlineTrans -> {
                    int pageSize = query.getPageSize() - onlineTrans.size();
                    query.setPageSize(pageSize);

                    // Get readonly transaction
                    return this.getListTransactionReadonly(connReadOnly, query, onlineTrans.size()).flatMap( readOnlyTrans -> {
                        List<MpayOrder> transactionsFinal = new ArrayList<>();
                        transactionsFinal.addAll(onlineTrans);
                        transactionsFinal.addAll(readOnlyTrans);
                        int index = 1;
                        for (MpayOrder transaction : transactionsFinal) {
                            transaction.setRow_num(index + (query.getPage() * query.getPageSize()));
                            index++;
                        }
                        transactions.setTransactions(transactionsFinal);
                        return Observable.just(transactions);
                    });
                });
            });
        });
    }

    @Override
    public Observable<MpayOrder> get(SQLConnection connOnline, String transactionId) {
        return this.getById(connOnline, transactionId).flatMap(mpayOrder -> {
            if(mpayOrder.getStatus().equals("not_paid") && mpayOrder.getAmount().getTotal() != null) {
                return merchantService.getMpayMerchant(connOnline, mpayOrder.getMerchant_id()).map(mpayMerchant -> {
                    MpayOrderPostModel orderPostModel = new MpayOrderPostModel();
                    orderPostModel.setCountryCode(mpayMerchant.getCountry());
                    orderPostModel.setMerchantCity(mpayMerchant.getCity());
                    orderPostModel.setMerchantCategoryCode(mpayMerchant.getCategoryCode());
                    orderPostModel.setMerchantName(mpayMerchant.getName());
                    orderPostModel.setReferenceId(mpayOrder.getMerchant_order_ref());
                    orderPostModel.setAmount(mpayOrder.getAmount().getTotal());
                    orderPostModel.setCurrencyCode(mpayOrder.getAmount().getCurrency());
                    orderPostModel.setBillNumber(mpayOrder.getOrder_info());
                    orderPostModel.setMerchantId(mpayOrder.getMerchant_id());

                    mpayOrder.setQrData(QrUtils.createMpayQrData(orderPostModel));
                    return mpayOrder;
                });
            }else {
                return  Observable.just(mpayOrder);
            }
        });
    }

    @Override
    public Observable<Map<String, Object>> getTransactionByInvoice(SQLConnection connOnline, String invoiceId) {
        JsonArray inParams = new JsonArray()
                .add(invoiceId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(GET_TRANSACTION_BY_INVOICE, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET TRANSACTION BY INVOICE ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map<String,Object> transaction = new HashMap<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction.put("service", jsonObject.getString("S_SERVICE"));
            transaction.put("id", jsonObject.getString("S_ID"));
            transaction.put("orderInfo", jsonObject.getString("S_ORDER_INFO"));
            transaction.put("cardNumber", jsonObject.getString("S_CARD_NUMBER"));
            transaction.put("cardType", jsonObject.getString("S_CARD_TYPE"));
            transaction.put("transactionType", jsonObject.getString("S_TRANSACTION_TYPE"));
            transaction.put("transactionStatus", jsonObject.getString("S_ADVANCE_STATUS"));
            transaction.put("transactionDate", jsonObject.getString("D_DATE"));
            return transaction;
        });
    }

    public Observable<MpayOrder> getById(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY ORDER  By ID ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayOrder transaction = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bind(jsonObject);

            return transaction;
        });
    }

//    @Override
//    public Observable<List<MpayOrder>> listHistory(SQLConnection connReadOnly, SQLConnection connOnline, String transactionId) {
//        return null;
//    }


    private Observable<List<MpayOrder>> getListTransactionOnline(SQLConnection sqlConnection, MpayOrderQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getMerchant_id())
                .add(query.getMerchant_order_ref())
                .add(query.getTerminal_id())
                .add(query.getOrder_info())
                .add(query.getCard_number())
                .add(query.getOrder_id())
                .add(query.getCurrency())
                .add(query.getStatus())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(LIST_TRANSACTION_ONLINE, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(15);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY ORDER ONLINE ERROR]: " + result.getOutput().getString(16));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayOrder> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(13).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    MpayOrder transaction = bind(jsonObject);
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }

    private Observable<Integer> getListTotalTransactionOnline(SQLConnection connOnline, MpayOrderQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getMerchant_id())
                .add(query.getMerchant_order_ref())
                .add(query.getTerminal_id())
                .add(query.getOrder_info())
                .add(query.getCard_number())
                .add(query.getOrder_id())
                .add(query.getCurrency())
                .add(query.getStatus());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(TRANSACTION_GET_TOTAL, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(12);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY ORDER TOTAL ONLINE ERROR]: " + result.getOutput().getString(13));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = result.getOutput().getInteger(11);

            return total ;
        });
    }



    /**
     *  get listByMerchant transaction history data read only database
     * @param connOnline
     * @param transactionId
     * @return
     */
    private Observable<List<MpayTransactionHistory>> getListTransactionHistoryOnline(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_HISTORY_ONLINE, inParams, outParams).map(result -> {

            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY ORDER HISTORY ONLINE ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            //JsonObject jsonObject = rs.getRows().get(0);
            for(JsonObject jsonObject : rs.getRows()){
                MpayTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }


    /**
     *  get listByMerchant transaction history data read only database
     * @param connReadOnly
     * @param transactionId
     * @return
     */
    private Observable<List<MpayTransactionHistory>> getListTransactionHistoryReadOnly(SQLConnection connReadOnly, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY_READ_ONLY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(2);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY ORDER HISTORY READONLY ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            //JsonObject jsonObject = rs.getRows().get(0);
            for(JsonObject jsonObject : rs.getRows()){
                MpayTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;

        });
    }

    private Observable<Integer> getListTotalTransactionReadonly(SQLConnection connOnline, MpayOrderQuery query) {
        JsonArray inParams = new JsonArray()
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getMerchant_id())
                .add(query.getMerchant_order_ref())
                .add(query.getTerminal_id())
                .add(query.getOrder_info())
                .add(query.getCard_number())
                .add(query.getOrder_id())
                .add(query.getCurrency())
                .add(query.getStatus());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(TRANSACTION_GET_TOTAL_READONLY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(12);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY ORDER TOTAL READONLY ERROR]: " + result.getOutput().getString(13));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = result.getOutput().getInteger(11);

            return total ;
        });
    }



    private Observable<List<MpayOrder>> getListTransactionReadonly(SQLConnection sqlConnection, MpayOrderQuery query, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getKeywords())
                .add(query.getMerchant_id())
                .add(query.getMerchant_order_ref())
                .add(query.getTerminal_id())
                .add(query.getOrder_info())
                .add(query.getCard_number())
                .add(query.getOrder_id())
                .add(query.getCurrency())
                .add(query.getStatus())
                .add(query.getPage())
                .add(query.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(LIST_TRANSACTION_READONLY, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(16);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY ORDER READONLY ERROR]: " + result.getOutput().getString(17));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayOrder> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(14).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    MpayOrder transaction = bind(jsonObject);
                    transactionList.add(transaction);
                }
            } else {
            }
            return transactionList;
        });
    }


    private MpayTransactionHistory bindTransactionHistory(JsonObject js) {
        MpayTransactionHistory result = new MpayTransactionHistory();

        Amount amount = new Amount();
        amount.setTotal(js.getDouble("N_AMOUNT"));
        amount.setCurrency(js.getString("S_CURRENCY"));

        result.setTransaction_id(js.getString("S_ID"));
        result.setOriginal_id(js.getString("S_ORIGINAL_ID"));
        result.setAmount(amount);
        result.setTransaction_type(js.getString("S_TRANSACTION_TYPE"));
        result.setOperator_id(js.getString("S_OPERATOR_ID"));
        result.setStatus(js.getString("S_STATE"));
        result.setMerchant_transaction_ref(js.getString("S_MERCH_TXN_REF"));
        result.setTransaction_time(js.getString("D_CREATE") == null ? null : Timestamp.valueOf(js.getString("D_CREATE") ));

        return result;
    }


    private MpayOrder bind(JsonObject rs){
        String id = rs.getString("S_ID");
        Double total = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCIES");
        String status = rs.getString("S_STATE");
        String terminalId = rs.getString("S_TERMINAL_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT");
        String orderInfo = rs.getString("S_INFO");
        String merchantOrderRef = rs.getString("S_MERCH_ORDER_REF");
        Timestamp createTime = Timestamp.valueOf(rs.getString("D_CREATE"));
        String txtUpdateTime = rs.getString("D_UPDATE");
        Timestamp updateTime = null;
        if(txtUpdateTime != null) {
            updateTime = Timestamp.valueOf(txtUpdateTime);
        }
        String txtExpireTime = rs.getString("D_EXPIRE");
        Timestamp expireTime =  null;

        if(txtExpireTime != null) {
            expireTime = Timestamp.valueOf(rs.getString("D_EXPIRE"));
        }
        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);

        MpayOrder order = new MpayOrder();
        order.setOrder_id(id);
        order.setMerchant_id(merchantId);
        order.setMerchant_name(merchantName);
        order.setOnecom_merchant_id(onecomMerchantId);
        order.setCreate_time(createTime);
        order.setUpdate_time(updateTime);
        order.setExpire_time(expireTime);
        order.setStatus(status);
        order.setOrder_info(orderInfo);
        order.setTerminal_id(terminalId);
        order.setAmount(amount);
        order.setMerchant_order_ref(merchantOrderRef);
        // Return.
        return order;
    }

    @Autowired
    private MerchantService merchantService;


    private static final String LIST_TRANSACTION_ONLINE = "{call PKG_MPAY.ORDER_SEARCH_111(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_READONLY = "{call PKG_MPAY.ORDER_SEARCH_113(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";


    private static final String TRANSACTION_GET_BY_ID = "{call PKG_MPAY.ORDER_GET_BY_ID(?,?,?,?)}";

    private static final String TRANSACTION_GET_TOTAL = "{call PKG_MPAY.GET_ORDER_TOTAL_111(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_GET_TOTAL_READONLY = "{call PKG_MPAY.GET_ORDER_TOTAL_113(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String DOWNLOAD_TRANSACTION_DATA = "{call PKG_MPAY.payment_download_data_111(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DOWNLOAD_TRANSACTION_DATA_READONLY = "{call PKG_MPAY.payment_download_data_113(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY_READ_ONLY = "{call PKG_MPAY.SEARCH_ORDER_HIS_113(?,?,?,?)}";
    private static final String LIST_TRANSACTION_HISTORY_ONLINE = "{call PKG_MPAY.SEARCH_ORDER_HIS_111(?,?,?,?)}";

    private static final String GET_TRANSACTION_BY_INVOICE = "{call pkg_ma_app_noti.get_transaction_by_invoiceid(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(MpayOrderServiceImpl.class.getName());
}
