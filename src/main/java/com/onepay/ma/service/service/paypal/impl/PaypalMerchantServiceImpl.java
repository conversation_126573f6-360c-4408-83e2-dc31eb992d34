package com.onepay.ma.service.service.paypal.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.service.paypal.PaypalMerchantService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.Map;
import java.util.logging.Level;

@Service
public class PaypalMerchantServiceImpl implements PaypalMerchantService {

    @Override
    public Observable<Void> insert(SQLConnection sqlConnectionO, Integer partnerId, String ppMerchantId, String paymentReceivable, String emailConfirmed) {
        JsonArray inParams = new JsonArray()
                .add(partnerId)
                .add(ppMerchantId)
                .add(paymentReceivable.toUpperCase())
                .add(emailConfirmed.toUpperCase())
                ;
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnectionO.callWithParamsObservable2(INSERT_PP_MERCHANT, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(4) != 201 ) {
                LOGGER.log(Level.SEVERE, "[INSERT PP MERCHANT ERROR] => " + result.getOutput().getString(5));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;

        });
    }

    @Override
    public Observable<String> getppMerchantIdByTransNo(SQLConnection conn, String transNo){
        JsonArray inParams = new JsonArray()
                .add(transNo);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(GET_PP_MER_ID_TRANS_NO, inParams, outParams).map(result -> {
            String ppMerchantId = "";
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET MERCHANT PP BY MERCHANT ID ] Failed " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            ppMerchantId = jsonObject.getString("S_PAYPAL_MERCHANT_ID");

            return ppMerchantId;
        });
    }

    private static final String INSERT_PP_MERCHANT = "{call PKG_ONECREDIT.insert_paypal_merchant(?,?,?,?,?,?)}";
    private static final String GET_PP_MER_ID_TRANS_NO = "{call PKG_ONECREDIT.get_pp_mer_id_by_trans_no(?,?,?,?)}";

    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PaypalMerchantServiceImpl.class.getName());
}
