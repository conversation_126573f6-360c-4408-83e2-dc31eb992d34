package com.onepay.ma.service.service;


import com.onepay.ma.service.models.ManualRefundParameter;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.RefundApproval;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonObject;
import rx.Observable;

import java.sql.ResultSet;
import java.util.List;

/**
 * Created by anhkh on 8/8/2016.
 */
public interface RefundApprovalService {

    Observable<RefundApproval> insert(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, String type, String transactionReference, Integer transactionType);

    default Observable<RefundApproval> insertRequest(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, String transactionReference, Integer transactionType) {
        return this.insert(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST.content, transactionReference, transactionType);
    }

    default Observable<RefundApproval> insertRequest3(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, String transactionReference, Integer transactionType, String note) {
        String data = new JsonObject().put("note", note).encode();
        return this.insert3(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST.content, null, transactionReference, 401, transactionType, data);
    }

    default Observable<RefundApproval> insertRequestOnepay(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, String merchantTransRef, Integer status, Integer transactionType) {
        if (parentId != null) {

            return this.update(backupConn, parentId, RefundApproval.Status.APPROVED.code, merchantTransRef).flatMap(o -> {
                return this.insert2(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType);
            });
        } else {
            return this.insert2(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType);
        }
    }

    default Observable<RefundApproval> insertRequestOnepay3(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, String merchantTransRef, Integer status, Integer transactionType, String note) {
        String data = new JsonObject().put("note", note == null ? "" : note).encode();
        if (parentId != null) {

            return this.update(backupConn, parentId, RefundApproval.Status.APPROVED.code, merchantTransRef).flatMap(o -> {
                return this.insert3Mpay(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType, data);
            });
        } else {
            return this.insert3Mpay(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType, data);
        }
    }

    default Observable<RefundApproval> insertRequestOnepay3Mpay(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, String merchantTransRef, Integer status, Integer transactionType, String note) {
        String data = new JsonObject().put("note", note == null ? "" : note).encode();
        if (parentId != null) {

        return this.update(backupConn, parentId, RefundApproval.Status.APPROVED.code, merchantTransRef).flatMap(o -> {
            return this.insert3(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType, data);
    });
} else {
    return this.insert3(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REQUEST_ONEPAY.content, parentId, merchantTransRef, status, transactionType, data);
}
}

    Observable<RefundApproval> insert2(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, String type, Integer parentId, String merchantTransRef,
            Integer status, Integer transactionType);


    default Observable<RefundApproval> insertApprove(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, String merchantTransRef, Integer status, Integer transactionType) {
        if (status != 400 && status != 300) { // on case refund not success & pending -> keep status -> able to retry
            return this.insert2(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.APPROVED.content, parentId, merchantTransRef, status, transactionType);
        } else { // on case refund success -> change status -> unable to retry
            return this.update(backupConn, parentId, RefundApproval.Status.APPROVED.code, merchantTransRef).flatMap(o -> {
                return this.insert2(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.APPROVED.content, parentId, merchantTransRef, RefundApproval.Status.SUCCESSFUL.code, transactionType);
            });
        }
    }

    default Observable<RefundApproval> insertApprove3(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, String merchantTransRef, Integer status, Integer transactionType, String note) {
        String data = new JsonObject().put("note", note == null ? "" : note).encode();
        if (status != 400 && status != 300) { // on case refund not success & pending -> keep status -> able to retry
            return this.insert3(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.APPROVED.content, parentId, merchantTransRef, status, transactionType, data);
        } else { // on case refund success -> change status -> unable to retry
            return this.update(backupConn, parentId, RefundApproval.Status.APPROVED.code, merchantTransRef).flatMap(o -> {
                return this.insert3(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.APPROVED.content, parentId, merchantTransRef, RefundApproval.Status.SUCCESSFUL.code, transactionType, data);
            });
        }
    }

    Observable<RefundApproval> insert3(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, String type, Integer parentId, String merchantTransRef,
            Integer status, Integer transactionType, String data);



    default Observable<RefundApproval> insertReject(SQLConnection backupConn, String userId, String merchantId, String transactionId,
            Double amount, String currency, Integer parentId, Integer transactionType, String transRef) {
        return this.update(backupConn, parentId, RefundApproval.Status.REJECTED.code, transRef).flatMap(o -> { // Change status of parent -> completed
            return this.insert2(backupConn, userId, merchantId, transactionId, amount, currency, RefundApproval.Status.REJECTED.content, parentId, transRef, RefundApproval.Status.SUCCESSFUL.code, transactionType);
        });
    }

    Observable<RefundApproval> insert3Mpay(SQLConnection backupConn, String userId, String merchantId, String transactionId,
        Double amount, String currency, String type, Integer parentId, String merchantTransRef,
        Integer status, Integer transactionType, String data);


    Observable<RefundApproval> get(SQLConnection backupConn, Integer id);

    Observable update(SQLConnection backupConn, Integer id, Integer newStatus, String merchantTransRef);


    // Observable<RefundApprovals> search(SQLConnection backupConn, RefundApprovalQuery query);

    Observable<String> insertTblRefund(SQLConnection clientOnline, ManualRefundParameter parameter);

    Observable<String> updateRefundStatus(SQLConnection connOnline, String refundId);

    Observable updateErrorRefundStatus(SQLConnection backupConn, Integer approvalId, Integer errorStatus, String errorMessage);

    Observable<String> updateRefundQrStatus(SQLConnection connOnline, String refundId);

    Observable<String> updateDomesticRefundQrStatus(SQLConnection connOnline, Long refundTransactionId);

    Observable<RefundApproval> getById(SQLConnection backupConn, Integer id);

    Observable<RefundApproval> getMpay(SQLConnection backupConn, Integer id);
    Observable<RefundApproval> getByIdMpay(SQLConnection backupConn, Integer id);
    Observable<List<JsonObject>> getGetByTransRef(SQLConnection backupConn, String transRef);
}
