package com.onepay.ma.service.service.impl;


import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.paypal.Partner;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.util.Encode;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by huynguyen on 3/7/16.
 */
@Service
public class UserServiceV2Impl implements UserServiceV2 {


    public static String ENCODE_KEY_1 = PropsUtil.get("app.key.password.1", ""); // mOnePAY
    public static String ENCODE_KEY_2 = PropsUtil.get("app.key.password.2", ""); // mOnePAY
    public static String ENCODE_KEY_3 = PropsUtil.get("app.key.password.3", ""); // mOnePAY

    @Override
    public Observable<Integer> changePass(SQLConnection conn, String userId, String pass, String newPass) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(pass)
                .add(newPass);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(USER_CHANGE_PASS, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] Change pass Failed" + StringPool.SPACE + result.getOutput().getString(4));
                if (resultCode == 404) {
                    throw IErrors.PASS_NOT_FOUND;
                }
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnValue = result.getOutput().getInteger(3);
            }

            return returnValue;
        });
    }

    @Override
    public String encodePassword(String password, int level) {

        try {
            if (level == 1) {
                return Encode.hmacSHA256ToHex(ENCODE_KEY_1, password);
            }
            if (level == 12) {
                password = Encode.hmacSHA256ToHex(ENCODE_KEY_2, password);
            }
            if (level == 2 || level == 12) {
                return Encode.hmacSHA256ToHex(ENCODE_KEY_3, password);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Encode password: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return null;
    }

    @Override
    public Observable<UserData> insert(SQLConnection connection, UserData user) {
        return insertUser(connection, user).flatMap(s_id -> {
            user.setS_id(s_id);
            return insertUserMer(connection, user).flatMap(interger -> {
                user.setN_id(interger);
                return updateRoleAndMerchant(connection, user);
            });
        });
    }

    @Override
    public Observable<UserData> getUserExisted(SQLConnection connection, String email, String phone) {
        JsonArray inParams = new JsonArray()
                .add(email)
                .add(phone);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(GET_USER_EXISTED_V2, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER CHECK EXIT ] email or pass existed" + StringPool.SPACE + result.getOutput().getString(4));
                if(resultCode==600){
                    throw IErrors.INVALID_USER_EMAIL_EXISTED;
                }
                if(resultCode==700){
                    throw IErrors.INVALID_USER_PHONE_EXISTED;
                }
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                return null;
//                Map map = result.getOutput().getJsonObject(2).getMap();
//                io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
//                if (rs.getRows().size() <= 0) return null;
//                JsonObject jsonObject = rs.getRows().get(0);
//                UserData user = resultSetToUser(jsonObject);
//                return user;
            }
        });
    }

    @Override
    public Observable<UserData> update(SQLConnection connection, UserData userInfo, String sId) {
        return getUserData(connection, sId).flatMap(user -> {
            if (user == null) {
                LOGGER.log(Level.SEVERE, "[ USER ] Update user failed because s_id not existed");
                throw IErrors.RESOURCE_NOT_FOUND;
            }
            userInfo.setN_id(user.getN_id());
            userInfo.setS_id(user.getS_id());
            if (userInfo.getStatus() == null) {
                // update oneAm
                return updateUser(connection, userInfo).flatMap(code -> {
                    // update merchantportal
                    return updateUserMer(connection, userInfo).flatMap(interger -> {
                        return updateRoleAndMerchant(connection, userInfo);
                    });
                });
            } else {// update oneAm
                return updateUser(connection, userInfo).flatMap(code -> {
                    user.setStatus(userInfo.getStatus());
                    return Observable.just(user);
                });
            }
        });
    }

    @Override
    public Observable<Partner> getPartnerIdByUserId(SQLConnection connection, String sId) {
        JsonArray inParams = new JsonArray()
                .add(sId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
            return connection.callWithParamsObservable2(GET_PARTNER_BY_ID, inParams, outParams).map(result -> {
                int resultCode = result.getOutput().getInteger(2);
                if (resultCode != 200) {
                    throw IErrors.INTERNAL_SERVER_ERROR;
                } else {
                    Map map = result.getOutput().getJsonObject(1).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs.getRows().size() <= 0) return null;
                    JsonObject jsonObject = rs.getRows().get(0);
                    Partner partner = new Partner();
                    partner.setN_partner_id(jsonObject.getInteger("N_PARTNER_ID"));
                    partner.setPartner_name(jsonObject.getString("S_PARTNER_NAME"));
                    partner.setShort_name(jsonObject.getString("S_SHORT_NAME"));
                    return partner;
                }
            });
    }

    @Override
    public Observable<List<UserData>> listUserByPartnerIds(SQLConnection connection, String userId, String partnerIds) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(partnerIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_USER_BY_PARTNER_IDS, inParams, outParams).map(result -> {
            List<UserData> listUser = null;

            int resultCode = result.getOutput().getInteger(3);
            if (resultCode != 200) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            listUser = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                UserData user = resultSetToUser(jsonObject);
                listUser.add(user);
            }
            return listUser;
        });
    }
    @Override
    public Observable<List<UserData>> listUserByMerchantIds(SQLConnection connection, String userId, String merchantIds) {
        LOGGER.log(Level.INFO, "userId: " + userId);
        LOGGER.log(Level.INFO, "merchantIds: " + merchantIds);

        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(merchantIds);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(LIST_USER_BY_MERCHANT_IDS, inParams, outParams).map(result -> {
            List<UserData> listUser = null;

            int resultCode = result.getOutput().getInteger(3);
            LOGGER.log(Level.INFO, "resultCode: " + resultCode);
            if (resultCode != 200) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            listUser = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                UserData user = resultSetToUser(jsonObject);
                listUser.add(user);
            }
            return listUser;
        });
    }

    @Override
    public Observable<UserData> resetPass(SQLConnection conn, String userId, String newPass, String platform) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(userId)
                .add(newPass);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull();

        return conn.callWithParamsObservable2(USER_RESET_PASS, inParams, outParams)
                .flatMap(result -> {
                    int resultCode = result.getOutput().getInteger(1);
                    String message = result.getOutput().getString(2);
                    if (resultCode != 200) {
                        return Observable.error(IErrors.INTERNAL_SERVER_ERROR);
                    }

                    Map map = result.getOutput().getJsonObject(0).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

                    JsonObject jsonObject = rs.getRows().get(0);
                    UserData user = resultsetUser(jsonObject);

                    //insert_audit_log bằng AuditLogService
                    return AuditLogService.auditLog(
                                    conn,
                                    userId,
                                    "MA_RESET_PASSWORD",
                                    Objects.equals(resultCode, 200) ? "SUCCESS" : "FAIL",
                                    Objects.equals(resultCode, 200) ? "Reset mật khẩu thành công" : message,
                                    platform
                            )
                            .onErrorResumeNext(err -> {
                                LOGGER.warning("[AUDIT_LOG] Ghi log MA_RESET_PASSWORD thất bại: " + err.getMessage());
                                return Observable.just(false);
                            })
                            .map(ignore -> user);
                });
    }

    public Observable<UserData> updateRoleAndMerchant(SQLConnection connection, UserData user) {
        //delete UserRole
        return userRoleService.delete(connection, user.getN_id()).flatMap(code -> {
            //insert role_user_insert
            String listUserRole = "";
            List<String> roleIdListDistinct = user.getRoles().stream().map(roleData -> {return String.valueOf(roleData.getRole_id());}).distinct().collect(Collectors.toList());
//            for (Integer id : roleIdListDistinct) {
//                if (!"".equals(listUserRole)) {
//                    listUserRole += ",";
//                }
//                listUserRole += Integer.toString(role.getRole_id());
//            }
            listUserRole = String.join(",", roleIdListDistinct);
            LOGGER.log(Level.SEVERE, "LIST ROLE ID: " +listUserRole);
            return userRoleService.insertList(connection, user.getN_id(), listUserRole).flatMap(interger -> {
                //delete UserMerchant
                return userMerchantService.deleteByUserIdV3(connection, user.getN_id(), user.getMerchants()).flatMap(result -> {
                    //insert list user_merchant
                    user.setMerchants(user.getMerchants().stream()
                            .filter(m -> (m.getMerchant_id() != null && !m.getMerchant_id().isEmpty() && !"No Select".equalsIgnoreCase(m.getMerchant_id())))
                            .collect(Collectors.toList()));
                    return this.insertMerchantList(connection, user.getMerchants(), user.getN_id(), 0).flatMap(merchantList -> {

                        return getUserData(connection, user.getS_id()).flatMap(userResult -> {
                            userResult.setRoles(user.getRoles());
                            userResult.setMerchants(user.getMerchants());
                            return Observable.just(userResult);
                        });
                    });
                });
            });
        });
    }

    private Observable<List<Merchant>> insertMerchantList(SQLConnection sqlConnection, List<Merchant> list, Integer userId, int index) {
        if (list.size() <= 0) {
            return Observable.just(list);
        }
        Merchant m = list.get(index);
        final int finalIndex = index;
        return Observable.just(m).flatMap(merchant -> {
            //insert approval for user group
            return userMerchantService.insert(sqlConnection, m, userId).flatMap(integer -> {
                if (finalIndex >= list.size() - 1) {
                    return Observable.just(list);
                } else {
                    return insertMerchantList(sqlConnection, list, userId, finalIndex + 1);
                }
            });
        });

    }


    @Override
    public Observable<List<UserData>> listByCreateId(SQLConnection connection, String createId) {
        JsonArray inParams = new JsonArray()
                .add(createId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(GET_USER_BY_CREATEID, inParams, outParams).map(result -> {
            List<UserData> userList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                UserData user = resultSetToUser(jsonObject);
                userList.add(user);
            }
            return userList;
        });
    }

    @Override
    public Observable<UserData> getUserDataBySId(SQLConnection connection, String sId) {
        return getUserData(connection, sId).flatMap(user -> {
            if (user == null) {
                LOGGER.log(Level.SEVERE, " USER ID NOT EXIT IN DATABASE ");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return getListRoleByUser(connection, user).flatMap(userData -> {
                return getListMerchantByUser(connection, userData);
            });
        });
    }

    /**
     * get list role for user
     *
     * @param sqlConnection
     * @param user
     * @return
     */
    private Observable<UserData> getListRoleByUser(SQLConnection sqlConnection, UserData user) {
        // return Observable.just(user).flatMap(userData -> {
        return roleService.listRoleByUserIdV2(sqlConnection, user.getS_id()).flatMap(roles -> {
            user.setRoles(roles);
            return Observable.just(user);
        });
        //});

    }

    /**
     * get list merchant for user
     *
     * @param sqlConnection
     * @param userData
     * @return
     */
    private Observable<UserData> getListMerchantByUser(SQLConnection sqlConnection, UserData userData) {
        return merchantService.listAllByUserId(sqlConnection, userData.getS_id()).flatMap(merchants -> {
            userData.setMerchants(merchants);
            return Observable.just(userData);
        });
    }

    /**
     * insert user data oneam
     *
     * @param connection
     * @param user
     * @return
     */
    private Observable<String> insertUser(SQLConnection connection, UserData user) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(user.getName())
                .addNull()
                .add(user.getPass())
                .add(user.getPhone())
                .add(user.getEmail())
                .add(user.getAuthLevel());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.NUMBER);
        return connection.callWithParamsObservable2(USER_INSERT, inParams, outParams).map(result -> {
            String returnData = "-1";
            int code = result.getOutput().getInteger(9);
            if (code != 201) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert user  ONEAM failed" + result.getOutput().getString(8));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnData = result.getOutput().getString(7);
            }

            return returnData;
        });
    }

    /**
     * insert user data merchant
     *
     * @param connection
     * @param user
     * @return
     */
    private Observable<Integer> insertUserMer(SQLConnection connection, UserData user) {
        JsonArray inParams = new JsonArray()
                .add(user.getS_id())
                .addNull()
                .add(user.getName())
                .add(user.getEmail())
                .add(user.getPhone() == null ? StringPool.BLANK : user.getPhone())
                .addNull()
                .addNull()
                .add(user.getCreateId() == null ? StringPool.BLANK : user.getCreateId())
                .add(user.getJobTitle() == null ? StringPool.BLANK : user.getJobTitle());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(USER_INSERT_MER, inParams, outParams).map(result -> {
            int returnData = 0;
            int code = result.getOutput().getInteger(9);
            if (code != 201) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert user MER failed" + result.getOutput().getString(11));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnData = result.getOutput().getInteger(10);
            }

            return returnData;
        });
    }

    /**
     * GET USER DATA from Mer
     *
     * @param sqlConnection
     * @param sid
     */
    private Observable<UserData> getUserData(SQLConnection sqlConnection, String sid) {
        JsonArray inParams = new JsonArray().add(sid);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(GET_USER_BY_SID, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            UserData user = resultSetToUser(jsonObject);
            return user;
        });

    }

    /**
     * update user data oneam
     *
     * @param connection
     * @param user
     * @return
     */
    private Observable<String> updateUser(SQLConnection connection, UserData user) {
        JsonArray inParams = new JsonArray();
        if (user.getStatus() == null) {
            inParams.add(user.getS_id())
                    .add(user.getName())
                    .addNull();
        } else {
            inParams.add(user.getS_id())
                    .addNull()
                    .add(user.getStatus());
        }
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(UPDATE_USER_ONEAM, inParams, outParams).map(result -> {
            String returnData = "";
            int code = result.getOutput().getInteger(3);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert user ONEAM failed" + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnData = result.getOutput().getString(4);
            }

            return returnData;
        });
    }

    /**
     * update user data merchant
     *
     * @param connection
     * @param user
     * @return
     */
    private Observable<Integer> updateUserMer(SQLConnection connection, UserData user) {
        JsonArray inParams = new JsonArray()
                .add(user.getS_id())
                .add(user.getName())
                .add(user.getJobTitle() == null ? StringPool.BLANK : user.getJobTitle());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(UPDATE_USER_MER, inParams, outParams).map(result -> {
            int returnData = 0;
            int code = result.getOutput().getInteger(3);
            if (code != 200) {
                LOGGER.log(Level.SEVERE, "[ USER ] Insert user MER failed" + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                returnData = code;
            }

            return returnData;
        });
    }

    @Override
    public Observable<String> findIdByMobile(SQLConnection connection, String mobile) {
        JsonArray inParams = new JsonArray()
                .add(mobile);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(GET_USER_BY_MOBILE, inParams, outParams).map(result -> {
            String userId = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                userId = jsonObject.getString("S_USER_ID");
            }
            return userId;
        });
    }

    @Override
    public Observable<List<String>> listRecentPasswords(SQLConnection connection, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);

        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connection.callWithParamsObservable2(LIST_RECENT_PASSWORDS, inParams, outParams).map(result -> {
            List<String> listRecentPasswords;

            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            listRecentPasswords = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                listRecentPasswords.add(jsonObject.getString("S_PASSWORD"));
            }
            return listRecentPasswords;
        });
    }

    /**
     * bind data to user
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private UserData resultSetToUser(JsonObject rs) {
        String name = rs.getString("S_NAME");
        String sid = rs.getString("S_ID");
        int id = rs.getInteger("N_ID");
        String address = rs.getString("S_ADDRESS");
        String phone = rs.getString("S_PHONE");
        String email = rs.getString("S_EMAIL");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        String create_id = rs.getString("S_CREATE_ID");
        String job_title = rs.getString("S_JOB_TITLE");
        String status = rs.getString("S_STATUS");
        String amFirstName = rs.getString("S_AM_FIRST_NAME");
        String amLastName = rs.getString("S_AM_LAST_NAME");
        String city = rs.getString("S_PROVINCE");
        String district = rs.getString("S_DISTRICT");
        String countryCode = rs.getString("S_COUNTRY_CODE");
        UserData user = new UserData();
        user.setN_id(id);
        user.setS_id(sid);
        user.setName(name);
        user.setAddress(address);
        user.setPhone(phone);
        user.setEmail(email);
        user.setUpdate_date(Timestamp.valueOf(rs.getString("D_CREATE")));
        user.setCreate_date(Timestamp.valueOf(rs.getString("D_CREATE")));
        user.setCreateId(create_id);
        user.setJobTitle(job_title);
        user.setAm_first_name(amFirstName);
        user.setAm_last_name(amLastName);
        user.setState(city);
        user.setCity(district);
        user.setCountry_code(countryCode);
        user.setStatus(status);

        return user;
    }
    private UserData resultsetUser(JsonObject rs) {
        String sid = rs.getString("S_ID");
        String name = rs.getString("S_NAME");
        String email = rs.getString("S_EMAIL");
        String phone = rs.getString("S_PHONE");
        UserData user = new UserData();
        user.setS_id(sid);
        user.setName(name);
        user.setEmail(email);
        user.setPhone(phone);

        return user;
    }

    /**
     * Thực hiện thay đổi mật khẩu cho người dùng và ghi log hoạt động.
     *
     * @param conn     Kết nối cơ sở dữ liệu.
     * @param userId   Mã người dùng.
     * @param pass     Mật khẩu hiện tại.
     * @param newPass  Mật khẩu mới.
     * @param platform Nền tảng thực hiện yêu cầu (ví dụ: APP, WEB).
     * @return Observable chứa mã kết quả sau khi thay đổi mật khẩu.
     */
    @Override
    public Observable<Integer> changePassV2(SQLConnection conn, String userId, String pass, String newPass, String platform) {
        LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] userId " + userId + " pass " + pass + " new pass " + newPass
                + " platform " + platform);
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(pass)
                .add(newPass);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(USER_CHANGE_PASS_V2, inParams, outParams)
                .flatMap(result -> {
                    int returnValue;
                    int resultCode = result.getOutput().getInteger(3);
                    String message = result.getOutput().getString(4);
                    if (resultCode != 200) {
                        LOGGER.log(Level.SEVERE, "[ USER CHANGE PASS ] Change pass Failed" + StringPool.SPACE + result.getOutput().getString(4));
                        if (resultCode == 404) {
                            throw IErrors.PASS_NOT_FOUND;
                        }
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    } else {
                        returnValue = result.getOutput().getInteger(3);
                    }

                    //insert_audit_log bằng AuditLogService
                    return AuditLogService.auditLog(
                                    conn,
                                    userId,
                                    "MA_CHANGE_PASSWORD",
                                    Objects.equals(resultCode, 200) ? "SUCCESS" : "FAIL",
                                    Objects.equals(resultCode, 200) ? "MA - đổi mật khẩu thành công" : message,
                                    platform
                            )
                            .onErrorResumeNext(err -> {
                                LOGGER.warning("[AUDIT_LOG] Ghi log MA_CHANGE_PASSWORD thất bại: " + err.getMessage());
                                return Observable.just(false);
                            })
                            .map(ignore -> returnValue); // now returnValue is passed through
                });

    }


    private static final String USER_CHANGE_PASS = "{call PKG_USER_V2.USER_CHANGE_PASS(?,?,?,?,?)}";
    private static final String GET_USER_BY_SID = "{call PKG_USER_V2.USER_SEARCH_BY_SID(?,?,?,?)}";
    private static final String USER_INSERT = "{call ONEAM.PKG_USER.INSERT_USER_V2(?,?,?,?,?,?,?,?,?,?)}";
    private static final String USER_INSERT_MER = "{call PKG_USER_V2.INSERT_USER(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_USER_BY_CREATEID = "{call PKG_USER_V2.USER_SEARCH_BY_CREATEID(?,?,?,?)}";
    private static final String GET_USER_BY_MOBILE = "{call PKG_USER_V2.find_user_id_by_mobile(?,?,?,?)}";
    private static final String UPDATE_USER_ONEAM = "{call ONEAM.UPDATE_USER_V3(?,?,?,?,?)}";
    private static final String UPDATE_USER_MER = "{call PKG_USER_V2.UPDATE_USER_1(?,?,?,?,?)}";
    private static final String GET_USER_EXISTED_V2 = "{call ONEAM.PKG_USER.GET_USER_EXISTED_V2(?,?,?,?,?)}";
    private static final String USER_RESET_PASS = "{call USER_RESET_PASS(?,?,?,?,?)}";
    private static final String GET_PARTNER_BY_ID = "{call PKG_USER_V2.get_partner_by_user_id(?,?,?,?)}";
    private static final String LIST_USER_BY_PARTNER_IDS = "{call PKG_USER_V2.list_user_by_partner_ids(?,?,?,?,?)}";
    private static final String LIST_USER_BY_MERCHANT_IDS = "{call PKG_USER_V2.list_user_by_merchant_ids(?,?,?,?,?)}";
    private static final String LIST_RECENT_PASSWORDS = "{call USER_RECENT_PASSWORDS(?,?,?,?)}";
    private static final String USER_CHANGE_PASS_V2 = "{call USER_CHANGE_PASS(?,?,?,?,?)}";

    @Autowired
    private RoleService roleService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserMerchantService userMerchantService;

    private static final Logger LOGGER = Logger.getLogger(UserServiceV2Impl.class.getName());

}
