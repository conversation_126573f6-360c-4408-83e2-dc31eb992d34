package com.onepay.ma.service.service.pay_collect;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_collect.UserConfigDto;
import com.onepay.ma.service.models.pay_collect.UserQueryPayCollectDto;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

import io.vertx.core.json.JsonObject;
import rx.Observable;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/7/2020
 * Time: 10:32 AM
 * To change this ma-web.
 */

public interface UserConfigService {
    Observable<List<UserConfigDto>> all(SQLConnection connection, Map<String, String> mIn);
    Observable<List<UserConfigDto>> search(SQLConnection connection, Map<String, String> mIn);
    Observable<JsonObject> get(SQLConnection connection, String id);
    ResultSet download(Connection connection, UserQueryPayCollectDto dto);
    Observable<BaseList<UserConfigDto>> total(SQLConnection connection, Map<String, String> mIn);

}
