package com.onepay.ma.service.service.mpayPromotion.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.PromotionAmount;
import com.onepay.ma.service.models.PromotionCard;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionQuery;
import com.onepay.ma.service.models.mpayPromotion.MpayPromotionTransaction;
import com.onepay.ma.service.service.mpayPromotion.MpayPromotionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 26-Jan-18.
 */
@Service
public class MpayPromotionServiceImpl implements MpayPromotionService {
    @Override
    public Observable<Transactions<MpayPromotionTransaction>> search(SQLConnection connOnline, SQLConnection connReadOnly, MpayPromotionQuery query) {

        Transactions<MpayPromotionTransaction> transactions = new Transactions<>();

        return this.getListTotalTransaction(connOnline, query,-1).flatMap(total1 -> {
            return this.getListTotalTransaction(connReadOnly, query,1).flatMap(total2 -> {
                transactions.setTotal_items(total1 + total2);
                // Get online transactions
                query.setOffset(0);
                return this.searchTransaction(connOnline, query,-1).flatMap(onlineTrans -> {
                    // offset
                    query.setOffset(total1);
                    // Get readonly transaction
                    return this.searchTransaction(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {
                        onlineTrans.addAll(readOnlyTrans);
                        // set & Return.
                        transactions.setTransactions(onlineTrans);
                        return Observable.just(transactions);
                    });
                });
            });
        });
    }

    @Override
    public Observable<MpayPromotionTransaction> get(SQLConnection connOnline, String id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET mPAY TRANSACTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            MpayPromotionTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bind(jsonObject);

            return transaction;
        });
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, MpayPromotionQuery query) {

        return this.getListTotalTransaction(connOnline, query,-1).flatMap(total1 -> {
            return this.getListTotalTransaction(connReadOnly, query,1).map(total2 -> {
                return total1 + total2;
            });
        });
    }
    @Override
    public ResultSet downloadOnline(Connection connOnline, MpayPromotionQuery parameter) throws SQLException {
        return downloadData(connOnline, parameter,-1);
    }

    @Override
    public ResultSet downloadReadonly(Connection connReadOnly, MpayPromotionQuery parameter) throws SQLException {
        return downloadData(connReadOnly, parameter,1);
    }

    public ResultSet downloadData(Connection connection, MpayPromotionQuery query, int rows) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getMerchant_id())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .add(query.getCard_number())
                .add(query.getOrder_info())
                .add(query.getTransaction_id())
                .add(query.getMobile())
                .add(query.getCurrency())
                .add(query.getPromotion_id())
                .addNull()
                .add(query.getStatus())
                .add(query.getCard_type())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_TRANSACTION, inParams, outParams);

            Integer reusltCode = callableStatement.getInt(21);

            if(reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD mPAY TRANSACTION ERROR]: " + callableStatement.getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(20);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return  rs;
    }

    private Observable<List<MpayPromotionTransaction>> searchTransaction(SQLConnection sqlConnection, MpayPromotionQuery query, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.SELECT.toString())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getMerchant_id())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .add(query.getCard_number())
                .add(query.getOrder_info())
                .add(query.getTransaction_id())
                .add(query.getMobile())
                .add(query.getCurrency())
                .add(query.getPromotion_id())
                .addNull()
                .add(query.getStatus())
                .add(query.getCard_type())
                .add(query.getPage())
                .add(query.getPageSize())
                .add(query.getOffset());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SEARCH_TRANSACTION, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(20);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY PR TRANSACTION ERROR]: " + result.getOutput().getString(21));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<MpayPromotionTransaction> transactionList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(19).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    transactionList.add(bind(jsonObject));
                }
            }
            return transactionList;
        });
    }

    private Observable<Integer> getListTotalTransaction(SQLConnection sqlConnection, MpayPromotionQuery query, int rows) {
        JsonArray inParams = new JsonArray()
                .add(rows)
                .add(QueryMethod.TOTAL.toString())
                .add(query.getFrom_date())
                .add(query.getTo_date())
                .add(query.getMerchant_id())
                .add(query.getMerchant_transaction_ref())
                .addNull()
                .add(query.getCard_number())
                .add(query.getOrder_info())
                .add(query.getTransaction_id())
                .add(query.getMobile())
                .add(query.getCurrency())
                .add(query.getPromotion_id())
                .addNull()
                .add(query.getStatus())
                .add(query.getCard_type())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SEARCH_TRANSACTION, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(20);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH mPAY PR TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(21));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total =0;

            Map map = result.getOutput().getJsonObject(19).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()){
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });
    }

    private MpayPromotionTransaction bind(JsonObject jo) {
        MpayPromotionTransaction model = new MpayPromotionTransaction();

        model.setTransaction_id(jo.getString("S_ID"));
        model.setMerchant_id(jo.getString("S_MERCHANT_ID"));
        model.setTransaction_reference(jo.getString("S_MERCHANT_INVOICE_REF"));
        model.setPromotion_id(jo.getString("S_PR_ID"));
        model.setOrder_info(jo.getString("S_INFO"));
        model.setStatus(jo.getString("S_STATUS"));
        model.setRef_id(jo.getString("S_MSP_PAYMENT_ID"));
        model.setInvoice_id(jo.getString("S_INVOICE_ID"));
        model.setMobile(jo.getString("S_MOBILE"));
        model.setStatus(jo.getString("S_STATE"));
        model.setCard_type(jo.getString("S_CARD_TYPE"));
        model.setTransaction_time(jo.getString("D_CREATE") == null ? null : Timestamp.valueOf(jo.getString("D_CREATE")));

        PromotionCard card = new PromotionCard();
        card.setCard_number(jo.getString("S_INS_NUMBER"));
        model.setCard(card);

        PromotionAmount amount = new PromotionAmount();
        amount.setCurrency(jo.getString("S_CURRENCY"));
        amount.setTotal(jo.getDouble("N_TOTAL_AMOUNT"));
        amount.setDiscount_amount(jo.getDouble("N_AMOUNT") - jo.getDouble("N_TOTAL_AMOUNT"));
        amount.setOriginal_total(jo.getDouble("N_AMOUNT"));
        model.setAmount(amount);

        return model;
    }

    private static final String SEARCH_TRANSACTION = "{call PKG_MPAY_PROMOTION.PAYMENT_SEARCH(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_GET_BY_ID = "{call PKG_MPAY_PROMOTION.PAYMENT_GET_BY_ID(?,?,?,?)}";

    private Logger LOGGER = Logger.getLogger(MpayPromotionServiceImpl.class.getName());


}
