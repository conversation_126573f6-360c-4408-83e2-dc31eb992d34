package com.onepay.ma.service.service.mpayPromotion;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReport;
import com.onepay.ma.service.models.mpayPromotion.MpayPrReportQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by anhkh on 01-Feb-18.
 */
public interface MpayPrReportService {

    Observable<BaseList<MpayPrReport>> list(SQLConnection connection, MpayPrReportQuery parameter);
    default Observable<Integer>  getTotalReport(SQLConnection connReadOnly, MpayPrReportQuery parameter) {
        return list(connReadOnly, parameter).map(mpayPrReportBaseList -> {
            return mpayPrReportBaseList.getTotal_items();
        });
    };
    ResultSet download(Connection connReadOnly, MpayPrReportQuery parameter) throws SQLException;
}
