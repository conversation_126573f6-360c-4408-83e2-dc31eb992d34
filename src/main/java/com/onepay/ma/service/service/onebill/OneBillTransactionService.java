package com.onepay.ma.service.service.onebill;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.onebill.OnebillClient;
import com.onepay.ma.service.models.onebill.OnebillTransactionHistory;
import com.onepay.ma.service.models.onebill.OnebillTransaction;
import com.onepay.ma.service.models.onebill.OnebillTransactionQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
public interface OneBillTransactionService {
    Observable<BaseList<OnebillTransaction>> search(SQLConnection onebillConn, OnebillTransactionQuery query);

    Observable<Integer> total(SQLConnection onebillConn, OnebillTransactionQuery query);

    Observable<OnebillTransaction> getById(SQLConnection onebillConn, Integer id);

    ResultSet download(Connection connection, OnebillTransactionQuery query) throws SQLException;

    Observable<BaseList<OnebillClient>> listClient(SQLConnection onebillConn);

    Observable<List<OnebillTransactionHistory>> listHistory(SQLConnection onebillConn, Integer transactionId);

    Observable<Void> refund(SQLConnection onebillConn, Integer transactionId, Double amount, String operator);
}
