package com.onepay.ma.service.service.orderApproval;

import com.onepay.ma.service.models.orderApproval.OrderApproval;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

public interface OrderApprovalService {
    Observable<OrderApproval> getByTransactionId(SQLConnection connection, String transactionId);

    Observable<OrderApproval> insert(SQLConnection connection, OrderApproval model);
}
