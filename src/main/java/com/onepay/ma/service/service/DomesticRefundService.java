package com.onepay.ma.service.service;

import com.onepay.ma.service.models.DomesticRefund;
import com.onepay.ma.service.models.DomesticRefundParameter;
import com.onepay.ma.service.models.Transactions;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/3/16.
 */
public interface DomesticRefundService {
    Observable<Transactions<DomesticRefund>> list(SQLConnection sqlReadOnly, SQLConnection sqlOnline,SQLConnection sqlBackup, DomesticRefundParameter parameter);
    Observable<DomesticRefund> get(SQLConnection sqlOnline,SQLConnection sqlBackup, String transactionId);
    Observable<DomesticRefund> getByRef(SQLConnection sqlOnline, String ref);
    Observable<Integer> getTotalDownload(SQLConnection connOnline,  SQLConnection readOnlyConn,SQLConnection connBackup,  DomesticRefundParameter parameter);
    default ResultSet downloadOnline(Connection connOnline, DomesticRefundParameter parameter) throws SQLException {
        return download(connOnline, parameter, -1);
    }
    default ResultSet downloadReadonly(Connection connReadOnly, DomesticRefundParameter parameter, int rows) throws SQLException {
        return download(connReadOnly, parameter, rows);
    }
    ResultSet downloadBackp(Connection connBackup, DomesticRefundParameter parameter) throws SQLException;

    ResultSet download(Connection connOnline, DomesticRefundParameter parameter, int rows) throws SQLException;

    Observable<List<DomesticRefund>> getListTransactionBackup(SQLConnection connBackup, DomesticRefundParameter parameter);

    Observable<List<DomesticRefund>> getListTransaction(SQLConnection connReadOnly, DomesticRefundParameter parameter, int rows);
}
