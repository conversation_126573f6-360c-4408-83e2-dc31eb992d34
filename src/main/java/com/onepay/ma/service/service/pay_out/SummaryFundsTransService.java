package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.pay_out.ListSummaryFundsTransDto;
import com.onepay.ma.service.models.pay_out.ModelDto;
import com.onepay.ma.service.models.pay_out.SummaryFundsTransDto;
import com.onepay.ma.service.models.pay_out.SummaryQueryDto;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 11/26/2020
 * Time: 4:25 PM
 * To change this ma-web.
 */

public interface SummaryFundsTransService {
    Observable<List<SummaryFundsTransDto>> getSummaryFundsTrans(SQLConnection connection, Map<String, String> mIn);

    Observable<ListSummaryFundsTransDto> getTotalSummaryFundsTrans(SQLConnection connection, Map<String, String> mIn);

    ResultSet dowloadfile(Connection connection, SummaryQueryDto query);
}
