package com.onepay.ma.service.service;

import com.onepay.ma.service.models.Approval;
import com.onepay.ma.service.models.ApprovalType;
import com.onepay.ma.service.models.Approvals;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/7/16.
 */
public interface ApprovalService {
    Observable<Approvals> list(SQLConnection connection, ApprovalType approvalType, String keyword, int page, String status);
    Observable<Approval>  get(SQLConnection connection, int nid);
    Observable<Approval> update(SQLConnection connection, int id, String status, String userId);
    Observable<Approval> insert(SQLConnection connection, Approval approval);
}
