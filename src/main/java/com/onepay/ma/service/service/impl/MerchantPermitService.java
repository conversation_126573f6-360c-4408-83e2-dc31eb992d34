package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.MerchantRefundPermit;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by anhkh on 8/9/2016.
 */
public interface MerchantPermitService {
    Observable<Integer> countMerchantUserApprovalById(SQLConnection backupConn, String merchantId, Integer type);

    default Observable<Boolean> isMerchantPermittedRefundApproval(SQLConnection backupConn, String merchantId, Integer type) {
        return this.countMerchantUserApprovalById(backupConn, merchantId,type).flatMap(integer -> {
            return Observable.just(integer > 0);
        });
    }

    Observable<MerchantRefundPermit> getMerchantRefundPermit(SQLConnection backupConn, String merchantId, Integer type);
}
