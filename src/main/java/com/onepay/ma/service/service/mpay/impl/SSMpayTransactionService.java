package com.onepay.ma.service.service.mpay.impl;

import com.google.gson.Gson;
import com.ibm.icu.text.DateFormat;
import com.ibm.icu.text.SimpleDateFormat;
import com.onepay.ma.service.models.ManualRefundDescription;
import com.onepay.ma.service.models.base.NewBaseList;
import com.onepay.ma.service.models.mpay.*;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.Acquirer;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.Instrument;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.sql.UpdateResult;
import oracle.jdbc.OracleTypes;

import org.springframework.stereotype.Service;

import groovy.json.JsonException;
import rx.Observable;

import java.io.InputStreamReader;
import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class SSMpayTransactionService {

    private static final Logger LOGGER = Logger.getLogger(SSMpayTransactionService.class.getName());

    private final static Gson gson = new Gson();

    private static final String SAMSUNG_SEARCH_TRANSACTION_2 = "{call PKG_MPAY.SAMSUNG_PAYMENT_SEARCH_V3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String SAMSUNG_TRANSACTION_GET_BY_ID = "{call PKG_MPAY.SAMSUNG_PAYMENT_GET_BY_ID(?,?,?,?)}";

    public static Observable<NewBaseList<SamsungMpayTransaction>> search(SQLConnection connReadOnly,
            SQLConnection connOnline, SamsungMpayTransactionQuery query) {

        // LOGGER.log(Level.INFO, "query condition: " + gson.toJson(query));

        NewBaseList<SamsungMpayTransaction> transactions = new NewBaseList<>();

        return getListTotalTransaction(connOnline, query, -1).flatMap(total1 -> {
            return getListTotalTransaction(connReadOnly, query, 1).flatMap(total2 -> {
                transactions.setTotalItems(total1 + total2);
                int basePageSize = query.getPageSize();
                // Get online transactions
                query.setOffset(0);
                return searchTransaction(connOnline, query, -1).flatMap(onlineTrans -> {
                    query.setOffset(total1);
                    // Get readonly transaction
                    return searchTransaction(connReadOnly, query, onlineTrans.size()).flatMap(readOnlyTrans -> {

                        onlineTrans.addAll(readOnlyTrans);
                        // Mark number index
                        int index = 1;
                        for (SamsungMpayTransaction transaction : onlineTrans) {
                            transaction.setRowNum(index + (query.getPage() * basePageSize));
                            index++;
                        }

                        // set & Return.
                        transactions.setList(onlineTrans);

                        return Observable.just(transactions);
                    });
                });
            });
        });
    }

    private static Observable<Integer> getListTotalTransaction(SQLConnection conn, SamsungMpayTransactionQuery query,
            int rows) {
        JsonArray inParams = new JsonArray().add(rows).add(QueryMethod.TOTAL.toString()).add(query.getFromDate())
                .add(query.getToDate()).add(query.getKeywords()).add(query.getMerchantId()).add(query.getMerchantName())
                .add(query.getAcqCode()).add(query.getMerchantTransactionRef()).add(query.getTerminalId())
                .add(query.getInstrumentNumber()).add(query.getOrderInfo()).add(query.getTransactionId())
                .add(query.getCustomerTransactionId()).add(query.getBankTransactionId()).add(query.getBankId())
                .add(query.getCurrency()).add(query.getStatus())
                .add(query.getMasking() == null ? "" : query.getMasking())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getQrId() == null ? "" : query.getQrId())
                .add(query.getInvoiceId() == null ? "" : query.getInvoiceId())
                .add(query.getClientId() == null ? "" : query.getClientId())
                .add(query.getVersion() == null ? "" : query.getVersion()).addNull().addNull().addNull()
                .add(query.getCustomerEmail() == null ? "" : query.getCustomerEmail())
                .add(query.getCustomerPhone() == null ? "" : query.getCustomerPhone())
                .add(query.getMerchantWebsite() == null ? "" : query.getMerchantWebsite());
        JsonArray outParams = new JsonArray().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().add(OracleTypes.CURSOR).add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return conn.callWithParamsObservable2(SAMSUNG_SEARCH_TRANSACTION_2, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(31);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE,
                        "[SEARCH SAMSUNG mPAY TRANSACTION TOTAL ERROR]: " + result.getOutput().getString(32));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = 0;

            Map map = result.getOutput().getJsonObject(30).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs != null) {
                for (JsonObject jsonObject : rs.getRows()) {
                    total = jsonObject.getInteger("N_TOTAL");
                }
            }
            return total;
        });
    }

    public static Observable<SamsungMpayTransaction> get(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray().add(transactionId);
        JsonArray outParams = new JsonArray().addNull().add(OracleTypes.INTEGER).add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        return connOnline.callWithParamsObservable2(SAMSUNG_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Integer reusltCode = result.getOutput().getInteger(1);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE, "[GET SAMSUNG mPAY TRANSACTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            SamsungMpayTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);

            return transaction;
        });
    }

    private static Observable<List<SamsungMpayTransaction>> searchTransaction(SQLConnection sqlConnection,
            SamsungMpayTransactionQuery query, int rows) {
        JsonArray inParams = new JsonArray().add(rows).add(QueryMethod.SELECT.toString()).add(query.getFromDate())
                .add(query.getToDate()).add(query.getKeywords()).add(query.getMerchantId()).add(query.getMerchantName())
                .add(query.getAcqCode()).add(query.getMerchantTransactionRef()).add(query.getTerminalId())
                .add(query.getInstrumentNumber()).add(query.getOrderInfo()).add(query.getTransactionId())
                .add(query.getCustomerTransactionId()).add(query.getBankTransactionId()).add(query.getBankId())
                .add(query.getCurrency()).add(query.getStatus()).add(query.getMasking()).add(query.getAppName())
                .add(query.getQrId()).add(query.getInvoiceId())
                .add(query.getClientId() == null ? "" : query.getClientId())
                .add(query.getVersion() == null ? "" : query.getVersion()).add(query.getPage()).add(query.getPageSize())
                .add(query.getOffset()).add(query.getCustomerEmail() == null ? "" : query.getCustomerEmail())
                .add(query.getCustomerPhone() == null ? "" : query.getCustomerPhone())
                .add(query.getMerchantWebsite() == null ? "" : query.getMerchantWebsite());
        JsonArray outParams = new JsonArray().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().add(OracleTypes.CURSOR).add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SAMSUNG_SEARCH_TRANSACTION_2, inParams, outParams)
                .map(result -> {
                    Integer reusltCode = result.getOutput().getInteger(31);

                    if (reusltCode != 200) {
                        LOGGER.log(Level.SEVERE,
                                "[SEARCH SAMSUNG mPAY TRANSACTION ERROR]: " + result.getOutput().getString(32));
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                    List<SamsungMpayTransaction> transactionList = new ArrayList<>();

                    Map map = result.getOutput().getJsonObject(30).getMap();
                    io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
                    if (rs != null) {
                        for (JsonObject jsonObject : rs.getRows()) {
                            SamsungMpayTransaction transaction = bindTransaction(jsonObject);
                            transactionList.add(transaction);
                        }
                    } else {
                    }
                    return transactionList;
                });
    }


    private static String SAMSUNG_EPP = PropsUtil.get("samsung.epp", "");

    private static SamsungMpayTransaction bindTransaction(JsonObject rs) {
        String id = rs.getString("S_ID");
        double total = rs.getDouble("N_AMOUNT");
        Double refund_total = rs.getDouble("N_REFUND_AMOUNT");
        String currency = rs.getString("S_CURRENCY");
        String status = rs.getString("S_STATE");
        String instrumentName = rs.getString("S_INSTRUMENT_NAME");
        String instrumentNumber = rs.getString("S_INSTRUMENT_NUMBER");
        String instrumentType = rs.getString("S_INSTRUMENT_TYPE");
        String instrumentBrandId = rs.getString("S_INSTRUMENT_BRAND_ID");
        String inputType = rs.getString("S_INPUT_TYPE");
        String terminalId = rs.getString("S_TERMINAL_ID");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String merchantName = rs.getString("S_MERCHANT_NAME");
        String onecomMerchantId = rs.getString("S_ONECOM_MERCHANT");
        String customerTransactionId = rs.getString("S_CUSTOMER_TRANS_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        String merchantOrderRef = rs.getString("S_MERCH_ORDER_REF");
        String merchantTxnRef = rs.getString("S_MERCH_TXN_REF");
        String client = rs.getString("S_CLIENT_ID");
        Timestamp createTime = Timestamp.valueOf(rs.getString("D_CREATE"));
        String bankTransRef = rs.getString("S_BANK_TRANS_ID");
        String acqCode = rs.getString("S_MSP_ID");

        // SAMSUNG
        String customerName = rs.getString("S_CUSTOMER_NAME");
        String customerEmail = rs.getString("S_CUSTOMER_EMAIL");
        String customerPhone = rs.getString("S_CUSTOMER_PHONE");
        String sRequestBody = rs.getString("S_REQUEST_BODY");

        // DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        // Timestamp createTime = null;
        //
        // try {
        // createTime = new
        // Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
        // } catch (ParseException e) {
        // LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
        // }

        MpayAcquirer acquirer = new MpayAcquirer();
        // acquirer.setCard_type(rs.getInteger("S_ACQUIRER_ID"));
        acquirer.setAcquirerName(rs.getString("S_INSTRUMENT_TYPE"));

        String txtUpdateTime = rs.getString("D_UPDATE");
        Timestamp updateTime = null;
        if (txtUpdateTime != null) {
            updateTime = Timestamp.valueOf(txtUpdateTime);
        }
        String txtExpireTime = rs.getString("D_EXPIRE");
        Timestamp expireTime = null;

        if (txtExpireTime != null) {
            expireTime = Timestamp.valueOf(rs.getString("D_EXPIRE"));
        }
        String instrumentId = rs.getString("S_INSTRUMENT_ID");

        // Instrument
        Instrument instrument = new Instrument();
        instrument.setName(instrumentName);
        instrument.setNumber(instrumentNumber);
        instrument.setType(instrumentType);

        instrument.setId(instrumentId);
        instrument.setBrandId(instrumentBrandId);

        // Amount
        MpayAmount amount = new MpayAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefundTotal(refund_total); // TODO: fill correct one.

        SamsungMpayTransaction transaction = new SamsungMpayTransaction();
        transaction.setTransactionId(id);
        transaction.setBankTransId(bankTransRef);
        transaction.setCustomerTransId(customerTransactionId);
        transaction.setClientRef(customerTransactionId);
        transaction.setMerchantId(merchantId);
        transaction.setMerchantName(merchantName);
        transaction.setOnecomMerchantId(onecomMerchantId);
        transaction.setCreateTime(createTime);
        transaction.setUpdateTime(updateTime);
        transaction.setExpireTime(expireTime);
        transaction.setStatus(status);
        transaction.setInstrument(instrument);
        transaction.setAmount(amount);
        transaction.setOrderInfo(orderInfo);
        transaction.setInputType(inputType);
        transaction.setTerminalId(terminalId);
        transaction.setMerchantOrderRef(merchantOrderRef);
        transaction.setMerchantTxnRef(merchantTxnRef);
        transaction.setAcquirer(acquirer);
        transaction.setClient(client);
        transaction.setAcqCode(acqCode);

        transaction.setAppName(rs.getString("S_APP_NAME"));
        transaction.setInvoiceId(rs.getString("S_INVOICE_ID"));
        transaction.setMasking(rs.getString("S_MASKING"));
        transaction.setQrId(rs.getString("S_QR_ID"));
        transaction.setClientId(rs.getString("S_CLIENT_ID"));

        transaction.setCustomerName(customerName);
        transaction.setCustomerEmail(customerEmail);
        transaction.setCustomerMobile(customerPhone);
        // Take epp from s_request_body
        List<String> eppL  = Arrays.asList(SAMSUNG_EPP.split(","));
        if (sRequestBody != null) {
            JsonObject sRqBodyJson = new JsonObject(sRequestBody);
            String sReturnUrl = sRqBodyJson.getJsonObject("request_samsung").getString("vpc_ReturnURL");
            String sEpp = sReturnUrl.split("/")[6];
            if (sEpp != null && eppL.contains(sEpp)) {
                transaction.setMerchantWebsite(sEpp);
            } else {
                transaction.setMerchantWebsite("");
            }
        }
        // Return.
        return transaction;
    }

    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly,
            SamsungMpayTransactionQuery query) {
        return getListTotalTransaction(connOnline, query, -1).flatMap(total1 -> {
            return getListTotalTransaction(connReadOnly, query, 1).flatMap(total2 -> {
                return Observable.just(total1 + total2);
            });
        });
    }

    public static ResultSet downloadOnline(Connection connOnline, SamsungMpayTransactionQuery query)
            throws SQLException {
        return getDonwloadData(connOnline, query, -1);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, SamsungMpayTransactionQuery query)
            throws SQLException {
        return getDonwloadData(connReadOnly, query, 1);
    }

    private static ResultSet getDonwloadData(Connection connection, SamsungMpayTransactionQuery query, int rows)
            throws SQLException {
        JsonArray inParams = new JsonArray().add(rows).add(QueryMethod.DOWNLOAD.toString()).add(query.getFromDate())
                .add(query.getToDate()).add(query.getKeywords()).add(query.getMerchantId()).add(query.getMerchantName())
                .add(query.getAcqCode()).add(query.getMerchantTransactionRef()).add(query.getTerminalId())
                .add(query.getInstrumentNumber()).add(query.getOrderInfo()).add(query.getTransactionId())
                .add(query.getCustomerTransactionId()).add(query.getBankTransactionId()).add(query.getBankId())
                .add(query.getCurrency()).add(query.getStatus())
                .add(query.getMasking() == null ? "" : query.getMasking())
                .add(query.getAppName() == null ? "" : query.getAppName())
                .add(query.getQrId() == null ? "" : query.getQrId())
                .add(query.getInvoiceId() == null ? "" : query.getInvoiceId())
                .add(query.getClientId() == null ? "" : query.getClientId())
                .add(query.getVersion() == null ? "" : query.getVersion()).addNull().addNull().addNull()
                .add(query.getCustomerEmail() == null ? "" : query.getCustomerEmail())
                .add(query.getCustomerPhone() == null ? "" : query.getCustomerPhone())
                .add(query.getMerchantWebsite() == null ? "" : query.getMerchantWebsite());
        JsonArray outParams = new JsonArray().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull().addNull()
                .addNull().addNull().addNull().add(OracleTypes.CURSOR).add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SAMSUNG_SEARCH_TRANSACTION_2, inParams,
                    outParams);
            Integer reusltCode = callableStatement.getInt(32);

            if (reusltCode != 200) {
                LOGGER.log(Level.SEVERE,
                        "[DOWNLOAD SAMSUNG mPAY TRANSACTION ONLINE ERROR]: " + callableStatement.getString(33));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(31);

        } catch (SQLException ex) {

            LOGGER.log(Level.SEVERE, "[SEARCH SAMSUNG mPAY TRANSACTION ERROR]: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }
}
