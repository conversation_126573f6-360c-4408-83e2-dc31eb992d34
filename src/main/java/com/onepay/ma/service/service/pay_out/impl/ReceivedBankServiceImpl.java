package com.onepay.ma.service.service.pay_out.impl;

import com.onepay.ma.service.models.pay_out.ModelDto;
import com.onepay.ma.service.service.pay_out.ReceivedBankService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Handle service for ReceivedBankServiceImpl
 * by Tiennv
 * 17/11/2020
 */

@Service
public class ReceivedBankServiceImpl implements ReceivedBankService {

    public static final String GET_RECEIVED_BANK = "{call PKG_ONEPAYOUT.GET_RECEIVED_BANK(?,?,?) }";

    @Override
    public Observable<List<ModelDto>> getListReceivedBank(SQLConnection connection) {
        JsonArray inParams = new JsonArray();
        JsonArray outParams = new JsonArray()
        .add(OracleTypes.CURSOR)
        .add(OracleTypes.INTEGER)
        .add(OracleTypes.VARCHAR);
        
        List<ModelDto> modelDtos = new ArrayList<>();

        return connection.callWithParamsObservable2(GET_RECEIVED_BANK, inParams, outParams).map(result -> {
            
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                ModelDto modelDto = new ModelDto(jsonObject.getString("S_SWIFT_CODE"), jsonObject.getString("S_SHORT_NAME"));
                modelDtos.add(modelDto);
            }
            return modelDtos;
        });
    }

    @Override
    public List<ModelDto> getListReceivedBank(Connection backupConn) throws SQLException {
        JsonArray inParams = new JsonArray();
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        List<ModelDto> modelDtos = new ArrayList<>();
        CallableStatement callableStatement = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(backupConn, GET_RECEIVED_BANK, inParams, outParams);
            Integer error = callableStatement.getInt(2);
            ResultSet rs = (ResultSet) callableStatement.getObject(1);
            if (error != 200) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                while (rs.next()) {
                    ModelDto modelDto = new ModelDto(rs.getString("S_SWIFT_CODE"), rs.getString("S_SHORT_NAME"));
                    modelDtos.add(modelDto);
                }
            }
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            if (callableStatement != null) {
                callableStatement.close();
            }

        }
        return modelDtos;
    }
}
