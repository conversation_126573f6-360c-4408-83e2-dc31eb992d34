package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionMerchant;
import com.onepay.ma.service.service.PromotionMerchantService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 4/10/16.
 */
@Service
public class PromotionMerchantServiceImpl implements PromotionMerchantService{

    @Override
    public Observable<List<PromotionMerchant>> list(SQLConnection sqlConnPr, String promotionId) {
        return getListPromotionMerchant(sqlConnPr, promotionId);
    }

    @Override
    public Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, String merchantId) {
        return insertPromotionMerchant(sqlConnPr, promotionId, merchantId);
    }

    @Override
    public Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId) {
        return deletePromotionMerchant(sqlConnPr, promotionId);
    }

    /**
     * insert promotion merchant
     * @param sqlConn
     * @param promotionId
     * @param merchantId
     * @return
     */
    private Observable<Integer> insertPromotionMerchant(SQLConnection sqlConn, int promotionId, String merchantId){

        JsonArray inParams = new JsonArray()
                .add(promotionId)
                .add(merchantId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return sqlConn.callWithParamsObservable2(PROMOTION_MERCHANT_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION MERCHANT ] Insert Merchant Promotion Failed" + StringPool.SPACE + result.getOutput().getString(3));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(4);
            }

            return returnValue;
        });
    }

    /**
     * delete promotion merchant
     * @param sqlConn
     * @param promotionId
     * @return
     */
    private Observable<Integer> deletePromotionMerchant(SQLConnection sqlConn, int promotionId){

        JsonArray inParams = new JsonArray()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConn.callWithParamsObservable2(PROMOTION_MERCHANT_DELETE, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION MERCHANT ] Insert Merchant Promotion Failed" + StringPool.SPACE +  result.getOutput().getString(2));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    /**
     * get list merchant for promotion
     * @param sqlConn
     * @param promotionId
     * @return
     */
    private Observable<List<PromotionMerchant>> getListPromotionMerchant(SQLConnection sqlConn, String promotionId){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR) ;
        return sqlConn.callWithParamsObservable2(LIST_PROMOTION_MERCHANT, inParams, outParams).map(result -> {

            List<PromotionMerchant> promotionMerchants = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PromotionMerchant promotionMerchant = bindMerchant(jsonObject);
                promotionMerchants.add(promotionMerchant);
            }

            return promotionMerchants;
        });
    }

    /**
     * bind result set to merchant
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionMerchant bindMerchant(JsonObject rs)  {
        int nid = rs.getInteger("N_ID");
        String merchantId = rs.getString("S_ID");
        String merchantName = rs.getString("S_NAME");
        String currencyCode = rs.getString("S_CURRENCY_CODE");

        PromotionMerchant promotionMerchant = new PromotionMerchant();
        promotionMerchant.setMerchant_id(merchantId);
        promotionMerchant.setN_id(nid);
        promotionMerchant.setMerchant_name(merchantName);
        promotionMerchant.setCurrency_code(currencyCode);
        return  promotionMerchant;
    }

    private final static String LIST_PROMOTION_MERCHANT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_MERCHANTS(?,?,?,?,?)}";

    private static final String PROMOTION_MERCHANT_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_MERCHANT_INSERT(?,?,?,?,?)}";

    private static final String PROMOTION_MERCHANT_DELETE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_MERCHANT_DELETE(?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PromotionMerchantService.class.getName());
}
