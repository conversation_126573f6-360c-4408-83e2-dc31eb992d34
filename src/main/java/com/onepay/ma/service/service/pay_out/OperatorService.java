package com.onepay.ma.service.service.pay_out;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonObject;
import rx.Observable;
import java.util.Map;

public interface OperatorService {
    Observable<BaseList<OperatorDTO>> search(SQLConnection sqlConnection, Map<String, String> mIn);
    Observable<Integer> total(SQLConnection onlineConn, Map<String, String> mIn);
    Observable<OperatorDTO> insert(SQLConnection connection, OperatorDTO param);
    Observable<OperatorDTO> update(SQLConnection backupConn, OperatorDTO param);
    Observable<OperatorDTO> updateState(SQLConnection backupConn, JsonObject param);
    Observable<OperatorDTO> getOperatorById(SQLConnection connection, String id);
    Observable<OperatorDTO> resetPass(SQLConnection connection, String operatorId, String newPass);
    Observable<OperatorDTO> getOperatorExisted(SQLConnection connection, String partnerId, String userName);
    Observable<BaseList<OperatorDTO>> getByUserAndPass(SQLConnection connection, String partnerId, String email, String pass );
}
