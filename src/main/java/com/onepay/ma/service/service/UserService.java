package com.onepay.ma.service.service;

import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.Users;
import com.onepay.ma.service.models.user.UserProfile;
import com.onepay.ma.service.models.user.UserSearchQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/3/16.
 */
public interface UserService {

    Observable<List<User>> listByIds(SQLConnection connection, String Ids);
    Observable<Users> list(SQLConnection connection, UserSearchQuery query);
    Observable<UserData> get(SQLConnection connection, String sid);
    Observable<UserProfile> getProfileInfo(SQLConnection connection,SQLConnection connectionBackUp, String sid);
    Observable<User> getOneAm(SQLConnection connection, String sid);
    Observable<User> update(SQLConnection connection, User user);
    Observable<User> insert(SQLConnection connection, User user);
    Observable<Integer> changePass(SQLConnection connection, String userId, String pass,String newPass);



    String encodePassword(String password, int level);
}
