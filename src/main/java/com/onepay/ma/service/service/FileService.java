package com.onepay.ma.service.service;

import com.onepay.ma.service.models.FileDownload;
import com.onepay.ma.service.models.FileDownloads;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/15/16.
 */
public interface FileService {
    Observable<FileDownload> insert(SQLConnection sqlBackUp, FileDownload fileDownload);
    Observable<FileDownloads> list(SQLConnection sqlBackUp, String keyword, String userId, int page);
    Observable<FileDownload> get(SQLConnection sqlBackUp, String fileHashName, String userid);
    Observable<FileDownload> updateStatus(SQLConnection sqlBackUp, String status, String fileHashName, long fileSize);
    Observable<Integer> updateExpTime(SQLConnection sqlBackUp, String fileHashName);
    Integer updateStatusDownload(Connection sqlBackUp, String status, String fileHashName, long fileSize) throws SQLException;
}
