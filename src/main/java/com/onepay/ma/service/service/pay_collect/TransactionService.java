package com.onepay.ma.service.service.pay_collect;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.pay_collect.Bank;
import com.onepay.ma.service.models.pay_collect.Transaction;
import com.onepay.ma.service.models.pay_collect.TransactionSearchReq;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;

public interface TransactionService {
    Observable<BaseList<Transaction>> search(SQLConnection sqlConnection, TransactionSearchReq searchRequest);
    Observable<Integer> total(SQLConnection onlineConn, TransactionSearchReq searchRequest);
    ResultSet download(Connection connection, TransactionSearchReq query);
    Observable<Transaction> getTransactionById(SQLConnection sqlConnection, String id);
    Observable<BaseList<Bank>> getAllBank(SQLConnection sqlConnection);
}
