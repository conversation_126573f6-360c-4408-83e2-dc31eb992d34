package com.onepay.ma.service.service.domestic;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;

import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by anhkh on 3/6/16.
 */
public class SSDomesticTransactionService {

    public static Observable<Void> updateOrder(SQLConnection connOnline, String transactionId, String status, String description) {

        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(status)
                .add(description);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR);

        return connOnline.callWithParamsObservable2(UPDATE_ORDER, inParams, outParams).map(result -> {
            Integer code = result.getOutput().getInteger(3);
            if(code != 200) {
                LOGGER.log(Level.SEVERE, "ERROR ON UPDATE ORDER APPROVAL " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            return null;
        });
    }

    public  static Observable<Transactions<SamsungDomesticTransaction>> list(SQLConnection connReadOnly, SQLConnection connOnline, DomesticTxnParameter domesticTxnParameter) {

        Transactions<SamsungDomesticTransaction> transactions = new Transactions();
        return getListTotalTransaction(connOnline, domesticTxnParameter, -1).flatMap(totalItem1 -> {
            return getListTotalTransaction(connReadOnly, domesticTxnParameter, totalItem1).flatMap(totalItem2 -> {
                transactions.setTotal_items(totalItem1 + totalItem2);
                domesticTxnParameter.setOffset(0);
                return getListTransaction(connOnline, domesticTxnParameter, -1).flatMap(transactionsListOnline -> {
                    domesticTxnParameter.setOffset(totalItem1);
                    return getListTransaction(connReadOnly, domesticTxnParameter, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                        List<SamsungDomesticTransaction> transactionsFinal = new ArrayList<>();
                        transactionsFinal.addAll(transactionsListOnline);
                        transactionsFinal.addAll(transactionsListReadOnly);
                        transactions.setTransactions(transactionsFinal);
                        return Observable.just(transactions);
                    });
                });
            });

        });
    }


    public static Observable<SamsungDomesticTransaction> get(SQLConnection sqlConnection, String transactionId, String target) {
        return getTransaction(sqlConnection, transactionId, target);
    }

    public static ResultSet download(Connection connOnline, DomesticTxnParameter parameter, int row) throws SQLException {
        return downloadData(connOnline, parameter, row);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, DomesticTxnParameter parameter, int row) throws SQLException {
        return download(connReadOnly, parameter, row);
    }

    public static ResultSet downloadOnline(Connection connOnline, DomesticTxnParameter parameter) throws SQLException {
        return download(connOnline, parameter, -1);
    }


    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, DomesticTxnParameter domesticTxnParameter) {
        return getListTotalTransaction(connOnline, domesticTxnParameter, -1).flatMap(totalItem1 -> {
            return getListTotalTransaction(connReadOnly, domesticTxnParameter, totalItem1).map(totalItem2 -> {
                return totalItem1 + totalItem2;
            });
        });
    }

    private  static Observable<List<SamsungDomesticTransaction>> getListTransaction(SQLConnection connOnline, DomesticTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget())
                .add(parameter.getPage())
                .add(parameter.getPageSize())
                .add(parameter.getOffset())
                .add(parameter.getOrder_status());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_SAMSUNG_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(21) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<SamsungDomesticTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                SamsungDomesticTransaction transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }


    /**
     * get listByMerchant transaction data total
     *
     * @param connOnline
     * @param parameter
     * @return
     */
    private  static Observable<Integer> getListTotalTransaction(SQLConnection connOnline, DomesticTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget() == null ? "" : parameter.getTarget())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getOrder_status());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_SAMSUNG_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(21) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH TOTAL DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }

    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadData(Connection connection, DomesticTxnParameter parameter, int row) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget())
                .addNull()
                .addNull()
                .addNull()
                .add(parameter.getOrder_status());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, LIST_SAMSUNG_TRANSACTION, inParams, outParams);
            if (callableStatement.getInt(22) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD DOMESTIC TRANSACTION ERROR]: " + callableStatement.getString(23));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(21);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }


    /**
     * get Samsung transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private static Observable<SamsungDomesticTransaction> getTransaction(SQLConnection sqlConnection, String transactionId, String target) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(target);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(SAMSUNG_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[GET DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            SamsungDomesticTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }

    /**
     * convert data from result set to samsung transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static SamsungDomesticTransaction bindTransaction(JsonObject rs) {

        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_NAME");
        String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String cardDate = rs.getString("S_CARD_DATE");
        String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
        String cardNumber = rs.getString("S_CARD_NUMBER");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
//        try {
//            report.setReport_date(formatter.parse(rs.getString("D_DATE")));
//        } catch (ParseException e) {
//            throw new ServerException(e);
//        }
        Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));

//        try {
//            merchantTransactionDate = new Timestamp(formatter.parse(rs.getString("D_MERCHANT_TRANSACTION_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        String orderInfo = rs.getString("S_ORDER_INFO");
        double amount = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCY_CODE");
        String status = String.valueOf(rs.getInteger("N_TRANSACTION_STATUS"));
        String refundStatus = String.valueOf(rs.getInteger("N_REFUND_TRANSACTION_STATUS"));
        String transactionInfo = rs.getString("S_TRANSACTION_INFO");
        String cardVerificationCode = String.valueOf(rs.getInteger("N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");
        String ipAddress = rs.getString("S_IP");
        double refundAmount = rs.getDouble("N_REFUND_AMOUNT");
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String orderStatus = rs.getString("S_REVIEW_STATE");
        String orderDesc = rs.getString("S_REVIEW_DESC");
        int nFraud = rs.getInteger("N_FRAUD") == null ? 0 : rs.getInteger("N_FRAUD");



        SamsungDomesticTransaction transaction = new SamsungDomesticTransaction();
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(merchantTransactionDate);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ipAddress);
        transaction.setOrder_info(orderInfo);
        transaction.setMerchant_transaction_ref(merchantTransactionRef);
        transaction.setStatus(status);
        transaction.setRefund_status(refundStatus);
        transaction.setTransaction_info(transactionInfo);
        transaction.setMerchant_id(merchantId);
        transaction.setAdvance_status(advanceStatus);
        transaction.setN_fraud(nFraud);
        transaction.setOrder_status(orderStatus);
        transaction.setOrder_desc(orderDesc);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);

        transaction.setAcquirer(acquirer);

        DomesticAmount amountData = new DomesticAmount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);
        amountData.setRefund_total(refundAmount);

        transaction.setAmount(amountData);

        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }

        transaction.setAuth_time(rs.getString("D_USER_AUTHENTICATION_DATE") == null ? null: java.sql.Timestamp.valueOf(rs.getString("D_USER_AUTHENTICATION_DATE")));

        /*
         Special case with response code
         Case status is 200 and failed, response code is 100
         the response code will be change with specific bank requirements
         */

        if (transaction.getStatus().equals("200") && transaction.getAdvance_status().equals("Failed") && cardVerificationCode.equals("100")) {

            Integer authCode = rs.getInteger("N_USER_AUTHENTICATION_CODE");
            String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                    acquirerId + StringPool.DOT + authCode, "1"); // default is 1
            card.setCard_verification_code(specialResponseCode.length() == 0 ? "1" : specialResponseCode);
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);


        // SAMSUNG
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

//        transaction.setAdvance_status(this.getAdvanceStatus(transaction.getCard().getCard_verification_code(), transaction.getStatus(), transaction.getTransaction_time()));


        return transaction;
    }




    private static final String LIST_SAMSUNG_TRANSACTION = "{call PKG_PAYMENT2.SEARCH_SAMSUNG_TRANSACTION(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";


    private static final String SAMSUNG_TRANSACTION_GET_BY_ID = "{call PKG_PAYMENT2.GET_SS_TRANSACTION_ORDER_v2(?,?,?,?,?) }";
    private static final String UPDATE_ORDER = "{call PKG_PAYMENT2.UPDATE_ORDER_STATUS(?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(SSDomesticTransactionService.class.getName());


}
