package com.onepay.ma.service.service.domestic;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.util.PropsUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import rx.Observable;
import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 3/6/16.
 */
public class DomesticTransactionService {

    public static Observable<Transactions<DomesticTransaction>> list(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection sqlBackup, DomesticTxnParameter domesticTxnParameter) {
        // cut off time
        // Date now = new Date();
        // Date fromDate = DateTimeUtil.convertStringtoDate(domesticTxnParameter.getFromDate(),
        // DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss);
        // Date toDate = DateTimeUtil.convertStringtoDate(domesticTxnParameter.getToDate(),
        // DateTimeUtil.DateTemplate.DD_MM_YYYY_HH_mm_ss);
        // Date cutOff = DateTimeUtil.subtractMinutesToDate(30, now);
        int basePageSize = domesticTxnParameter.getPageSize();

        Transactions<DomesticTransaction> transactions = new Transactions();
        return getListTotalTransRefundBackup(sqlBackup, domesticTxnParameter).flatMap(totalApproval -> {
            LOGGER.log(Level.INFO, "------------------START GET TOTAL Domestic TRansaction List By Ids----------------- ");
            return DomesticTransactionService.transMapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), domesticTxnParameter).flatMap(totalPurchaseMap -> {
                LOGGER.log(Level.INFO, "DATA GET TOTAL Domestic Transaction List By Ids: " + totalPurchaseMap.size());
                return getListTotalTransaction(connOnline, domesticTxnParameter, -1).flatMap(totalItem1 -> {
                    return getListTotalTransaction(connReadOnly, domesticTxnParameter, totalItem1).flatMap(totalItem2 -> {
                        Integer totalBackup = countFilterById(totalPurchaseMap, totalApproval);
                        transactions.setTotal_items(totalItem1 + totalItem2 + totalBackup);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_ONLINE: " + totalItem1);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_READONLY: " + totalItem2);
                        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_BACKUP: " + totalBackup);

                        return getListDomesticRefundBackup(sqlBackup, domesticTxnParameter).flatMap(refundApproval -> {
                            return DomesticTransactionService.transMapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), domesticTxnParameter).flatMap(purchaseMap -> {
                                List<DomesticTransaction> refundApproval2 = refundApproval.stream()
                                        .map(approval -> {
                                            return joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_id()));
                                        })
                                        .filter(domesticRefund -> domesticRefund != null)
                                        .collect(Collectors.toList());
                                domesticTxnParameter.setOffset(totalBackup);
                                return getListTransaction(connOnline, domesticTxnParameter, -1).flatMap(transactionsListOnline -> {
                                    domesticTxnParameter.setOffset(totalBackup + totalItem1);
                                    return getListTransaction(connReadOnly, domesticTxnParameter, transactionsListOnline.size()).flatMap(transactionsListReadOnly -> {
                                        List<DomesticTransaction> transactionsFinal = new ArrayList<>();
                                        transactionsFinal.addAll(refundApproval2);
                                        transactionsFinal.addAll(transactionsListOnline);
                                        transactionsFinal.addAll(transactionsListReadOnly);
                                        LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_BACKUP: " + refundApproval2.size());
                                        LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_ONLINE: " + transactionsListOnline.size());
                                        LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_READONLY: " + transactionsListReadOnly.size());

                                        int index = 1;
                                        for (DomesticTransaction transaction : transactionsFinal) {
                                            transaction.setRow_num(index + (domesticTxnParameter.getPage() * basePageSize));
                                            index++;
                                        }
                                        transactions.setTransactions(transactionsFinal);
                                        return Observable.just(transactions);
                                    });
                                });
                            });
                        });
                    });

                });
            });

        });

    }

    private static Integer countFilterById(Map<Integer, DomesticTransaction> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    private static DomesticTransaction joinApproveWithPurchase(DomesticTransaction approve, DomesticTransaction purchase) {

        if (purchase == null) {
            return null;
        }

        DomesticTransaction result = approve;
        result.setOrder_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());

        result.setCard(purchase.getCard());
        result.getAmount().setRefund_total(purchase.getAmount().getTotal());
        result.setIp_address(purchase.getIp_address());

        return result;
    }

    public static Observable<List<DomesticTransactionHistory>> listHistory(SQLConnection connReadOnly, SQLConnection connOnline, SQLConnection connBackup, String transactionId) {
        // DomesticTransactionHistories transactions = new DomesticTransactionHistories();

        return getListTransactionHistoryOnline(connOnline, transactionId).flatMap(domesticTransactions -> {
            // transactions.setTotal_items(domesticTransactions.size());
            return getListTransactionHistoryReadOnly(connReadOnly, transactionId).flatMap(domesticTransactionsReadOnly -> {

                return getListApprovalHistoryBackup(connBackup, transactionId).flatMap(approvalHistories -> {


                    List<DomesticTransactionHistory> transactionsFinal = new ArrayList<>();
                    // transactionsFinal.addAll(approvalHistories);
                    transactionsFinal.addAll(domesticTransactions);
                    transactionsFinal.addAll(domesticTransactionsReadOnly);
                    transactionsFinal.addAll(approvalHistories);

                    if (transactionsFinal.size() > 0) {
                        return getTransaction(connOnline, String.valueOf(transactionsFinal.get(0).getOriginal_id())).flatMap(originalTransaction -> {
                            List<DomesticTransactionHistory> newTransactionsFinal = new ArrayList<>();
                            /**
                             * Đối với các đơn vị chạy thẻ nội địa VCB qua OnePAY,
                             * các GD refund nội địa chỉ hiển thị 01 GD thành công cuối cùng
                             */
                            // if (originalTransaction.getAcquirer().getAcquirer_name().equalsIgnoreCase("Vietcombank")) {
                            // for (DomesticTransactionHistory trans : transactionsFinal) {
                            // if (trans.getSubHistories() == null || trans.getSubHistories().size() <= 1) {
                            // /**
                            // * Transaction request refund sẽ có trans ref. là id của trans refund
                            // * Kiểm tra nếu trans ref. khác <blank>, tìm xem đã có trans refund hay chưa
                            // * Nếu tìm thấy trans refund, sẽ không add transaction request refund vào list
                            // * Nếu không tìm thấy trans refund, có nghĩa là transaction request refund chưa được xử lý. thêm
                            // nó vào list
                            // */
                            // if (trans.getMerchant_transaction_ref().isEmpty()) {
                            // newTransactionsFinal.add(trans);
                            // } else {
                            // DomesticTransactionHistory foundTransaction = null;
                            // for (DomesticTransactionHistory t : transactionsFinal) {
                            // if (trans.getMerchant_transaction_ref().equalsIgnoreCase(String.valueOf(t.getTransaction_id())))
                            // {
                            // foundTransaction = t;
                            // break;
                            // }
                            // }
                            // if (foundTransaction == null) {
                            // newTransactionsFinal.add(trans);
                            // }
                            // }
                            //
                            // } else {
                            // newTransactionsFinal.add(trans.getSubHistories().get(0));
                            // }
                            // }
                            // } else {
                            newTransactionsFinal = transactionsFinal;
                            // }

                            // sort by trans time.
                            newTransactionsFinal
                                    .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

                            return Observable.just(newTransactionsFinal);
                        });
                    } else {
                        return Observable.just(transactionsFinal);
                    }

                });
            });

        });
    }

    public static Observable<Integer> getTotalDownload(SQLConnection connOnline, SQLConnection connReadOnly, SQLConnection connReadBackup, DomesticTxnParameter domesticTxnParameter) {
        return getListTotalTransRefundBackup(connReadBackup, domesticTxnParameter).flatMap(totalApproval -> {
            return DomesticTransactionService.transMapByIds(connOnline, String.join(StringPool.COMMA, totalApproval), domesticTxnParameter).flatMap(totalPurchaseMap -> {
                return getListTotalTransaction(connOnline, domesticTxnParameter, -1).flatMap(totalItem1 -> {
                    return getListTotalTransaction(connReadOnly, domesticTxnParameter, totalItem1).map(totalItem2 -> {
                        return totalItem1 + totalItem2 + countFilterById(totalPurchaseMap, totalApproval);
                    });
                });
            });
        });
    }

    public static Observable<DomesticTransaction> get(SQLConnection sqlConnection, String transactionId) {
        return getTransaction(sqlConnection, transactionId);
    }

    public static Observable<DomesticTransaction> getDetail(SQLConnection sqlConnection, String transactionId, String target) {
        return getTransaction2(sqlConnection, transactionId, target);
    }

    public static ResultSet downloadBackup(Connection connBackup, DomesticTxnParameter parameter) throws SQLException {
        return downloadBackupData(connBackup, parameter);
    }

    public static ResultSet downloadReadonly(Connection connReadOnly, DomesticTxnParameter parameter, int row) throws SQLException {
        return download(connReadOnly, parameter, row);
    }

    public static ResultSet downloadOnline(Connection connOnline, DomesticTxnParameter parameter) throws SQLException {
        return download(connOnline, parameter, -1);
    }

    public static ResultSet download(Connection connOnline, DomesticTxnParameter parameter, int row) throws SQLException {
        return downloadData(connOnline, parameter, row);
    }

    /**
     * file data from readonly database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadBackupData(Connection connection, DomesticTxnParameter parameter) throws SQLException {
        String status = "2";
        if (null == parameter.getStatus() || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getStatus())) {
            status = "401";
        }

        JsonArray inParams = new JsonArray()
            .add(parameter.getKeywords())
            .add(parameter.getMerchantId())
            .add(parameter.getBankId())
            .add(parameter.getTransactionId())
            .add(parameter.getFromDate())
            .add(parameter.getToDate())
            .add(parameter.getMerchantTransactionRef())
            .add(status)
            .add(RefundData.Type.DOMESTIC.getValue())
            .add(parameter.getTransactionType()==null?"":parameter.getTransactionType());
        JsonArray outParams = new JsonArray()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .addNull()
            .add(OracleTypes.CURSOR)
            .add(OracleTypes.INTEGER)
        .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL ]: PKG_REFUND_APPROVE.get_refund_download, IN: " + inParams + ", OUT: " + outParams);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, DOWNLOAD_TRANSACTION_BACKUP, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(11);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;
    }

    public static ResultSet downloadOnlineByIds(Connection connOnline, String transIds, DomesticRefundParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardNumber())
                .add(parameter.getFromDate())
                .add(parameter.getToDate());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.WARNING, "[CALL FOR DOWNLOAD]: PKG_PAYMENT2.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        ResultSet rs;
        CallableStatement callableStatement = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connOnline, LIST_TRANSACTION_ONLINE_BY_IDS, inParams, outParams);

            if (callableStatement.getInt(9) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS DOMESTIC TRANSACTION ONLINE ERROR]: " + callableStatement.getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) callableStatement.getObject(8);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }

        return rs;
    }

    public static Observable<List<DomesticTransaction>> listByIds(SQLConnection connOnline, String transIds, DomesticRefundParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getAcquirerId())
                .add(parameter.getCardNumber())
                .add(parameter.getFromDate())
                .add(parameter.getToDate());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.WARNING, "[CALL ]: PKG_PAYMENT2.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_ONLINE_BY_IDS, inParams, outParams).map(resultSet -> {
            List<DomesticTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(8) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS DOMESTIC TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransaction transaction = bindTransaction(jsonObject);
                result.add(transaction);
            }

            return result;
        });
    }


    public static Observable<List<DomesticTransaction>> transListByIds(SQLConnection connOnline, String transIds, DomesticTxnParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getBankId())
                .add(parameter.getCardNumber())
                .add(parameter.getFromDate())
                .add(parameter.getToDate());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        // LOGGER.log(Level.WARNING, "[CALL ]: PKG_PAYMENT2.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);
        return connOnline.callWithParamsObservable2(LIST_TRANS_DOMESTIC_ONLINE_BY_IDS, inParams, outParams).map(resultSet -> {
            List<DomesticTransaction> result = new ArrayList<>();
            if (resultSet.getOutput().getInteger(8) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST BY IDS DOMESTIC TRANSACTION ONLINE ERROR]: " + resultSet.getOutput().getString(9));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }


            Map map = resultSet.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransaction transaction = bindTransaction(jsonObject);
                transaction.setOriginal_id(jsonObject.getInteger("N_ORIGINAL_ID"));
                result.add(transaction);
            }

            return result;
        });
    }

    /**
     * get transaction data online database
     *
     * @param connBackup
     * @param parameter
     * @return
     */

    private static Observable<List<DomesticTransaction>> getListDomesticRefundBackup(SQLConnection connBackup, DomesticTxnParameter parameter) {
        String status = "2";
        if (null == parameter.getStatus() || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getStatus())) {
            status = "401";
        }

        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(status)
                .add("")
                .add(null != parameter.getTransactionType() ? parameter.getTransactionType() : "")
                .add(RefundData.Type.DOMESTIC.getValue())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LISTS_DOMESTIC_APPROVAL, inParams, outParams).map(result -> {
            List<DomesticTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(13).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransaction transaction = bindApproval(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticTransaction bindApproval(JsonObject rs) {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        int originalId = Integer.valueOf(rs.getString("N_ORIGINAL_ID"));
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInteger("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");


        DomesticTransaction transaction = new DomesticTransaction();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(String.valueOf(status));
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId); // setID giao dich goc (originalId)
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setOperator_id(operatorId);
        DomesticAmount refundAmount = new DomesticAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);

        transaction.setAmount(refundAmount);
        return transaction;
    }

    private static Observable<List<DomesticTransaction>> getListTransaction(SQLConnection connOnline, DomesticTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget())
                .add(parameter.getTransactionType() == null ? "" : parameter.getTransactionType())
                .add(parameter.getPage())
                .add(parameter.getPageSize())
                .add(parameter.getOffset());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(21) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<DomesticTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransaction transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * get transaction data total
     *
     * @param connBackup
     * @param parameter
     * @return
     */
    private static Observable<List<String>> getListTotalTransRefundBackup(SQLConnection connBackup, DomesticTxnParameter parameter) {
        String status = "2";
        if (null == parameter.getStatus() || parameter.getStatus().isEmpty()) {
            status = "";
        } else if ("Waiting for approval".equalsIgnoreCase(parameter.getStatus())) {
            status = "401";
        }

        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(status)
                .add("")
                .add(null != parameter.getTransactionType() ? parameter.getTransactionType() : "")
                .add(RefundData.Type.DOMESTIC.getValue());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connBackup.callWithParamsObservable2(LISTS_DOMESTIC_APPROVAL_TOTAL, inParams, outParams).map(resultSet -> {

            Integer resultCode = resultSet.getOutput().getInteger(12);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ERROR] GET TOTAL DOMESTIC REFUND ERROR : " + resultSet.getOutput().getString(13));
                throw IErrors.DOMESTIC_SERVER_ERROR;
            }

            List<String> result = new ArrayList<>();
            Map map = resultSet.getOutput().getJsonObject(11).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                result.add(jsonObject.getString("N_ORIGINAL_ID"));
            }
            return result;
        });
    }

    /**
     * get listByMerchant transaction data total
     *
     * @param connOnline
     * @param parameter
     * @return
     */
    private static Observable<Integer> getListTotalTransaction(SQLConnection connOnline, DomesticTxnParameter parameter, int row) {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget() == null ? "" : parameter.getTarget())
                .add(parameter.getTransactionType() == null ? "" : parameter.getTransactionType())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(21) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH TOTAL DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(22));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            Map map = result.getOutput().getJsonObject(20).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }


    /**
     * file ddata from online database
     *
     * @param connection
     * @param parameter
     * @return
     */
    private static ResultSet downloadData(Connection connection, DomesticTxnParameter parameter, int row) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(row)
                .add(QueryMethod.DOWNLOAD.toString())
                .add(parameter.getMerchantId())
                .add(parameter.getBankId())
                .add(parameter.getTransactionId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getMerchantTransactionRef())
                .add(parameter.getOrderInfo())
                .add(parameter.getCardNumber())
                .add(parameter.getStatus())
                .add(parameter.getCustomer_mobile())
                .add(parameter.getCustomer_email())
                .add(parameter.getMerchant_website())
                .add(parameter.getFraud_check())
                .add(parameter.getTarget())
                .add(parameter.getTransactionType() == null ? "" : parameter.getTransactionType())
                .addNull()
                .addNull()
                .addNull();
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {

            callableStatement = ExportDatasourceUtil.execute(connection, LIST_TRANSACTION, inParams, outParams);
            if (callableStatement.getInt(22) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD DOMESTIC TRANSACTION ERROR]: " + callableStatement.getString(23));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(21);
        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }


    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connReadOnly
     * @param transactionId
     * @return
     */
    private static Observable<List<DomesticTransactionHistory>> getListTransactionHistoryReadOnly(SQLConnection connReadOnly, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connReadOnly.callWithParamsObservable2(LIST_TRANSACTION_HISTORY_READ_ONLY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[HISTORY DOMESTIC TRANSACTION READONLY ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<DomesticTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connOnline
     * @param transactionId
     * @return
     */
    private static Observable<List<DomesticTransactionHistory>> getListTransactionHistoryOnline(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_TRANSACTION_HISTORY_ONLINE, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[HISTORY DOMESTIC TRANSACTION ONLINE ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<DomesticTransactionHistory> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransactionHistory transaction = bindTransactionHistory(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * get listByMerchant transaction history data read only database
     *
     * @param connOnline
     * @param transactionId
     * @return
     */
    private static Observable<List<DomesticTransactionHistory>> getListApprovalHistoryBackup(SQLConnection connOnline, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(0);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connOnline.callWithParamsObservable2(LIST_REFUND_APPROVAL_HISTORY, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[LIST DOMESTIC APPROVAL HISTORY ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            List<DomesticTransactionHistory> childList = new ArrayList<>();
            List<DomesticTransactionHistory> parentList = new ArrayList<>();

            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                DomesticTransactionHistory transaction = bindApprovalHistory(jsonObject);
                parentList.add(transaction);
                if (transaction.getParent_id() != null) {
                    childList.add(transaction);
                }
            }

            Map<Integer, List<DomesticTransactionHistory>> childMap = childList.stream()
                    .collect(Collectors.groupingBy(DomesticTransactionHistory::getParent_id));

            return parentList.stream().map(parent -> {
                DomesticTransactionHistory re = new DomesticTransactionHistory(parent);

                if (childMap.get(parent.getTransaction_id()) != null) {
                    re.setSubHistories(childMap.get(parent.getTransaction_id()));
                } else {
                    re.setSubHistories(new ArrayList<>());
                }
                re.getSubHistories().add(parent);
                re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                return re;
            })
                    .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                    .collect(Collectors.toList());
        });
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @return
     */
    private static Observable<DomesticTransaction> getTransaction(SQLConnection sqlConnection, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(2) != 200) {
                LOGGER.log(Level.SEVERE, "[GET DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            DomesticTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }

    /**
     * get transaction data
     *
     * @param sqlConnection
     * @param transactionId
     * @param target
     * @return
     */
    private static Observable<DomesticTransaction> getTransaction2(SQLConnection sqlConnection, String transactionId, String target) {
        JsonArray inParams = new JsonArray()
                .add(transactionId)
                .add(target);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnection.callWithParamsObservable2(TRANSACTION_GET_BY_ID_v2, inParams, outParams).map(result -> {

            if (result.getOutput().getInteger(3) != 200) {
                LOGGER.log(Level.SEVERE, "[GET DOMESTIC TRANSACTION ERROR]: " + result.getOutput().getString(4));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            DomesticTransaction transaction = null;
            Map map = result.getOutput().getJsonObject(2).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() <= 0)
                return null;
            JsonObject jsonObject = rs.getRows().get(0);
            transaction = bindTransaction(jsonObject);


            return transaction;
        });
    }


    public static Observable<Map<Integer, DomesticTransaction>> mapByIds(SQLConnection connOnline, String transIds, DomesticRefundParameter parameter) {
        return listByIds(connOnline, transIds, parameter).map(domesticTransactions -> {
            Map<Integer, DomesticTransaction> map = new HashMap<>();
            for (DomesticTransaction dt : domesticTransactions) {
                map.put(dt.getTransaction_id(), dt);
            }
            return map;
        });
    }


    public static Map<Integer, DomesticTransaction> transMapByIdsDownload(Connection connOnline, String transIds, DomesticTxnParameter parameter) {
        List<DomesticTransaction> domesticTransactions = transListByIdsDownload(connOnline, transIds, parameter);
        Map<Integer, DomesticTransaction> map = new HashMap<>();
        for (DomesticTransaction dt : domesticTransactions) {
            map.put(dt.getTransaction_id(), dt);
        }
        return map;
    }

    public static List<DomesticTransaction> transListByIdsDownload(Connection connOnline, String transIds, DomesticTxnParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(transIds)
                .add(parameter.getOrderInfo())
                .add(parameter.getBankId())
                .add(parameter.getCardNumber())
                .add(parameter.getFromDate())
                .add(parameter.getToDate());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        // LOGGER.log(Level.INFO, "[CALL ]: PKG_PAYMENT2.GET_TRANSACTION_BY_IDS, IN: " + inParams + ", OUT: " + outParams);

        ResultSet rs;
        CallableStatement callableStatement = null;
        List<DomesticTransaction> result = new ArrayList<>();
        try {

            callableStatement = ExportDatasourceUtil.execute(connOnline, LIST_TRANS_DOMESTIC_ONLINE_BY_IDS, inParams, outParams);
            if (callableStatement.getInt(9) != 200) {
                LOGGER.log(Level.SEVERE, "[DOWNLOAD BY IDS DOMESTIC TRANSACTION ONLINE ERROR]: " + callableStatement.getString(10));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) callableStatement.getObject(8);
            while (rs.next()) {
                int transactionId = rs.getInt("N_TRANSACTION_ID");
                int acquirerId = rs.getInt("N_ACQUIRER_ID");
                String acquirerName = rs.getString("S_ACQUIRER_NAME");
                String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");
                String merchantId = rs.getString("S_MERCHANT_ID");
                String cardDate = rs.getString("S_CARD_DATE");
                String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
                String cardNumber = rs.getString("S_CARD_NUMBER");
                String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
                Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
                String orderInfo = rs.getString("S_ORDER_INFO");
                double amount = rs.getDouble("N_AMOUNT");
                String currency = rs.getString("S_CURRENCY_CODE");
                String status = rs.getString("N_TRANSACTION_STATUS");
                String refundStatus = rs.getString("N_REFUND_TRANSACTION_STATUS");
                String cardVerificationCode = rs.getString("N_CARD_VERIFICATION_CODE");
                String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");
                String ipAddress = rs.getString("S_IP");
                double refundAmount = rs.getDouble("N_REFUND_AMOUNT");
                String advanceStatus = rs.getString("S_ADVANCE_STATUS");
               
                DomesticTransaction transaction = new DomesticTransaction();
                transaction.setTransaction_id(transactionId);
                transaction.setTransaction_time(merchantTransactionDate);
                transaction.setIp_address(ipAddress);
                transaction.setOrder_info(orderInfo);
                transaction.setMerchant_transaction_ref(merchantTransactionRef);
                transaction.setStatus(status);
                transaction.setRefund_status(refundStatus);
                transaction.setMerchant_id(merchantId);
                transaction.setAdvance_status(advanceStatus);
                transaction.setAuth_time(rs.getString("D_USER_AUTHENTICATION_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_USER_AUTHENTICATION_DATE")));
                transaction.setAuth_code(rs.getInt("N_USER_AUTHENTICATION_CODE"));
                Acquirer acquirer = new Acquirer();
                acquirer.setAcquirer_id(acquirerId);
                acquirer.setAcquirer_name(acquirerName);
                acquirer.setAcquirer_short_name(acquirerShortName);

                transaction.setAcquirer(acquirer);

                DomesticAmount amountData = new DomesticAmount();
                amountData.setCurrency(currency);
                amountData.setTotal(amount);
                amountData.setRefund_total(refundAmount);

                transaction.setAmount(amountData);

                DomesticCard card = new DomesticCard();
                card.setCard_number(cardNumber);
                card.setCard_verification_code(cardVerificationCode);
                card.setCard_holder_name(cardHolderName);
                card.setCard_verification_info(cardVerificationInfo);

                CardDate cardDateData = new CardDate();
                if (cardDate != null && cardDate.length() == 4) {
                    cardDateData.setMonth(cardDate.substring(0, 2));
                    cardDateData.setYear(cardDate.substring(2, 4));
                }

                if (transaction.getStatus().equals("200") && transaction.getAdvance_status().equals("Failed") && cardVerificationCode.equals("100")) {

                    Integer authCode = rs.getInt("N_USER_AUTHENTICATION_CODE");
                    String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                            acquirerId + StringPool.DOT + authCode, "1"); // default is 1
                    card.setCard_verification_code(specialResponseCode.length() == 0 ? "1" : specialResponseCode);
                }
                card.setCard_date(cardDateData);
                transaction.setCard(card);
                result.add(transaction);
            }
            return result;

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }


    }

    public static Observable<Map<Integer, DomesticTransaction>> transMapByIds(SQLConnection connOnline, String transIds, DomesticTxnParameter parameter) {
        return transListByIds(connOnline, transIds, parameter).map(domesticTransactions -> {
            Map<Integer, DomesticTransaction> map = new HashMap<>();
            for (DomesticTransaction dt : domesticTransactions) {
                map.put(dt.getTransaction_id(), dt);
            }
            return map;
        });
    }
    

    public static Map<Integer, DomesticTransaction> mapByIdsDownload(Connection connOnline, String transIds, DomesticRefundParameter parameter) throws SQLException {
        ResultSet rs = downloadOnlineByIds(connOnline, transIds, parameter);
        HashMap<Integer, DomesticTransaction> map = new HashMap();
        while (rs.next()) {

            int acquirerId = rs.getInt("N_ACQUIRER_ID");
            String acquirerName = rs.getString("S_ACQUIRER_NAME");
            String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");
            String orderInfo = rs.getString("S_ORDER_INFO");
            double amount = rs.getDouble("N_AMOUNT");
            String currency = rs.getString("S_CURRENCY_CODE");
            Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));

            DomesticTransaction transaction = new DomesticTransaction();

            String cardVerificationCode = rs.getString("N_CARD_VERIFICATION_CODE");
            String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");
            String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
            DomesticCard card = new DomesticCard();
            card.setCard_number(rs.getString("S_CARD_NUMBER"));
            card.setCard_verification_code(cardVerificationCode);
            card.setCard_holder_name(cardHolderName);
            card.setCard_verification_info(cardVerificationInfo);
            Acquirer acquirer = new Acquirer();
            acquirer.setAcquirer_id(acquirerId);
            acquirer.setAcquirer_name(acquirerName);
            acquirer.setAcquirer_short_name(acquirerShortName);

            DomesticAmount amountData = new DomesticAmount();
            amountData.setCurrency(currency);
            amountData.setTotal(amount);

            transaction.setAcquirer(acquirer);
            transaction.setOrder_info(orderInfo);
            transaction.setAmount(amountData);
            transaction.setTransaction_time(merchantTransactionDate);
            transaction.setCard(card);

            map.put(rs.getInt("N_TRANSACTION_ID"), transaction);
        }
        return map;

    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticTransaction bindTransaction(JsonObject rs) {

        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        int acquirerId = rs.getInteger("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_NAME");
        String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String cardDate = rs.getString("S_CARD_DATE");
        String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
        String cardNumber = rs.getString("S_CARD_NUMBER");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String mspMerchantTransactionRef = rs.getString("S_MSP_TRANSACTION_REF");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        // try {
        // report.setReport_date(formatter.parse(rs.getString("D_DATE")));
        // } catch (ParseException e) {
        // throw new ServerException(e);
        // }
        Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));

        // try {
        // merchantTransactionDate = new
        // Timestamp(formatter.parse(rs.getString("D_MERCHANT_TRANSACTION_DATE")).getTime());
        // } catch (ParseException e) {
        // LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
        // }
        String orderInfo = rs.getString("S_ORDER_INFO");
        double amount = rs.getDouble("N_AMOUNT");
        String currency = rs.getString("S_CURRENCY_CODE");
        String status = String.valueOf(rs.getInteger("N_TRANSACTION_STATUS"));
        String refundStatus = String.valueOf(rs.getInteger("N_REFUND_TRANSACTION_STATUS"));
        String transactionInfo = rs.getString("S_TRANSACTION_INFO");
        String cardVerificationCode = String.valueOf(rs.getInteger("N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");
        String ipAddress = rs.getString("S_IP");
        double refundAmount = rs.getDouble("N_REFUND_AMOUNT");
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        DomesticTransaction transaction = new DomesticTransaction();
        transaction.setTransaction_id(transactionId);
        transaction.setOriginal_id(rs.getInteger("N_ORIGINAL_ID"));
        transaction.setTransaction_time(merchantTransactionDate);
        transaction.setTransaction_type(rs.getString("S_TRANSACTION_TYPE"));
        transaction.setIp_address(ipAddress);
        transaction.setOrder_info(orderInfo);
        transaction.setMerchant_transaction_ref(merchantTransactionRef);
        transaction.setMsp_merchant_transaction_ref(mspMerchantTransactionRef);
        transaction.setStatus(status);
        transaction.setRefund_status(refundStatus);
        transaction.setTransaction_info(transactionInfo);
        transaction.setMerchant_id(merchantId);
        transaction.setAdvance_status(advanceStatus);
        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);

        transaction.setAcquirer(acquirer);

        DomesticAmount amountData = new DomesticAmount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);
        amountData.setRefund_total(refundAmount);

        transaction.setAmount(amountData);

        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }

        transaction.setAuth_time(rs.getString("D_USER_AUTHENTICATION_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_USER_AUTHENTICATION_DATE")));

        /*
         Special case with response code
         Case status is 200 and failed, response code is 100
         the response code will be change with specific bank requirements
         */

        if (transaction.getStatus().equals("200") && transaction.getAdvance_status().equals("Failed") && cardVerificationCode.equals("100")) {

            Integer authCode = rs.getInteger("N_USER_AUTHENTICATION_CODE");
            String specialResponseCode = PropsUtil.get("txncode" + StringPool.DOT +
                    acquirerId + StringPool.DOT + authCode, "1"); // default is 1
            card.setCard_verification_code(specialResponseCode.length() == 0 ? "1" : specialResponseCode);
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);


        // SAMSUNG
        transaction.setCustomer_name(rs.getString("S_CUSTOMER_NAME"));
        transaction.setEpp(rs.getString("S_EPP"));
        transaction.setCustomer_email(rs.getString("S_CUSTOMER_EMAIL"));
        transaction.setCustomer_mobile(rs.getString("S_CUSTOMER_PHONE"));
        transaction.setFraud_check(rs.getString("S_FRAUD"));

        
        //QUICK LINK
        String rawData = rs.getString("S_RAW_DATA");
        if(rawData != null) {
            JsonObject rawDataJ;
            try {
                rawDataJ = new JsonObject(new String(Base64.getDecoder().decode(rawData),"UTF-8"));
                transaction.setCustomer_address(rawDataJ.getString("vpc_SHIP_Street01"));
            } catch (UnsupportedEncodingException e) {
                LOGGER.log(Level.SEVERE, "error");
               throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }


//        transaction.setAdvance_status(this.getAdvanceStatus(transaction.getCard().getCard_verification_code(), transaction.getStatus(), transaction.getTransaction_time()));


        return transaction;
    }

    /**
     * convert data from result set to transaction history
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticTransactionHistory bindTransactionHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_TRANSACTION_ID");
        int originalId = rs.getInteger("N_ORIGINAL_ID");
        String currency = rs.getString("S_CURRENCY_CODE");
        String status = String.valueOf(rs.getInteger("N_TRANSACTION_STATUS"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");
        String operator = rs.getString("S_OPERATOR_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        // Timestamp date = Timestamp.valueOf(rs.getString("D_LOG_DATE"));
        Timestamp date = Timestamp.valueOf(rs.getString("D_LOG_DATE"));

        DomesticTransactionHistory domesticTransactionHistory = new DomesticTransactionHistory();
        domesticTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        domesticTransactionHistory.setOperator_id(operator);
        domesticTransactionHistory.setTransaction_id(transactionId);
        domesticTransactionHistory.setTransaction_type(transactionType);
        domesticTransactionHistory.setTransaction_time(date);
        domesticTransactionHistory.setOriginal_id(originalId);
        domesticTransactionHistory.setAdvanced_status(advanceStatus);

        if (domesticTransactionHistory.getTransaction_type().equals("Purchase")) {
            domesticTransactionHistory.setStatus(advanceStatus);
        } else {
            domesticTransactionHistory.setStatus(status);
        }
        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        domesticTransactionHistory.setAmount(amountData);
        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        domesticTransactionHistory.setNote(data.getString("note"));

        return domesticTransactionHistory;
    }

    /**
     * convert data from result set to transaction history
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticTransactionHistory bindApprovalHistory(JsonObject rs) {

        int transactionId = rs.getInteger("N_ID");
        int originalId = Integer.valueOf(rs.getString("N_TRANS_REF_ID"));
        String currency = rs.getString("S_CURRENCY");
        String status = String.valueOf(rs.getInteger("N_STATUS"));
        String operator = rs.getString("S_OPERATOR_ID");
        double amount = rs.getDouble("N_AMOUNT");
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String merchantTransactionRef = rs.getString("S_MERCHANT_TRANS_REF");

        Integer parent_id = rs.getInteger("N_PARENT_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(rs.getString("D_CREATE"));
        String advanceStatus = rs.getString("S_ADVANCE_STATUS");

        DomesticTransactionHistory domesticTransactionHistory = new DomesticTransactionHistory();
        domesticTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        domesticTransactionHistory.setStatus(status);
        domesticTransactionHistory.setOperator_id(operator);
        domesticTransactionHistory.setTransaction_id(transactionId);
        domesticTransactionHistory.setTransaction_type(transactionType);
        domesticTransactionHistory.setTransaction_time(date);
        domesticTransactionHistory.setOriginal_id(originalId);
        domesticTransactionHistory.setParent_id(parent_id);
        domesticTransactionHistory.setAdvanced_status(advanceStatus != null ? advanceStatus : StringPool.BLANK);

        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);

        domesticTransactionHistory.setAmount(amountData);
        JsonObject data = rs.getString("S_DATA") == null ? new JsonObject() : new JsonObject(rs.getString("S_DATA"));
        domesticTransactionHistory.setNote(data.getString("note"));

        return domesticTransactionHistory;
    }


    private static final String LIST_TRANSACTION = "{call PKG_PAYMENT2.SEARCH_TRANSACTION_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY_READ_ONLY = "{call PKG_PAYMENT2.GET_TRANSACTION_HIS_113(?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY_ONLINE = "{call PKG_PAYMENT2.GET_TRANSACTION_HIS_111(?,?,?,?)}";

    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_REFUND_APPROVE.get_approval_history_2(?,?,?,?,?)}";

    private static final String LISTS_DOMESTIC_APPROVAL_TOTAL = "{call PKG_REFUND_APPROVE.get_refund_total_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LISTS_DOMESTIC_APPROVAL = "{call PKG_REFUND_APPROVE.get_refund_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DOWNLOAD_TRANSACTION_BACKUP = "{call PKG_REFUND_APPROVE.get_refund_download_2(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANS_DOMESTIC_ONLINE_BY_IDS = "{call PKG_PAYMENT2.get_trans_domestic_by_ids(?,?,?,?,?,?,?,?,?,?)}";
    
    private static final String TRANSACTION_GET_BY_ID = "{call PKG_PAYMENT2.GET_TRANSACTION_ORDER(?,?,?,?) }";
    private static final String TRANSACTION_GET_BY_ID_v2 = "{call PKG_PAYMENT2.get_transaction_order_v2(?,?,?,?,?) }";

    private static final String LIST_TRANSACTION_ONLINE_BY_IDS = "{call PKG_PAYMENT2.GET_TRANSACTION_BY_IDS(?,?,?,?,?,?,?,?,?,?)}";
    // private static final String LIST_TRANSACTION_TOTAL_ONLINE_BY_IDS = "{call
    // PKG_PAYMENT2.GET_TRANSACTION_TOTAL_BY_IDS(?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionService.class.getName());


}
