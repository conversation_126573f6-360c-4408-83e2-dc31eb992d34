package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.PromotionTransactionService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;
import com.onepay.ma.service.models.base.QueryMethod;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huyng<PERSON><PERSON> on 4/4/16.
 */
@Service
public class PromotionTransactionServiceImpl implements PromotionTransactionService {
    @Override
    public Observable<Transactions<PromotionTransaction>> list(SQLConnection sqlConnPr, PromotionTransactionParameter promotionTransactionParameter) {

        return getTotal(sqlConnPr, promotionTransactionParameter).flatMap(integer -> {
            Transactions<PromotionTransaction> transactions = new Transactions<>();
            transactions.setTotal_items(integer);
           return getLisTransaction(sqlConnPr, promotionTransactionParameter).map(promotionTransactions -> {
               transactions.setTransactions(promotionTransactions);
               return transactions;
           });
        });
    }

    @Override
    public Observable<PromotionTransaction> get(SQLConnection sqlConnPr, String transactionId) {
        return getTransaction(sqlConnPr, transactionId);
    }

    @Override
    public Observable<Integer> revert(SQLConnection sqlConnPr, String transactionId) {
        return revertTransaction(sqlConnPr, transactionId);
    }

    @Override
    public Observable<Integer> getTotalDownload(SQLConnection sqlConnPr, PromotionTransactionParameter parameter) {
        return getTotal(sqlConnPr, parameter);
    }

    @Override
    public ResultSet download(Connection connPr, PromotionTransactionParameter parameter) throws SQLException {
        return downloadData(connPr, parameter);
    }

    /**
     * get total rows promotion transaction
     * @param connPr
     * @param parameter
     * @return
     */
    private Observable<Integer> getTotal(SQLConnection connPr, PromotionTransactionParameter parameter) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getKeywords())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionId())
                .add(parameter.getPromotionSid())
                .add(parameter.getPromotion_code())
                .add(parameter.getMerchantId())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getOrderInfo())
                .add(parameter.getCurrency())
                .add(parameter.getTransactionReference())
                .add(parameter.getPaygate())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return connPr.callWithParamsObservable2(PR_TRANSACTION_LIST, inParams, outParams).map(result -> {

            Map map = result.getOutput().getJsonObject(0).getMap();
            if (result.getOutput().getInteger(1) != 200) {
                LOGGER.log(Level.SEVERE, "[SEARCH TOTAL PROMOTION ERROR]: " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Integer total = 0;
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                total = jsonObject.getInteger("N_TOTAL");
            }
            return total;
        });
    }


    /**
     * revert promotion transaction
     * @param connPr
     * @param transactionId
     * @return
     */
    private Observable<Integer> revertTransaction(SQLConnection connPr, String transactionId) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.INTEGER)
                .addNull();
        return connPr.callWithParamsObservable2(PROMOTION_TXN_REVERT_PROCEDURE_CALL_STR, inParams, outParams).map(result -> {

            int rs = result.getOutput().getInteger(0);

            return rs;
        });
    }


    /**
     * get list promotion transaction
     * @param connPr
     * @param parameter
     * @return
     */
    private Observable<List<PromotionTransaction>> getLisTransaction(SQLConnection connPr, PromotionTransactionParameter parameter) {
        JsonArray inParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getKeywords())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionId())
                .add(parameter.getPromotionSid())
                .add(parameter.getPromotion_code())
                .add(parameter.getMerchantId())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getOrderInfo())
                .add(parameter.getCurrency())
                .add(parameter.getTransactionReference())
                .add(parameter.getPaygate())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();
        return connPr.callWithParamsObservable2(PR_TRANSACTION_LIST, inParams, outParams).map(result -> {

            List<PromotionTransaction> transactionList = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()) {
                PromotionTransaction transaction = bindTransaction(jsonObject);
                transactionList.add(transaction);
            }

            return transactionList;
        });
    }

    /**
     * Get transaction by transaction id
     * @param connPr
     * @param transactionId
     * @return
     */
    private Observable<PromotionTransaction> getTransaction(SQLConnection connPr, String transactionId) {
        JsonArray inParams = new JsonArray()
                .add(transactionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);

        return connPr.callWithParamsObservable2(PR_TRANSACTION_GET_BY_ID, inParams, outParams).map(result -> {
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if (rs.getRows().size() == 0) {
                return null;
            }
            PromotionTransaction transaction = bindTransaction(rs.getRows().get(0));
            return transaction;
        });
    }

    /**
     * file ddata from online database
     * @param connection
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection connection, PromotionTransactionParameter parameter) throws SQLException {

        JsonArray inParams = new JsonArray()
                .add(parameter.getKeywords())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getTransactionId())
                .add(parameter.getPromotionSid())
                .add(parameter.getPromotion_code())
                .add(parameter.getMerchantId())
                .add(parameter.getCardNumber())
                .add(parameter.getCardType())
                .add(parameter.getOrderInfo())
                .add(parameter.getCurrency())
                .add(parameter.getTransactionReference())
                .add(parameter.getPaygate());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR);
        CallableStatement callableStatement = null;
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, PR_TRANSACTION_EX, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(16);

        } catch (SQLException ex) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return rs;

    }

    /**
     * bind result set to promotion transaction
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionTransaction bindTransaction(JsonObject rs) {

        Timestamp date = Timestamp.valueOf(rs.getString("D_DATE"));
        double total = rs.getDouble("N_AMOUNT");
        double originalTotal = rs.getDouble("N_PR_ORG_AMOUNT");
        int prStatus = rs.getInteger("N_PR_STATUS");
        String transactionId = rs.getString("N_TRANSACTION_ID");
        String addressLine = rs.getString("S_ADDRESS");
        String cardExp = rs.getString("S_CARD_EXP");
        String cardNumber = rs.getString("S_CARD_NO");
        String cardType = rs.getString("S_CARD_TYPE");
        String city = rs.getString("S_CITY_TOWN");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String country = rs.getString("S_COUNTRY");
        String cscResultCode = rs.getString("S_CSCRESULT_CODE");
        String currency = rs.getString("S_CURRENCY");
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        String promotionList = rs.getString("S_PROMOTION_LIST");
        String promotionName = rs.getString("S_PROMOTION_NAME");
        String responseCode = rs.getString("S_RESPONSE_CODE");
        String state = rs.getString("S_STATE_PROVINCE");
        String status = rs.getString("S_STATUS");
        String ipAddress = rs.getString("S_TICKET_NUMBER");
        String transactionNo = rs.getString("S_TRANSACTION_NO");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE");
        String zipCode = rs.getString("S_ZIP_POSTAL_CODE");
        String paygate = rs.getString("S_PAYGATE");

        PromotionTransaction promotionTransaction = new PromotionTransaction();
        promotionTransaction.setTransaction_time(date);
        promotionTransaction.setTransaction_id(transactionId);
        promotionTransaction.setTransaction_no(transactionNo);
        promotionTransaction.setTransaction_reference(transactionRef);
        promotionTransaction.setCsc_result_code(cscResultCode);
        promotionTransaction.setMerchant_id(merchantId);
        promotionTransaction.setIp_address(ipAddress);
        promotionTransaction.setOrder_info(orderInfo);
        promotionTransaction.setStatus(status);
        promotionTransaction.setPromotion_code(promotionList);
        promotionTransaction.setPromotion_status(prStatus);
        promotionTransaction.setPromotion_name(promotionName);
        promotionTransaction.setResponse_code(responseCode);
        promotionTransaction.setPaygate(paygate);

        PromotionAmount amount = new PromotionAmount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setOriginal_total(originalTotal);
        promotionTransaction.setAmount(amount);

        PromotionCard card = new PromotionCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setCommercial_card(commercialCard);
        CardDate cardDate = new CardDate();
        if(cardExp != null && cardExp.length() == 4){
            cardDate.setYear(cardExp.substring(2,4));
            cardDate.setMonth(cardExp.substring(0,2));
        }
        card.setCard_date(cardDate);
        promotionTransaction.setCard(card);

        PromotionAddress address = new PromotionAddress();
        address.setAddress(addressLine);
        address.setCity(city);
        address.setState(state);
        address.setCountry(country);
        address.setZip_code(zipCode);

        promotionTransaction.setAvs(address);
        return  promotionTransaction;


    }

    private final static String PR_TRANSACTION_LIST = "{ call PKG_ONEPR_ADMIN.PROMOTION_TXN_SEARCH_6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String PR_TRANSACTION_GET_BY_ID = "{ call PKG_ONEPR_ADMIN.PROMOTION_TXN_SEARCH_BY_ID_5(?,?,?,?) }";

    private final static String PR_TRANSACTION_TOTAL = "{ call PKG_ONEPR_ADMIN.PROMOTION_TXN_TOTAL_4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private final static String PR_TRANSACTION_EX = "{ call PKG_ONEPR_ADMIN.PROMOTION_TXN_SEARCH_EXPORT_6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    public static final String PROMOTION_TXN_REVERT_PROCEDURE_CALL_STR = "{? = call PK_PR.REVERT_TBLOCK(?)}";

    private static final Logger LOGGER = Logger.getLogger(PromotionTransactionServiceImpl.class.getName());
}
