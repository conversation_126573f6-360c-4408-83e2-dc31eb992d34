package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.*;
import com.onepay.ma.service.service.CdrService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON><PERSON><PERSON> on 4/3/16.
 */
@Service
public class CdrServiceImpl implements CdrService {
    @Override
    public Observable<Transactions<CompareCdr>> list(SQLConnection sqlConCdr, CdrParameter parameter) {
        return getListCompareCDR(sqlConCdr, parameter);
    }

    @Override
    public Observable<Integer> getTotal(SQLConnection sqlConCdr, CdrParameterPost parameter) {
        return getTotalCdr(sqlConCdr, parameter);
    }

    @Override
    public ResultSet download(Connection sqlConCdr, CdrParameterPost parameter) throws SQLException {
        return downloadData(sqlConCdr, parameter);
    }

    @Override
    public Observable<FileLine> getFileLine(SQLConnection sqlConCdr, String  fileLineId) {
        return getFileLineData(sqlConCdr, fileLineId);
    }

    @Override
    public Observable<Integer> update(SQLConnection sqlConCdr, String id, String transId, String resultCode, String description) {
        return updateCdr(sqlConCdr, id, transId, resultCode, description);
    }

    @Override
    public Observable<List<CdrServiceModel>> listService(SQLConnection sqlConCdr, String source, String service) {
        return getListCdrService(sqlConCdr, source, service);
    }

    /**
     * get file line data
     * @param sqlConn
     * @param fileLineId
     * @return
     */
    private Observable<FileLine> getFileLineData(SQLConnection sqlConn, String fileLineId){
        if(fileLineId.isEmpty()){
            return Observable.just(new FileLine());
        }
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(fileLineId);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .addNull() ;

        return sqlConn.callWithParamsObservable2(CDR_PINE_LINE, inParams, outParams).map(result -> {

            FileLine fileLine =  new FileLine();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            fileLine = bindFileLine(jsonObject);

            return fileLine;
        });
    }

    /**
     * get list data cdr service
     * @param sqlConn
     * @param source
     * @param service
     * @return
     */
    private Observable<List<CdrServiceModel>> getListCdrService(SQLConnection sqlConn, String source, String service){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(service)
                .add(source);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
               ;

        return sqlConn.callWithParamsObservable2(SERVICE_CDR, inParams, outParams).map(result -> {

            List<CdrServiceModel> cdrServices = new ArrayList<CdrServiceModel>();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                CdrServiceModel cdrServiceModel = bindServiceCdr(jsonObject);
                cdrServices.add(cdrServiceModel);
            }

            return cdrServices;
        });
    }

    /**
     * get list total cdr
     * @param sqlConn
     * @param parameter
     * @return
     */
    private Observable<Integer> getTotalCdr(SQLConnection sqlConn, CdrParameterPost parameter){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(parameter.getTransaction_type())
                .add(parameter.getInfo())
                .add(parameter.getState())
                .add(parameter.getService())
                .add(parameter.getSource_left())
                .add(parameter.getSource_right())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.NUMBER)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();


        return sqlConn.callWithParamsObservable2(CDR_COMPARE_TOTAL, inParams, outParams).map(result -> {

            int total = result.getOutput().getInteger(0);

            return total;
        });
    }

    /**
     * get list total cdr
     * @param sqlConn
     * @param parameter
     * @return
     */
    private ResultSet downloadData(Connection sqlConn, CdrParameterPost parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(parameter.getTransaction_type())
                .add(parameter.getInfo())
                .add(parameter.getState())
                .add(parameter.getService())
                .add(parameter.getSource_left())
                .add(parameter.getSource_right())
                .add(parameter.getFrom_date())
                .add(parameter.getTo_date());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        CallableStatement callableStatement =  ExportDatasourceUtil.execute(sqlConn, CDR_COMPARE_DOWNLOAD, inParams, outParams);

        ResultSet rs  = (ResultSet) callableStatement.getObject(1);

        //callableStatement.close();

        return rs;

    }

    /**
     * update cdr content
     * @param sqlConCdr
     * @param id
     * @param transId
     * @param resultCode
     * @param description
     * @return
     */
    private Observable<Integer> updateCdr(SQLConnection sqlConCdr, String id, String transId, String resultCode, String description){
        if(id == null || id.isEmpty()) {
           return Observable.just(0);
        }
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(id)
                .add(transId)
                .add(resultCode)
                .add(description);
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.INTEGER)
                .addNull()
                .addNull()
                .addNull()
                .addNull();

        return sqlConCdr.callWithParamsObservable2(UPDATE_TRANS_CDR, inParams, outParams).map(result -> {
            int returnValue = result.getOutput().getInteger(0);

            return returnValue;

        });
    }

    /**
     *  get  transaction cdr data
     * @param sqlConCdr
     * @param parameter
     * @return
     */
    private Observable<Transactions<CompareCdr>> getListCompareCDR(SQLConnection sqlConCdr, CdrParameter parameter) {

        JsonArray inParams = new JsonArray()
                .addNull()
                .add(parameter.getTransactionType())
                .add(parameter.getInfo())
                .add(parameter.getState())
                .add(parameter.getService())
                .add(parameter.getSourceLeft())
                .add(parameter.getSourceRight())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getPage())
                .add(parameter.getPageSize());
        JsonArray outParams = new JsonArray()
                .add(OracleTypes.CURSOR)
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.NUMBER);

        return sqlConCdr.callWithParamsObservable2(CDR_COMPARE, inParams, outParams).map(result -> {
            Transactions<CompareCdr> compareCdrTransactions = new Transactions<>();
            List<CompareCdr> compareCdrs = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(0).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            //JsonObject jsonObject = rs.getRows().get(0);
            if(rs.getRows().size() > 0){
                for(JsonObject jsonObject : rs.getRows()){
                    CompareCdr compareCdr = bindCompareCdr(jsonObject);
                    compareCdrs.add(compareCdr);
                }
                compareCdrTransactions.setTotal_items(result.getOutput().getInteger(11));
            }else{
                compareCdrTransactions.setTotal_items(0);
            }

            compareCdrTransactions.setTransactions(compareCdrs);
            return compareCdrTransactions;
        });
    }

    /**
     * convert data from result set to cdr data
     * @param rs
     * @return
     * @throws SQLException
     */
    private CompareCdr bindCompareCdr(JsonObject rs)  {
        int index = rs.getInteger("RNUM");
        int nId1 = rs.getInteger("N_ID_1") == null ? -1 : rs.getInteger("N_ID_1");
        int nId2 = rs.getInteger("N_ID_2") == null ? -1 : rs.getInteger("N_ID_2");
        String transactionId1 = rs.getString("S_TRANS_ID_1");
        String transactionId2 = rs.getString("S_TRANS_ID_2");
        double amount1 = rs.getDouble("N_AMOUNT_1") == null ? 0 : rs.getDouble("N_AMOUNT_1");
        double amount2 = rs.getDouble("N_AMOUNT_2") == null ? 0 : rs.getDouble("N_AMOUNT_2");
        Timestamp date1 = rs.getString("D_TRANS_DATE_2") != null ? Timestamp.valueOf(rs.getString("D_TRANS_DATE_2")) : null;
        Timestamp date2 = rs.getString("D_TRANS_DATE_2") != null ? Timestamp.valueOf(rs.getString("D_TRANS_DATE_2")) : null;
        String status1 = rs.getString("S_TRANS_STATUS_1");
        String status2 = rs.getString("S_TRANS_STATUS_2");
        String currency1 = rs.getString("S_CURRENCY_CODE_1");
        String currency2 = rs.getString("S_CURRENCY_CODE_2");
        String transactionType1 = rs.getString("S_TRANS_TYPE_1");
        String transactionType2 = rs.getString("S_TRANS_TYPE_2");
        String serviceId1 = rs.getString("S_SERVICE_ID_1");
        String serviceId2 = rs.getString("S_SERVICE_ID_2");
        String resultCode1 = rs.getString("S_RESULT_CODE_1");
        String resultCode2 = rs.getString("S_RESULT_CODE_2");
        String description1 = rs.getString("S_DESCRIPTION_1");
        String description2 = rs.getString("S_DESCRIPTION_2");
        String source1 = rs.getString("S_SOURCE_ID_1");
        String source2 = rs.getString("S_SOURCE_ID_2");
        String extId1 = rs.getString("S_EXT_ID_1");
        String extId2 = rs.getString("S_EXT_ID_2");
        String transactionInfo1 = rs.getString("S_TRANS_INFO_1");
        String transactionInfo2 = rs.getString("S_TRANS_INFO_2");

        CdrTransaction leftTransaction = new CdrTransaction();
        leftTransaction.setTransaction_type(transactionType1);
        leftTransaction.setCompare_id(nId1);
        leftTransaction.setDescription(description1);
        leftTransaction.setTransaction_time(date1);
        leftTransaction.setExt_id(extId1);
        leftTransaction.setTransaction_status(status1);
        leftTransaction.setResult_code(resultCode1);
        leftTransaction.setService_id(serviceId1);
        leftTransaction.setSource_id(source1);
        leftTransaction.setTransaction_info(transactionInfo1);
        leftTransaction.setTransaction_id(transactionId1);
        Amount leftAmount = new Amount();
        leftAmount.setTotal(amount1);
        leftAmount.setCurrency(currency1);
        leftTransaction.setAmount(leftAmount);

        CdrTransaction rightTransaction = new CdrTransaction();
        rightTransaction.setTransaction_type(transactionType2);
        rightTransaction.setCompare_id(nId2);
        rightTransaction.setDescription(description2);
        rightTransaction.setTransaction_time(date2);
        rightTransaction.setExt_id(extId2);
        rightTransaction.setTransaction_status(status2);
        rightTransaction.setResult_code(resultCode2);
        rightTransaction.setService_id(serviceId2);
        rightTransaction.setSource_id(source2);
        rightTransaction.setTransaction_info(transactionInfo2);
        rightTransaction.setTransaction_id(transactionId2);
        Amount rightAmount = new Amount();
        rightAmount.setTotal(amount2);
        rightAmount.setCurrency(currency2);
        rightTransaction.setAmount(rightAmount);

        CompareCdr compareCdr = new CompareCdr();
        compareCdr.setIndex(index);
        compareCdr.setLeft(leftTransaction);
        compareCdr.setRight(rightTransaction);
        return compareCdr;
    }

    /**
     * convert data from result set to cdr service
     * @param rs
     * @return
     * @throws SQLException
     */
    private CdrServiceModel bindServiceCdr(JsonObject rs) {

        int nid = rs.getInteger("N_ID");
        String serviceId = rs.getString("S_SERVICE_ID");
        String serviceName = rs.getString("S_SERVICE_NAME");
        String sourceId1 = rs.getString("S_SOURCE_ID_1");
        String sourceId2 = rs.getString("S_SOURCE_ID_2");
        String desc = rs.getString("S_DESC");
        CdrServiceModel cdrService = new CdrServiceModel();
        cdrService.setDescription(desc);
        cdrService.setN_id(nid);
        cdrService.setService_id(serviceId);
        cdrService.setService_name(serviceName);
        cdrService.setSource_id_1(sourceId1);
        cdrService.setSource_id_2(sourceId2);
        return cdrService;
    }

    /**
     * convert data from result set to file line
     * @param rs
     * @return
     * @throws SQLException
     */
    private FileLine bindFileLine(JsonObject rs) {

        String line = rs.getString("S_LINE");
        FileLine fileLine = new FileLine();
        fileLine.setLine(line);
        return fileLine;
    }


    private static final String CDR_COMPARE = "{ ? = call PKG_CDR.COMPARE_TRANS_2(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String CDR_COMPARE_TOTAL = "{ ? = call PKG_CDR.COMPARE_TRANS_TOTAL(?,?,?,?,?,?,?,?)}";
    private static final String CDR_COMPARE_DOWNLOAD = "{ ? = call PKG_CDR.DOWNLOAD_TRANS_2(?,?,?,?,?,?,?,?)}";
    private static final String UPDATE_TRANS_CDR = " { ? = call PKG_CDR.UPDATE_TRANS(?,?,?,?) }";
    private static final String SERVICE_CDR = " { ? = call PKG_CDR.SEARCH_SERVICE(?,?) }";
    private static final String CDR_PINE_LINE = " { ? = call PKG_CDR.GET_CDR_FILE_LINE(?) } ";

    private static final Logger LOGGER = Logger.getLogger(CdrServiceImpl.class.getName());

}
