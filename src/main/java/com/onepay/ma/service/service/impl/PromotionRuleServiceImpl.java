package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.models.PromotionMessage;
import com.onepay.ma.service.models.PromotionRule;
import com.onepay.ma.service.models.PromotionRuleParam;
import com.onepay.ma.service.models.PromotionRuleType;
import com.onepay.ma.service.service.PromotionRuleParamService;
import com.onepay.ma.service.service.PromotionRuleService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * Created by huynguyen on 4/10/16.
 */
@Service
public class PromotionRuleServiceImpl implements PromotionRuleService {

    @Override
    public Observable<List<PromotionRule>> list(SQLConnection sqlConnPr, String promotionId) {
        return listPromotionRule(sqlConnPr, promotionId).flatMap(rules -> {
            return getListPromotionRuleParam(sqlConnPr, rules, 0);
        });
    }

    @Override
    public Observable<Integer> insert(SQLConnection sqlConnPr, int promotionId, PromotionRule promotionRuleDataParam) {
        return insertPromotionRule(sqlConnPr, promotionId, promotionRuleDataParam).flatMap(integer -> {
            // insert promotion rule param
            return insertPromotionRuleParam(sqlConnPr, integer,promotionRuleDataParam.getRule_type().getRule_type_id(), promotionRuleDataParam.getParams(), 0).map(promotionRuleParams -> {
                // return integer
                return integer;
            });
        });
    }

    @Override
    public Observable<Integer> delete(SQLConnection sqlConnPr, int promotionId, int ruleId) {
        return deletePromotionRule(sqlConnPr, ruleId, promotionId);
    }

    @Override
    public Observable<Integer> updateByField(SQLConnection sqlConnPr,  int ruleId, String field, String value) {
        return updatePromotionRuleByField(sqlConnPr, ruleId, field, value);
    }

    @Override
    public Observable<Integer> updateOrder(SQLConnection sqlConnPr, int promotionId, int ruleId, String value) {
        return updatePromotionRuleOrder(sqlConnPr, promotionId, ruleId, value);
    }

    /**
     * update status promotion
     * @param sqlConnPr
     * @param ruleId
     * @return
     */
    private Observable<Integer> updatePromotionRuleByField(SQLConnection sqlConnPr, int ruleId, String field, String value){

        String sql = "UPDATE " + SCHEMA_DATA + "."
                + "TB_RULE SET "
                + field + "= ?"
                + " WHERE N_ID = ?";
        JsonArray params = new JsonArray()
                .add(value).add(ruleId);

        return  sqlConnPr.updateWithParamsObservable(sql, params).flatMap(updateResult -> {
            // update data
            return Observable.just(updateResult.getUpdated());
        });
    }

    /**
     * update status promotion
     * @param sqlConnPr
     * @param ruleId
     * @return
     */
    private Observable<Integer> updatePromotionRuleOrder(SQLConnection sqlConnPr, int promotionId, int ruleId, String value){

        String sql = "UPDATE " + SCHEMA_DATA + "."
                + "TB_PR_RULE SET "
                + "N_ORDER = ?"
                + " WHERE N_PR_ID = ? AND N_RULE_ID = ?";
        JsonArray params = new JsonArray()
                .add(value)
                .add(promotionId).add(ruleId);

        return  sqlConnPr.updateWithParamsObservable(sql, params).flatMap(updateResult -> {
            // update data
            return Observable.just(updateResult.getUpdated());
        });
    }

    /**
     * insert promotion rule data
     * @param sqlConnPr
     * @param promotionId
     * @param promotionRuleDataParam
     * @return
     */
    private Observable<Integer> insertPromotionRule(SQLConnection sqlConnPr, int promotionId, PromotionRule promotionRuleDataParam){

        JsonArray inParams = new JsonArray()
                .add(promotionId)
                .add(promotionRuleDataParam.getRule_type().getRule_type_id())
                .add(promotionRuleDataParam.getRule_name())
                .add(promotionRuleDataParam.getDescription())
                .add(0)
                .add(promotionRuleDataParam.getMessage().getEn())
                .add(promotionRuleDataParam.getMessage().getVi())
                .add(promotionRuleDataParam.getOrder());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return sqlConnPr.callWithParamsObservable2(RULE_INSERT, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(8);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION MERCHANT ] Insert Rules Promotion Failed" + StringPool.SPACE + result.getOutput().getString(9));
                throw  IErrors.INTERNAL_SERVER_ERROR;
            }else{
                returnValue = result.getOutput().getInteger(10);
            }

            return returnValue;
        });
    }

    /**
     * insert list promotion rule param
     * @param sqlConn
     * @param promotionRuleParams
     * @param index
     * @return
     */
    private Observable<List<PromotionRuleParam>> insertPromotionRuleParam(SQLConnection sqlConn, int ruleId, int ruleTypeId, List<PromotionRuleParam> promotionRuleParams, int index){
        if(promotionRuleParams.size() <= 0){
            return Observable.just(promotionRuleParams);
        }
        PromotionRuleParam promotionRuleParam = promotionRuleParams.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRuleParam).flatMap(ruleParam -> {
            if(ruleParam.getRule_param_value() != null && !ruleParam.getRule_param_value().isEmpty()) {
                //get regex
                return promotionRuleParamService.getRegexRuleTYpe(sqlConn, ruleTypeId, ruleParam.getRule_param_name()).flatMap(s -> {
                    if (s == null || s.isEmpty()) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION RULE ] Empty validation regex with rule type = " + ruleTypeId + " and param name = " + ruleParam.getRule_param_name());
                        throw  IErrors.VALIDATION_ERROR;
                    }
                    final Pattern pattern = Pattern.compile(s);

//                    String value = null;
                    if(this.tryParseDouble(ruleParam.getRule_param_value())) {
                        ruleParam.setRule_param_value(String.valueOf(Double.valueOf(ruleParam.getRule_param_value()).intValue()));
                    }

                    if (!pattern.matcher(ruleParam.getRule_param_value()).matches()) {
                        LOGGER.log(Level.SEVERE, "[ PROMOTION RULE ] Invalid param value format with paramName " + ruleParam.getRule_param_name());
                        throw  IErrors.VALIDATION_ERROR;
                    }
                    //insert promotion rule
                    return promotionRuleParamService.insert(sqlConn, ruleId, promotionRuleParam).flatMap(integer -> {
                        if (finalIndex >= promotionRuleParams.size() - 1) {
                            return Observable.just(promotionRuleParams);
                        } else {
                            return insertPromotionRuleParam(sqlConn, ruleId, ruleTypeId, promotionRuleParams, finalIndex + 1);
                        }
                    });
                });
            }else{
                //insert promotion rule
                return promotionRuleParamService.insert(sqlConn, ruleId, promotionRuleParam).flatMap(integer -> {
                    if (finalIndex >= promotionRuleParams.size() - 1) {
                        return Observable.just(promotionRuleParams);
                    } else {
                        return insertPromotionRuleParam(sqlConn, ruleId, ruleTypeId, promotionRuleParams, finalIndex + 1);
                    }
                });
            }

        });
    }

    private boolean tryParseDouble(Object obj) {
        Double retVal;
        try {
            retVal = Double.parseDouble((String) obj);
        } catch (NumberFormatException nfe) {
           return false;
        }
        return true;
    }

    /**
     * get list promotion rule param
     * @param sqlConn
     * @param promotionRuleList
     * @param index
     * @return
     */
    private Observable<List<PromotionRule>> getListPromotionRuleParam(SQLConnection sqlConn, List<PromotionRule> promotionRuleList, int index){
        if(promotionRuleList.size() <= 0){
            return Observable.just(promotionRuleList);
        }
        PromotionRule promotionRule = promotionRuleList.get(index);
        final int finalIndex = index;
        return Observable.just(promotionRule).flatMap(serviceApproval -> {
            //get user data
            return promotionRuleParamService.list(sqlConn, promotionRule.getRule_id()).flatMap(promotionRuleParams -> {
                promotionRule.setParams(promotionRuleParams);
                if(finalIndex >= promotionRuleList.size() - 1){
                    return Observable.just(promotionRuleList);
                }else{
                    return getListPromotionRuleParam(sqlConn, promotionRuleList, finalIndex + 1);
                }
            });
        });
    }

    /**
     * get list promotion rule
     * @param sqlConn
     * @param promotionId
     * @return
     */
    private Observable<List<PromotionRule>> listPromotionRule(SQLConnection sqlConn, String promotionId){
        JsonArray inParams = new JsonArray()
                .addNull()
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.CURSOR) ;
        return sqlConn.callWithParamsObservable2(LIST_PROMOTION_RULE, inParams, outParams).map(result -> {

            List<PromotionRule> promotionRules = new ArrayList<>();
            Map map = result.getOutput().getJsonObject(4).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                PromotionRule promotionRule = bindRule(jsonObject);
                promotionRules.add(promotionRule);
            }

            return promotionRules;
        });
    }

    /**
     * delete promotion rule
     * @param sqlConn
     *  @param ruleId
     * @param promotionId
     * @return
     */
    private Observable<Integer> deletePromotionRule(SQLConnection sqlConn, int ruleId, int promotionId){

        JsonArray inParams = new JsonArray()
                .add(ruleId)
                .add(promotionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR)
                .add(OracleTypes.INTEGER);
        return sqlConn.callWithParamsObservable2(RULE_DELETE, inParams, outParams).map(result -> {
            int returnValue = 0;
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ PROMOTION RULE ] Delete Promotion Rule Failed" + StringPool.SPACE +  result.getOutput().getString(3));
                throw  IErrors.VALIDATION_ERROR;
            }else{
                returnValue =  result.getOutput().getInteger(4);
            }

            return returnValue;
        });
    }

    /**
     * bind result set to promotion rule
     * @param rs
     * @return
     * @throws SQLException
     */
    private PromotionRule bindRule(JsonObject rs) {

        int ruleId = rs.getInteger("N_ID");
        String ruleName = rs.getString("S_NAME");
        String description = rs.getString("S_DESC");
        String viMessage = rs.getString("S_VI_MESSAGE");
        String enMessage = rs.getString("S_EN_MESSAGE");
        Timestamp createTime = Timestamp.valueOf(rs.getString("D_CREATE"));

//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
////
//        try {
//            createTime = new Timestamp(formatter.parse(rs.getString("D_CREATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        int ruleTypeId = rs.getInteger("N_TYPE_ID");
        String ruleTypeName = rs.getString("S_TYPE_NAME");
        String ruleTypeDesc = rs.getString("S_TYPE_DESC");
        int order = rs.getInteger("N_ORDER");

        PromotionRule promotionRule = new PromotionRule();
        promotionRule.setCreate_time(createTime);
        promotionRule.setRule_id(ruleId);
        promotionRule.setRule_name(ruleName);
        promotionRule.setDescription(description);
        promotionRule.setOrder(order);

        PromotionMessage message  = new PromotionMessage();
        message.setEn(enMessage);
        message.setVi(viMessage);
        promotionRule.setMessage(message);

        PromotionRuleType promotionRuleType = new PromotionRuleType();
        promotionRuleType.setDescription(ruleTypeDesc);
        promotionRuleType.setRule_type_id(ruleTypeId);
        promotionRuleType.setRule_type_name(ruleTypeName);
        promotionRule.setRule_type(promotionRuleType);

        return promotionRule;
    }

    private final static String LIST_PROMOTION_RULE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULES(?,?,?,?,?)}";

    private static final String RULE_INSERT = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_INSERT(?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String RULE_DELETE = "{call ONEPR.PKG_ONEPR_ADMIN.PROMOTION_RULE_DELETE(?,?,?,?,?)}";

    private static final String SCHEMA_DATA = "ONEPR";

    @Autowired
    private PromotionRuleParamService promotionRuleParamService;

    private static final Logger LOGGER = Logger.getLogger(PromotionRuleServiceImpl.class.getName());


}
