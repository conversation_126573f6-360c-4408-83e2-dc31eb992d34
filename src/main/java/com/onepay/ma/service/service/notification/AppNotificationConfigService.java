package com.onepay.ma.service.service.notification;

import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.notification.MpayNotificationConfig;
import com.onepay.ma.service.models.notification.NotificationConfigSearchQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.util.List;

/**
 * Created by anhkh on 25-Sep-17.
 */
public interface AppNotificationConfigService {

    Observable<BaseList<MpayNotificationConfig>> search(SQLConnection connectionB, NotificationConfigSearchQuery query);

    Observable<MpayNotificationConfig> insert(SQLConnection connectionB, MpayNotificationConfig config);

    Observable<MpayNotificationConfig> update(SQLConnection connectionB, MpayNotificationConfig config);


    Observable<MpayNotificationConfig> get(SQLConnection connectionB, Integer id);


    Observable<Void> delete(SQLConnection connectionB, Integer id);


    Observable<List<String>> findUserBy(SQLConnection connectionB, NotificationConfigSearchQuery query);
}
