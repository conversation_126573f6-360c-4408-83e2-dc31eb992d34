package com.onepay.ma.service.service;


import java.util.List;
import com.onepay.ma.service.models.Merchant;
import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/3/16.
 */
public interface UserMerchantService {
    Observable<Integer> deleteByUserId(SQLConnection connection, int userId, String type);
    Observable<Integer> insert(SQLConnection connection, Merchant merchant, int userId);
    Observable<BaseList<User>> findUserByMerchant(SQLConnection connection, String merchantId, String keyword);
    Observable<List<String>> listUserIdByMerchant(SQLConnection connection, String merchantId, String type);
    Observable<Integer> deleteByUserIdV2(SQLConnection connection, int userId);
    Observable<Integer> deleteByUserIdV3(SQLConnection connection, int userId, List<Merchant> merchant);
}
