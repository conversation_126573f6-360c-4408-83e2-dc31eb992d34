package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.models.DomesticReport;
import com.onepay.ma.service.models.DomesticReportParameter;
import com.onepay.ma.service.models.base.QueryMethod;
import com.onepay.ma.service.service.DomesticReportService;
import com.onepay.ma.service.util.ExportDatasourceUtil;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huy<PERSON>uy<PERSON> on 4/2/16.
 */
@Service
public class DomesticReportServiceImpl implements DomesticReportService {
    @Override
    public Observable<List<DomesticReport>> list(SQLConnection connection, DomesticReportParameter parameter) {
        return listReport(connection, parameter);
    }

    @Override
    public Observable<List<DomesticReport>> listDetail(SQLConnection connection, DomesticReportParameter parameter) {
        return  listReportDetail(connection, parameter);
    }

    @Override
    public Observable<Integer> getTotalReportDetail(SQLConnection connReadOnly, DomesticReportParameter parameter) {
        return getListTotalReportDetail(connReadOnly, parameter);
    }

    @Override
    public Observable<Integer> getTotalReport(SQLConnection connReadOnly, DomesticReportParameter parameter){
        return getListTotalReport(connReadOnly, parameter);
    }

    @Override
    public List<DomesticReport> download(Connection connReadOnly, DomesticReportParameter parameter) throws SQLException {
        return downloadData(connReadOnly, parameter);
    }

    /**
     * file data from readonly database
     * @param connection
     * @param parameter
     * @return
     */
    private List<DomesticReport> downloadData(Connection connection, DomesticReportParameter parameter) throws SQLException {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getVersion())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
//        String callName = REPORT_DOMESTIC_DOWNLOAD;
//        if("v2".equals(parameter.getVersion())) {
//            callName = REPORT_DOMESTIC_V2;
//        }
        CallableStatement callableStatement = null;
        List<DomesticReport> domesticReports = new ArrayList<DomesticReport>();
        ResultSet rs = null;
        try {
            callableStatement = ExportDatasourceUtil.execute(connection, SEARCH_REPORT_DOMESTIC, inParams, outParams);
            rs = (ResultSet) callableStatement.getObject(8);
            if (rs != null) {
                while (rs.next()) {
                    DomesticReport  domesticReport = bindReportDownload(rs,parameter.getVersion());
                    domesticReports.add(domesticReport);
                }
            }
        } catch (SQLException e) {
            throw new DatabaseException("Error Sql", e);
        }  finally {
            if(rs != null){
                try {
                    rs.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error close result set", e);
                }
            }
            if(callableStatement != null){
                try {
                    callableStatement.close();
                } catch (SQLException e) {
                    LOGGER.log(Level.SEVERE, "Error close statement", e);
                }

            }
        }

        return domesticReports;
    }

    private Observable<List<DomesticReport>> listReport(SQLConnection connection, DomesticReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.SELECT.toString())
                .add(parameter.getVersion())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
//        String callName =REPORT_DOMESTIC;
//        if("v2".equals(parameter.getVersion())) {
//            callName = REPORT_DOMESTIC_V2;
//        }
        return connection.callWithParamsObservable2(SEARCH_REPORT_DOMESTIC, inParams, outParams).map(result -> {
            List<DomesticReport> domesticReports = new ArrayList<DomesticReport>();
            Map map = result.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                DomesticReport domesticReport = bindReport(jsonObject);
                domesticReports.add(domesticReport);
            }

            return domesticReports;
        });
    }

    private Observable<List<DomesticReport>> listReportDetail(SQLConnection connection, DomesticReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(REPORT_DOMESTIC_DETAIL, inParams, outParams).map(result -> {
            List<DomesticReport> domesticReports = new ArrayList<DomesticReport>();
            Map map = result.getOutput().getJsonObject(5).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                DomesticReport domesticReport = bindReport(jsonObject);
                domesticReports.add(domesticReport);
            }

            return domesticReports;
        });
    }

    /**
     *  get report data total
     * @param connection
     * @param parameter
     * @return
     */
    private Observable<Integer> getListTotalReport(SQLConnection connection, DomesticReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add(QueryMethod.TOTAL.toString())
                .add(parameter.getVersion())
                .add(parameter.getMerchantId())
                .add(parameter.getAcquirerId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);


        return connection.callWithParamsObservable2(SEARCH_REPORT_DOMESTIC, inParams, outParams).map(result -> {
            int total = 0;
            Map map = result.getOutput().getJsonObject(7).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            for (JsonObject jsonObject : rs.getRows()){
                total = jsonObject.getInteger("N_TOTAL");
            }

            return total;
        });
    }

    private Observable<Integer> getListTotalReportDetail(SQLConnection connection, DomesticReportParameter parameter) {
        JsonArray inParams = new JsonArray()
                .add("")
                .add(parameter.getAcquirerId())
                .add(parameter.getFromDate())
                .add(parameter.getToDate())
                .add(parameter.getInterval());
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.NUMBER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return connection.callWithParamsObservable2(REPORT_DOMESTIC_TOTAL_DETAIL, inParams, outParams).map(result -> {
            int total = result.getOutput().getInteger(5);

            return total ;
        });
    }


    private DomesticReport bindReport(JsonObject rs) {
        DomesticReport domesticReport = new DomesticReport();
        domesticReport.setAcquirer_id(String.valueOf(rs.getInteger("BANK_ID")));
        domesticReport.setAcquirer_name(rs.getString("ACQUIRER_NAME"));

        domesticReport.setReport_date(Timestamp.valueOf(rs.getString("TXN_DATE")));
//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
//
//        try {
//            domesticReport.setReport_date( formatter.parse(rs.getString("TXN_DATE")));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        domesticReport.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        domesticReport.setCount(rs.getInteger("TOTAL_ROWS"));
        domesticReport.setTotal(rs.getDouble("AMOUNT"));
        domesticReport.setCommand(rs.getString("COMMAND"));
        return domesticReport;
    }

    private DomesticReport bindReportDownload(ResultSet rs, String version) throws SQLException {
        DomesticReport domesticReport = new DomesticReport();
        // domesticReport.setAcquirer_id(rs.getString("BANK_ID"));
        domesticReport.setAcquirer_name(rs.getString("ACQUIRER_NAME"));

        domesticReport.setReport_date(rs.getDate("TXN_DATE"));
//        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
//
//        try {
//            domesticReport.setReport_date( formatter.parse(rs.getString("TXN_DATE")));
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        if("v2".equals(version)) {
            domesticReport.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        }
        domesticReport.setCount(rs.getInt("TOTAL_ROWS"));
        domesticReport.setTotal(rs.getDouble("AMOUNT"));
        domesticReport.setCommand(rs.getString("COMMAND"));
        return domesticReport;
    }
    private final static String REPORT_DOMESTIC_DOWNLOAD = "{call PKG_PAYMENT2.GET_REPORTS_REPORT(?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC_DOWNLOAD_V2 = "{call PKG_PAYMENT2.GET_REPORTS_REPORT_V2(?,?,?,?,?,?,?,?,?)}";

    private final static String SEARCH_REPORT_DOMESTIC = "{call PKG_PAYMENT2.SEARCH_REPORTS(?,?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC = "{call PKG_PAYMENT2.GET_REPORTS_2(?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC_V2 = "{call PKG_PAYMENT2.GET_REPORTS_V2(?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC_TOTAL = "{call PKG_PAYMENT2.GET_TOTAL_REPORTS(?,?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC_TOTAL_DETAIL = "{call PKG_PAYMENT2.GET_TOTAL_REPORTS_DETAIL(?,?,?,?,?,?,?,?)}";

    private final static String REPORT_DOMESTIC_DETAIL = "{call PKG_PAYMENT2.GET_REPORTS_DETAIL(?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(DomesticReportServiceImpl.class.getName());


}
