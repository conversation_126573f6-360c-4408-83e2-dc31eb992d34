package com.onepay.ma.service.service.reconcile;

import com.onepay.ma.service.models.InternationalReport;
import com.onepay.ma.service.models.InternationalReportParameter;
import com.onepay.ma.service.models.reconciliation.Reconciliation;
import com.onepay.ma.service.models.reconciliation.ReconciliationDetailQuery;
import com.onepay.ma.service.models.reconciliation.ReconciliationQuery;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import rx.Observable;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by tuydv on 19/09/18.
 */
public interface ReconcileService {
    Observable<List<InternationalReport>> listByCurrency(SQLConnection connection, InternationalReportParameter parameter);
    ResultSet downloadDetail(Connection connection, InternationalReportParameter parameter) throws SQLException;

    Observable<List<Reconciliation>> listPayment(SQLConnection connection, ReconciliationQuery parameter);

    List<Reconciliation> downloadPayment(Connection connection, ReconciliationQuery parameter) throws SQLException;

    Observable<List<Reconciliation>> listDetailPayment(SQLConnection connection, ReconciliationDetailQuery parameter);

    List<Reconciliation> downloadDetailPayment(Connection connection, ReconciliationDetailQuery parameter) throws SQLException;
}
