package com.onepay.ma.service.service.partner.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.User;
import com.onepay.ma.service.models.base.BaseList;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.models.partner.Partners;
import com.onepay.ma.service.service.partner.PartnerService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.Map;
import java.util.List;
import java.util.logging.Level;

@Service
public class PartnerServiceImpl implements PartnerService {
    @Override
    public Observable<Partners> list(SQLConnection sqlConnection, int page, int pageSize, String keyword) {
        JsonArray inParams = new JsonArray()
                .add(page)
                .add(pageSize)
                .add(keyword);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(GET_LIST_PARTNERS, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(5);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET PARTNERS ] Failed " + result.getOutput().getString(6));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int totalRows = result.getOutput().getInteger(4);
            Map map = result.getOutput().getJsonObject(3).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            Partners partners = new Partners();
            List<Partner> list = new ArrayList<>();
            for (JsonObject jsonObject : rs.getRows()) {
                list.add(bind(jsonObject));
            }

            partners.setPartners(list);
            partners.setTotal_items(totalRows);

            return partners;
        });
    }

    @Override
    public Observable<Partner> getPartnerById(SQLConnection sqlConnectionB, Integer id) {
        JsonArray inParams = new JsonArray()
                .add(id);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return sqlConnectionB.callWithParamsObservable2(GET_PARTNER_BY_ID, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET PARTNER BY ID ] Failed " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            Partner partner = null;
            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");
            if(rs.getRows().size() <= 0) return null;
            JsonObject jsonObject = rs.getRows().get(0);
            partner = this.bind(jsonObject);

            return partner;
        });
    }

    private Partner bind(JsonObject js) {
        Partner p = new Partner();
        p.setId(js.getInteger("N_ID").toString());
        p.setShortName(js.getString("S_SHORT_NAME"));
        p.setName(js.getString("S_PARTNER_NAME"));
        p.setBussinessType(js.getString("S_NGANHNGHE_KHAC"));
        p.setAddress(js.getString("S_ADDRESS_LINE1"));
        p.setWebsite(js.getString("S_WEBSITE"));
        p.setPhoneNumber(js.getString("S_PHONE_NUMBER"));
        return p;
    }


    private static final String GET_LIST_PARTNERS = "{ call PKG_PARTNER.get_list_partner(?,?,?,?,?,?,?) }";

    private static final String GET_PARTNER_BY_ID = "{ call PKG_PARTNER.get_partner_by_id(?,?,?,?) }";


    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PartnerServiceImpl.class.getName());
}
