package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.paypal.Partner;
import com.onepay.ma.service.service.EmailService;
import com.onepay.ma.service.util.PropsUtil;
import org.springframework.stereotype.Service;
import rx.Observable;


import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.text.MessageFormat;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class EmailServiceImpl implements EmailService {
    private static final Logger LOGGER = Logger.getLogger(EmailServiceImpl.class.getName());
    public static String EMAIL_SITE_HOST = PropsUtil.get("email.support.site.host", "");
    public static String EMAIL_SITE_PORT = PropsUtil.get("email.support.site.port", "");
    public static String EMAIL_SITE_USER = PropsUtil.get("email.support.site.user", "");
    public static String EMAIL_SITE_FROM = PropsUtil.get("email.support.site.from", "");
    public static String EMAIL_SITE_PASSWORD = PropsUtil.get("email.supoport.site.password", "");
    public static String EMAIL_SUBJECT = PropsUtil.get("email.subject", "");
    public static String EMAIL_CONTENT_VI = "Dear Merchant,<br>Yêu cầu đặt lại mật khẩu cho User {1} đã được xử lý thành công.<br>Vui lòng đăng nhập lại theo thông tin bên dưới:<br><br>User: {1}<br> Mật khẩu mới : <b>{2}</b><br/>---------------------------------------------------------------------------------<br/>";
    public static String EMAIL_CONTENT_EN = PropsUtil.get("email.content_en", "");
    public static String EMAIL_SSL = PropsUtil.get("email.support.ssl", "true");
    public static String EMAIL_STARTTLS = PropsUtil.get("email.support.starttls", "true");


    public static String EMAIL_SENT_REGISTER_PAYPAL = PropsUtil.get("email.support.register.to.paypal", "");
    public static String EMAIL_SUBJECT_REGISTER_PAYPAL = "Yêu cầu cấu hình paypal merchant";
    public static String EMAIL_CONTENT_REGISTER_PAYPAL_VI = "Dear Tech,<br><br/> " +
            "Đề nghị liên kết tài khoản PayPal cho Merchant như sau: <br/> <br/>" +
            "Tài khoản MADM yêu cầu: {0}  <br/> " +
            "PayPal Account: {1} <br/> " +
            "Partner: {2} ({3}) <br/> " +
            "Partner Short Name: {4} <br/>  " +
            "---------------------------------------------------------------------------------<br/>";

    private static String EMAIL_OPERATOR_SUBJECT = "[OnePAY] Payout Operator - Merchant Name: {0} / User Name: {1}";
    private static String EMAIL_OPERATOR_CONTENT = "Kính gửi Đơn vị,<br/> <i>Dear Merchant,</i><br><br> OnePAY xin gửi thông tin tài khoản Operator như sau:    <br/><i>OnePAY send Operator account as follows:    </i><br/><br/>Merchant Name: {0}  <br/><br/>  User Name: {1} <br> Email Address: {2} <br><b> Password: {3}</b><br><br> Quý khách vui lòng truy cập theo đường link dưới để kích hoạt tài khoản:    <br/> <i>Please access the link below to active account:</i> <br> {4}<br/><br/> Quý Đơn vị vui lòng không trả lời email này. Trường hợp cần hỗ trợ thêm, vui lòng liên hệ OnePAY theo thông tin dưới đây: <br/> <i>Please do not reply to this email. Should you need further assistance, please feel free to contact us via:</i>    <br/><br/>Service Support<br/> OnePAY Vietnam<br/> 5th Floor, BIDV Tower<br/> 194 Tran Quang Khai Street, Hoan Kiem Dist., Hanoi, Vietnam<br/>Tel: (84) 24 3936 6668 | Hotline: (84) 1900 633 927<br/> <EMAIL> | www.onepay.vn";
    private static String ONEPAY_MA_PAYOUT_SERVICE_BASE_URL = PropsUtil.get("email.operator_change_pass_url", "");
    private static String EMAIL_OPERATOR_CONTENT_RESET_PASS_SUBJECT = PropsUtil.get("email.operator_reset_pass_subject", "");
    private static String EMAIL_OPERATOR_CONTENT_RESET_PASS_VI = "Dear Merchant,<br>Yêu cầu đặt lại mật khẩu cho Operator {1} đã được xử lý thành công.<br>Vui lòng đăng nhập lại theo thông tin bên dưới:<br><br>User: {2}<br> Mật khẩu mới : <b>{3}</b><br/>---------------------------------------------------------------------------------<br/>";
    private static String EMAIL_OPERATOR_CONTENT_RESET_PASS_EN = "Dear Merchant,<br>The password for Operator {1} has been reset successfully!<br>Please login using the following information:<br><br>User: {2}<br> New password : <b>{3}</b><br><br>Thanks & Best Regards,<br>------------------<br>OnePay JSC<br>Phone: (84) 24 39366668 | Hotline: (84) 1900 633 927<br>Email: <a href='mailto:<EMAIL>'><EMAIL></a> | Website: <a href='https://www.onepay.vn'>www.onepay.vn</a><br>";

//    public static String URL_SITE = PropsUtil.get("url.site", "");
//    public static String EMAIL_SUPPORT = PropsUtil.get("email.support", "");
//    public static String PHONE_SUPPORT = PropsUtil.get("phone.support", "");
//    public static String PHONE_SUPPORT_EXT = PropsUtil.get("phone.support.ext", "");

    @Override
    public Observable<String> sentMail(String emailSendTo, String emailSendCc, String userName, String pass) {
        //Get the session object
        Properties props = new Properties();
        props.put("mail.smtp.host", EMAIL_SITE_HOST);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", EMAIL_SITE_PORT);
        props.put("mail.smtp.starttls.enable", EMAIL_STARTTLS);
        props.put("mail.smtp.ssl.enable", EMAIL_SSL);
//		props.put("mail.debug", "true");
        //Compose the message
        try {
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_SITE_USER, EMAIL_SITE_PASSWORD);
                }
            });

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(EMAIL_SITE_FROM));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(emailSendTo));
            if (emailSendCc != null) {
                message.addRecipient(Message.RecipientType.CC, new InternetAddress(emailSendCc));
            }
            message.setSubject(EMAIL_SUBJECT);
            String htmlContent = genContent(emailSendTo, pass);
            message.setContent(htmlContent, "text/html; charset=UTF-8");

            //send the message
            Transport.send(message);

            return null;
        } catch (MessagingException | SecurityException e) {
            LOGGER.log(Level.SEVERE, "Error when sent mail: ", e);
            throw IErrors.INVALID_SENT_EMAIL;
        }
    }

    @Override
    public Observable<String> sentMailRegisterPP(UserData userData, Partner partner, String ppAccount) {
        //Get the session object
        Properties props = new Properties();
        props.put("mail.smtp.host", EMAIL_SITE_HOST);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", EMAIL_SITE_PORT);
        props.put("mail.smtp.starttls.enable", EMAIL_STARTTLS);
        props.put("mail.smtp.ssl.enable", EMAIL_SSL);


        //Compose the message
        try {
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_SITE_USER, EMAIL_SITE_PASSWORD);
                }
            });

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(EMAIL_SITE_FROM));

            message.addRecipients(Message.RecipientType.TO, InternetAddress.parse(EMAIL_SENT_REGISTER_PAYPAL));
            message.setSubject(EMAIL_SUBJECT_REGISTER_PAYPAL);
            String htmlContent = genContentRegisterPaypal(userData.getEmail(), ppAccount, partner);
            message.setContent(htmlContent, "text/html; charset=UTF-8");

            //send the message
            Transport.send(message);

            return Observable.just("OK");
        } catch (MessagingException | SecurityException e) {
            LOGGER.log(Level.SEVERE, "Error when sent mail: ", e);
            throw IErrors.INVALID_SENT_EMAIL;
        }
    }

    @Override
    public Observable<String> sentMailCreateOperator(OperatorDTO operator, String pass) {
        Properties props = new Properties();
        props.put("mail.smtp.host", EMAIL_SITE_HOST);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", EMAIL_SITE_PORT);
        props.put("mail.smtp.starttls.enable", EMAIL_STARTTLS);
        props.put("mail.smtp.ssl.enable", EMAIL_SSL);
        try {
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_SITE_USER, EMAIL_SITE_PASSWORD);
                }
            });

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(EMAIL_SITE_FROM));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(operator.getEmail()));
            String subject = genSubjectOperator(operator.getPartnerId(), operator.getUserName());
            message.setSubject(subject);
            String url = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + operator.getOperatorId()+"&email="+operator.getEmail();
            String htmlContent = genContentOperator(operator.getPartnerId(), operator.getUserName(), operator.getEmail(), pass, url);
            htmlContent+="</br><p><img src='https://www.onepay.vn/home/<USER>/onepay/images/logo.png'/></p>";
            message.setContent(htmlContent, "text/html; charset=UTF-8");
            Transport.send(message);
            return null;
        } catch (MessagingException | SecurityException e) {
            LOGGER.log(Level.SEVERE, "Error when sent mail: ", e);
            throw IErrors.INVALID_SENT_EMAIL;
        }
    }

    @Override
    public Observable<String> sentMailResetPassOperator(OperatorDTO operator, String newPass) {
        Properties props = new Properties();
        props.put("mail.smtp.host", EMAIL_SITE_HOST);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", EMAIL_SITE_PORT);
        props.put("mail.smtp.starttls.enable", EMAIL_STARTTLS);
        props.put("mail.smtp.ssl.enable", EMAIL_SSL);
        try {
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(EMAIL_SITE_USER, EMAIL_SITE_PASSWORD);
                }
            });

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(EMAIL_SITE_FROM));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(operator.getEmail()));

            message.setSubject(EMAIL_OPERATOR_CONTENT_RESET_PASS_SUBJECT);
            operator.setPassword(newPass);
            String htmlContent = genContentResetPass(operator);
            message.setContent(htmlContent, "text/html; charset=UTF-8");

            Transport.send(message);

            return null;
        } catch (MessagingException | SecurityException e) {
            LOGGER.log(Level.SEVERE, "Error when sent mail: ", e);
            throw IErrors.INVALID_SENT_EMAIL;
        }
    }


    private static String genContent(String emailSendTo, String pass) {
        return stringContentFormat(EMAIL_CONTENT_VI, emailSendTo, emailSendTo, pass) + stringContentFormat(EMAIL_CONTENT_EN, emailSendTo, emailSendTo, pass);
//        StringBuilder htmlContent =new StringBuilder("");
//        htmlContent.append("Dear ");
//        htmlContent.append(userName);
//        htmlContent.append(",<br> ");
//        htmlContent.append("We have received your password reset request.<br>");
//        htmlContent.append("We are pleased to confirm that your new password is: <br>");
//        htmlContent.append("<b> ");
//        htmlContent.append(pass);
//        htmlContent.append("</b> ");
//        htmlContent.append("<br><br> ");
//        htmlContent.append("Please log in to  ");
//        htmlContent.append(URL_SITE);
//        htmlContent.append(" to reset your own password.<br>");
//        htmlContent.append("If you have any questions please contact OnePAY Merchant Service team via email ");
//        htmlContent.append(EMAIL_SUPPORT);
//        htmlContent.append(" or the phone number  ");
//        htmlContent.append(PHONE_SUPPORT);
//        htmlContent.append(" ext ");
//        htmlContent.append("<b> ");
//        htmlContent.append(PHONE_SUPPORT_EXT);
//        htmlContent.append("</b>.<br><br> ");
//        htmlContent.append("Yours sincerely<br> ");
//        htmlContent.append("OnePAY Merchant Service<br> ");
//        return  htmlContent.toString();
    }

    private static String genContentRegisterPaypal(String emailSendTo, String ppAccount, Partner partner) {
        return stringContentFormat(EMAIL_CONTENT_REGISTER_PAYPAL_VI, emailSendTo, ppAccount, partner.getPartner_name(), partner.getN_partner_id().toString(), partner.getShort_name());

    }

    private static String genSubjectOperator(String merchantId, String userName) {
        return stringContentFormat(EMAIL_OPERATOR_SUBJECT, merchantId, userName);
    }

    private static String genContentOperator(String merchantId, String userName, String email, String pass,  String linkActive) {
        return stringContentFormat(EMAIL_OPERATOR_CONTENT, merchantId, userName, email, pass, linkActive);
    }

    private static String genContentResetPass(OperatorDTO operator) {
        return stringContentFormat(EMAIL_OPERATOR_CONTENT_RESET_PASS_VI, operator.getEmail(), operator.getUserName(), operator.getPassword()) + stringContentFormat(EMAIL_OPERATOR_CONTENT_RESET_PASS_EN, operator.getEmail(), operator.getUserName(), operator.getPassword());
    }

    public static String stringContentFormat(String s, Object... args) {
        return new MessageFormat(s).format(args);
    }
}
