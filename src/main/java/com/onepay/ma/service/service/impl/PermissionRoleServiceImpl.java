package com.onepay.ma.service.service.impl;

import com.onepay.ma.service.util.DatabaseException;
import com.onepay.ma.service.service.PermissionRoleService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/2/16.
 */
@Service
public class PermissionRoleServiceImpl implements PermissionRoleService {
    @Override
    public Observable<Integer> insert(SQLConnection connection, int permissionId, int roleId) {
        return insertRoleUser(connection, permissionId, roleId);
    }

    @Override
    public Observable<Integer> delete(SQLConnection connection, int permissionId) {
        return deleteByPermission(connection, permissionId);
    }

    @Override
    public Observable<Integer> deleteByRoleId(SQLConnection connection, int roleId) {
        return deleteByRole(connection, roleId);
    }

    /**
     * insert role user
     * @param connection
     * @param permissionId
     * @param roleId
     * @return
     */
    private Observable<Integer> insertRoleUser(SQLConnection connection, int permissionId, int roleId){
        JsonArray inParams = new JsonArray()
                .add(permissionId)
                .add(roleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(INSERT_FUNCTION_ROLE, inParams, outParams).map(result -> {
            int returnId = result.getOutput().getInteger(2);
            if(returnId == 0){
                throw  new DatabaseException("insert permission role failed");
            }


            return returnId;
        });
    }

    /**
     * delete permssion role by permission Id
     * @param connection
     * @param permissionId
     * @return
     */
    private Observable<Integer> deleteByPermission(SQLConnection connection, int permissionId){
        JsonArray inParams = new JsonArray()
                .add(permissionId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_FUNCTION_ROLE, inParams, outParams).map(result -> {
            int returnId = result.getOutput().getInteger(1);
            if(returnId == 0){
                throw  new DatabaseException("Delete permission role failed");
            }

            return returnId;
        });
    }


    /**
     * delete permssion role by role Id
     * @param connection
     * @param roleId
     * @return
     */
    private Observable<Integer> deleteByRole(SQLConnection connection, int roleId){
        JsonArray inParams = new JsonArray()
                .add(roleId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);
        return connection.callWithParamsObservable2(DELETE_FUNCTION_ROLE_BY_ROLE_ID, inParams, outParams).map(result -> {
            int returnId = result.getOutput().getInteger(1);
            if(returnId == 0){
                throw  new DatabaseException("Delete permission role failed");
            }


            return returnId;
        });
    }

    private static final String INSERT_FUNCTION_ROLE = "{call PKG_MERCHANTPORTAL_2.ROLE_FUNCTION_INSERT(?,?,?,?)}";

    private static final String DELETE_FUNCTION_ROLE = "{call PKG_MERCHANTPORTAL_2.ROLE_FUNCTION_DELETE(?,?,?)}";

    private static final String DELETE_FUNCTION_ROLE_BY_ROLE_ID = "{call PKG_MERCHANTPORTAL_2.FUNCTION_ROLE_DELETE(?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(PermissionRoleServiceImpl.class.getName());
}
