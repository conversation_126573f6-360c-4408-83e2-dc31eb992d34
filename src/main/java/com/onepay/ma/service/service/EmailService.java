package com.onepay.ma.service.service;

import com.onepay.ma.service.models.UserData;
import com.onepay.ma.service.models.pay_out.OperatorDTO;
import com.onepay.ma.service.models.paypal.Partner;
import rx.Observable;

/**
 * Created by  on tuydv 21/5/18.
 */
public interface EmailService {

    Observable<String> sentMail(String emailSendTo,String emailSendCc, String userName, String pass);
    Observable<String> sentMailRegisterPP(UserData userData, Partner partner, String ppAccount);
    Observable<String> sentMailCreateOperator(OperatorDTO operator,String pass);
    Observable<String> sentMailResetPassOperator(OperatorDTO operator,String newPass);
}
