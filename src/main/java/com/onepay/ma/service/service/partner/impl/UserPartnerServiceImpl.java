package com.onepay.ma.service.service.partner.impl;

import com.onepay.ma.service.util.IErrors;
import com.onepay.ma.service.models.partner.Partner;
import com.onepay.ma.service.models.partner.Partners;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.vertx.rxjava.ext.sql.SQLConnection;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.springframework.stereotype.Service;
import rx.Observable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

@Service
public class UserPartnerServiceImpl implements UserPartnerService {
    @Override
    public Observable<Integer> insert(SQLConnection sqlConnection, int userId, String partnerId) {
        JsonArray inParams = new JsonArray()
                .add(userId)
                .add(partnerId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(INSERT_USER_PARTNER, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 201) {
                LOGGER.log(Level.SEVERE, "[ INSERT USER PARTNER ] Failed " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    @Override
    public Observable<Integer> deleteByUserId(SQLConnection sqlConnection, int userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(DELETE_PARTNERS_BY_USER_ID, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(1);
            if (resultCode != 204) {
                LOGGER.log(Level.SEVERE, "[ DELETE USER PARTNER BY USER ID ] Failed " + result.getOutput().getString(2));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            return resultCode;
        });
    }

    @Override
    public Observable<Partners> getPartnersByUserId(SQLConnection sqlConnection, String userId) {
        JsonArray inParams = new JsonArray()
                .add(userId);
        JsonArray outParams = new JsonArray()
                .addNull()
                .add(OracleTypes.CURSOR)
                .add(OracleTypes.INTEGER)
                .add(OracleTypes.VARCHAR);

        return sqlConnection.callWithParamsObservable2(GET_LIST_PARTNERS_BY_USER_ID, inParams, outParams).map(result -> {
            int resultCode = result.getOutput().getInteger(2);
            if (resultCode != 200) {
                LOGGER.log(Level.SEVERE, "[ GET PARTNERS BY USER ID ] Failed " + result.getOutput().getString(3));
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            Map map = result.getOutput().getJsonObject(1).getMap();
            io.vertx.ext.sql.ResultSet rs = (io.vertx.ext.sql.ResultSet) map.get("result_set");

            Partners partners = new Partners();
            List<Partner> list = new ArrayList<>();
            for (JsonObject jsonObject : rs.getRows()) {
                list.add(bind(jsonObject));
            }

            partners.setPartners(list);
            partners.setTotal_items(list.size());

            return partners;
        });
    }

    private Partner bind(JsonObject js) {
        Partner p = new Partner();
        p.setId(js.getString("S_PARTNER_ID"));
        p.setShortName(js.getString("S_PARTNER_SHORT_NAME"));
        p.setName(js.getString("S_PARTNER_NAME"));
        return p;
    }

    private static final String GET_LIST_PARTNERS_BY_USER_ID = "{ call PKG_PARTNER.get_partner_by_user_id(?,?,?,?) }";

    private static final String DELETE_PARTNERS_BY_USER_ID = "{ call PKG_PARTNER.delete_partner_by_user_id(?,?,?) }";

    private static final String INSERT_USER_PARTNER = "{ call PKG_PARTNER.insert_partner_user(?,?,?,?) }";

    private final static java.util.logging.Logger LOGGER = java.util.logging.Logger.getLogger(PartnerServiceImpl.class.getName());

}
