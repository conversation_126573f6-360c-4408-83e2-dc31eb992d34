package com.onepay.ma.service.server;

import com.onepay.ma.service.handler.quicklink.ExRateHandler;
import com.onepay.ma.service.handler.quicklink.ExchangeRateHandler;
import com.onepay.ma.service.handler.quicklink.PaymentLinkHandler;
import com.onepay.ma.service.handler.quicklink.StaticLinkHandler;
import com.onepay.ma.service.handler.installment.InstallmentHandler;
import com.onepay.ma.service.handler.pay_collect.PayCollectHandler;
import com.onepay.ma.service.handler.pay_out.PayOutHandler;
import com.onepay.ma.service.handler.startisticChart.ChartStatisticHandler;
import com.onepay.ma.service.handler.transaction.international.*;
import com.onepay.ma.service.util.StringPool;
import com.onepay.ma.service.handler.acquirer.domestic.DomesticAcquirerGetHandler;
import com.onepay.ma.service.handler.approval.ApprovalPromotionGetHandler;
import com.onepay.ma.service.handler.approval.ApprovalPromotionPatchHandler;
import com.onepay.ma.service.handler.cdr.CdrFileLineGetHandler;
import com.onepay.ma.service.handler.cdr.CdrGetHandler;
import com.onepay.ma.service.handler.cdr.CdrPutHandler;
import com.onepay.ma.service.handler.cdr.CdrServiceGetHandler;
import com.onepay.ma.service.handler.common.ErrorHandler;
import com.onepay.ma.service.handler.common.RequestLoggingHandler;
import com.onepay.ma.service.handler.common.ResponseHandler;
import com.onepay.ma.service.handler.file.*;
import com.onepay.ma.service.handler.financial_report.FinancialReportHandler;
import com.onepay.ma.service.handler.merchant.MerchantGetHandler;
import com.onepay.ma.service.handler.merchant.MerchantHander;
import com.onepay.ma.service.handler.merchant.UserPartnerMerchantGetHandler;
import com.onepay.ma.service.handler.mpayNotification.MpayNotificationHandler;
import com.onepay.ma.service.handler.mpayPromotion.MpayPromotionHandler;
import com.onepay.ma.service.handler.orderApproval.OrderApprovalHandler;
import com.onepay.ma.service.handler.partner.PartnerGetHandler;
import com.onepay.ma.service.handler.payment.PaymentHandler;
import com.onepay.ma.service.handler.paypal.PayPalHandler;
import com.onepay.ma.service.handler.permission.*;
import com.onepay.ma.service.handler.promotion.*;
import com.onepay.ma.service.handler.reconcile.ReconcileHandler;
import com.onepay.ma.service.handler.refund.domestic.DomesticRefundGetHandler;
import com.onepay.ma.service.handler.refund.domestic.DomesticRefundHandler;
import com.onepay.ma.service.handler.refund.international.InternationalRefundHandler;
import com.onepay.ma.service.handler.refund.mpay.MpayRefundHandler;
import com.onepay.ma.service.handler.report.domestic.DomesticReportGetHandler;
import com.onepay.ma.service.handler.report.general.GeneralReportHandler;
import com.onepay.ma.service.handler.report.international.InternationalReportGetHandler;
import com.onepay.ma.service.handler.report.mpay.MpayReportHandler;
import com.onepay.ma.service.handler.report.statistics.StatisticsReportDetailDtGetHandler;
import com.onepay.ma.service.handler.report.statistics.StatisticsReportDetailGetHandler;
import com.onepay.ma.service.handler.report.statistics.StatisticsReportGetHandler;
import com.onepay.ma.service.handler.risk.bin.BinGetHandler;
import com.onepay.ma.service.handler.risk.ip.IpGetHandler;
import com.onepay.ma.service.handler.role.*;
import com.onepay.ma.service.handler.store.StoreHandler;
import com.onepay.ma.service.handler.terminal.TerminalHandler;
import com.onepay.ma.service.handler.token.AppTokenHandler;
import com.onepay.ma.service.handler.tokenization.TokenizationHandler;
import com.onepay.ma.service.handler.tokenization.TranTokenizationHandler;
import com.onepay.ma.service.handler.transaction.domestic.DomesticTransactionHandler;
import com.onepay.ma.service.handler.transaction.financial.FinancialTransactionHandler;
import com.onepay.ma.service.handler.vinpearl.VinpearlTransactionHandler;
import com.onepay.ma.service.handler.transaction.mpay.MpayTransactionHandler;
import com.onepay.ma.service.handler.transaction.onebill.OneBillHandler;
import com.onepay.ma.service.handler.transaction.promotion.PromotionTransactionGetHandler;
import com.onepay.ma.service.handler.transaction.promotion.PromotionTransactionPatchHandler;
import com.onepay.ma.service.handler.user.*;
import com.onepay.ma.service.models.OneAMConfig;
import com.onepay.ma.service.models.RefundConfig;
import com.onepay.ma.service.models.ServerConfig;
import com.onepay.ma.service.service.*;
import com.onepay.ma.service.service.financial.FinancialTransactionService;
import com.onepay.ma.service.service.impl.MerchantPermitService;
import com.onepay.ma.service.service.mpayPromotion.MpayPrReportService;
import com.onepay.ma.service.service.mpayPromotion.MpayPromotionService;
import com.onepay.ma.service.service.partner.PartnerService;
import com.onepay.ma.service.service.partner.UserPartnerService;
import com.onepay.ma.service.util.CacheGuava;
import com.onepay.ma.service.util.ParamsPool;
import com.onepay.ma.service.util.RoutePool;
import com.onepay.ma.service.vertx.rxjava.ext.jdbc.JDBCClient;
import com.zaxxer.hikari.HikariDataSource;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.http.HttpServerOptions;
import io.vertx.rx.java.ObservableFuture;
import io.vertx.rx.java.RxHelper;
import io.vertx.rxjava.core.AbstractVerticle;
import io.vertx.rxjava.core.Vertx;
import io.vertx.rxjava.core.http.HttpServerRequest;
import io.vertx.rxjava.ext.web.Router;
import io.vertx.rxjava.ext.web.handler.BodyHandler;
import io.vertx.rxjava.ext.web.handler.ResponseTimeHandler;
import io.vertx.rxjava.ext.web.handler.TimeoutHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.metadata.HikariDataSourcePoolMetadata;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.jms.Queue;
import javax.sql.DataSource;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by huynguyen on 3/5/16.
 */
@Component
@DependsOn(value = "queueServer")
public class ServiceServer extends AbstractVerticle {

        private final static Logger LOGGER = Logger.getLogger(ServiceServer.class.getName());
        @Autowired
        private UserService userService;

        @Autowired
        private InternationalTransactionService internationalTransactionService;
        @Autowired
        private InternationalRefundService internationalRefundService;
        @Autowired
        private DomesticRefundService domesticRefundService;
        @Autowired
        private MerchantService merchantService;
        @Autowired
        private PartnerService partnerService;
        @Autowired
        private TerminalService terminalService;
        @Autowired
        private DomesticReportService domesticReportService;
        @Autowired
        private InternationalReportService internationalReportService;
        @Autowired
        private AcquirerService acquirerService;
        @Autowired
        private CdrService cdrService;
        @Autowired
        private PromotionTransactionService promotionTransactionService;
        @Autowired
        private PromotionService promotionService;
        @Autowired
        private IpService ipService;
        @Autowired
        private BinService binService;
        @Autowired
        private PromotionDiscountService promotionDiscountService;
        @Autowired
        private PromotionMerchantService promotionMerchantService;
        @Autowired
        private PromotionRuleService promotionRuleService;
        @Autowired
        private PromotionRuleParamService promotionRuleParamService;
        @Autowired
        private ApprovalService approvalService;
        @Autowired
        @Qualifier(value = "refundConfig")
        private RefundConfig refundConfig;
        @Autowired
        private RoleService roleService;
        @Autowired
        private StoreHandler storeHandler;
        @Autowired
        private MpayRefundHandler mpayRefundHandler;
        @Autowired
        private ReconcileHandler reconcileHandler;
        @Autowired
        private FileHandler fileHandler;
        @Autowired
        private OneBillHandler oneBillHandler;

        // @Autowired
        // private DataSourceConfigUtil dataSourceConfig;
        @Autowired
        private PermissionService permissionService;
        @Autowired
        private PermissionRoleService permissionRoleService;
        @Autowired
        private UserRoleService userRoleService;
        @Autowired
        private UserPermissionService userPermissionService;
        @Autowired
        private UserPartnerService userPartnerService;
        @Autowired
        private OneAMConfig oneAMConfig;
        @Autowired
        private ServerConfig serverConfig;
        @Autowired
        private FileService fileService;
        @Autowired
        private UserMerchantService userMerchantService;
        @Autowired
        private UserTerminalService userTerminalService;
        @Autowired
        @Qualifier(value = "cacheGuava")
        private CacheGuava cacheGuava;
        @Autowired
        @Qualifier(value = "downloadFastQueueIn")
        private Queue downloadFastInQueue;
        @Autowired
        @Qualifier(value = "downloadFastQueueOut")
        private Queue downloadFastOutQueue;
        @Autowired
        @Qualifier(value = "downloadSlowQueueIn")
        private Queue downloadSlowInQueue;
        @Autowired
        @Qualifier(value = "downloadSlowQueueOut")
        private Queue downloadSlowOutQueue;
        @Autowired
        @Qualifier(value = "onlineDataSource")
        private DataSource onlineDataSource;
        @Autowired
        @Qualifier(value = "backUpDataSource")
        private DataSource backUpDataSource;
        @Autowired
        @Qualifier(value = "readOnlyDataSource")
        private DataSource readOnlyDataSource;
        @Autowired
        @Qualifier(value = "prDataSource")
        private DataSource prDataSource;

        @Autowired
        @Qualifier(value = "payCollectDataSource")
        private DataSource payCollectDataSource;

        @Autowired
        @Qualifier(value = "mpDataSource")
        private DataSource merchantPortalDataSource;

        // @Autowired
        // @Qualifier(value = "cdrDataSource")
        // private DataSource cdrDataSource;
        @Autowired
        @Qualifier(value = "onereconDataSource")
        private DataSource onereconDataSource;
        @Autowired
        private MerchantPermitService merchantPermitService;
        @Autowired
        private RefundApprovalService refundApprovalService;
        @Autowired
        private PromotionHandler promotionHandler;
        @Autowired
        private MidService midService;
        @Autowired
        private PaymentHandler paymentHandler;
        @Autowired
        private MpayTransactionHandler mpayTransactionHandler;
        @Autowired
        private AppTokenHandler appTokenHandler;
        @Autowired
        private MerchantHander merchantHander;

        @Autowired
        private MpayPromotionService mpayPromotionService;

        @Autowired
        private MpayNotificationHandler mpayNotificationHandler;

        @Autowired
        private TokenService internationalTokenService;

        @Autowired
        private MpayPromotionHandler mpayPromotionHandler;

        @Autowired
        private MpayPrReportService mpayPrReportService;

        @Autowired
        private FinancialTransactionHandler financialTransactionHandler;

        @Autowired
        private FinancialTransactionService financialTransactionService;

        @Autowired
        private InternationalAuthPaymentService internationalAuthPaymentService;

        @Autowired
        private UserServiceV2 userServiceV2;

        @Autowired
        private EmailService emailService;

        @Autowired
        private StatisticsReportService statisticsReportService;

        @Autowired
        private MpayReportHandler mpayReportHandler;

        @Autowired
        private PayPalHandler payPalHandler;

        @Autowired
        private GeneralReportHandler generalReportHandler;

        @Autowired
        private InternationalTransactionHander internationalTransactionHander;

        @Autowired
        private DomesticTransactionHandler domesticTransactionHandler;

        @Autowired
        private OrderApprovalHandler orderApprovalHandler;

        @Autowired
        private DomesticRefundHandler domesticRefundHandler;

        @Autowired
        private ChartStatisticHandler chartStatisticHandler;

        @Autowired
        private PayCollectHandler payCollectHandler;

        @Autowired
        private PayOutHandler payOutHandler;

        @Autowired
        private InternationalRefundHandler internationalRefundHandler;

        @Autowired
        private ExchangeRateHandler exchangeRateHandler;

        @Autowired
        private VinpearlTransactionHandler vinpearlTransactionHandler;

        public Vertx getCurrentVertx() {
                return this.vertx;
        }

        @Override
        public void start() throws Exception {
                Thread.sleep(2000);

                LOGGER.log(Level.INFO, "--------------------- START SERVER -------------------------");
                super.start();
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientO = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), onlineDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientR = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), readOnlyDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientPr = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), prDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientStandBy = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), backUpDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientOnere = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), onereconDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientPc = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), payCollectDataSource);
                com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientMp = com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(), merchantPortalDataSource);

                // com.onepay.ma.service.vertx.ext.jdbc.JDBCClient jdbcClientCdrV =
                // com.onepay.ma.service.vertx.ext.jdbc.JDBCClient.create((io.vertx.core.Vertx) vertx.getDelegate(),
                // cdrDataSource);

                JDBCClient jdbcClientReadOnly = JDBCClient.newInstance(jdbcClientR);
                JDBCClient jdbcClientBackUp = JDBCClient.newInstance(jdbcClientStandBy);
                JDBCClient jdbcClientOnline = JDBCClient.newInstance(jdbcClientO);
                JDBCClient jdbcClientPromotion = JDBCClient.newInstance(jdbcClientPr);
                // JDBCClient jdbcClientCdr = JDBCClient.newInstance(jdbcClientCdrV);
                JDBCClient jdbcClientOnerecon = JDBCClient.newInstance(jdbcClientOnere);
                JDBCClient jdbcClientPayCollect = JDBCClient.newInstance(jdbcClientPc);
                JDBCClient jdbcClientMerchantPortal = JDBCClient.newInstance(jdbcClientMp);
                Router router = Router.router(vertx);

                // setup MainRouter

                router.route().handler(BodyHandler.create());
                router.route().handler(ResponseTimeHandler.create());
                router.route().handler(TimeoutHandler.create(serverConfig.getTimeout()));
                router.route().handler(RequestLoggingHandler.create());

                router.route().handler(routingContext -> {
                        final HttpServerRequest serverRequest = routingContext.request();
                        String requestId = serverRequest.getHeader(ParamsPool.X_REQUEST_ID);
                        String userId = serverRequest.getHeader(ParamsPool.X_USER_ID);
                        String ipAddress = serverRequest.getHeader(ParamsPool.X_REAL_IP);
                        String partnerId = serverRequest.getHeader(ParamsPool.X_PARTNER_ID);
                        routingContext.put(ParamsPool.X_REQUEST_ID, requestId);
                        routingContext.put(ParamsPool.X_USER_ID, userId);
                        routingContext.put(ParamsPool.X_REAL_IP, ipAddress);
                        routingContext.put(ParamsPool.X_PARTNER_ID, partnerId);
                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE + StringPool.BLANK, ParamsPool.JSON_HEADER);
                        routingContext.put(ParamsPool.BACK_UP_DATASOURCE_NAME, jdbcClientBackUp);
                        routingContext.put(ParamsPool.PROMOTION_DATASOURCE_NAME, jdbcClientPromotion);
                        // routingContext.put(ParamsPool.CDR_DATASOURCE_NAME, jdbcClientCdr);
                        routingContext.put(ParamsPool.ONLINE_DATASOURCE_NAME, jdbcClientOnline);
                        routingContext.put(ParamsPool.READ_ONLY_DATASOURCE_NAME, jdbcClientReadOnly);
                        routingContext.put(ParamsPool.ONERECON_DATASOURCE_NAME, jdbcClientOnerecon);
                        routingContext.put(ParamsPool.PAYCOLLECT_DATASOURCE_NAME, jdbcClientPayCollect);
                        routingContext.put(ParamsPool.MERCHANT_PORTAL_DATASOURCE_NAME, jdbcClientMerchantPortal);

                        routingContext.put(ParamsPool.BACK_UP_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) backUpDataSource));
                        routingContext.put(ParamsPool.PROMOTION_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) prDataSource));
                        routingContext.put(ParamsPool.ONLINE_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) onlineDataSource));
                        routingContext.put(ParamsPool.READ_ONLY_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) readOnlyDataSource));
                        routingContext.put(ParamsPool.ONERECON_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) onereconDataSource));
                        routingContext.put(ParamsPool.PAYCOLLECT_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) payCollectDataSource));
                        routingContext.put(ParamsPool.ONEBILL_METADATA, new HikariDataSourcePoolMetadata((HikariDataSource) merchantPortalDataSource));

                        routingContext.next();
                });


                UserRouter(router);

                ApprovalRouter(router);

                transactionDomesticRouter(router);

                transactionInternationalRouter(router);

                authPaymentInternationalRouter(router);

                riskRouter(router);

                paypalRouter(router);

                reportDomesticRouter(router);

                reportInternationalRouter(router);

                refundInternationalRouter(router);

                refundDomesticRouter(router);

                acquirerDomesticRouter(router);

                cdrRouter(router);

                transactionIPromotionRouter(router);

                promotionRouter(router);

                permissionRouter(router);

                roleRouter(router);

                downloadRouter(router);

                merchantRouter(router);

                partnerRouter(router);

                mpayTransactionRouter(router);

                mpayReportRouter(router);

                mpayPrTransactionRouter(router);

                mpayPrReportRouter(router);

                mpayOrderRouter(router);

                terminalRouter(router);

                storeRouter(router);

                userTokenizationRouter(router);

                appTokenRouter(router);

                mpayNotificationRouter(router);

                financialTransactionRouter(router);

                UserV2Router(router);

                mPayStatisicsRouter(router);

                statisticChartRouter(router);

                payCollectRouter(router);

                refundMpayRouter(router);

                reconcileRouter(router);

                generalReport(router);

                billing(router);

                orderApprovalRouter(router);

                installmentRouter(router);

                payoutRouter(router);

                quickLinkRouter(router);

                vinpearlRouter(router);

                financialReportRouter(router);

                router.route().failureHandler(ErrorHandler.create());
                router.route().last().handler(ResponseHandler.create(fileService));

                ObservableFuture<io.vertx.rxjava.core.http.HttpServer> observable = RxHelper.observableFuture();
                observable.subscribe(
                                server -> {
                                        // Server is listening
                                        LOGGER.log(Level.INFO, "--------------------- SERVER START SUCCESS AT"
                                                        + StringPool.SPACE + serverConfig.getIp() + StringPool.COLON
                                                        + serverConfig.getPort() + " -------------------------" + StringPool.NEW_LINE);
                                },
                                failure -> {
                                        // Server could not start
                                        LOGGER.log(Level.SEVERE, "--------------------- SERVER START FAILED ------------------------" + StringPool.NEW_LINE, failure);
                                });


                // router.route("/eventbus/*").handler(eventBusHandler());

                // start server
                io.vertx.rxjava.core.http.HttpServer server = vertx.createHttpServer(new HttpServerOptions()
                                .setIdleTimeout(65)
                                .setPort(serverConfig.getPort()).setHost(serverConfig.getIp()).setTcpKeepAlive(serverConfig.isKeepAlive()));
                server.requestHandler(req -> {
                        req.setExpectMultipart(true);
                });
                server.requestHandler(router::accept);
                server.listen(observable.toHandler());


        }

        /**
         * Config Router Merchant for application
         *
         * @return router
         */
        private void ApprovalRouter(Router router) {


                router.route(HttpMethod.GET, RoutePool.APPROVAL_PROMOTION_ROUTE)
                                .handler(ApprovalPromotionGetHandler.create(approvalService, userService));

                router.route(HttpMethod.PATCH, RoutePool.APPROVAL_PROMOTION_ROUTE_ID)
                                .handler(ApprovalPromotionPatchHandler.create(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, promotionRuleParamService, approvalService));


        }

        /**
         * @return
         */
        private void UserRouter(Router router) {

                // User PROFILE
                router.route(HttpMethod.GET, RoutePool.USER_PROFILE_ROUTE)
                                .handler(UserProfileGetHandler.create(userService));
                //
                // User
                router.route(HttpMethod.GET, RoutePool.USER_ROUTE)
                                .handler(UserGetHandler.create(userService, userPermissionService));
                // User
                router.route(HttpMethod.GET, RoutePool.USER_ROUTE_SID)
                                .handler(UserGetHandler.create(userService, userPermissionService));
                router.route(HttpMethod.PATCH, RoutePool.USER_ROUTE_SID)
                                .handler(UserPatchHandler.create(userService, userRoleService, cacheGuava, userMerchantService, userTerminalService, userPartnerService));

                // User Session
                router.route(HttpMethod.GET, RoutePool.USER_SESSION_ROUTE_ID)
                                .handler(UserSessionGetHandler.create(oneAMConfig, userService, userPermissionService, cacheGuava, merchantService, userTerminalService));

                router.route(HttpMethod.POST, RoutePool.USER_SESSION_ROUTE_CODE)
                                .handler(UserSessionPostHandler.create(oneAMConfig, userService, userPermissionService, merchantService));


                // User Permission
                router.route(HttpMethod.GET, RoutePool.USER_PERMISSION_ROUTE)
                                .handler(UserPermissionGetHandler.create(userPermissionService, cacheGuava));

                router.route(HttpMethod.DELETE, RoutePool.USER_SESSION_ROUTE_ID)
                                .handler(UserSessionDeleteHandler.create(cacheGuava, oneAMConfig));

                router.route(HttpMethod.DELETE, RoutePool.USER_SESSION_ROUTE)
                                .handler(UserSessionDeleteHandler.create(cacheGuava, oneAMConfig));

                router.route(HttpMethod.DELETE, RoutePool.USER_ROUTE_SID)
                                .handler(UserDeleteHandler.create(cacheGuava, oneAMConfig));

                router.route(HttpMethod.DELETE, RoutePool.USER_ROUTE)
                                .handler(UserDeleteHandler.create(cacheGuava, oneAMConfig));

                router.route(HttpMethod.GET, RoutePool.USER_FILE_ROUTE).handler(UserFileGetHandler.create(fileService));

        }

        /**
         * DomesticTransaction domestic router
         *
         * @return
         */
        private void transactionDomesticRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE)
                                .handler(fileHandler.downloadDomesticTransaction());
                router.route(HttpMethod.POST, RoutePool.SAMSUNG_DOWNLOAD_DOMESTIC_TRANSACTION_ROUTE)
                                .handler(fileHandler.downloadSSDomesticTransaction());

                // Transaction history
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_DOMESTIC_HISTORY_ROUTE)
                                .handler(domesticTransactionHandler.getHistory());
                // Transaction
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_DOMESTIC_ROUTE)
                                .handler(domesticTransactionHandler.get());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_DOMESTIC_ROUTE)
                                .handler(domesticTransactionHandler.getSamsungTransaction());

                // Transaction Get detail
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_DOMESTIC_ROUTE_ID)
                                .handler(domesticTransactionHandler.get());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_DOMESTIC_ROUTE_ID)
                                .handler(domesticTransactionHandler.getSamsungTransaction());


                // Transaction Get detail
                router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_DOMESTIC_ROUTE_ID)
                                .handler(domesticTransactionHandler.patch());


        }

        private void paypalRouter(Router router) {


                // DISPUTE
                router.route(HttpMethod.POST, RoutePool.LISTEN_DISPUTE_PAYPAL_CREATE_ROUTE).handler(payPalHandler.postDispute());
                router.route(HttpMethod.POST, RoutePool.LISTEN_DISPUTE_PAYPAL_UPDATE_ROUTE).handler(payPalHandler.postDispute());
                router.route(HttpMethod.POST, RoutePool.LISTEN_DISPUTE_PAYPAL_RESOLVE_ROUTE).handler(payPalHandler.postDispute());
                router.route(HttpMethod.POST, RoutePool.LISTEN_MERCHANT_ONBOARD_PAYPAL_ROUTE).handler(payPalHandler.postMerchantOnboard());
                router.route(HttpMethod.POST, RoutePool.MERCHANT_ONBOARD_PAYPAL_ROUTE).handler(payPalHandler.registerMerchantOnboard());
                router.route(HttpMethod.POST, RoutePool.MERCHANT_POST_PAYPAL_ROUTE).handler(payPalHandler.registerMerchantPost());
                router.route(HttpMethod.POST, RoutePool.PAYPAL_SENTMAIL_REGISTER_ROUTE).handler(payPalHandler.sentMailRegister());
                router.route(HttpMethod.POST, RoutePool.PAYPAL_SENT_MESSAGE_ROUTE).handler(payPalHandler.sentMessageDispute());


        }

        /**
         * Domestic Report router
         *
         * @return
         */
        private void reportDomesticRouter(Router router) {


                // Transaction
                router.route(HttpMethod.GET, RoutePool.REPORT_DOMESTIC_ROUTE)
                                .handler(DomesticReportGetHandler.create(domesticReportService, merchantService));

                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_DOMESTIC_REPORT_ROUTE)
                                .handler(FilePostDomesticReportHandler.create(domesticReportService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));


        }

        /**
         * International Report router
         *
         * @return
         */
        private void reportInternationalRouter(Router router) {


                // Transaction
                router.route(HttpMethod.GET, RoutePool.REPORT_INTERNATIONAL_ROUTE)
                                .handler(InternationalReportGetHandler.create(internationalReportService, merchantService));

                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_REPORT_ROUTE)
                                .handler(FilePostInternationalReportHandler.create(internationalReportService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));


        }

        /**
         * International transaction router
         *
         * @return
         */
        private void transactionInternationalRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_TRANSACTION_ROUTE)
                                .handler(FilePostInternationalTransactionHandler.create(internationalTransactionService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_INTERNATIONAL_HISTORY_ROUTE)
                                .handler(InternationalTransactionHistoryGetHandler.create(internationalTransactionService, merchantService, userService));
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_AUTHORIZE_HISTORY_ROUTE)
                                .handler(InternationalAuthorizeHistoryGetHandler.create(internationalTransactionService, merchantService, userService));
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_CAPTURE_HISTORY_ROUTE)
                                .handler(InternationalCaptureHistoryGetHandler.create(internationalTransactionService, merchantService, userService));
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_REFUND_CAPTURE_HISTORY_ROUTE)
                                .handler(InternationalRefundCaptureHistoryGetHandler.create(internationalTransactionService, merchantService, userService));
                // User
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_INTERNATIONAL_ROUTE)
                                .handler(InternationalTransactionGetHandler.create(internationalTransactionService, merchantService, midService, serverConfig));

                // User Session
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(InternationalTransactionGetHandler.create(internationalTransactionService, merchantService, midService, serverConfig));

                router.route(HttpMethod.GET, RoutePool.BRAND_CONFIG_ROUTER)
                                .handler(internationalTransactionHander.getBrandConfig());

                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalTransactionHander.getSSTrans());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_AUTHORIZE_TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalTransactionHander.getSSAuthorizeTrans());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_CAPTURE_TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalTransactionHander.getSSCaptureTrans());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_REFUND_CAPTURE_TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalTransactionHander.getSSRefundCaptureTrans());

                // router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_INTERNATIONAL_ROUTE_ID)
                // .handler(InternationalTransactionPatchHandler.create(internationalTransactionService,
                // refundConfig, merchantService, userService));
                router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalTransactionHander.patch());


        }


        /**
         * International transaction router
         *
         * @return
         */
        private void authPaymentInternationalRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_PAYMENT_ROUTE)
                                .handler(FilePostInternationalAuthPaymentHandler.create(internationalAuthPaymentService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue, internationalTransactionService));

                // User
                router.route(HttpMethod.GET, RoutePool.PAYMENT_INTERNATIONAL_ROUTE)
                                .handler(paymentHandler.get());

                // User Session
                router.route(HttpMethod.GET, RoutePool.PAYMENT_INTERNATIONAL_ROUTE_ID)
                                .handler(paymentHandler.get());


        }

        /**
         * International refund router
         *
         * @return
         */
        private void refundInternationalRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_REFUND_ROUTE)
                                .handler(FilePostInternationalRefundHandler.create(internationalRefundService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_EMAIL_TEMPLATE_REFUND_ROUTE)
                                .handler(FilePostInternationalEmailTemplateRefundHandler.create(merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                // User
                router.route(HttpMethod.GET, RoutePool.REFUND_INTERNATIONAL_ROUTE)
                                .handler(internationalRefundHandler.get());

                // User Session
                router.route(HttpMethod.GET, RoutePool.REFUND_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalRefundHandler.get());

                router.route(HttpMethod.PATCH, RoutePool.REFUND_INTERNATIONAL_ROUTE_ID)
                                .handler(internationalRefundHandler.patch());


        }

        /**
         * International tokenization user router
         *
         * @return
         */
        private void userTokenizationRouter(Router router) {


                router.get(RoutePool.USERTOKEN_INTERNATIONAL_ROUTE_ID)
                                .handler(TokenizationHandler.create(internationalTokenService, userService));
                router.get(RoutePool.USERTOKEN_INTERNATIONAL_ROUTE)
                                .handler(TokenizationHandler.create(internationalTokenService, userService));

                router.get(RoutePool.TOKENTRAN_INTERNATIONAL_ROUTE)
                                .handler(TranTokenizationHandler.create(internationalTokenService, userService));

                // router.route(HttpMethod.GET, RoutePool.TRANSACTION_INTERNATIONAL_ROUTE_ID)
                // .handler(InternationalTransactionGetHandler.create(internationalTransactionService,
                // merchantService, midService, serverConfig));
                //// router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_TRANSACTION_ROUTE)
                //// .handler(FilePostMpayTransactionHandler.create(mpayTransactionService, serverConfig,
                // downloadFastInQueue, downloadFastOutQueue, cacheGuava,
                //// fileService, downloadSlowInQueue, downloadSlowOutQueue, userService, merchantService));
                //
                // router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_ROUTE)
                // .handler(mpayTransactionHandler.get());
                //
                //
                // router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_ROUTE_ID)
                // .handler(mpayTransactionHandler.get());
                //
                //
                // // get History
                // router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_HISTORY_ROUTE)
                // .handler(mpayTransactionHandler.getHistories());


        }

        private void appTokenRouter(Router router) {


                router.post(RoutePool.APP_TOKEN_ROUTE).handler(appTokenHandler.post());
                router.delete(RoutePool.APP_TOKEN_ROUTE_ID).handler(appTokenHandler.delete());


        }

        private void mpayNotificationRouter(Router router) {


                router.get(RoutePool.MPAY_NOTIFY_ROUTE).handler(mpayNotificationHandler.getConfig());
                router.get(RoutePool.MPAY_NOTIFY_ROUTE_ID).handler(mpayNotificationHandler.getConfig());
                router.post(RoutePool.MPAY_NOTIFY_ROUTE).handler(mpayNotificationHandler.postConfig());
                router.put(RoutePool.MPAY_NOTIFY_ROUTE_ID).handler(mpayNotificationHandler.putConfig());
                router.delete(RoutePool.MPAY_NOTIFY_ROUTE_ID).handler(mpayNotificationHandler.deleteConfig());

                router.get(RoutePool.MPAY_NOTIFICATION_ROUTE).handler(mpayNotificationHandler.getNotify());
                router.patch(RoutePool.MPAY_NOTIFICATION_ROUTE_ID).handler(mpayNotificationHandler.patchNotify());

                router.post(RoutePool.PUSH_MPAY_NOTIFY_ROUTE).handler(mpayNotificationHandler.pushNotification());

                router.post(RoutePool.PUSH_APP_NOTIFY_ROUTE).handler(mpayNotificationHandler.pushAppNotify());

                router.post(RoutePool.PUSH_CUSTOMER_MPAY_NOTIFY_ROUTE).handler(mpayNotificationHandler.pushCustomerNotification());

                router.post(RoutePool.OP_NOTIFY_ROUTE).handler(mpayNotificationHandler.postMobileNotification());

                router.get(RoutePool.OP_NOTIFY_ROUTE).handler(mpayNotificationHandler.getMobileNotification());

                router.get(RoutePool.OP_NOTIFY_ROUTE_ID).handler(mpayNotificationHandler.getMobileNotification());

                router.get(RoutePool.CUSTOMER_MPAY_NOTIFY_MSG_ROUTE).handler(mpayNotificationHandler.getCustomerMsg());

                router.get(RoutePool.CUSTOMER_MPAY_NOTIFY_MSG_ROUTE_ID).handler(mpayNotificationHandler.getCustomerMsg());


                router.get(RoutePool.PUSH_CUSTOMER_MPAY_NOTIFY_ROUTE).handler(mpayNotificationHandler.getCustomerNotify());


                router.patch(RoutePool.PUSH_CUSTOMER_MPAY_NOTIFY_ROUTE_ID).handler(mpayNotificationHandler.patchCustomerNotification());


        }


        /**
         * Domestic refund router
         *
         * @return
         */
        private void refundDomesticRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_DOMESTIC_REFUND_ROUTE)
                                .handler(FilePostDomesticRefundHandler.create(domesticRefundService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                // User
                router.route(HttpMethod.GET, RoutePool.REFUND_DOMESTIC_ROUTE)
                                .handler(DomesticRefundGetHandler.create(domesticRefundService, merchantService, serverConfig));

                // User Session
                router.route(HttpMethod.GET, RoutePool.REFUND_DOMESTIC_ROUTE_ID)
                                .handler(DomesticRefundGetHandler.create(domesticRefundService, merchantService, serverConfig));

                router.route(HttpMethod.PATCH, RoutePool.REFUND_DOMESTIC_ROUTE_ID)
                                .handler(domesticRefundHandler.patchApproval());

                router.route(HttpMethod.PATCH, RoutePool.REFUND_DOMESTIC_UPDATE_ROUTE_ID)
                                .handler(domesticRefundHandler.patchRefund());


        }

        private void mpayPrTransactionRouter(Router router) {


                router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_PR_ROUTE)
                                .handler(mpayPromotionHandler.get());


                router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_PR_ROUTE_ID)
                                .handler(mpayPromotionHandler.get());


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_PR_TRANSACTION_ROUTE)
                                .handler(FilePostMpayPrTransactionHandler.create(mpayPromotionService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava,
                                                fileService, downloadSlowInQueue, downloadSlowOutQueue, userService, merchantService));

        }

        private void mpayPrReportRouter(Router router) {


                router.route(HttpMethod.GET, RoutePool.REPORT_MPAY_PR_ROUTE)
                                .handler(mpayPromotionHandler.getReport());

                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_PR_REPORT_ROUTE)
                                .handler(FilePostMpayPrReportHandler.create(mpayPrReportService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava,
                                                fileService, downloadSlowInQueue, downloadSlowOutQueue, userService, merchantService));

        }

        private void mpayReportRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_REPORT_ROUTE)
                                .handler(mpayReportHandler.download());
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_REPORT_PAYMENT_ROUTE)
                                .handler(mpayReportHandler.downloadPayment());

                router.route(HttpMethod.GET, RoutePool.REPORT_MPAY_ROUTE)
                                .handler(mpayReportHandler.get());

                router.route(HttpMethod.GET, RoutePool.REPORT_PAYMENT_MPAY_ROUTE)
                                .handler(mpayReportHandler.getPayment());

                                
                router.route(HttpMethod.GET, RoutePool.REPORT_MPAY_ROUTE_MOCA)
                                .handler(mpayReportHandler.getMoca());
                
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_REPORT_ROUTE_MOCA)
                                .handler(mpayReportHandler.download());

        }


        private void mpayTransactionRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_TRANSACTION_ROUTE)
                                .handler(mpayTransactionHandler.downloadTransaction());
                router.route(HttpMethod.POST, RoutePool.SAMSUNG_DOWNLOAD_MPAY_TRANSACTION_ROUTE)
                                .handler(mpayTransactionHandler.downloadSSTransaction());

                router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.get());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.getSSTrans());

                router.route(HttpMethod.GET, RoutePool.LIST_TRANSACTION_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.listTransaction());

                router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_ROUTE_ID)
                                .handler(mpayTransactionHandler.get());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_MPAY_ROUTE_ID)
                                .handler(mpayTransactionHandler.getSSTrans());

                router.patch(RoutePool.TRANSACTION_MPAY_ROUTE_ID).handler(mpayTransactionHandler.patch());

                router.route(HttpMethod.GET, RoutePool.TRANSACTION_UPDATE_MPAY_ROUTE_ID).handler(mpayTransactionHandler.update());

                // get History
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_HISTORY_ROUTE)
                                .handler(mpayTransactionHandler.getHistories());

                //Moca special user
                router.route(HttpMethod.GET, RoutePool.MOCA_TRANSACTION_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.getMocaTrans());
                router.route(HttpMethod.GET, RoutePool.MOCA_TRANSACTION_MPAY_ROUTE_ID)
                                .handler(mpayTransactionHandler.getMocaTrans());
                router.route(HttpMethod.POST, RoutePool.MOCA_DOWNLOAD_MPAY_TRANSACTION_ROUTE)
                                .handler(mpayTransactionHandler.downloadMocaTransaction());
                router.patch(RoutePool.MOCA_TRANSACTION_MPAY_ROUTE_ID).handler(mpayTransactionHandler.patch());

        }

        private void mpayOrderRouter(Router router) {


                router.route(HttpMethod.GET, RoutePool.QUERY_ORDER_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.queryOrder());

                router.route(HttpMethod.GET, RoutePool.ORDER_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.getOrder());


                router.route(HttpMethod.GET, RoutePool.ORDER_MPAY_ROUTE_ID)
                                .handler(mpayTransactionHandler.getOrder());


                router.route(HttpMethod.POST, RoutePool.ORDER_MPAY_ROUTE)
                                .handler(mpayTransactionHandler.postOrder());


                // get History
                // router.route(HttpMethod.GET, RoutePool.TRANSACTION_MPAY_HISTORY_ROUTE)
                // .handler(mpayTransactionHandler.getHistories());


        }

        /**
         * Mpay refund router
         *
         * @return
         */
        private void refundMpayRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_MPAY_REFUND_ROUTE)
                                .handler(mpayRefundHandler.download());

                router.route(HttpMethod.GET, RoutePool.REFUND_MPAY_ROUTE)
                                .handler(mpayRefundHandler.get());

                router.route(HttpMethod.GET, RoutePool.REFUND_MPAY_ROUTE_ID)
                                .handler(mpayRefundHandler.get());

                router.route(HttpMethod.GET, RoutePool.UPDATE_MPAY_REFUND_ROUTE)
                                .handler(mpayRefundHandler.update());


                router.route(HttpMethod.GET, RoutePool.MPAY_REFUND_APPROVAL_ROUTE_ID)
                                .handler(mpayRefundHandler.getApproval());


                router.route(HttpMethod.GET, RoutePool.MPAY_REFUND_APPROVAL_ROUTE)
                                .handler(mpayRefundHandler.getApproval());


                router.route(HttpMethod.PATCH, RoutePool.MPAY_REFUND_APPROVAL_ROUTE_ID)
                                .handler(mpayRefundHandler.patch());
                // router.route(HttpMethod.PATCH, RoutePool.REFUND_DOMESTIC_ROUTE_ID)
                // .handler(DomesticRefundApprovalPatchHandler.create(merchantPermitService, refundApprovalService,
                // userService, refundConfig,domesticTransactionService, domesticRefundService ));


        }

        /**
         * risk router
         *
         * @return
         */
        private void riskRouter(Router router) {

                // router.route(RoutePool.RISK_ROUTE + "*").handler(routingContext -> {
                // Observable.using(SQLConnectionFactory::new, f -> f.create(clientReadOnly), f-> f.dispose())
                // .subscribe(connReadOnly -> {
                //
                // // save the connection on the context
                // routingContext.put(ParamsPool.CONNECTION_READONLY, connReadOnly);
                //
                // routingContext.next();
                //
                // }, throwable -> {
                // routingContext.fail(throwable);
                // });
                // });

                // Ip
                router.route(HttpMethod.GET, RoutePool.RISK_ROUTE_IP)
                                .handler(IpGetHandler.create(ipService));

                // Bin
                router.route(HttpMethod.GET, RoutePool.RISK_ROUTE_BIN)
                                .handler(BinGetHandler.create(binService));


        }

        /**
         * Domestic acquirer router
         *
         * @param
         * @return
         */
        private void acquirerDomesticRouter(Router router) {


                // Acquirer
                router.route(HttpMethod.GET, RoutePool.ACQUIRER_DOMESTIC_ROUTE)
                                .handler(DomesticAcquirerGetHandler.create(acquirerService, merchantService));


        }

        /**
         * Promotion transaction router
         *
         * @return
         */
        private void transactionIPromotionRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_INTERNATIONAL_TRANSACTION_PROMOTION_ROUTE)
                                .handler(FilePostPromotionTransactionHandler.create(promotionTransactionService, merchantService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                // User
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_PROMOTION_ROUTE)
                                .handler(PromotionTransactionGetHandler.create(promotionTransactionService, merchantService));

                router.route(HttpMethod.GET, RoutePool.TRANSACTION_PROMOTION_ROUTE_ID)
                                .handler(PromotionTransactionGetHandler.create(promotionTransactionService, merchantService));

                router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_PROMOTION_ROUTE_ID)
                                .handler(PromotionTransactionPatchHandler.create(promotionTransactionService));


        }

        /**
         * Store router
         *
         * @return
         */
        private void generalReport(Router router) {


                // Merchant List
                router.route(HttpMethod.GET, RoutePool.REPORT_GENERAL_ROUTE)
                                .handler(generalReportHandler.getGeneralReport());
                // Merchant List
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_REPORT_GENERAL_ROUTE)
                                .handler(generalReportHandler.downloadGeneralReport());


        }

        /**
         * Store router
         *
         * @return
         */
        private void billing(Router router) {


                // CLIENT List
                router.route(HttpMethod.GET, RoutePool.CLIENT_BILLING_ROUTE)
                                .handler(oneBillHandler.listClient());
                // Merchant List
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_BILLING_ROUTE)
                                .handler(oneBillHandler.get());
                // Merchant List
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_BILLING_ROUTE_ID)
                                .handler(oneBillHandler.get());
                // Download
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_TRANSACTION_BILLING_ROUTE)
                                .handler(oneBillHandler.download());
                // Patch
                router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_BILLING_ROUTE_ID)
                                .handler(oneBillHandler.patch());
                // HISTORY
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_BILLING_HISTORY_ROUTE)
                                .handler(oneBillHandler.getHistory());


        }

        /**
         * CDR router
         *
         * @return
         */
        private void cdrRouter(Router router) {


                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_CDR_ROUTE)
                                .handler(FilePostCdrHandler.create(cdrService, serverConfig, downloadFastInQueue, downloadFastOutQueue, cacheGuava, fileService, downloadSlowInQueue, downloadFastOutQueue));
                // CDR
                router.route(HttpMethod.GET, RoutePool.CDR_ROUTE_COMPARE)
                                .handler(CdrGetHandler.create(cdrService));

                router.route(HttpMethod.GET, RoutePool.CDR_ROUTE_FILE_LINE)
                                .handler(CdrFileLineGetHandler.create(cdrService));
                // CDR
                router.route(HttpMethod.GET, RoutePool.CDR_ROUTE_SERVICE)
                                .handler(CdrServiceGetHandler.create(cdrService));

                router.route(HttpMethod.PUT, RoutePool.CDR_ROUTE_COMPARE)
                                .handler(CdrPutHandler.create(cdrService));

        }

        /**
         * Merchant router
         *
         * @return
         */
        private void merchantRouter(Router router) {


                // Merchant List
                router.route(HttpMethod.GET, RoutePool.MERCHANT_ROUTE)
                                .handler(MerchantGetHandler.create(merchantService));


                // Currencies Merchant List
                router.route(HttpMethod.GET, RoutePool.CURRENCIES_MERCHANT_ROUTE_ID)
                                .handler(merchantHander.getCurrencieMerchant());


                // Customer Merchant List
                router.route(HttpMethod.GET, RoutePool.CUSTOMER_MERCHANT_ROUTE_ID)
                                .handler(merchantHander.getCustomerMerchant());


                // Customer Merchant List
                router.route(HttpMethod.GET, RoutePool.USER_MERCHANT_ROUTE)
                                .handler(merchantHander.findUserByMerchant());

                // Customer Merchant List
                router.route(HttpMethod.GET, RoutePool.PARTNER_MERCHANT_ROUTE)
                                .handler(UserPartnerMerchantGetHandler.create(merchantService));
        }

        /**
         * Partner router
         *
         * @param router
         */
        private void partnerRouter(Router router) {
                // List partners
                router.route(HttpMethod.GET, RoutePool.PARTNER_CUSTOMER_ROUTE)
                                .handler(PartnerGetHandler.create(partnerService, userPartnerService));
        }

        /**
         * Terminal router
         *
         * @return
         */
        private void terminalRouter(Router router) {


                // Merchant List
                router.route(HttpMethod.GET, RoutePool.TERMINAL_ROUTE)
                                .handler(TerminalHandler.get(terminalService));

                // Merchant List
                router.route(HttpMethod.GET, RoutePool.TERMINAL_ROUTE_MER_ID)
                                .handler(TerminalHandler.get(terminalService));


        }

        /**
         * Store router
         *
         * @return
         */
        private void storeRouter(Router router) {


                // Merchant List
                router.route(HttpMethod.GET, RoutePool.STORE_ROUTE)
                                .handler(storeHandler.get());


        }

        /**
         * Promotion router
         *
         * @return
         */
        private void promotionRouter(Router router) {


                // Promotion
                router.route(HttpMethod.GET, RoutePool.PROMOTION_ROUTER)
                                .handler(PromotionGetHandler.create(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService));

                router.route(HttpMethod.GET, RoutePool.PROMOTION_ROUTER_ID)
                                .handler(PromotionGetHandler.create(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService));


                router.route(HttpMethod.POST, RoutePool.PROMOTION_ROUTER)
                                .handler(PromotionPostHandler.create(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, approvalService));

                router.route(HttpMethod.PUT, RoutePool.PROMOTION_ROUTER_ID)
                                .handler(PromotionPutHandler.create(promotionService, merchantService, promotionDiscountService, promotionMerchantService, promotionRuleService, promotionRuleParamService, approvalService));

                router.route(HttpMethod.DELETE, RoutePool.PROMOTION_ROUTER_ID)
                                .handler(PromotionDeleteHandler.create(promotionService, approvalService));

                router.get(RoutePool.PROMOTION_CODE_ROUTER).handler(promotionHandler.getCode());
                router.get(RoutePool.PROMOTION_CODE_ROUTER_ID).handler(promotionHandler.getCode());
                router.patch(RoutePool.PROMOTION_CODE_ROUTER_ID).handler(promotionHandler.patchCode());


        }

        /**
         * role router
         *
         * @return
         */
        private void roleRouter(Router router) {


                // Role
                router.route(HttpMethod.GET, RoutePool.ROLE_ROUTE)
                                .handler(RoleGetHandler.create(roleService));

                router.route(HttpMethod.GET, RoutePool.ROLE_ROUTE_ID)
                                .handler(RoleGetHandler.create(roleService));

                router.route(HttpMethod.POST, RoutePool.ROLE_ROUTE)
                                .handler(RolePostHandler.create(roleService));

                router.route(HttpMethod.PUT, RoutePool.ROLE_ROUTE_ID)
                                .handler(RolePutHandler.create(roleService, cacheGuava));

                router.route(HttpMethod.PATCH, RoutePool.ROLE_ROUTE_ID)
                                .handler(RolePatchHandler.create(roleService, permissionRoleService, cacheGuava));

                router.route(HttpMethod.PATCH, RoutePool.ROLE_ROUTE)
                                .handler(RolePatchHandler.create(roleService, permissionRoleService, cacheGuava));


                router.route(HttpMethod.DELETE, RoutePool.ROLE_ROUTE_ID)
                                .handler(RoleDeleteHandler.create(roleService));


        }

        /**
         * permission router
         *
         * @return
         */
        private void permissionRouter(Router router) {


                // Permission List
                router.route(HttpMethod.GET, RoutePool.PERMISSION_ROUTE)
                                .handler(PermissionGetHandler.create(permissionService));
                // Permission Detail
                router.route(HttpMethod.GET, RoutePool.PERMISSION_ROUTE_ID)
                                .handler(PermissionGetHandler.create(permissionService));

                // Permission Put
                router.route(HttpMethod.PUT, RoutePool.PERMISSION_ROUTE_ID)
                                .handler(PermissionPutHandler.create(permissionService, cacheGuava));

                // Permission Post
                router.route(HttpMethod.POST, RoutePool.PERMISSION_ROUTE)
                                .handler(PermissionPostHandler.create(permissionService));


                // Permission Delete
                router.route(HttpMethod.DELETE, RoutePool.PERMISSION_ROUTE_ID)
                                .handler(PermissionDeleteHandler.create(permissionService));

                // Permission patch with id
                router.route(HttpMethod.PATCH, RoutePool.PERMISSION_ROUTE_ID)
                                .handler(PermissionPatchHandler.create(permissionService, permissionRoleService));

                // Permission patch without id
                router.route(HttpMethod.PATCH, RoutePool.PERMISSION_ROUTE)
                                .handler(PermissionPatchHandler.create(permissionService, permissionRoleService));


        }

        /**
         * permission router
         *
         * @return
         */
        private void downloadRouter(Router router) {


                router.route(HttpMethod.GET, RoutePool.FILE_DOWNLOAD_ROUTE)
                                .handler(FileGetHandler.create(serverConfig, fileService));


        }

        private void financialTransactionRouter(Router router) {
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_FINANCICAL_ROUTE)
                                .handler(financialTransactionHandler.get());
                router.route(HttpMethod.GET, RoutePool.TRANSACTION_FINANCICAL_ROUTE_ID)
                                .handler(financialTransactionHandler.get());
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE)
                                .handler(fileHandler.downloadFinancial());

                // SAMSUNG
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_FINANCICAL_ROUTE)
                                .handler(financialTransactionHandler.getSSTrans());
                router.route(HttpMethod.GET, RoutePool.SAMSUNG_TRANSACTION_FINANCICAL_ROUTE_ID)
                                .handler(financialTransactionHandler.getSSTrans());
                // router.post(RoutePool.SAMSUNG_DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE).handler(internationalTransactionHander.downloadSSFinancialTrans());
                router.route(HttpMethod.POST, RoutePool.SAMSUNG_DOWNLOAD_FINANCICAL_TRANSACTION_ROUTE)
                                .handler(fileHandler.downloadSSFinancial());
        }

        /**
         * @return
         */
        private void UserV2Router(Router router) {
                // changePass version 1 - cho hệ thống paymentlink
                router.route(HttpMethod.POST, RoutePool.USER_CHANGE_PASS_ROUTE)
                                .handler(UserChangePassHandler.create(userServiceV2));
                // changePass version 2 - theo password policies
                router.route(HttpMethod.POST, RoutePool.USER_CHANGE_PASSWORD_V2)
                                .handler(UserChangePassV2Handler.changePasswordV2(userServiceV2));
                // create user
                router.route(HttpMethod.POST, RoutePool.USER_V2_ROUTE)
                                .handler(UserV2CreateHandler.create(userServiceV2));

                // get list user by create id
                // router.route(HttpMethod.GET, RoutePool.USER_V2_ROUTE)
                //                 .handler(UserV2GetHandler.create(userServiceV2, userPartnerService));
                // get list user by create id by merchant ID
                router.route(HttpMethod.GET, RoutePool.USER_V2_ROUTE)
                                .handler(UserV2GetHandlerV2.create(userServiceV2, merchantService));
                // upadate user
                router.route(HttpMethod.PUT, RoutePool.USER_V2_UPDATE_ROUTE)
                                .handler(UserV2PutHandler.create(userServiceV2, cacheGuava, merchantService));
                // resetPass
                router.route(HttpMethod.POST, RoutePool.USER_RESET_PASS_ROUTE)
                                .handler(UserResetPassHandler.create(userServiceV2, emailService, merchantService));

        }

        private void mPayStatisicsRouter(Router router) {
                router.route(HttpMethod.GET, RoutePool.STATISTICS_REPORT_ROUTE)
                                .handler(StatisticsReportGetHandler.create(statisticsReportService));
                router.route(HttpMethod.GET, RoutePool.STATISTICS_REPORT_DETAIL_ROUTE)
                                .handler(StatisticsReportDetailGetHandler.create(statisticsReportService));
                router.route(HttpMethod.GET, RoutePool.STATISTICS_REPORT_DETAIL_DT_ROUTE)
                                .handler(StatisticsReportDetailDtGetHandler.create(statisticsReportService));
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_STATISTICS_REPORT_ROUTE)
                                .handler(FilePostStatisticsReportHandler.create(statisticsReportService, serverConfig, downloadFastInQueue, downloadFastOutQueue, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_STATISTICS_DETAIL_REPORT_ROUTE)
                                .handler(FilePostStatisticsDetailReportHandler.create(statisticsReportService, serverConfig, downloadFastInQueue, downloadFastOutQueue, fileService, downloadSlowInQueue, downloadSlowOutQueue));
                router.route(HttpMethod.POST, RoutePool.DOWNLOAD_STATISTICS_DETAIL_DT_REPORT_ROUTE)
                                .handler(FilePostStatisticsDetailDtReportHandler.create(statisticsReportService, serverConfig, downloadFastInQueue, downloadFastOutQueue, fileService, downloadSlowInQueue, downloadSlowOutQueue));

        }


        private void reconcileRouter(Router router) {

                router.get(RoutePool.REPORT_RECONCILE_ROUTE).handler(reconcileHandler.getReconcile());
                router.post(RoutePool.DOWNLOAD_RECONCILE_ROUTE).handler(fileHandler.downloadReconcile());

                router.get(RoutePool.REPORT_RECONCILE_PAYMENT_ROUTE).handler(reconcileHandler.getPayment());
                router.get(RoutePool.REPORT_RECONCILE_PAYMENT_DETAIL_ROUTE).handler(reconcileHandler.getPaymentDetail());
                router.post(RoutePool.DOWNLOAD_RECONCILE_PAYMENT_ROUTE).handler(fileHandler.downloadPaymentReconcile());
                router.post(RoutePool.DOWNLOAD_RECONCILE_PAYMENT_DETAIL_ROUTE).handler(fileHandler.downloadPaymentDetailReconcile());
        }

        private void orderApprovalRouter(Router router) {
                router.post(RoutePool.INTERNATIONAL_ORDER_APPORVAL_ROUTE).handler(orderApprovalHandler.postInternational());
                router.post(RoutePool.DOMESTIC_ORDER_APPORVAL_ROUTE).handler(orderApprovalHandler.postDomestic());
        }

        private void installmentRouter(Router router) {
                router.get(RoutePool.INSTALLMENT_BANK_ROUTE).handler(InstallmentHandler::getAllBank);
        }

        /**
         * Api for Firm Banking Module
         * by Tiennv
         * 30/09/2020
         * 
         * @param router
         */
        private void payoutRouter(Router router) {
                router.post(RoutePool.FUNDS_TRANS_HISTORY_ROUTE).handler(payOutHandler.getFundsTransHistory());
                router.post(RoutePool.FUNDS_TRANS_HISTORY_PENDING).handler(payOutHandler.getFundsTransHistoryForApproval());
                router.put(RoutePool.FUNDS_TRANS_CHANGE_STATUS).handler(payOutHandler.fundsChangeStatus());
                router.get(RoutePool.FUNDS_TRANS_HISTORY_BY_ID_ROUTE).handler(payOutHandler.getFundsTransById());
                router.post(RoutePool.MERCHANT_ACCOUNT_ROUTE).handler(payOutHandler.getMerchantAccount());
                router.post(RoutePool.DOWNLOAD_FUNDS_TRANS_HIS_ROUTE).handler(payOutHandler.getDownloadFundsTransHandler());
                router.post(RoutePool.TRANSFER_ROUTE).handler(payOutHandler.getTransferTransaction());
                router.get(RoutePool.TRANSFER_CHECK_AUTHOR).handler(payOutHandler.partnerInfo());
                router.get(RoutePool.RECEIVED_INFO_ROUTE).handler(payOutHandler.getReceivedInfo());
                router.get(RoutePool.RECEIVED_BANK_ROUTE).handler(payOutHandler.getReceivedBank());
                router.get(RoutePool.GET_MERCHANT_BY_USER).handler(payOutHandler.getMerchantByUser());
                router.get(RoutePool.SUMMARY_FUNDS_TRANS_ROUTE).handler(payOutHandler.getSummaryFundsTransHandler());
                router.post(RoutePool.DOWNLOAD_SUMMARY_FUNDS_TRANS_ROUTE).handler(payOutHandler.getDownloadSummaryFundsTransHandler());
                router.post(RoutePool.FUNDS_TRANS_BATCH_UPLOAD).handler(payOutHandler.fundsTransferBatch());
                router.get(RoutePool.FUNDS_TRANS_BATCH).handler(payOutHandler.getBatch());
                router.get(RoutePool.FUNDS_TRANS_BATCH_DETAIL).handler(payOutHandler.getBatchDetail());
                router.post(RoutePool.DOWNLOAD_FUNDS_TRANS_BATCH_DETAIL).handler(payOutHandler.getDownloadBatchDetailCSV());
                router.put(RoutePool.FUNDS_TRANS_BATCH_CHANGE_STATUS).handler(payOutHandler.changeStatusBatchHandler());
                router.get(RoutePool.FUNDS_TRANS_BATCH_CHECK_AUTHEN).handler(payOutHandler.checkAuthenBatchApproval());
                router.get(RoutePool.OPERATOR).handler(payOutHandler.getOperator());
                router.post(RoutePool.OPERATOR).handler(payOutHandler.insertOperator());
                router.put(RoutePool.OPERATOR).handler(payOutHandler.updateOperator());
                router.put(RoutePool.OPERATOR_RESET_PASS).handler(payOutHandler.resetPass());
                router.put(RoutePool.OPERATOR_CHANGE_PASS).handler(payOutHandler.changePass());
                router.put(RoutePool.OPERATOR_CHANGE_STATUS).handler(payOutHandler.changeStatusOperator());
                router.post(RoutePool.OPERATOR_GET_BY_USER_AND_PASS).handler(payOutHandler.receiveByUserAndPass());
        }

        /**
         * Created by IntelliJ IDEA.
         * User: GiangFu
         * Date: 8/13/2020
         * Time: 3:18 PM
         * To change this ma-service.
         */
        private void statisticChartRouter(Router router) {
                router.post(RoutePool.STATISTIC_CHART_INTERNATIONAL_ROUTE).handler(chartStatisticHandler.getInternational());
                router.post(RoutePool.STATISTIC_CHART_DOMESTIC_ROUTE).handler(chartStatisticHandler.getDomestic());
                router.post(RoutePool.STATISTIC_CHART_TOTAL_ROUTE).handler(chartStatisticHandler.getTotal());
                router.post(RoutePool.STATISTIC_CHART_INTERNATIONAL_DETAIL).handler(chartStatisticHandler.getIntDetail());
                router.post(RoutePool.STATISTIC_CHART_DOMESTIC_ROUTE_DETAIL).handler(chartStatisticHandler.getDmsDetail());
                /*dowload*/
                router.post(RoutePool.DOWNLOAD_STATISTIC_CHART_DOMESTIC_ROUTE).handler(chartStatisticHandler.downloadDomestic());
                router.post(RoutePool.DOWNLOAD_STATISTIC_CHART_INTERNATIONAL_ROUTE).handler(chartStatisticHandler.downloadInternational());
                router.post(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_DOMESTIC_ROUTE).handler(chartStatisticHandler.downloadTotalDms());
                router.post(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_INTERNATIONAL_ROUTE).handler(chartStatisticHandler.downloadTotalInter());
                router.post(RoutePool.DOWNLOAD_STATISTIC_CHART_TOTAL_ROUTE).handler(chartStatisticHandler.downloadTotal());
        }

        /**
         * Created by IntelliJ IDEA.
         * User: GiangFu
         * Date: 07/12/2020
         * Time: 11:50 AM
         * To change this payCollect Router.
         */
        private void payCollectRouter(Router router) {
                router.get(RoutePool.PAYCOLLECT_GET_ALL_USER_ROUTER).handler(payCollectHandler.all());
                router.get(RoutePool.PAYCOLLECT_SEARCH_USER_ROUTER).handler(payCollectHandler.searchUser());
                router.post(RoutePool.DOWNLOAD_PAYCOLLECT_USER_ROUTER).handler(payCollectHandler.downloadUser());
                router.get(RoutePool.PAYCOLLECT_GET_USER_ROUTER).handler(payCollectHandler.getUser());
                router.post(RoutePool.PAYCOLLECT_UPSERT_USER_ROUTER).handler(payCollectHandler.upsertUser());
                router.get(RoutePool.PAYCOLLECT_GET_ALL_MERCHANT_ROUTER).handler(payCollectHandler.getAllMerchant());

                router.route(HttpMethod.GET, RoutePool.PAY_COLLECT_TRANSACTION_ROUTE).handler(payCollectHandler.searchTransaction());
                router.get(RoutePool.PAY_COLLECT_BANK_ROUTE).handler(payCollectHandler.getAllBank());
                router.route(HttpMethod.GET, RoutePool.PAY_COLLECT_TRANSACTION_DETAIL_ROUTE).handler(payCollectHandler.getTransaction());
                router.route(HttpMethod.POST, RoutePool.PAY_COLLECT_DOWNLOAD_TRANSACTION_ROUTE).handler(payCollectHandler.downloadTransaction());
                router.route(HttpMethod.GET, RoutePool.PAY_COLLECT_REPORT_ROUTE).handler(payCollectHandler.searchReport());
                router.route(HttpMethod.POST, RoutePool.PAY_COLLECT_DOWNLOAD_REPORT_ROUTE).handler(payCollectHandler.downloadReport());

        }

        private void quickLinkRouter(Router router) {
                router.get(RoutePool.STATIC_LINK_LIST).handler(StaticLinkHandler::list);
                router.get(RoutePool.STATIC_LINK_LIST_DETAIL).handler(StaticLinkHandler::detail);
                router.put(RoutePool.STATIC_LINK_CHANGE_STATE).handler(StaticLinkHandler::changeState);

                router.get(RoutePool.PAYMENT_LINK_LIST).handler(PaymentLinkHandler::list);
                router.post(RoutePool.PAYMENT_LINK_LIST_DOWNLOAD).handler(a -> PaymentLinkHandler.download(a, downloadFastOutQueue, downloadFastInQueue));
                router.get(RoutePool.PAYMENT_LINK_LIST_DETAIL).handler(PaymentLinkHandler::detail);
                router.post(RoutePool.PAYMENT_LINK_LIST).handler(PaymentLinkHandler::create);
                router.post(RoutePool.UPLOAD_FILE).handler(PaymentLinkHandler::upload);
                router.put(RoutePool.PAYMENT_LINK_LIST_DETAIL).handler(PaymentLinkHandler::update);
                router.put(RoutePool.PAYMENT_LINK_CHANGE_STATE).handler(PaymentLinkHandler::changeState);

                router.get(RoutePool.PAYMENT_LINK_MERCHANT_PROFILE).handler(PaymentLinkHandler::merchantProfile);
                router.get(RoutePool.PAYMENT_LINK_MERC_PROFILE_ID).handler(PaymentLinkHandler::merchantProfileId);
                router.get(RoutePool.PAYMENT_LINK_MERC_ID_BY_PROFILE).handler(PaymentLinkHandler::merchantIdByProfile);

                router.get(RoutePool.EXCHANGE_RATE).handler(ExRateHandler::getExchangeRate);
                router.put(RoutePool.EXCHANGE_RATE).handler(exchangeRateHandler.upsertExchangeRate());
                router.put(RoutePool.EXCHANGE_RATE_CHANGE_STATE).handler(exchangeRateHandler.changeState());
                router.put(RoutePool.EXCHANGE_RATE_CHANGE_TYPE).handler(exchangeRateHandler.changeType());
                router.get(RoutePool.EXCHANGE_RATE_GET_CURRENCY).handler(ExRateHandler::getCurrency);
                router.get(RoutePool.EXCHANGE_RATE_GET_CURRENCY_WITHOUT_VCB).handler(ExRateHandler::getCurrencyWithoutVCB);
                router.get(RoutePool.EXCHANGE_RATE_VCB).handler(ExRateHandler::getExchangeRateVCB);
        }
        private void vinpearlRouter(Router router) {
                LOGGER.info("Innit Vinpearl Router");
                router.route(HttpMethod.PATCH, RoutePool.TRANSACTION_VINPEARL_REFUND).handler(vinpearlTransactionHandler.refund());
        }

        private void financialReportRouter(Router router) {
                router.post(RoutePool.MERCHANT_PAYMENT_BY_USER_ID).handler(FinancialReportHandler::getListMerchantPaymentByUserId);
        }

}
