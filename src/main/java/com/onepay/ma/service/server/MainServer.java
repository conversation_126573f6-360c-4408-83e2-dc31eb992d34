package com.onepay.ma.service.server;

import com.hazelcast.config.Config;
import com.onepay.ma.service.models.ServerConfig;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import io.vertx.core.spi.cluster.ClusterManager;
import io.vertx.spi.cluster.hazelcast.HazelcastClusterManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/4/16.
 */
@Component
public class MainServer implements Runnable {

    @PostConstruct
    public void deploy() {
        Thread t = new Thread(this);
        t.start();

    }

    @Override
    public void run() {
        VertxOptions options = new VertxOptions()
                .setWorkerPoolSize(serverConfig.getWorkers())
                .setEventLoopPoolSize(serverConfig.getThreads())
                .setMaxEventLoopExecuteTime(6000000000l * 100000);

        if (serverConfig.isClustering()) {
            Config config = new Config();
            config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(serverConfig.isMulticast());
            config.getNetworkConfig().getJoin().getTcpIpConfig().setEnabled(!serverConfig.isMulticast());
            config.getNetworkConfig().getJoin().getTcpIpConfig().addMember(serverConfig.getGroupIp());
            config.getNetworkConfig().getInterfaces().setEnabled(!serverConfig.isMulticast());
            config.getNetworkConfig().getInterfaces().addInterface(serverConfig.getInterfaceHazelcast());
            config.getGroupConfig().setName(serverConfig.getGroupName());
            config.getNetworkConfig().setPort(serverConfig.getGroupPort());
            config.getGroupConfig().setPassword(serverConfig.getGroupPassword());
            ClusterManager mgr = new HazelcastClusterManager(config);
            options.setClustered(true);
            options.setHAEnabled(true);
            options.setClusterManager(mgr);

            Vertx.clusteredVertx(options, res -> {
                if (res.succeeded()) {
                    Vertx vertx = res.result();
                    vertx.deployVerticle(serviceServer);
                } else {
                    // failed!
                }
            });
        } else {
            Vertx.vertx(options).deployVerticle(serviceServer);
        }
    }

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private ServiceServer serviceServer;
}
