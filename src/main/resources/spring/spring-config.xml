<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
             http://www.springframework.org/schema/jms
            http://www.springframework.org/schema/jms/spring-jms-3.0.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.onepay.ma.service"/>

    <context:property-placeholder location="classpath:/server.properties" system-properties-mode="OVERRIDE" />


    <bean id="hikariConfigBackUpDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_back_up.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_back_up.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_back_up.idleTimeout}" />
        <property name="connectionTimeout" value="${database_back_up.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_back_up.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_back_up.url}</prop>
                <prop key="user">${database_back_up.username}</prop>
                <prop key="password">${database_back_up.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_back_up.poolName}" />
    </bean>

    <bean id="backUpDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigBackUpDatasource" />
    </bean>


    <bean id="hikariConfigOnlineSource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_online.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_online.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_online.idleTimeout}" />
        <property name="connectionTimeout" value="${database_online.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_online.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_online.url}</prop>
                <prop key="user">${database_online.username}</prop>
                <prop key="password">${database_online.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_online.poolName}" />
    </bean>

    <bean id="onlineDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigOnlineSource" />
    </bean>

    <!--<bean id="hikariConfigCdrDatasource" class="com.zaxxer.hikari.HikariConfig">-->
        <!--<property name="maximumPoolSize" value="${database_cdr.maxPoolSize}" />-->
        <!--<property name="dataSourceClassName" value="${database_cdr.dataSourceClassName}" />-->
        <!--<property name="idleTimeout" value="${database_cdr.idleTimeout}" />-->
        <!--<property name="connectionTimeout" value="${database_cdr.connectionTimeout}"/>-->
        <!--&lt;!&ndash;<property name="initializationFailFast" value="false"/>&ndash;&gt;-->
        <!--<property name="minimumIdle" value="${database_cdr.minPoolSize}"/>-->
        <!--<property name="validationTimeout" value="1000"/>-->
        <!--<property name="leakDetectionThreshold" value="10000"/>-->
        <!--<property name="dataSourceProperties">-->
            <!--<props >-->
                <!--<prop key="url">${database_cdr.url}</prop>-->
                <!--<prop key="user">${database_cdr.username}</prop>-->
                <!--<prop key="password">${database_cdr.password}</prop>-->
            <!--</props>-->
        <!--</property>-->
        <!--<property name="poolName" value="${database_cdr.poolName}" />-->
    <!--</bean>-->

    <!--<bean id="cdrDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">-->
        <!--<constructor-arg ref="hikariConfigCdrDatasource" />-->
    <!--</bean>-->

    <bean id="hikariConfigReadOnlyDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_read_only.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_read_only.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_read_only.idleTimeout}" />
        <property name="connectionTimeout" value="${database_read_only.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_read_only.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_read_only.url}</prop>
                <prop key="user">${database_read_only.username}</prop>
                <prop key="password">${database_read_only.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_read_only.poolName}" />
    </bean>


    <bean id="readOnlyDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigReadOnlyDatasource" />
    </bean>

    <bean id="hikariConfigPromotionDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_promotion.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_promotion.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_promotion.idleTimeout}" />
        <property name="connectionTimeout" value="${database_promotion.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_promotion.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_promotion.url}</prop>
                <prop key="user">${database_promotion.username}</prop>
                <prop key="password">${database_promotion.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_promotion.poolName}" />
    </bean>

    <bean id="prDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigPromotionDatasource" />
    </bean>

    <bean id="hikariConfigPayCollectDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_pay_collect.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_pay_collect.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_pay_collect.idleTimeout}" />
        <property name="connectionTimeout" value="${database_pay_collect.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_pay_collect.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_pay_collect.url}</prop>
                <prop key="user">${database_pay_collect.username}</prop>
                <prop key="password">${database_pay_collect.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_pay_collect.poolName}" />
    </bean>

    <bean id="payCollectDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigPayCollectDatasource" />
    </bean>


    <bean id="hikariConfigMPDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_merchantportal.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_merchantportal.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_merchantportal.idleTimeout}" />
        <property name="connectionTimeout" value="${database_merchantportal.connectionTimeout}"/>
        <property name="minimumIdle" value="${database_merchantportal.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_merchantportal.url}</prop>
                <prop key="user">${database_merchantportal.username}</prop>
                <prop key="password">${database_merchantportal.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_merchantportal.poolName}" />
    </bean>

    <bean id="mpDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigMPDatasource" />
    </bean>
    <!-- Download Datasource -->
    <!--<bean id="hikariConfigCdrDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">-->
        <!--<property name="maximumPoolSize" value="${database_cdr.file.maxPoolSize}" />-->
        <!--<property name="dataSourceClassName" value="${database_cdr.dataSourceClassName}" />-->
        <!--<property name="idleTimeout" value="${database_cdr.idleTimeout}" />-->
        <!--<property name="connectionTimeout" value="${database_cdr.connectionTimeout}"/>-->
        <!--&lt;!&ndash;<property name="initializationFailFast" value="false"/>&ndash;&gt;-->
        <!--<property name="minimumIdle" value="${database_cdr.minPoolSize}"/>-->
        <!--<property name="validationTimeout" value="1000"/>-->
        <!--<property name="leakDetectionThreshold" value="10000"/>-->
        <!--<property name="dataSourceProperties">-->
            <!--<props >-->
                <!--<prop key="url">${database_cdr.url}</prop>-->
                <!--<prop key="user">${database_cdr.username}</prop>-->
                <!--<prop key="password">${database_cdr.password}</prop>-->
            <!--</props>-->
        <!--</property>-->
        <!--<property name="poolName" value="${database_cdr.poolName}_DOWNLOAD" />-->
    <!--</bean>-->

    <!--<bean id="cdrDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">-->
        <!--<constructor-arg ref="hikariConfigCdrDownloadDatasource" />-->
    <!--</bean>-->

    <bean id="hikariConfigOnlineDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_online.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_online.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_online.idleTimeout}" />
        <property name="connectionTimeout" value="${database_online.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_online.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_online.url}</prop>
                <prop key="user">${database_online.username}</prop>
                <prop key="password">${database_online.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_online.poolName}_DOWNLOAD" />
    </bean>

    <bean id="onlineDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigOnlineDatasource" />
    </bean>

    <bean id="hikariConfigReadOnlyDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_read_only.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_read_only.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_read_only.idleTimeout}" />
        <property name="connectionTimeout" value="${database_read_only.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_read_only.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="60000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_read_only.url}</prop>
                <prop key="user">${database_read_only.username}</prop>
                <prop key="password">${database_read_only.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_read_only.poolName}_DOWNLOAD" />
    </bean>

    <bean id="hikariConfigPayCollectDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_pay_collect.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_pay_collect.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_pay_collect.idleTimeout}" />
        <property name="connectionTimeout" value="${database_pay_collect.connectionTimeout}"/>
        <property name="minimumIdle" value="${database_pay_collect.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="60000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_pay_collect.url}</prop>
                <prop key="user">${database_pay_collect.username}</prop>
                <prop key="password">${database_pay_collect.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_pay_collect.poolName}_DOWNLOAD" />
    </bean>

    <bean id="payCollectDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigPayCollectDownloadDatasource" />
    </bean>

    <bean id="hikariConfigMPDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_merchantportal.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_merchantportal.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_merchantportal.idleTimeout}" />
        <property name="connectionTimeout" value="${database_merchantportal.connectionTimeout}"/>
        <property name="minimumIdle" value="${database_merchantportal.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_merchantportal.url}</prop>
                <prop key="user">${database_merchantportal.username}</prop>
                <prop key="password">${database_merchantportal.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_merchantportal.poolName}_DOWNLOAD" />
    </bean>

    <bean id="MPDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigMPDownloadDatasource" />
    </bean>

    <bean id="readOnlyDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigReadOnlyDownloadDatasource" />
    </bean>



    <!--<bean id="hikariConfigBackUpDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">-->
        <!--<property name="maximumPoolSize" value="${database_back_up.file.maxPoolSize}" />-->
        <!--<property name="dataSourceClassName" value="${database_back_up.dataSourceClassName}" />-->
        <!--<property name="idleTimeout" value="${database_back_up.idleTimeout}" />-->
        <!--<property name="connectionTimeout" value="${database_back_up.connectionTimeout}"/>-->
        <!--<property name="initializationFailFast" value="false"/>-->
        <!--<property name="minimumIdle" value="${database_back_up.minPoolSize}"/>-->
        <!--<property name="validationTimeout" value="1000"/>-->
        <!--<property name="dataSourceProperties">-->
            <!--<props >-->
                <!--<prop key="url">${database_back_up.url}</prop>-->
                <!--<prop key="user">${database_back_up.username}</prop>-->
                <!--<prop key="password">${database_back_up.password}</prop>-->
            <!--</props>-->
        <!--</property>-->
        <!--<property name="poolName" value="${database_back_up.poolName}_DOWNLOAD" />-->
    <!--</bean>-->

    <!--<bean id="backUpDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">-->
        <!--<constructor-arg ref="hikariConfigBackUpDownloadDatasource" />-->
    <!--</bean>-->

    <bean id="hikariConfigPromotionDownloadDatasource" class="com.zaxxer.hikari.HikariConfig">
        <property name="maximumPoolSize" value="${database_promotion.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_promotion.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_promotion.idleTimeout}" />
        <property name="connectionTimeout" value="${database_promotion.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_promotion.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>
        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_promotion.url}</prop>
                <prop key="user">${database_promotion.username}</prop>
                <prop key="password">${database_promotion.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_promotion.poolName}_DOWNLOAD" />
    </bean>

    <bean id="prDataSourceDownload" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigPromotionDownloadDatasource" />
    </bean>


    <!--<bean id="readOnlyDataSourceConfig" class="com.onepay.ma.service.models.ReadOnlyDataSource"   >-->
        <!--<property name="dataSourceClassName" value="${database_read_only.dataSourceClassName}"/>-->
        <!--<property name="provider" value="${database_read_only.provider}" />-->
        <!--<property name="url" value="${database_read_only.url}"/>-->
        <!--<property name="username" value="${database_read_only.username}"/>-->
        <!--<property name="password" value="${database_read_only.password}"/>-->
        <!--<property name="connectionTimeout" value="${database_read_only.connectionTimeout}" />-->
        <!--<property name="idleTimeout" value="${database_read_only.idleTimeout}" />-->
        <!--<property name="initPoolSize" value="${database_read_only.initPoolSize}" />-->
        <!--<property name="maxPoolSize" value="${database_read_only.maxPoolSize}" />-->
        <!--<property name="minPoolSize" value="${database_read_only.minPoolSize}" />-->
        <!--<property name="poolName" value="${database_read_only.poolName}" />-->
        <!--<property name="minimumIdle" value="${database_read_only.minPoolSize}"/>-->
    <!--</bean>-->

    <!--<bean id="backUpDataSourceConfig" class="com.onepay.ma.service.models.BackUpDataSource"   >-->
        <!--<property name="dataSourceClassName" value="${database_back_up.dataSourceClassName}"/>-->
        <!--<property name="provider" value="${database_back_up.provider}" />-->
        <!--<property name="url" value="${database_back_up.url}"/>-->
        <!--<property name="username" value="${database_back_up.username}"/>-->
        <!--<property name="password" value="${database_back_up.password}"/>-->
        <!--<property name="connectionTimeout" value="${database_back_up.connectionTimeout}" />-->
        <!--<property name="idleTimeout" value="${database_back_up.idleTimeout}" />-->
        <!--<property name="initPoolSize" value="${database_back_up.initPoolSize}" />-->
        <!--<property name="maxPoolSize" value="${database_back_up.maxPoolSize}" />-->
        <!--<property name="minPoolSize" value="${database_back_up.minPoolSize}" />-->
        <!--<property name="poolName" value="${database_back_up.poolName}" />-->
        <!--<property name="minimumIdle" value="${database_back_up.minPoolSize}"/>-->
    <!--</bean>-->

    <!--<bean id="onlineDataSourceConfig" class="com.onepay.ma.service.models.OnlineDataSource"   >-->
        <!--<property name="dataSourceClassName" value="${database_online.dataSourceClassName}"/>-->
        <!--<property name="provider" value="${database_online.provider}" />-->
        <!--<property name="url" value="${database_online.url}"/>-->
        <!--<property name="username" value="${database_online.username}"/>-->
        <!--<property name="password" value="${database_online.password}"/>-->
        <!--<property name="connectionTimeout" value="${database_online.connectionTimeout}" />-->
        <!--<property name="idleTimeout" value="${database_online.idleTimeout}" />-->
        <!--<property name="initPoolSize" value="${database_online.initPoolSize}" />-->
        <!--<property name="maxPoolSize" value="${database_online.maxPoolSize}" />-->
        <!--<property name="minPoolSize" value="${database_online.minPoolSize}" />-->
        <!--<property name="poolName" value="${database_online.poolName}" />-->
        <!--<property name="minimumIdle" value="${database_online.minPoolSize}"/>-->
    <!--</bean>-->

    <!--<bean id="promotionDataSourceConfig" class="com.onepay.ma.service.models.PromotionDataSource"   >-->
        <!--<property name="dataSourceClassName" value="${database_promotion.dataSourceClassName}"/>-->
        <!--<property name="provider" value="${database_promotion.provider}" />-->
        <!--<property name="url" value="${database_promotion.url}"/>-->
        <!--<property name="username" value="${database_promotion.username}"/>-->
        <!--<property name="password" value="${database_promotion.password}"/>-->
        <!--<property name="connectionTimeout" value="${database_promotion.connectionTimeout}" />-->
        <!--<property name="idleTimeout" value="${database_promotion.idleTimeout}" />-->
        <!--<property name="initPoolSize" value="${database_promotion.initPoolSize}" />-->
        <!--<property name="maxPoolSize" value="${database_promotion.maxPoolSize}" />-->
        <!--<property name="minPoolSize" value="${database_promotion.minPoolSize}" />-->
        <!--<property name="poolName" value="${database_promotion.poolName}" />-->
        <!--<property name="minimumIdle" value="${database_promotion.minPoolSize}"/>-->
    <!--</bean>-->

    <!--<bean id="cdrDataSourceConfig" class="com.onepay.ma.service.models.CdrDataSource"   >-->
        <!--<property name="dataSourceClassName" value="${database_cdr.dataSourceClassName}"/>-->
        <!--<property name="provider" value="${database_cdr.provider}" />-->
        <!--<property name="url" value="${database_cdr.url}"/>-->
        <!--<property name="username" value="${database_cdr.username}"/>-->
        <!--<property name="password" value="${database_cdr.password}"/>-->
        <!--<property name="connectionTimeout" value="${database_cdr.connectionTimeout}" />-->
        <!--<property name="idleTimeout" value="${database_cdr.idleTimeout}" />-->
        <!--<property name="initPoolSize" value="${database_cdr.initPoolSize}" />-->
        <!--<property name="maxPoolSize" value="${database_cdr.maxPoolSize}" />-->
        <!--<property name="minPoolSize" value="${database_cdr.minPoolSize}" />-->
        <!--<property name="poolName" value="${database_cdr.poolName}" />-->
    <!--</bean>-->
 <!--onerecon-->
    <bean id="hikariConfigOnereconDatasource" class="com.zaxxer.hikari.HikariConfig">
        <!--<property name="maximumPoolSize" value="${database_onerecon.file.maxPoolSize}" />-->
        <!--<property name="dataSourceClassName" value="${database_onerecon.dataSourceClassName}" />-->
        <!--<property name="idleTimeout" value="${database_onerecon.idleTimeout}" />-->
        <!--<property name="connectionTimeout" value="${database_onerecon.connectionTimeout}"/>-->
        <!--<property name="initializationFailFast" value="false"/>-->
        <!--<property name="minimumIdle" value="${database_onerecon.minPoolSize}"/>-->
        <!--<property name="validationTimeout" value="1000"/>-->
        <property name="maximumPoolSize" value="${database_onerecon.file.maxPoolSize}" />
        <property name="dataSourceClassName" value="${database_onerecon.dataSourceClassName}" />
        <property name="idleTimeout" value="${database_onerecon.idleTimeout}" />
        <property name="connectionTimeout" value="${database_onerecon.connectionTimeout}"/>
        <!--<property name="initializationFailFast" value="false"/>-->
        <property name="minimumIdle" value="${database_onerecon.minPoolSize}"/>
        <property name="validationTimeout" value="1000"/>
        <property name="leakDetectionThreshold" value="10000"/>

        <property name="dataSourceProperties">
            <props >
                <prop key="url">${database_onerecon.url}</prop>
                <prop key="user">${database_onerecon.username}</prop>
                <prop key="password">${database_onerecon.password}</prop>
            </props>
        </property>
        <property name="poolName" value="${database_onerecon.poolName}" />
    </bean>

    <bean id="onereconDataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfigOnereconDatasource" />
    </bean>

    <bean id="notificationConfig" class="com.onepay.ma.service.models.NotificationConfig">
        <property name="url" value="${notification.eventbus.url}"/>
    </bean>

    <bean id="oneSMConfig" class="com.onepay.ma.service.models.OneSMConfig">
        <property name="url" value="${onesm.service.url}"/>
        <property name="clientId" value="${onesm.service.client.id}"/>
        <property name="clientKey" value="${onesm.service.client.key}"/>
        <property name="hmacKeyLabel" value="${onesm.service.hmac.key.label}"/>
        <property name="timeOut" value="${onesm.service.connection.timeout}"/>
    </bean>

    <bean id="serverConfig" class="com.onepay.ma.service.models.ServerConfig"   >
        <property name="ip" value="${server.ip}"/>
        <property name="port" value="${server.port}" />
        <property name="keepAlive" value="${server.keepAlive}"/>
        <property name="threads" value="${server.thread}"/>
        <property name="workers" value="${server.worker}"/>
        <property name="timeout" value="${server.timeout}"/>
        <property name="threadInterval" value="${server.threadInterval}"/>
        <property name="rowLevel" value="${server.rowLevel}" />
        <property name="rowLimit" value="${server.rowLimit}" />
        <property name="exportLocation" value="${server.exportLocation}" />
        <property name="groupIp" value="${server.groupIp}"/>
        <property name="groupName" value="${server.groupName}"/>
        <property name="groupPassword" value="${server.groupPassword}"/>
        <property name="groupPort" value="${server.groupPort}"/>
        <property name="interfaceHazelcast" value="${server.interface}"/>
        <property name="multicast" value="${server.multicast}" />
        <property name="clustering" value="${server.clustering}" />
        <property name="oneSMConfig" ref="oneSMConfig" />
    </bean>

    <bean id="oneAMConfig" class="com.onepay.ma.service.models.OneAMConfig"   >
        <property name="user" value="${one_am.user}"/>
        <property name="password" value="${one_am.password}" />
        <property name="continueUrl" value="${one_am.continue}" />
        <property name="logOutUrl" value="${one_am.logout}"/>
        <property name="tokenUri" value="${one_am.token_uri}"/>
        <property name="resourceUri" value="${one_am.resources_uri}"/>
    </bean>

    <bean id="refundConfig" class="com.onepay.ma.service.models.RefundConfig"   >
        <property name="oneCommUrl" value="${refund.one_comm.url}"/>
        <property name="oneCreditUrl" value="${refund.one_credit.url}" />
        <property name="secureCode" value="${refund.one_credit.secure_code}"/>
        <property name="timeout" value="${refund.one_credit.time_out}"/>
    </bean>

    <bean id="cacheGuava" class="com.onepay.ma.service.util.CacheGuava" >
    </bean>


    <bean id="serviceQueueIn" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.service.in.name}"/>
    </bean>


    <bean id="serviceQueueOut" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.service.out.name}"/>
    </bean>


    <bean id="downloadFastQueueIn" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.fast.in.name}"/>
    </bean>

    <bean id="downloadFastQueueOut" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.fast.out.name}"/>
    </bean>

    <bean id="downloadSlowQueueIn" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.slow.in.name}"/>
    </bean>

    <bean id="downloadSlowQueueOut" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.slow.out.name}"/>
    </bean>

    <bean id="downloadMultiFileIn" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.multi.file.in.name}"/>
    </bean>

    <bean id="downloadMultiFileOut" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="${ma_service.queue.download.multi.file.out.name}"/>
    </bean>

    <bean id="queueServer" class="com.onepay.ma.service.queue.server.QueueServer" init-method="init">
        <property name="queueURL" value="${queue.uri}"/>
        <property name="queueDataDirectory" value="${queue.dir}"/>
        <property name="queueTempLimit" value="${ma_service.queue.temp.limit}"/>
        <property name="queueMemoryLimit" value="${ma_service.queue.memory.limit}"/>
        <property name="queueStorageLimit" value="${ma_service.queue.storage.limit}"/>
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <property name="downloadQueueSlowInListener" ref="downloadSlowInQueue"/>
        <property name="downloadQueueSlowOutListener" ref="downloadSlowOutQueue"/>
        <property name="downloadQueueMultiInListener" ref="downloadMultiInQueue"/>
        <property name="downloadQueueMultiOutListener" ref="downloadMultiOutQueue"/>
    </bean>



    <bean id="jmsTemplate" class="org.springframework.jms.core.JmsTemplate">
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <property name="deliveryPersistent" value="true"/>
        <property name="timeToLive" value="${ma_service.queue.timeout}"/>
        <property name="defaultDestination" ref="downloadFastQueueIn"/>
    </bean>

    <bean id="queueProducer" class="com.onepay.ma.service.queue.producer.QueueProducer">
        <property name="jmsTemplate" ref="jmsTemplate"/>
    </bean>

    <bean id="activeMqConnectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory" primary="true">
        <property name="brokerURL" value="${queue.uri}"/>
        <property name="trustAllPackages" value="true"/>

    </bean>

    <bean id="jmsConnectionFactory" class="org.springframework.jms.connection.CachingConnectionFactory" >
        <constructor-arg ref="activeMqConnectionFactory"/>
    </bean>

    <bean id="downloadFastInQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueFastInListener"/>

    <bean id="downloadFastOutQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueFastOutListener" />

    <bean id="downloadSlowInQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueSlowInListener"/>

    <bean id="downloadSlowOutQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueSlowOutListener" />

    <bean id="downloadMultiInQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueMultiInListener"/>

    <bean id="downloadMultiOutQueue" class="com.onepay.ma.service.queue.listener.DownloadQueueMultiOutListener" />


    <jms:listener-container connection-factory="activeMqConnectionFactory" container-type="default" acknowledge="auto">

        <jms:listener destination="QUEUE_FAST_DOWNLOAD_IN" ref="downloadFastInQueue" method="onMessage"/>

        <jms:listener destination="QUEUE_FAST_DOWNLOAD_OUT" ref="downloadFastOutQueue" method="onMessage"/>

        <jms:listener destination="QUEUE_SLOW_DOWNLOAD_IN" ref="downloadSlowInQueue" method="onMessage"/>

        <jms:listener destination="QUEUE_SLOW_DOWNLOAD_OUT" ref="downloadSlowOutQueue" method="onMessage"/>

        <jms:listener destination="QUEUE_MULTI_FILE_DOWNLOAD_IN" ref="downloadMultiInQueue" method="onMessage"/>

        <jms:listener destination="QUEUE_MULTI_FILE_DOWNLOAD_OUT" ref="downloadMultiOutQueue" method="onMessage"/>

    </jms:listener-container>



</beans>