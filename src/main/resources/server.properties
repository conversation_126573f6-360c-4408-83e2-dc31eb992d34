# SERVER CONFIGURATION
server.ip=0.0.0.0
server.port=8180
server.thread=5
server.worker=20
server.keepAlive=true
server.timeout=1000000
server.threadInterval=45000
server.rowLevel=200000
server.rowLimit=200000
server.exportLocation = /opt/ma-service/export
server.groupName=vertxserver
server.groupPassword=vertxserver
server.groupIp=127.0.0.1
server.groupPort=8890
server.interface=127.0.0.1
server.multicast=false
server.clustering=false
server.cacheTimeout=15


# DATABASE READ ONLY CONFIGURATION
database_read_only.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_read_only.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_read_only.url=***********************************************
database_read_only.username=merchantportal
database_read_only.password=merchantportal
database_read_only.connectionTimeout=30000
database_read_only.poolName=READ_ONLY_POOL_1113
database_read_only.idleTimeout=20000
database_read_only.initPoolSize=0
database_read_only.minPoolSize=0
database_read_only.maxPoolSize=20
database_read_only.file.maxPoolSize=2


# DATABASE BACK UP CONFIGURATION
database_back_up.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_back_up.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_back_up.url=*****************************************
database_back_up.username=merchantportal
database_back_up.password=merchantportal
database_back_up.connectionTimeout=30000
database_back_up.poolName=BACK_UP_POOL_1114
database_back_up.idleTimeout=20000
database_back_up.initPoolSize=1
database_back_up.minPoolSize=1
database_back_up.maxPoolSize=5
database_back_up.file.maxPoolSize=2

# DATABASE ONLINE CONFIGURATION
database_online.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_online.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_online.url=****************************************
database_online.username=merchantportal
database_online.password=merchantportal
database_online.connectionTimeout=30000
database_online.poolName=ONLINE_POOL_1111
database_online.idleTimeout=20000
database_online.initPoolSize=1
database_online.minPoolSize=1
database_online.maxPoolSize=10
database_online.file.maxPoolSize=10

# DATABASE PROMOTION CONFIGURATION
database_promotion.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_promotion.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_promotion.url=****************************************
database_promotion.username=onepr
database_promotion.password=onepr
database_promotion.connectionTimeout=30000
database_promotion.poolName=PROMOTION_POOL_1111
database_promotion.idleTimeout=20000
database_promotion.initPoolSize=1
database_promotion.minPoolSize=1
database_promotion.maxPoolSize=5
database_promotion.file.maxPoolSize=2

# DATABASE CDR CONFIGURATION
database_cdr.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_cdr.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_cdr.url=*****************************************
database_cdr.username=onecdr
database_cdr.password=onecdr
database_cdr.connectionTimeout=30000
database_cdr.poolName=CDR_POOL_1114
database_cdr.idleTimeout=20000
database_cdr.initPoolSize=1
database_cdr.minPoolSize=1
database_cdr.maxPoolSize=5
database_cdr.file.maxPoolSize=2

# DATABASE CDR CONFIGURATION
database_mpay.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_mpay.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_mpay.url=*****************************************
database_mpay.username=onecdr
database_mpay.password=onecdr
database_mpay.connectionTimeout=30000
database_mpay.poolName=CDR_POOL_1114
database_mpay.idleTimeout=20000
database_mpay.initPoolSize=1
database_mpay.minPoolSize=1
database_mpay.maxPoolSize=5
database_mpay.file.maxPoolSize=5

# DATABASE PAYCOLLECT CONFIGURATION
database_pay_collect.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_pay_collect.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_pay_collect.url=*****************************************
database_pay_collect.username=paycollect
database_pay_collect.password=paycollect
database_pay_collect.connectionTimeout=30000
database_pay_collect.poolName=PAY_COLLECT_POOL
database_pay_collect.idleTimeout=20000
database_pay_collect.initPoolSize=1
database_pay_collect.minPoolSize=1
database_pay_collect.maxPoolSize=5
database_pay_collect.file.maxPoolSize=2


# DATABASE MERCHANTPORTAL24 CONFIGURATION
database_merchantportal.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_merchantportal.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_merchantportal.url=*****************************************
database_merchantportal.username=merchantportal
database_merchantportal.password=merchantportal
database_merchantportal.connectionTimeout=30000
database_merchantportal.poolName=MERCHANTPORTAL24_POOL
database_merchantportal.idleTimeout=20000
database_merchantportal.initPoolSize=1
database_merchantportal.minPoolSize=1
database_merchantportal.maxPoolSize=5
database_merchantportal.file.maxPoolSize=2

# DATABASE ONERECON CONFIGURATION
database_onerecon.provider=io.vertx.ext.jdbc.spi.impl.HikariCPDataSourceProvider
database_onerecon.dataSourceClassName=oracle.jdbc.pool.OracleDataSource
database_onerecon.url=*****************************************
database_onerecon.username=onecdr
database_onerecon.password=onecdr
database_onerecon.connectionTimeout=30000
database_onerecon.poolName=ONERECON
database_onerecon.idleTimeout=600000
database_onerecon.initPoolSize=1
database_onerecon.minPoolSize=1
database_onerecon.maxPoolSize=1
database_onerecon.file.maxPoolSize=5


# ONEAM CONFIG
one_am.user=ONEMADM
one_am.password=Aa123456654321
one_am.token_uri=http://localhost/oauth2/tokens
one_am.resources_uri=http://localhost/oauth2/tokens/%s/resources
one_am.logout = https://dev18-secure.onepay.vn/accounts/#/logout?continue
one_am.continue = http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fdev18-secure.onepay.vn%2Fmm%2Flogin%26clientId%3DONEMADM%26authLevel%3D1
#one_am.continue_2 = http%3A%2F%2Foneam.onepay.vn%2F%3FresponseType%3Dcode%26scope%3Dprofile%26redirectUri%3Dhttps%3A%2F%2Fdev18-ma.onepay.vn%2Flogin%26clientId%3DONEMADM%26authLevel%3D1
#one_am.continue_2=
one_am.logout_2=https://dev18-ma.onepay.vn/accounts/#/logout?continue

# REFUND CONFIG
refund.one_comm.url = http://localhost/onecomm-payservice/execute
refund.one_credit.url = http://localhost/onecredit/execute
refund.one_credit.secure_code = 23BF1DC999734E6A6860BD56273AA455
refund.one_credit.time_out = 60000
refund.one_comm.url_2 = http://localhost/onecomm-pay/refund.op


customer_google_notifucation_api_key=AIzaSyCuRtzHw8ExZZWWMEpMP6UnSG4c807k-Mc
customer_google_notifucation_api_url=http://partners.onepay.vn/fcm/send

#onepay_google_notifucation_api_key=AIzaSyCuRtzHw8ExZZWWMEpMP6UnSG4c807k-Mc
onepay_google_notifucation_api_key=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
# onepay_google_notifucation_api_url=http://partners.onepay.vn/fcm/send
onepay_google_notifucation_api_url=http://partners.onepay.vn/fcm/v1/projects/production-mpayvn/messages:send
onepay_google_notifucation_api_url_scopes=https://www.googleapis.com/auth/firebase.messaging

#
# MSP Configuration
#
onepay_msp_service_base_url=http://localhost
onepay_msp_service_client_id=MADM_SERVICE
onepay_msp_service_name=msp
onepay_msp_service_region=onepay
onepay_msp_service_client_key=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
onepay_msp_service_extend_prefix="/msp-extend/api/v1"


# CARD HELPER Configuration
#
onepay_card_helper_service_base_url=http://127.0.0.1
onepay_card_helper_service_client_id=MADM_SERVICE
onepay_card_helper_service_name=msp
onepay_card_helper_service_region=onepay
onepay_card_helper_service_client_key=3avDXvTfaCmdXUWVJuucXkbLFFa845GUCGgZmhUFuH7


# NOTIFICATION
notification.eventbus.url = http://localhost/ma-eventbus


# paycollect Configuration
onepay_paycollect_service_base_url=http://localhost
onepay_paycollect_service_name=paycollect
onepay_paycollect_service_region=onepay
onepay_paycollect_service_client_id=MA_PAYCOLLECT
onepay_paycollect_service_client_key=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA

# paycollect Configuration

# payout Configuration
onepay_payout_service_base_url=http://************
onepay_payout_service_client_id=MADM_SERVICE
onepay_payout_service_name=onepayout
onepay_payout_service_region=onepay
onepay_payout_service_client_key=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA

# ma-permission Configuration
onepay_ma_permisssion_base_url="http://************/ma-permission/api/v1"
onepay_ma_permisssion_timeout= 60000
#QUICKLINK
onepay_quicklink_upload_url=/opt/ufiles/ma
onepay_quicklink_upload_ext=png,jpg,jpeg,pdf,doc,docx
onepay_quicklink_service_base_url=http://127.0.0.1/prod/quicklink-backend/quicklink

#email
email.operator_change_pass_url=https://dev18.onepay.vn/onepayout/change-password?operator_id=
email.operator.subject =[PAYOUT] Sending Operator Information - Merchant ID:
email.operator_reset_pass_subject =[PAYOUT]- New password for Operator
# payout Configuration

#
#  Message Queue Configurations (ActiveMQ)
#
ma_service.queue.url=vm://localbroker
ma_service.queue.data.directory=/opt/ma-service/queue-data
queue.uri=vm://localbroker
queue.dir=/opt/ma-service/queue-data

# Limit(MB)
ma_service.queue.temp.limit=64
ma_service.queue.memory.limit=32
ma_service.queue.storage.limit=128

# Timeout(milliseconds)
ma_service.queue.timeout=3600

# Download Fast Queue
ma_service.queue.download.fast.in.name=QUEUE_FAST_DOWNLOAD_IN
ma_service.queue.download.fast.out.name=QUEUE_FAST_DOWNLOAD_OUT

# Dowload Slow Queue
ma_service.queue.download.slow.in.name=QUEUE_SLOW_DOWNLOAD_IN
ma_service.queue.download.slow.out.name=QUEUE_SLOW_DOWNLOAD_OUT

# Dowload Multi File Queue
ma_service.queue.download.multi.file.in.name=QUEUE_MULTI_FILE_DOWNLOAD_IN
ma_service.queue.download.multi.file.out.name=QUEUE_MULTI_FILE_DOWNLOAD_OUT

# Service Queue
ma_service.queue.service.in.name=QUEUE_SERVICE_IN
ma_service.queue.service.out.name=QUEUE_SERVICE_OUT

# Pattern
mobile.validation.pattern=^(\\+84|84|0)(3[2-9]|5[2-9]|7[0|6-9]|8[1-9]|9[0-9])\\d{7}$
email.validation.pattern=^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@[A-Za-z0-9-]+(\\.[A-Za-z0-9-]+)*(\\.[A-Za-z]{2,})$

#danh sach key
app.key.password.1=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
app.key.password.2=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
app.key.password.3=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA

#PAYPAL
paypal.partner.client=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
paypal.partner.pass=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
paypal.partner.id=RVUFQ37P7PE6A
paypal.partner.logo_url=https://dev18-secure.onepay.vn/accounts/img/logo_2x.png
paypal.return_url=https://dev18-ma.onepay.vn/paypal-register
paypal.action_url=https://dev18-ma.onepay.vn/paypal-register
paypal.base_url=http://partners.onepay.vn/v1
email.support.register.to.paypal=<EMAIL>,<EMAIL>

#IPN
ipn.base_url=http://localhost/ipn/api/v1

#SAMSUNG
samsung.epp=

#//--------------- MP CODE -------------------------- //
Blank = No Response
0 = Approved
1 = Unspecified Failure
2 = Declined
3 = Timed Out
4 = Expired Card
5 = Insufficient Funds
6 = Bank Error
7 = System Error
8 = Not Supported
9 = Bank declined
A = Transaction Aborted
B = Transaction Blocked
C = Transaction Cancelled
D = awaiting processing
E = Referred
F = 3D Secure Failure
I = CSC Failed
L = Transaction Locked
N = Not enrolled Auth
P = being processed
R = not processed
S = Duplicate SessionID
T = ACS Timeout
U = Undertermined
V = AVS CSC Failed
? = Unknown Status
W = Undetermined
OP = Not Process
Y = Cardholder Verified
M = Verification Attemped
#//--------------- MP CODE -------------------------- //

#//--------------- AUTHENTICATION CODE -------------------------- //
AUTH_Y= Cardholder Verified
AUTH_M= Verification Attempt
AUTH_E= Cardholder Not Enrolled
AUTH_N= Cardholder Not Verified
AUTH_T= ACS Timeout
AUTH_A= Authentication Failed
AUTH_P= Parse Error
AUTH_U= Undetermined
AUTH_S= Security Error
AUTH_I= Internal Error
AUTH_D= Directory Communication Error
#//--------------- AUTHENTICATION CODE -------------------------- //

#//--------------- DOMESTIC RESPONSE CODE -------------------------- //
D0 = Approved
D1 = Declined
D3 = Merchant Is Not Exited
D4 = Invalid Access Code
D5 = Invalid Amount
D6 = Invalid Currency Code
D7 = Unspecified Failure
D8 = Invalid Card Number
D9 = Invalid Card Name
D10 = Expired Card
D11 = Not Registered
D12 = Invalid Card Date
D13 = Exist Amount
D21 = Insufficient Fund
D22 = Invalid Account
D23 = Account Lock
D24 = Invalid Card Info
D25 = Invalid OTP
D99 = User Cancel Transaction
D100 = No Response
D253 = Transaction Timeout

#//--------------- DOMESTIC RESPONSE CODE -------------------------- //

#//--------------- MA CODE -------------------------- //
400 = Successful
300 = Pending
200 = Failed
100 = Not Processed

#
# OneSM Service Configuration
#
onesm.service.url=http://localhost/onesm/api/v1
onesm.service.client.id=onecredit
onesm.service.client.key=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
onesm.service.hmac.key.label=onecredit.hmac
#onesm.service.aes.key.label=madm.aes
onesm.service.connection.timeout= 60000


#Vietcombank:1
txncode.1.23=25
#Vietinbank:4
txncode.4.2=25
#MB:8
txncode.8.17=25
#MSB:10
txncode.10.6=13
txncode.10.15=25
#EXIM:11
txncode.11.8=25
#SHB:12
txncode.12.2=25
#VPB:14
txncode.14.3=25
txncode.14.13=25
txncode.14.14=25
#ABB:15
txncode.15.6=13
txncode.15.15=25
#SACOMBANK:16
txncode.16.51=21
txncode.16.55=25
#NAB:17
txncode.17.10=13
txncode.17.1=25
#OCB:18
txncode.18.24=25
#BIDV:19
txncode.19.5=25
#BAB:22
txncode.22.10=13
txncode.22.1=25
#NCB:23
txncode.23.20=25
#Agribank
txncode.24.21=25
#PVCOMBANK:27
txncode.27.5=25

#card - bank
card=V,M,J,A
visa=Visa
mastercard=Mastercard
amex=Amex
jcb=JCB
agribank_card=Agribank
abb_card=An Binh Bank
bab_card=Bac A Bank
exim_card=Eximbank
hdb_card=HDBank
msb_card=Maritime Bank
nab_card=Nam A Bank
ncb_card=NCB
pvcombank_card=PVCOMBANK
sacombank_card=Sacombank
scb_card=SCB
sab_card=Seabank
vpb_card=VP Bank
vietinbank_card=Vietinbank
vib_card=VIB
bidv_card=BIDV
vietcombank_card=Vietcombank
vccb_card=Viet Capital Bank
ewallet=eWallet
MOMO=MOMO

#//--------------- START SENT MAIL -------------------------- //
email.support.site.host =mail.onepay.com.vn
email.support.site.from =<EMAIL>
email.support.site.user =<EMAIL>
email.supoport.site.password=4F2A1B3C5D6E7F8A9B0C1D2E3F4AAAAA
email.support.site.port=25
email.support.ssl =false
email.support.starttls =false
email.subject =OnePAY- New password for User
email.content_vi=Dear Merchant,<br>Y\uFFFDu c?u ??t l?i m?t kh?u cho User {1} ?\uFFFD ???c x? l\uFFFD th\uFFFDnh c\uFFFDng.<br>Vui l\uFFFDng ??ng nh?p l?i theo th\uFFFDng tin b\uFFFDn d??i:<br><br>User: {1}<br> M?t kh?u m?i:<b>{2}<b>/<br/>---------------------------------------------------------------------------------<br/>
email.content_en=Dear Merchant,<br>The password for User {1} has been reset successfully!<br>Please login using the following information:<br><br>User: {1}<br> New password : <b>{2}</b><br><br>Thanks & Best Regards,<br>------------------<br>OnePay JSC<br>Phone: (84) 24 ******** | Hotline: (84) 1900 633 927<br>Email: <a href='mailto:<EMAIL>'><EMAIL></a> | Website: <a href='https://www.onepay.vn'>www.onepay.vn</a><br>


#//--------------- END SENT MAIL -------------------------- //


one_am.merchant.url=https://dev18-ma.onepay.vn/accounts/
ma.url=https://dev18-ma.onepay.vn


#//---------------- SAMSUNG MERCHANT APPROVAL ------------- //
samsung.merchant.approvals=OP_SAMSUNG

onesched-service-url= http://localhost
onesched-service-timeout= 600000
onesched-service-domestic_synx_id=20009
onesched-service-international_view_sync_id=2096
onesched-service-international_full_sync_id=2099
onesched-service-inter_pc_view_sync_id = 20002
onesched-service-inter_rf_view_sync_id = 20003
onesched-service-inter_ct_view_sync_id = 20004
onesched-service-inter_void_view_sync_id = 20005
onesched-service-qr_synx_id=20010
onesched-service-po_synx_id=20026
page_size=200
bigMerchantId=TESTAPPLE,TESTAPPLEND

onepay_ma_service_base_url=http://localhost/ma-service

key_hash_history_refund=MDrR9%q`@ETJ_@[7B6$5jQ]CS4Z0qL}R-U,qfP8[ai&Oi|U|`BTc!V,C~T>^#M$
