<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="statistics_detail_dt_pdf" pageWidth="1522" pageHeight="595" orientation="Landscape" columnWidth="1482" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6abb5b6b-d6ad-4dad-bb1f-81100b804b05">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="p_acqType" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="rowNumber" class="java.lang.Integer"/>
	<field name="merchantId" class="java.lang.String"/>
	<field name="merchantName" class="java.lang.String"/>
	<field name="acqCode" class="java.lang.String"/>
	<field name="transDate" class="java.lang.String"/>
	<field name="cardType" class="java.lang.String"/>
	<field name="transactionSource" class="java.lang.String"/>
	<field name="transType" class="java.lang.String"/>
	<field name="bankTransId" class="java.lang.String"/>
	<field name="cusTransId" class="java.lang.String"/>
	<field name="onepayTransId" class="java.lang.String"/>
	<field name="cardNumber" class="java.lang.String"/>
	<field name="currency" class="java.lang.String"/>
	<field name="amountIss" class="java.lang.Double"/>
	<field name="amountAcq" class="java.lang.Double"/>
	<field name="issFee" class="java.lang.Double"/>
	<field name="acqFee" class="java.lang.Double"/>
	<field name="percentFeeIss" class="java.lang.Double"/>
	<field name="percentFeeAcq" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</title>
	<pageHeader>
		<band height="153" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="0" y="21" width="1180" height="30" isRemoveLineWhenBlank="true" uuid="d48b0374-6d45-4cb5-8fb5-e5de8b1269c7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType})  ?"CHI TIẾT PHẢI THU":"CHI TIẾT PHẢI TRẢ"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="135" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="ca75099e-bb17-4262-8ddf-b2acb236f1ba"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant name]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="235" y="123" width="75" height="30" backcolor="#E3E5E6" uuid="0c4c8767-82fd-49f6-a603-1bf43cd19197"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Acq]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="310" y="123" width="80" height="30" backcolor="#E3E5E6" uuid="fd86b180-d1f6-48b1-bf2f-2155b1c9d3af"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[CardType]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="390" y="123" width="90" height="30" backcolor="#E3E5E6" uuid="f56bac08-e475-44c4-b814-c9a1928c0eee"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="480" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="fad59a56-7d7a-43a4-b6d8-772de9ae9c7c"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction Source]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="580" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="02755de6-e59f-4fb5-b661-92eb875996a9"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction Type]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="680" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="f9069f9a-7edf-4f69-9484-627fdf195169"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Bank Transaction ID]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="880" y="103" width="200" height="20" backcolor="#1C95EB" uuid="f1aeb6fd-9466-4c0b-bb07-a364d30dd52b"/>
				<box>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[ĐVTT: VND]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" isPrintRepeatedValues="false" x="880" y="83" width="200" height="20" isRemoveLineWhenBlank="true" uuid="********-7ec3-40bd-8c0d-525389c4c901">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mẫu báo cáo: 02_OFFUS-DETAIL"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="35" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="dec91a88-4f30-4864-983c-712cd76c5c14"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant Id]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="123" width="35" height="30" backcolor="#E3E5E6" uuid="d41b82be-7f75-4223-8453-1e6a93d4994e"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="780" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="61677a55-5665-42e2-ab64-39ac136e6326"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction ID (Khách hàng - DSP)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="880" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="df4ac5e2-c3a9-4ad2-9b47-f4efc43fa92c"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant transaction ID]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="980" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="2c6e95ce-d61d-4650-bf07-f94b41f9d5de"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Card No]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1080" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="72fe9a96-ed78-4628-b31c-9b3d639c0729"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Currency]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1180" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="ed5081ed-0be9-4c2e-85a0-b9ea8367c919"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1280" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="1bf5f894-20c5-49d0-bf21-4ed1ad07e140"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[% Fee]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1380" y="123" width="100" height="30" backcolor="#E3E5E6" uuid="65ab37a9-c648-464f-9643-df1eaadffd88"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[ Fee amount]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="135" y="0" width="100" height="20" uuid="6c816f3f-fa74-4239-9e02-ef168b17d710"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{merchantName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="235" y="0" width="75" height="20" uuid="b2968fbd-ef3a-4e76-b7fc-bb87f08f10fa"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acqCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="310" y="0" width="80" height="20" uuid="bf7a7d87-ca4b-45cc-980a-b5252cca532b"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="480" y="0" width="100" height="20" uuid="*************-40c1-b313-4c0e77f3a89a"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionSource}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="580" y="0" width="100" height="20" uuid="9d8ddc8f-2884-4408-bbd0-2948ec04611f"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="680" y="0" width="100" height="20" uuid="0987ae62-e0d1-4cdc-834a-f4ccb053eaa5"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="390" y="0" width="90" height="20" uuid="23e7c20c-d729-46f6-abf2-8c60e4ae4ff6"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="35" y="0" width="100" height="20" uuid="3eec9fe2-d6bc-46f3-bb29-2cb2c87efc31"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{merchantId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="35" height="20" uuid="4839f602-8c5d-4db8-9887-5c6a9014e84d"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rowNumber}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="780" y="0" width="100" height="20" uuid="dd96feeb-49b1-409e-94e1-78e085912508"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cusTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="880" y="0" width="100" height="20" uuid="3b1e0e38-2c9a-469b-bbc6-01e68eb1994c"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{onepayTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1080" y="0" width="100" height="20" uuid="ac699926-1565-41a9-ae0c-5f431b5eca1a"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1180" y="0" width="100" height="20" uuid="96bf2308-f2cb-421e-9a03-be3c5025b5c6"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType}) ?($F{amountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountIss}))):($F{amountAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountAcq})))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1280" y="0" width="100" height="20" uuid="e0dbc882-8f3c-4083-a1a6-de8ac03e4f70"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType})?($F{percentFeeIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{percentFeeIss}))):($F{percentFeeAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{percentFeeAcq})))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="980" y="0" width="100" height="20" uuid="6e24ef02-e9d8-418c-b392-1f79dfcc5c0b"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardNumber}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1380" y="0" width="100" height="20" uuid="b34c827f-0feb-4b2e-9e6b-e96673e3369a"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType})?($F{issFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{issFee}))):($F{acqFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{acqFee})))]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
