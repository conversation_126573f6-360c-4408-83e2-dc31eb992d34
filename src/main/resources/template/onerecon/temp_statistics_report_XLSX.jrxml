<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="temp_statistics_report_XLSX" printOrder="Horizontal" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="NoPages" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="6abb5b6b-d6ad-4dad-bb1f-81100b804b05">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="fromDate" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="toDate" class="java.lang.String"/>
	<parameter name="bankId" class="java.lang.String"/>
	<parameter name="totalReceiable" class="java.lang.Double"/>
	<parameter name="totalPayable" class="java.lang.Double"/>
	<parameter name="totalDiff" class="java.lang.Double"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="settlementDate" class="java.lang.String"/>
	<field name="cardType" class="java.lang.String"/>
	<field name="acqCode" class="java.lang.String"/>
	<field name="amountIss" class="java.lang.Double"/>
	<field name="amountAcq" class="java.lang.Double"/>
	<field name="issFee" class="java.lang.Double"/>
	<field name="acqFee" class="java.lang.Double"/>
	<field name="amountRecon" class="java.lang.Double"/>
	<field name="feeRecon" class="java.lang.Double"/>
	<variable name="sumAmountIss" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{amountIss}]]></variableExpression>
	</variable>
	<variable name="sumFeeIss" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{issFee}]]></variableExpression>
	</variable>
	<variable name="sumAmountACQ" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{amountAcq}]]></variableExpression>
	</variable>
	<variable name="sumFeeACQ" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{acqFee}]]></variableExpression>
	</variable>
	<variable name="sumAmountRecon" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{amountRecon}]]></variableExpression>
	</variable>
	<variable name="sumFeeRecon" class="java.lang.Double" resetType="Group" resetGroup="groupTransDate" calculation="Sum">
		<variableExpression><![CDATA[$F{feeRecon}]]></variableExpression>
	</variable>
	<group name="groupTransDate" keepTogether="true">
		<groupExpression><![CDATA[$F{settlementDate}]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="20" splitType="Stretch">
				<textField>
					<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="113" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="acd6be21-78a6-4d18-9f13-84504ba1e3fa"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{settlementDate}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="473" y="0" width="120" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="9eee7535-f670-4ec5-ae96-e98d04f18dbe"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeACQ}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeACQ}))]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="593" y="0" width="125" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="47723699-30a8-4f90-98b3-ab98362e30ce"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountRecon}))]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="718" y="0" width="123" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="5590a000-78d6-4c46-9f78-6d5f8c5136d2"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeRecon}))]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement mode="Opaque" x="113" y="0" width="120" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="bcfb5910-8f2f-4da2-a68b-7e9b1a8ef348"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountIss}))]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="233" y="0" width="120" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="1efcf01c-5d3c-4d9f-8695-0a9a3f36ba69"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeIss}))]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="353" y="0" width="120" height="20" printWhenGroupChanges="groupTransDate" backcolor="#EDEFF0" uuid="5d3c4e74-0247-4cbe-b2c3-c5d1dc372235"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountACQ}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountACQ}))]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</title>
	<pageHeader>
		<band height="193" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="0" width="842" height="30" uuid="21af19ee-5639-459e-a182-9b3b12a8b025">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="16"/>
				</textElement>
				<text><![CDATA[BÁO CÁO TỔNG HỢP CÁC KHOẢN PHẢI THU, PHẢI TRẢ CỦA ONEPAY - NGÂN HÀNG mPAY]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="153" width="113" height="20" backcolor="#E3E5E6" uuid="d0a1a67f-6ebf-4263-bf6b-22abfd76a508"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="113" y="153" width="240" height="20" backcolor="#E3E5E6" uuid="649ac4e5-0819-4615-a52d-3836928b8b19"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[OnePAY phải thu (Receivable)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="353" y="153" width="240" height="20" backcolor="#E3E5E6" uuid="a6eb7d64-c5c2-483b-a562-498d36864c06"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[OnePAY phải trả (Payable)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="593" y="153" width="248" height="20" backcolor="#E3E5E6" uuid="30361bf9-b772-4e25-9208-68014ed03fc5"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Diff]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="233" y="31" width="485" height="20" isRemoveLineWhenBlank="true" uuid="d48b0374-6d45-4cb5-8fb5-e5de8b1269c7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{fromDate}==null  ?"":("Từ ngày " +$P{fromDate} + ($P{toDate}==null  ?"":(" Đến ngày " + $P{toDate})))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="233" y="51" width="485" height="20" isRemoveLineWhenBlank="true" uuid="5eeb5729-ac19-4584-afa5-c204e629308a">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{bankId} ==""?"":($P{bankId} ==null  ?"":("BankId: "+$P{bankId}))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="0" y="173" width="113" height="20" backcolor="#E3E5E6" uuid="ca75099e-bb17-4262-8ddf-b2acb236f1ba"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Bank ID]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="113" y="173" width="120" height="20" backcolor="#E3E5E6" uuid="0c4c8767-82fd-49f6-a603-1bf43cd19197"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(1)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="233" y="173" width="120" height="20" backcolor="#E3E5E6" uuid="fd86b180-d1f6-48b1-bf2f-2155b1c9d3af"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(1)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="353" y="173" width="120" height="20" backcolor="#E3E5E6" uuid="f56bac08-e475-44c4-b814-c9a1928c0eee"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(2)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="473" y="173" width="120" height="20" backcolor="#E3E5E6" uuid="fad59a56-7d7a-43a4-b6d8-772de9ae9c7c"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(2)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="593" y="173" width="125" height="20" backcolor="#E3E5E6" uuid="02755de6-e59f-4fb5-b661-92eb875996a9"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(3)=Amount(1-2)]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="718" y="173" width="123" height="20" backcolor="#E3E5E6" uuid="f9069f9a-7edf-4f69-9484-627fdf195169"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(3) =Fee(1-2)]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="233" y="71" width="120" height="20" isRemoveLineWhenBlank="true" uuid="d07b6cd5-9e15-427c-a0fb-8fe3afb53d78">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Receiable Amt: "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="353" y="71" width="240" height="20" isRemoveLineWhenBlank="true" uuid="0f60ca90-8a74-4627-bc16-2f7a10581da7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{totalReceiable}==null?0:(new java.text.DecimalFormat("#,###.##").format($P{totalReceiable}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="233" y="91" width="120" height="20" isRemoveLineWhenBlank="true" uuid="cbb366ed-8a5c-44b1-aec0-2ff3e218ebe0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Payable Amt: "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="353" y="91" width="240" height="20" isRemoveLineWhenBlank="true" uuid="65c8083a-f451-474b-847c-97095806294c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{totalPayable}==null?0:(new java.text.DecimalFormat("#,###.##").format($P{totalPayable}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="233" y="111" width="120" height="20" isRemoveLineWhenBlank="true" uuid="d91b8655-6c2b-48f8-a592-a5acf313fa03">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA["Total Diff Amt: "]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="353" y="111" width="240" height="20" isRemoveLineWhenBlank="true" uuid="51f677f0-59e8-4ce9-9480-0f59e1ca9799">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{totalDiff}==null?0:(new java.text.DecimalFormat("#,###.##").format($P{totalDiff}))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="593" y="132" width="249" height="20" backcolor="#1C95EB" uuid="66ea4234-c31e-43da-b80b-36939cfbd708"/>
				<box>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[ĐVTT: VND]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="593" y="111" width="248" height="20" isRemoveLineWhenBlank="true" uuid="a06f0a5d-88d6-4517-b33c-ecd55f97f945">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mẫu: RP03-Financial Report"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="113" height="20" uuid="6c816f3f-fa74-4239-9e02-ef168b17d710"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="113" y="0" width="120" height="20" uuid="b2968fbd-ef3a-4e76-b7fc-bb87f08f10fa"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountIss}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="233" y="0" width="120" height="20" uuid="bf7a7d87-ca4b-45cc-980a-b5252cca532b"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{issFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{issFee}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="473" y="0" width="120" height="20" uuid="*************-40c1-b313-4c0e77f3a89a"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acqFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{acqFee}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="593" y="0" width="125" height="20" uuid="9d8ddc8f-2884-4408-bbd0-2948ec04611f"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountRecon}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="718" y="0" width="123" height="20" uuid="0987ae62-e0d1-4cdc-834a-f4ccb053eaa5"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{feeRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{feeRecon}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="353" y="0" width="120" height="20" uuid="23e7c20c-d729-46f6-abf2-8c60e4ae4ff6"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountAcq}))]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
