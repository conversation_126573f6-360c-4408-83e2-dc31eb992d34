<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="statistics_detail_xlsx" pageWidth="1480" pageHeight="595" orientation="Landscape" whenNoDataType="NoPages" columnWidth="1480" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="6abb5b6b-d6ad-4dad-bb1f-81100b804b05">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="p_acqType" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="rowNumber" class="java.lang.Integer"/>
	<field name="merchantId" class="java.lang.String"/>
	<field name="merchantName" class="java.lang.String"/>
	<field name="acqCode" class="java.lang.String"/>
	<field name="transDate" class="java.lang.String"/>
	<field name="cardType" class="java.lang.String"/>
	<field name="transactionSource" class="java.lang.String"/>
	<field name="transType" class="java.lang.String"/>
	<field name="bankTransId" class="java.lang.String"/>
	<field name="cusTransId" class="java.lang.String"/>
	<field name="onepayTransId" class="java.lang.String"/>
	<field name="cardNumber" class="java.lang.String"/>
	<field name="currency" class="java.lang.String"/>
	<field name="amountIss" class="java.lang.Double"/>
	<field name="amountAcq" class="java.lang.Double"/>
	<field name="issFee" class="java.lang.Double"/>
	<field name="acqFee" class="java.lang.Double"/>
	<field name="percentFeeIss" class="java.lang.Double"/>
	<field name="percentFeeAcq" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</title>
	<pageHeader>
		<band height="150" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="0" y="0" width="1180" height="30" isRemoveLineWhenBlank="true" uuid="623b02cf-23fc-4f89-ad34-6fccb7ac2cfe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="16"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType})  ?"CHI TIẾT PHẢI THU":"CHI TIẾT PHẢI TRẢ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="880" y="80" width="300" height="20" isRemoveLineWhenBlank="true" uuid="a06f0a5d-88d6-4517-b33c-ecd55f97f945">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mẫu báo cáo: 02_OFFUS-DETAIL"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="880" y="100" width="300" height="20" backcolor="#1C95EB" uuid="66ea4234-c31e-43da-b80b-36939cfbd708"/>
				<box>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[ĐVTT: VND]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="135" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="656d5d2d-ec37-4cb1-812b-045590f652a8"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant name]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="235" y="120" width="75" height="30" backcolor="#E3E5E6" uuid="dec8c94f-7e42-45f0-a5f0-d2261dc4c879"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Acq]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="310" y="120" width="80" height="30" backcolor="#E3E5E6" uuid="57c79cea-9edf-4bf2-bcd3-fd14912f3f7a"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[CardType]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="390" y="120" width="90" height="30" backcolor="#E3E5E6" uuid="0071a29e-e914-47f9-90c4-9d7dfdf97856"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="480" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="fa622688-a8b2-428d-9288-d2f8bd605387"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction Source]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="580" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="2b47d4ad-07bd-4fc6-b645-b24f31fb4acc"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction Type]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="680" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="9d87d623-228f-4eb7-975c-53a6cb23a900"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Bank Transaction ID]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="35" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="06ba1d7b-4741-48fa-8285-6113f6b26e50"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant Id]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="120" width="35" height="30" backcolor="#E3E5E6" uuid="3beef9ea-1fbc-4663-90df-0481eb2b067d"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="780" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="23d5c961-7699-4a31-8ccc-5180adaf0f0e"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Transaction ID (Khách hàng - DSP)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="880" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="169542bf-98db-42be-a2d1-ccf198f8628a"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Merchant transaction ID]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="980" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="e7909b20-45ad-4b33-a803-81fd6a3cd7d5"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Card No]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1080" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="0bcff0b5-9bc4-46fc-82f8-1232d5077265"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Currency]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1180" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="1515d48a-02fa-4255-b8f6-c90a52d30ccb"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1280" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="b31ad073-5536-42dd-9765-99dac8c313f7"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[% Fee]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="1380" y="120" width="100" height="30" backcolor="#E3E5E6" uuid="c68d8614-e2b1-41ba-bb6e-bae7e4302156"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee amount]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="135" y="0" width="100" height="20" uuid="56fbe792-9a58-42d1-b651-0bd9fbc4f6ec"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{merchantName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="235" y="0" width="75" height="20" uuid="36605f2e-0f05-4fbb-ac2e-0c917ded00ab"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acqCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="310" y="0" width="80" height="20" uuid="c1a5b046-5071-4bf2-9558-0732f82e6a70"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="480" y="0" width="100" height="20" uuid="682fe08b-7b67-4e18-beea-6a0010e0f569"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionSource}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="580" y="0" width="100" height="20" uuid="b45287bd-519b-4945-94dc-c3a6e008918c"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="680" y="0" width="100" height="20" uuid="006bed65-7271-44df-9aca-9f0f47283bcf"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="390" y="0" width="90" height="20" uuid="d05fa6e4-e234-4ffd-8d0e-d3c0f46f7b61"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="35" y="0" width="100" height="20" uuid="dc92e413-3f31-4934-a967-674568cb3229"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{merchantId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="35" height="20" uuid="6b5ca299-bbee-45cf-bdbc-ca998eda7ad5"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rowNumber}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="780" y="0" width="100" height="20" uuid="56c76518-1fa6-4e2f-962a-0067fcba5998"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cusTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="880" y="0" width="100" height="20" uuid="6d2dc3be-beee-440d-b97c-04f567dcb379"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{onepayTransId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1080" y="0" width="100" height="20" uuid="e34d5359-b003-402c-9cb9-b1ce20566606"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1180" y="0" width="100" height="20" uuid="1108048f-27f9-4fa7-b856-03c3f939caf8"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType}) ?($F{amountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountIss}))):($F{amountAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountAcq})))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1280" y="0" width="100" height="20" uuid="7f723c95-102a-4762-812f-5885921d48d9"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType}) ?($F{percentFeeIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{percentFeeIss}))):($F{percentFeeAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{percentFeeAcq})))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="980" y="0" width="100" height="20" uuid="5e8c413c-3ce0-4a52-9dc7-abdddf9fb8e6"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cardNumber}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="1380" y="0" width="100" height="20" uuid="dac3ab3d-3724-4212-9a4d-************"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="2" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA["ISS".equals($P{p_acqType}) ?($F{issFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{issFee}))):($F{acqFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{acqFee})))]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
