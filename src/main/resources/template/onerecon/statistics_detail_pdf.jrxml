<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.6.0.final using JasperReports Library version 6.6.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="statistics_detail_pdf" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6abb5b6b-d6ad-4dad-bb1f-81100b804b05">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="p_fromDate" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="p_toDate" class="java.lang.String"/>
	<parameter name="p_merchantName" class="java.lang.String"/>
	<parameter name="p_merchantId" class="java.lang.String"/>
	<parameter name="p_transType" class="java.lang.String"/>
	<parameter name="p_bankId" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="transDate" class="java.lang.String"/>
	<field name="cardType" class="java.lang.String"/>
	<field name="acqCode" class="java.lang.String"/>
	<field name="amountIss" class="java.lang.Double"/>
	<field name="amountAcq" class="java.lang.Double"/>
	<field name="issFee" class="java.lang.Double"/>
	<field name="acqFee" class="java.lang.Double"/>
	<field name="amountRecon" class="java.lang.Double"/>
	<field name="feeRecon" class="java.lang.Double"/>
	<field name="transType" class="java.lang.String"/>
	<field name="countIss" class="java.lang.Integer"/>
	<field name="countAcq" class="java.lang.Integer"/>
	<variable name="sumAmountIss" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{amountIss}]]></variableExpression>
	</variable>
	<variable name="sumFeeIss" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{issFee}]]></variableExpression>
	</variable>
	<variable name="sumAmountACQ" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{amountAcq}]]></variableExpression>
	</variable>
	<variable name="sumFeeACQ" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{acqFee}]]></variableExpression>
	</variable>
	<variable name="sumAmountRecon" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{amountRecon}]]></variableExpression>
	</variable>
	<variable name="sumFeeRecon" class="java.lang.Double" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{feeRecon}]]></variableExpression>
	</variable>
	<variable name="sumCountIss" class="java.lang.Integer" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{countIss}]]></variableExpression>
	</variable>
	<variable name="sumCountAcq" class="java.lang.Integer" resetType="Group" resetGroup="groupCardType" calculation="Sum">
		<variableExpression><![CDATA[$F{countAcq}]]></variableExpression>
	</variable>
	<group name="groupCardType" keepTogether="true">
		<groupExpression><![CDATA[$F{cardType}]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="20" splitType="Stretch">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="86" y="0" width="73" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="acd6be21-78a6-4d18-9f13-84504ba1e3fa"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cardType}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="529" y="0" width="80" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="9eee7535-f670-4ec5-ae96-e98d04f18dbe"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeACQ}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeACQ}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="609" y="0" width="85" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="47723699-30a8-4f90-98b3-ab98362e30ce"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountRecon}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="694" y="0" width="85" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="5590a000-78d6-4c46-9f78-6d5f8c5136d2"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeRecon}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="224" y="0" width="80" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="bcfb5910-8f2f-4da2-a68b-7e9b1a8ef348"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountIss}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="304" y="0" width="80" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="1efcf01c-5d3c-4d9f-8695-0a9a3f36ba69"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumFeeIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumFeeIss}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="449" y="0" width="80" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="5d3c4e74-0247-4cbe-b2c3-c5d1dc372235"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumAmountACQ}==null?0:(new java.text.DecimalFormat("#,###.##").format($V{sumAmountACQ}))]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="13" y="0" width="73" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="c74fb97a-c5fc-4484-bbfb-8a8626677dee"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="159" y="0" width="65" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="160be9d5-f409-43b0-a883-63f977a19a25"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumCountIss}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement positionType="Float" stretchType="RelativeToTallestObject" mode="Opaque" x="384" y="0" width="65" height="20" printWhenGroupChanges="groupCardType" backcolor="#EDEFF0" uuid="6fd52df8-635a-4fda-9221-eba777afeb0e"/>
					<box>
						<pen lineWidth="1.0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
						<paragraph leftIndent="0" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sumCountAcq}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</title>
	<pageHeader>
		<band height="182" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="13" y="21" width="766" height="30" uuid="21af19ee-5639-459e-a182-9b3b12a8b025">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="16"/>
				</textElement>
				<text><![CDATA[BẢNG THỐNG KÊ CHI TIẾT CÁC KHOẢN PHẢI THU PHẢI TRẢ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="13" y="132" width="146" height="20" backcolor="#E3E5E6" uuid="d0a1a67f-6ebf-4263-bf6b-22abfd76a508"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="159" y="132" width="225" height="20" backcolor="#E3E5E6" uuid="649ac4e5-0819-4615-a52d-3836928b8b19"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[OnePAY phải thu (Receivable)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="384" y="132" width="225" height="20" backcolor="#E3E5E6" uuid="a6eb7d64-c5c2-483b-a562-498d36864c06"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[OnePAY phải trả (Payable)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="609" y="132" width="170" height="20" backcolor="#E3E5E6" uuid="30361bf9-b772-4e25-9208-68014ed03fc5"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Diff]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="180" y="51" width="514" height="20" isRemoveLineWhenBlank="true" uuid="d48b0374-6d45-4cb5-8fb5-e5de8b1269c7"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_fromDate}==null  ?"":("Từ ngày " +$P{p_fromDate} + ($P{p_toDate}==null  ?"":(" Đến ngày " + $P{p_toDate})))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="86" y="152" width="73" height="30" backcolor="#E3E5E6" uuid="ca75099e-bb17-4262-8ddf-b2acb236f1ba"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Bank ID]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="224" y="152" width="80" height="30" backcolor="#E3E5E6" uuid="0c4c8767-82fd-49f6-a603-1bf43cd19197"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(1)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="304" y="152" width="80" height="30" backcolor="#E3E5E6" uuid="fd86b180-d1f6-48b1-bf2f-2155b1c9d3af"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(1)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="449" y="152" width="80" height="30" backcolor="#E3E5E6" uuid="f56bac08-e475-44c4-b814-c9a1928c0eee"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(2)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="529" y="152" width="80" height="30" backcolor="#E3E5E6" uuid="fad59a56-7d7a-43a4-b6d8-772de9ae9c7c"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(2)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="609" y="152" width="85" height="30" backcolor="#E3E5E6" uuid="02755de6-e59f-4fb5-b661-92eb875996a9"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Amount(3)= Amount(1-2)]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="694" y="152" width="85" height="30" backcolor="#E3E5E6" uuid="f9069f9a-7edf-4f69-9484-627fdf195169"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Fee(3) = Fee(1-2)]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" isPrintRepeatedValues="false" x="609" y="91" width="170" height="20" isRemoveLineWhenBlank="true" uuid="48955284-7ec3-40bd-8c0d-525389c4c901">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mẫu báo cáo: RP02-OFFUS"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="13" y="152" width="73" height="30" backcolor="#E3E5E6" uuid="dec91a88-4f30-4864-983c-712cd76c5c14"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Trans Date]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="180" y="91" width="429" height="20" isRemoveLineWhenBlank="true" uuid="cb4a21ca-57fb-4233-9726-931dfcafb7a5">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_bankId} ==null?"":($P{p_bankId}.isEmpty()  ?"":("BankId: "+$P{p_bankId}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="180" y="71" width="514" height="20" isRemoveLineWhenBlank="true" uuid="ee610224-8d5c-48be-bc07-7f696c5ae95e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="10" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_transType} ==null?"":($P{p_transType}.isEmpty()  ?"":("Trans Type: "+$P{p_transType}))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="159" y="152" width="65" height="30" backcolor="#E3E5E6" uuid="d1c77c24-ec13-4085-94ab-4bfe219e8256"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Count of Trans]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="384" y="152" width="65" height="30" backcolor="#E3E5E6" uuid="97d30da1-1172-4375-9eab-15ae989ee148"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Count of Trans]]></text>
			</staticText>
			<textField>
				<reportElement isPrintRepeatedValues="false" x="609" y="111" width="170" height="20" isRemoveLineWhenBlank="true" uuid="b1d19ce3-a4c7-49f1-a54f-000f2f926afa">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["ĐVTT: VND"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="86" y="0" width="73" height="20" uuid="6c816f3f-fa74-4239-9e02-ef168b17d710"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="224" y="0" width="80" height="20" uuid="b2968fbd-ef3a-4e76-b7fc-bb87f08f10fa"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountIss}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountIss}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="304" y="0" width="80" height="20" uuid="bf7a7d87-ca4b-45cc-980a-b5252cca532b"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{issFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{issFee}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="529" y="0" width="80" height="20" uuid="*************-40c1-b313-4c0e77f3a89a"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acqFee}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{acqFee}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="609" y="0" width="85" height="20" uuid="9d8ddc8f-2884-4408-bbd0-2948ec04611f"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountRecon}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="694" y="0" width="85" height="20" uuid="0987ae62-e0d1-4cdc-834a-f4ccb053eaa5"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{feeRecon}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{feeRecon}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="449" y="0" width="80" height="20" uuid="23e7c20c-d729-46f6-abf2-8c60e4ae4ff6"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2" spacingAfter="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amountAcq}==null?0:(new java.text.DecimalFormat("#,###.##").format($F{amountAcq}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="13" y="0" width="73" height="20" uuid="3eec9fe2-d6bc-46f3-bb29-2cb2c87efc31"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="159" y="0" width="65" height="20" uuid="8246f3a9-9b01-4b88-ad79-8bc4b00b34f0"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{countIss}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="384" y="0" width="65" height="20" uuid="74e3f0bc-59b9-48c8-82ef-8bd6ccb7362f"/>
				<box>
					<pen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial"/>
					<paragraph leftIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{countAcq}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
		</band>
	</summary>
</jasperReport>
