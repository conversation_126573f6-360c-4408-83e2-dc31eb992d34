

import java.sql.ResultSet;
import java.util.*;
import java.util.HashMap;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.onepay.ma.service.models.Amount;
import com.onepay.ma.service.models.RefundApproval;

public class Test {

}


class TestResults {
    public static class Student {
        private String name;
        private int score;

        public Student(String name, int score) {
            this.name = name;
            this.score = score;
        }

        public int getScore() {
            return score;
        }

        public String getName() {
            return name;
        }


    }

    public static List<String> studentsThatPass(Stream<Student> students, int passingScore) {
        // throw new UnsupportedOperationException("Waiting to be implemented.");

        return students.filter(s -> s.getScore() >= passingScore).sorted(Comparator.comparingInt(Student::getScore)).map(Student::getName).collect(Collectors.toList());

    }

    // public static void main(String[] args) {

    // List<Student> students = new ArrayList<Student>();

    // students.add(new Student("<PERSON>", 80));
    // students.add(new Student("<PERSON>", 57));
    // students.add(new Student("<PERSON>", 21));

    // studentsThatPass(students.stream(), 50).forEach(System.out::println);
    // }



    public static void main(String[] args) {
        // ResultSet sqlQueryRs = null;
        try {
            List<InternationalTransactionHistory> listHistory = new ArrayList<>();
            listHistory.add(new InternationalTransactionHistory("Successfull", 526.30, "Purchase"));
            listHistory.add(new InternationalTransactionHistory("Successfull", 476.30, "Refund"));
            listHistory.add(new InternationalTransactionHistory(Status.REQUEST.code.toString(), 50.0, Status.REQUEST.content));
            double remainAmt = 0;
            double expectedResultAmount = 0.00;
            for (InternationalTransactionHistory his : listHistory) {

                if (his.getType() == RefundApproval.Status.REQUEST.content && Integer.valueOf(his.getStatus()) == Status.REQUEST.code) {
                    remainAmt -= his.getAmount();
                } else if (his.getType() == "Refund" && his.getStatus() == "Successfull") {
                    remainAmt -= his.getAmount();
                } else if (his.getType() == "Purchase") {
                    remainAmt += his.getAmount();
                }
            }
            if (remainAmt != expectedResultAmount) {
                // throw new Exception("remainAmt not match expectedResultAmount");
                throw new Exception("remainAmt: " + remainAmt + " not match expectedResultAmount: " + expectedResultAmount);
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
     public static enum Status {
        REQUEST(405, "Refund Waiting for OnePAY's Approval");

        public String content;
        public Integer code;

        private Status(Integer code, String content) {
            this.content = content;
            this.code = code;
        }
    }
    public static class InternationalTransactionHistory {
        private String status;
        private Double amount;
        private String type;



        public InternationalTransactionHistory(String status, Double amount, String type) {
            this.status = status;
            this.amount = amount;
            this.type = type;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Double getAmount() {
            return amount;
        }

        public void setAmount(Double amount) {
            this.amount = amount;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

    }
}

