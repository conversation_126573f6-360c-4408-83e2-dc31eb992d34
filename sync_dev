

env JAVA_HOME=/usr/java/jdk8 mvn clean
env JAVA_HOME=/usr/java/jdk8 mvn -U package

rsync -av --progress --delete --delete-excluded --include=classes --include=lib --include=classes/** --include=lib/** -f "+ setown" --exclude=* target/ build/
rsync -av --progress --delete --delete-excluded --rsh='ssh -p7109' --exclude=".cache" --exclude=".java" --exclude="activemq-data" --exclude="export" --exclude="tmp" --exclude="queue-data*" --exclude="file-*" root@10.38.130.41:/opt/ma-service/ ./promox/
meld ./build/ ./promox/


rsync -av --progress --delete --rsh='ssh -p7109' \
 --exclude=".cache" \
 --exclude=".java" \
 --exclude="activemq-data" \
 --exclude="export" \
 --exclude="tmp" \
 --exclude="queue-data*" \
 --exclude="file-*" \
  ./promox/ root@10.38.130.41:/opt/ma-service/

ssh root@10.38.130.41 -p 7109 '/opt/ma-service/setown 
  systemctl restart ma-service
  systemctl restart ma-service2
  systemctl restart ma-service3'