#!/bin/bash
#Creator: Vuongtc
#Date: 01-09-2021
env JAVA_HOME=/usr/java/jdk8 mvn clean

env JAVA_HOME=/usr/java/jdk8 mvn -U package

rsync -av --progress --delete --delete-excluded --rsh='ssh -p7109' \
  --include=classes \
  --include=lib \
  --include=classes/** \
  --include=lib/** \
  --exclude=* \
  target/ root@dc:/root/tmp_deploy/ma-service/

ssh root@localhost -p 7109 'cp -Rp /opt/ma-service/classes/ /opt/tmp_backup/ma-service-$(date +%Y%m%d_%H%M%S)/'
ssh root@localhost -p 7109 -X 'meld /root/tmp_deploy/ma-service/ /opt/ma-service/ && /opt/ma-service/setown'