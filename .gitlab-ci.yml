
image: java:latest

stages:
  - clean
  - deploy

clean:
  stage: clean
  tags:
    - portal
  script: env JAVA_HOME=/usr/java/jdk8 mvn clean
  rules:
    - if: $CI_COMMIT_BRANCH == "master_promox18"
  resource_group: ma-service

deploy:
  stage: deploy
  tags:
    - portal
  script:
    - env JAVA_HOME=/usr/java/jdk8 mvn -U package
    - pwd
    - rsync -av --progress --delete --rsh='ssh -o StrictHostKeyChecking=no -p 7109' --exclude=classes/server.properties --include=classes --include=lib --include=classes/** --include=lib/** --exclude=* target/ root@*************:/opt/ma-service/
    - ssh -o StrictHostKeyChecking=no root@************* -p 7109 -X '/opt/ma-service/setown && systemctl restart ma-service'
    - rsync -av --progress --delete --rsh='ssh -o StrictHostKeyChecking=no -p 7108' --exclude=classes/server.properties --include=classes --include=lib --include=classes/** --include=lib/** --exclude=* target/ root@*************:/opt/ma-service/
    - ssh -o StrictHostKeyChecking=no root@************* -p 7108 -X '/opt/ma-service/setown && systemctl restart ma-service'
  rules:
    - if: $CI_COMMIT_BRANCH == "master_promox18"
  resource_group: ma-service
